﻿using cmdiapp.n.core.Areas.crm.Core;
using cmdiapp.n.core.Areas.crm.Domain.Data;
using cmdiapp.n.core.Areas.legislator.Domain.Data;
using cmdiapp.n.core.Areas.legislator.Domain.Models;
using cmdiapp.n.core.Core;
using cmdiapp.n.core.Domain.Data;
using cmdiapp.n.core.Domain.Models;
using cmdiapp.n.core.Domain.ViewModels;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.Data.SqlClient;
using System.Globalization;
using System.Linq;
using System.Web;

namespace cmdiapp.n.core.Areas.legislator.Domain.Services.implementation
{
    public class ContactChangeService : IContactChangeService
    {
        private I_entity_legislator _entity;
        private IContactService _contactService;
        private I_entity _appEntity;
        private List<string> _PROPERTY_DELETE_EXCEPTIONS = new List<string>
            {
                "Id", "contactId", "statusId", "typeId", "note",
                "legislatorId", "CREATEDON", "UPDATEDON", "updatingUserId_i", "userId_i", "projectId"
            };

        public ContactChangeService(I_entity_legislator entity, IContactService contactService, I_entity appEntity)
        {
            _entity = entity;
            _contactService = contactService;
            _appEntity = appEntity;
        }

        public ContactChange Get(int id)
        {
            return _entity.Single<ContactChange>(cc => cc.Id == id);
        }

        public List<ContactChange> GetByContact(int contactId, bool pendingOnly = false)
        {
            return (from cc in _entity.All<ContactChange>()
                    join status in _entity.All<ContactChangeStatus>() on cc.statusId equals status.Id
                    where cc.contactId == contactId
                        && (!pendingOnly || status.code == "P")
                    select cc).ToList();
        }

        public ContactChange GetPreviouslyApprovedChange(int id)
        {
            // get necessary change record fields with status code
            var referenceChange = _entity.All<ContactChange>()
                .Where(c => c.Id == id)
                .Join(_entity.All<ContactChangeStatus>(),
                    change => change.statusId,
                    status => status.Id,
                    (change, status) => new { change, status })
                .Select(cs => new
                {
                    contactId = cs.change.contactId,
                    changeId = cs.change.Id,
                    updatedOn = cs.change.UPDATEDON,
                    statusCode = cs.status.code
                }).FirstOrDefault();

            if (referenceChange == null)
            {
                return null;
            }
            // returns last change approved before the given change if it's approved, else the
            // last approved change overall
            return (from change in _entity.All<ContactChange>()
                    join status in _entity.All<ContactChangeStatus>() on change.statusId equals status.Id
                    where change.contactId == referenceChange.contactId
                        && change.Id != referenceChange.changeId
                        && status.code == "A"
                        && change.UPDATEDON < ((referenceChange.updatedOn == null || referenceChange.statusCode != "A")
                            ? DateTime.Now 
                            : referenceChange.updatedOn)
                    orderby change.UPDATEDON descending
                    select change).FirstOrDefault();
        }

        public genericResponse Submit(ContactChange change)
        {
            string errMessage = ValidateAndPrepareContactChange(change);
            if (!string.IsNullOrEmpty(errMessage))
            {
                return new genericResponse
                {
                    success = false,
                    message = errMessage
                };
            }

            // ensure that status is "Pending"
            ContactChangeStatus pendingStatus = _entity.Single<ContactChangeStatus>(s => s.code == "P");
            change.statusId = pendingStatus.Id;

            change.CREATEDON = DateTime.Now;
            change.userId_i = session.userSession.UserId_i;
            change.projectId = session.currentDomain_projectId;

            // add
            try
            {
                _entity.Add(change);
                _entity.CommitChanges();

                SendNotification(change);

                return new genericResponse
                {
                    success = true
                };
            }
            catch (Exception ex)
            {
                return new genericResponse
                {
                    success = false,
                    message = "An error occurred. Unable to submit contact change request.",
                    messageKey = ex.Message
                };
            }
            
        }

        private string ValidateAndPrepareContactChange(ContactChange change)
        {
            // check if legislator exists
            Legislator legislator = _entity.Single<Legislator>(l => l.Id == change.legislatorId);
            if (legislator == null)
            {
                return $"There is no Congress Member record with ID {change.legislatorId}.";
            }
            // check that type is valid
            ContactChangeType type = _entity.Single<ContactChangeType>(t => t.Id == change.typeId);
            if (type == null)
            {
                return $"There is no valid change type with ID {change.typeId}";
            }
            
            // check if contact exists
            Contact contact = _entity.Single<Contact>(c => c.Id == change.contactId && !c.deleted);
            if (contact == null)
            {
                if (type.code != "I")
                {
                    // contact does not exist and change is now trying to insert new contact
                    // so return and notify
                    return $"You are attempting to {type.type} a contact, but it does not exist.";
                }
                else
                {
                    // set contactId = null for new contact so we don't conflict with ones subsequently added
                    change.contactId = null;
                }
            }
            else
            {
                if (type.code == "I")
                {
                    // contact exists, but trying to insert
                    return $"You are attempting to {type.type} a contact, but it already exists.";
                }

                if (type.code == "D")
                {
                    // null out properties for delete type change for display purposes
                    PrepareDeleteChange(change);
                }
            }

            return "";
        }

        private void PrepareDeleteChange(ContactChange change)
        {
            foreach (var prop in change.GetType().GetProperties())
            {
                if (!_PROPERTY_DELETE_EXCEPTIONS.Contains(prop.Name))
                {
                    prop.SetValue(change, null);
                }
            }
        }

        public List<ContactChangeStatus> GetChangeStatuses()
        {
            return _entity.All<ContactChangeStatus>().ToList();
        }

        public List<ContactChangeType> GetChangeTypes()
        {
            return _entity.All<ContactChangeType>().ToList();
        }

        public List<ContactChangeStatus> GetAccessibleStatuses(int id)
        {
            var change = (from c in _entity.All<ContactChange>()
                          join s in _entity.All<ContactChangeStatus>() on c.statusId equals s.Id
                          join t in _entity.All<ContactChangeType>() on c.typeId equals t.Id
                          where c.Id == id
                          select new
                          {
                              Id = c.Id,
                              typeCode = t.code,
                              statusCode = s.code,
                              statusId = s.Id,
                              contactId = c.contactId
                          }).FirstOrDefault();
            if (change == null)
            {
                return new List<ContactChangeStatus>();
            }
            return GetChangeStatuses().Where(status => status.Id != change.statusId
                    && _contactService.CanUpdateStatusTo(
                        change.contactId ?? 0, change.Id, change.typeCode, change.statusCode, status.code)).ToList();
        }

        public genericResponse UpdateStatus(int id, int statusId)
        {
            // make sure this is valid status
            ContactChangeStatus newStatus = _entity.Single<ContactChangeStatus>(s => s.Id == statusId);
            if (newStatus == null)
            {
                return new genericResponse
                {
                    success = false,
                    message = $"There is no valid status with ID {statusId}"
                };
            }

            // make sure change record exists
            ContactChange change = _entity.Single<ContactChange>(c => c.Id == id);
            if (change == null)
            {
                return new genericResponse
                {
                    success = false,
                    message = $"There is no Contact Change Record with ID {id}"
                };
            }

            // get current status
            ContactChangeStatus oldStatus = _entity.Single<ContactChangeStatus>(s => s.Id == change.statusId);
            // make sure status is actually changing
            if (newStatus.Id == oldStatus.Id)
            {
                return new genericResponse
                {
                    success = false,
                    message = "Contact Change Record already has this status."
                };
            }

            try
            {                
                // send to ContactService to handle any necessary changes to Contact table
                // which returns a genericresponse
                var response = _contactService.HandleChange(change, oldStatus, newStatus);
                if (response.success)
                {
                    // if successful, set statusId
                    change.statusId = newStatus.Id;
                    change.UPDATEDON = DateTime.Now;
                    change.updatingUserId_i = session.userSession.UserId_i;
                    _entity.Update(change);
                    _entity.CommitChanges();
                }
                return response;

            }
            catch (Exception ex)
            {
                return new genericResponse
                {
                    success = true,
                    message = "An error occurred. Unable to update change status",
                    messageKey = ex.Message
                };
            }
        }

        public ContactChangeDisplayPage GetLegislatorDisplay(
            string legislatorId = "all", bool pendingOnly = true, int contactId = 0, int pageNo = 1)
        {
            int PAGE_SIZE = 10;
            // since users table and legislator contact changes table are on different servers
            // need to get results from legislator db first and then join usernames
            var changes = (from change in _entity.All<ContactChange>()
                           join legislator in _entity.All<Legislator>() on change.legislatorId equals legislator.Id
                           join status in _entity.All<ContactChangeStatus>() on change.statusId equals status.Id
                           join type in _entity.All<ContactChangeType>() on change.typeId equals type.Id
                           join contact in _entity.All<Contact>() on change.contactId equals contact.Id into contacts
                           from contact in contacts.DefaultIfEmpty()
                           where (legislatorId == "all" || change.legislatorId == legislatorId)
                           && (contactId == 0 || change.contactId == contactId)
                           && (!pendingOnly || status.code == "P")
                           orderby status.code == "P" descending,
                            (status.code == "P" ? change.CREATEDON : change.UPDATEDON) descending,
                            change.LNAME,
                            change.FNAME,
                            change.Id
                           select new ContactChangeDisplay()
                           {
                               contactChangeId = change.Id,
                               status = status.status,
                               type = type.type,
                               contactDisplayName = (contact == null
                                ? (string.IsNullOrEmpty(change.FNAME) ? "" : change.FNAME + " ") + change.LNAME
                                : (string.IsNullOrEmpty(contact.FNAME) ? "" : contact.FNAME + " ") + contact.LNAME),
                               submittedOn = change.CREATEDON,
                               submittedById = change.userId_i,
                               statusUpdatedOn = change.UPDATEDON,
                               statusUpdatedById = change.updatingUserId_i,
                               note = change.note,
                               legislatorName = legislator.name,
                               legislatorId = legislator.Id
                           }
                       );
            int totalCount = changes?.Count() ?? 0;
            int totalPages = (int)Math.Ceiling(totalCount / (decimal)PAGE_SIZE);
            // no need to query for users
            if (totalCount < 1)
            {
                return new ContactChangeDisplayPage
                {
                    pageNo = 1,
                    changes = changes?.ToList() ?? new List<ContactChangeDisplay>(),
                    totalCount = totalCount,
                    totalPages = totalPages,
                    pageSize = PAGE_SIZE
                };
            }

            var changesWithUsers =
                    (from change in changes.Skip((pageNo - 1) * PAGE_SIZE).Take(PAGE_SIZE).ToList()
                    join submittedUser in _appEntity.All<user>() on change.submittedById equals submittedUser.UserId_i
                    join updatingUser in _appEntity.All<user>() on change.statusUpdatedById equals updatingUser.UserId_i into updatingUsers
                    from updatingUser in updatingUsers.DefaultIfEmpty()
                    select new ContactChangeDisplay()
                    {
                        contactChangeId = change.contactChangeId,
                        status = change.status,
                        type = change.type,
                        contactDisplayName = change.contactDisplayName,
                        submittedOn = change.submittedOn,
                        submittedById = change.submittedById,
                        statusUpdatedOn = change.statusUpdatedOn,
                        statusUpdatedById = change.statusUpdatedById,
                        submittedBy = (string.IsNullOrEmpty(submittedUser.FName) ? "" : submittedUser.FName + " ")
                            + submittedUser.LName,
                        statusUpdatedBy = updatingUser == null
                            ? ""
                            : (string.IsNullOrEmpty(updatingUser.FName) ? "" : updatingUser.FName + " ")
                                + updatingUser.LName,
                        note = change.note,
                        legislatorName = change.legislatorName,
                        legislatorId = change.legislatorId
                    }).ToList();

            return new ContactChangeDisplayPage
            {
                pageNo = pageNo,
                totalPages = totalPages,
                totalCount = totalCount,
                changes = changesWithUsers,
                pageSize = PAGE_SIZE
            };

        }

        public genericResponse AmendChangeField(int changeId, string fieldName, string value)
        {
            ContactChange change = _entity.Single<ContactChange>(c => c.Id == changeId);
            if (change == null)
            {
                return new genericResponse
                {
                    success = false,
                    message = $"There is no Contact Change record with ID {changeId}"
                };
            }

            ContactChangeStatus status = _entity.Single<ContactChangeStatus>(s => s.Id == change.statusId);
            if ((status?.code ?? "") != "P")
            {
                return new genericResponse
                {
                    success = false,
                    message = $"Only Pending changes can be amended.  This change is {status?.status ?? "Unknown Status"}"
                };
            }

            // make sure field is valid
            var prop = change.GetType().GetProperty(fieldName);
            if (prop == null
                // these fields should not be editable by user
                || _PROPERTY_DELETE_EXCEPTIONS.Contains(prop.Name))
            {
                return new genericResponse
                {
                    success = false,
                    message = $"{fieldName} is not a valid field."
                };
            }

            try
            {
                // types of editable fields for ContactChange will be string, bool, or DateTime?
                if (prop.PropertyType == typeof(bool))
                {
                    bool valueToSet = (value?.ToLower().Trim() ?? "") == "true" ? true : false;
                    prop.SetValue(change, valueToSet);
                }

                if (prop.PropertyType == typeof(DateTime?))
                {
                    DateTime? valueToSet = string.IsNullOrEmpty(value) ? null as DateTime? : Convert.ToDateTime(value);
                    prop.SetValue(change, valueToSet);
                }

                if (prop.PropertyType == typeof(string))
                {
                    string valueToSet = value?.Trim() ?? "";
                    prop.SetValue(change, valueToSet);
                }

                _entity.Update(change);
                _entity.CommitChanges();

                return new genericResponse
                {
                    success = true,
                    results = new List<iItemType>() { change }
                };

            }
            catch (Exception ex)
            {
                return new genericResponse
                {
                    success = false,
                    message = "An error occurred. Unable to amend Contact Change.",
                    messageKey = ex.Message
                };
            }           
            
        }

        private void SendNotification(ContactChange change)
        {
            // get all info we need for notification
            user user = _appEntity.Single<user>(u => u.UserId_i == change.userId_i);
            Legislator legislator = _entity.Single<Legislator>(l => l.Id == change.legislatorId);
            Contact contact = _entity.Single<Contact>(c => c.Id == change.contactId);

            string userName = $"{user.FName} {user.LName} ({user.LoweredUserName})";
            string contactName = (contact == null
                                ? (string.IsNullOrEmpty(change.FNAME) ? "" : change.FNAME + " ") + change.LNAME
                                : (string.IsNullOrEmpty(contact.FNAME) ? "" : contact.FNAME + " ") + contact.LNAME);
            string legislatorName = $"{new CultureInfo("en-US", false).TextInfo.ToTitleCase(legislator.termType)}. {legislator.name}";
            string href = $@"#/crm/Member/ContactChanges/{legislator.Id}/{contact?.Id ?? 0}/true";
            string subjectLine = $"{userName} submitted a Contact Change for {legislatorName}";

            // insert into html template
            var template = $@"<!DOCTYPE html>
                <html>
                <head>
                    <meta charset=""utf-8"" />
                    <meta http-equiv=""X-UA-Compatible"" content=""IE=edge"">
                    <meta name=""viewport"" content=""width=device-width, initial-scale=1"">
                    <style>
                        html {{
                            box-sizing: border-box;
                            font-family: sans-serif;
                            font-size: 16px;
                        }}
                        body {{
                            font-size: 1rem;
                            line-height: 1.5;
                            color: #373a3c;
                            background-color: #fff;
                        }}
                        *, *::before, *::after {{
                            box-sizing: inherit;
                            margin: 0;
                            padding: 0;
                            border:0;
                        }}

                        header,
                        section {{
                            display: block;
                        }}
                        a {{
                            display: inline-block
                        }}
                        h1 {{
                            font-size: 1.25rem;
                            font-weight: bold;
                        }}
                        header {{
                            padding: 1rem;
                        }}
                        section {{
                            padding: 2rem;
                            background-color: #fcfcfc;
                            box-shadow: 0px 3px 10px 3px rgba(0,0,0,0.1);
                            color: black;
                        }}
                        p {{
                            margin-bottom: 1rem;
                        }}
                        section p a {{
                            padding: 0.25rem 0.5rem;
                            border-radius: 3px;
                            background-color: #fff;
                            color: inherit;
                            text-decoration: none;
                            box-shadow: 3px 0px 10px 3px rgba(0,0,0,0.1);
                            transition: transform 300ms ease, color 300ms ease;
                            font-weight: bold;
                        }}
                        section p a:hover {{
                            transform: translateY(5%);
                            color: #00a8ff;
                        }}
                    </style>
                </head>
                <body>
                    <header>
                        <h1 style=""margin-bottom:0;"">{subjectLine}</h1>
                    </header>
                    <section>
                        <p>Contact Name: {contactName}</p>
                        <p>Submitted On: {(change.CREATEDON ?? DateTime.Now).ToString("MM/dd/yyyy hh:mm tt")}</p>
                        <p>Note: {change.note}</p>
                        <p>Click <a href=""{href}"">here</a> to see pending changes for this contact.</p>
                    </section>
                </body>
                </html>";

            try
            {
                // get usernames from web.config
                List<string> userNames = ConfigurationManager.AppSettings["notifyWhenCongressMemberContactChange"]?
                    .Split(',')
                    .Select(u => u.Trim().ToLower())
                    .ToList() ?? new List<string>();
                
                // join to user records so we know we have real users          
                List<user> users = _appEntity.All<user>()
                    .Join(userNames,
                        use => use.LoweredUserName,
                        username => username,
                        (use, username) => use)
                    .ToList();

                if (users.Count() > 0)
                {
                    // this proc will create the content entry and insert notific for only one record
                    // then will return the content id
                    var result = _appEntity.getContext().Database.SqlQuery<noticProcess>(
                            "EXEC dbo.notific_send 'Crimson',@subject,@content,@username, '', 1, -1, -1, 0,''",
                            new SqlParameter("@subject", subjectLine),
                            new SqlParameter("@content", template),
                            new SqlParameter("@username", users.FirstOrDefault()?.UserName ?? ""))
                        .SingleOrDefault();

                    // now do the remaining if any
                    if (result != null && result.notificContentId > 0 && users.Count > 1)
                    {
                        // start at 1 since we already did the first via the stored proc
                        for (int i = 1; i < users.Count; i++)
                        {
                            // insert notific record that will join to content record
                            _appEntity.Add(new notific()
                            {
                                userId_i = users[i].UserId_i,
                                projectId = -1,
                                notificContentId = result.notificContentId,
                                createdAt = DateTime.Now
                            });
                        }
                        _appEntity.CommitChanges();
                    }
                }
            }
            catch (Exception ex)
            {
                // it's ok if notifications don't get sent, but we want to make sure
                // we get submission success message back to user, so just do nothing if exception
                var e = ex;
            }

        }
    }
}