﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Data;
using System.Data.SqlClient;
using System.Text.RegularExpressions;
using Ninject;
using Ninject.Web.Mvc;

using cmdiapp.n.core.Core.App_Start;
using cmdiapp.n.core.Domain.Models;
using cmdiapp.n.core.Areas.crm.Domain.Models;
using cmdiapp.n.core.Areas.crm.Domain.ViewModels;
using cmdiapp.n.core.Areas.crm.Domain.Repositories;
using cmdiapp.n.core.Domain.ViewModels;
using cmdiapp.n.core.Library;

namespace cmdiapp.n.core.Areas.crm.Domain.Services
{
    public class dataentryService : IdataentryService
    {
        private I___unitOfWork _unitOfWork;
        private I__DBFactory _dbFactory;
        private IBatchRepository _batchRepository;
        supportMethods _supportMethods = new supportMethods();

        public dataentryService(I___unitOfWork unitOfWork, I__DBFactory dbFactory, IBatchRepository BatchRepository)
        {
            _unitOfWork = unitOfWork;
            _dbFactory = dbFactory;
            _batchRepository = BatchRepository;
                    
        }

        public MessageBoard process_sql(string p_sql, string p_datasetName, MessageBoard _msgBoard)
        {
            try
            {
                //get the data
                DataSet _data = _supportMethods.GetDataSet(_dbFactory.getContext().Database.Connection.ConnectionString, p_sql, p_datasetName);
                //cehck what we have
                if (_data != null)
                {
                    _msgBoard.status = true;
                    _msgBoard.xmlresponse = _data.GetXml();
                }
                else
                {
                    _msgBoard.status = false;
                    _msgBoard.xmlresponse = "<error msg=\"error occurred during data retrieval\"/>";
                    _msgBoard.message = "Error in DB Process: Saving DT Batch Data : Dataset Name  " + p_datasetName + " ";
                }

            }
            catch (System.Exception ex)
            {
                //we have some issue
                _msgBoard.status = false;
                _msgBoard.xmlresponse = "";
                _msgBoard.message = "Error in DB Process: Saving DT Batch Data : Dataset Name " + p_datasetName + " " + ex.InnerException.ToString();
            }
            //return response
            return _msgBoard;
        }

        //public DataSet GetDataSet(string ConnectionString, string SQL, string p_datasetName)
        //{
        //    SqlConnection conn = new SqlConnection(ConnectionString);
        //    SqlDataAdapter da = new SqlDataAdapter();
        //    SqlCommand cmd = conn.CreateCommand();
        //    cmd.CommandText = SQL;
        //    da.SelectCommand = cmd;
        //    DataSet ds = new DataSet();
        //    //Let us open the connection
        //    conn.Open();
        //    da.Fill(ds, p_datasetName);
        //    conn.Close();
        //    //get the Dataset
        //    return ds;
        //}

        public bool check_SourceCode_exists(string sourceCode)
        {
            string sql = string.Format("SELECT Count(*) as recordCount FROM SOURCE WHERE Upper(SRCECODE) ='{0}'", sourceCode.ToUpper());
            var q = _dbFactory.getContext().Database.SqlQuery<UniquenessCheck>(sql);
            int isZero = q.FirstOrDefault().recordCount;
            return !(isZero == 0);
        }

        public string get_SourceCode(int srcId)
        {
            //For Source Code
            string sql = string.Format("SELECT * FROM SOURCE WHERE SRCEID = {0}", srcId);
            var q = _dbFactory.getContext().Database.SqlQuery<SOURCE>(sql);
            return q.FirstOrDefault().SRCECODE;
        }
        
        public string get_ExceptionCode(int excpId)
        {
            //For Source Code
            string sql = string.Format("SELECT * FROM lkEXCEP WHERE EXCEPID = {0}", excpId);
            var q = _dbFactory.getContext().Database.SqlQuery<lkEXCEP>(sql);
            return q.FirstOrDefault().EXCEP;
        }

        public bool check_ExceptionCode_exists(string exceptionCode)
        {
            string sql = string.Format("SELECT Count(*) as recordCount FROM lkExcep WHERE Upper(EXCEP) ='{0}'", exceptionCode.ToUpper());
            var q = _dbFactory.getContext().Database.SqlQuery<UniquenessCheck>(sql);
            int isZero = q.FirstOrDefault().recordCount;
            return !(isZero == 0);
        }

        public bool check_dtBatchRecord_exists(int batchId)
        {
            string sql = string.Format("SELECT Count(*) as recordCount FROM dtBATCH WHERE BATCHID ={0}", batchId);
            var q = _dbFactory.getContext().Database.SqlQuery<UniquenessCheck>(sql);
            int isZero = q.FirstOrDefault().recordCount;
            return !(isZero == 0);
        }

        public bool check_MonyRecord_exists(int batchId)
        {
            string sql = string.Format("SELECT Count(*) as recordCount FROM MONY WHERE BATCHID ={0}", batchId);
            var q = _dbFactory.getContext().Database.SqlQuery<UniquenessCheck>(sql);
            int isZero = q.FirstOrDefault().recordCount;
            return !(isZero == 0);
        }

        public bool check_PeopleRecord_exists(string pid)
        {
            string sql = string.Format("SELECT Count(*) as recordCount FROM PEOPLE where PID = dbo.oCONFIRMPID({0})", pid);
            var q = _dbFactory.getContext().Database.SqlQuery<UniquenessCheck>(sql);
            int isZero = q.FirstOrDefault().recordCount;
            return !(isZero == 0);
        }
        
        public bool check_MonyTableRecord_exists(int dtBatchId)
        {
            string sql = string.Format("SELECT Count(*) as recordCount FROM MONY WHERE dtBATCHID ={0}", dtBatchId);
            var q = _dbFactory.getContext().Database.SqlQuery<UniquenessCheck>(sql);
            int isZero = q.FirstOrDefault().recordCount;
            return !(isZero == 0);
        }

        public bool check_dtBatchTableRecord_exists(int dtBatchId)
        {
            string sql = string.Format("SELECT Count(*) as recordCount FROM dtBATCH WHERE dtBATCHID ={0}", dtBatchId);
            var q = _dbFactory.getContext().Database.SqlQuery<UniquenessCheck>(sql);
            int isZero = q.FirstOrDefault().recordCount;
            return !(isZero == 0);
        }
        
        public bool check_BatchNumber_exists(string BatchNumber, string batchDate)
        {
            string sql = string.Format("SELECT Count(*) as recordCount FROM BATCH WHERE BATCHNO ='{0}' AND BATCHDTE = '{1}'", BatchNumber, batchDate);
            var q = _dbFactory.getContext().Database.SqlQuery<UniquenessCheck>(sql);
            int isZero = q.FirstOrDefault().recordCount;
            return !(isZero == 0);
        }

        public int get_SourceId(string sourceCode)
        {
            //For Source Id
            string sql = string.Format("SELECT * FROM SOURCE WHERE Upper(SRCECODE) ='{0}'", sourceCode.ToUpper());
            var q = _dbFactory.getContext().Database.SqlQuery<SOURCE>(sql);
            return q.FirstOrDefault().SRCEID;
        }
        
        public string get_FlagAsString(int FlagId)
        {
            //For Flag as String
            string sql = string.Format("SELECT * FROM dmFLAG WHERE flagid ='{0}'", FlagId);
            var q = _dbFactory.getContext().Database.SqlQuery<dmFLAG>(sql);
            return q.FirstOrDefault().FLAG;
        }

        public string get_FundCode(int fundId)
        {
            //For Fund
            string sql = string.Format("SELECT * FROM dmFUND WHERE FUNDID ='{0}'", fundId);
            var q = _dbFactory.getContext().Database.SqlQuery<dmFUND>(sql);
            return q.FirstOrDefault().FUNDCODE;
        }

        public string get_KeywordAsString(int kwrdId)
        {
            //For Keyword as String
            string sql = string.Format("SELECT * FROM dmKWRD WHERE kwrdid ='{0}'", kwrdId);
            var q = _dbFactory.getContext().Database.SqlQuery<dmKWRD>(sql);
            return q.FirstOrDefault().KWRD;
        }

        public int get_ExceptionId(string exceptionCode)
        {
            //For Exception Id
            string sql = string.Format("SELECT * FROM lkExcep WHERE Upper(EXCEP) ='{0}'", exceptionCode.ToUpper());
            var q = _dbFactory.getContext().Database.SqlQuery<lkEXCEP>(sql);
            return Convert.ToInt32(q.FirstOrDefault().EXCEPID);
        }
        
        public BATCH get_BatchById(string sql)
        {
            var q = _dbFactory.getContext().Database.SqlQuery<BATCH>(sql);
            return q.FirstOrDefault();
        }

        public BatchExtended get_BatchExtended(string sql)
        {
            var q = _dbFactory.getContext().Database.SqlQuery<BatchExtended>(sql);
            return q.FirstOrDefault();
        }

        public List<peopleMatchingRecords> get_PeopleMatchingRecords(string sql)
        {
            var q = _dbFactory.getContext().Database.SqlQuery<peopleMatchingRecords>(String.Format("EXEC dbo.get_allDataSet '{0}'", sql));
            return q.ToList();
        }

        public List<_ssCONFIG> get_ssConfigList()
        {
            string sql = "SELECT ISNULL(CONFIGID,0) as CONFIGID, ISNULL(CONFIGCODE,'''') as CONFIGCODE, ISNULL(CONFIGDESC,'''') as CONFIGDESC, ISNULL(CONFIGVALUE,'''') as CONFIGVALUE from ssCONFIG";
            var q = _dbFactory.getContext().Database.SqlQuery<_ssCONFIG>(String.Format("EXEC dbo.get_allDataSet '{0}'", sql));
            return q.ToList();
        }
        
        public List<PeopleSummary> get_PeopleSummary(string sql)
        {
            var q = _dbFactory.getContext().Database.SqlQuery<PeopleSummary>(String.Format("EXEC dbo.get_allDataSet '{0}'", sql));
            return q.ToList();
        }

        public decimal get_PeopleTotalDonation(string sql)
        {
            var q = _dbFactory.getContext().Database.SqlQuery<PeopleDonationTotal>(String.Format("EXEC dbo.get_allDataSet '{0}'", sql));
            return q.FirstOrDefault().TOTALCUMAMTDONATED;
        }

        public List<selPeopleFLAGs> get_selPeopleFlags(string sql)
        {
            var q = _dbFactory.getContext().Database.SqlQuery<selPeopleFLAGs>(String.Format("EXEC dbo.get_allDataSet '{0}'", sql));
            return q.ToList();
        }

        public List<selPeopledmKWRDs> get_selPeopleKeywords(string sql)
        {
            var q = _dbFactory.getContext().Database.SqlQuery<selPeopledmKWRDs>(String.Format("EXEC dbo.get_allDataSet '{0}'", sql));
            return q.ToList();
        }

        public peopleSelected get_PeopleRecordById(string sql)
        {
            var q = _dbFactory.getContext().Database.SqlQuery<peopleSelected>(sql);
            return q.FirstOrDefault();
        }

        public completedtBatchRecord get_dtBatchRecordForEdit(string sql)
        {
            var q = _dbFactory.getContext().Database.SqlQuery<completedtBatchRecord>(sql);
            return q.FirstOrDefault();
        }
        

        public List<peopleMatchingRecords_ext> get_all_PeopleMatchingRecords(string sql, string orderBy, int pageSize, int pageNo)
        {
            string final_sql = String.Format("EXEC dbo.get_pagedDataSet '{0}', '{1}', {2}, {3}", sql, orderBy, pageSize, pageNo);
            var q = _dbFactory.getContext().Database.SqlQuery<peopleMatchingRecords_ext>(final_sql);

            return q.ToList();
        }

        public List<batchKeyedRecords_ext> get_all_KeyedRecords(string sql, string orderBy, int pageSize, int pageNo)
        {
            string final_sql = String.Format("EXEC dbo.get_pagedDataSet '{0}', '{1}', {2}, {3}", sql, orderBy, pageSize, pageNo);
            var q = _dbFactory.getContext().Database.SqlQuery<batchKeyedRecords_ext>(final_sql);

            return q.ToList();
        }

        public List<peopleFundDetails> get_all_PeopleFunds(string sql, string orderBy, int pageSize, int pageNo)
        {
            string final_sql = String.Format("EXEC dbo.get_pagedDataSet '{0}', '{1}', {2}, {3}", sql, orderBy, pageSize, pageNo);
            var q = _dbFactory.getContext().Database.SqlQuery<peopleFundDetails>(final_sql);

            return q.ToList();
        }


        public void Add(BATCH _BATCH)
        {
            _batchRepository.Add(_BATCH);
            _unitOfWork.commit();
        }

        public void Update(BATCH _BATCH)
        {
            _batchRepository.Update(_BATCH);
            _unitOfWork.commit();
        }

        public void Delete(BATCH _BATCH)
        {
            _batchRepository.Delete(_BATCH);
            _unitOfWork.commit();
        }

        public bool IsAlphaNumeric(string strToCheck)
        {
            Regex objAlphaNumericPattern = new Regex("[^a-zA-Z0-9]");
            return !objAlphaNumericPattern.IsMatch(strToCheck);
        }
           
        public List<dmCENTERForDataEntry> get_dmCenterOptions(string sql)
        {
            var q = _dbFactory.getContext().Database.SqlQuery<dmCENTERForDataEntry>(String.Format("EXEC dbo.get_allDataSet '{0}'", sql));
            return q.ToList();
        }
        
        public List<dmFUNDForDataEntry> get_FundOptions(string sql)
        {
            var q = _dbFactory.getContext().Database.SqlQuery<dmFUNDForDataEntry>(String.Format("EXEC dbo.get_allDataSet '{0}'", sql));
            return q.ToList();
        }



        public List<lkMONYTYPEForDataEntry> get_MonyTypeOptions(string sql)
        {
            var q = _dbFactory.getContext().Database.SqlQuery<lkMONYTYPEForDataEntry>(String.Format("EXEC dbo.get_allDataSet '{0}'", sql));
            return q.ToList();
        }

        public List<lkPEOCODEForDataEntry> get_PeopleCodeOptions(string sql)
        {
            var q = _dbFactory.getContext().Database.SqlQuery<lkPEOCODEForDataEntry>(String.Format("EXEC dbo.get_allDataSet '{0}'", sql));
            return q.ToList();
        }

        public List<lkPEOTYPEForDataEntry> get_PeopleTypeOptions(string sql)
        {
            var q = _dbFactory.getContext().Database.SqlQuery<lkPEOTYPEForDataEntry>(String.Format("EXEC dbo.get_allDataSet '{0}'", sql));
            return q.ToList();
        }

        public List<lkCCMonthOptions> get_CCMonthOptions(string sql)
        {
            var q = _dbFactory.getContext().Database.SqlQuery<lkCCMonthOptions>(String.Format("EXEC dbo.get_allDataSet '{0}'", sql));
            return q.ToList();
        }

        public List<lkCCYearOptions> get_CCYearOptions(string sql)
        {
            var q = _dbFactory.getContext().Database.SqlQuery<lkCCYearOptions>(String.Format("EXEC dbo.get_allDataSet '{0}'", sql));
            return q.ToList();
        }

        //public List<lkEXCEP> exceptionByFilter(string term)
        //{
        //    //remove the spaces from source code and desc and get it
        //    string sql = string.Format("Select Top 10 [EXCEPID] ,[EXCEP] , rtrim(ltrim([EXCEP])) + '' - '' + rtrim(ltrim([DESCRIP])) as DESCRIP from lkExcep where Upper(DESCRIP) LIKE ''{0}%'' OR Upper(EXCEP) LIKE ''{0}%''", term.ToUpper());

        //    var q = _dbFactory.getContext().Database.SqlQuery<lkEXCEP>(String.Format("EXEC dbo.get_allDataSet '{0}'", sql));
        //    //return the result
        //    return q.ToList();

        //}

    }
}