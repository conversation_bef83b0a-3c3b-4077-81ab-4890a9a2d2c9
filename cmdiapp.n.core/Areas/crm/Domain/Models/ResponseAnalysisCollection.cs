﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Data;
using System.Linq;
using System.Web.Mvc;
using System.Data.Linq;
using System.ComponentModel.DataAnnotations.Schema;

using cmdiapp.n.core.Domain.ViewModels;
using cmdiapp.n.core.Areas.crm.Domain.Data;

namespace cmdiapp.n.core.Areas.crm.Domain.Models
{
    public class ResponseAnalysisCollection : _entityBase_crm, iItemType
    {
        public string PROGRAM { get; set; }

        public int[] SELECTEDPKGITEMS { get; set; }

        public bool MAILDTE { get; set; }

        public DateTime? STARTDTE { get; set; }

        public DateTime? ENDDTE { get; set; }

        public bool SHOWSUBTOTBYPCKG { get; set; }

        public bool SHOWSUBTOTBYGRP { get; set; }

        public bool SHOWSUBTOTBYLIST { get; set; }

        public bool SHOWDETAILS { get; set; }

        public bool ISTELEMVERSION { get; set; }

        public int PROGID { get; set; }

        public bool SUBPROGVERSION { get; set; }
        public bool SHOWNONDONORS { get; set; }

    }

    public class ResponseAnalysisCollectionSub : _entityBase_crm, iItemType
    {
        public bool SUBPROGVERSION { get; set; }

        public string SUBPROGRAM { get; set; }

        public int[] SUBSELECTEDPKGITEMS { get; set; }

        public bool SUBMAILDTE { get; set; }

        public DateTime? SUBSTARTDTE { get; set; }

        public DateTime? SUBENDDTE { get; set; }

        public bool SUBSHOWSUBTOTBYPCKG { get; set; }

        public bool SUBSHOWSUBTOTBYGRP { get; set; }

        public bool SUBSHOWSUBTOTBYLIST { get; set; }

        public bool SUBSHOWDETAILS { get; set; }

        public bool ISTELEMVERSION { get; set; }

        public int SUBPROGID { get; set; }

    }


    public class ResponseAnalysisData : _entityBase_crm, iItemType
    {
        public string PROGTYPE { get; set; }

        public string DESCRIP { get; set; }

        public string PKGECODE { get; set; }

        public string PKGEDESC { get; set; }

        public Int64? K_MDTKEY { get; set; }

        public DateTime? PLastCage { get; set; }

        public string LISTNOG { get; set; }

        public string LISTNO { get; set; }

        public string SRCECODE { get; set; }

        public string SRCEDESC { get; set; }

        public DateTime? MAILDATE { get; set; }

        public int? MAILQTY { get; set; }

        public DateTime? FIRSTCAGE { get; set; }

        public DateTime? LASTCAGE { get; set; }
        
        public int? DONORS { get; set; }

        public int? DONATIONS { get; set; }

        public Single? RESPONSE { get; set; }

        public Decimal? GROSS { get; set; }

        public Decimal? GPMAIL { get; set; }

        public Decimal? GPDONTN { get; set; }

        public Decimal? COST { get; set; }

        public Decimal? CPMAIL { get; set; }

        public Decimal? CPDONTN { get; set; }

        public Decimal? NET { get; set; }

        public Decimal? NPMAIL { get; set; }

        public Decimal? NPDONTN { get; set; }

        public Decimal? ROI { get; set; }

        public int? TELECOMPCALLS { get; set; }

        public int? TELEPLEDGES { get; set; }

        public Decimal? TELETOTPLEDGES { get; set; }

        public string GRPPKGCDEONE { get; set; }
        
        public string GRPPKGCDETWO { get; set; }
        
        public string GRPLSTNG { get; set; }
        
        public string GRPLST { get; set; }
        
    }

    public class MoneyPrintDetails
    {
        public int MID { get; set; }

        public int PID { get; set; }

        public string CCAUTHCODE { get; set; }

        public string CCRESPMSG  { get; set; }

        public string CCREFNO  { get; set; }

        public string DONORNAME { get; set; }

        public string STREET { get; set; }

        public string CITY { get; set; }

        public string STATE { get; set; }

        public string ZIP { get; set; }
        
        public string CKNO { get; set; }
                        
        public string MONYTYPE { get; set; }

        public string CARDTYPE { get; set; }

        public string CARDLAST4 { get; set; }

        public Decimal? AMT { get; set; }

        public DateTime? ENTRYDTE { get; set; }

        public string FUNDORGNAME { get; set; }

        public string FUNDORGADDR { get; set; }

                
    }

}

///****** Script for SelectTopNRows command from SSMS  ******/
//SELECT [MID]
//      ,p.[PID],
//      f.FUNDORGNAME,
//      f.FUNDORGADDR
//      ,ENTRYDTE,AMT 
//      ,CKNO, CCAUTHCODE, CCRESPMSG, CCREFNO,
//      CASE WHEN m.MONYTYPEID = 4 THEN 'Credit Card - Internet' ELSE (CASE WHEN m.CCPROCESS = 1 OR m.MONYTYPEID = 3 THEN 'Credit Card' ELSE 'Check' END) END as MONYTYPE,
//CASE WHEN m.MONYTYPEID = 4 OR m.MONYTYPEID = 3 OR m.CCPROCESS = 1 THEN (CASE LEFT(m.CKNO,1) WHEN '3' THEN 'AMEX' WHEN '4' THEN 'VISA' WHEN '5' THEN 'MASTER' WHEN '6' THEN 'DISCOVER' END) ELSE '' END as CARDTYPE,
//CASE WHEN m.MONYTYPEID = 4 OR m.MONYTYPEID = 3 OR m.CCPROCESS = 1 THEN RIGHT(m.CKNO,4) ELSE '' END as CARDLAST4,
//dbo.oFULLNAME(0, P.PREFIX, P.FNAME, P.MNAME, P.LNAME, P.SUFFIX) AS DONORNAME, 
//        ISNULL(A.STREET,'') AS STREET, 
//        ISNULL(A.CITY,'') AS CITY,
//        ISNULL(A.STATE,'') AS [STATE] ,
//        ISNULL(A.ZIP,'') AS [ZIP] 
		      
//  FROM [MONY] m
//  INNER JOIN People P ON m.pid = p.pid
//  INNER JOIN DMFUND F on m.FUNDID = f.FUNDID 
//  LEFT OUTER JOIN ADDRESS A ON P.PID = A.PID AND A.PRIME = 1  	
//  where m.MID = 132110


