﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Web;
using System.Configuration;
using System.Data.Entity;
using Ninject;
using Ninject.Web.Mvc;

using cmdiapp.n.core.Core;
using cmdiapp.n.core.Domain.Models;
using cmdiapp.n.core.Areas.crm.Domain.Models;
using cmdiapp.n.core.Areas.crm.Domain.ViewModels;
using cmdiapp.n.core.Areas.crm.Domain.Repositories;
using cmdiapp.n.core.Areas.crm.Domain.Services;
using cmdiapp.n.core.Areas.crm.Domain.Data;
using cmdiapp.n.core.Library;

namespace cmdiapp.n.core.Areas.crm.Core
{
    public abstract class crmSession
    {
        public static bool is_nonProfit_version()
        {
            //return (session.userSession.configItems.Where(a => a.Key == crmConstants.nonProfit).FirstOrDefault().Val.ToString().ToUpper() == "Y");
            /*
            IEnumerable<configItem> cItems = session.userSession.configItems.Where(a => a.Key == crmConstants.nonProfit);
            string isNonProfit = null;

            if (cItems.Count() > 0)
                isNonProfit = cItems.FirstOrDefault().Val.ToUpper();
            if (isNonProfit == "Y")
                return true; //Non-Profit,so NOT political
            return false;
            */
            return session.currentDomain_project.projectType.appVersion.ToLower() == "nonprofit";
        }

        #region [[ Used By Bhavesh ]]
                
        public static string WealthEng_Uname_Pass()
        {
            if (session.userSession.configItems.Where(a => a.Key == crmConstants.WealthEng_Uname_Pass).FirstOrDefault() != null)
            {
                return (session.userSession.configItems.Where(a => a.Key == crmConstants.WealthEng_Uname_Pass).FirstOrDefault().Val.ToString());
            }
            else
            {
                return "";
            }
        }

        public static string jfc_databaseId()
        {
            if (session.userSession.configItems.Where(a => a.Key == crmConstants.jfc_databaseId).FirstOrDefault() != null)
            {
                return (session.userSession.configItems.Where(a => a.Key == crmConstants.jfc_databaseId).FirstOrDefault().Val.ToString());
            }
            else
            {
                return "";
            }
        }
                
        //Used in Event Module - BRT
        public static string event_unit_field()
        {
            if (session.userSession.configItems.Where(a => a.Key == crmConstants.event_unit_field).FirstOrDefault() != null)
            {
                return (session.userSession.configItems.Where(a => a.Key == crmConstants.event_unit_field).FirstOrDefault().Val.ToString());
            }
            else
            {
                return "";
            }
        }

        //Used in People Search and Browser
        public static string people_class_field()
        {
            if (session.userSession.configItems.Where(a => a.Key == crmConstants.people_class_field).FirstOrDefault() != null)
            {
                return (session.userSession.configItems.Where(a => a.Key == crmConstants.people_class_field).FirstOrDefault().Val.ToString());
            }
            else
            {
                return "";
            }
        }

        //Used in People Search and Browser : STR1 column in the People Table...
        public static string people_str1_field()
        {
            if (session.userSession.configItems.Where(a => a.Key == crmConstants.people_str1_field).FirstOrDefault() != null)
            {
                return (session.userSession.configItems.Where(a => a.Key == crmConstants.people_str1_field).FirstOrDefault().Val.ToString());
            }
            else
            {
                return "";
            }
        }

        //Used in People Search and Browser : STR2 column in the People Table...
        public static string people_str2_field()
        {
            if (session.userSession.configItems.Where(a => a.Key == crmConstants.people_str2_field).FirstOrDefault() != null)
            {
                return (session.userSession.configItems.Where(a => a.Key == crmConstants.people_str2_field).FirstOrDefault().Val.ToString());
            }
            else
            {
                return "";
            }
        }

        //Used in People Browser : ACCTNO column in the People Table...
        public static string people_ACCTNO_field()
        {
            if (session.userSession.configItems.Where(a => a.Key == crmConstants.people_ACCTNO_field).FirstOrDefault() != null)
            {
                return (session.userSession.configItems.Where(a => a.Key == crmConstants.people_ACCTNO_field).FirstOrDefault().Val.ToString());
            }
            else
            {
                return "";
            }
        }

        //Used in People Search and Browser : NUM1 column in the People Table...
        public static string people_num1_field()
        {
            if (session.userSession.configItems.Where(a => a.Key == crmConstants.people_num1_field).FirstOrDefault() != null)
            {
                return (session.userSession.configItems.Where(a => a.Key == crmConstants.people_num1_field).FirstOrDefault().Val.ToString());
            }
            else
            {
                return "";
            }
        }



        //Used in Data Entry Module - BRT
        public static string show_received_date()
        {
            if (session.userSession.configItems.Where(a => a.Key == crmConstants.show_received_date).FirstOrDefault() != null)
            {
                return (session.userSession.configItems.Where(a => a.Key == crmConstants.show_received_date).FirstOrDefault().Val.ToString());
            }
            else
            {
                return "";
            }
        }

        //Used in Data Entry Module - BRT
        public static string cmdi_cc_processing_recur_option()
        {
            if (session.userSession.configItems.Where(a => a.Key == crmConstants.cmdi_cc_processing_recur_option).FirstOrDefault() != null)
            {
                return (session.userSession.configItems.Where(a => a.Key == crmConstants.cmdi_cc_processing_recur_option).FirstOrDefault().Val.ToString());
            }
            else
            {
                return "";
            }
        }
        //Used in People Module (Fundraised) - BRT
        public static string fundraise_Trackno_range()
        {
            if (session.userSession.configItems.Where(a => a.Key == crmConstants.fundraise_Trackno_range).FirstOrDefault() != null)
            {
                return (session.userSession.configItems.Where(a => a.Key == crmConstants.fundraise_Trackno_range).FirstOrDefault().Val.ToString());
            }
            else
            {
                return "";
            }
        }

        public static string fundraise_Previous_Cycle()
        {
            if (session.userSession.configItems.Where(a => a.Key == crmConstants.fundraise_Previous_Cycle).FirstOrDefault() != null)
            {
                return (session.userSession.configItems.Where(a => a.Key == crmConstants.fundraise_Previous_Cycle).FirstOrDefault().Val.ToString());
            }
            else
            {
                return "";
            }
        }

        public static string Fundraise_RaisedTowardsSandS()
        {
            if (session.userSession.configItems.Where(a => a.Key == crmConstants.Fundraise_RaisedTowardsSandS).FirstOrDefault() != null)
            {
                return (session.userSession.configItems.Where(a => a.Key == crmConstants.Fundraise_RaisedTowardsSandS).FirstOrDefault().Val.ToString());
            }
            else
            {
                return "";
            }
        }

        public static string rpmCampaignId()
        {
            if (session.userSession.configItems.Where(a => a.Key == crmConstants.rpmCampaignId).FirstOrDefault() != null)
            {
                return (session.userSession.configItems.Where(a => a.Key == crmConstants.rpmCampaignId).FirstOrDefault().Val.ToString());
            }
            else
            {
                return "";
            }
        }

        public static string rpmCampaignName()
        {
            if (session.userSession.configItems.Where(a => a.Key == crmConstants.rpmCampaignName).FirstOrDefault() != null)
            {
                return (session.userSession.configItems.Where(a => a.Key == crmConstants.rpmCampaignName).FirstOrDefault().Val.ToString());
            }
            else
            {
                return "";
            }
        }

        public static string fundraiser_downlines_reportName()
        {
            if (session.userSession.configItems.Where(a => a.Key == crmConstants.fundraiser_downlines_reportName).FirstOrDefault() != null)
            {
                return (session.userSession.configItems.Where(a => a.Key == crmConstants.fundraiser_downlines_reportName).FirstOrDefault().Val.ToString());
            }
            else
            {
                return "";
            }
        }
        #endregion

        #region [[ For VoterMapping ]]
        public static string vm_customerId()
        {
            if (session.userSession.configItems.Where(a => a.Key == crmConstants.vm_customerId).FirstOrDefault() != null)
            {
                return (session.userSession.configItems.Where(a => a.Key == crmConstants.vm_customerId).FirstOrDefault().Val.ToString());
            }
            else
            {
                return "";
            }
        }
        public static string vm_userCredential()
        {
            if (session.userSession.configItems.Where(a => a.Key == crmConstants.vm_userCredential).FirstOrDefault() != null)
            {
                return (session.userSession.configItems.Where(a => a.Key == crmConstants.vm_userCredential).FirstOrDefault().Val.ToString());
            }
            else
            {
                return "";
            }
        }
        #endregion

        public static short? UID()
        {
            if (HttpContext.Current.Session != null)
            {
                var ssUserUID = HttpContext.Current.Session["ssUserUID"];
                if (ssUserUID != null)
                    return Convert.ToInt16(ssUserUID.ToString());
            }

            #region [ Create a ssUSER record if new; Otherwise, return ssUSER.UID ]
            I_entity_crm _entity_crm = I_entityManager_crm.getEntity();
            ssUSER _user = _entity_crm.Single<ssUSER>(a => a.USERID == session.userSession.UserName);

            if (_user != null)
            {
                if (HttpContext.Current.Session != null)
                    HttpContext.Current.Session["ssUserUID"] = _user.UID;

                return _user.UID;
            }
            else 
            {
                _user = new ssUSER();
                _user.USERID = session.userSession.UserName;
                _user.FNAME = session.userSession.FName;
                _user.LNAME = session.userSession.LName;
                _user.ACTIVE = true;
                _user.CREATEDON = System.DateTime.Now;
                _user.eMail = session.userSession.Email;
                _entity_crm.Add(_user);
                _entity_crm.CommitChanges();

                if (_user.UID > 0)
                    return _user.UID;
                else
                    return 0;
            }
            #endregion

        }

        public static string configValue(string configKey)
        {
            configItem _value = session.userSession.configItems.Where(a => a.Key == configKey).FirstOrDefault();
            return (_value == null ? "" : _value.Val);
        }

        // jfc availability config val -- added by Lydia on 2014-01-10
        // Updated 8/17/2021 - eliminated JFC Availability configuration
        /*
        public static string Jfc_Availability()
        {

            if (session.userSession.configItems.Where(a => a.Key == crmConstants.Jfc_Availability).FirstOrDefault() != null)
            {
                return (session.userSession.configItems.Where(a => a.Key == crmConstants.Jfc_Availability).FirstOrDefault().Val.ToString());
            }
            else
            {
                return "";
            }
        }
        */

        // Solicitor label in Club Entry 
        public static string club_solicitor_label()
        {
            if (session.userSession.configItems.Where(a => a.Key == crmConstants.club_solicitor_label).FirstOrDefault() != null)
            {
                return (session.userSession.configItems.Where(a => a.Key == crmConstants.club_solicitor_label).FirstOrDefault().Val.ToString());
            }
            else
            {
                return "Solicitor";
            }
        }

        // Candidate label in Club Entry 
        public static string club_candidate_label()
        {
            if (session.userSession.configItems.Where(a => a.Key == crmConstants.club_candidate_label).FirstOrDefault() != null)
            {
                return (session.userSession.configItems.Where(a => a.Key == crmConstants.club_candidate_label).FirstOrDefault().Val.ToString());
            }
            else
            {
                return "Candidate";
            }
        }

        public static string ssConfigVal(string configCode)
        {
            I_entity_crm _entity_crm = I_entityManager_crm.getEntity();
            ssCONFIG _ssconfig = _entity_crm.Single<ssCONFIG>(a => a.CONFIGCODE == configCode);
            return (_ssconfig == null ? "" : _ssconfig.CONFIGVALUE);
        }

        //Used in Fundraising tab to default to Statement view
        public static string Money_StatementView()
        {
            if (session.userSession.configItems.Where(a => a.Key == crmConstants.Money_StatementView).FirstOrDefault() != null)
            {
                return (session.userSession.configItems.Where(a => a.Key == crmConstants.Money_StatementView).FirstOrDefault().Val.ToString());
            }
            else
            {
                return "";
            }
        }

        // Conduit Module
        public static string Conduit_Availability()
        {
            if (session.userSession.configItems.Where(a => a.Key == crmConstants.ConduitModule).FirstOrDefault() != null)
            {
                return (session.userSession.configItems.Where(a => a.Key == crmConstants.ConduitModule).FirstOrDefault().Val.ToString());
            }
            else
            {
                return "";
            }
        }

        // show check digit in open people, used for PeopleR2 model
        public static string showCheckDigit()
        {
            if (session.userSession.configItems.Where(a => a.Key == crmConstants.showCheckDigit).FirstOrDefault() != null)
            {
                return (session.userSession.configItems.Where(a => a.Key == crmConstants.showCheckDigit).FirstOrDefault().Val.ToString());
            }
            else
            {
                return "";
            }
        }

        // Account Code Label
        public static string acctcodeLabel()
        {
            if (session.userSession.configItems.Where(a => a.Key == crmConstants.acctcodeLabel).FirstOrDefault() != null)
            {
                return (session.userSession.configItems.Where(a => a.Key == crmConstants.acctcodeLabel).FirstOrDefault().Val.ToString());
            }
            else
            {
                return "";
            }
        }

        // Campgn Code Label
        public static string campgncodeLabel()
        {
            if (session.userSession.configItems.Where(a => a.Key == crmConstants.campgncodeLabel).FirstOrDefault() != null)
            {
                return (session.userSession.configItems.Where(a => a.Key == crmConstants.campgncodeLabel).FirstOrDefault().Val.ToString());
            }
            else
            {
                return "";
            }
        }

        #region [ (func)getAsOf - Returns a timestamp (eg. (As of Apr 17, 2018 2:05am EST)) of a specific data-point] 
        public static string getAsOf(string datapoint)
        {
            String asOf = "";
            String cacheKey = "";

            switch (datapoint)
            {
                case "fundraising":
                    cacheKey = session.get_cacheKey_for_appId_projectId("[asOf]fundraising>|");
                    var o = cacheManager.get(cacheKey);
                    if (o != null)
                        asOf = Convert.ToString(o);
                    else
                    {
                        I_entity_crm _entity_crm = I_entityManager_crm.getEntity();
                        asOf = _entity_crm.getContext().Database.SqlQuery<string>("SELECT '(As of '+ CONVERT(VARCHAR(50), MAX([timestamp]),100)+' EST)' FROM dbd_numbers WHERE dataPoint='Donations by Program'").FirstOrDefault();
                        cacheManager.insert(cacheKey, asOf, 43200);
                    }
                    return asOf;
                case "jfc":
                    cacheKey = session.get_cacheKey_for_appId_projectId("[asOf]jfc>|");
                    var o2 = cacheManager.get(cacheKey);
                    if (o2 != null)
                        asOf = Convert.ToString(o2);
                    else
                    {
                        I_entity_crm _entity_crm = I_entityManager_crm.getEntity();
                        asOf = _entity_crm.getContext().Database.SqlQuery<string>("SELECT CASE WHEN MAX([timestamp]) IS NULL THEN '' ELSE '(As of '+ CONVERT(VARCHAR(50), MAX([timestamp]),100)+' EST)' END FROM dbd_numbers WHERE dataPoint like 'JFC%'").FirstOrDefault();
                        cacheManager.insert(cacheKey, asOf, 43200);
                    }
                    return asOf;
                case "compliance":
                    cacheKey = session.get_cacheKey_for_appId_projectId("[asOf]compliance>|");
                    var o3 = cacheManager.get(cacheKey);
                    if (o3 != null)
                        asOf = Convert.ToString(o3);
                    else
                    {
                        I_entity_crm _entity_crm = I_entityManager_crm.getEntity();
                        asOf = _entity_crm.getContext().Database.SqlQuery<string>("SELECT CASE WHEN MAX([timestamp]) IS NULL THEN '' ELSE '(As of '+ CONVERT(VARCHAR(50), MAX([timestamp]),100)+' EST)' END FROM dbd_listData").FirstOrDefault();
                        cacheManager.insert(cacheKey, asOf, 43200);
                    }
                    return asOf;
            }

            return asOf;
        }
        #endregion
        #region [ (func)getProgramDescription - Returns a Program Description ] 
        public static string getProgramDescription(string program)
        {
            I_entity_crm _entity_crm = I_entityManager_crm.getEntity();
            //get description
            string description = "";
            PROGRAM_GROUP pg = _entity_crm.Single<PROGRAM_GROUP>(a => a.Descrip == program);
            if (pg != null)
            {
                description = program;
            }
            else
            {
                PROGRAM p = _entity_crm.Single<PROGRAM>(a => a.PROGTYPE == program);
                description = p.DESCRIP;
            }
            // end
            return description;
        }
        #endregion

        public static DateTime? ftdCycleStart()
        {
            I_entity_crm _entity_crm = I_entityManager_crm.getEntity();
            dm_Cycle _dmcycle = _entity_crm.Single<dm_Cycle>(a => a.cycleGroup == 1);
            if (_dmcycle == null)
                return null;
            else
                return _dmcycle.CYCLESTART;
        }

        public static DateTime? ftdCycleEnd()
        {
            I_entity_crm _entity_crm = I_entityManager_crm.getEntity();
            dm_Cycle _dmcycle = _entity_crm.Single<dm_Cycle>(a => a.cycleGroup == 1);
            if (_dmcycle == null)
                return null;
            else
                return _dmcycle.CYCLEEND;
        }

        public static int getCurrentFiscalYear()
        {
            var today = DateTime.Now;
            var thisYear = today.Year;
            var cycleEnd = ftdCycleEnd();
            if (cycleEnd == null)
            {
                return thisYear;
            }
            return thisYear == cycleEnd.Value.Year && today > cycleEnd
                ? thisYear + 1
                : thisYear;
        }
    }
}