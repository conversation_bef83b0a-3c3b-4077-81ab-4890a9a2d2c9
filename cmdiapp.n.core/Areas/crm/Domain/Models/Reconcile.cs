﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Data;
using System.Linq;
using System.Web.Mvc;
using System.Data.Linq;
using System.ComponentModel.DataAnnotations.Schema;
using cmdiapp.n.core.Areas.crm.Domain.Data;

using cmdiapp.n.core.Domain.ViewModels;

namespace cmdiapp.n.core.Areas.crm.Domain.Models
{
    [Table("v_reconcile_txn")]
    public class v_reconcile_txn : _entityBase_crm, iItemType
    {
        [Column]
        [Key]
        public int TXNID { get; set; }

        [Column]
        public int ENTITYID { get; set; }

        [Column]
        public string CODE { get; set; }

        [Column]
        public string VENDOR { get; set; }

        [Column]
        public DateTime TXNDTE { get; set; }

        [Column]
        public string MONYTYPE { get; set; }

        [Column]
        public string CHKNO { get; set; }

        [Column]
        public Nullable<decimal> RECAMT { get; set; }

        [Column]
        public Nullable<decimal> EXPAMT { get; set; }

        [Column]
        public Nullable<bool> isMEMO { get; set; }

        [Column]
        public Nullable<DateTime> CLEARDATE { get; set; }
    }

    [NotMapped]
    public class v_reconcile_ext : v_reconcile_txn
    {
        public int count_ { get; set; }

        public Int64 rowNo { get; set; }
    }

    [NotMapped]
    public class reconcile_selected : _entityBase_crm, iItemType
    {
        public List<string> selected { get; set; }

        public DateTime? date { get; set; }
    }
}
