﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.Mvc;

using cmdiapp.n.core.Presentation.Controllers;
using cmdiapp.n.core.Domain.Models;
using cmdiapp.n.core.Areas.crm.Core;
using cmdiapp.n.core.Areas.crm.Domain.Data;
using cmdiapp.n.core.Areas.crm.Domain.Models;
using cmdiapp.n.core.Domain.ViewModels;
using System.IO;
using cmdiapp.n.core.Library;

namespace cmdiapp.n.core.Areas.crm.Presentation.Controllers
{
    [Authorize]
    public class ConduitController : Controller
    {
        private readonly I_entity_crm _entity_crm;
        public ConduitController(I_entity_crm entity)
        {
            _entity_crm = entity;
        }
        #region [[ Conduit Distribution ]]
        [aElementAuthorize(AccessElement = "Conduit", AccessLevel = "a")]
        public ActionResult Distribution(string udlo)
        {
            return View(@"ConduitDistribQuickSearch");
        }
        #endregion

        #region [[ (HttpGet) ActionResult.search -> (View)Search ]]
        [HttpGet]
        [aElementAuthorize(AccessElement = "Conduit", AccessLevel = "a")]
        public ActionResult SearchDistrib(string udlo)
        {
            return Redirect("/query/search?qdefname=crm_newConduitDistrib&jsName=newConduitDistrib" + (!String.IsNullOrEmpty(udlo) && udlo.ToLower() == "y" ? "&udlo=y" : ""));
         
        }
        #endregion

        #region [[ Edit Conduit Distribution ]]
        [aElementAuthorize(AccessElement = "Conduit", AccessLevel = "e")]
        public ActionResult ConduitDistribEdit(string udlo, int? id)
        {
            pageInfo _pageInfo = new pageInfo();
            if (udlo == "y")
                _pageInfo.LayoutPage = crmConstants.DEFAULT_LAYOUT_BLANK;
            else
                _pageInfo.LayoutPage = "";

            _pageInfo.title = "Conduit Distribution";
            _pageInfo.title_short = "Edit";
            _pageInfo.pageEntityz_uniqueId = id.ToString();
            return View(@"ConduitDistribEdit", _pageInfo);
        }
        #endregion

        [aElementAuthorize(AccessElement = "Conduit", AccessLevel = "v")]
        public ActionResult ContributionDetails(string udlo, int? id, string status)
        {
            pageInfo _pageInfo = new pageInfo();
            if (udlo == "y")
                _pageInfo.LayoutPage = crmConstants.DEFAULT_LAYOUT_BLANK;
            else
                _pageInfo.LayoutPage = "";

            _pageInfo.title = "Contribution Details";
            _pageInfo.title_short = "View";
            _pageInfo.pageEntityz_uniqueId = id.ToString();
            _pageInfo.pageData1 = status;
            return View(@"ContributionDetails", _pageInfo);
        }

        [aElementAuthorize(AccessElement = "Conduit", AccessLevel = "v")]
        public ActionResult Open(string udlo, int? id)
        {
            pageInfo _pageInfo = new pageInfo();
            if (udlo == "y")
                _pageInfo.LayoutPage = crmConstants.DEFAULT_LAYOUT_BLANK;
            else
                _pageInfo.LayoutPage = "";

            _pageInfo.title = "Conduit";
            _pageInfo.title_short = "View";
            _pageInfo.pageEntityz_uniqueId = id.ToString();
            return View(@"Conduit", _pageInfo);
        }


        [HttpPost]
        public JsonResult UploadTemplateDoc(HttpPostedFileBase file, int Id)
        {
            genericResponse _response;
            try
            {
                //only word doc allowed
                if (!file.FileName.ToLower().Contains(".png"))
                {
                    _response = new genericResponse() { success = false, message = "Only PNG file is allowed!" };
                    return Json(_response, JsonRequestBehavior.AllowGet);
                }
                //Let us check for the file size first
                if (file.ContentLength <= (4 * 1024 * 1024))
                {
                    byte[] ByteArray = new byte[file.ContentLength];
                    file.InputStream.Read(ByteArray, 0, file.ContentLength);
                    x_gs_org _record = _entity_crm.Single<x_gs_org>(a => a.Id == Id);
                    _record.imageContent = ByteArray;

                    try
                    {
                        _entity_crm.Update<x_gs_org>(_record);
                        _entity_crm.CommitChanges();
                        //return filename
                        _response = new genericResponse() { success = true, message = file.FileName };
                    }
                    catch (Exception ex)
                    {
                        _response = new genericResponse() { success = false, message = "There is a problem uploading this file" };
                    }

                }
                else
                {
                    _response = new genericResponse() { success = false, message = "Attachment exceeded the maximum size of 4 MB.", };
                }

                return Json(_response, JsonRequestBehavior.AllowGet);
            }
            catch (Exception ex)
            {
                _response = new genericResponse() { success = false, message = "There is a problem processing attachment." };
                return Json(_response, JsonRequestBehavior.AllowGet);
            }
        }

        public ActionResult downloadTemplate(int id)
        {
            try
            {
                x_gs_org _record = _entity_crm.Single<x_gs_org>(a => a.Id == id);

                //Grab the Data
                byte[] png = _record.imageContent;
                string fileName = _record.orgName.Trim() + ".png";
                //Add header and show the file as Inline
                Response.AppendHeader("Content-Disposition", "attachment; filename=" + fileName);
                return File(png.ToArray(), util.get_contentType(fileName));
            }
            catch (FileNotFoundException)
            {
                throw new HttpException(404, string.Format("The file can not be processed at this time."));
            }
            catch (Exception ex)
            {
                throw new HttpException();
            }

        }
    }
}
