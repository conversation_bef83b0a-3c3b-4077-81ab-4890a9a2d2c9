﻿using cmdiapp.n.core.Areas.datax.Domain.Data;
using cmdiapp.n.core.Domain.ViewModels;
using Newtonsoft.Json;
using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.IO;
using System.Runtime.Serialization;

namespace cmdiapp.n.core.Areas.datax.Domain.Models
{
    /// <summary>
    /// Class that represents a data integration account.
    /// </summary>
    [Table("Account")]
    public class Account : _entityBase_datax, iItemType
    {
        /// <summary>
        /// The primary key.
        /// </summary>
        [Column, Key]
        public int Id { get; set; }

        /// <summary>
        /// The Id of the caller, e.g., Anedot.
        /// </summary>
        [Column]
        public int? callerId { get; set; }

        /// <summary>
        /// The base-64 key associated with this integration account that we generated.
        /// </summary>
        [Column, MaxLength(100)]
        public string appKey { get; set; }

        /// <summary>
        /// The name of the client account associated with this integration account.
        /// Comes from account name provided by caller.
        /// </summary>
        [Column, MaxLength(100)]
        public string client { get; set; }

        /// <summary>
        /// The authorization type. 'B' = base authorization
        /// </summary>
        [Column, MaxLength(1)]
        public string clientCallerAuth { get; set; }

        /// <summary>
        /// The caller's authorization key.
        /// </summary>
        [Column, MaxLength(250)]
        public string clientCallerCredential { get; set; }

        /// <summary>
        /// Unique ID of caller's client account associated with this integration.
        /// </summary>
        [Column, MaxLength(100)]
        public string clientCallerAccountId { get; set; }

        /// <summary>
        /// JSON string of Crimson configuration used when loading data from caller.
        /// </summary>
        [Column("loadInfo")]
        [IgnoreDataMember]
        public string loadInfoJson { get; set; }

        /// <summary>
        /// Deserialized class of <see cref="loadInfoJson"/>.
        /// Setter will serialize and set <see cref="loadInfoJson"/>.
        /// </summary>
        [NotMapped]
        public LoadInfo loadInfo
        {
            get
            {
                if (string.IsNullOrEmpty(loadInfoJson)) { return null; }
                JsonSerializer serializer = new JsonSerializer();
                JsonReader reader = new JsonTextReader(new StringReader(loadInfoJson));
                return serializer.Deserialize<LoadInfo>(reader);
            }
            set
            {
                JsonSerializer serializer = new JsonSerializer();
                StringWriter stringWriter = new StringWriter();
                ///	added timezone default EST on 6/21/2019                
                if (string.IsNullOrEmpty(value.timezone))
                {
                    value.timezone = "Eastern Standard Time";
                }
                serializer.Serialize(stringWriter, value);
                loadInfoJson = stringWriter.ToString();
            }
        }

        /// <summary>
        /// Whether the integration account is active.
        /// </summary>
        [Column]
        public bool? active { get; set; }

        /// <summary>
        /// UTC date account was created.
        /// </summary>
        [Column]
        public DateTime? createdAtUtc { get; set; }

        /// <summary>
        /// UTC date account was inactivated.
        /// </summary>
        [Column]
        public DateTime? inactivatedAtUtc { get; set; }
    }
}