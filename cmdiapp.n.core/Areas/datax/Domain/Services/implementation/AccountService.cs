﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Web;
using cmdiapp.n.core._Domain.Services;
using cmdiapp.n.core.Areas.datax.Domain.Data;
using cmdiapp.n.core.Areas.datax.Domain.Models;
using cmdiapp.n.core.Areas.datax.Domain.Models.ViewModels;
using cmdiapp.n.core.Core;
using cmdiapp.n.core.Domain.ViewModels;

namespace cmdiapp.n.core.Areas.datax.Domain.Services.implementation
{
    /// <summary>
    /// An implementation of <see cref="IAccountService"/>
    /// </summary>
    public class AccountService : IAccountService
    {
        private I_entity_datax _entity;
        private IDataxService _coreService;

        public AccountService(I_entity_datax entity, IDataxService coreService)
        {
            _entity = entity;
            _coreService = coreService;
        }

        public genericResponse Delete(int accountId)
        {
            Account account = Get(accountId);

            if (account == null)
            {
                return new genericResponse()
                {
                    success = false,
                    message = $"Could not find Account record with Id {accountId}."
                };
            }

            try
            {
                account.active = false;
                account.inactivatedAtUtc = DateTime.UtcNow;
                _entity.Update(account);
                _entity.CommitChanges();
                _coreService.RemoveAccountIdForCurrentProject(accountId, account.callerId ?? 0);
                return new genericResponse()
                {
                    success = true
                };
            }
            catch (Exception ex)
            {
                return new genericResponse()
                {
                    success = false,
                    message = "An error occurred.  Unable to delete Account record.",
                    messageKey = ex?.Message ?? ""
                };
            }
        }

        public Account Get(int accountId)
        {
            return _entity.Single<Account>(account => account.Id == accountId && (account.active ?? false));
        }

        public List<Account> Get(params int[] accountIds)
        {
            return _entity.All<Account>()
                .Where(account => accountIds.Contains(account.Id) && (account.active ?? false))
                .ToList();
        }

        private List<Account> GetWithCallerId(int callerId, params int[] accountIds)
        {
            return _entity.All<Account>()
                .Where(account => accountIds.Contains(account.Id)
                    && (account.active ?? false)
                    && account.callerId == callerId)
                .ToList();
        }

        public genericResponse Save(Account account)
        {
            // ensure that projectId is set to current project
            SetCrimsonProjectIdToCurrent(account);

            Account existingRecord = _entity.Single<Account>(acct => acct.Id == account.Id);
            bool isAdding = existingRecord == null;

            string errMessage = ValidateAccount(account, isAdding);
            if (!string.IsNullOrEmpty(errMessage))
            {
                return new genericResponse()
                {
                    success = false,
                    message = errMessage
                };
            }

            try
            {
                if (isAdding)
                {
                    account.createdAtUtc = DateTime.UtcNow;
                    _entity.Add(account);
                }
                else
                {
                    UpdateAccountProperties(existingRecord, account);
                    _entity.Update(existingRecord);
                }

                _entity.CommitChanges();
                if (isAdding)
                {
                    _coreService.AddAccountIdForCurrentProject(account.Id, account.callerId ?? 0);
                }

                genericResponse response = new genericResponse()
                {
                    success = true,
                    results = new List<iItemType>() { isAdding ? account : existingRecord }
                };
                return response;
            }
            catch (Exception ex)
            {
                return new genericResponse()
                {
                    success = false,
                    message = "An error occurred. Unable to save Account.",
                    messageKey = ex.Message
                };
            }
        }

        private string ValidateAccount(Account account, bool isAdding)
        {
            if (string.IsNullOrEmpty(account.appKey))
            {
                return "App Key is required.";
            }

            if (string.IsNullOrEmpty(account.clientCallerAccountId))
            {
                return "Please select an integrator account.";
            }

            LoadInfo loadInfo = account.loadInfo;

#pragma warning disable CS0472 // The result of the expression is always the same since a value of this type is never equal to 'null'
            if (loadInfo.fundId == null || loadInfo.fundId < 1)
#pragma warning restore CS0472 // The result of the expression is always the same since a value of this type is never equal to 'null'
            {
                return "Please select a Fund Code.";
            }

            if (isAdding)
            {
                // Check that there is not already a Crimson project linked to this callerClientAccount.
                // Since we can't parse loadInfoJson in LINQ to SQL query,
                // get all accounts with same caller account into memory and then check their loadInfo.crimsonProjectIds
                List<Account> sameCallerAccount = _entity.All<Account>()
                    .Where(a => a.clientCallerAccountId == account.clientCallerAccountId
                        && a.Id != account.Id
                        && (a.active ?? false)).ToList();
                bool isDuplicateAccount = sameCallerAccount
                    .Any(a => a.loadInfo.crimsonProjectId == loadInfo.crimsonProjectId);
                if (isDuplicateAccount)
                {
                    string message = "There is already a Crimson Integration Account linked to this " +
                        "integrator account for this project. Please edit or delete that Integration Account.";
                    return message;
                }
            }

            return "";

        }

        private void UpdateAccountProperties(Account existing, Account updated)
        {
            // loadInfoJson will be handled by loadInfo setter
            string[] exceptions = new string[] { "Id", "loadInfoJson", "createdAtUtc" };
            foreach (var prop in existing.GetType().GetProperties())
            {
                if (!exceptions.Contains(prop.Name))
                {
                    prop.SetValue(existing, prop.GetValue(updated));
                }
            }
        }

        private void SetCrimsonProjectIdToCurrent(Account account)
        {
            // deserialized JSON by getter
            LoadInfo loadInfo = account.loadInfo;
            // re-serialize by setter
            account.loadInfo = new LoadInfo()
            {
                fundId = loadInfo.fundId,
                batchNo = loadInfo.batchNo,
                sourceCode = loadInfo.sourceCode,
                channel = loadInfo.channel,
                centerCode = loadInfo.centerCode,
                monyType = loadInfo.monyType,
                timezone = loadInfo.timezone,
                crimsonProjectId = session.currentDomain_project.projectId,
            };
        }

        public List<Account> GetAccountsForCurrentProject()
        {
            return Get(_coreService.GetAccountIdsForCurrentProject());
        }

        public List<Account> GetAccountsForCurrentProject(int callerId)
        {
            return GetWithCallerId(callerId, _coreService.GetAccountIdsForCurrentProject(callerId));
        }

        public int GetCountofAccountsForCurrentProject(int callerId)
        {
            return GetWithCallerId(callerId, _coreService.GetAccountIdsForCurrentProject(callerId)).Count;
        }

        public Account GetNewAccountModel(NewAccountInfo info)
        {
            if (info == null)
            {
                return new Account();
            }

            return new Account()
            {
                Id = 0,
                callerId = info.CallerId,
                appKey = Convert.ToBase64String(Encoding.UTF8.GetBytes(Guid.NewGuid().ToString())),
                client = info.CallerAccount?.Name ?? session.currentDomain_project.name,
                clientCallerCredential = info.Credentials?.Base64EncodedCredentials,
                clientCallerAccountId = info.CallerAccount?.Id ?? Guid.NewGuid().ToString(),
                active = true,
                // assume that if credentials are passed, that it's Basic, else there's no auth
                clientCallerAuth = string.IsNullOrEmpty(info.Credentials?.PassKey) ? null : "B",
                loadInfo = new LoadInfo()
                {
                    crimsonProjectId = session.currentDomain_projectId                    
                }
            };

        }        

        public void AddAccount(Account account)
        {
            _entity.Add(account);
            _entity.CommitChanges();
        }
    }
}