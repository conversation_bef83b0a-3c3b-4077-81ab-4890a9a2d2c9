﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Data.Linq;
using cmdiapp.n.core.Areas.crm.Domain.Models;

namespace cmdiapp.n.core.Areas.crm.Domain.Repositories
{
    internal class vmDemographicRepository : _baseRepository<vm_Demographic>, IvmDemographicRepository
    {
        public vmDemographicRepository(I__DBFactory databaseFactory)  : base(databaseFactory)
        {

        }
    }

    internal class vmVoterRepository : _baseRepository<vm_Voter>, IvmVoterRepository
    {
        public vmVoterRepository(I__DBFactory databaseFactory) : base(databaseFactory)
        {

        }
    }


    internal class vmListInfoRepository : _baseRepository<vm_ListInfo>, IvmListInfoRepository
    {
        public vmListInfoRepository(I__DBFactory databaseFactory) : base(databaseFactory)
        {

        }
    }


    internal class vmVoteHistoryRepository : _baseRepository<vm_VoteHistory>, IvmVoteHistoryRepository
    {
        public vmVoteHistoryRepository(I__DBFactory databaseFactory) : base(databaseFactory)
        {

        }
    }

    internal class vmjtListvoterRepository : _baseRepository<vm_jt_Listvoter>, IvmjtListvoterRepository
    {
        public vmjtListvoterRepository(I__DBFactory databaseFactory) : base(databaseFactory)
        {

        }
    }
}