﻿using System.Collections.Generic;
using System.Web.Mvc;

using cmdiapp.n.core.Areas.crm.Domain.Models;
using cmdiapp.n.core.Areas.crm.Domain.ViewModels;
using cmdiapp.n.core.Areas.crm.Domain.Repositories;
using System;
using cmdiapp.n.core.Areas.query.Domain.Models;
using cmdiapp.n.core.Areas.crm.ViewModels;
using cmdiapp.n.core.Domain.ViewModels;

namespace cmdiapp.n.core.Areas.crm.Domain.Services
{
    public interface ImonyService
    {
        List<v_mony_1> get_v_mony_1(int pid);
        string availableBatchNo(string date);

        #region [[ By Bhavesh for Fundraised Page ]]
        void Add(jtFNDRGRP _jtFNDRGRP);
        void Delete(jtFNDRGRP _jtFNDRGRP);
        genericResponse SaveFundraise(FUNDRAISE fundraise);
        lkFNDRGRP get_lkFNDRGRP(string FNDRGRP);
        jtFNDRGRP get_jtfndgroup(int FUNDRAISEID, int FNDRGRPID);
        #endregion

        Tuple<int, List<MonyR>> getMonyRWithCount(
            int pid, int page, int pageSize, QueryRuntimeSort sort, string fundCode, string srceCode, string progType, string date);
        Tuple<int, List<MonyStatement>> getMonyStatement(
            int pid, int page, int pageSize, QueryRuntimeSort sort, string fundCode, string adjType, string batchDate, string adjDate);
        Tuple<int, List<BundlerGiftDisplay>> MonyByTrackNoCountAndResults(int trackNo, int page, int pageSize, QueryRuntimeSort sort);

        IEnumerable<MonyGraphPoint> GetDonorGivingHistory(int pid);

        ConsecutiveDonorYears GetConsecutiveDonorYears(int pid);

        List<MonyForAdj> GetMonyForAdjust(int pid);
        bool bulkRedesignation(bulkRedesignationData data);
    }
}
