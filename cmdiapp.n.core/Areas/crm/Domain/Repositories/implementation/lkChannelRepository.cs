﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Data.Linq;
using cmdiapp.n.core.Areas.crm.Domain.Models;

namespace cmdiapp.n.core.Areas.crm.Domain.Repositories
{
    internal class lkChannelRepository : _baseRepository<lkChannel>, IlkChannelRepository
    {
        public lkChannelRepository(I__DBFactory databaseFactory) : base(databaseFactory)
        {

        }
    }

    internal class lkTargetChannelRepository : _baseRepository<lkTargetChannel>, IlkTargetChannelRepository
    {
        public lkTargetChannelRepository(I__DBFactory databaseFactory)   : base(databaseFactory)
        {

        }
    }
}
