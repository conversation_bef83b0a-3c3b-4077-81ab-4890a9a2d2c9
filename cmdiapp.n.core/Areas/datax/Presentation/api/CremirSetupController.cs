﻿using cmdiapp.n.core.Areas.datax.Domain.Models;
using cmdiapp.n.core.Areas.datax.Domain.Models.ViewModels;
using cmdiapp.n.core.Areas.datax.Domain.Services;
using cmdiapp.n.core.Core.App_Start;
using cmdiapp.n.core.Presentation.Controllers;
using Ninject;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Threading.Tasks;
using System.Web.Http;

namespace cmdiapp.n.core.Areas.datax.Presentation.api
{
    [apiAuthorize]
    [RoutePrefix("datax/api/CremirSetup")]
    public class CremirSetupController : ApiController
    {
        private readonly ICremirSetupService _service;

        public CremirSetupController()
        {
            _service = NinjectMVC.kernel.Get<ICremirSetupService>();
        }

        [HttpPost]
        [Route("Start/{callerId}")]
        public Task<string> StartAsync(int callerId)
        {
            return _service.StartAsync(callerId);
        }

        [HttpGet]
        [Route("AuthorizationUrl/{token}")]
        public Task<string> GetAuthorizationUrl(string token)
        {
            if (string.IsNullOrEmpty(token)) { return Task.FromResult(string.Empty); }

            return _service.GetAuthorizationRequestUrlAsync(token);
        }

        [HttpGet]
        [Route("Status/{token}")]
        public Task<CremirSetupStatus> StatusAsync(string token)
        {
            if (string.IsNullOrEmpty(token))
            {
                return Task.FromResult(new CremirSetupStatus()
                {
                    StatusId = 0,
                    Status = "Failed",
                    Message = "No access token provided."
                });
            }
            return _service.CheckStatusAsync(token);
        }
    }
}
