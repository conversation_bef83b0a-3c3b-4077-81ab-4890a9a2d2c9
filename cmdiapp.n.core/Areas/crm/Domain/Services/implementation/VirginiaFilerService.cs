﻿using cmdiapp.n.core._Domain.ViewModels;
using cmdiapp.n.core.Areas.crm.Core;
using cmdiapp.n.core.Areas.crm.Domain.Data;
using cmdiapp.n.core.Areas.crm.Domain.Models;
using cmdiapp.n.core.Areas.crm.ViewModels;
using cmdiapp.n.core.Library;
using System;
using System.Collections.Generic;
using System.Data.Entity;
using System.Linq;
using System.Threading.Tasks;

namespace cmdiapp.n.core.Areas.crm.Domain.Services.implementation
{
    public class VirginiaFilerService : IVirginiaFilerService
    {
        private readonly I_entity_crm _entity;
        private readonly IErrorLogger _errorLogger;
        private readonly ICrmSession _crmSession;

        public VirginiaFilerService(
            I_entity_crm entity,
            IErrorLogger errorLogger,
            ICrmSession crmSession)
        {
            _entity = entity;
            _errorLogger = errorLogger;
            _crmSession = crmSession;
        }

        public Task<List<VirginiaFilerOption>> GetFilerOptionsAsync()
        {
            try
            {
                return _entity.All<VirginiaFiler>()
                    .Select(filer => new VirginiaFilerOption
                    {
                        Id = filer.Id,
                        CommitteeName = filer.CommitteeName
                    }).ToListAsync();
            }
            catch (Exception ex)
            {
                _errorLogger.Log($"Failed to get VirginiaFilers due to {ex.GetType().Name}: {ex.Message}");
                return Task.FromResult(new List<VirginiaFilerOption>());
            }
        }

        public Task<VirginiaFiler> GetFilerAsync(int id)
        {
            try
            {
                return _entity.All<VirginiaFiler>().Where(filer => filer.Id == id).FirstOrDefaultAsync();
            }
            catch (Exception ex)
            {
                _errorLogger.Log($"Failed to get first VirginiaFiler due to {ex.GetType().Name}: {ex.Message}");
                return Task.FromResult<VirginiaFiler>(null);
            }
        }

        public async Task<GenericSuccessResponse<VirginiaFiler>> SaveAsync(VirginiaFiler filer)
        {
            try
            {
                var errors = ValidateFiler(filer);
                if (errors.Any())
                {
                    string errorMessage = $"Please address the following errors: {string.Join("; ", errors)}";
                    return new GenericSuccessResponse<VirginiaFiler>()
                    {
                        Success = false,
                        Message = errorMessage
                    };
                }

                filer.UpdatedOn = DateTime.Now;
                filer.UpdatedByUid = _crmSession.GetCurrentUid() ?? 0;
                if (filer.OfficeId == 0) filer.OfficeId = null;
                var existing = await GetFilerAsync(filer.Id);
                if (existing == null)
                {
                    filer.CreatedOn = filer.UpdatedOn;
                    _entity.Add(filer);
                }
                else
                {
                    UpdateFiler(existing, filer);
                    _entity.Update(existing);
                }
                _entity.CommitChanges();

                return new GenericSuccessResponse<VirginiaFiler>()
                {
                    Success = true,
                    Results = existing ?? filer
                };
            }
            catch (Exception ex)
            {
                _errorLogger.Log($"Failed to get save VirginiaFiler due to {ex.GetType().Name}: {ex.Message}");
                return new GenericSuccessResponse<VirginiaFiler>()
                {
                    Success = false,
                    Message = "An error occurred. Unable to save filer."
#if DEBUG
                    ,MessageKey = $"{ex.GetType().Name}: {ex.Message}"
#endif
                };
            }
        }

        public async Task<GenericSuccessResponse<int>> DeleteAsync(int id)
        {
            var existing = await GetFilerAsync(id);
            if (existing == null)
            {
                return new GenericSuccessResponse<int>()
                {
                    Success = false,
                    Message = $"There is no filer record with id {id}."
                };
            }

            try
            {
                _entity.Delete(existing);
                _entity.CommitChanges();
                return new GenericSuccessResponse<int>()
                {
                    Success = true,
                    Results = id
                };
            }
            catch (Exception ex)
            {
                _errorLogger.Log($"Failed to delete VirginiaFiler {id} due to {ex.GetType().Name}: {ex.Message}");
                return new GenericSuccessResponse<int>()
                {
                    Success = false,
                    Message = "An error occurred. Unable to delete filer."
#if DEBUG
                    ,MessageKey = $"{ex.GetType().Name}: {ex.Message}"
#endif
                };
            }
        }

        public Task<List<VirginiaOffice>> GetOfficesAsync()
        {
            return _entity.All<VirginiaOffice>().ToListAsync();
        }

        private List<string> ValidateFiler(VirginiaFiler filer)
        {
            var errors = new List<string>();
            if (filer == null)
            {
                errors.Add("Filer cannot be null");
                // Early return if null, since all other
                // validation will fail as well.
                return errors;
            }

            if (string.IsNullOrEmpty(filer.CommitteeCode))
            {
                errors.Add("Committee Code is required");
            }

            if (string.IsNullOrEmpty(filer.CommitteeName))
            {
                errors.Add("Committee Name is required");
            }

            if (string.IsNullOrEmpty(filer.ElectionCycle))
            {
                errors.Add("Election Cycle is required");
            }

            return errors;
        }

        private void UpdateFiler(VirginiaFiler existing, VirginiaFiler newFiler)
        {
            var excludedProperties = new string[2] { nameof(VirginiaFiler.Id), nameof(VirginiaFiler.CreatedOn) };
            var properties = existing.GetType().GetProperties();
            foreach (var property in properties)
            {
                if (!excludedProperties.Contains(property.Name))
                {
                    property.SetValue(existing, property.GetValue(newFiler));
                }
            }
        }
    }
}