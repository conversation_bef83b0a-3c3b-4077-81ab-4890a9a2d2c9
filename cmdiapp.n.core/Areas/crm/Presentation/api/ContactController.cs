﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Net;
using System.Web.Http;
using System.Text.RegularExpressions;

using Ninject;
using Ninject.Web.Mvc;

using cmdiapp.n.core.Domain.ViewModels;
using cmdiapp.n.core.Areas.crm.Core;
using cmdiapp.n.core.Areas.crm.Domain.Models;
using cmdiapp.n.core.Areas.crm.Domain.ViewModels;
using cmdiapp.n.core.Areas.crm.Domain.Data;
using cmdiapp.n.core.Areas.crm.Domain.Repositories;
using cmdiapp.n.core.Presentation.Controllers;

namespace cmdiapp.n.core.Areas.crm.Presentation.Controllers.api
{
    [Authorize]
    [apiAuthorize(AccessElement = "cmdiapp.dms.People_Contact", AccessLevel = "v")]
    public class ContactController : ApiController
    {
        private I_entity_crm _entity_crm;

        public ContactController()
        {
            _entity_crm = I_entityManager_crm.getEntity();
        }
        //For Add
        [HttpGet, Route("crm/api/Contacts/GetByPid/{id}")]  // PID
        public List<Contact> GetByPid(int id)
        {
            return _entity_crm.All<Contact>().Where(c => c.PID == id).OrderByDescending(p => p.PRIME).ToList();
        }

        //For Edit
        [HttpGet, Route("crm/api/Contacts/GetByContactID/{contactID}")]  // Contact ID
        public Contact GetByContactID(int? contactID)
        {
            if (contactID >= 0)
            {
                Contact _contact = _entity_crm.All<Contact>().Where(c => c.CONTACTID == contactID).FirstOrDefault();
                return _contact;
            }
            else
                return new Contact { CONTACTID = -1 };
        }

        [HttpPost, Route("crm/api/Contacts/Save")]
        [apiAuthorize(AccessElement = "cmdiapp.dms.People_Contact", AccessLevel = "e")]
        public genericResponse Save(Contact _contact)
        {
            int _contactid = _contact.CONTACTID;
            Contact _record = _entity_crm.Single<Contact>(a => a.CONTACTID == _contactid);
            if (_record == null || _contactid < 0)
            {
                _record = new Contact();
            }
            
            try
            {
                    // existing record
                    _record.CONTACTID =_contact.CONTACTID;
                    _record.PID = _contact.PID;
                    _record.PREFIX = _contact.PREFIX;
                    _record.FNAME = _contact.FNAME;
                    _record.MNAME = _contact.MNAME;
                    _record.LNAME = _contact.LNAME;
                    _record.SUFFIX = _contact.SUFFIX;
                    _record.ORGANIZATION = _contact.ORGANIZATION;
                    _record.TITLE = _contact.TITLE;
                    _record.STREET = _contact.STREET;
                    _record.ADDR1 = _contact.ADDR1;
                    _record.ADDR2 = _contact.ADDR2;
                    _record.CITY = _contact.CITY;
                    _record.STATE = _contact.STATE;
                    _record.ZIP = _contact.ZIP;
                    _record.PLUS4 = _contact.PLUS4;
                    _record.EMAIL = _contact.EMAIL;
                    _record.HMPHN = _contact.HMPHN == null ? null : Regex.Replace(_contact.HMPHN, @"[^\w]", "");
                    _record.BSPHN = _contact.BSPHN == null ? null : Regex.Replace(_contact.BSPHN, @"[^\w]", "");
                    _record.FAX = _contact.FAX == null ? null : Regex.Replace(_contact.FAX, @"[^\w]", "");
                    _record.CELL = _contact.CELL == null ? null : Regex.Replace(_contact.CELL, @"[^\w]", "");
                    _record.COMMENT = _contact.COMMENT;
                    _record.PRIME = _contact.PRIME;
                    _record.INFSALUT = _contact.INFSALUT;
                    _record.ASSISTANT = _contact.ASSISTANT;
                    _record.UPDATEDON = System.DateTime.Now;
                    _record.updating_uid = crmSession.UID();
                    _record.INACTIVE = _contact.INACTIVE;
                    _record.INACTIVEDTE = _contact.INACTIVEDTE;
                    if (_record != null && _contactid >= 0)
                        _entity_crm.Update(_record);
                    else
                    {
                        _record.UID = crmSession.UID();
                        _entity_crm.Add(_record);
                    }
              
                _entity_crm.CommitChanges();
                _contactid = _record.CONTACTID;

                if (_contactid <= 0)//meaning new record
                {
                    List<iItemType> _dataset = new List<iItemType>();
                    _dataset.Add(_contact);

                    genericResponse _response = new genericResponse() { success = false, __count = _dataset.Count, results = _dataset.ToList() };
                    return _response;
                }
                else
                {
                    _contact = _entity_crm.Single<Contact>(a => a.CONTACTID == _contactid);

                    List<iItemType> _dataset = new List<iItemType>();
                    _dataset.Add(_contact);

                    genericResponse _response = new genericResponse() { success = true, __count = _dataset.Count, results = _dataset.ToList() };
                    return _response;
                }
            }
            catch
            {
                genericResponse _response = new genericResponse() { success = false, __count = 0 };
                return _response;
            }
        }

        [HttpPost, Route("crm/api/Contacts/Delete")]
        [apiAuthorize(AccessElement = "cmdiapp.dms.People_Contact", AccessLevel = "d")]
        public genericResponse Delete(Contact _contact)
        {
            try
            {
                int _contactid = _contact.CONTACTID;
                //delete record from jtContactFlag
                _entity_crm.Delete<jtCONTFLAG>(a => a.CONTACTID == _contactid);
                _entity_crm.CommitChanges();

                //now delete record from contact
                _entity_crm.Delete<Contact>(a => a.CONTACTID == _contactid);
                _entity_crm.CommitChanges();

                genericResponse _response = new genericResponse() { success = true, __count = 0 };
                return _response;
            }
            catch
            {
                genericResponse _response = new genericResponse() { success = false, __count = 0 };
                return _response;
            }
        }

        [HttpGet, Route("crm/api/jtcontactFlagIds/{contactID}")]
        public List<string> GetbyContactFlagID(int contactID)
        {
            List<jtCONTFLAG> all_jtcontactFlags = _entity_crm.All<jtCONTFLAG>().Where(a => a.CONTACTID == contactID).ToList();
            //["",""]
            List<string> all_jtcontactFlagIDs = new List<string>();

            foreach (jtCONTFLAG jtcFlag in all_jtcontactFlags)
            {
                all_jtcontactFlagIDs.Add(jtcFlag.CONTFLAGID.ToString());
            }

            return all_jtcontactFlagIDs;
        }

        [HttpPost, Route("crm/api/GetContactFlagsByIDs")]
        public List<ContactFlags> GetContactFlagsByIDs(List<ContactFlags> contactFlags)
        {
            if (contactFlags.Count() > 0)
            {
                for (var i = 0; i < contactFlags.Count(); i++)
                {
                    int cid = contactFlags[i].ContactID;
                    List<jtCONTFLAG> all_jtcontactFlags = _entity_crm.All<jtCONTFLAG>().Where(a => a.CONTACTID == cid).ToList();
                    //["",""]
                    List<string> jtcontactFlags = new List<string>();

                    foreach (jtCONTFLAG jtcFlag in all_jtcontactFlags)
                    {
                        jtcontactFlags.Add(jtcFlag.lkCONTFLAG.CONTFLAG.ToString());
                    }
                    contactFlags[i].ContactFlag = jtcontactFlags;
                }

            }
            return contactFlags;
        }

        //for multi-selection
        #region [ crm/api/jtCONTFLAG ] SaveJtcFlag
        [HttpPost, Route("crm/api/jtCONTFLAG/SaveJtcFlag")]
        [apiAuthorize(AccessElement = "cmdiapp.dms.People_Contact", AccessLevel = "e")]
        public genericResponse SaveJtcFlag(jtCONTFLAG _jtCONTFLAG)
        {
            try
            {
                int _cflagid = _jtCONTFLAG.CONTFLAGID;
                int _cid = _jtCONTFLAG.CONTACTID;
                jtCONTFLAG _record = _entity_crm.Single<jtCONTFLAG>(a => a.CONTFLAGID == _cflagid && a.CONTACTID == _cid);

                // new record
                _record = new jtCONTFLAG();
                //_record.updating_uid = crmSession.UID();
                _record.UPDATEDON = System.DateTime.Now;
                _record.CONTACTID = _jtCONTFLAG.CONTACTID;
                _record.CONTFLAGID = _jtCONTFLAG.CONTFLAGID;
                _entity_crm.Add(_record);

                _entity_crm.CommitChanges();
                _jtCONTFLAG.UPDATEDON = _record.UPDATEDON;
                _cflagid = _record.CONTFLAGID;
                

                if (_cflagid <= 0)
                {
                    List<iItemType> _dataset = new List<iItemType>();
                    
                    _dataset.Add(_jtCONTFLAG);

                    genericResponse _response = new genericResponse() { success = false, __count = _dataset.Count, results = _dataset.ToList() };
                    return _response;
                }
                else
                {
                    _jtCONTFLAG = _entity_crm.Single<jtCONTFLAG>(a => a.CONTFLAGID == _cflagid && a.CONTACTID == _cid);

                    List<iItemType> _dataset = new List<iItemType>();
                    _dataset.Add(_jtCONTFLAG);

                    genericResponse _response = new genericResponse() { success = true, __count = _dataset.Count, results = _dataset.ToList() };
                    return _response;
                }
            }
            catch
            {
                genericResponse _response = new genericResponse() { success = false, __count = 0 };
                return _response;
            }
        }
        #endregion

        #region [ crm/api/jtCONTFLAG ] DeleteJtcFLAG
        [HttpPost, Route("crm/api/jtCONTFLAG/DeleteJtcFLAG")]
        [apiAuthorize(AccessElement = "cmdiapp.dms.People_Contact", AccessLevel = "d")]
        public genericResponse DeleteJtcFLAG(jtCONTFLAG _jtCONTFLAG)
        {
            try
            {
                int _cflagid = _jtCONTFLAG.CONTFLAGID;
                int _cid = _jtCONTFLAG.CONTACTID;
                _entity_crm.Delete<jtCONTFLAG>(a => a.CONTFLAGID == _cflagid && a.CONTACTID == _cid);
                _entity_crm.CommitChanges();

                genericResponse _response = new genericResponse() { success = true, __count = 0 };
                return _response;
            }
            catch
            {
                genericResponse _response = new genericResponse() { success = false, __count = 0 };
                return _response;
            }
        }
        #endregion
    }
}