﻿using Ninject;
using cmdiapp.n.core.Core.App_Start;

namespace cmdiapp.n.core.Areas.datax.Domain.Data
{
    public static class entityManager_datax
    {
        public static I_entity_datax getEntity()
        {
            return NinjectMVC.kernel.Get<I_entity_datax>();
        }

        public static I_entity_datax getEntity(string connectionString)
        {
            return NinjectMVC.kernel.Get<I_entity_datax>(new Ninject.Parameters.ConstructorArgument("connectionString", connectionString, true));
        }
    }    
}
