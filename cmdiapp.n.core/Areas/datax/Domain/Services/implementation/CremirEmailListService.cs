﻿using System;
using System.Collections.Generic;
using System.Data.SqlClient;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;
using cmdiapp.n.core.Areas.crm.Domain.Data;
using cmdiapp.n.core.Areas.crm.Domain.Models;
using cmdiapp.n.core.Areas.datax.Domain.Models.ViewModels;
using cmdiapp.n.core.Areas.query.Domain.Models;
using cmdiapp.n.core.Core;
using cmdiapp.n.core.Domain.Models;
using cmdiapp.n.core.Domain.ViewModels;
using cmdiapp.n.core.Library;
using Newtonsoft.Json;

namespace cmdiapp.n.core.Areas.datax.Domain.Services.implementation
{
    /// <summary>
    /// Implementation of <see cref="ICremirEmailListService"/>
    /// </summary>
    public class CremirEmailListService : ICremirEmailListService
    {
        private readonly ICremirUtilService _util;
        private readonly I_entity_crm _entity;
        private readonly userSession _user;

        public CremirEmailListService(ICremirUtilService util, I_entity_crm entity)
        {
            _util = util;
            _entity = entity;
            _user = session.userSession;
        }
        /// <summary>
        /// Creates a new email list
        /// </summary>
        /// <param name="emailList">the email list to create</param>
        /// <param name="queryInstance">the query instance to generate SQL from for selecting records</param>
        /// <returns>a <see cref="Task"/> indicating success of creation</returns>
        /// <exception cref="ArgumentNullException">if <paramref name="emailList"/> is null</exception>
        public async Task<genericResponse> CreateAsync(CremirEmailList emailList, QueryInstanceInfo queryInstance)
        {
            if (emailList == null) { throw new ArgumentNullException(nameof(emailList)); }
            if (emailList.FrequencyType == "P" && !IsValidTimeSpanString(emailList.UpdateInterval))
            {
                return new genericResponse()
                {
                    success = false,
                    message = $"'{emailList.UpdateInterval}' is not a valid update interval."
                };
            }

            try
            {
                // if SQL is null, this is treated as Master list
                string querySql = queryInstance == null ? null : new QueryRuntime(queryInstance).sql();
                string customFieldIds = string.Join(
                            ",",
                            emailList.CustomFields?.Select(field => field.Id).ToList() ?? new List<int>());
                var createProcQuery = _entity.getContext().Database.SqlQuery<EmailListCreateResult>(
                    "EXEC [dbo].[x_es_createEmailList] @sql, @frequencyType, @customFieldIds",
                    new SqlParameter("@sql", querySql),
                    new SqlParameter("@frequencyType", emailList.FrequencyType),
                    new SqlParameter("@customFieldIds", customFieldIds));
                EmailListCreateResult createResult = await createProcQuery.FirstOrDefaultAsync();

                if (createResult?.Success ?? false)
                {
                    emailList.ListId = createResult.ListId;
                    // add crimson user id
                    emailList.CrimsonUserId = _user.UserId;
                    var request = new HttpRequestMessage(HttpMethod.Post, $"{_util.BaseUrl}/api/EmailLists")
                    {
                        Content = new StringContent(
                            JsonConvert.SerializeObject(emailList),
                            Encoding.UTF8,
                            "application/json")
                    };
                    return await SendRequestToCremirAsync<genericResponse>(request);
                }
                else
                {
                    return new genericResponse()
                    {
                        success = false,
                        message = "An error occurred. Unable to create email list.",
                        messageKey = createResult?.Message ?? "Proc failed and returned no error message"
                    };
                }
            }
            catch (Exception ex)
            {
                LogException(ex);
                return new genericResponse()
                {
                    success = false,
                    message = "An error occurred. Unable to create email list.",
                    messageKey = $"{ex.GetType().Name}: {ex.Message}"
                };
            }
        }
        /// <summary>
        /// Deletes an email list
        /// </summary>
        /// <param name="accountId">the account Id of the list</param>
        /// <param name="listId">the list Id of the list</param>
        /// <returns>a <see cref="Task"/> indicating success of deletion</returns>
        public async Task<genericResponse> DeleteAsync(int accountId, string listId)
        {
            try
            {
                var deleteProcQuery = _entity.getContext().Database.SqlQuery<EmailListProcResult>(
                    "EXEC [dbo].[x_es_deleteEmailList] @listId",
                    new SqlParameter("@listId", listId));
                EmailListProcResult queryResult = await deleteProcQuery.FirstOrDefaultAsync();
                if (queryResult?.Success ?? false)
                {
                    var request = new HttpRequestMessage(
                        HttpMethod.Delete,
                        $"{_util.BaseUrl}/api/EmailLists/{accountId}/{listId}");
                    return await SendRequestToCremirAsync<genericResponse>(request);
                }
                else
                {
                    return new genericResponse()
                    {
                        success = false,
                        message = "An error occurred. Unable to delete email list.",
                        messageKey = queryResult?.Message ?? "Proc failed and returned no error message"
                    };
                }
            }
            catch (Exception ex)
            {
                LogException(ex);
                return new genericResponse()
                {
                    success = false,
                    message = "An error occurred. Unable to delete email list.",
                    messageKey = $"{ex.GetType().Name}: {ex.Message}"
                };
            }
        }
        /// <summary>
        /// Returns a particular email list with the given account and list Ids
        /// </summary>
        /// <param name="accountId">the account Id of the email list</param>
        /// <param name="listId">the id of the email ist</param>
        /// <returns>a <see cref="Task"/> resulting in the email list</returns>
        public Task<CremirEmailList> GetAsync(int accountId, string listId)
        {
            var request = new HttpRequestMessage(
                HttpMethod.Get,
                $"{_util.BaseUrl}/api/EmailLists/{accountId}/{listId}");
            return SendRequestToCremirAsync<CremirEmailList>(request);
        }
        /// <summary>
        /// Returns all email lists with the given account Id
        /// </summary>
        /// <param name="accountId">the account Id</param>
        /// <returns>a <see cref="Task"/> resulting in a list of email lists with the given account Id</returns>
        public Task<List<CremirEmailList>> GetByAccountAsync(int accountId)
        {
            var request = new HttpRequestMessage(
                HttpMethod.Get,
                $"{_util.BaseUrl}/api/EmailLists/{accountId}");
            return SendRequestToCremirAsync<List<CremirEmailList>>(request);
        }
        /// <summary>
        /// Updates an email list
        /// </summary>
        /// <param name="emailList">the email list to update</param>
        /// <returns>a <see cref="Task"/> indicating success of the update</returns>
        /// <exception cref="ArgumentNullException">if <paramref name="emailList"/> is null</exception>
        public async Task<genericResponse> UpdateAsync(CremirEmailList emailList)
        {
            if (emailList == null) { throw new ArgumentNullException(nameof(emailList)); }
            // only P-type lists should be updated
            if (emailList.FrequencyType != "P")
            {
                return new genericResponse()
                {
                    success = false,
                    message = "Only Periodic Email Lists can be edited."
                };
            }
            // check if the update interval is valid
            if (!IsValidTimeSpanString(emailList.UpdateInterval))
            {
                return new genericResponse()
                {
                    success = false,
                    message = $"'{emailList.UpdateInterval}' is not a valid update interval."
                };
            }

            try
            {
                var customFieldsUpdateResult = await UpdateCustomFieldsAsync(emailList);
                if (!(customFieldsUpdateResult?.Success ?? false))
                {
                    return new genericResponse()
                    {
                        success = false,
                        message = $"Failed to update list's custom fields due to " +
                            $"{customFieldsUpdateResult?.Message ?? "unspecified error"}."
                    };
                }
            }
            catch (Exception ex)
            {
                LogException(ex);
                return new genericResponse()
                {
                    success = false,
                    message = "Failed to update list's custom fields."
                };
            }

            var request = new HttpRequestMessage(
                HttpMethod.Put,
                $"{_util.BaseUrl}/api/EmailLists/{emailList.AccountId}/{emailList.ListId}")
            { 
                Content = new StringContent(
                    JsonConvert.SerializeObject(emailList),
                    Encoding.UTF8,
                    "application/json")
            };
            return await SendRequestToCremirAsync<genericResponse>(request);
        }
        /// <summary>
        /// Gets available <see cref="EmailListCustomField"/>s
        /// </summary>
        /// <returns>list of available <see cref="EmailListCustomField"/>s</returns>
        public List<EmailListCustomField> GetCustomFields()
        {
            return _entity.All<EmailListCustomField>().ToList();
        }

        private Task<EmailListProcResult> UpdateCustomFieldsAsync(CremirEmailList emailList)
        {
            string customFieldIds = string.Join(
                ",",
                emailList.CustomFields?.Select(field => field.Id).ToList() ?? new List<int>());

            var updateProcQuery = _entity.getContext().Database.SqlQuery<EmailListProcResult>(
                    "EXEC [dbo].[x_es_updateEmailListCustomFields] @listId, @customFieldIds",
                    new SqlParameter("@listId", emailList.ListId),
                    new SqlParameter("@customFieldIds", customFieldIds));

            return updateProcQuery.FirstOrDefaultAsync();
        }

        private void LogErrorStatusResponse(HttpResponseMessage response)
        {
            util.recordInErrorLog(
                $"Cremir responded with {response.StatusCode} " +
                $"{response.ReasonPhrase} to {response.RequestMessage.RequestUri.OriginalString}");
        }

        private void LogException(Exception ex)
        {
            util.recordInErrorLog(
                        $"Encountered exception " +
                        $"{ex.GetType().Name}: {ex.Message}{Environment.NewLine}{ex.StackTrace}");
        }

        private void LogException(string url, Exception ex)
        {
            util.recordInErrorLog(
                        $"Encountered exception when performing Cremir email list operation at " +
                        $"{url}{Environment.NewLine}" +
                        $"{ex.GetType().Name}: {ex.Message}{Environment.NewLine}{ex.StackTrace}");
        }

        private async Task<T> SendRequestToCremirAsync<T>(HttpRequestMessage request) where T : class
        {
            // required to establish SSL/TLS connection
            ServicePointManager.Expect100Continue = true;
            ServicePointManager.SecurityProtocol = SecurityProtocolType.Tls12;
            using (request)
            using (var http = new HttpClient())
            {
                _util.AddAuthToRequest(request);
                try
                {
                    HttpResponseMessage response = await http.SendAsync(request);
                    if (response.IsSuccessStatusCode && response.Content != null)
                    {
                        return await response.Content.ReadAsAsync<T>();
                    }
                    else
                    {
                        LogErrorStatusResponse(response);
                        return null;
                    }
                }
                catch (Exception ex)
                {
                    LogException(request.RequestUri.OriginalString, ex);
                    return null;
                }
            };
        }

        private bool IsValidTimeSpanString(string s)
        {
            return !string.IsNullOrEmpty(s) && TimeSpan.TryParse(s, out TimeSpan result);
        }        

        private class EmailListProcResult
        {
            public bool Success { get; set; }
            public string Message { get; set; }
        }

        private class EmailListCreateResult : EmailListProcResult
        {
            public string ListId { get; set; }
        }
    }
}