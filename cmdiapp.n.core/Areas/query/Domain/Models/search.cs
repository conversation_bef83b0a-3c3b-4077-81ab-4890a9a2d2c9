﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

using cmdiapp.n.core.Library;
using cmdiapp.n.core.Areas.crm.Core;

namespace cmdiapp.n.core.Areas.query.Domain.Models
{
    public class searchSortOpt
    {
        public string field { get; set; }
        public string dir { get; set; }
    }

    public class searchItem
    {
        public string id { get; set; }
        public string fieldType { get; set; }
        public string fieldName { get; set; }
        public string opValue { get; set; }
        public string value1 { get; set; }
        public string value2 { get; set; }
        public string valuef { get; set; }
        public bool applyOR { get; set; }
        public bool applyEXCL { get; set; }
    }

    public class searchItem_S: searchItem
    {
        public q_whereItem_S qItem { get; set; }

        #region [ property.string.whereS ]
        public string whereS
        {
            get
            {
                if (opValue != "[[ NULL ]]"
                    && opValue != "[[ !NULL ]]"
                    && fieldType != "d"
                    && String.IsNullOrEmpty(valuef))
                    return "";
                else
                {
                    string items = "";

                    #region **If no fieldName is specified, return only the value (Added by <PERSON><PERSON> at 10:49a, 02/11/2011 to handle parameters for Report and Stored Procedures
                    if (string.IsNullOrEmpty(fieldName))
                    {
                        switch (fieldType)
                        {
                            case "s":
                                return "'" + valuef + "'";
                            case "c":
                                return valuef.Replace(",", "").Replace("$", "");
                            case "n":
                                return valuef;
                            case "d":
                                return String.Format("{0:MM/dd/yyyy}", Convert.ToDateTime(value1));
                        }
                    }
                    #endregion

                    if (fieldType == "b")
                    {
                        if (valuef.ToLower() == "true")
                            return fieldName;
                        else
                            return "";
                    }

                    switch (opValue)
                    {
                        #region [ String \\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\ ]
                        case "[[ eLIKE ]]":
                            return String.Format("{0} = '{1}'", fieldName, value1);
                        case "[[ LIKE% ]]":
                            return String.Format("{0} LIKE '{1}%'", fieldName, value1);
                        case "[[ %LIKE ]]":
                            return String.Format("{0} LIKE '%{1}'", fieldName, value1);
                        case "[[ %LIKE% ]]":
                            return String.Format("{0} LIKE '%{1}%'", fieldName, value1);
                        case "[[ NULL ]]":
                            return String.Format("({0} IS NULL OR {1} = '')", fieldName, fieldName);
                        case "[[ !NULL ]]":
                            return String.Format("{0} <> ''", fieldName);
                        case "[[ IN ]]":
                            items = q_library.form_comma_separated(value1, "'");
                            if (!String.IsNullOrEmpty(items))
                                return String.Format("{0} IN ({1})", fieldName, items);
                            else
                                return "";
                        case "[[ !IN ]]":
                            items = q_library.form_comma_separated(value1, "'");
                            if (!String.IsNullOrEmpty(items))
                                return String.Format("{0} NOT IN ({1})", fieldName, items);
                            else
                                return "";
                        #endregion

                        #region [ Numeric|Currency \\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\ ]
                        case "[[: ]]":
                        case "[[:$ ]]":
                            return (util.is_it_number(value1) ? String.Format("{0} = {1}", fieldName, value1) : "");
                        case "[[ >= ]]":
                        case "[[ >=$ ]]":
                            return (util.is_it_number(value1) ? String.Format("{0} >= {1}", fieldName, value1) : "");
                        case "[[ <= ]]":
                        case "[[ <=$ ]]":
                            return (util.is_it_number(value1) ? String.Format("{0} <= {1}", fieldName, value1) : "");
                        case "[[ != ]]":
                        case "[[ !=$ ]]":
                            return (util.is_it_number(value1) ? String.Format("{0} <> {1}", fieldName, value1) : "");
                        case "[[ nBTW ]]":
                        case "[[ cBTW ]]":
                            return String.Format("{0} BETWEEN {1} AND {2}", fieldName, value1, value2);
                        #endregion

                        #region [ Date \\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\ ]
                        case "[[ dYET ]]":
                            return "";
                        case "[[ On ]]":
                            // (New version by Junho 2011-07-20 to ignore Time portion) return String.Format("{0} = '{1}'", fieldName, String.Format("{0:MM/dd/yyyy HH:mm:ss}", fieldValue_date_1));
                            return String.Format("{0} >= '{1}' AND {0} <= '{2}'", fieldName, String.Format("{0:MM/dd/yyyy HH:mm:ss}", Convert.ToDateTime(value1).Date), String.Format("{0:MM/dd/yyyy HH:mm:ss}", Convert.ToDateTime(value1).Date.AddSeconds(86399)));
                        case "[[ On/After ]]":
                            return String.Format("{0} >= '{1}'", fieldName, String.Format("{0:MM/dd/yyyy HH:mm:ss}", Convert.ToDateTime(value1).Date));
                        case "[[ On/Before ]]":
                            return String.Format("{0} <= '{1}'", fieldName, String.Format("{0:MM/dd/yyyy HH:mm:ss}", Convert.ToDateTime(value1).Date.AddSeconds(86399))); // Handle 11:59:59pm
                        case "[[ dBTW ]]":
                            if (Convert.ToDateTime(value1).Date < Convert.ToDateTime(value2).Date)
                                return String.Format("{0} BETWEEN '{1}' AND '{2}'", fieldName, String.Format("{0:MM/dd/yyyy HH:mm:ss}", Convert.ToDateTime(value1).Date), String.Format("{0:MM/dd/yyyy HH:mm:ss}", Convert.ToDateTime(value2).Date.AddSeconds(86399)));
                            else
                                return String.Format("{0} BETWEEN '{1}' AND '{2}'", fieldName, String.Format("{0:MM/dd/yyyy HH:mm:ss}", Convert.ToDateTime(value2).Date), String.Format("{0:MM/dd/yyyy HH:mm:ss}", Convert.ToDateTime(value1).Date.AddSeconds(86399)));
                        case "[[ FX-today ]]":
                            return String.Format("{0} >= '{1}' AND {0} <= '{2}'", fieldName, String.Format("{0:MM/dd/yyyy HH:mm:ss}", System.DateTime.Today.Date), String.Format("{0:MM/dd/yyyy HH:mm:ss}", System.DateTime.Today.Date.AddSeconds(86399)));
                        case "[[ FX-yesterday ]]":
                            return String.Format("{0} >= '{1}' AND {0} <= '{2}'", fieldName, String.Format("{0:MM/dd/yyyy HH:mm:ss}", System.DateTime.Today.Date.AddDays(-1)), String.Format("{0:MM/dd/yyyy HH:mm:ss}", System.DateTime.Today.Date.AddDays(-1).AddSeconds(86399)));
                        case "[[ FX-wk ]]":
                            return String.Format("{0} BETWEEN '{1}' AND '{2}'", fieldName, String.Format("{0:MM/dd/yyyy HH:mm:ss}", util.getFirstDayOfWeek(System.DateTime.Today)), String.Format("{0:MM/dd/yyyy HH:mm:ss}", util.getLastDayOfWeek(System.DateTime.Today).Date.AddSeconds(86399)));
                        case "[[ FX-mo ]]":
                            return String.Format("{0} BETWEEN '{1}' AND '{2}'", fieldName, String.Format("{0:MM/dd/yyyy HH:mm:ss}", util.getFirstDayOfMonth(System.DateTime.Today)), String.Format("{0:MM/dd/yyyy HH:mm:ss}", util.getLastDayOfMonth(System.DateTime.Today).Date.AddSeconds(86399)));
                        case "[[ FX-yr ]]":
                            return String.Format("{0} BETWEEN '{1}' AND '{2}'", fieldName, String.Format("{0:MM/dd/yyyy HH:mm:ss}", util.getFirstDayOfYear(System.DateTime.Today)), String.Format("{0:MM/dd/yyyy HH:mm:ss}", util.getLastDayOfYear(System.DateTime.Today).Date.AddSeconds(86399)));
                        case "[[ FX-lwk ]]":
                            return String.Format("{0} BETWEEN '{1}' AND '{2}'", fieldName, String.Format("{0:MM/dd/yyyy HH:mm:ss}", util.getFirstDayOfWeek(System.DateTime.Today.AddDays(-7))), String.Format("{0:MM/dd/yyyy HH:mm:ss}", util.getLastDayOfWeek(System.DateTime.Today.AddDays(-7)).Date.AddSeconds(86399)));
                        case "[[ FX-lmo ]]":
                            return String.Format("{0} BETWEEN '{1}' AND '{2}'", fieldName, String.Format("{0:MM/dd/yyyy HH:mm:ss}", util.getFirstDayOfMonth(System.DateTime.Today.AddMonths(-1))), String.Format("{0:MM/dd/yyyy HH:mm:ss}", util.getLastDayOfMonth(System.DateTime.Today.AddMonths(-1)).Date.AddSeconds(86399)));
                        case "[[ FX-lyr ]]":
                            return String.Format("{0} BETWEEN '{1}' AND '{2}'", fieldName, String.Format("{0:MM/dd/yyyy HH:mm:ss}", util.getFirstDayOfYear(System.DateTime.Today.AddYears(-1))), String.Format("{0:MM/dd/yyyy HH:mm:ss}", util.getLastDayOfYear(System.DateTime.Today.AddYears(-1)).Date.AddSeconds(86399)));
                        case "[[ FX-l7d ]]":
                            return String.Format("{0} BETWEEN '{1}' AND '{2}'", fieldName, String.Format("{0:MM/dd/yyyy HH:mm:ss}", System.DateTime.Today.AddDays(-7).Date), String.Format("{0:MM/dd/yyyy HH:mm:ss}", System.DateTime.Today.Date.AddSeconds(86399)));
                        case "[[ FX-l30d ]]":
                            return String.Format("{0} BETWEEN '{1}' AND '{2}'", fieldName, String.Format("{0:MM/dd/yyyy HH:mm:ss}", System.DateTime.Today.AddDays(-30).Date), String.Format("{0:MM/dd/yyyy HH:mm:ss}", System.DateTime.Today.Date.AddSeconds(86399)));
                        case "[[ FX-l3m ]]":
                            return String.Format("{0} BETWEEN '{1}' AND '{2}'", fieldName, String.Format("{0:MM/dd/yyyy HH:mm:ss}", System.DateTime.Today.AddMonths(-3).Date), String.Format("{0:MM/dd/yyyy HH:mm:ss}", System.DateTime.Today.Date.AddSeconds(86399)));
                        case "[[ FX-l6m ]]":
                            return String.Format("{0} BETWEEN '{1}' AND '{2}'", fieldName, String.Format("{0:MM/dd/yyyy HH:mm:ss}", System.DateTime.Today.AddMonths(-6).Date), String.Format("{0:MM/dd/yyyy HH:mm:ss}", System.DateTime.Today.Date.AddSeconds(86399)));
                        case "[[ FX-l1y ]]":
                            return String.Format("{0} BETWEEN '{1}' AND '{2}'", fieldName, String.Format("{0:MM/dd/yyyy HH:mm:ss}", System.DateTime.Today.AddYears(-1).Date), String.Format("{0:MM/dd/yyyy HH:mm:ss}", System.DateTime.Today.Date.AddSeconds(86399)));
                        case "[[ FX-ftd ]]":
                            return String.Format("{0} BETWEEN '{1}' AND '{2}'", fieldName, String.Format("{0:MM/dd/yyyy HH:mm:ss}", (crmSession.ftdCycleStart() == null ? util.getFirstDayOfYear(System.DateTime.Today) : crmSession.ftdCycleStart())), String.Format("{0:MM/dd/yyyy HH:mm:ss}", (crmSession.ftdCycleEnd() == null ? util.getLastDayOfYear(System.DateTime.Today).Date.AddSeconds(86399) : ((DateTime)crmSession.ftdCycleEnd()).AddSeconds(86399))));
                        case "[[ FX-lftd ]]":
                            return String.Format("{0} BETWEEN '{1}' AND '{2}'", fieldName, String.Format("{0:MM/dd/yyyy HH:mm:ss}", (crmSession.ftdCycleStart() == null ? util.getFirstDayOfYear(System.DateTime.Today.AddYears(-1)) : ((DateTime)crmSession.ftdCycleStart()).AddYears(-1).Date)), String.Format("{0:MM/dd/yyyy HH:mm:ss}", (crmSession.ftdCycleEnd() == null ? util.getLastDayOfYear(System.DateTime.Today.AddYears(-1)).Date.AddSeconds(86399) : ((DateTime)crmSession.ftdCycleEnd()).AddYears(-1).Date.AddSeconds(86399))));

                        #endregion

                            #region [ Custom \\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\ ]
                        default:
                            if (value1.IndexOf(" | ")>=0 || value1.IndexOf(',') >= 0)
                            {
                                items = q_library.form_comma_separated(value1, (fieldType == "n" ? "" : "'"));
                                if (!String.IsNullOrEmpty(items))
                                    return String.Format("{0} IN ({1})", fieldName, items);
                                else
                                    return "";
                            }
                            else  // Single Value
                            {
                                if (fieldType == "n")
                                    return String.Format("{0} = {1}", fieldName, value1);
                                else
                                    return String.Format("{0} = '{1}'", fieldName, value1);
                            }
                        #endregion
                    }
                }
            }
        }
        #endregion
    }
    public class searchParam
    {
        public string searchText { get; set; }
        public int? page { get; set; }
        public int? pageSize { get; set; }
        public int? skip { get; set; }
        public int? take { get; set; }

        public List<searchSortOpt> sort { get; set; }

        public List<q_sortOption> sortOptions { get; set; }

        public List<searchItem> searchData { get; set; }
        public string qId { get; set; } // such as reportId
        public string name { get; set; }

        #region [ Read-only Properties ]

        public string searchText_ 
        { 
            get 
            {
                string lc_searchText = (searchText ?? "");
                lc_searchText = lc_searchText.Trim().Replace("'", "''''");     // Double-Escape Single-Quote that is passed as a SQL-SELECT String.
                lc_searchText = Library.util.kill_sqlBlacklistWord(lc_searchText);

                return lc_searchText;
            } 
        }

        public int page_ { get { return (page == null || page.Value == 0 ? 1 : page.Value); } }
        
        public int pageSize_ { get { return (pageSize == null || pageSize.Value == 0 ? 10 : pageSize.Value); } }

        public string sortOption1_
        {
            get 
            {
                string sortField = (sort != null ? sort[0].field : "");
                string sortDir = (sort != null ? sort[0].dir : "");

                string _sortOptions = "";
                if (!string.IsNullOrEmpty(sortField))
                    _sortOptions = sortField;
                if (!string.IsNullOrEmpty(sortField) && !string.IsNullOrEmpty(sortDir))
                    _sortOptions = _sortOptions + " " + sortDir;

                return _sortOptions;
            }
        }

        public string sqlOrderBy(q_def_S _def)
        {
            #region [ Sort specified by Grid Column Header ]
            if (!String.IsNullOrEmpty(sortOption1_))
                return sortOption1_;
            #endregion

            #region [ Sort specified by Sort Options ]
            else if (sortOptions != null)
            {
                var q = from a in _def.qSortOptions
                        join b in sortOptions on a.id equals b.id
                        orderby b.idx
                        select new q_sortOption_S() { id=a.id, caption=a.caption, name=a.name, order=b.order };

                string sqlOrderBy_ = "";
                List<q_sortOption_S> sortColumns = q.ToList();
                foreach (q_sortOption_S item in sortColumns)
                    sqlOrderBy_ = sqlOrderBy_ + (string.IsNullOrEmpty(sqlOrderBy_) ? "" : ", ") + item.name + (item.order == "DESC" ? " DESC" : "");

                return sqlOrderBy_;
            }

            return "";
            #endregion
        }
        #endregion

    }
}