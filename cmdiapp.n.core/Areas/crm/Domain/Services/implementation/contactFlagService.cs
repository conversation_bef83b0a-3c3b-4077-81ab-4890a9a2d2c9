﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

using Ninject;
using Ninject.Web.Mvc;

using cmdiapp.n.core.Core.App_Start;
using cmdiapp.n.core.Areas.crm.Domain.Models;
using cmdiapp.n.core.Areas.crm.Domain.ViewModels;
using cmdiapp.n.core.Areas.crm.Domain.Repositories;
namespace cmdiapp.n.core.Areas.crm.Domain.Services
{
    public class contactFlagService : IcontactFlagService
    {
        private I___unitOfWork _unitOfWork;
        private I__DBFactory _dbFactory;
        private IcontactFlagRepository _contactFlagRepository;

        public contactFlagService(I___unitOfWork unitOfWork, I__DBFactory dbFactory, IcontactFlagRepository ContactFlagRepository)
        {
            _unitOfWork = unitOfWork;
            _dbFactory = dbFactory;
            _contactFlagRepository = ContactFlagRepository;
        }

        public lkCONTFLAG get_contact_flag(int contFlagId)
        {
            string sql = string.Format("SELECT * FROM lkCONTFLAG WHERE CONTFLAGID={0}", contFlagId);
            var q = _dbFactory.getContext().Database.SqlQuery<lkCONTFLAG>(sql);
            return q.FirstOrDefault();
        }

        public bool check_unqiueness(lkCONTFLAG _lkcontFlag)
        {
            // take care of apostrophe
            string v = _lkcontFlag.CONTFLAG.Replace("'", "''");
            string sql = "";

            if (_lkcontFlag.CONTFLAGID > 0)   // Existing Record
                sql = String.Format("SELECT Count(*) as recordCount FROM lkCONTFLAG WHERE CONTFLAG = '{0}' AND CONTFLAGID <> {1}", v, _lkcontFlag.CONTFLAGID);
            else
                sql = String.Format("SELECT Count(*) as recordCount FROM lkCONTFLAG WHERE CONTFLAG = '{0}'", v);

            //Check in CONTFLAG Table
            int isZero = _dbFactory.getContext().Database.SqlQuery<int>(sql).FirstOrDefault();
            
            return (isZero == 0) ? true : false;
        }

        public bool check_record_exists(int contFlagId)
        {
            //string sql = string.Format("SELECT * FROM jtCONTFLAG WHERE CONTFLAGID='{0}'", contFlagId);
            //var q = _dbFactory.getContext().Database.SqlQuery<jtCONTFLAG>(sql);
            //return q.ToList().Count > 0 ? true : false;

            //Check 
            string sql = string.Format("SELECT Count(*) as recordCount FROM jtCONTFLAG WHERE CONTFLAGID={0}", contFlagId);
            var q = _dbFactory.getContext().Database.SqlQuery<UniquenessCheck>(sql);
            int isZero = q.FirstOrDefault().recordCount;
            return (isZero > 0) ? true : false;


        }

        public void Add(lkCONTFLAG _lkCONTFLAG)
        {
            _contactFlagRepository.Add(_lkCONTFLAG);
            _unitOfWork.commit();
        }

        public void Update(lkCONTFLAG _lkCONTFLAG)
        {
            _contactFlagRepository.Update(_lkCONTFLAG);
            _unitOfWork.commit();
        }

        public void Delete(lkCONTFLAG _lkCONTFLAG)
        {
            _contactFlagRepository.Delete(_lkCONTFLAG);
            _unitOfWork.commit();
        }

        //Read all Contact Flags
        public List<lkCONTFLAG> read_all_contact_flags()
        {
            return _contactFlagRepository.All().ToList();
        }

        public List<lkCONTFLAG_ext> get_all_contact_flags(string where, string orderBy, int pageSize, int pageNo)
        {
            string sql = (!string.IsNullOrEmpty(where) ? string.Format("SELECT * FROM lkCONTFLAG WHERE {0}", where) : "SELECT * FROM lkCONTFLAG");

            var q = _dbFactory.getContext().Database.SqlQuery<lkCONTFLAG_ext>(String.Format("EXEC dbo.get_pagedDataSet '{0}', '{1}', {2}, {3}", sql, orderBy, pageSize, pageNo));

            return q.ToList();
        }


    }
}