﻿using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

using cmdiapp.n.core.Areas.legislator.Domain.Data;
using cmdiapp.n.core.Domain.ViewModels;

namespace cmdiapp.n.core.Areas.legislator.Domain.Models
{
    [Table("Legislator")]
    public class Legislator: _entityBase_legislator, iItemType
    {
        [Column, Key, MaxLength(25)]
        public string Id { get; set; }

        [Column, MaxLength(25)]
        public string govtrack { get; set; }

        [Column, MaxLength(50)]
        public string fecId { get; set; }

        [Column, MaxLength(250)]
        public string name { get; set; }

        [Column, MaxLength(50)]
        public string fn { get; set; }

        [Column, MaxLength(50)]
        public string mn { get; set; }

        [Column, MaxLength(100)]
        public string ln { get; set; }

        [<PERSON>umn, <PERSON><PERSON>ength(50)]
        public string suffix { get; set; }

        [Column, <PERSON><PERSON>ength(10)]
        public string gender { get; set; }

        [Column]
        public DateTime? birthday { get; set; }

        [Column, MaxLength(25)]
        public string termType { get; set; }

        [Column]
        public DateTime? termStart { get; set; }

        [Column]
        public DateTime? termEnd { get; set; }

        [Column, MaxLength(25)]
        public string termState { get; set; }

        [Column, MaxLength(25)]
        public string termDistrict { get; set; }

        [Column, MaxLength(50)]
        public string termParty { get; set; }

        [Column, MaxLength(50)]
        public string termPhone { get; set; }

        [Column, MaxLength(100)]
        public string termUrl { get; set; }

        [Column, MaxLength(100)]
        public string termAddress { get; set; }

        [Column]
        public int nextElectionYear { get; set; }

        [Column]
        public string terms { get; set; }

        [Column]
        public bool active { get; set; }

        [Column]
        public DateTime? createdAt { get; set; }

        [Column]
        public DateTime? updatedAt { get; set; }

        [Column]
        public DateTime? inactivatedAt { get; set; }
    }
}