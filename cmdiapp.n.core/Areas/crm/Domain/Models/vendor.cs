﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Data;
using System.Linq;
using System.Web.Mvc;
using System.Data.Linq;
using System.ComponentModel.DataAnnotations.Schema;

using cmdiapp.n.core.Domain.ViewModels;
using cmdiapp.n.core.Areas.crm.Domain.Data;
using cmdiapp.n.core.Domain.Data;

namespace cmdiapp.n.core.Areas.crm.Domain.Models
{
    // (view)v_entity_list - Search output ]]

    [Table("v_entity_list")]
    public class vendorR : _entityBase_crm, iItemType
    {
        [Column]
        [Key]
        public int ENTITYID { get; set; }

        [Column]
        public string CODE { get; set; }

        [Column]
        public string ORGNAME { get; set; }

        [Column]
        public string PREFIX { get; set; }

        [Column]
        public string FNAME { get; set; }

        [Column]
        public string MNAME { get; set; }

        [Column]
        public string LNAME { get; set; }

        [Column]
        public string SUFFIX { get; set; }

        [Column]
        public string STREET { get; set; }

        [Column]
        public string STREET2 { get; set; }

        [Column]
        public string CITY { get; set; }

        [Column]
        public string STATE { get; set; }

        [Column]
        public string ZIP { get; set; }

        [Column]
        public bool IS1099 { get; set; }

        [Column]
        public string TAXID { get; set; }

        [Column]
        public Nullable<DateTime> FTXNDTE { get; set; }

        [Column]
        public Nullable<DateTime> LTXNDTE { get; set; }

        [Column]
        public Nullable<int> CTDNO_EXP { get; set; }

        [Column]
        public Nullable<decimal> CTDAMT_EXP { get; set; }

        [Column]
        public Nullable<int> YTDNO_EXP { get; set; }

        [Column]
        public Nullable<decimal> YTDAMT_EXP { get; set; }

        [Column]
        public Nullable<decimal> CUMAMT_EXP { get; set; }

        [Column]
        public Nullable<int> CTDNO_REC { get; set; }

        [Column]
        public Nullable<decimal> CTDAMT_REC { get; set; }

        [Column]
        public Nullable<int> YTDNO_REC { get; set; }

        [Column]
        public Nullable<decimal> YTDAMT_REC { get; set; }

        [Column]
        public Nullable<decimal> CUMAMT_REC { get; set; }

        [Column]
        public bool RPTCMTEADDR { get; set; }
    }

    [NotMapped]
    public class v_entity_ext : vendorR
    {
        public int count_ { get; set; }

        public Int64 rowNo { get; set; }
    }

    [Table("ENTITY")]
    public class Vendor : _entityBase_crm, iItemType
    {
        [Key]
        public int ENTITYID { get; set; }

        [Column]
        public Int16? ENTITYTYPEID { get; set; }

        [Column]
        public string ORGNAME { get; set; }

        [Column]
        public string PREFIX { get; set; }

        [Column]
        public string LNAME { get; set; }

        [Column]
        public string FNAME { get; set; }

        [Column]
        public string MNAME { get; set; }

        [Column]
        public string SUFFIX { get; set; }

        [Column]
        public string STREET1 { get; set; }

        [Column]
        public string STREET2 { get; set; }
        [Column]
        public string COUNTY { get; set; }

        [Column]
        public string CITY { get; set; }

        [Column]
        public string STATE { get; set; }

        [Column]
        public string ZIP { get; set; }

        [Column]
        public string PLUS4 { get; set; }

        [Column]
        public string PHONE { get; set; }

        [Column]
        public string FAX { get; set; }

        [Column]
        public string CELL { get; set; }

        [Column]
        public string EMAIL { get; set; }

        [Column]
        public string CONTACT { get; set; }

        [Column]
        public string TAXID { get; set; }

        [Column]
        public Boolean IS1099 { get; set; }

        [Column]
        public string EMPLOYER { get; set; }

        [Column]
        public string OCCUPATION { get; set; }

        [Column]
        public string FECCMTEID { get; set; }

        [Column]
        public string CANDPREFIX { get; set; }

        [Column]
        public string CANDLNAME { get; set; }

        [Column]
        public string CANDFNAME { get; set; }

        [Column]
        public string CANDMNAME { get; set; }

        [Column]
        public string CANDSUFFIX { get; set; }

        [Column]
        public string CANDFECID { get; set; }

        [Column]
        public string CANDOFFICE { get; set; }

        [Column]
        public string CANDSTATE { get; set; }

        [Column]
        public string CANDDIST { get; set; }

        [Column]
        public string VENDFECDESCRIP { get; set; }

        [Column]
        [DatabaseGenerated(DatabaseGeneratedOption.None)]
        public Nullable<DateTime> FTXNDTE { get; set; }

        [Column]
        [DatabaseGenerated(DatabaseGeneratedOption.None)]
        public Nullable<decimal> FTXNAMT { get; set; }

        [Column]
        [DatabaseGenerated(DatabaseGeneratedOption.None)]
        public Nullable<DateTime> LTXNDTE { get; set; }

        [Column]
        [DatabaseGenerated(DatabaseGeneratedOption.None)]
        public Nullable<decimal> LTXNAMT { get; set; }

        [Column]
        [DatabaseGenerated(DatabaseGeneratedOption.None)]
        public Nullable<int> YTDNO { get; set; }

        [Column]
        [DatabaseGenerated(DatabaseGeneratedOption.None)]
        public Nullable<decimal> YTDAMT { get; set; }

        [Column]
        [DatabaseGenerated(DatabaseGeneratedOption.None)]
        public Nullable<int> CTDNO { get; set; }

        [Column]
        [DatabaseGenerated(DatabaseGeneratedOption.None)]
        public Nullable<decimal> CTDAMT { get; set; }

        [Column]
        [DatabaseGenerated(DatabaseGeneratedOption.None)]
        public Nullable<int> CUMNO { get; set; }

        [Column]
        [DatabaseGenerated(DatabaseGeneratedOption.None)]
        public Nullable<decimal> CUMAMT { get; set; }

        [Column]
        [DatabaseGenerated(DatabaseGeneratedOption.None)]
        public Nullable<DateTime> UPDATEDON { get; set; }

        [Column]
        public string VENDACCTID { get; set; }

        [Column]
        public Nullable<Int16> UID { get; set; }

        [Column("_updating_uid")]
        public Nullable<short> updating_uid { get; set; }

        [Column]
        [DatabaseGenerated(DatabaseGeneratedOption.None)]
        public Nullable<int> CTDNO_EXP { get; set; }

        [Column]
        [DatabaseGenerated(DatabaseGeneratedOption.None)]
        public Nullable<decimal> CTDAMT_EXP { get; set; }

        [Column]
        [DatabaseGenerated(DatabaseGeneratedOption.None)]
        public Nullable<int> YTDNO_EXP { get; set; }

        [Column]
        [DatabaseGenerated(DatabaseGeneratedOption.None)]
        public Nullable<decimal> YTDAMT_EXP { get; set; }

        [Column]
        [DatabaseGenerated(DatabaseGeneratedOption.None)]
        public Nullable<int> CTDNO_REC { get; set; }

        [Column]
        [DatabaseGenerated(DatabaseGeneratedOption.None)]
        public Nullable<decimal> CTDAMT_REC { get; set; }

        [Column]
        [DatabaseGenerated(DatabaseGeneratedOption.None)]
        public Nullable<int> YTDNO_REC { get; set; }

        [Column]
        [DatabaseGenerated(DatabaseGeneratedOption.None)]
        public Nullable<decimal> YTDAMT_REC { get; set; }

        [Column]
        [DatabaseGenerated(DatabaseGeneratedOption.None)]
        public Nullable<decimal> CUMAMT_EXP { get; set; }

        [Column]
        [DatabaseGenerated(DatabaseGeneratedOption.None)]
        public Nullable<decimal> CUMAMT_REC { get; set; }

        [Column]
        [DatabaseGenerated(DatabaseGeneratedOption.None)]
        public Nullable<DateTime> FINVDTE { get; set; }

        [Column]
        [DatabaseGenerated(DatabaseGeneratedOption.None)]
        public Nullable<decimal> FINVAMT { get; set; }

        [Column]
        [DatabaseGenerated(DatabaseGeneratedOption.None)]
        public Nullable<DateTime> LINVDTE { get; set; }

        [Column]
        [DatabaseGenerated(DatabaseGeneratedOption.None)]
        public Nullable<decimal> LINVAMT { get; set; }

        [Column]
        [DatabaseGenerated(DatabaseGeneratedOption.None)]
        public Nullable<int> CTDNO_INV { get; set; }

        [Column]
        [DatabaseGenerated(DatabaseGeneratedOption.None)]
        public Nullable<decimal> CTDAMT_INV { get; set; }

        [Column]
        [DatabaseGenerated(DatabaseGeneratedOption.None)]
        public Nullable<int> YTDNO_INV { get; set; }

        [Column]
        [DatabaseGenerated(DatabaseGeneratedOption.None)]
        public Nullable<decimal> YTDAMT_INV { get; set; }

        [Column]
        public bool RPTCMTEADDR { get; set; }

        //Added on 8/12/2015 for Vendor Default Line No.
        [Column]
        public Int16? FECTXNTYPEID_EXP { get; set; }

        [Column]
        public Int16? FECTXNTYPEID_REC { get; set; }

        [Column]
        public Int16? FECTXNTYPEID3P_EXP { get; set; }

        [Column]
        public Int16? FECTXNTYPEID3P_REC { get; set; }

        [Column]
        public Int16? FECTXNTYPEID3X_EXP { get; set; }

        [Column]
        public Int16? FECTXNTYPEID3X_REC { get; set; }

        [Column]
        public Int16? CHARTACCTID { get; set; }

        [Column]
        public byte[] PICTURE { get; set; }

        [Column]
        public string partyAffil { get; set; }

        [Column, MaxLength(25)]
        public string LEGISLATORID { get; set; }

        [Column]
        [DatabaseGenerated(DatabaseGeneratedOption.None)]
        public Nullable<int> LFTDNO_EXP { get; set; }

        [Column]
        [DatabaseGenerated(DatabaseGeneratedOption.None)]
        public Nullable<decimal> LFTDAMT_EXP { get; set; }

        [Column]
        [DatabaseGenerated(DatabaseGeneratedOption.None)]
        public Nullable<int> L2FTDNO_EXP { get; set; }

        [Column]
        [DatabaseGenerated(DatabaseGeneratedOption.None)]
        public Nullable<decimal> L2FTDAMT_EXP{ get; set; }

        [Column]
        [DatabaseGenerated(DatabaseGeneratedOption.None)]
        public Nullable<int> LFTDNO_REC { get; set; }

        [Column]
        [DatabaseGenerated(DatabaseGeneratedOption.None)]
        public Nullable<decimal> LFTDAMT_REC { get; set; }

        [Column]
        [DatabaseGenerated(DatabaseGeneratedOption.None)]
        public Nullable<int> L2FTDNO_REC { get; set; }

        [Column]
        [DatabaseGenerated(DatabaseGeneratedOption.None)]
        public Nullable<decimal> L2FTDAMT_REC { get; set; }

        [Column]
        [DatabaseGenerated(DatabaseGeneratedOption.None)]
        public Nullable<int> LFTDNO_INV { get; set; }

        [Column]
        [DatabaseGenerated(DatabaseGeneratedOption.None)]
        public Nullable<decimal> LFTDAMT_INV { get; set; }

        [Column]
        [DatabaseGenerated(DatabaseGeneratedOption.None)]
        public Nullable<int> L2FTDNO_INV { get; set; }

        [Column]
        [DatabaseGenerated(DatabaseGeneratedOption.None)]
        public Nullable<decimal> L2FTDAMT_INV { get; set; }
    }

    [NotMapped]
    public class VendorMatch
    {
        public int ENTITYID { get; set; }

        public Int16? ENTITYTYPEID { get; set; }

        public string ORGNAME { get; set; }

        public string PREFIX { get; set; }

        public string LNAME { get; set; }

        public string FNAME { get; set; }

        public string MNAME { get; set; }

        public string SUFFIX { get; set; }

        public string STREET1 { get; set; }

        public string STREET2 { get; set; }

        public string CITY { get; set; }

        public string STATE { get; set; }

        public string ZIP { get; set; }

        public string PLUS4 { get; set; }

        public string PHONE { get; set; }

        public string FAX { get; set; }

        public string CELL { get; set; }

        public string EMAIL { get; set; }

        public string CONTACT { get; set; }

        public string TAXID { get; set; }

        public Boolean IS1099 { get; set; }

        public string EMPLOYER { get; set; }

        public string OCCUPATION { get; set; }

        public string FECCMTEID { get; set; }

        public string CANDPREFIX { get; set; }

        public string CANDLNAME { get; set; }

        public string CANDFNAME { get; set; }

        public string CANDMNAME { get; set; }

        public string CANDSUFFIX { get; set; }

        public string CANDFECID { get; set; }

        public string CANDOFFICE { get; set; }

        public string CANDSTATE { get; set; }

        public string CANDDIST { get; set; }

        public string VENDFECDESCRIP { get; set; }

        public Nullable<DateTime> FTXNDTE { get; set; }

        public Nullable<decimal> FTXNAMT { get; set; }

        public Nullable<DateTime> LTXNDTE { get; set; }

        public Nullable<decimal> LTXNAMT { get; set; }

        public Nullable<int> YTDNO { get; set; }

        public Nullable<decimal> YTDAMT { get; set; }

        public Nullable<int> CTDNO { get; set; }

        public Nullable<decimal> CTDAMT { get; set; }

        public Nullable<int> CUMNO { get; set; }

        public Nullable<decimal> CUMAMT { get; set; }

        public Nullable<DateTime> UPDATEDON { get; set; }

        public string VENDACCTID { get; set; }

        public Nullable<Int16> UID { get; set; }

        public Nullable<short> updating_uid { get; set; }

        public Nullable<int> CTDNO_EXP { get; set; }

        public Nullable<decimal> CTDAMT_EXP { get; set; }

        public Nullable<int> YTDNO_EXP { get; set; }

        public Nullable<decimal> YTDAMT_EXP { get; set; }

        public Nullable<int> CTDNO_REC { get; set; }

        public Nullable<decimal> CTDAMT_REC { get; set; }

        public Nullable<int> YTDNO_REC { get; set; }

        public Nullable<decimal> YTDAMT_REC { get; set; }

        public Nullable<decimal> CUMAMT_EXP { get; set; }

        public Nullable<decimal> CUMAMT_REC { get; set; }

        public Nullable<DateTime> FINVDTE { get; set; }

        public Nullable<decimal> FINVAMT { get; set; }

        public Nullable<DateTime> LINVDTE { get; set; }

        public Nullable<decimal> LINVAMT { get; set; }

        public Nullable<int> CTDNO_INV { get; set; }

        public Nullable<decimal> CTDAMT_INV { get; set; }

        public Nullable<int> YTDNO_INV { get; set; }

        public Nullable<decimal> YTDAMT_INV { get; set; }

        public bool RPTCMTEADDR { get; set; }

        //Added on 8/12/2015 for Vendor Default Line No.

        public Int16? FECTXNTYPEID_EXP { get; set; }


        public Int16? FECTXNTYPEID_REC { get; set; }


        public Int16? FECTXNTYPEID3P_EXP { get; set; }


        public Int16? FECTXNTYPEID3P_REC { get; set; }


        public Int16? FECTXNTYPEID3X_EXP { get; set; }


        public Int16? FECTXNTYPEID3X_REC { get; set; }


        public Int16? CHARTACCTID { get; set; }

        public string matchType { get; set; }
    }

    [Table("lkENTITYTYPE")]
    public class lkENTITYTYPE : _entityBase_crm
    {
        [Column]
        [Key]
        public Int16 ENTITYTYPEID { get; set; }

        [Column]
        public string CODE { get; set; }

        [Column]
        public string DESCRIP { get; set; }
    }

    /*
    [Table("v_entity_ExpSummary")]
    public class v_entity_ExpSummary : _entityBase_crm
    {
        [Column]
        public int ORD { get; set; }

        [Column]
        public string CAT { get; set; }

        [Column]
        [Key]
        public int ENTITYID { get; set; }

        [Column]
        public Nullable<decimal> AMT { get; set; }
    }

    [Table("v_entity_RecSummary")]
    public class v_entity_RecSummary : _entityBase_crm
    {
        [Column]
        public int ORD { get; set; }

        [Column]
        public string CAT { get; set; }

        [Column]
        [Key]
        public int ENTITYID { get; set; }

        [Column]
        public Nullable<decimal> AMT { get; set; }
    }
    */

    /*
    [Table("v_entity_txn")]
    public class v_entity_txn : _entityBase_crm
    {
        [Column]
        [Key]
        public int TXNID { get; set; }
        
        [Column]
        public string VENDOR { get; set; }

        [Column]
        public Nullable<DateTime> TXNDTE { get; set; }

        [Column]
        public Nullable<decimal> AMT { get; set; }

        [Column]
        public string ADJTYPE { get; set; }

        [Column]
        public Nullable<DateTime> ADJDTE { get; set; }

        [Column]
        public string FUNDCODE { get; set; }

        [Column]
        public Boolean ISMEMO { get; set; }

        [Column]
        public string DESCRIP { get; set; }

        [Column]
        public string FECTYPE { get; set; }

        [Column]
        public string CHKNO { get; set; }

        [Column]
        public string ELECTYR { get; set; }
    }
    */

    [Table("v_ultimate_vendor")]
    public class v_ultimate_vendor : _entityBase_crm
    {
        [Column]
        [Key]
        public int TXNID { get; set; }

        [Column]
        public string NAME { get; set; }

        [Column]
        public Nullable<DateTime> TXNDTE { get; set; }

        [Column]
        public Nullable<decimal> AMT { get; set; }

        [Column]
        public int LINKTXNID { get; set; }

        [Column]
        public string TXNTYPE { get; set; }
    }

    public class canoffice
    {
        public string code { get; set; }

        public string descrip { get; set; }
    }

    public class v_entity_sum_quarter
    {
        public string PERIOD { get; set; }

        public string FUNDCODE { get; set; }

        public Nullable<decimal> TOTAL { get; set; }

    }

    [Table("VENDHIST")]
    public class VENDHIST : _entityBase_crm, iItemType
    {
        [Column]
        [Key]
        public int VENDHISTID { get; set; }
        [Column]
        public DateTime? HSTDATE { get; set; }
        [Column]
        public String TABLENAME { get; set; }
        [Column]
        public String FIELDNAME { get; set; }
        [Column]
        public String OLDVALUE { get; set; }
        [Column]
        public String NEWVALUE { get; set; }
        [Column]
        public string ACTION { get; set; }

        //[NotMapped]
        public String USERNAME { get; set; }
        //[NotMapped]
        public String UPDDESC { get; set; }
    }

    [Table("iGetTxnStatement")]
    public class v_entity_txn : _entityBase_crm, iItemType
    {
        [Column]
        [Key]
        public int TXNID { get; set; }

        [Column]
        public string ROOT { get; set; }

        [Column]
        public string VENDOR { get; set; }

        [Column]
        public DateTime? TXNDTE { get; set; }

        [Column]
        public decimal? AMT { get; set; }

        [Column]
        public string ADJTYPE { get; set; }

        [Column]
        public DateTime? ADJDTE { get; set; }

        [Column]
        public string FUNDCODE { get; set; }

        [Column]
        public bool ISMEMO { get; set; }

        [Column]
        public string DESCRIP { get; set; }

        [Column]
        public string FECTYPE { get; set; }

        [Column]
        public string CHKNO { get; set; }

        [Column]
        public string ELECTYR { get; set; }

        [Column]
        public string FECDESCRIP { get; set; }

        [Column]
        public string SORTSEQ { get; set; }

        public DateTime? SORTDTE { get; set; }
    }

    [NotMapped]
    public class v_entity_txn_ext : v_entity_txn
    {
        public int count_ { get; set; }

        public Int64 rowNo { get; set; }
    }

    public class CandidateDetails : _entityBase
    {
        public string CANDFECID { get; set; }
        public string CANDPREFIX { get; set; }
        public string CANDFNAME { get; set; }
        public string CANDMNAME { get; set; }
        public string CANDLNAME { get; set; }
        public string CANDSUFFIX { get; set; }
        public string CANDOFFICE { get; set; }
        public string CANDSTATE { get; set; }
        public string CANDDIST { get; set; }
        public string FECCMTEID { get; set; }
        public string COMMITTEE { get; set; }
    }
    [NotMapped]
    public class v_entity_summaryR
    {
        public int ENTITYID { get; set; }
        public int CTDNO { get; set; }
        public decimal CTDAMT { get; set; }
        public int YTDNO { get; set; }
        public decimal YTDAMT { get; set; }
        public int CUMTNO { get; set; }
        public decimal CUMTOT { get; set; }
        public DateTime? FIRSTDTE { get; set; }
        public decimal? FIRSTAMT { get; set; }
        public DateTime? LASTDTE { get; set; }
        public decimal? LASTAMT { get; set; }
        public DateTime? HIGHDTE { get; set; }
        public decimal? HIGHAMT { get; set; }
        public decimal? AVERAGE { get; set; }
        public int LYRNO { get; set; }
        public decimal LYRAMT { get; set; }
        public int L2YRNO { get; set; }
        public decimal L2YRAMT { get; set; }
        public int L3YRNO { get; set; }
        public decimal L3YRAMT { get; set; }
        public int LFTDNO { get; set; }
        public decimal LFTDAMT { get; set; }
        public int L2FTDNO { get; set; }
        public decimal L2FTDAMT { get; set; }
        public int L3FTDNO { get; set; }
        public decimal L3FTDAMT { get; set; }

    }

    [NotMapped]
    public class v_entity_transactionR: iItemType
    {
        public int TXNID { get; set; }
        public string CODE { get; set; }
        public int ENTITYID { get; set; }
        public string VENDORTYPE { get; set; }
        public string ORGNAME { get; set; }
        public string PREFIX { get; set; }
        public string FNAME { get; set; }
        public string MNAME { get; set; }
        public string LNAME { get; set; }
        public string SUFFIX { get; set; }
        public string STREET { get; set; }
        public string ADDR1 { get; set; }
        public string CITY { get; set; }
        public string STATE { get; set; }
        public string ZIP { get; set; }
        public DateTime TXNDTE { get; set; }
        public decimal AMT { get; set; }
        public string FUNDCODE { get; set; }
        public string LINE { get; set; }
        public string DESCRIPTION { get; set; }
        public string BANKACCT { get; set; }
        public bool MEMO { get; set; }
        public string MEMOTXT { get; set; }
        public string PAYTYPE { get; set; }
        public string CHECKNO { get; set; }
        public string FEC_DESCRIPTION { get; set; }
        public byte COUNTER { get; set; }
    }

    [NotMapped]
    public class v_entity_transactionR_ext : v_entity_transactionR
    {
        public int count_ { get; set; }

        public Int64 rowNo { get; set; }
    }

    public class entityPicture
    {
        public int ENTITYID { get; set; }
        public byte[] PICTURE { get; set; }
    }

    
    public class NeedAttentionData : iItemType
    {
        public int count_ { get; set; }
        public string Type { get; set; }
        public int VendorId { get; set; }
        public string FirstName { get; set; }
        public string LastName { get; set; }
        public string OrgName { get; set; }
        public string STREET1 { get; set; }
        public string STREET2 { get; set; }
        public string CITY { get; set; }
        public string STATE { get; set; }
        public string ZIP { get; set; }
        public string EMPLOYER { get; set; }
        public string OCCUPATION { get; set; }
        public string FECID { get; set; }
        public int TXNID { get; set; }
        public int TXNTYPEID { get; set; }
        public decimal AMOUNT { get; set; }
        public string FECDESCRIP { get; set; }
        public string CandidatePrefix { get; set; }
        public string CandidateFirstName { get; set; }
        public string CandidateMiddleName { get; set; }
        public string CandidateLastName { get; set; }
        public string CandidateSuffix { get; set; }
        public string CandidateFECID { get; set; }
        public string CandidateOffice { get; set; }
        public string CandidateState { get; set; }
        public string CandidateDistrict { get; set; }
    }
}
