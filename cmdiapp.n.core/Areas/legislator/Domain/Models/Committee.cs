﻿using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

using cmdiapp.n.core.Areas.legislator.Domain.Data;
using cmdiapp.n.core.Domain.ViewModels;

namespace cmdiapp.n.core.Areas.legislator.Domain.Models
{
    [Table("Committee")]
    public class Committee: _entityBase_legislator, iItemType
    {
        [Column, MaxLength(20), Key]
        public string Id { get; set; }

        [Column]
        public bool isSubCommittee { get; set; }

        [Column, MaxLength(20)]
        public string parentId { get; set; }

        [Column, MaxLength(100)]
        public string name { get; set; }

        [Column, MaxLength(100)]
        public string url { get; set; }

        [Column, MaxLength(50)]
        public string phone { get; set; }

        [Column, MaxLength(100)]
        public string address { get; set; }

        [Column]
        public bool active { get; set; }

        [Column]
        public DateTime? createdAt { get; set; }

        [Column]
        public DateTime? updatedAt { get; set; }

        [Column]
        public DateTime? inactivatedAt { get; set; }
    }
}