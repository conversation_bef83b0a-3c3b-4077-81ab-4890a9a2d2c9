﻿using System;
using System.Data;

using cmdiapp.n.core.Areas.crm.Core;

namespace cmdiapp.n.core.Areas.query.Domain.Models
{
    public class QExportCsv : IQExportFormat
    {
        public string FileFormat { get; set; }
        public bool IncludeHeader { get; set; }

        public QExportCsv(bool includeHeader=true)
        {
            FileFormat = "csv";
            IncludeHeader = includeHeader;
        }

        public Tuple<string, string> ToFile(DataTable result, string sheetName)
        {
            TxtFormatParams fmtParams = new TxtFormatParams(Environment.NewLine, ",", true, '\"', false);
            QExportTxt txt = new QExportTxt(fmtParams, IncludeHeader, "csv");
            return txt.ToFile(result, sheetName);
        }
    }
}