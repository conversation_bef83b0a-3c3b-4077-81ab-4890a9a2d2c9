﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Net;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Web.Http;
using System.Web.Script.Serialization;
using System.Drawing;
using System.IO;
using System.Runtime.Serialization.Json;
using System.Xml;
using System.Xml.Linq;
using System.Web.Caching;

using Ninject;
using Ninject.Web.Mvc;

using AutoMapper;

using cmdiapp.n.core;
using cmdiapp.n.core.Core;
using cmdiapp.n.core.Core.App_Start;
using cmdiapp.n.core.Library;
using cmdiapp.n.core.Domain.ViewModels;
using cmdiapp.n.core.Presentation.Controllers;
using cmdiapp.n.core.Areas.crm.Core;
using cmdiapp.n.core.Areas.crm.Domain.Models;
using cmdiapp.n.core.Areas.crm.Domain.Data;
using cmdiapp.n.core.Areas.crm.Domain.Services;
using cmdiapp.n.core.Areas.query.Domain.Models;
using cmdiapp.n.core.Areas.query;
using cmdiapp.n.core.Areas.dataService.Domain.Data;
using cmdiapp.n.core.Areas.dataService.Domain.Models;
using cmdiapp.n.core.Areas.dataService.Domain.Services;

using cmdiapp.n.core.Domain.Models;
using cmdiapp.n.core.Areas.crm.Domain.ViewModels;
using cmdiapp.n.core.Areas.crm.Domain.Repositories;
using cmdiapp.n.core.Areas.crm.ViewModels;

namespace cmdiapp.n.core.Areas.crm.Presentation.Controllers.api
{
     
    [Authorize]
    public class BundlerQController : ApiController
    {
        #region [[ Declaration ]]
        private I_entity_crm _entity_crm;
        private IpeopleService _peopleService;
        private IdataService _dataService;
        //Get User Session Info
        userSession _userSession = session.userSession;
        
        #endregion

        #region [[ (constructor) PeopleQController ]]
        public BundlerQController()
        {
            _entity_crm = I_entityManager_crm.getEntity();
            _peopleService = NinjectMVC.kernel.Get<IpeopleService>();
            _dataService = I_entityManager_ds.getService();
        }
        #endregion
        
        #region [[ sub routines for Search & Export ]]

        #region [ Quick Text Search ]
        private List<BundlerR_ext1> quickTextSearch(string searchText, int pageSize, int pageNo, string sortOptions)
        {
            int trackNo;
            searchText = Library.util.kill_sqlBlacklistWord((searchText ?? "").Trim().Replace("'", "''''"));

            // SQL Definition
            q_def_S _def = q_library.get_q_def_fr_cache("crm_bundlerSearch");

            if (string.IsNullOrEmpty(sortOptions)) sortOptions = "TRACKNO";

            #region [[ by TrackNo ]]
            if (int.TryParse(searchText, out trackNo))
                return _peopleService.get_Bundlers(_def.sq_fieldsV.ToString(), _def.sq_from.ToString(),string.Format("FN.TRACKNO={0}", trackNo), sortOptions, pageSize, pageNo);
            #endregion

            #region [[ by Name ]]
            else
            {
                string _where = "";

                #region [ Split Search Text > string1, string2 & string3 and create "Where" statement ]
                char[] delimiterChars = { ' ', ',', '.', ';', ':', '|', '\t' };

                string[] words = searchText.Split(delimiterChars);

                if (words.Count() > 0)
                {
                    string string1 = words[0];
                    string string2 = (words.Count() > 1 ? words[1] : "");
                    string string3 = (words.Count() > 2 ? words[2] : "");

                    string _FNAME_where = "";

                    #region [ Where Clause ]

                    _FNAME_where = q_library.get_normFNAMEwhere("FNAME", string1, _dataService).Replace("'", "''");

                    switch (words.Count())
                    {
                        case 1: // First or Last Name
                            _where = string.Format(_FNAME_where + " OR LNAME LIKE ''{0}%''", string1);
                            break;

                        case 2: // First and Last Name OR Middle and Last  OR First OR Middle OR Last
                            string _MNAME_where = _FNAME_where.Replace("FNAME", "MNAME");
                            _where = string.Format("(" + _FNAME_where + " AND LNAME LIKE ''{0}%'') OR " + "(" + _MNAME_where + " AND LNAME LIKE ''{0}%'')" + " OR FNAME LIKE ''{1}%'' OR MNAME LIKE ''{1}%'' OR LNAME LIKE ''{1}%''", string2, searchText);                            
                            break;

                        case 3: // First, Middle and Last Name
                            _where = string.Format("(" + _FNAME_where + " AND MNAME LIKE ''{0}%'' AND LNAME LIKE ''{1}%'') OR FNAME LIKE ''{2}%'' OR LNAME LIKE ''{2}%''", string2, string3, searchText);
                            break;
                        default:    // 3+.  Match all with First and Last
                            _where = string.Format("(FNAME LIKE ''{0}%'' OR LNAME LIKE ''{0}%'')", searchText);
                            break;
                    }
                    #endregion

                    System.Diagnostics.Stopwatch stopWatch = new System.Diagnostics.Stopwatch();
                    stopWatch.Start();
                    List<BundlerR_ext1> result = _peopleService.get_Bundlers(_def.sq_fieldsV.ToString(), _def.sq_from.ToString(), _where, sortOptions, pageSize, pageNo);
                    stopWatch.Stop();

                    cl_query.create_a_sqlLog(
                        "Quick Text Search",
                        "crm/api/BundlerQ/Search(Text Search)",
                        _where,
                        crmSession.UID().Value,
                        (result != null && result.Count() > 0 ? result.FirstOrDefault().count_ : 0),
                        "Bundler Search - View Columns",
                        "",
                        _entity_crm,
             Convert.ToSingle(stopWatch.ElapsedMilliseconds / 1000.0));

                    return result;
                }
                else
                    return new List<BundlerR_ext1>();
                #endregion

            }
            #endregion
        }
        #endregion

        #region [ (private) Advanced Search - SQL Filtering ]

            #region [ func.string.get_sqlFROM ] Enable optional JOIN tables if used.
            private string get_sqlFROM(string p_sql_where, string p_default_from)
            {
                return p_default_from;
            }
            #endregion

            #region [ func.string.get_sqlFIELDs ] Enable optional OUTPUT fields if used.
            private string get_sqlFIELDs(string p_sql_where, string p_default_fields)
            {
                return p_default_fields;
            }
            #endregion

            #endregion

        #region [ Advanced Search ]
        private List<BundlerR_ext1> sqlSearch(List<searchItem> filters, int pageSize, int pageNo, string sortOptions, string qDefName = "crm_bundlerSearch")
            {
                if (string.IsNullOrEmpty(sortOptions)) sortOptions = "TRACKNO";

                // SQL Definition
                q_def_S _def = q_library.get_q_def_fr_cache(qDefName);

                System.Diagnostics.Stopwatch stopWatch = new System.Diagnostics.Stopwatch();
                stopWatch.Start();
                // Compose SQL
                string sql = q_library.getSQL(_def, filters, session.currentDomain_project._connectionString(), get_sqlFROM, get_sqlFIELDs, true);
                
                string sqlParam = sql.Trim().Replace("'", "''");
                var q = _entity_crm.getContext().Database.SqlQuery<BundlerR_ext1>(String.Format("EXEC dbo.get_pagedDataSet '{0}', '{1}', {2}, {3}", sqlParam, sortOptions, pageSize, pageNo));

                List<BundlerR_ext1> result = q.ToList();
                stopWatch.Stop();

                cl_query.create_a_sqlLog(
                            sql.Trim(), 
                            "crm/api/BundlerQ/Search(Adv)", 
                            util.serialize_toXmlString(filters), 
                            crmSession.UID().Value, 
                            (result != null && result.Count()>0 ? result.FirstOrDefault().count_ : 0), 
                            "Bundler Search - View Columns",
                            "",
                            _entity_crm,
             Convert.ToSingle(stopWatch.ElapsedMilliseconds / 1000.0));

                return result;
            }
        #endregion
                
        #region [ (private-System.Data.DataTable) Advanced Search - Export ]

        private System.Data.DataSet sqlSearch_forExport__dataset(List<searchItem> filters, string finalSortOrder)
        {
            System.Diagnostics.Stopwatch stopWatch = new System.Diagnostics.Stopwatch();
            stopWatch.Start();
            // SQL Definition
            q_def_S _def = q_library.get_q_def_fr_cache("crm_bundlerSearch");

            // Compose SQL
            string sql = q_library.getSQL(_def, filters, session.currentDomain_project._connectionString(), get_sqlFROM, get_sqlFIELDs, false);
            
            sql = sql.Replace("SELECT DISTINCT", "SELECT DISTINCT TOP 100000"); // ############ LIMIT TO 100K FOR NOW)

            System.Data.DataSet _dataSet = q_library.get_dataset_w_sql__single(session.currentDomain_project._connectionString(), sql, "result", 0, 0);
            stopWatch.Stop();

            cl_query.create_a_sqlLog(
            sql.Trim(),
            "crm/api/BundlerQ/Export",
            util.serialize_toXmlString(filters),
            crmSession.UID().Value,
            (_dataSet != null && _dataSet.Tables.Count > 0 && _dataSet.Tables[0].Rows.Count > 0 ? _dataSet.Tables[0].Rows.Count : 0),
            "Bundler Search - Export Columns",
            "HTTP",     // Saved Path: HTTP Flush 
            _entity_crm,
             Convert.ToSingle(stopWatch.ElapsedMilliseconds / 1000.0));

            //return _dataSet.Tables[0];
            return _dataSet;
        }

        //Export to Excel for Quick Search...
        private System.Data.DataSet Export_QuickSearch(string searchText, string sortOrder)
        {
            //Now let us process to get the Dataset...
            int trackNo;
            string sql = "";
                          
            #region [[ by TrackNo ]]
            if (int.TryParse(searchText, out trackNo))
                sql = string.Format("SELECT DISTINCT TRACKNO,PID, PEOTYPE, PEOCODE, PREFIX, FNAME, MNAME,LNAME, EMAIL,SUFFIX, TITLE,FORMSALUT, INFSALUT, MAILSALUT, MAILNAME,PRIMEMAIL, ASSISTANT, STREET, ADDR1, ADDR2, CITY, STATE,ZIP,PLUS4,COUNTY,REGION,METROAREA,HMPHN,BSPHN,FAX,CELL,EMAIL,CTDGIFT,COMMITMENT,cRaised, cRollup,cSHARED,cReferred AS INCENTIVE,Contact_Prefix,Contact_FirstName,Contact_MiddleName, Contact_LastName,Contact_Suffix, Contact_Email, Contact_CellPhone, Contact_BusPhone, Contact_HomePhone FROM v_Bundler WHERE TRACKNO={0}", trackNo);
                
            #endregion

            #region [[ by Name ]]
            else
            {
                string _where = "";

                #region [ Split Search Text > string1, string2 & string3 and create "Where" statement ]
                char[] delimiterChars = { ' ', ',', '.', ';', ':', '|', '\t' };

                string[] words = searchText.Split(delimiterChars);

                if (words.Count() > 0)
                {
                    string string1 = words[0];
                    string string2 = (words.Count() > 1 ? words[1] : "");
                    string string3 = (words.Count() > 2 ? words[2] : "");

                    string _FNAME_where = "";

                    #region [ Where Clause ]

                    _FNAME_where = q_library.get_normFNAMEwhere("FNAME", string1.Replace("'", "''"), _dataService);

                    switch (words.Count())
                    {
                        case 1: // First or Last Name
                            _where = string.Format(_FNAME_where + " OR LNAME LIKE '{0}%'", string1.Replace("'", "''"));
                            break;

                        case 2: // First and Last Name  OR First OR Last
                            _where = string.Format("(" + _FNAME_where + " AND LNAME LIKE '{0}%') OR FNAME LIKE '{1}%' OR LNAME LIKE '{1}%'", string2.Replace("'", "''"), searchText);
                            break;

                        case 3: // First, Middle and Last Name
                            _where = string.Format("(" + _FNAME_where + " AND MNAME LIKE '{0}%' AND LNAME LIKE '{1}%') OR FNAME LIKE '{2}%' OR LNAME LIKE '{2}%'", string2.Replace("'", "''"), string3.Replace("'", "''"), searchText.Replace("'", "''"));
                            break;
                        default:
                            break;
                    }
                    #endregion

                    #region [[ Prepare the SQL ]]

                    //main SQL
                    sql = "SELECT DISTINCT TRACKNO,PID, PEOTYPE, PEOCODE, PREFIX, FNAME, MNAME,LNAME, EMAIL,SUFFIX, TITLE,FORMSALUT, INFSALUT, MAILSALUT, MAILNAME,PRIMEMAIL, ASSISTANT, STREET, ADDR1, ADDR2, CITY, STATE,ZIP,PLUS4,COUNTY,REGION,METROAREA,HMPHN,BSPHN,FAX,CELL,EMAIL,CTDGIFT,COMMITMENT,cRaised, cRollup,cSHARED,cReferred AS INCENTIVE,Contact_Prefix,Contact_FirstName,Contact_MiddleName, Contact_LastName,Contact_Suffix, Contact_Email, Contact_CellPhone, Contact_BusPhone, Contact_HomePhone from v_Bundler ";

                    //Final  
                    sql = string.Format(sql + " WHERE {0} ", _where);
                                        
                    //For Sort Order...
                    if (!string.IsNullOrEmpty(sortOrder))
                    {
                        sql = sql + " Order By " + sortOrder;
                    }
                    #endregion

                }
                #endregion

            }
            #endregion

            System.Data.DataSet _dataSet = q_library.get_dataset_w_sql__single(session.currentDomain_project._connectionString(), sql, "result", 0, 0);
            //return _dataSet.Tables[0];
            return _dataSet;
        }

        private List<BundlerR_ext1> sqlSearch_forExport__list(List<searchItem> filters)
        {
            // SQL Definition
            q_def_S _def = q_library.get_q_def_fr_cache("crm_bundlerSearch");

            // Compose SQL
            string sql = q_library.getSQL(_def, filters, session.currentDomain_project._connectionString(), get_sqlFROM, get_sqlFIELDs, false);
            sql = sql.Replace("SELECT DISTINCT", "SELECT DISTINCT TOP 100000"); 

            var q = _entity_crm.getContext().Database.SqlQuery<BundlerR_ext1>(String.Format("{0}", sql));
            return q.ToList();
        }
        #endregion

        #endregion
        
        #region [[ (genericResponse) Search[POST] - crm/api/BundlerQ/Search ]]
        [HttpPost, Route("crm/api/BundlerQ/Search")]
        [apiAuthorize(AccessElement = "cmdiapp.dms.bundler_qq", AccessLevel = "v")]
        public genericResponse Search(searchParam _param, string qDefName="crm_bundlerSearch")
        {
            #region [ Retrieve Input ]
            string _searchText = _param.searchText_;

            if (string.IsNullOrEmpty(_searchText) 
                && (_param.searchData == null || _param.searchData.Count()==0 || string.IsNullOrEmpty(_param.searchData[0].valuef)))
                return new genericResponse(){ __count=0, success=true };

            int _pageSize = _param.pageSize_;
            int _pageNo = _param.page_;
            string _sortOptions = _param.sortOption1_;
            #endregion

            List<BundlerR_ext1> _list;

            #region [ 1. Quick Text Search ]
            if (!string.IsNullOrEmpty(_searchText))
                _list = quickTextSearch(_searchText, _pageSize, _pageNo, _sortOptions);
            #endregion

            #region [ 2. Search Data ]
            else
                _list = sqlSearch(_param.searchData, _pageSize, _pageNo, _sortOptions, qDefName);
            #endregion

            if (_list != null && _list.Count() > 0)
            {
                Mapper.CreateMap<BundlerR_ext1, rBundlerR>();
                IEnumerable<iItemType> results = _list.Select(a => Mapper.Map<BundlerR_ext1, rBundlerR>(a)).ToList();
                return new genericResponse() { success = true, __count = _list.FirstOrDefault().count_, results = results.ToList() };
            }

            return new genericResponse() { success = true, __count = 0 };
        }
        #endregion 
                
        #region [[ Export ]]
        
        [HttpPost, Route("crm/api/BundlerQ/ExportKey")]
        [apiAuthorize(AccessElement = "cmdiapp.dms.bundler_qq", AccessLevel = "e")]
        public string ExportKey(searchParam param)
        {
            string key_ = System.Guid.NewGuid().ToString();
            HttpRuntime.Cache.Insert(key_, param, null, DateTime.UtcNow.Add(new TimeSpan(1, 0, 1)), System.Web.Caching.Cache.NoSlidingExpiration);
            //back...
            return key_;
        }

        #region [[ For Quick Search Export : By Bhavesh ]]
        [HttpPost, Route("crm/api/BundlerQ/QExportKey")]
        [apiAuthorize(AccessElement = "cmdiapp.dms.bundler_qq", AccessLevel = "e")]
        public string QExportKey(searchParam param)
        {
            string key_ = System.Guid.NewGuid().ToString();
            HttpRuntime.Cache.Insert(key_, param, null, DateTime.UtcNow.Add(new TimeSpan(1, 0, 1)), System.Web.Caching.Cache.NoSlidingExpiration);
            //back
            return key_;
        }

        [HttpGet, Route("crm/api/BundlerQ/QExport")]
        [apiAuthorize(AccessElement = "cmdiapp.dms.bundler_qq", AccessLevel = "e")]
        public string QExport(string key)
        {
            key = key.Replace("\"", "").Trim();
            //get the Parameter with SearchText and Sort Options Back...
            
            searchParam _param = (searchParam)HttpRuntime.Cache[key];

            //Let us get sort Options..
            string _sortOptions = "TRACKNO"; 

            //Create Dataset now...
            System.Data.DataSet _ds = Export_QuickSearch(_param.searchText, _sortOptions);
            //go ahead...
            if (_ds != null)
            {
                string key_ = System.Guid.NewGuid().ToString();
                HttpRuntime.Cache.Insert(key_, _ds, null, DateTime.UtcNow.Add(new TimeSpan(1, 0, 1)), System.Web.Caching.Cache.NoSlidingExpiration);
                //return the Key
                return key_;
            }
            else
            {
                return null;
            }
        }
        #endregion

        [HttpGet, Route("crm/api/BundlerQ/Export")]
        [apiAuthorize(AccessElement = "cmdiapp.dms.bundler_qq", AccessLevel = "e")]
        public string Export(string key)
        {
            key = key.Replace("\"", "").Trim();

            searchParam _param = (searchParam)HttpRuntime.Cache[key];

            //Let us get sort Options..
            string _sortOptions = "TRACKNO"; 

            System.Data.DataSet _ds = sqlSearch_forExport__dataset(_param.searchData, _sortOptions);

            if (_ds != null)
            {
                string key_ = System.Guid.NewGuid().ToString();
                HttpRuntime.Cache.Insert(key_, _ds, null, DateTime.UtcNow.Add(new TimeSpan(1, 0, 1)), System.Web.Caching.Cache.NoSlidingExpiration);
                //return the Key
                return key_;
            }
            else
            {
                return null;
            }

        }
        #endregion

        [HttpGet, Route("crm/api/BundlerQ/Search")]
        [apiAuthorize(AccessElement = "cmdiapp.dms.bundler_qq", AccessLevel = "v")]
        public List<BundlerListDisplay> GetBundlers(string searchText, int page = 1, int pageSize = 10)
        {
            return _peopleService.GetBundlersList(searchText, page, pageSize);
        }
    }
}
