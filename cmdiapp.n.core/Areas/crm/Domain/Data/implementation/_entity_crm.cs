﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Linq.Expressions;
using System.Data.Linq;
using System.Diagnostics;
using System.IO;
using System.Data;
using System.Data.Entity;

using Ninject;
using Ninject.Web.Mvc;

using cmdiapp.n.core.Core;
using cmdiapp.n.core.Core.App_Start;
using cmdiapp.n.core.Domain.Models;
using cmdiapp.n.core.Domain.Repositories;
using cmdiapp.n.core.Areas.crm.Domain.Repositories;
using System.Data.Common;

namespace cmdiapp.n.core.Areas.crm.Domain.Data
{
    public class _entity_crm : I_entity_crm
    {
        private DbContext _context;
        private bool _contextDisposed;
        private bool _contextInitialized;
        private readonly string _connectionString;

        #region [[ Constructors ]]
        public _entity_crm()
        {
            //project currentProject = session.active_projectDomains.Where(a => a.projectId == session.currentDomain_projectId).FirstOrDefault().project;
            //IprojectRepository _projRepository = NinjectMVC.kernel.Get<IprojectRepository>();
            //project currentProject = _projRepository.All().Where(a => a.projectId == session.currentDomain_projectId).FirstOrDefault();
            project currentProject = session.currentDomain_project;

            _connectionString = (currentProject != null ? currentProject._connectionString() : "no valid project");
        }

        public _entity_crm(project project)
        {
            _connectionString = (project != null ? project._connectionString() : "no valid project");
        }

        public _entity_crm(string connectionString)
        {
            _connectionString = connectionString;
        }
        #endregion

        public DbContext getContext()
        {
            return _context ?? (_context = new _crmDBContext(_connectionString));
        }

        public DbConnection GetConnection()
        {
            return getContext().Database.Connection;
        }

        public void CommitChanges()
        {
            Initialize();
            _context.SaveChanges();
        }

        public void Delete<TSource>(Expression<Func<TSource, bool>> expression) where TSource : _entityBase_crm, new()
        {
            Initialize();
            var query = All<TSource>().Where(expression);
            foreach (TSource item in query.ToList())
                Delete<TSource>(item);
        }

        public void Delete<TSource>(TSource item) where TSource : _entityBase_crm, new()
        {
            Initialize();
            GetTable<TSource>().Remove(item);
        }

        public TSource Single<TSource>(Expression<Func<TSource, bool>> expression) where TSource : _entityBase_crm, new()
        {
            Initialize();
            return GetTable<TSource>().FirstOrDefault<TSource>(expression);
        }

        public IQueryable<TSource> All<TSource>() where TSource : _entityBase_crm, new()
        {
            Initialize();
            return GetTable<TSource>().AsQueryable<TSource>();
        }

        public void Add<TSource>(TSource item) where TSource : _entityBase_crm, new()
        {
            Initialize();
            GetTable<TSource>().Add(item);
        }

        public void Add<TSource>(IEnumerable<TSource> items) where TSource : _entityBase_crm, new()
        {
            Initialize();
            foreach (TSource item in items.ToList())
                Add<TSource>(item);
        }

        public void Update<TSource>(TSource item) where TSource : _entityBase_crm, new()
        {
            Initialize();
            _context.Entry<TSource>(item).State = EntityState.Modified;
        }

        private DbSet<TSource> GetTable<TSource>() where TSource : _entityBase_crm, new()
        {
            return _context.Set<TSource>();
        }

        public void SetLog(TextWriter writer)
        {
            Initialize();

            // .Log is supported in EF6
            //_context.Database.Log = writer;
        }

        /*
        public void LoadWith<TSource>(Expression<Func<TSource, object>> function) where TSource : _entityBase, new()
        {
            Initialize();
            var loadOptions = _context.LoadOptions ?? new DataLoadOptions();
            loadOptions.LoadWith<TSource>(function);
        }
        */
        public void Dispose()
        {
            if (_context != null)
            {
                _context.Dispose();
                _contextDisposed = true;
            }
        }

        private void Initialize()
        {
            if (!_contextInitialized || _contextDisposed == true)
            {
                _context = new _crmDBContext(_connectionString);
                _context.Configuration.LazyLoadingEnabled = true;
                _contextInitialized = true;
            }
        }

        public void Refresh<TSource>(TSource item) where TSource : _entityBase_crm, new()
        {
            _context.Entry<TSource>(item).Reload();
        }

        class DebugTextWriter : System.IO.TextWriter
        {
            public override void Write(char[] buffer, int index, int count)
            {
                // Debug.Write(new String(buffer, index, count));
            }

            public override void Write(string value)
            {
                Debug.Write(value);
            }

            public override Encoding Encoding
            {
                get { return Encoding.Default; }
            }
        }
    }
}