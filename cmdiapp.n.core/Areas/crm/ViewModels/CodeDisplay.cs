﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.Serialization;
using System.Web;

namespace cmdiapp.n.core.Areas.crm.ViewModels
{
    [DataContract]
    public class CodeDisplay
    {
        
        [DataMember(Name = "id")]
        public int Id { get; set; }

        [DataMember(Name = "code")]
        public string Code { get; set; }

        [DataMember(Name = "description")]
        public string Description { get; set; }

        [DataMember(Name = "searchOn")]
        public string SearchOn { get; set; }

        [DataMember(Name = "codeType")]
        public CodeType CodeType { get; set; }
    }
}