﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

using Ninject;
using Ninject.Web.Mvc;

using cmdiapp.n.core.Core.App_Start;
using cmdiapp.n.core.Areas.crm.Domain.Models;
using cmdiapp.n.core.Areas.crm.Domain.ViewModels;
using cmdiapp.n.core.Areas.crm.Domain.Repositories;

namespace cmdiapp.n.core.Areas.crm.Domain.Services
{
    public class clubStatusService : IclubStatusService
    {
        private I___unitOfWork _unitOfWork;
        private I__DBFactory _dbFactory;
        private IclubStatusRepository _clubStatusRepository;

        public clubStatusService(I___unitOfWork unitOfWork, I__DBFactory dbFactory, IclubStatusRepository ClubStatusRepository)
        {
            _unitOfWork = unitOfWork;
            _dbFactory = dbFactory;
            _clubStatusRepository = ClubStatusRepository;
        }

        public lkCLUBSTAT get_clubStatus(int clubStatusId)
        {
            string sql = string.Format("SELECT * FROM lkCLUBSTAT WHERE CLUBSTATID={0}", clubStatusId);
            var q = _dbFactory.getContext().Database.SqlQuery<lkCLUBSTAT>(sql);
            return q.FirstOrDefault();
        }

        public bool check_unqiueness(lkCLUBSTAT _lkCLUBSTAT)
        {
            // take care of apostrophe
            string v = _lkCLUBSTAT.DESCRIP.Replace("'", "''");
            string sql = "";

            if (_lkCLUBSTAT.CLUBSTATID > 0)   // Existing Record
                sql = String.Format("SELECT Count(*) as recordCount FROM lkCLUBSTAT WHERE DESCRIP = '{0}' AND CLUBSTATID <> {1} AND CLUBID = {2}", v, _lkCLUBSTAT.CLUBSTATID, _lkCLUBSTAT.CLUBID);
            else
                sql = String.Format("SELECT Count(*) as recordCount FROM lkCLUBSTAT WHERE DESCRIP = '{0}' AND CLUBID = {1}", v, _lkCLUBSTAT.CLUBID);

            var q = _dbFactory.getContext().Database.SqlQuery<UniquenessCheck>(sql);
            return q.FirstOrDefault().recordCount == 0 ? true : false;
        }

        public bool check_record_exists(int clubStatusId)
        {
            //For jtCLUB
            string sql = string.Format("SELECT Count(*) as recordCount FROM jtCLUB WHERE cSTATUS='{0}'", clubStatusId);
            var q = _dbFactory.getContext().Database.SqlQuery<UniquenessCheck>(sql);

            //return q.ToList().Count > 0 ? true : false;
            return q.FirstOrDefault().recordCount > 0 ? true : false;
            
        }
        
        public void Add(lkCLUBSTAT _lkCLUBSTAT)
        {
            _clubStatusRepository.Add(_lkCLUBSTAT);
            _unitOfWork.commit();
        }

        public void Update(lkCLUBSTAT _lkCLUBSTAT)
        {
            _clubStatusRepository.Update(_lkCLUBSTAT);
            _unitOfWork.commit();
        }

        public void Delete(lkCLUBSTAT _lkCLUBSTAT)
        {
            _clubStatusRepository.Delete(_lkCLUBSTAT);
            _unitOfWork.commit();
        }

        //Read all Club Status Records
        public List<lkCLUBSTAT_ext> read_all_clubStatuses()
        {
            string sql = "SELECT CLUBSTATID,lkCLUBSTAT.CLUBID, CLUBCODE, DESCRIP from lkCLUBSTAT join pmclub on pmclub.CLUBID = lkCLUBSTAT.CLUBID";

            var q = _dbFactory.getContext().Database.SqlQuery<lkCLUBSTAT_ext>(String.Format("EXEC dbo.get_allDataSet '{0}'", sql));

            return q.ToList();
        }

        public List<lkCLUBSTAT_ext> get_all_clubstatuses(string where, string orderBy, int pageSize, int pageNo)
        {
            string sql = (!string.IsNullOrEmpty(where) ? string.Format("SELECT CLUBSTATID,lkCLUBSTAT.CLUBID, CLUBCODE, DESCRIP from lkCLUBSTAT join pmclub on pmclub.CLUBID = lkCLUBSTAT.CLUBID WHERE {0}", where) : "SELECT CLUBSTATID,lkCLUBSTAT.CLUBID, CLUBCODE, DESCRIP from lkCLUBSTAT join pmclub on pmclub.CLUBID = lkCLUBSTAT.CLUBID");

            var q = _dbFactory.getContext().Database.SqlQuery<lkCLUBSTAT_ext>(String.Format("EXEC dbo.get_pagedDataSet '{0}', '{1}', {2}, {3}", sql, orderBy, pageSize, pageNo));

            return q.ToList();
        }
    }
}