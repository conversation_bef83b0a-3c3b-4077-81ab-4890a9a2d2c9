﻿using cmdiapp.n.core._Domain.Models;
using cmdiapp.n.core.Areas.crm.Core;
using cmdiapp.n.core.Areas.crm.Domain.Data;
using cmdiapp.n.core.Areas.crm.Domain.Models;
using cmdiapp.n.core.Areas.query;
using cmdiapp.n.core.Areas.query.Domain.Models;
using cmdiapp.n.core.Core;
using cmdiapp.n.core.Domain.Data;
using cmdiapp.n.core.Domain.Models;
using cmdiapp.n.core.Domain.ViewModels;
using cmdiapp.n.core.Presentation.Controllers;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.Data.SqlTypes;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Web;
using System.Web.Http;

namespace cmdiapp.n.core.Areas.crm.Presentation.api
{
    [Authorize]
    public class CagingController : ApiController
    {
        private I_entity _entity;
        private I_entity_crm _entity_crm;
        userSession _userSession = session.userSession;

        class exportbatchlogparam
        {
            public int cageacctid { get; set; }
            public DateTime batchdte { get; set; }
        }

        class exportbalancesheetparam
        {
            public int projectid { get; set; }
            public DateTime batchdte { get; set; }
        }

        /*
        public class CheckImageId
        {
            public int MONYimageID { get; set; }
            public Int16 Seq { get; set; }
        }
        */

        public CagingController()
        {
            _entity = I_entityManager.getEntity();
            _entity_crm = I_entityManager_crm.getEntity();
        }

        [HttpGet, Route("api/caging/clients/get")]
        [apiAuthorize(AccessElement = "CMDI Caging", AccessLevel = "v")]
        public IQueryable<CagingClient> CagingClientsGet()
        {
            string sql = "select distinct projectid, name from v_caging_config  order by name";
            return _entity.getContext().Database.SqlQuery<CagingClient>(sql).AsQueryable();
        }

        [HttpGet, Route("api/caging/accounts/get/{id}")]
        [apiAuthorize(AccessElement = "CMDI Caging", AccessLevel = "v")]
        public IQueryable<caging_account> CagingAccountsGet(int id)
        {
            string sql = String.Format("select * from caging_account where projectid = {0} order by acctname", id);
            return _entity.getContext().Database.SqlQuery<caging_account>(sql).AsQueryable();
        }

        [HttpGet, Route("api/caging/traylog/get")]
        [apiAuthorize(AccessElement = "CMDI Caging", AccessLevel = "v")]
        public IQueryable<CagingTray> CagingTrayGet(int id, string receiveddte)
        {
            string sql = String.Format("SELECT t.cagetrayid, a.cageacctid, a.acctname, CAST('{1}' as DATE) as receiveddte, t.traycount, t.piececount " +
                "FROM caging_account a LEFT JOIN caging_tray_log t ON a.cageacctid = t.cageacctid AND t.receiveddte = '{1}' " +
                "WHERE a.projectid = {0}", id, receiveddte.ToString());
            return _entity.getContext().Database.SqlQuery<CagingTray>(sql).AsQueryable();
        }

        [HttpPost, Route("api/caging/traylog/save")]
        public genericResponse TrayLogSave(string data)
        {
            try
            {
                caging_tray_log record;
                genericResponse result = new genericResponse();

                List<CagingTray> logs = JsonConvert.DeserializeObject<List<CagingTray>>(data);
                foreach (CagingTray log in logs)
                {
                    if (log.cagetrayid == null || log.cagetrayid == 0)
                    {
                        if (log.traycount > 0)
                        {
                            record = new caging_tray_log();
                            record.cageacctid = (int)log.cageacctid;
                            record.receiveddte = log.receiveddte;
                            record.traycount = log.traycount;
                            record.piececount = log.piececount;
                            record.updatedon = DateTime.Now;

                            _entity.Add<caging_tray_log>(record);
                            _entity.CommitChanges();
                        }
                    }
                    else
                    {
                        record = _entity.Single<caging_tray_log>(t => t.cagetrayid == log.cagetrayid);
                        if (log.traycount == null || log.traycount == 0)
                        {
                            if (record != null)
                            {
                                _entity.Delete(record);
                                _entity.CommitChanges();
                            }
                        }
                        else
                        {
                            record.cageacctid = (int)log.cageacctid;
                            record.receiveddte = log.receiveddte;
                            record.traycount = log.traycount;
                            record.piececount = log.piececount;
                            record.updatedon = DateTime.Now;
                            _entity.Update(record);
                            _entity.CommitChanges();
                        }
                    }
                }
                result.success = true;
                return result;
            }
            catch (Exception ex)
            {
                return new genericResponse() { success=false };
            }
        }

        [HttpGet, Route("api/caging/fundcodes/get")]
        [apiAuthorize(AccessElement = "CMDI Caging", AccessLevel = "v")]
        public IQueryable<caging_fundlist> CagingFundCodeGet(int projectid, int cageacctid)
        {
            try
            {
                project _project = _entity.Single<project>(p => p.projectId == projectid);
                string sql = String.Format("select distinct f.fundid, f.fundcode, f.funddesc from v_caging_config v inner join {0}.{1}.dbo.dmFUND f on v.fundid = f.fundid and f.active = 1 where v.cageacctid = {2} order by f.fundcode ",
                        _project.db_datasource, _project.db_initialcatalog, cageacctid);
                return _entity.getContext().Database.SqlQuery<caging_fundlist>(sql).AsQueryable();
            }
            catch (Exception ex)
            {
                return null;
            }
        }

        [HttpGet, Route("api/caging/acctcodes/get")]
        [apiAuthorize(AccessElement = "CMDI Caging", AccessLevel = "v")]
        public IQueryable<caging_acctlist> CagingAcctCodeGet(int projectid, int cageacctid)
        {
            try
            {
                project _project = _entity.Single<project>(p => p.projectId == projectid);
                string sql = String.Format("select CENTERID, CENTERCODE, DESCRIP, CASE WHEN CENTERCODE = 'N/A' THEN '' ELSE CENTERCODE END as SORT from {0}.{1}.dbo.dmCENTER where ACTIVE = 1 ORDER BY 4", _project.db_datasource, _project.db_initialcatalog);
                return _entity.getContext().Database.SqlQuery<caging_acctlist>(sql).AsQueryable();
            }
            catch (Exception ex)
            {
                return null;
            }
        }

        [HttpGet, Route("api/caging/batchlog/get")]
        [apiAuthorize(AccessElement = "CMDI Caging", AccessLevel = "v")]
        public IQueryable<CagingBatch> CagingBatchGet(int id, string batchdte)
        {
            DateTime _batchdte = DateTime.Parse(batchdte);
            string sql = String.Format("EXEC dbo.z_caging_batch_log {0}, '{1}'", id, _batchdte);
            return _entity.getContext().Database.SqlQuery<CagingBatch>(sql).AsQueryable();
        }

        [HttpPost, Route("api/caging/batchlog/add")]
        public genericResponse BatchLogAdd(string data)
        {
            caging_batch_log batch = JsonConvert.DeserializeObject<caging_batch_log>(data);
            caging_account cagingacct = _entity.Single<caging_account>(a => a.cageacctid == batch.cageacctid);
            if (cagingacct == null)
            {
                return new genericResponse() { success = false, message = "Problem locating Caging Account - notify Admin!" };
            }
            project client = _entity.Single<project>(p => p.projectId == cagingacct.projectid);
            if (client == null)
            {
                return new genericResponse() { success = false, message = "Problem locating Caging Client - notify Admin!" };
            }
            string monytype;
            switch (batch.paytype)
            {
                case "Cash":
                    monytype = "CA";
                    break;
                case "Card":
                    monytype = "CC";
                    break;
                case "Other":
                    monytype = "OT";
                    break;
                case "In-Kind":
                    monytype = "IK";
                    break;
                default:
                    monytype = "CH";
                    break;
            }
            DateTime batchdte = Convert.ToDateTime(batch.batchdte.Date.ToString("MM/dd/yyyy"));
            // create batch in crimson
            string sql = String.Format("EXEC {0}.{1}.dbo.z_cagebatch_create '{2}', '{3}', {4} , {5}, '{6}', '{7}', '{8}', '{9}', {10}, '{11}'",
                client.db_datasource, client.db_initialcatalog, batch.batchno, batchdte, 
                (batch.batchcnt == null? -1 : batch.batchcnt), 
                (batch.batchamt == null? -1 : batch.batchamt),
                monytype, batch.fundcode, batch.acctcode, batch.srcecode, (batch.nondonorct == null ? -1 : batch.nondonorct), session.userSession.UserName) ;
            sp_caging_result rtnresult = _entity.getContext().Database.SqlQuery<sp_caging_result>(sql).FirstOrDefault();
            if (rtnresult.BATCHID <= 0)
            {
                return new genericResponse() { success = false, message = rtnresult.MESSAGE };
            }
            // save batch log
            caging_batch_log record;
            if (batch.cagebatchid == 0)
            {
                record = new caging_batch_log()
                {
                    cageacctid = batch.cageacctid,
                    batchno = batch.batchno,
                    batchdte = batchdte,
                    processeddte = Convert.ToDateTime(batch.processeddte.Date.ToString("MM/dd/yyyy")),
                    batchcnt = batch.batchcnt,
                    batchamt = batch.batchamt,
                    paytype = batch.paytype,
                    fundcode = batch.fundcode,
                    acctcode = batch.acctcode,
                    srcecode = batch.srcecode,
                    nondonorct = batch.nondonorct,
                    processor = batch.processor,
                    note = batch.note,
                    crmbatchid = rtnresult.BATCHID,
                    updatedon = DateTime.Now,
                    processed = batch.processed 
                };
                _entity.Add<caging_batch_log>(record);
                _entity.CommitChanges();
            }
            else
            {
                record = _entity.Single<caging_batch_log>(b => b.cagebatchid == batch.cagebatchid);
                record.cageacctid = batch.cageacctid;
                record.batchno = batch.batchno;
                record.batchdte = batchdte;
                record.processeddte = Convert.ToDateTime(batch.processeddte.Date.ToString("MM/dd/yyyy"));
                record.batchcnt = batch.batchcnt;
                record.batchamt = batch.batchamt;
                record.paytype = batch.paytype;
                record.fundcode = batch.fundcode;
                record.acctcode = batch.acctcode;
                record.srcecode = batch.srcecode;
                record.nondonorct = batch.nondonorct;
                record.processor = batch.processor;
                record.note = batch.note;
                record.processed = batch.processed;
                record.crmbatchid = rtnresult.BATCHID;
                record.updatedon = DateTime.Now;
                _entity.Update<caging_batch_log>(record);
                _entity.CommitChanges();
            }

            caging_batch_log result = _entity.Single<caging_batch_log>(b => b.cagebatchid == record.cagebatchid);
            List<iItemType> _dataset = new List<iItemType>();
            _dataset.Add(result);
            return new genericResponse() { success = true, message = "Batch is created successfully.", results = _dataset };
        }

        [HttpPost, Route("api/caging/batchlog/update")]
        public genericResponse BatchLogUpdate(string data)
        {
            caging_batch_log batch = JsonConvert.DeserializeObject<caging_batch_log>(data);
            caging_account cagingacct = _entity.Single<caging_account>(a => a.cageacctid == batch.cageacctid);
            if (cagingacct == null)
            {
                return new genericResponse() { success = false, message = "Problem locating Caging Account - notify Admin!" };
            }
            project client = _entity.Single<project>(p => p.projectId == cagingacct.projectid);
            if (client == null)
            {
                return new genericResponse() { success = false, message = "Problem locating Caging Client - notify Admin!" };
            }
            string monytype;
            switch (batch.paytype)
            {
                case "Cash":
                    monytype = "CA";
                    break;
                case "Card":
                    monytype = "CC";
                    break;
                case "Other":
                    monytype = "OT";
                    break;
                case "In-Kind":
                    monytype = "IK";
                    break;
                default:
                    monytype = "CH";
                    break;
            }
            DateTime batchdte = Convert.ToDateTime(batch.batchdte.Date.ToString("MM/dd/yyyy"));
            // create batch in crimson
            string sql = String.Format("EXEC {0}.{1}.dbo.z_cagebatch_update '{2}', '{3}', {4}, {5}, '{6}', '{7}', '{8}', '{9}', {10}, '{11}', {12}",
                client.db_datasource, client.db_initialcatalog, batch.batchno, batchdte, (batch.batchcnt == null ? "null" : batch.batchcnt.ToString()), 
                (batch.batchamt == null ? "null" : batch.batchamt.ToString()),
                monytype, batch.fundcode, batch.acctcode, batch.srcecode, (batch.nondonorct == null ? -1 : batch.nondonorct), session.userSession.UserName, batch.crmbatchid);
            sp_caging_result rtnresult = _entity.getContext().Database.SqlQuery<sp_caging_result>(sql).FirstOrDefault();
            if (rtnresult.BATCHID <= 0)
            {
                return new genericResponse() { success = false, message = rtnresult.MESSAGE };
            }
            // Update batch log
            caging_batch_log record;
            record = _entity.Single<caging_batch_log>(b => b.cagebatchid == batch.cagebatchid);
            record.batchno = batch.batchno;
            record.batchdte = batchdte;
            record.processeddte = Convert.ToDateTime(batch.processeddte.Date.ToString("MM/dd/yyyy"));
            record.batchcnt = batch.batchcnt;
            record.batchamt = batch.batchamt;
            record.paytype = batch.paytype;
            record.fundcode = batch.fundcode;
            record.acctcode = batch.acctcode;
            record.srcecode = batch.srcecode;
            record.nondonorct = batch.nondonorct;
            record.processor = batch.processor;
            record.note = batch.note;
            record.processed = batch.processed;
            record.crmbatchid = rtnresult.BATCHID;
            record.updatedon = DateTime.Now;
            _entity.Update<caging_batch_log>(record);
            _entity.CommitChanges();

            caging_batch_log result = _entity.Single<caging_batch_log>(b => b.cagebatchid == record.cagebatchid);
            List<iItemType> _dataset = new List<iItemType>();
            _dataset.Add(result);
            return new genericResponse() { success = true, message = "Batch is updated successfully.", results = _dataset };
        }

        [HttpPost, Route("api/caging/batchlog/delete")]
        public genericResponse BatchLogDelete(string data)
        {
            caging_batch_log batch = JsonConvert.DeserializeObject<caging_batch_log>(data);
            caging_account cagingacct = _entity.Single<caging_account>(a => a.cageacctid == batch.cageacctid);
            if (cagingacct == null)
            {
                return new genericResponse() { success = false, message = "Problem locating Caging Account - notify Admin!" };
            }
            project client = _entity.Single<project>(p => p.projectId == cagingacct.projectid);
            if (client == null)
            {
                return new genericResponse() { success = false, message = "Problem locating Caging Client - notify Admin!" };
            }
            // delete batch in crimson
            string sql = String.Format("EXEC {0}.{1}.dbo.z_cagebatch_delete '{2}'",
                client.db_datasource, client.db_initialcatalog, batch.crmbatchid);
            sp_caging_result rtnresult = _entity.getContext().Database.SqlQuery<sp_caging_result>(sql).FirstOrDefault();
            if (rtnresult.BATCHID <= 0)
            {
                return new genericResponse() { success = false, message = rtnresult.MESSAGE };
            }
            // delete batch log
            caging_batch_log record;
            record = _entity.Single<caging_batch_log>(b => b.cagebatchid == batch.cagebatchid);
            _entity.Delete<caging_batch_log>(record);
            _entity.CommitChanges();

            return new genericResponse() { success = true, message = "Batch is deleted successfully." };
        }

        [HttpPost, Route("api/caging/batchlog/savelog")]
        public genericResponse BatchLogSavelog(string data)
        {
            caging_batch_log batch = JsonConvert.DeserializeObject<caging_batch_log>(data);
            caging_account cagingacct = _entity.Single<caging_account>(a => a.cageacctid == batch.cageacctid);
            if (cagingacct == null)
            {
                return new genericResponse() { success = false, message = "Problem locating Caging Account - notify Admin!" };
            }
            project client = _entity.Single<project>(p => p.projectId == cagingacct.projectid);
            if (client == null)
            {
                return new genericResponse() { success = false, message = "Problem locating Caging Client - notify Admin!" };
            }
            // save batch log
            DateTime batchdte = Convert.ToDateTime(batch.batchdte.Date.ToString("MM/dd/yyyy"));
            caging_batch_log record;
            if (batch.cagebatchid == 0)
            {
                record = new caging_batch_log()
                {
                    cageacctid = batch.cageacctid,
                    batchno = batch.batchno,
                    batchdte = batchdte,
                    processeddte = Convert.ToDateTime(batch.processeddte.Date.ToString("MM/dd/yyyy")),
                    batchcnt = batch.batchcnt,
                    batchamt = batch.batchamt,
                    paytype = batch.paytype,
                    fundcode = batch.fundcode,
                    acctcode = batch.acctcode,
                    srcecode = batch.srcecode,
                    nondonorct = batch.nondonorct,
                    processor = batch.processor,
                    note = batch.note,
                    processed = batch.processed,
                    crmbatchid = batch.crmbatchid,
                    updatedon = DateTime.Now
                };
                _entity.Add<caging_batch_log>(record);
                _entity.CommitChanges();
            }
            else
            {
                record = _entity.Single<caging_batch_log>(b => b.cagebatchid == batch.cagebatchid);
                record.cageacctid = batch.cageacctid;
                record.batchno = batch.batchno;
                record.batchdte = batchdte;
                record.processeddte = Convert.ToDateTime(batch.processeddte.Date.ToString("MM/dd/yyyy"));
                record.batchcnt = batch.batchcnt;
                record.batchamt = batch.batchamt;
                record.paytype = batch.paytype;
                record.fundcode = batch.fundcode;
                record.acctcode = batch.acctcode;
                record.srcecode = batch.srcecode;
                record.nondonorct = batch.nondonorct;
                record.processor = batch.processor;
                record.note = batch.note;
                record.processed = batch.processed;
                record.crmbatchid = batch.crmbatchid;
                record.updatedon = DateTime.Now;
                _entity.Update<caging_batch_log>(record);
                _entity.CommitChanges();
            }

            caging_batch_log result = _entity.Single<caging_batch_log>(b => b.cagebatchid == record.cagebatchid);
            List<iItemType> _dataset = new List<iItemType>();
            _dataset.Add(result);
            return new genericResponse() { success = true, message = "Batch Log is saved successfully.", results = _dataset };
        }

        [HttpPost, Route("api/caging/batchlog/deletelog")]
        public genericResponse BatchLogDellog(string data)
        {
            caging_batch_log batch = JsonConvert.DeserializeObject<caging_batch_log>(data);
            // delete batch log
            caging_batch_log record;
            record = _entity.Single<caging_batch_log>(b => b.cagebatchid == batch.cagebatchid);
            _entity.Delete<caging_batch_log>(record);
            _entity.CommitChanges();

            return new genericResponse() { success = true, message = "Batch Log is deleted successfully." };
        }

        [HttpGet, Route("api/caging/tray/get")]
        [apiAuthorize(AccessElement = "CMDI Caging", AccessLevel = "v")]
        public v_caging_tray_log CagingTray(int id)
        {
            return _entity.Single<v_caging_tray_log>(t => t.cagetrayid == id); 
        }

        [HttpGet, Route("api/caging/batch/get")]
        [apiAuthorize(AccessElement = "CMDI Caging", AccessLevel = "v")]
        public v_caging_batch_log CagingBatch(int id)
        {
            return _entity.Single<v_caging_batch_log>(t => t.cagebatchid == id);
        }

        #region [ Retrieve tray counts dashboard data ] GetBy cageacctid
        [HttpGet, Route("api/caging/dailytraycounts/{id}")]
        [apiAuthorize(AccessElement = "CMDI Caging", AccessLevel = "v")]
        public List<cagingdailytraycounts> GetDailyTrayCounts(int id)
        {
            string sql = String.Format("EXEC dbo.z_dsb_caginglast7traycounts {0}", id);
            return _entity.getContext().Database.SqlQuery<cagingdailytraycounts>(sql).ToList();
        }
        #endregion

        #region [ Retrieve batch counts dashboard data ] GetBy cageacctid
        [HttpGet, Route("api/caging/dailybatchcounts/{id}")]
        [apiAuthorize(AccessElement = "CMDI Caging", AccessLevel = "v")]
        public List<cagingdailybatchcounts> GetDailyBatchCounts(int id)
        {
            string sql = String.Format("EXEC dbo.z_dsb_cagingdailybatchcounts {0}", id);
            return _entity.getContext().Database.SqlQuery<cagingdailybatchcounts>(sql).ToList();
        }
        #endregion

        #region [ Retrieve tray counts by period dashboard data ] GetBy cageacctid
        [HttpGet, Route("api/caging/summarytraycounts/{id}")]
        [apiAuthorize(AccessElement = "CMDI Caging", AccessLevel = "v")]
        public List<cagingsummarytraycounts> GetSummaryTrayCounts(int id)
        {
            string sql = String.Format("EXEC dbo.z_dsb_cagingsummarytraycounts {0}", id);
            return _entity.getContext().Database.SqlQuery<cagingsummarytraycounts>(sql).ToList();
        }
        #endregion

        #region [ Retrieve tray counts by period dashboard data ] GetBy cageacctid
        [HttpGet, Route("api/caging/summarybatchcounts/{id}")]
        [apiAuthorize(AccessElement = "CMDI Caging", AccessLevel = "v")]
        public List<cagingsummarybatchcounts> GetSummaryBatchCounts(int id)
        {
            string sql = String.Format("EXEC dbo.z_dsb_cagingsummarybatchcounts {0}", id);
            return _entity.getContext().Database.SqlQuery<cagingsummarybatchcounts>(sql).ToList();
        }
        #endregion

        #region [[ Export Batch Log ]]

        private System.Data.DataSet sqlSearch_forExport__batchlog(int cageacctid, DateTime batchdte)
        {
            // Compose SQL
            string sql = String.Format("EXEC dbo.z_caging_control_sheet {0}, '{1}'", cageacctid, batchdte.ToString());

            System.Data.DataSet _dataSet = q_library.get_dataset_w_sql__single(ConfigurationManager.ConnectionStrings["kernelContext"].ConnectionString, sql, "result", 0, 0);

            //return 
            return _dataSet;
        }
        [HttpPost, Route("api/Caging/ExportBatchLogKey")]
        [apiAuthorize(AccessElement = "CMDI Caging", AccessLevel = "v")]
        public string ExportBatchLogKey(searchParam param)
        {
            string key_ = System.Guid.NewGuid().ToString();
            HttpRuntime.Cache.Insert(key_, param, null, DateTime.UtcNow.Add(new TimeSpan(1, 0, 1)), System.Web.Caching.Cache.NoSlidingExpiration);
            //back...
            return key_;
        }

        [HttpGet, Route("api/Caging/ExportBatchLog")]
        [apiAuthorize(AccessElement = "CMDI Caging", AccessLevel = "v")]
        public string ExportBatchLog(string key)
        {
            searchParam _param = (searchParam)HttpRuntime.Cache[key];

            exportbatchlogparam searchparam = JsonConvert.DeserializeObject<exportbatchlogparam>(_param.searchText);

            System.Data.DataSet _ds = sqlSearch_forExport__batchlog(searchparam.cageacctid, searchparam.batchdte);

            if (_ds != null)
            {
                string key_ = System.Guid.NewGuid().ToString();
                HttpRuntime.Cache.Insert(key_, _ds, null, DateTime.UtcNow.Add(new TimeSpan(1, 0, 1)), System.Web.Caching.Cache.NoSlidingExpiration);
                return key_;
            }
            else
            {
                return null;
            }
        }
        #endregion

        #region [[ Export Balance Sheet ]]

        private System.Data.DataSet sqlSearch_forExport__balancesheet(int projectid, DateTime batchdte)
        {
            // Compose SQL
            project _project = _entity.Single<project>(p => p.projectId == projectid);
            string sql = String.Format("EXEC {0}.{1}.dbo.z_caging_balance_sheet '{2}', '{3}'", _project.db_datasource, _project.db_initialcatalog, _project.name, batchdte.ToString());

            System.Data.DataSet _dataSet = q_library.get_dataset_w_sql__single(ConfigurationManager.ConnectionStrings["kernelContext"].ConnectionString, sql, "result", 0, 0);

            //return 
            return _dataSet;
        }
        [HttpPost, Route("api/Caging/ExportBalanceSheetKey")]
        [apiAuthorize(AccessElement = "CMDI Caging", AccessLevel = "v")]
        public string ExportBalanceSheetKey(searchParam param)
        {
            string key_ = System.Guid.NewGuid().ToString();
            HttpRuntime.Cache.Insert(key_, param, null, DateTime.UtcNow.Add(new TimeSpan(1, 0, 1)), System.Web.Caching.Cache.NoSlidingExpiration);
            //back...
            return key_;
        }

        [HttpGet, Route("api/Caging/ExportBalanceSheet")]
        [apiAuthorize(AccessElement = "CMDI Caging", AccessLevel = "v")]
        public string ExportBalanceSheet(string key)
        {
            searchParam _param = (searchParam)HttpRuntime.Cache[key];

            exportbalancesheetparam searchparam = JsonConvert.DeserializeObject<exportbalancesheetparam>(_param.searchText);

            System.Data.DataSet _ds = sqlSearch_forExport__balancesheet(searchparam.projectid, searchparam.batchdte);

            if (_ds != null)
            {
                string key_ = System.Guid.NewGuid().ToString();
                HttpRuntime.Cache.Insert(key_, _ds, null, DateTime.UtcNow.Add(new TimeSpan(1, 0, 1)), System.Web.Caching.Cache.NoSlidingExpiration);
                return key_;
            }
            else
            {
                return null;
            }
        }
        #endregion

        #region [[ Check Images ]]
        [HttpGet, Route("api/caging/getCheckImages/{id}")]
        [apiAuthorize(AccessElement = "/crm/DataEntryDE", AccessLevel = "v")]
        public IEnumerable<MonyImage> GetCheckImages(int? id)
        {
            try
            {
                //Prepare SQL
                string _sql = String.Format("EXEC dbo.z_caging_checkimages {0}", id);
                IEnumerable<MonyImage> result = _entity.getContext().Database.SqlQuery<MonyImage>(_sql).ToList();
                return result;
            }
            catch
            {
                return null;
            }
        }

        #endregion

        #region [[ check if batch already existed ]]
        [HttpPost, Route("api/caging/batchlog/crimsonbatch")]
        [apiAuthorize(AccessElement = "CMDI Caging", AccessLevel = "v")]
        public genericResponse CrimsonBatch(string data)
        {
            caging_batch_log batch = JsonConvert.DeserializeObject<caging_batch_log>(data);
            caging_account cagingacct = _entity.Single<caging_account>(a => a.cageacctid == batch.cageacctid);
            if (cagingacct == null)
            {
                return new genericResponse() { success = false, UniqueId = 0, message = "Problem locating Caging Account - notify Admin!" };
            }
            project client = _entity.Single<project>(p => p.projectId == cagingacct.projectid);
            if (client == null)
            {
                return new genericResponse() { success = false, UniqueId = 0, message = "Problem locating Caging Client - notify Admin!" };
            }
            DateTime batchdte = Convert.ToDateTime(batch.batchdte.Date.ToString("MM/dd/yyyy"));
            string sql = String.Format("SELECT TOP 1 * FROM {0}.{1}.dbo.BATCH WHERE BATCHNO = '{2}' AND BATCHDTE = '{3}' ORDER BY BATCHID DESC",
                client.db_datasource, client.db_initialcatalog, batch.batchno, batchdte);
            BATCH record = _entity.getContext().Database.SqlQuery<BATCH>(sql).FirstOrDefault();
            if (record == null)
            {
                return new genericResponse() { success = true, UniqueId = 0 };
            }
            else 
            {
                return new genericResponse() { success = true, UniqueId = record.BATCHID };
            }
        }

        [HttpPost, Route("api/caging/batchlog/crimsonbatchbyid")]
        [apiAuthorize(AccessElement = "CMDI Caging", AccessLevel = "v")]
        public genericResponse CrimsonBatchbyId(string data)
        {
            caging_batch_log batch = JsonConvert.DeserializeObject<caging_batch_log>(data);
            caging_account cagingacct = _entity.Single<caging_account>(a => a.cageacctid == batch.cageacctid);
            if (cagingacct == null)
            {
                return new genericResponse() { success = false, UniqueId = 0, message = "Problem locating Caging Account - notify Admin!" };
            }
            project client = _entity.Single<project>(p => p.projectId == cagingacct.projectid);
            if (client == null)
            {
                return new genericResponse() { success = false, UniqueId = 0, message = "Problem locating Caging Client - notify Admin!" };
            }

            string sql = String.Format("SELECT TOP 1 * FROM {0}.{1}.dbo.BATCH WHERE BATCHID = '{2}'",
                client.db_datasource, client.db_initialcatalog, batch.crmbatchid);
            BATCH record = _entity.getContext().Database.SqlQuery<BATCH>(sql).FirstOrDefault();
            if (record == null)
            {
                return new genericResponse() { success = true, UniqueId = 0 };
            }
            else
            {
                return new genericResponse() { success = true, UniqueId = record.BATCHID };
            }
        }
        #endregion

        #region [[ check if log record for batch no and date already existed ]]
        [HttpPost, Route("api/caging/batchlog/logexist")]
        public int BatchLogExist(string data)
        {
            caging_batch_log batch = JsonConvert.DeserializeObject<caging_batch_log>(data);
            caging_account acct = _entity.Single<caging_account>(a => a.cageacctid == batch.cageacctid); 
            v_caging_batch_log record = _entity.Single<v_caging_batch_log>(b => b.projectid == acct.projectid && b.batchno == batch.batchno && b.batchdte == batch.batchdte);
            if (record == null)
                return 0;
            else
                return record.cagebatchid;
        }
        #endregion

    }
}