﻿using cmdiapp.n.core.Areas.datax.Domain.Models;
using cmdiapp.n.core.Areas.query.Domain.Models;
using cmdiapp.n.core.Domain.ViewModels;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace cmdiapp.n.core.Areas.datax.Domain.Services
{
    /// <summary>
    /// Exposes methods that retrieve data related to Cremir email list send jobs
    /// </summary>
    public interface ICremirJobService
    {
        /// <summary>
        /// Retrieves job info for accounts with <paramref name="accountId"/>
        /// </summary>
        /// <param name="accountId">the id of the account to get jobs for</param>
        /// <param name="skip">the number of records to skip</param>
        /// <param name="take">the number of records to take</param>
        /// <returns>list of jobs for this account</returns>
        Task<genericResponse> GetJobsByAccountAsync(
            int accountId,
            int page,
            int pageSize,
            QueryRuntimeSort sort,
            string listId,
            string jobTypeCode,
            string jobStatusCode);
        /// <summary>
        /// Retrieves the log entries for this job
        /// </summary>
        /// <param name="jobId">the id of the job to get logs for</param>
        /// <returns>list of log entries for this job</returns>
        Task<List<CremirJobLog>> GetJobLogsAsync(int jobId);
    }
}
