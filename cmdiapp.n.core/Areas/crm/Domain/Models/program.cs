﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Data;
using System.Linq;
using System.Web.Mvc;
using System.Data.Linq;
using System.ComponentModel.DataAnnotations.Schema;
using cmdiapp.n.core.Areas.crm.Domain.Data;
using cmdiapp.n.core.Domain.ViewModels;

namespace cmdiapp.n.core.Areas.crm.Domain.Models
{
    [Table("PROGRAM")]
    public class PROGRAM : _entityBase_crm
    {
        [Column]
        [Key]
        public Int16 PROGID { get; set; }

        [Column]
        [Required(ErrorMessage = "The Program Code is required.")]
        public string PROGTYPE { get; set; }

        [Column]
        [Required(ErrorMessage = "The Description is required.")]
        public string DESCRIP { get; set; }

        [Column]
        public Int16? ProgGroupID { get; set; }

        [Column]
        public bool? includeInDashboard { get; set; }

        [Column]
        public bool? suppressFromTku { get; set; }

    }

    [Table("PROGRAM_GROUP")]
    public class PROGRAM_GROUP : _entityBase_crm
    {
        [Column]
        [Key]
        public Int16 ProgGroupID { get; set; }

        [Column]
        public Int16? ProgGroupOrder { get; set; }

        [Column]
        public string Descrip { get; set; }

    }

    [Table("dtPROG")]
    public class dtPROG : _entityBase_crm
    {
        [Column]
        [Key]
        public int dtPROGID { get; set; }

        public Int16 PROGID { get; set; }

        [Column]
        public string dtPROGCODE { get; set; }

        [Column]
        public string dtPROGDESC { get; set; }

       
        public string CODEDESC
        {
            get
            {
                if (!string.IsNullOrEmpty(dtPROGCODE))
                    return dtPROGCODE + " - " + dtPROGDESC;
                else
                    return dtPROGDESC;
            }
        }

    }
    
    public class PROGRAM_ext
    {
        public Int16 PROGID { get; set; }

        public string PROGTYPE { get; set; }

        public string DESCRIP { get; set; }

        public int count_ { get; set; }

        public Int64 rowNo { get; set; }
    }

    [NotMapped]
    public class ProgramTopNData : iItemType
    {
        public int? ID { get; set; }
        public string PKGECODE { get; set; }
        public string PKGEDESC { get; set; }
        public decimal? value { get; set; }
        public int? count { get; set; }
        public DateTime? MAILDTE { get; set; }

        public int count_ { get; set; }
        public Int64 rowNo { get; set; }
    }
}