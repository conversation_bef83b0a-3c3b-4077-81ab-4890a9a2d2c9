﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

using Ninject;
using Ninject.Web.Mvc;

using cmdiapp.n.core.Core.App_Start;
using cmdiapp.n.core.Areas.crm.Domain.Models;
using cmdiapp.n.core.Areas.crm.Domain.ViewModels;
using cmdiapp.n.core.Areas.crm.Domain.Repositories;


namespace cmdiapp.n.core.Areas.crm.Domain.Services
{
    public class lkIMPTYPEService : IlkIMPTYPEService
    {
        private I___unitOfWork _unitOfWork;
        private I__DBFactory _dbFactory;
        private IlkIMPTYPERepository _lkIMPTYPERepository;

        public lkIMPTYPEService(I___unitOfWork unitOfWork, I__DBFactory dbFactory, IlkIMPTYPERepository lkIMPTYPERepository)
        {
            _unitOfWork = unitOfWork;
            _dbFactory = dbFactory;
            _lkIMPTYPERepository = lkIMPTYPERepository;
        }

        //Read all Imp Types
        public List<lkIMPTYPE> get_all_imptypes()
        {
            var _allRecords = _lkIMPTYPERepository.All();
            //return data
            return _allRecords.Where(x => x.IsActive == true).OrderBy(p => p.IMPID).ToList();
        }

        //Exclude Interactions all Imp Types
        public List<lkIMPTYPE> get_selected_imptypes()
        {
            var _allRecords = _lkIMPTYPERepository.All();
            //return data
            return _allRecords.Where(x => x.IsActive == true && !(x.IMPTYPEID >= 4 && x.IMPTYPEID <= 5) ).OrderBy(p => p.IMPID).ToList();
        }


    }


}