﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Data.Linq;
using cmdiapp.n.core.Areas.crm.Domain.Models;

namespace cmdiapp.n.core.Areas.crm.Domain.Repositories
{
    internal class batchRepository : _baseRepository<BATCH>, IBatchRepository
    {
        public batchRepository(I__DBFactory databaseFactory) : base(databaseFactory)
        {

        }
    }

    internal class exceptionViewRepository : _baseRepository<v_lkEXCEP>, IExceptionViewRepository
    {
        public exceptionViewRepository(I__DBFactory databaseFactory) : base(databaseFactory)
        {

        }
    }

    internal class sourceViewRepository : _baseRepository<v_SOURCE>, ISourceViewRepository
    {
        public sourceViewRepository(I__DBFactory databaseFactory) : base(databaseFactory)
        {

        }
    }
}