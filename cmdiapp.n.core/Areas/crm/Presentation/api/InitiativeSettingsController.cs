﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Net;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Web.Http;
using System.Web.Script.Serialization;
using System.Drawing;
using System.IO;
using System.Data.SqlClient;
using System.Collections.ObjectModel;
using System.Xml.Linq;

using Ninject;
using Ninject.Web.Mvc;

using AutoMapper;
using cmdiapp.n.core.Core;
using cmdiapp.n.core.Core.App_Start;
using cmdiapp.n.core.Domain.ViewModels;
using cmdiapp.n.core.Areas.crm.Core;
using cmdiapp.n.core.Areas.crm.Domain.Models;
using cmdiapp.n.core.Areas.crm.Domain.ViewModels;
using cmdiapp.n.core.Areas.crm.Domain.Data;
using cmdiapp.n.core.Areas.crm.Domain.Repositories;
using cmdiapp.n.core.Areas.crm.Domain.Services;
using cmdiapp.n.core.Areas.dataService.Domain.Data;
using cmdiapp.n.core.Areas.dataService.Domain.Models;
using cmdiapp.n.core.Areas.dataService.Domain.Services;
using cmdiapp.n.core.Presentation.Controllers;
using cmdiapp.n.core.Areas.query.Domain.Models;
using cmdiapp.n.core.Domain.Models;
using System.Data;
using cmdiapp.n.core.Library;
using cmdiapp.n.core.Domain.Data;

namespace cmdiapp.n.core.Areas.crm.Presentation.Controllers.api
{
    [Authorize]
    public class InitiativeSettingsController : ApiController
    {
        #region [[ Declaration ]]
        public class GraphResponse
        {
            public bool success { get; set; }
            public string message { get; set; }

            public int __count1 { get; set; }
            public List<iItemType> list1 { get; set; }
            public int __count2 { get; set; }
            public List<iItemType> list2 { get; set; }
        }

        private I_entity_crm _entity_crm;
        private readonly IpackageService _packageService;
        private readonly IReportDataService _reportService;
        private readonly IsourceService _sourceService;
        
        private userSession _userSession;
        //for initiative
        private string p0, p1, p2, p3, p4, p5, p6, p7, p8, p9, p10;
        private int x = 0;
        ObservableCollection<Row> rows = new ObservableCollection<Row>();
        List<string> columns = new List<string>();
        private bool bNoRecordHasListNo;
        private List<EXCELSOURCEREC> ExcelSourceRec = new List<EXCELSOURCEREC>();
        private List<_SOURCE> _checkUniqueSrceCodeList = new List<_SOURCE>();
        private readonly IsrceBudgetService _srceBudgetService;
        private string gSrceCodeList;

        #endregion
        
        #region [[ (constructor) SettingsController ]]
        public InitiativeSettingsController()
        {
            _entity_crm = I_entityManager_crm.getEntity();
            _packageService = NinjectMVC.kernel.Get<IpackageService>();
            _reportService = NinjectMVC.kernel.Get<IReportDataService>();
            _sourceService = NinjectMVC.kernel.Get<IsourceService>();
            _srceBudgetService = NinjectMVC.kernel.Get<IsrceBudgetService>();
            _userSession = session.userSession;
        }
        #endregion

        #region [[ (HttpResponseMessage) Image - crm/api/InitiativeSettings/Image/{id}/{seed} ]]
        [HttpGet, Route("crm/api/InitiativeSettings/Image/{id}/{seed}")]
        [apiAuthorize(AccessElement = "cmdiapp.dms.packages", AccessLevel = "v")]
        public HttpResponseMessage Image(int? id, string seed)
        {
            string anonymousPic_path = Path.Combine(System.Web.Hosting.HostingEnvironment.MapPath(@"~/contents/_shared/image/No_Image.jpg"));

            HttpResponseMessage response = new HttpResponseMessage(HttpStatusCode.OK);

            if (id == null)
            {
                var fileStream = new System.IO.FileStream(anonymousPic_path, System.IO.FileMode.Open, FileAccess.Read, FileShare.ReadWrite);
                //var fileStream = new System.IO.FileStream( anonymousPic_path, System.IO.FileMode.Open);
                response.Content = new StreamContent(fileStream);
            }
            else
            {
                byte[] pic = _packageService.PackageImage(id.Value);
                if (pic == null)
                {
                    var fileStream = new System.IO.FileStream(anonymousPic_path, System.IO.FileMode.Open, FileAccess.Read, FileShare.ReadWrite);
                    //var fileStream = new System.IO.FileStream(anonymousPic_path, System.IO.FileMode.Open);
                    response.Content = new StreamContent(fileStream);
                }
                else
                {
                    var memoryStream = new MemoryStream(pic);
                    response.Content = new StreamContent(memoryStream);
                }

            }
            response.Content.Headers.ContentType = new MediaTypeHeaderValue("image/jpeg");
            return response;
        }

        [HttpGet, Route("crm/api/InitiativeSettings/GetPDFImage/{seed}")]
        [apiAuthorize(AccessElement = "cmdiapp.dms.packages", AccessLevel = "v")]
        public HttpResponseMessage GetPDFImage(string seed)
        {
            string  anonymousPic_path = Path.Combine(System.Web.Hosting.HostingEnvironment.MapPath(@"~/contents/_shared/image/PDF.png"));
            
            HttpResponseMessage response = new HttpResponseMessage(HttpStatusCode.OK);

            var fileStream = new System.IO.FileStream(anonymousPic_path, System.IO.FileMode.Open, FileAccess.Read, FileShare.ReadWrite);
            response.Content = new StreamContent(fileStream);

            response.Content.Headers.ContentType = new MediaTypeHeaderValue("image/png");
            return response;
        }

        [HttpGet, Route("crm/api/InitiativeSettings/ImageFileName/{id}/{seed}")]
        [apiAuthorize(AccessElement = "cmdiapp.dms.packages", AccessLevel = "v")]
        public string ImageFileName(int? id, string seed)
        {
            if (id == null)
            {
                return "";
            }
            else
            {
                return _packageService.PackageImageName(id.Value);
            }
       }

        #endregion

        #region [[ SQL Query]]
        const string _sql_base_insert_into_source =
                @" INSERT INTO SOURCE ([PKGEID],[SRCECODE],[SRCEDESC],[LISTNOG],[LISTNO],[MAILDTE],[FIRSTCAGE],[LASTCAGE],[COSTPROD1],[COSTPROD2],[COSTPROD3],[COSTPOSTG],[COSTRESP1],[COSTRESP2],[COSTRESP3],[sQTYMAIL],[sMONY],[sPEOPLE],[sGROSS],[sNET],[sCOST],[sGROSSPM],[sNETPM],[sCOSTPM],[sGROSSRSP],[sNETRSP],[sCOSTRSP],[sRSPPCT],[isLM_SOURCE],[COMMENT],[UPDATEDON],[universe],[PROGSETID],[SPCEVNTID]) VALUES ( {0},'{1}','{2}','{3}','{4}','{5}',null,null, {6}, {7},0.00, {8},0.00,0.00,0.00, {9},0,0,0,0,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0,'','{10}',0,0,null)
                ";
        const string _sql_base_check_unique_source_code =
                @"
                    SELECT SRCECODE FROM SOURCE WHERE SRCECODE IN ({0})
                ";
        const string _sql_base_pkg =
                  @"SELECT {0}
                                    ( SELECT COUNT(*) FROM PACKAGE F {1} ) AS CNT,
                                    F.PKGEID AS PKGEID,
                                    ISNULL(F.PROGID,0) AS PROGID,
                                    ISNULL(P.DESCRIP,'''') AS DESCRIP,
                                    ISNULL(F.PKGECODE,'''') AS PKGECODE,
                                    ISNULL(F.PKGEDESC,'''') AS PKGEDESC, 
                                                        
                                          ISNULL(PKS.MAILQTY,0) AS MAILQTY,
                                          ISNULL(PKS.MAILDTE, NULL) AS MAILDTE,
                                          ISNULL(PKS.FIRSTCAGE, NULL) AS FIRSTCAGE,
                                          ISNULL(PKS.LASTCAGE, NULL) AS LASTCAGE,
                                          ISNULL(PKS.NOGIFT,0.00) AS NOGIFT,
                                          ISNULL(PKS.GROSS,0.00) AS GROSS,
                                          ISNULL(PKS.COST,0.00) AS COST,
                                          ISNULL(PKS.NET,0.00) AS NET,
                                   
                                    ISNULL(F.SENDACKW,0) AS SENDACKW,
                                    ISNULL(F.SENDRECV,0) AS SENDRECV,
                                    ISNULL(F.COMMENT,'''') AS COMMENT,
                                    ISNULL(F.UPDATEDON,null) AS UPDATEDON,
                                    ISNULL(F.INITIATIVETYPEID,0) AS INITIATIVETYPEID,
                                    -- For Last Activity Date
                                    ISNULL(LASTACTI, NULL) AS LASTACTI,
                                    ISNULL(SACTIVITY,0) AS SACTIVITY
        
         
                            FROM PACKAGE F
                                 LEFT OUTER JOIN PROGRAM P ON P.PROGID=F.PROGID
                                   LEFT OUTER JOIN (
                                                 SELECT PKGEID, 
                                                              ISNULL(SUM(sQTYMAIL),0) AS MAILQTY,
                                                              ISNULL(MAX(MAILDTE), NULL) AS MAILDTE,
                                                              ISNULL(MIN(FIRSTCAGE), NULL) AS FIRSTCAGE,
                                                              ISNULL(MAX(LASTCAGE), NULL) AS LASTCAGE,
                                                              ISNULL(MAX(SMONY),0.00) AS NOGIFT,
                                                              ISNULL(MAX(SGROSS),0.00) AS GROSS,
                                                              ISNULL(MAX(SCOST),0.00) AS COST,
                                                              ISNULL(MAX(SNET),0.00) AS NET,
                                                              ISNULL(MAX(LASTACTI), NULL) AS LASTACTI,
                                                              ISNULL(SUM(SACTIVITY),0) AS SACTIVITY
                                                 FROM SOURCE
                                                 GROUP BY PKGEID
                                   ) PKS ON F.PKGEID = PKS.PKGEID {1}";


        const string _sql_base_export_pkg =
            @"
                        SELECT 
                            ISNULL(F.PKGEID,0) AS PKGEID,
                            ISNULL(F.PROGID,0) AS PROGID,
                            ISNULL(P.DESCRIP,'''') AS DESCRIP,
                            ISNULL(F.PKGECODE,'''') AS PKGECODE,
                            ISNULL(F.PKGEDESC,'''') AS PKGEDESC, 
                            ISNULL((SELECT SUM(SQTYMAIL) FROM SOURCE WHERE PKGEID = F.PKGEID),0) AS MAILQTY, 
                            -- For Mail Date
                            Case When (SELECT MAX(MAILDTE) FROM SOURCE WHERE PKGEID = F.PKGEID) = ''1/1/1900'' Then null
                            Else 
                            ISNULL((SELECT MAX(MAILDTE) FROM SOURCE WHERE PKGEID = F.PKGEID),null) End AS MAILDTE,
                            -- For First Cage Date 
                            Case When (SELECT MIN(FIRSTCAGE) FROM SOURCE WHERE PKGEID = F.PKGEID) = ''1/1/1900'' Then null
                            Else
                            ISNULL((SELECT MIN(FIRSTCAGE) FROM SOURCE WHERE PKGEID = F.PKGEID ),null) End AS FIRSTCAGE, 
                            -- For Last Cage Date
                            Case When (SELECT MAX(LASTCAGE) FROM SOURCE WHERE PKGEID = F.PKGEID) = ''1/1/1900'' Then null
                            Else
                            ISNULL((SELECT MAX(LASTCAGE) FROM SOURCE WHERE PKGEID = F.PKGEID),null) End AS LASTCAGE, 
                            ISNULL((SELECT SUM(SMONY) FROM SOURCE WHERE PKGEID = F.PKGEID ),0.00) AS NOGIFT,
                            ISNULL((SELECT SUM(SGROSS) FROM SOURCE WHERE PKGEID = F.PKGEID ),0.00) AS GROSS, 
                            ISNULL((SELECT SUM(SCOST) FROM SOURCE WHERE PKGEID = F.PKGEID ),0.00) AS COST, 
                            ISNULL((SELECT SUM(SNET) FROM SOURCE WHERE PKGEID = F.PKGEID ),0.00) AS NET,
                            ISNULL(F.SENDACKW,0) AS SENDACKW,
                            ISNULL(F.SENDRECV,0) AS SENDRECV,
                            ISNULL(F.COMMENT,'''') AS COMMENT,
                            ISNULL(F.UPDATEDON,null) AS UPDATEDON,
                            ISNULL(F.INITIATIVETYPEID,0) AS INITIATIVETYPEID,
                            -- For Last Activity Date
                            Case When (SELECT MAX(LASTACTI) FROM SOURCE WHERE PKGEID = F.PKGEID) = ''1/1/1900'' Then null
                            Else
                            ISNULL((SELECT MAX(LASTACTI) FROM SOURCE WHERE PKGEID = F.PKGEID ),null) End AS LASTACTI, 
                            ISNULL((SELECT SUM(SACTIVITY) FROM SOURCE WHERE PKGEID = F.PKGEID ),0) AS SACTIVITY
                            FROM PACKAGE F
                            INNER JOIN PROGRAM P ON P.PROGID=F.PROGID
                                            
                    ";



        const string _sql_base_source = @"SELECT 
                        (SELECT COUNT(DISTINCT SRCEID) FROM SOURCE S, PACKAGE P) AS CNT,
                        ISNULL(S.SRCEID,0) AS SRCEID,
                        ISNULL(S.PKGEID,0) AS PKGEID,
                        ISNULL(K.PKGECODE,'''') AS PKGECODE,
                        ISNULL(K.PKGEDESC,'''') AS PKGEDESC,
                        ISNULL(S.SRCECODE,'''') AS SRCECODE,
                        ISNULL(S.SRCEDESC,'''') AS SRCEDESC,
                        ISNULL(S.LISTNO,'''') AS LISTNO,
                        -- For Mail Date
                        Case When S.MAILDTE = ''1/1/1900'' Then null
                        Else
                        ISNULL(S.MAILDTE,null) End AS MAILDTE,
                        -- For First Cage
                        Case When S.FIRSTCAGE = ''1/1/1900'' Then null
                        Else
                        ISNULL(S.FIRSTCAGE,null) End AS FIRSTCAGE,
                        -- For Last Cage
                        Case When S.LASTCAGE = ''1/1/1900'' Then null
                        Else
                        ISNULL(S.LASTCAGE,null) End AS LASTCAGE,
                        ISNULL(S.COSTPROD1,0.00) AS COSTPROD1,
                        ISNULL(S.COSTPROD2,0.00) AS COSTPROD2,
                        ISNULL(S.COSTPROD3,0.00) AS COSTPROD3,
                        ISNULL(S.COSTPOSTG,0.00) AS COSTPOSTG,
                        ISNULL(S.COSTRESP1,0.00) AS COSTRESP1,
                        ISNULL(S.COSTRESP2,0.00) AS COSTRESP2,
                        ISNULL(S.COSTRESP3,0.00) AS COSTRESP3,
                        ISNULL(S.sQTYMAIL,0) AS sQTYMAIL,
                        ISNULL(S.sMONY,0) AS sMONY,
                        ISNULL(S.sPEOPLE,0) AS sPEOPLE,
                        ISNULL(S.sGROSS,0.00) AS sGROSS,
                        ISNULL(S.sNET,0.00) AS sNET,
                        ISNULL(S.sCOST,0.00) AS sCOST,
                        ISNULL(S.sGROSSPM,0.00) AS sGROSSPM,
                        ISNULL(S.sNETPM,0.00) AS sNETPM,
                        ISNULL(S.sCOSTPM,0.00) AS sCOSTPM,
                        ISNULL(S.sGROSSRSP,0.00) AS sGROSSRSP,
                        ISNULL(S.sNETRSP,0.00) AS sNETRSP,
                        ISNULL(S.sCOSTRSP,0.00) AS sCOSTRSP,
                        ISNULL(S.sRSPPCT,0.00) AS sRSPPCT,
                        ISNULL(S.COMMENT,'''') AS COMMENT,
                        ISNULL(S.UPDATEDON,null) AS UPDATEDON,
                        -- For Last Activity
                        Case When S.LASTACTI = ''1/1/1900'' Then null
                        Else
                        ISNULL(S.LASTACTI,null) End AS LASTACTI,
                        ISNULL(S.SACTIVITY,0) AS SACTIVITY
                        FROM SOURCE S
				        INNER JOIN PACKAGE K ON K.PKGEID = S.PKGEID 
                        {0}
                  ";
        const string _sql_export_to_excel =
                            @"SELECT                         
                            ISNULL(P.DESCRIP,'''') AS DESCRIP,
                            ISNULL(F.PKGECODE,'''') AS PKGECODE,
                            ISNULL(F.PKGEDESC,'''') AS PKGEDESC,
                            ISNULL((SELECT SUM(SQTYMAIL) FROM SOURCE WHERE PKGEID = F.PKGEID),0) AS MAILQTY,
                            -- For Mail Date
                            Case When (SELECT MAX(MAILDTE) FROM SOURCE WHERE PKGEID = F.PKGEID) = '1/1/1900' Then null
                            Else 
                            ISNULL((SELECT MAX(MAILDTE) FROM SOURCE WHERE PKGEID = F.PKGEID),null) End AS MAILDTE,
                            -- For First Cage Date 
                           Case When (SELECT MIN(FIRSTCAGE) FROM SOURCE WHERE PKGEID = F.PKGEID) = '1/1/1900' Then null
                           Else
                           ISNULL((SELECT MIN(FIRSTCAGE) FROM SOURCE WHERE PKGEID = F.PKGEID ),null) End AS FIRSTCAGE, 
                           -- For Last Cage Date
                           Case When (SELECT MAX(LASTCAGE) FROM SOURCE WHERE PKGEID = F.PKGEID) = '1/1/1900' Then null
                           Else
                           ISNULL((SELECT MAX(LASTCAGE) FROM SOURCE WHERE PKGEID = F.PKGEID),null) End AS LASTCAGE, 
                           ISNULL((SELECT SUM(SMONY) FROM SOURCE WHERE PKGEID = F.PKGEID ),0.00) AS NOGIFT,
                           ISNULL((SELECT SUM(SGROSS) FROM SOURCE WHERE PKGEID = F.PKGEID ),0.00) AS GROSS, 
                           ISNULL((SELECT SUM(SCOST) FROM SOURCE WHERE PKGEID = F.PKGEID ),0.00) AS COST, 
                           ISNULL((SELECT SUM(SNET) FROM SOURCE WHERE PKGEID = F.PKGEID ),0.00) AS NET,
                           ISNULL(F.COMMENT,'''') AS COMMENT,
                           ISNULL(F.UPDATEDON,null) AS UPDATEDON
                           FROM PACKAGE F
                           INNER JOIN PROGRAM P ON P.PROGID=F.PROGID";

        //for People Setting Export
        //const string _sql_exportFlag = @"SELECT ISNULL(FLAG,'''') AS FLAG, ISNULL(FLAGDESC,'''') AS FLAGDESC, ISNULL(COMMENT,'''') AS COMMENT, ISNULL(LASTUSED,null) AS LASTUSED, ISNULL(UPDATEDON,null) AS UPDATEDON FROM dmFLAG";

        const string _sql_exportContFlag = @"SELECT ISNULL(CONTFLAG,'''') AS CONTFLAG, ISNULL(DESCRIP,'''') AS DESCRIP FROM lkCONTFLAG";

        const string _sql_exportKwrd = @"SELECT ISNULL(KWRD,'''') AS KWRD, ISNULL(KWRDDESC,'''') AS KWRDDESC, ISNULL(LASTUSED,null) AS LASTUSED, ISNULL(UPDATEDON,null) AS UPDATEDON FROM dmKWRD";

        const string _sql_exportClub = @"SELECT CLUBCODE FROM pmCLUB";

        const string _sql_exportClubs = @"SELECT CLUBCODE FROM pmCLUB Order By ClubId";

        const string _sql_exportClubStatus = @"SELECT ISNULL(CLUBCODE,'''') AS CLUBCODE ,ISNULL(DESCRIP,'''') AS DESCRIP from lkCLUBSTAT join pmclub on pmclub.CLUBID = lkCLUBSTAT.CLUBID";
        //Done for People Setting Export

        //For Money Settings
        const string _sql_exportProgram = @"SELECT ISNULL(PROGTYPE,'''') AS CODE, ISNULL(DESCRIP,'''') AS DESCRIP FROM PROGRAM";

        const string _sql_exportSource = @"SELECT 
	        LTRIM(RTRIM(ISNULL(S.SRCECODE,''))) AS Code,
            LTRIM(RTRIM(ISNULL(S.SRCEDESC,''))) AS Description,
            LTRIM(RTRIM(ISNULL(K.PKGECODE,''))) AS 'Initiative Code',
            LTRIM(RTRIM(ISNULL(K.PKGEDESC,''))) AS 'Initiative Description',  
	        LTRIM(RTRIM(ISNULL(S.LISTNO,''))) AS [No],
    
	        -- For Mail Date
	        case When	convert(date, S.MAILDTE, 101) = convert(date, '1/1/1900', 101) Then '' 
	        when S.MAILDTE is null Then ''
	        Else	CONVERT(VARCHAR(10),S.MAILDTE,101) 
	        End AS 'Mail Date',

            -- For First Cage
	        case When	convert(date, S.FIRSTCAGE, 101) = convert(date, '1/1/1900', 101) Then '' 
	        when S.FIRSTCAGE is null Then ''
	        Else	CONVERT(VARCHAR(10),S.FIRSTCAGE,101) 
	        End AS 'First Cage Date',

	        case When	convert(date, S.LASTCAGE, 101) = convert(date, '1/1/1900', 101) Then '' 
	        when S.LASTCAGE is null Then ''
	        Else	CONVERT(VARCHAR(10),S.LASTCAGE,101) 
	        End AS 'Last Cage Date',

            ISNULL(S.COSTPROD1,0.00) AS 'Production Cost1',
            ISNULL(S.COSTPROD2,0.00) AS 'Production Cost2',
            ISNULL(S.COSTPROD3,0.00) AS 'Production Cost3',
            ISNULL(S.COSTPOSTG,0.00) AS 'Postage Cost',
            ISNULL(S.COSTRESP1,0.00) AS 'Response Cost1',
            ISNULL(S.COSTRESP2,0.00) AS 'Response Cost2',
            ISNULL(S.COSTRESP3,0.00) AS 'Response Cost3',
            ISNULL(S.sQTYMAIL,0) AS 'Qty Mailed',
            ISNULL(S.sMONY,0) AS 'Number of Gifts',
            ISNULL(S.sPEOPLE,0) AS 'Number of Donors',
            ISNULL(S.sGROSS,0.00) AS 'Gross $',
            ISNULL(S.sNET,0.00) AS 'Net $',
            ISNULL(S.sCOST,0.00) AS Cost,
            ISNULL(S.sGROSSPM,0.00) AS 'Gross/Mail',
            ISNULL(S.sNETPM,0.00) AS 'Net/Mail',
            ISNULL(S.sCOSTPM,0.00) AS 'Cost/Mail',
            ISNULL(S.sGROSSRSP,0.00) AS 'Gross/Response',
            ISNULL(S.sNETRSP,0.00) AS 'Net/Response', 
            ISNULL(S.sCOSTRSP,0.00) AS 'Cost/Response',
            ISNULL(S.sRSPPCT,0.00) AS 'Response%',

            -- For Last Activity
	        case When	convert(date, S.LASTACTI, 101) = convert(date, '1/1/1900', 101) Then '' 
	        when S.LASTACTI is null Then ''
	        Else	CONVERT(VARCHAR(10),S.LASTACTI,101) 
	        End AS 'Last Activity Date',
            ISNULL(S.SACTIVITY,0) AS 'Number of Activities',
	        ISNULL(S.COMMENT,'') AS Comment,
	        -- For UPDATEDON
	        case When	convert(date, S.UPDATEDON, 101) = convert(date, '1/1/1900', 101) Then '' 
	        when S.UPDATEDON is null Then ''
	        Else	CONVERT(VARCHAR(10),S.UPDATEDON,101) 
	        End AS 'Last Modified'
            FROM SOURCE S
            INNER JOIN PACKAGE K ON K.PKGEID = S.PKGEID";

        //For Compliance Settings
        const string _sql_exportExceptionCode =
            @"select ISNULL(EXCEP,'') As Code,
            ISNULL(DESCRIP,'') As [Description], 
            ISNULL(FECMEMO,'') As [FEC Memo] 
            from lkEXCEP";

        const string _sql_exportChartAccounts =
           @"SELECT 
            [ACCTCODE] as [No], 
            [lkCHARTACCT].[DESCRIP] AS [Account], 
            [lkCHARTACCTTYPE].[DESCRIP] AS [Type]
            FROM  [dbo].[lkCHARTACCT]
            LEFT OUTER JOIN [dbo].[lkCHARTACCTTYPE] ON [lkCHARTACCT].[ACCTTYPEID] = [lkCHARTACCTTYPE].[ACCTTYPEID]";

        const string _sql_exportFECdescrip = @"select ISNULL(DESCRIP,'') as [Description] from lkFECDesc";

        #endregion

        #region[MONEY->SETTINGS->INITIATIVE by Tanvir]
        private string ApostropheCheck(string sApostropheCheckThis)
        {
            // take care of apostrophe                                              
            string sApostropheCheck_String = sApostropheCheckThis;
            return sApostropheCheck_String.Replace("'", "''");
        }

        #region [[ Additional Classes ]]

        public class Row
        {
            private Dictionary<string, object> _data = new Dictionary<string, object>();

            public object this[string index]
            {
                get { return _data[index]; }
                set { _data[index] = value; }
            }
        }
        #region [[ Support Functions ]]
        private string GetTheVALIDIntValue(string sIntVal)
        {
            int MyIntRetVal;

            try
            {
                MyIntRetVal = Convert.ToInt32(sIntVal);
            }
            catch
            {
                MyIntRetVal = 0;
            }

            return MyIntRetVal.ToString();
        }

        private string GetTheVALIDMoneyValue(string sDoubleVal)
        {
            double MyDoubleRetVal;

            try
            {
                MyDoubleRetVal = Convert.ToDouble(sDoubleVal);
            }
            catch
            {
                MyDoubleRetVal = 0.00;
            }

            return MyDoubleRetVal.ToString();
        }

        private string GetTheVALIDDateTimeValue(string sDateTime)
        {
            DateTime? MyDateTimeRetVal;
            string sThisDate = null;
            try
            {
                MyDateTimeRetVal = Convert.ToDateTime(sDateTime);
                // trifs 14 Oct 2010 10:08pm  ... to address datetime issue when saving with Custom Data on a Mac Machine ... 
                // updated 7 Oct 2011 1:15am
                sThisDate = String.Format("{0:MM/dd/yyyy}", MyDateTimeRetVal);
            }
            catch
            {
                MyDateTimeRetVal = null;
            }

            return sThisDate;
        }

        /// <summary>
        /// This is used to Parse the row
        /// </summary>
        /// <param name="_row"></param>
        /// <returns></returns>
        List<string> ParseXlsLine(DataRow _row)
        {
            try
            {
                List<string> cols = new List<string>();
                cols.Clear();
                if (_row != null)
                {
                    foreach (var col in _row.ItemArray)
                    {
                        if (col != null)
                            cols.Add(col.ToString());
                        else
                            cols.Add(string.Empty);
                    }
                }
                return cols;
            }
            catch (System.Exception ex)
            {

            }
            return null;
        }
        /// <summary>
        /// This is used to validate Empty row
        /// </summary>
        /// <param name="data"></param>
        /// <returns></returns>
        Boolean ValidateEmptyRow(List<string> data)
        {
            foreach (string item in data)
            {
                if (!String.IsNullOrEmpty(item))
                    return true;
            }
            return false;
        }
        #endregion

        public class _SOURCE
        {
            public string SRCECODE { get; set; }
        }

        public class EXCELSOURCEREC
        {
            public string SEQ { get; set; }
            public string SRCECODE { get; set; }
            public string SRCEDESC { get; set; }
            public string LISTNOG { get; set; }
            public string LISTNO { get; set; }
            public string MAILDTE { get; set; }
            public DateTime? _MAILDTE { get; set; }
            public string sQTYMAIL { get; set; }
            public int _sQTYMAIL { get; set; }
            public string COSTPROD1 { get; set; }
            public double _COSTPROD1 { get; set; }
            public string COSTPROD2 { get; set; }
            public double _COSTPROD2 { get; set; }
            public string COSTPOSTG { get; set; }
            public double _COSTPOSTG { get; set; }
        }

        //GraphData Implemented by Tanvir
        public class GraphData
        {
            public bool success { get; set; }
            public string message { get; set; }

            public List<iItemType> plist{ get; set; }
            public int pcount{ get; set; }
            public List<iItemType> lList{ get; set; }
            public int lcount{ get; set; }
        }
        #endregion

        [HttpGet, Route("crm/api/InitiativeSettings/Edit")]
        [aElementAuthorize(AccessElement = "cmdiapp.dms.packages", AccessLevel = "v")]
        public vwInitiative ShowInitiative(int pkgid)
        {
            vwInitiative viewModel = new vwInitiative();
            //Get Package Info
            viewModel.package = _packageService.get_package(pkgid);
            //Get Initiative Type Info
            viewModel.lkInitiativeType = _packageService.get_InitiativeTypes();
            viewModel.PROGRAM = _packageService.get_Programs();

            PACKAGE package = new PACKAGE();
            package = _packageService.get_package(pkgid);
            //For subProgram
            List<dtPROG> _allSubProgram = _packageService.get_SubPrograms().Where(x => x.PROGID == package.PROGID).ToList();
            //For First Item
            dtPROG _firstItem = new dtPROG();
            _firstItem.dtPROGID = 0;
            _firstItem.dtPROGCODE = "";
            _firstItem.dtPROGDESC = "Not Applicable";
            _allSubProgram.Insert(0, _firstItem); //We nbeed to add as first Item
            viewModel.SUBPROGRAM = _allSubProgram;

            viewModel.SubReportConfig = "";
            if (!string.IsNullOrEmpty(session.userSession.getConfigVal(crmConstants.RAbySub)) && session.userSession.getConfigVal(crmConstants.RAbySub).ToUpper() == "Y")
            {
                viewModel.SubReportConfig = "Y";
            }

            viewModel.sourceCounts = _packageService.getSourceCounts(pkgid);

            return viewModel;
        }

        [HttpGet, Route("crm/api/InitiativeSettings/Summary/{pkgeId}")]
        [aElementAuthorize(AccessElement = "cmdiapp.dms.packages", AccessLevel = "v")]
        public initiativeSummary InitiativeSummary(int pkgeId)
        {
            return _packageService.getInitiativeSummary(pkgeId);
        }

        [HttpGet, Route("crm/api/InitiativeSettings/DoesSourceExist")]
        [apiAuthorize(AccessElement = "cmdiapp.dms.packages", AccessLevel = "v")]
        public bool DoesSourceExist(int pkge_id)
        {
            return _packageService.IsReferencedBySource(pkge_id);
        }

        [HttpPost, Route("crm/api/InitiativeSettings/CreateSingleSource")]
        [apiAuthorize(AccessElement = "cmdiapp.dms.packages", AccessLevel = "a")]
        public genericResponse CreateSingleSource(vwInitiative model)
        {
            if (model != null)
            {
                SOURCE src = null;
                try
                {
                    src = new SOURCE
                    {
                        PKGEID = model.package.PKGEID,
                        SRCECODE = model.package.PKGECODE.ToUpper(),
                        SRCEDESC = model.package.PKGEDESC.ToUpper(),
                        COMMENT = model.package.COMMENT,
                        UPDATEDON = System.DateTime.Now,
                        SPCEVNTID = null,
                    };
                    //check uniqueness for source
                    if (!_sourceService.check_unqiueness(src))
                    {
                        // try again with current date string added
                        src.SRCECODE = model.package.PKGECODE.ToUpper()
                            + System.DateTime.Now.ToShortDateString().Replace("/", "");

                        if (!_sourceService.check_unqiueness(src))
                        {
                            return new genericResponse { success = false, message = "Source " + src.SRCECODE + " already exists." };
                        }
                        else
                        {
                            _sourceService.Add(src);

                            return new genericResponse { success = true, message = "Source " + src.SRCECODE + " added successfully." };
                        }
                    }                   
                    else
                    {
                        _sourceService.Add(src);

                        return new genericResponse { success = true, message = "Source " + src.SRCECODE + " added successfully." };
                    }

                }
                catch
                {
                    //To Do: Log the Exception here
                    return new genericResponse { success = false, message = "There is a problem processing your request. Please try again later." };
                }
            }
            else
            {
                //Model is not valid - We can not go ahead
                return new genericResponse { success = false, message = "Missing Required Field(s). Please try again." };
            }
        }

        [HttpPost, Route("crm/api/InitiativeSettings/ExportInitiativeKey")]
        [apiAuthorize(AccessElement = "cmdiapp.dms.packages", AccessLevel = "v")]
        public string ExportInitiativeKey(string searchText)
        {
            if (string.IsNullOrEmpty(searchText)) searchText = "";

            searchParam param = new searchParam();
            param.searchText = searchText;

            string key_ = System.Guid.NewGuid().ToString();
            HttpRuntime.Cache.Insert(key_, param, null, DateTime.UtcNow.Add(new TimeSpan(1, 0, 1)), System.Web.Caching.Cache.NoSlidingExpiration);

            return key_;
        }

        [HttpGet, Route("crm/api/InitiativeSettings/ExportInitiative")]
        [apiAuthorize(AccessElement = "cmdiapp.dms.packages", AccessLevel = "v")]
        public string ExportInitiative(string key)
        {
            searchParam _param = (searchParam)HttpRuntime.Cache[key];
            string sToday = String.Format("{0:yyyyMMMdd__hh_mm_ss tt}", DateTime.Now);
            string moduleName = "Initiatives";
            string fileName = moduleName + "_" + System.Guid.NewGuid() + "_" + _userSession.UserName + "_" + sToday;

            //System.Data.DataTable _dt = null;
            DataSet ds = null;

            if (string.IsNullOrEmpty(_param.searchText))
            {
                ds = _reportService.get_dataset_w_sql__single(_sql_export_to_excel , "Initiatives");
            }
            else
            {
                string _searchText = Library.util.kill_sqlBlacklistWord(_param.searchText);

                string _where = "";
                if (!string.IsNullOrEmpty(_searchText))
                {
                    _searchText = Library.util.kill_sqlBlacklistWord(_searchText);

                    #region [ Split Search Text > string1, string2 & string3 and create "Where" statement ]
                    char[] delimiterChars = { ' ', ',', '.', ';', ':', '|', '\t' };

                    string[] words = _searchText.Split(delimiterChars);

                    if (words.Count() > 0)
                    {
                        string string1 = words[0];
                        string string2 = (words.Count() > 1 ? words[1] : "");
                        string string3 = (words.Count() > 2 ? words[2] : "");

                        #region [ Where Clause ]
                        switch (words.Count())
                        {
                            case 1: // PKGCODE or DESCRIP
                                _where = string.Format("WHERE F.PKGECODE LIKE '%{0}%' OR F.PKGEDESC LIKE '%{0}%'", string1);
                                break;

                            case 2: // PKGCODE and DESCRIP
                                _where = string.Format("WHERE F.PKGECODE LIKE '%{0}%' OR F.PKGEDESC LIKE '%{1}%'", string1, string2);
                                break;

                            default:
                                break;
                        }
                        #endregion

                    }
                    #endregion
                }
                
                //We need SQL with Where condition
                string sql = _sql_export_to_excel +" "+ _where;
                //Compose the SQL
                //sql = String.Format(sql, _where);

                ds = _reportService.get_dataset_w_sql__single(sql, "Initiatives");

            }


            if (ds != null)
            {
                string key_ = System.Guid.NewGuid().ToString();
                HttpRuntime.Cache.Insert(key_, ds, null, DateTime.UtcNow.Add(new TimeSpan(1, 0, 1)), System.Web.Caching.Cache.NoSlidingExpiration);
                //return the Key
                return key_;
            }
            else
            {
                return null;
            }
        }

        [HttpGet, Route("crm/api/InitiativeSettings/getInitiativeInteractGraph")]
        [apiAuthorize(AccessElement = "cmdiapp.dms.packages", AccessLevel = "v")]
        public GraphData getInitiativeInteractGraph(int pPkgeID, int ActivityGroupId)
        {
            const string _sql_base_code_telerik_pie_chart =
                    @"
                        SELECT
                             gAGROUP.activityGroupId as _activityGROUPid
                            ,gAGROUP.descrip as _activityGROUP
                            ,CAST(0 AS INTEGER) as _activityCODEid
                            ,'' as _activityCODE
                            ,count(gAGROUP.activityGroupId) as _activityYvalue
                            ,gAGROUP.descrip as _activityXlabel
                        FROM 
                             gActivity gACT
                            ,Source s
                            ,Package p
                            ,gActivityGroup gAGROUP
                            ,gActivityCode gACODE
                        WHERE
                             gACT.srceid = s.srceid and
                             p.pkgeid = s.pkgeid and
                             gACT.activityCodeId = gACODE.activityCodeId and 
                             gACODE.activityGroupId = gAGROUP.activityGroupId and
                             p.pkgeid = {0}
                        GROUP BY
                             gAGROUP.activityGroupId
                            ,gAGROUP.descrip
                    ";

            const string _sql_base_group_telerik_pie_chart =
                @"
                        SELECT
                             gACODE.activityCodeId as _activityCODEid
                            ,gACODE.activityCode as _activityCODE
                            ,max(gAGROUP.activityGroupId) as _activityGROUPid
                            ,max(gAGROUP.descrip) as _activityGROUP
                            ,count(gACODE.activityCodeId) as _activityYvalue
                            ,gACODE.activityCode as _activityXlabel
                        FROM 
                             gActivity gACT
                            ,Source s
                            ,Package p
                            ,gActivityGroup gAGROUP
                            ,gActivityCode gACODE
                        WHERE
                             gACT.srceid = s.srceid and
                             p.pkgeid = s.pkgeid and
                             gACT.activityCodeId = gACODE.activityCodeId and 
                             gACODE.activityGroupId = gAGROUP.activityGroupId and
                             p.pkgeid = {0} and
							 gACODE.activityGroupId = {1}
                        GROUP BY
                             gACODE.activityCodeId
                            ,gACODE.activityCode
                    ";
            const string _sql_base_scroll_line_chart =
                    @"
                        IF OBJECT_ID('tempdb..#gACTIVITIES') IS NOT NULL 
	                        drop table #gACTIVITIES

                        IF OBJECT_ID('tempdb..#uniqCOUNT') IS NOT NULL 
	                        drop table #uniqCOUNT

                        IF OBJECT_ID('tempdb..#signUpCOUNT') IS NOT NULL 
	                        drop table #signUpCOUNT

                        -- First, create temporary table to hold data
                        CREATE TABLE #gACTIVITIES 
                        (
                             _date datetime
                            ,_monthYear varchar(8)
                            ,_signUpCount int
                            ,_uniqActivityCount int  
                        )

                        -- trifs 27 Jan 2011 1:48pm
                        -- Now, make query for SIGNUP count
						select
	                        LEFT(DATENAME(mm, [when] ),3) + ' ' + DATENAME(yyyy, [when] ) as _monthYear
                            , count(DISTINCT A.PID) as _signUpCount
	                        , max([when]) as _when	                        
                        into #signUpCOUNT
                        from gActivity A, Package P, Source S
                        where [when] IS NOT NULL and 
						A.activityCodeId = 4 and				-- activityCodeId = 4 (Web SignUp)
			            A.srceid = S.srceid and
			            S.pkgeid = P.pkgeid and
			            P.pkgeid = {0}
                        group by DATENAME(yyyy, [when] ), LEFT( DATENAME(mm, [when] ),3) + ' ' + DATENAME(yyyy, [when] )
                        order by _when DESC

                        -- Then, make query for UNIQUE People Count
                        select
	                        LEFT(DATENAME(mm, [when] ),3) + ' ' + DATENAME(yyyy, [when] ) as _monthYear
                            , count(DISTINCT A.PID) as _uniqActivityCount
	                        , max([when]) as _when	                        
                        into #uniqCOUNT
                        from gActivity A, Package P, Source S
                        where [when] IS NOT NULL and 
			            A.srceid = S.srceid and
			            S.pkgeid = P.pkgeid and
			            P.pkgeid = {0}
                        group by DATENAME(yyyy, [when] ), LEFT( DATENAME(mm, [when] ),3) + ' ' + DATENAME(yyyy, [when] )
						order by _when DESC

                        -- trifs 27 Jan 2011 11:24am
                        DECLARE @MAXWHEN DATETIME
                        SET @MAXWHEN = (SELECT MAX(_when) FROM #uniqCOUNT)

                        -- Then, create the Month/Year List to cover the last 12 months of gActivities
                        DECLARE @Date DATETIME
                        SET @Date = @MAXWHEN

                        DECLARE @sMONTH_YEAR VARCHAR(20)
                        SET @sMONTH_YEAR = ( SELECT LEFT(DATENAME(mm, @MAXWHEN ),3) + ' ' + DATENAME(yyyy, @MAXWHEN ))

                        DECLARE @dLOOP DATETIME
                        SET @dLOOP = @MAXWHEN

                        DECLARE @iLOOP INT
                        SET @iLOOP = 1

                        WHILE (@iLOOP <= 12 AND @MAXWHEN IS NOT NULL) BEGIN
                            BEGIN       
                                -- PRINT @dLOOP
                                -- PRINT @sMONTH_YEAR

		                        INSERT INTO #gACTIVITIES
			                        (_date,_monthYear, _signUpCount, _uniqActivityCount) VALUES (@dLoop,@sMONTH_YEAR,0,0)

                                SET @dLOOP = ( SELECT DATEADD(s,-1,DATEADD(mm, DATEDIFF(m,0,@dLOOP),0)) )
                                SET @sMONTH_YEAR = ( SELECT LEFT(DATENAME(mm, @dLOOP ),3) + ' ' + DATENAME(yyyy, @dLOOP ))
                                SET @iLOOP = @iLOOP + 1   
                            END
                        END

                         -- Now, make the actual query
                        select
                              g._date
                            , g._monthYear
							, ISNULL(b._signUpCount,0) as _signUpCount
                            , ISNULL(c._uniqActivityCount,0) as _uniqActivityCount
                        from #gACTIVITIES g
						left outer join #signUpCOUNT b ON b._monthYear = g._monthYear
                        left outer join #uniqCOUNT c ON c._monthYear = g._monthYear
                    ";

            const string _sql_base_group_scroll_line_chart =
                @"
                        IF OBJECT_ID('tempdb..#gACTIVITIES') IS NOT NULL 
	                        drop table #gACTIVITIES

                        IF OBJECT_ID('tempdb..#uniqCOUNT') IS NOT NULL 
	                        drop table #uniqCOUNT

                        IF OBJECT_ID('tempdb..#signUpCOUNT') IS NOT NULL 
	                        drop table #signUpCOUNT

                        -- First, create temporary table to hold data
                        CREATE TABLE #gACTIVITIES 
                        (
                             _date datetime
                            ,_monthYear varchar(8)
                            ,_signUpCount int
                            ,_uniqActivityCount int  
                        )

                        -- trifs 27 Jan 2011 1:48pm
                        -- Now, make query for SIGNUP count
						select
	                        LEFT(DATENAME(mm, [when] ),3) + ' ' + DATENAME(yyyy, [when] ) as _monthYear
                            , count(DISTINCT A.PID) as _signUpCount
	                        , max([when]) as _when	                        
                        into #signUpCOUNT
                        from gActivity A, Package P, Source S, gActivityGroup gAG, gActivityCode gAC
                        where [when] IS NOT NULL and 
						A.activityCodeId = 4 and				-- activityCodeId = 4 (Web SignUp)
			            A.srceid = S.srceid and
			            S.pkgeid = P.pkgeid and
			            P.pkgeid = {0} and 
						A.activityCodeId = gAC.activityCodeId and
						gAC.activityGroupId = gAG.activityGroupId and
						gAG.activityGroupId = {1}
                        group by DATENAME(yyyy, [when] ), LEFT( DATENAME(mm, [when] ),3) + ' ' + DATENAME(yyyy, [when] )
                        order by _when DESC

                        -- Then, make query for UNIQUE People Count
                        select
	                        LEFT(DATENAME(mm, [when] ),3) + ' ' + DATENAME(yyyy, [when] ) as _monthYear
                            , count(DISTINCT A.PID) as _uniqActivityCount
	                        , max([when]) as _when	                        
                        into #uniqCOUNT
                        from gActivity A, Package P, Source S, gActivityGroup gAG, gActivityCode gAC
                        where [when] IS NOT NULL and 
			            A.srceid = S.srceid and
			            S.pkgeid = P.pkgeid and
			            P.pkgeid = {0} and 
						A.activityCodeId = gAC.activityCodeId and
						gAC.activityGroupId = gAG.activityGroupId and
						gAG.activityGroupId = {1}
                        group by DATENAME(yyyy, [when] ), LEFT( DATENAME(mm, [when] ),3) + ' ' + DATENAME(yyyy, [when] )
						order by _when DESC

                        -- trifs 27 Jan 2011 11:24am
                        DECLARE @MAXWHEN DATETIME
                        SET @MAXWHEN = (SELECT MAX(_when) FROM #uniqCOUNT)

                        -- Then, create the Month/Year List to cover the last 12 months of gActivities
                        DECLARE @Date DATETIME
                        SET @Date = @MAXWHEN

                        DECLARE @sMONTH_YEAR VARCHAR(20)
                        SET @sMONTH_YEAR = ( SELECT LEFT(DATENAME(mm, @MAXWHEN ),3) + ' ' + DATENAME(yyyy, @MAXWHEN ))

                        DECLARE @dLOOP DATETIME
                        SET @dLOOP = @MAXWHEN

                        DECLARE @iLOOP INT
                        SET @iLOOP = 1

                        WHILE (@iLOOP <= 12 AND @MAXWHEN IS NOT NULL) BEGIN
                            BEGIN       
                                -- PRINT @dLOOP
                                -- PRINT @sMONTH_YEAR

		                        INSERT INTO #gACTIVITIES
			                        (_date,_monthYear, _signUpCount, _uniqActivityCount) VALUES (@dLoop,@sMONTH_YEAR,0,0)

                                SET @dLOOP = ( SELECT DATEADD(s,-1,DATEADD(mm, DATEDIFF(m,0,@dLOOP),0)) )
                                SET @sMONTH_YEAR = ( SELECT LEFT(DATENAME(mm, @dLOOP ),3) + ' ' + DATENAME(yyyy, @dLOOP ))
                                SET @iLOOP = @iLOOP + 1   
                            END
                        END

                         -- Now, make the actual query
                        select
                              g._date
                            , g._monthYear
							, ISNULL(b._signUpCount,0) as _signUpCount
                            , ISNULL(c._uniqActivityCount,0) as _uniqActivityCount
                        from #gACTIVITIES g
						left outer join #signUpCOUNT b ON b._monthYear = g._monthYear
                        left outer join #uniqCOUNT c ON c._monthYear = g._monthYear
                    ";
            string sqlTextPie, sqlTextLine;

            if (ActivityGroupId > -1)
            {
                sqlTextPie = String.Format(_sql_base_group_telerik_pie_chart, pPkgeID, ActivityGroupId);
                sqlTextLine = String.Format(_sql_base_group_scroll_line_chart, pPkgeID, ActivityGroupId);
            }
            else
            {
                sqlTextPie = String.Format(_sql_base_code_telerik_pie_chart, pPkgeID);
                sqlTextLine = String.Format(_sql_base_scroll_line_chart, pPkgeID);
            }

            //List<initiativePieCHARTdata> pieList = _packageService.getInitiativeInteractPieData(sqlTextPie);
            //List<initiativescrollLineCHARTdata> lineList = _packageService.getInitiativeInteractLineData(sqlTextLine);


            IEnumerable<initiativePieCHARTdata> pieList = _packageService.getInitiativeInteractPieData(sqlTextPie);
            IEnumerable<initiativescrollLineCHARTdata> lineList = _packageService.getInitiativeInteractLineData(sqlTextLine);

            return new GraphData { plist = pieList.ToList<iItemType>(), pcount = pieList.Count(), lList = lineList.ToList<iItemType>(), lcount = lineList.Count() };

        }

        [HttpGet, Route("crm/api/InitiativeSettings/getInitiativeFundGraph")]
        [apiAuthorize(AccessElement = "cmdiapp.dms.packages", AccessLevel = "v")]
        public genericResponse getInitiativeFundGraph(int whatPeriod, int PkgeID, int random, DateTime? STARTDTE, DateTime? ENDDTE) // DateTime _lastcage,
        {
            _donationTOTALSfinal dTOTALSfinal = _packageService.get_donationTOTALSfinal(PkgeID);
            DateTime nowPlus90 = DateTime.Now.AddDays(90);
            //Last Cage Date
            DateTime dtEnd = dTOTALSfinal._lastcage;
            //First Response Date
            DateTime dtFirstResponse = dTOTALSfinal._firstcage;
            DateTime dtStart = DateTime.Now;
            string sEnd = String.Format("{0:M/d/yyyy}", dtEnd);
            string sFirstResponse = String.Format("{0:M/d/yyyy}", dtFirstResponse);
            int dateDiff = 0;
            
            if (sEnd.Equals("1/1/1900"))
            {
                dtEnd = DateTime.Now;
                sEnd = String.Format("{0:M/d/yyyy}", dtEnd);
            }

            if (whatPeriod == 1)
            {
                //Let us work out Start and End Date manually as user just click on "Custom Date Range" Option and dates are not yet selected
                if (sFirstResponse.Equals("1/1/1900"))
                {
                    dtFirstResponse = DateTime.Now;
                    sFirstResponse = String.Format("{0:M/d/yyyy}", dtFirstResponse);
                }
                //Now find the End Date
                if (dtFirstResponse.AddDays(90) < nowPlus90)
                {
                    sEnd = String.Format("{0:M/d/yyyy}", dtFirstResponse.AddDays(90));
                }
                dateDiff = 90;
            }
            
            if (whatPeriod == 5 )
            {
                //We have Dates from user selection
                sFirstResponse = String.Format("{0:M/d/yyyy}", STARTDTE);
                sEnd = String.Format("{0:M/d/yyyy}", ENDDTE);
                TimeSpan ts = ENDDTE.Value.Subtract(STARTDTE.Value);
                //Also calculate weeks between two dates
                dateDiff = ts.Days;
            }
            
            if (dTOTALSfinal._totalDonationCount > 0)
            {
                string sStart = "";
                if (whatPeriod == 2)
                {
                    dtStart = dtEnd.AddDays(-30);
                    sStart = String.Format("{0:M/d/yyyy}", dtStart);
                    dateDiff = 30;
                }
                if (whatPeriod == 3)
                {
                    dtStart = dtEnd.AddDays(-90);
                    sStart = String.Format("{0:M/d/yyyy}", dtStart);
                    dateDiff = 90;
                }
                if (whatPeriod == 4)
                {
                    dtStart = dtEnd.AddDays(-365);
                    sStart = String.Format("{0:M/d/yyyy}", dtStart);
                    dateDiff = 365;
                }
                if (whatPeriod == 1 || whatPeriod == 5)
                {
                    sStart = sFirstResponse;
                }
                
                IEnumerable<initiativeLineCHARTdata> cdList_initiative = _packageService.getInitiativeFundGraphData(sStart, sEnd, PkgeID);
                //then like the person above said. divide by 7
                int totalWeeks = dateDiff / 7;

                return new genericResponse { results = cdList_initiative.ToList<iItemType>(), __count = cdList_initiative.Count(), message = totalWeeks .ToString()};
                //return Json(new { list = cdList_initiative, count = cdList_initiative.Count() }, JsonRequestBehavior.AllowGet);

            }
            return new genericResponse { __count = 0, results = new List<iItemType>() };
            //return Json(new { count = 0, list = new List<source_lineCHARTdata>() }, JsonRequestBehavior.AllowGet);
        }

        private MessageBoard process_ImportSource(string uploadedfilename, MessageBoard msgBoard, int pkgId)
        {

            try
            {
                // Open Excel and get the 1st row with Header
                var filepath = HttpContext.Current.Server.MapPath(uploadedfilename);//Added by Tanvir
                //var filepath = Server.MapPath(uploadedfilename);
                ImportExcelXls impExcel = new ImportExcelXls(filepath, true, true);
                DataSet ds = impExcel.result;
                //Set rows for the future use
                #region [[ Set Columns for future use ]]
                foreach (DataColumn dc in ds.Tables[0].Columns)
                {
                    //store in List for future use
                    columns.Add(dc.ToString());
                }
                #endregion

                #region [[ Process Rows ]]
                string _seq = "", s0 = "", s1 = "", s2 = "", s3 = "", s4 = "", s5 = "", s6 = "", s7 = "", s8 = "", s9 = "", s10 = "";
                DateTime? _md = null;
                int ss7 = 0;
                string _sql = "";
                double ss8 = 0.00, ss9 = 0.00, ss10 = 0.00;
                ExcelSourceRec.Clear();
                bNoRecordHasListNo = true;
                //bGotFileToImport = true;

                if (columns[0].ToString().Equals("Code") && columns[1].ToString().Equals("Description 1"))
                {
                    for (int j = 0; j < ds.Tables[0].Rows.Count; j++)
                    {
                        Row row = new Row();
                        List<string> data = new List<string>();
                        data.Clear();
                        data = ParseXlsLine(ds.Tables[0].Rows[j]);

                        int _empty_colcount = 0;

                        _seq = j.ToString();

                        for (int i = 0; i < columns.Count; i++)
                        {

                            string colHeader = columns[i];

                            switch (colHeader)
                            {
                                case "Code":
                                    row[i.ToString()] = data[i];
                                    if (String.IsNullOrEmpty(data[i]))
                                    {
                                        s0 = "";
                                    }
                                    else
                                    {
                                        s0 = row[i.ToString()].ToString();
                                    }
                                    break;
                                case "Description 1":
                                    row[i.ToString()] = data[i];
                                    if (String.IsNullOrEmpty(data[i]))
                                    {
                                        s1 = "";
                                    }
                                    else
                                    {
                                        s1 = row[i.ToString()].ToString();
                                    }
                                    break;
                                case "Description 2":
                                    // s2,
                                    row[i.ToString()] = data[i];
                                    s2 = "";
                                    break;
                                case "Description 3":
                                    // s3,
                                    row[i.ToString()] = data[i];
                                    s3 = "";
                                    break;
                                case "@":
                                    // LISTNOG = s4,
                                    row[i.ToString()] = data[i];
                                    if (String.IsNullOrEmpty(data[i]))
                                    {
                                        s4 = "";
                                    }
                                    else
                                    {
                                        s4 = row[i.ToString()].ToString();
                                    }
                                    break;
                                case "List#":
                                    // LISTNO = s5,                                   
                                    row[i.ToString()] = data[i];
                                    if (String.IsNullOrEmpty(data[i]))
                                    {
                                        s5 = "";
                                    }
                                    else
                                    {
                                        s5 = row[i.ToString()].ToString();
                                        bNoRecordHasListNo = false;
                                    }
                                    break;
                                case "Mail Date":

                                    if (String.IsNullOrEmpty(data[i]))
                                    {
                                        s6 = "";
                                        _md = null;
                                        row[i.ToString()] = _md.ToString();
                                    }
                                    else
                                    {

                                        row[i.ToString()] = data[i];
                                        s6 = row[i.ToString()].ToString();

                                        if (GetTheVALIDDateTimeValue(s6) != null)
                                        {
                                            _md = Convert.ToDateTime(GetTheVALIDDateTimeValue(s6));
                                        }
                                        else
                                        {
                                            _md = null;
                                        }

                                    }
                                    break;
                                case "#Mailed":

                                    row[i.ToString()] = data[i];

                                    if (String.IsNullOrEmpty(data[i]))
                                    {
                                        s7 = "";
                                        ss7 = 0;
                                    }
                                    else
                                    {
                                        s7 = data[i].ToString();
                                        // trifs 17 May 2011 7:00am
                                        if (s7.Contains("."))
                                        {
                                            double dS7 = Convert.ToDouble(s7);
                                            s7 = Math.Round(dS7, 0).ToString();
                                        }
                                        if (i > 0) ss7 = Convert.ToInt32(s7);
                                    }
                                    break;
                                case "Prod Cost":
                                    // COSTPROD1 = s8,
                                    // _COSTPROD1 = ss8,
                                    row[i.ToString()] = data[i];

                                    if (String.IsNullOrEmpty(data[i]))
                                    {
                                        s8 = "";
                                        ss8 = 0.00;
                                    }
                                    else
                                    {
                                        s8 = data[i].ToString();
                                        if (i > 0) ss8 = Convert.ToDouble(s8);
                                    }
                                    break;
                                case "List Cost":
                                    // COSTPROD2 = s9,
                                    // _COSTPROD2 = ss9,
                                    row[i.ToString()] = data[i];

                                    if (String.IsNullOrEmpty(data[i]))
                                    {
                                        s9 = "";
                                        ss9 = 0.00;
                                    }
                                    else
                                    {
                                        s9 = data[i].ToString();
                                        if (i > 0) ss9 = Convert.ToDouble(s9);
                                    }
                                    break;
                                case "Post Cost":
                                    row[i.ToString()] = data[i];

                                    if (String.IsNullOrEmpty(data[i]))
                                    {
                                        s10 = "";
                                        ss10 = 0.00;
                                    }
                                    else
                                    {
                                        s10 = data[i].ToString();
                                        if (i > 0) ss10 = Convert.ToDouble(s10);
                                    }

                                    break;
                                default:
                                    row[i.ToString()] = data[i];
                                    break;
                            }

                            if (String.IsNullOrEmpty(data[i]))
                                _empty_colcount++;
                        }

                        if (columns.Count != _empty_colcount)
                        {
                            rows.Add(row);

                            ExcelSourceRec.Add(new EXCELSOURCEREC
                            {
                                SEQ = _seq,
                                SRCECODE = s0,
                                SRCEDESC = s1,
                                LISTNOG = s4,
                                LISTNO = s5,
                                MAILDTE = s6,
                                sQTYMAIL = s7,
                                COSTPROD1 = s8,
                                COSTPROD2 = s9,
                                COSTPOSTG = s10,
                                _MAILDTE = _md,
                                _sQTYMAIL = ss7,
                                _COSTPROD1 = ss8,
                                _COSTPROD2 = ss9,
                                _COSTPOSTG = ss10
                            });
                        }
                    }
                }
                #endregion

                var qChkDupsExcelSourceRec = from esr in ExcelSourceRec
                                             where esr.SRCECODE != ""
                                             group esr by esr.SRCECODE into esrGroup
                                             where esrGroup.Count() > 1
                                             select esrGroup;

                if (qChkDupsExcelSourceRec.Count() > 0)
                {
                    //We have found Duplicate codes in the file so we need to inform user
                    msgBoard.status = false;
                    msgBoard.message = "Excel file has duplicate codes. Please fix it first and retry.";
                }
                else
                {
                    // Now ... check whether code(s) in the Excel file already exist in the system
                    gSrceCodeList = "";

                    #region [[ Prepare the List ]]
                    for (int i = 0; i < ExcelSourceRec.Count; i++)
                    {
                        if (gSrceCodeList != "") gSrceCodeList = gSrceCodeList + ",";
                        gSrceCodeList = gSrceCodeList + "'" + ExcelSourceRec[i].SRCECODE.ToString() + "'";
                    }
                    #endregion

                    //Prepare SQL for the Query
                    _sql = String.Format(_sql_base_check_unique_source_code, gSrceCodeList);
                    //Now Check if file has unique source code or not
                    msgBoard = _packageService.process_sql(_sql, "checkUniqueSrceCode", msgBoard);
                    //We have response from the Database
                    if (msgBoard.status && !string.IsNullOrEmpty(msgBoard.xmlresponse))
                    {
                        #region [[ Prepare Source Code List ]]
                        System.IO.TextReader tr = new System.IO.StringReader(msgBoard.xmlresponse);
                        XDocument oDoc = XDocument.Load(tr);

                        var qSOURCE = from a in oDoc.Descendants("checkUniqueSrceCode")
                                      select new _SOURCE
                                      {
                                          SRCECODE = Convert.ToString(a.Element("SRCECODE").Value),
                                      };
                        #endregion

                        _checkUniqueSrceCodeList = null;
                        if (qSOURCE.Count() > 0)
                        {
                            #region [[ Prepare Message for the User ]]
                            //Yes code already exist in the system so we need to inform the user
                            _checkUniqueSrceCodeList = qSOURCE.ToList();
                            string sTheSrceCodeListToCheck = "";
                            for (int i = 0; i < _checkUniqueSrceCodeList.Count; i++)
                            {
                                if (sTheSrceCodeListToCheck != "") sTheSrceCodeListToCheck = sTheSrceCodeListToCheck + " + ";
                                sTheSrceCodeListToCheck = sTheSrceCodeListToCheck + _checkUniqueSrceCodeList[i].SRCECODE.ToString().Trim();
                            }
                            //Prepare message
                            msgBoard.status = false;
                            msgBoard.message = "Codes: " + sTheSrceCodeListToCheck + " already exist(s) in the system.  Please fix it first and retry.";
                            #endregion
                        }
                        else
                        {
                            //All is ok so let us go ahead and Import
                            #region [[ Proecess for Import ]]
                            // begin saving data here ...
                            string InsertStringList = "";
                            for (int i = 0; i < ExcelSourceRec.Count; i++)
                            {
                                p0 = pkgId.ToString();
                                if (ExcelSourceRec[i].SRCECODE == null)
                                {
                                    p1 = "";
                                }
                                else
                                {
                                    p1 = ApostropheCheck(ExcelSourceRec[i].SRCECODE.ToString());

                                }
                                if (ExcelSourceRec[i].SRCEDESC == null)
                                {
                                    p2 = "";
                                }
                                else
                                {
                                    p2 = ApostropheCheck(ExcelSourceRec[i].SRCEDESC.ToString());
                                }
                                if (ExcelSourceRec[i].LISTNOG == null)
                                {
                                    p3 = "";
                                }
                                else
                                {
                                    p3 = ApostropheCheck(ExcelSourceRec[i].LISTNOG.ToString());
                                }
                                if (ExcelSourceRec[i].LISTNO == null)
                                {
                                    p4 = "";
                                }
                                else
                                {
                                    p4 = ApostropheCheck(ExcelSourceRec[i].LISTNO.ToString());
                                }
                                if (ExcelSourceRec[i].MAILDTE == null)
                                {
                                    p5 = null;
                                }
                                else
                                {
                                    p5 = GetTheVALIDDateTimeValue(ExcelSourceRec[i]._MAILDTE.ToString());
                                }
                                if ((ExcelSourceRec[i].COSTPROD1 == null) || (ExcelSourceRec[i].COSTPROD1 == ""))
                                {
                                    p6 = "0.00";
                                }
                                else
                                {
                                    p6 = GetTheVALIDMoneyValue(ExcelSourceRec[i].COSTPROD1.ToString());
                                }
                                if ((ExcelSourceRec[i].COSTPROD2 == null) || (ExcelSourceRec[i].COSTPROD2 == ""))
                                {
                                    p7 = "0.00";
                                }
                                else
                                {
                                    p7 = GetTheVALIDMoneyValue(ExcelSourceRec[i].COSTPROD2.ToString());
                                }
                                if ((ExcelSourceRec[i].COSTPOSTG == null) || (ExcelSourceRec[i].COSTPOSTG == ""))
                                {
                                    p8 = "0.00";
                                }
                                else
                                {
                                    p8 = GetTheVALIDMoneyValue(ExcelSourceRec[i].COSTPOSTG.ToString());
                                }
                                if ((ExcelSourceRec[i].sQTYMAIL == null) || (ExcelSourceRec[i].sQTYMAIL == ""))
                                {
                                    p9 = "0";
                                }
                                else
                                {
                                    p9 = GetTheVALIDIntValue(ExcelSourceRec[i].sQTYMAIL.ToString());
                                }

                                p10 = String.Format("{0:MM/dd/yyyy HH:mm:ss}", DateTime.Now);

                                InsertStringList = InsertStringList + String.Format(_sql_base_insert_into_source, p0, p1, p2, p3, p4, p5, p6, p7, p8, p9, p10);

                                //For Loop
                                x++;
                                if (x > 7)
                                {
                                    x = 0; // reset counter 
                                    msgBoard = _packageService.process_sql(InsertStringList, "ImportSourceCodeList", msgBoard);
                                    InsertStringList = "";
                                }
                            }
                            //Final Save if records are left                             
                            if (x > 0)
                            {
                                //  SaveThis(InsertStringList);  // ... used for debugging ... trifs 10 Oct 2011 10:26am
                                msgBoard = _packageService.process_sql(InsertStringList, "ImportSourceCodeList", msgBoard);
                            }

                            //Let us check the Response
                            System.IO.TextReader tr1 = new System.IO.StringReader(msgBoard.xmlresponse);
                            XDocument oDoc1 = XDocument.Load(tr1);

                            var qIMPORT = from a in oDoc1.Descendants("ImportSourceCodeList")
                                          select a;

                            //All done so inform the user
                            if (x > 0)
                            {
                                msgBoard.status = true;
                                msgBoard.message = "Excel file Successfully loaded!";
                            }
                            #endregion

                        }
                    }
                    else
                    {
                        msgBoard.status = false;
                        msgBoard.message = "Error in Checking Unique Source Codes. Please try again later.";
                    }
                }

            }
            catch (System.Exception ex)
            {
                msgBoard.status = false;
                msgBoard.message = "Error in Processing Import Source Codes Data. Please try again later.";
            }
            //return the response
            return msgBoard;

        }

        [HttpPost, Route("crm/api/InitiativeSettings/ManageInitiative")]
        [apiAuthorize(AccessElement = "cmdiapp.dms.packages", AccessLevel = "a")]
        public genericResponse ManageInitiative(vwInitiative model)
        {
            if ((model?.package ?? null) == null)
            {
                return new genericResponse
                {
                    success = false,
                    message = "Unable to save Initiative.",
                    messageKey = "Invalid view model.  Package is null."
                };
            }

            return _packageService.ManagePackage(model.package);
        }

        [HttpGet, Route("crm/api/InitiativeSettings/GetInitiative")]
        [apiAuthorize(AccessElement = "cmdiapp.dms.packages", AccessLevel = "v")]
        public genericResponse GetInitiative(string searchText, int? page, int? pageSize)
        {
            #region [ Retrieve Input ]
            string _searchText = Library.util.kill_sqlBlacklistWord((searchText ?? "").Trim().Replace("'", "''''"));

            int _pageSize = (pageSize == null || pageSize.Value == 0 ? 10 : pageSize.Value);
            int _pageNo = (page == null || page.Value == 0 ? 1 : page.Value);

            string sortField = HttpContext.Current.Request.QueryString["sort[0][field]"];
            string sortDir = HttpContext.Current.Request.QueryString["sort[0][dir]"];
            string _sortOptions = "";
            if (!string.IsNullOrEmpty(sortField))
                _sortOptions = sortField;
            if (!string.IsNullOrEmpty(sortField) && !string.IsNullOrEmpty(sortDir))
                _sortOptions = _sortOptions + " " + sortDir;
            #endregion

            List<PACKAGE_ext> _list = new List<PACKAGE_ext>();
            genericResponse _response;
            string sqlText = _sql_base_pkg;
            string _loadSize = "DISTINCT";
            string _where = "";

            if (!string.IsNullOrEmpty(_searchText))
            {
                _searchText = Library.util.kill_sqlBlacklistWord(_searchText);

                #region [ Split Search Text > string1, string2 & string3 and create "Where" statement ]
                char[] delimiterChars = { ' ', ',', '.', ';', ':', '|', '\t' };

                string[] words = _searchText.Split(delimiterChars);

                if (words.Count() > 0)
                {
                    string string1 = words[0];
                    string string2 = (words.Count() > 1 ? words[1] : "");
                    string string3 = (words.Count() > 2 ? words[2] : "");

                    #region [ Where Clause ]
                    switch (words.Count())
                    {
                        case 1: // PKGCODE or DESCRIP
                            _where = string.Format("WHERE F.PKGECODE LIKE ''%{0}%'' OR F.PKGEDESC LIKE ''%{0}%''", string1);
                            break;

                        case 2: // PKGCODE and DESCRIP'
                            _where = string.Format("WHERE F.PKGECODE LIKE ''%{0}%'' OR F.PKGEDESC LIKE ''%{1}%''", string1, string2);
                            break;

                        default:
                            break;
                    }
                    #endregion

                    if (string.IsNullOrEmpty(_sortOptions)) _sortOptions = "LASTCAGE Desc";

                }
                #endregion
            }
            else
            {
                //Coming here when SearchText is null
                if (string.IsNullOrEmpty(_sortOptions)) _sortOptions = "LASTCAGE Desc";
            }
            //Compose SQL query
            sqlText = String.Format(sqlText, _loadSize, _where);
            //get data
            _list = _packageService.get_all_initiatives(sqlText, _sortOptions, _pageSize, _pageNo);

            Mapper.CreateMap<PACKAGE_ext, rPackage>();
            IEnumerable<iItemType> results = _list.Select(a => Mapper.Map<PACKAGE_ext, rPackage>(a)).ToList();
            if (results.Count() > 0)
            {
                _response = new genericResponse() { success = true, __count = _list.FirstOrDefault().count_, results = results.ToList() };
            }
            else
            {
                _response = new genericResponse() { success = true, __count = 0 };
            }

            return _response;
        }

        [HttpPost, Route("crm/api/InitiativeSettings/DestroyInitiative")]
        [apiAuthorize(AccessElement = "cmdiapp.dms.packages", AccessLevel = "d")]
        public genericResponse DestroyInitiative(int pkgId)
        {
            return _packageService.Delete(pkgId);
        }

        [HttpGet, Route("crm/api/InitiativeSettings/GetSourceForPackage")]
        [apiAuthorize(AccessElement = "cmdiapp.dms.packages", AccessLevel = "v")]
        public genericResponse GetSourceForPackage(string pkgId,  int? page, int? pageSize)
        {
            #region [ Retrieve "Sort" options ]
            string sortField = HttpContext.Current.Request.QueryString["sort[0][field]"];
            string sortDir = HttpContext.Current.Request.QueryString["sort[0][dir]"];

            int _pageSize = (pageSize == null ? 10 : pageSize.Value);
            int _pageNo = (page == null ? 1 : page.Value);

            string _sortOptions = "";
            if (!string.IsNullOrEmpty(sortField))
                _sortOptions = sortField;
            if (!string.IsNullOrEmpty(sortField) && !string.IsNullOrEmpty(sortDir))
                _sortOptions = _sortOptions + " " + sortDir;
            #endregion

            #region [[ Old code ]]
            //List<rSOURCE> _list = new List<rSOURCE>();
            //genericResponse _response;

            //if (string.IsNullOrEmpty(_sortOptions))
            //    _sortOptions = "PKGECODE ASC, SRCECODE ASC";

            //string sqlText = _sql_base_source;
            //string _where = "";

            ////Compose SQL query to get Source based on this Package
            //_where = string.Format("WHERE S.PKGEID={0}", pkgId);
            //sqlText = String.Format(sqlText, _where);
            ////Get Data
            //_list = _packageService.get_Sources(sqlText, _sortOptions, _pageSize, _pageNo);

            //IEnumerable<iItemType> results = _list;
            //if (results.Count() > 0)
            //{
            //    _response = new genericResponse() { success = true, __count = _list.FirstOrDefault().count_, results = results.ToList() };
            //}
            //else
            //{
            //    _response = new genericResponse() { success = true, __count = 0 };
            //}

            //return Json(_response, JsonRequestBehavior.AllowGet);
            #endregion

            List<SOURCE_ext> _list = new List<SOURCE_ext>();
            genericResponse _response;

            if (string.IsNullOrEmpty(_sortOptions))
                _sortOptions = "PKGECODE ASC, SRCECODE ASC";

            string sqlText = _sql_base_source;
            string _where = "";

            //Compose SQL query to get Source based on this Package
            _where = string.Format("WHERE S.PKGEID={0}", pkgId);
            sqlText = String.Format(sqlText, _where);
            //Get Data
            _list = _packageService.get_Sources(sqlText, _sortOptions, _pageSize, _pageNo);

            Mapper.CreateMap<SOURCE_ext, rSOURCE>();
            IEnumerable<iItemType> results = _list.Select(a => Mapper.Map<SOURCE_ext, rSOURCE>(a)).ToList();
            if (results.Count() > 0)
            {
                _response = new genericResponse() { success = true, __count = _list.FirstOrDefault().count_, results = results.ToList() };
            }
            else
            {
                _response = new genericResponse() { success = true, __count = 0 };
            }

            return _response;
        }
        #endregion

        [HttpGet, Route("crm/api/InitiativeSettings/CheckIfInitiativeHasEventSourceCodes/{pkgeId}")]
        [apiAuthorize(AccessElement = "cmdiapp.dms.packages", AccessLevel = "e")]
        public bool CheckIfInitiativeHasEventSourceCodes(int pkgeId)
        {
            return _packageService.CheckIfInitiativeHasEventSourceCodes(pkgeId);
        }

        [HttpPost, Route("crm/api/InitiativeSettings/LinkPackageSourceCodesToEvent/{pkgeId}/{eventId}/{overwrite}")]
        [apiAuthorize(AccessElement = "cmdiapp.dms.packages", AccessLevel = "e")]
        public genericResponse LinkPackageSourceCodesToEvent(int pkgeId, int eventId, bool overwrite = false)
        {
            return _packageService.LinkPackageSourceCodesToEvent(eventId, pkgeId, overwrite);
        }

        [HttpGet, Route("crm/api/InitiativeSettings/CanLinkEvent")]
        [apiAuthorize(AccessElement = "cmdiapp.dms.packages", AccessLevel = "e")]
        public bool CanLinkEvent()
        {
            return _packageService.CanLinkEvent();
        }
    }
}
