﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Data;
using System.Linq;
using System.Web.Mvc;
using System.Data.Linq;
using System.ComponentModel.DataAnnotations.Schema;
using cmdiapp.n.core.Domain.ViewModels;
using cmdiapp.n.core.Areas.crm.Domain.Data;

namespace cmdiapp.n.core.Areas.crm.Domain.Models
{

    [Table("crmIMPORTQUE")]
    public class crmIMPORTQUE : _entityBase_crm, iItemType
    {
        [Column]
        [Key]
        public int crmIMPORTQUEID { get; set; }

        [Column]
        public int? QUETYPEID { get; set; }

        [Column]        
        public int? IMPTYPEID { get; set; }

        [Column]
        public Int16? UID { get; set; }

        [Column]
        public DateTime? QUEDATE { get; set; }

        [Column]
        public DateTime? PROCESSDATE { get; set; }

        [Column]
        public string TABLENAME { get; set; }

        [Column]
        public string SQLTEXT { get; set; }
    }

}