﻿using System.Collections.Generic;
using System.Web.Mvc;

using cmdiapp.n.core.Areas.crm.Domain.Models;
using cmdiapp.n.core.Areas.crm.Domain.ViewModels;
using cmdiapp.n.core.Areas.crm.Domain.Repositories;
using System;
using cmdiapp.n.core.Domain.ViewModels;

namespace cmdiapp.n.core.Areas.crm.Domain.Services
{
    public interface IjfcService
    {
        List<v_JFC_batch_ext> get_v_distributions(string where, string orderBy, int pageSize, int pageNo);
        int deleteDistribution(string _sql);
        int saveDistribution(JFCDISTRIB _jfcdistrib);
        int addDistribution(JFCDISTRIB _jfcdistrib);
        List<v_JFC_batch_ext> get_jfcmisslink(string fundcode, string orderBy, int pageSize, int pageNo);
        List<jfc_Distrib_ext> get_jfcDistrib(string fundcode, string participant, string orderBy, int pageSize, int pageNo);
        List<jfc_AwaitDistrib_ext> get_jfcAwaitDistrib(string fundcode, string participant, string orderBy, int pageSize, int pageNo);
        List<jfc_Alloc_ext> get_jfcAlloc(string fundcode, string participant, string orderBy, int pageSize, int pageNo);
    }
}

