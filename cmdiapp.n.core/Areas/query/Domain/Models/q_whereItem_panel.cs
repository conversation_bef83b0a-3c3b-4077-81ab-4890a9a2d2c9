﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace cmdiapp.n.core.Areas.query.Domain.Models
{
    public class q_whereItem_panel
    {
        public string name { get; set; }
        public string caption { get; set; }
        #region [ Sub-Query Properties ]
        public bool showOR { get; set; }
        public bool showEXCL { get; set; }
        public bool forceSuppress { get; set; }
        public bool andSuppress { get; set; }
        #endregion
    }

    public class q_whereItem_panel_C : q_whereItem_panel
    {
        public List<q_whereItem> qItems { get; set; }
    }

    public class q_whereItem_panel_S : q_whereItem_panel
    {
        public bool groupBy { get; set; }
        public string sq_uniqId { get; set; }
        public string sq_from { get; set; }
        public string sq_groupBy { get; set; }
        public List<q_whereItem_S> qItems { get; set; }

        #region [ Sub-Query Properties ]
        public bool subquery { get; set; }
        #endregion

    }

}