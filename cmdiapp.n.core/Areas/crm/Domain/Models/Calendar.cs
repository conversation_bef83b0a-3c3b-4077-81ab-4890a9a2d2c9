﻿using System;
using cmdiapp.n.core.Domain.ViewModels;
using System.ComponentModel.DataAnnotations.Schema;
using cmdiapp.n.core.Areas.crm.Domain.Data;

namespace cmdiapp.n.core.Areas.crm.Domain.Models
{
    [Table("user_montly_calendar")]
    public class user_montly_calendar : _entityBase_crm, iItemType
    {
        public string INFOTYPE { get; set; }
        public int? PID { get; set; }
        public int ID { get; set; }
        public DateTime? STARTDATE { get; set; }
        public DateTime? ENDDATE { get; set; }
        public string DESCRIP { get; set; }
    }
}