﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.Serialization;
using System.Web;

namespace cmdiapp.n.core.Areas.quickdash.Domain.Models
{
    [DataContract]
    public class StartResult
    {
        [DataMember(Name = "success")]
        public bool IsSuccessful { get; set; }

        [DataMember(Name = "message")]
        public string ErrorMessage { get; set; }

        [DataMember(Name = "key")]
        public string Key { get; set; }

        [DataMember(Name = "definition")]
        public Definition Definition { get; set; }
    }
}