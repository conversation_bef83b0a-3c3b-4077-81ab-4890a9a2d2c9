﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace cmdiapp.n.core.Areas.query.Domain.Models
{
    public class q_def
    {
        public string name { get; set; }
        public string caption { get; set; }
        public bool showExportBtn { get; set; }
        public bool showQuickTextSearch { get; set; }
        public string searchBtnCaption { get; set; }    // Search Button Caption
        public string customBtnTop { get; set; }    // Custom Button at Top - Caption
        public string customBtnBottom { get; set; } // Custom Button at Bottom - Caption (Per Result)

    }

    public class q_def_C : q_def
    {
        public List<q_whereItem_panel_C> qPanels { get; set; }
    }

    public class q_def_S : q_def
    {
        public string aElementIdentifier { get; set; }
        public string sq_uniqId { get; set; }
        public string sq_fields { get; set; }   // Fields for Export
        public string sq_fieldsV { get; set; }  // Fields for View (Should be same as v_people_1
        public string sq_from { get; set; }
        public string sq_groupBy { get; set; }

        public int reportId { get; set; }
        public int reportNo { get; set; }
        public string reportName { get; set; }
        public string reportScript { get; set; }

        public List<q_whereItem_panel_S> qPanels { get; set; }

        public List<q_whereItem_S> qItems_
        {
            get
            {
                List<q_whereItem_S> allItems = new List<q_whereItem_S>();

                foreach (q_whereItem_panel_S item in qPanels)
                    allItems = allItems.Concat(item.qItems).ToList();

                return allItems;
            }
        }
    }

    public class q_defOptions
    {
        public string qDefName { get; set; }  // eg. crm/peopleSearch
    }

}