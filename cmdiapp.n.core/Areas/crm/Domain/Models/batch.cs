﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Data;
using System.Linq;
using System.Web.Mvc;
using System.Data.Linq;
using System.ComponentModel.DataAnnotations.Schema;

using cmdiapp.n.core.Domain.ViewModels;
using cmdiapp.n.core.Areas.crm.Domain.Data;

namespace cmdiapp.n.core.Areas.crm.Domain.Models
{
    // Updated by Lydia 2013-10-23
    [Table("BATCH")]
    public class BATCH : _entityBase_crm, iItemType
    {
        [Column]
        [Key]
        public int BATCHID { get; set; }

        [Column]
        [Required(ErrorMessage = "The Batch Header Number is required.")]
        public string BATCHNO { get; set; }

        [Column]
        [Required(ErrorMessage = "The Batch Date is required.")]
        public DateTime? BATCHDTE { get; set; }

        [Column]
        public Int16? BATTYPEID { get; set; }

        [Column]
        public Int16? MATLVLID { get; set; }

        [Column]
        public Int16? KEYLVLID { get; set; }

        [Column]
        public Byte ADDPEO { get; set; }//NOTE: This does not allow NULL

        [Column]
        public Byte UPDPEO { get; set; }//NOTE: This does not allow NULL

        [Column]
        public Byte REQCHKDGT { get; set; }//NOTE: This does not allow NULL

        [Column]
        public Byte? VERIFADDR { get; set; }

        [Column]
        public Int16? FUNDID { get; set; }

        [Column]
        public Int16? DEFCENTER { get; set; }

        [Column]
        public Int16? DEFCAMPGN { get; set; }

        [Column]
        public Int16? DEFMNYTYPE { get; set; }

        [Column]
        public Int16? DEFSRCTYPE { get; set; }

        [Column]
        public int? DEFSRCEID { get; set; }

        [Column]
        public Byte? DEFMNYCODE { get; set; }

        [Column]
        public int? FLAG1 { get; set; }

        [Column]
        public int? FLAG2 { get; set; }

        [Column]
        public int? FLAG3 { get; set; }

        [Column]
        public int? BATCHCNT { get; set; }

        [Column]
        public Decimal? BATCHAMT { get; set; }

        [Column]
        public int? cENTRYCNT { get; set; }

        [Column]
        public Decimal? cENTRYAMT { get; set; }

        [Column]
        public Byte cBALANCED { get; set; }//NOTE: This does not allow NULL
        
        [Column]
        public DateTime? cLOADDTE { get; set; }
        
        [Column]
        public DateTime? cLASTENTON { get; set; }
        
        [Column]
        public DateTime? cCREATEDON { get; set; }

        [Column]
        public int? cLOADCNT { get; set; }

        [Column]
        public Decimal? cLOADAMT { get; set; }

        [Column]
        public Byte LOADED { get; set; }//NOTE: This does not allow NULL
        
        [Column]
        public int? sNEWPEO { get; set; }

        [Column]
        public int? sNEWPEOP { get; set; }

        [Column]
        public int? sEXISPEO { get; set; }

        [Column]
        public int? sMODIPEO { get; set; }

        [Column]
        public int? sNOMONY { get; set; }

        [Column]
        public Decimal? sAMTMONY { get; set; }

        [Column]
        public int? CREATEDBY { get; set; }

        [Column]
        public int? ENTEREDBY { get; set; }

        [Column]
        public int? LOADEDBY { get; set; }

        [Column]
        public string COMMENT { get; set; }

        [Column]
        public Int16? defEXCEP { get; set; }
        
        [Column]
        public int? FLAG4 { get; set; }
        
        [Column]
        public int? FLAG5 { get; set; }
        
        [Column]
        public int? FLAG6 { get; set; }
        
        [Column]
        public int? cCCCNT { get; set; }

        [Column]
        public Decimal? cCCAMT { get; set; }

        [Column]
        public int? cCCAPPRCNT { get; set; }

        [Column]
        public Decimal? cCCAPPRAMT { get; set; }
        
        [Column]
        public int? cCCDECLCNT { get; set; }

        [Column]
        public Decimal? cCCDECLAMT { get; set; }

        [Column]
        public int? cage_declines { get; set; }

        [Column]
        public DateTime? billedOn { get; set; }

        [Column]
        public Int16? DEFMATCHID { get; set; }

        [Column]
        public string nondonor_pkg { get; set; }

        [Column]
        public int? nondonor_counts { get; set; }

        [Column]
        public Int16? DEFCHANNELID { get; set; }
    }

    public class NewDonorInfo 
    {
        public string FNAME { get; set; }

        public string LNAME { get; set; }

        public string ZIP { get; set; }
    }
    
    public class FieldcheckerBatch
    {
        public int BATCHID { get; set; }

        public string BATCHNO { get; set; }

        public DateTime BATCHDTE { get; set; }

        public Int16? FUNDID { get; set; }

        public Decimal? BATCHAMT { get; set; }

        public Int16? CENTERID { get; set; }

        public string searchText { get; set; }

        public string srcecode { get; set; }

        public int BATCHCNT { get; set; }

        public int? SRCEID { get; set; }

        public string QPID { get; set; }

        public string QSEARCHTEXT { get; set; }

        public string WHATFORQSEARCH { get; set; }

        public string FNAMEQ { get; set; }

        public string LNAMEQ { get; set; }

        public string ZIP { get; set; }

        public string CITY { get; set; }

        public string STREET { get; set; }

        public string STATE { get; set; }

        public bool IsNumberChanged { get; set; }

        public bool IsDateChanged { get; set; }
                        
    }
    
    public class FieldcheckerEnterReceipts
    {
        public string ersrcecode { get; set; }

        public string CKNO { get; set; }

        public int? MONYTYPE { get; set; }

        public Decimal? AMT { get; set; }

        public string erexcpcode { get; set; }
                
        public int? SRCEID { get; set; }
        
        public int? ADJTYPEID { get; set; }

        public Decimal? ADJAMT { get; set; }

        public DateTime? ADJDTE { get; set; }

        public int? FUNDID { get; set; }

        public int? ADJFUNDID { get; set; }

        public int? DTBATCHID { get; set; }

        public int? ODTBATCHID { get; set; }

        public int? BATCHID { get; set; }

        public int? PID { get; set; }

        public bool PROCESSCC { get; set; }

        public int? TRACKNO { get; set; }

        public Int16? PEOTYPE { get; set; }

        public string FECCMTEID { get; set; }

    }

    public class EnterReceipt
    {
        public string srcecode { get; set; }

        public string ersrcecode { get; set; }

        public string CKNO { get; set; }

        public string CARDFFNO { get; set; }

        public string CARDLFNO { get; set; }
        
        public int? MONYTYPE { get; set; }

        public Decimal? AMT { get; set; }

        public string erexcpcode { get; set; }

        public int? ADJTYPEID { get; set; }

        public Decimal? ADJAMT { get; set; }

        public DateTime? ADJDTE { get; set; }

        public DateTime? EXCEPDTE { get; set; }

        public int? FUNDID { get; set; }

        public int? ADJFUNDID { get; set; }

        public int? DTBATCHID { get; set; }

        public int? ODTBATCHID { get; set; }
                
        public int? BATCHID { get; set; }

        public string BATCHNO { get; set; }

        public int BATCHCNT { get; set; }

        public Int16? BATCHFUNDID { get; set; }

        public Int16? CENTERID { get; set; }

        public Decimal? BATCHAMT { get; set; }

        public DateTime? BATCHDTE { get; set; }

        public DateTime? RECVDTE { get; set; }
        
        public int? PID { get; set; }

        public Int16? PEOCODE { get; set; }
        
        public Int16? PEOTYPE { get; set; }

        public string FECCMTEID { get; set; }

        public string fecpac { get; set; }

        public int? ADDRESSID { get; set; }

        public int? SRCEID { get; set; }

        public Byte cRENEW { get; set; }

        public int? SELADDRESSID { get; set; }

        public string PREFIX { get; set; }

        public string FNAME { get; set; }

        public string MNAME { get; set; }

        public string LNAME { get; set; }

        public string SUFFIX { get; set; }

        public string SALUTATION { get; set; }

        public string INFSALUTAT { get; set; }

        public string PRESSALUT { get; set; }

        public string SPOUSENAME { get; set; }

        public string OCCUPATION { get; set; }
               
        public string EMPLOYER { get; set; }
               
        public string TITLE { get; set; }
        
        public string CITY { get; set; }
                
        public string STATE { get; set; }

        public string STREET { get; set; }
        
        public string ZIP { get; set; }
                
        public string PLUS4 { get; set; }

        public string ADDR1 { get; set; }

        public string ADDR2 { get; set; }
                
        public string CCEXPYR { get; set; }
                
        public string CCEXPMO { get; set; }
        
        public int? TRACKNO { get; set; }

        public string PHNNO1 { get; set; }

        public string PHNNO2 { get; set; }

        public string PHNNO3 { get; set; }

        public string PHNNO4 { get; set; }
               
        public string PHNNO5 { get; set; }

        public string MAILNAME { get; set; }

        public string MAILSALUATION { get; set; }
                
        public string CPREFIX { get; set; }
                
        public string CFNAME { get; set; }
                
        public string CMNAME { get; set; }
                
        public string CLNAME { get; set; }
                
        public string CSUFFIX { get; set; }

        public string FECMEMOTXT { get; set; }

        public string MCOMMENT { get; set; }

        public bool PrimMailRec { get; set; }

        public bool PROCESSCC { get; set; }

        public string[] FlagSelection { get; set; }

        public string[] KeywordSelection { get; set; }

        //public string CVV { get; set; }

        public bool RECURR { get; set; }

        public DateTime? RECURENDDTE { get; set; }

        public string AdjType { get; set; }

        public Nullable<short> status { get; set; }

        public string CCAUTHCODE { get; set; } 
        
        public string CCRESPMSG { get; set; }

        public string CCREFNO { get; set; }

        public int WEBGIFTID { get; set; }

        public int logid { get; set; }

        public Int16 ADDRTYPEID { get; set; }
    }

    public class genericResponseDE
    {
        public bool success { get; set; }
        public string message { get; set; }
        public int BatchId { get; set; }
        public int PID { get; set; }
        public bool IsWrngMsgExist { get; set; }

        public int prevDtBatchId { get; set; }
        public string adjustmentType { get; set; }
        public int newRecordId { get; set; }
        public bool duplicate { get; set; }

        public bool reloadPage { get; set; }

        public int __count { get; set; }
        public List<iItemType> results { get; set; }
    }

    public class BatchQR : _entityBase_crm, iItemType
    {
        public int BATCHID { get; set; }
             
        public string BATCHNO { get; set; }
        
        public DateTime? BATCHDTE { get; set; }

        public Int16? BATTYPEID { get; set; }

        public string BATTYPEDESC { get; set; }

        public Int16? MATLVLID { get; set; }

        public Int16? KEYLVLID { get; set; }

        public Byte ADDPEO { get; set; }

        public Byte UPDPEO { get; set; }

        public Byte REQCHKDGT { get; set; }

        public Int16? FUNDID { get; set; }

        public string FUNDDESC { get; set; }

        public Int16? DEFMNYTYPE { get; set; }

        public Int16? DEFSRCTYPE { get; set; }

        public int DEFSRCEID { get; set; }

        public int? FLAG1 { get; set; }

        public int? FLAG2 { get; set; }

        public int? FLAG3 { get; set; }               
       
        public int BATCHCNT { get; set; }

        public decimal? BATCHAMT { get; set; }
       
        public int? cENTRYCNT { get; set; }

        public decimal? cENTRYAMT { get; set; }

        public Byte cBALANCED { get; set; }
       
        public int? cLOADCNT { get; set; }

        public decimal? cLOADAMT { get; set; }

        public DateTime? cLOADDTE { get; set; }

        public Byte LOADED { get; set; }       

        public int? sNEWPEO { get; set; }

        public int? sEXISPEO { get; set; }

        public int? sMODIPEO { get; set; }

        public int? sNOMONY { get; set; }

        public decimal? sAMTMONY { get; set; }

        public int? CREATEDBY { get; set; }

        public int? ENTEREDBY { get; set; }

        public int? LOADEDBY { get; set; }
    }

    public class BatchQR_ext1 : BatchQR
    {
        public int count_ { get; set; }

        public Int64 rowNo { get; set; }
    }

    public class thisBatchList_ext  
    {
        public int BATCHID { get; set; }
        public string BATCHNO { get; set; }
        public DateTime? BATCHDTE { get; set; }
        public Int16 BATTYPEID { get; set; }
        public string BATTYPEDESC { get; set; }
        public Int16 FUNDID { get; set; }
        public string FUNDDESC { get; set; }
        public Int16 DEFCENTER { get; set; }
        public int DEFSRCEID { get; set; }
        public int BATCHCNT { get; set; }
        public decimal BATCHAMT { get; set; }
        public int cENTRYCNT { get; set; }
        public decimal cENTRYAMT { get; set; }
        public Byte cBALANCED { get; set; }
        public int sNOMONY { get; set; }
        public decimal sAMTMONY { get; set; }
        public int cLOADCNT { get; set; }
        public decimal cLOADAMT { get; set; }
        public Byte LOADED { get; set; }
        public DateTime? cLOADDTE { get; set; }
        public int TOTALCOUNT { get; set; }
        public int count_ { get; set; }
        public Int64 rowNo { get; set; }

    }

    public class BatchExtended
    {
        public int CNTCC { get; set; }
        public int CNTAPV { get; set; }
        public bool LOADED { get; set; }
        public int AddEligible { get; set; }
        public int LoadEligible { get; set; }
        
        public string cENTRYAMTReadOnly { get; set; }
        public string cLOADDTEReadOnly { get; set; }
        public string sAMTMONYReadOnly { get; set; }

        public int dtBatchCount { get; set; }
        public int MonyCount { get; set; }
        public int showHideEnterReceiptsBtn { get; set; }
        public int showHideLoadTransactionsBtn { get; set; }

        [Required(ErrorMessage = "The Source Code is required.")]
        public string SelectedSourceCode { get; set; }
        
    }
        
    [Table("dtBATCH")]
    public class dtBATCH : _entityBase_crm, iItemType
    {
        [Column]
        [Key]
        public int DTBATCHID { get; set; }

        [Column]
        public int BATCHID { get; set; }

        [Column]
        public int? INPUTID { get; set; }

        [Column]
        public int MONYCODE { get; set; }

        [Column]
        public int? PID { get; set; }

        [Column]
        public Byte? CHKDGT { get; set; }
                
        [Column]
        public string ACCTNO { get; set; }

        [Column]
        public Int16? PEOCODE { get; set; }
        
        [Column]
        public Int16? PEOTYPE { get; set; }
              
        [Column]
        public string PREFIX { get; set; }

        [Column]
        public string FNAME { get; set; }

        [Column]
        public string MNAME { get; set; }

        [Column]
        [Required(ErrorMessage = "The Last Name is required.")]
        public string LNAME { get; set; }

        [Column]
        public string SUFFIX { get; set; }

        [Column]
        public string SALUTATION { get; set; }

        [Column]
        public string INFSALUTAT { get; set; }

        [Column]
        public string SPOUSENAME { get; set; }

        [Column]
        public string PHNNO1 { get; set; }

        [Column]
        public Int16? PHNTYPE1 { get; set; }

        [Column]
        public string PHNNO2 { get; set; }

        [Column]
        public Int16? PHNTYPE2 { get; set; }

        [Column]
        public string PHNNO3 { get; set; }

        [Column]
        public Int16? PHNTYPE3 { get; set; }

        [Column]
        public string PHNNO4 { get; set; }

        [Column]
        public Int16? PHNTYPE4 { get; set; }

        [Column]
        public string PHNNO5 { get; set; }

        [Column]
        public Int16? PHNTYPE5 { get; set; }
        
        [Column]
        [Required(ErrorMessage = "The Street is required.")]
        public string STREET { get; set; }

        [Column]
        public string ADDR1 { get; set; }

        [Column]
        public string ADDR2 { get; set; }

        [Column]
        [Required(ErrorMessage = "The City is required.")]
        public string CITY { get; set; }

        [Column]
        [Required(ErrorMessage = "The State is required.")]
        public string STATE { get; set; }

        [Column]
        [Required(ErrorMessage = "The Zip is required.")]
        public string ZIP { get; set; }

        [Column]
        public string PLUS4 { get; set; }
        
        [Column]
        public Int16? COUNTRY { get; set; }

        [Column]
        public string OCCUPATION { get; set; }

        [Column]
        public string EMPLOYER { get; set; }

        [Column]
        public string TITLE { get; set; }

        [Column]
        public string PCOMMENT { get; set; }

        [Column]
        public int? MID { get; set; }

        [Column]
        public string BATCHNO { get; set; }

        [Column]
        public DateTime? BATCHDTE { get; set; }

        [Column]
        public Int16? CENTERID { get; set; }

        [Column]
        public Int16? CAMPGNID { get; set; }

        [Column]
        public int SRCEID { get; set; }
        
        [Column]
        public Int16? SRCTYPE { get; set; }

        [Column]
        public Int16? MONYTYPE { get; set; }
        
        [Column]
        public string CKNO { get; set; }

        [Column]
        [Required(ErrorMessage = "The AMT is required!!")]
        public Decimal? AMT { get; set; }

        [Column]
        public Byte ATTRIBUTED { get; set; }
        
        [Column]
        public string MCOMMENT { get; set; }

        [Column]
        public Nullable<Byte> cNEWREC { get; set; }

        [Column]
        public Nullable<Byte> cNEWRECP { get; set; }

        [Column]
        public Nullable<Byte> cRENEW { get; set; }

        [Column]
        public Nullable<Byte> cRENEWCHG { get; set; }

        [Column]
        public DateTime? ENTRYDTE { get; set; }

        [Column]
        public Int16? ENTEREDBY { get; set; }

        [Column]
        public Byte LOADED { get; set; }

        [Column]
        public int? FLAG1 { get; set; }

        [Column]
        public int? FLAG2 { get; set; }

        [Column]
        public int? FLAG3 { get; set; }
        
        [Column]
        public Byte SOFTMONEY { get; set; }

        [Column]
        public int? OdtBATCHID { get; set; }

        [Column]
        public Int16? ADJTYPEID { get; set; }

        [Column]
        public DateTime? ADJDTE { get; set; }
        
        [Column]
        public bool? HASSPLIT { get; set; }

        [Column]
        public bool? REATTRIBUTE { get; set; }

        [Column]
        public int? IMAGEID { get; set; }

        [Column]
        public Int16? EXCEPID { get; set; }

        [Column]
        public int? FLAG4 { get; set; }

        [Column]
        public int? FLAG5 { get; set; }

        [Column]
        public int? FLAG6 { get; set; }

        [Column]
        public string FECCMTEID { get; set; }

        [Column]
        public int? TRACKNO { get; set; }

        [Column]
        public string CPREFIX { get; set; }
        
        [Column]
        public string CFNAME { get; set; }

        [Column]
        public string CMNAME { get; set; }

        [Column]
        public string CLNAME { get; set; }

        [Column]
        public string CSUFFIX { get; set; }
        
        [Column]
        public DateTime? EXCEPDTE { get; set; }

        [Column]
        public DateTime? RECVDTE { get; set; }
        
        [Column]
        public string FECMEMOTXT { get; set; }

        [Column]
        public string CCEXPYR { get; set; }

        [Column]
        public string CCEXPMO { get; set; }

        [Column]
        public string CCAUTHCODE { get; set; }

        [Column]
        public string CCREFNO { get; set; }

        [Column]
        public string CCRESPMSG { get; set; }

        [Column]
        public bool CCPROCESS { get; set; }

        [Column]
        public bool CCAPPROVED { get; set; }

        [Column]
        public Int16? ADDRTYPEID { get; set; }

        [Column]
        public Nullable<int> ADDRESSID { get; set; }

        [Column]
        public Decimal? ADJAMT { get; set; }
        
        [Column]
        public string ASSISTANT { get; set; }

        [Column]
        public string INPUTNO { get; set; }

        [Column]
        public int? SPOUSEPID { get; set; }

        [Column]
        public Int16? FUNDID { get; set; }

        [Column]
        public bool? FULL_REDESIGNATION { get; set; }

        [Column]
        public bool? PARTIAL_REDESIGNATION { get; set; }

        [Column]
        public string CCAVSRESP { get; set; }

        [Column]
        public Int16? CHANNELID { get; set; }

        [Column]
        public string KWRD1 { get; set; }

        [Column]
        public string KWRD2 { get; set; }

        [Column]
        public string KWRD3 { get; set; }

        [Column]
        public string KWRD4 { get; set; }

        [Column]
        public string KWRD5 { get; set; }

        [Column]
        public string KWRD6 { get; set; }

        [Column]
        public bool? PRIMEMAIL { get; set; }

        [Column]
        public DateTime? DOB { get; set; }

        [Column]
        public string PRESSALUT { get; set; }

        [Column]
        public string MAILNAME { get; set; }

        [Column]
        public string CTITLE { get; set; }

        [Column]
        public string EMAIL2 { get; set; }

        [NotMapped]
        public string CARDFFNO
        {
            get
            {
                return (!string.IsNullOrEmpty(CKNO) && CKNO.Length > 12 ? CKNO.Substring(0, 4) : " ");
            }
        }

        [NotMapped]
        public string CARDLFNO
        {
            get
            {
                return (!string.IsNullOrEmpty(CKNO) && CKNO.Length > 12 ? CKNO.Substring((CKNO.Length - 4), 4) : " ");
                
            }
        }


    }

    [NotMapped]
    public class dtBatch_ext : dtBATCH
    {
        public string FLAGCODE1 { get; set; }

        public string FLAGCODE2 { get; set; }

        public string FLAGCODE3 { get; set; }

        public string FLAGCODE4 { get; set; }

        public string FLAGCODE5 { get; set; }

        public string FLAGCODE6 { get; set; }

        public int? KWRDID1 { get; set; }

        public int? KWRDID2 { get; set; }

        public int? KWRDID3 { get; set; }

        public int? KWRDID4 { get; set; }

        public int? KWRDID5 { get; set; }

        public int? KWRDID6 { get; set; }

        public Int16? pADJTYPEID { get; set; }

        public DateTime? pADJDTE { get; set; }

        public Decimal? pADJAMT { get; set; }

        public int? pADJFUNDID { get; set; }

        public bool? RECURRED { get; set; }

        public DateTime? RECURENDDTE { get; set; }

        public int? WEBGIFTID { get; set; }

        public int? MONYCCBATID { get; set; }

        public int? CAGEBATCHDTLID { get; set; }

        public IEnumerable<dtBATCHCONDUIT> dtBATCHCONDUIT { get; set; }
    }

    public class PeopleSummary
    {
        public string FUNDCODE { get; set; }
        public decimal CUMTOT { get; set; }
        public decimal? REMAIN { get; set; }
        public string CUMTOTS { get; set; }
        public string REMAINS { get; set; }
    }

    public class PeopleDonationTotal
    {
        public decimal TOTALCUMAMTDONATED { get; set; }
        
    }

    public class dtBatchRec
    {
        public int DTBATCHID { get; set; }
        public int BATCHID { get; set; }
    }
    
    public class peopleFundDetails
    {
        public string Fund { get; set; }
        public Decimal Total { get; set; }
        public Decimal? Remaining { get; set; }
        public string CUMTOTS { get; set; }
        public string REMAINS { get; set; }
        public int count_ { get; set; }
        public Int64 rowNo { get; set; }
    }

    public class peopleSelected
    {
        public int PID { get; set; }

        public string CHKDGT { get; set; }

        public string PREFIX { get; set; }  

        public string FNAME { get; set; }   

        public string MNAME { get; set; }   

        public string LNAME { get; set; }   

        public string SUFFIX { get; set; }  

        public string SALUTATION { get; set; } 

        public string INFSALUT { get; set; }   

        public string PRESSALUT { get; set; }

        public string SPOUSENAME { get; set; }

        public int PRIMEMAIL { get; set; }

        public string EMPLOYER { get; set; }  
        
        public string OCCUPATION { get; set; }
        
        public Int16 PEOTYPEID { get; set; }
                
        public Int16 PEOCODEID { get; set; }

        public string FECCMTEID { get; set; }

        public int cRENEW { get; set; }

        public Nullable<int> ADDRESSID { get; set; }

        public string ADDR1 { get; set; }

        public string ADDR2 { get; set; }

        public string STREET { get; set; }

        public string CITY { get; set; }

        public string STATE { get; set; }

        public string ZIP { get; set; }

        public string PLUS4 { get; set; }
                
        public string cPREFIX { get; set; } 
                
        public string cFNAME { get; set; } 
                
        public string cMNAME { get; set; } 
                
        public string cLNAME { get; set; } 
                
        public string cSUFFIX { get; set; }

        public string cTITLE { get; set; }

        public string MAILNAME { get; set; }

        public string PHNNO1 { get; set; }
        
        public Int16 PHNTYPE1 { get; set; }

        public string PHNNO2 { get; set; }

        public Int16 PHNTYPE2 { get; set; }

        public string PHNNO3 { get; set; }

        public Int16 PHNTYPE3 { get; set; }

        public string PHNNO4 { get; set; }

        public Int16 PHNTYPE4 { get; set; }

        public string PHNNO5 { get; set; }

        public Int16 PHNTYPE5 { get; set; }
        
    }
    
    public class dtBatchExtended
    {
        [Required(ErrorMessage = "The Source Code is required.")]
        public string SelectedSourceCode { get; set; }

        public string SelectedExceptionCode { get; set; }

        public bool PrimeMailSelected { get; set; }

        public string MailName { get; set; }

        public string MailSaluation { get; set; }

        public string BatchDateString { get; set; }
                
        public int BATCHCNT { get; set; }
                
        public string BATCHAMT { get; set; }

        public int? sNOMONY { get; set; }

        public string sAMTMONY { get; set; }

        public string TOTALCUMAMTDONATEDS { get; set; }

        public string MessageOnScreen { get; set; }

        public string AdjustmentTypeOptionText { get; set; }

        public Int16 AdjFUNDID { get; set; }

        public bool AutoCaseReq { get; set; }
        
    }

    public class matchingCrieteria
    {
        public string fname { get; set; }

        public string lname { get; set; }

        public string zip { get; set; }

        public string p { get; set; }
    }

    public class peopleMatchingRecords
    {
        public int PID { get; set; }

        public string PEOCODE { get; set; }

        public string PEOTYPE { get; set; }

        public string PREFIX { get; set; }

        public string NAME { get; set; }

        public Nullable<int> ADDRESSID { get; set; }

        public string ADDRTYPE { get; set; }

        public string STREET { get; set; }

        public string CITY { get; set; }

        public string STATE { get; set; }

        public string ZIP { get; set; }

        public string KEYLINE3 { get; set; }

        public Int16 PEOCODEID { get; set; }

    }

    public class peopleMatchingRecords_ext
    {
        public int PID { get; set; }

        public string PEOCODE { get; set; }

        public string PEOTYPE { get; set; }

        public string PREFIX { get; set; }

        public string NAME { get; set; }

        public int ADDRESSID { get; set; }

        public string ADDRTYPE { get; set; }

        public string STREET { get; set; }

        public string CITY { get; set; }

        public string STATE { get; set; }

        public string ZIP { get; set; }

        public string KEYLINE3 { get; set; }

        public Int16 PEOCODEID { get; set; }

        public int count_ { get; set; }
        public Int64 rowNo { get; set; }

    }

    public class batchKeyedRecords_ext
    {
        public int BATCHID { get; set; }
        public int DTBATCHID { get; set; }
        public int PID { get; set; }
        public string NAME { get; set; }
        public string STREET { get; set; }
        public string CITY { get; set; }
        public string STATE { get; set; }
        public string ZIP { get; set; }
        public string SRCECODE { get; set; }
        public Decimal AMT { get; set; }
        public string CCRESPMSG { get; set; }
        public DateTime? ENTRYDTE { get; set; }
        public string ENTEREDBY { get; set; }
        public int count_ { get; set; }
        public Int64 rowNo { get; set; }
        public int MID { get; set; }
        public string ADJ { get; set; }
        //Additional Fields
        public int? OdtBATCHID { get; set; }
        public Boolean? HASSPLIT { get; set; }
        public Boolean? REATTRIBUTE { get; set; }
        public Boolean? PARTIAL_REDESIGNATION { get; set; }
        public int? ADJCNT { get; set; }
        //this controls if remove Adjustment button will be shown or not in the UI...
        public Boolean HideOrShow
        {
            get
            {
                if (ADJCNT > 0)
                    return false;
                else if(ADJCNT == 0 && (HASSPLIT == true || REATTRIBUTE == true || PARTIAL_REDESIGNATION == true))
                    return true;
                else
                    return false;
            }
        }

    }

    public class lkCCMonthOptions : _entityBase_crm, iItemType
    {
        public int MonthId { get; set; }

        public string MonthValue { get; set; }

        public string MonthText { get; set; }

    }

    public class lkCCYearOptions : _entityBase_crm, iItemType
    {
        public int YearId { get; set; }

        public string YearValue { get; set; }

        public string YearText { get; set; }

    }
    
    [Table("v_lkEXCEP")]
    public class v_lkEXCEP : _entityBase_crm
    {
        [Column]
        [Key]
        public Int16? EXCEPID { get; set; }

        [Column]
        public string CODEDESCP { get; set; }
        
    }

    [Table("v_SOURCE")]
    public class v_SOURCE : _entityBase_crm, iItemType
    {
        [Column]
        [Key]
        public int SRCEID { get; set; }

        [Column]
        public string SRCECODE { get; set; }

        [Column]
        public string CODEDESCP { get; set; }

    }


    public class selPeopleFLAGs : _entityBase_crm, iItemType
    {
        public int FLAGID { get; set; }
                
        public string FLAG { get; set; }
                
        public string FLAGDESC { get; set; }
                
    }

    public class selPeopledmKWRDs : _entityBase_crm, iItemType
    {
       public int KWRDID { get; set; }
               
        public string KWRD { get; set; }
                
        public string KWRDDESC { get; set; }
    }

    public class completedtBatchRecord
    {
        public int PID { get; set; }
        public Byte CHKDGT { get; set; }
        public string PREFIX { get; set; }
        public string FNAME { get; set; }
        public string MNAME { get; set; }
        public string LNAME { get; set; }
        public string SUFFIX { get; set; }
        public string SALUTATION { get; set; }
        public string INFSALUT { get; set; }
        public string PRESSALUT { get; set; }
        public string SPOUSENAME { get; set; }
        public string EMPLOYER { get; set; }
        public string OCCUPATION { get; set; }
        public int PRIMEMAIL { get; set; }
        public Int16 PEOCODEID { get; set; }
        public string FECCMTEID { get; set; }
        public int ADDRESSID { get; set; }
        public string ADDR1 { get; set; }
        public string ADDR2 { get; set; }
        public string STREET { get; set; }
        public string CITY { get; set; }
        public string STATE { get; set; }
        public string ZIP { get; set; }
        public string PLUS4 { get; set; }
        public string cPREFIX { get; set; }
        public string cFNAME { get; set; }
        public string cMNAME { get; set; }
        public string cLNAME { get; set; }
        public string cSUFFIX { get; set; }
        public string cTITLE { get; set; }
        public string PHNNO1 { get; set; }
        public Int16 PHNTYPE1 { get; set; }
        public string PHNNO2 { get; set; }
        public Int16 PHNTYPE2 { get; set; }
        public string PHNNO3 { get; set; }
        public Int16 PHNTYPE3 { get; set; }
        public string PHNNO4 { get; set; }
        public Int16 PHNTYPE4 { get; set; }
        public string PHNNO5 { get; set; }
        public Int16 PHNTYPE5 { get; set; }
        public int DTBATCHID { get; set; }
        public int BATCHID { get; set; }
        public int INPUTID { get; set; }
        public int MONYCODE { get; set; }
        public string ACCTNO { get; set; }
        public Int16 PEOTYPEID { get; set; }
        public Int16 COUNTRY { get; set; }
        public string TITLE { get; set; }
        public string PCOMMENT { get; set; }
        public int MID { get; set; }
        public string BATCHNO { get; set; }
        public DateTime? BATCHDTE { get; set; }
        public Int16 CENTERID { get; set; }
        public Int16 CAMPGNID { get; set; }
        public int SRCEID { get; set; }
        public Int16 SRCTYPE { get; set; }
        public Int16 MONYTYPEID { get; set; }
        public string CKNO { get; set; }
        public Decimal AMT { get; set; }
        public Byte ATTRIBUTED { get; set; }
        public string MCOMMENT { get; set; }
        public Nullable<Byte> cNEWREC { get; set; }
        public Nullable<Byte> cNEWRECP { get; set; }
        public Nullable<Byte> cRENEW { get; set; }
        public Nullable<Byte> cRENEWCHG { get; set; }
        public DateTime? ENTRYDTE { get; set; }
        public Int16? ENTEREDBY { get; set; }
        public Byte LOADED { get; set; }
        public int FLAGID1 { get; set; }
        public int FLAGID2 { get; set; }
        public int FLAGID3 { get; set; }
        public Byte SOFTMONEY { get; set; } //tinyInt datatype
        public int OdtBATCHID { get; set; }
        public Int16 ADJTYPEID { get; set; }
        public DateTime? ADJDTE { get; set; }
        public int HASSPLIT { get; set; }
        public int REATTRIBUTE { get; set; }
        public int IMAGEID { get; set; }
        public Int16 EXCEPID { get; set; } //smallInt
        public int FLAGID4 { get; set; }
        public int FLAGID5 { get; set; }
        public int FLAGID6 { get; set; }
        public int TRACKNO { get; set; }
        public DateTime? EXCEPDTE { get; set; }
        public DateTime? RECVDTE { get; set; }
        public string FECMEMOTXT { get; set; }
        public string CCEXPYR { get; set; }
        public string CCEXPMO { get; set; }
        public string CCAUTHCODE { get; set; }
        public string CCREFNO { get; set; }
        public string CCRESPMSG { get; set; }
        public bool CCPROCESS { get; set; } //bit datatype
        public bool CCAPPROVED { get; set; }
        public Int16 ADDRTYPEID { get; set; }
        public Decimal ADJAMT { get; set; }
        public string ASSISTANT { get; set; }
        public string INPUTNO { get; set; }
        public Int16 FUNDID { get; set; }
        public int FULL_REDESIGNATION { get; set; }
        public int PARTIAL_REDESIGNATION { get; set; } //COALESCE is used to it will be Int32
        public string MAILNAME { get; set; }
        
    }

    [Table("dtBATCHADDI")]
    public class dtBATCHADDI : _entityBase_crm, iItemType
    {
        [Column]
        [Key]
        public int dtBATCHADDIID { get; set; }

        [Column]
        public int dtBATCHID { get; set; }

        [Column]
        public string TMCAMPAIGN { get; set; }

        [Column]
        public string TMUNIQNUM { get; set; }

        [Column]
        public Nullable<bool> RECURRED { get; set; }

        [Column]
        public Nullable<DateTime> RECURENDDTE { get; set; }

        [Column]
        public int? WEBGIFTID { get; set; }

        [Column]
        public int? MONYCCBATID { get; set; }

        [Column]
        public int? CAGEBATCHDTLID { get; set; }
    }

    public class deleteBatchResults
    {
        public string tableName { get; set; }
        public string tableDescription { get; set; }
        public int count { get; set; }
        public bool isCoreTable { get; set; }
        public int batchId { get; set; }
    }

}
