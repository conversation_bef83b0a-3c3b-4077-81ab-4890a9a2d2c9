﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Data;
using System.Linq;
using System.Web.Mvc;
using System.Data.Linq;
using System.ComponentModel.DataAnnotations.Schema;

using cmdiapp.n.core.Domain.ViewModels;
using cmdiapp.n.core.Areas.crm.Domain.ViewModels;

namespace cmdiapp.n.core.Areas.crm.Domain.Models
{
    public class PeopleMatch: iItemType
    {
        public int PID { get; set; }
        public bool? ACTIVE { get; set; }
        public string PREFIX { get; set; }
        public string FNAME { get; set; }
        public string MNAME { get; set; }
        public string LNAME { get; set; }
        public string SUFFIX { get; set; }
        public string FULLNAME { get; set; }

        public string EMPLOYER { get; set; }
        public string OCCUPATION { get; set; }
        public string HMPH { get; set; }
        public string BUSPH { get; set; }
        public string CELLPH { get; set; }
        public string EMAIL { get; set; }
        public string STREET { get; set; }
        public string ADDR1 { get; set; }
        public string ADDR2 { get; set; }
        public string CITY { get; set; }
        public string STATE { get; set; }
        public string ZIP { get; set; }
        public string FULLADDR { get; set; }
        public string FACEBOOK { get; set; }
        public string TWITTER { get; set; }
        public string LINKEDIN { get; set; }

        public string matchHPHN { get; set; }
        public string matchCPHN { get; set; }
        public string matchEMAIL { get; set; }
        public string matchADDRESS { get; set; }
        public string matchEMPLOYER { get; set; }
        public string matchSPOUSE { get; set; }
        public string matchFACEBOOK { get; set; }
        public string matchTWITTER { get; set; }
        public string matchLINKEDIN { get; set; }
        public string matchANYPHONE { get; set; }

        public int? NOGIFTS { get; set; }
        public decimal? CUMTOT { get; set; }
        public int? YTDNO { get; set; }
        public decimal? YTDAMT { get; set; }
        public int? CTDNO { get; set; }
        public decimal? CTDAMT { get; set; }
        public decimal? FGIFT { get; set; }
        public DateTime? FGIFTDTE { get; set; }
        public decimal? LGIFT { get; set; }
        public DateTime? LGIFTDTE { get; set; }

        public bool notAdupe { get; set; }
    }
}
