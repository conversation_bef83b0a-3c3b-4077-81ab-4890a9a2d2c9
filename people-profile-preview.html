<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Crimson CRM - People Profile Preview</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #f8f9fa;
            margin: 0;
            padding: 20px;
        }
        
        /* Current Profile Overview Styles */
        .overview-container {
            display: flex;
            flex-flow: column nowrap;
            height: 490px;
            overflow: hidden;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            background: white;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .top-card {
            display: flex;
            flex-flow: column nowrap;
            justify-content: center;
            align-items: center;
            text-align: center;
            padding: 28px 15px 23px;
            background-color: #f8f9fa;
            border-bottom: 1px solid #dee2e6;
        }
        
        .picture-container {
            width: 110px;
            height: 110px;
            margin: 0 auto .5rem;
            position: relative;
        }
        
        .picture-container img {
            display: block;
            width: 100%;
            height: 100%;
            border-radius: 50%;
            object-fit: cover;
            border: 3px solid #fff;
            box-shadow: 0 2px 8px rgba(0,0,0,0.15);
        }
        
        .name {
            font-weight: 600;
            margin-bottom: 5px;
            font-size: 1.2rem;
            color: #333;
        }
        
        .id {
            font-size: .72rem;
            color: #6c7a86;
        }
        
        .bottom-card {
            padding: 15px;
            display: flex;
            flex-flow: column nowrap;
            justify-content: space-between;
            height: 50%;
        }
        
        .address-marker-container {
            display: flex;
            flex-flow: row nowrap;
            margin-bottom: 15px;
        }
        
        .address-marker-container a {
            width: 21px;
            color: #007bff;
            text-decoration: none;
        }
        
        .address-container {
            display: flex;
            flex-flow: column nowrap;
            margin-left: 10px;
        }
        
        .contact-container {
            display: flex;
            flex-flow: column nowrap;
            margin-bottom: 15px;
        }
        
        .contact-container > div {
            margin-bottom: 5px;
            font-size: 0.9rem;
        }
        
        .contact-container > div > i {
            width: 17px;
            margin-right: 8px;
            color: #6c757d;
        }
        
        .contact-container a {
            color: #007bff;
            text-decoration: none;
        }
        
        .buttons-footer {
            display: flex;
            flex-flow: row wrap;
            justify-content: space-between;
            align-items: center;
            padding-top: 15px;
            border-top: 1px solid #dee2e6;
        }
        
        .social-buttons {
            display: flex;
            flex-flow: row wrap;
            justify-content: flex-start;
            align-items: center;
        }
        
        .social-buttons > a {
            margin-right: 10px;
            font-size: 1.2rem;
            color: #6c757d;
            text-decoration: none;
        }
        
        .social-buttons > a:hover {
            color: #007bff;
        }
        
        .action-button {
            display: flex;
            flex-flow: row wrap;
            justify-content: flex-end;
        }
        
        .action-button > a {
            margin-left: 10px;
            font-size: 1.5rem;
            color: #007bff;
            text-decoration: none;
        }
        
        .action-button > a:hover {
            color: #0056b3;
        }
        
        /* Footer Styles */
        .page-footer-fixed {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            background: white;
            border-top: 1px solid #dee2e6;
            padding: 15px 20px;
            box-shadow: 0 -2px 4px rgba(0,0,0,0.1);
        }
        
        .footer-container {
            display: flex;
            justify-content: space-between;
            align-items: center;
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .left-buttons, .right-buttons {
            display: flex;
            gap: 10px;
        }
        
        .btn {
            padding: 8px 16px;
            border-radius: 4px;
            border: 1px solid;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 5px;
            font-size: 0.9rem;
        }
        
        .btn-primary {
            background-color: #007bff;
            border-color: #007bff;
            color: white;
        }
        
        .btn-success {
            background-color: #28a745;
            border-color: #28a745;
            color: white;
        }
        
        .btn-primary-outline {
            background-color: transparent;
            border-color: #007bff;
            color: #007bff;
        }
        
        .container-fluid {
            max-width: 1200px;
            margin: 0 auto;
            padding-bottom: 100px; /* Space for fixed footer */
        }
        
        .profile-section {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        
        .section-header {
            background-color: #f8f9fa;
            padding: 15px 20px;
            border-bottom: 1px solid #dee2e6;
            font-weight: 600;
            color: #333;
        }
        
        .section-content {
            padding: 20px;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <h1 class="mb-4">Current People Profile Preview</h1>
        
        <div class="row">
            <div class="col-md-4">
                <!-- Profile Overview Card -->
                <section class="overview-container">
                    <div class="top-card">
                        <div class="picture-container">
                            <img src="https://via.placeholder.com/110x110/007bff/ffffff?text=JS" alt="Profile Photo" />
                        </div>
                        <div class="name">John Smith</div>
                        <div class="id">ID #74178535</div>
                    </div>
                    
                    <div class="bottom-card">
                        <div class="address-marker-container">
                            <a href="#" title="Map Address">
                                <i class="fas fa-map-marker-alt fa-lg fa-2x"></i>
                            </a>
                            <div class="address-container">
                                <div>123 Main Street</div>
                                <div>Suite 100</div>
                                <div>Minneapolis, MN</div>
                                <div>55401</div>
                                <div>United States</div>
                            </div>
                        </div>
                        
                        <div class="contact-container">
                            <div>
                                <i class="fal fa-envelope"></i>
                                <a href="mailto:<EMAIL>"><EMAIL></a>
                            </div>
                            <div>
                                <i class="fal fa-home"></i>
                                <span>(*************</span>
                            </div>
                            <div>
                                <i class="fal fa-building"></i>
                                <span>(*************</span>
                            </div>
                            <div>
                                <i class="fal fa-mobile"></i>
                                <span>(*************</span>
                            </div>
                        </div>
                        
                        <div class="buttons-footer">
                            <div class="social-buttons">
                                <a href="#" target="_blank">
                                    <i class="fab fa-facebook-square"></i>
                                </a>
                                <a href="#" target="_blank">
                                    <i class="fab fa-twitter-square"></i>
                                </a>
                                <a href="#" target="_blank">
                                    <i class="fab fa-linkedin-square"></i>
                                </a>
                            </div>
                            <div class="action-button">
                                <a href="#" title="Address Book">
                                    <i class="fal fa-address-book"></i>
                                </a>
                                <a href="#" title="Phone Book">
                                    <i class="fal fa-phone-square"></i>
                                </a>
                            </div>
                        </div>
                    </div>
                </section>
            </div>
            
            <div class="col-md-8">
                <!-- Additional Profile Sections -->
                <div class="profile-section">
                    <div class="section-header">
                        <i class="fas fa-chart-line me-2"></i>
                        Giving History
                    </div>
                    <div class="section-content">
                        <p><strong>Total Contributions:</strong> $15,750</p>
                        <p><strong>First Gift:</strong> $250 on Jan 15, 2020</p>
                        <p><strong>Last Gift:</strong> $500 on Sep 10, 2024</p>
                        <p><strong>Number of Gifts:</strong> 12</p>
                    </div>
                </div>
                
                <div class="profile-section">
                    <div class="section-header">
                        <i class="fas fa-clock me-2"></i>
                        Recent Activity
                    </div>
                    <div class="section-content">
                        <p><strong>Sep 15, 2024:</strong> Email sent - Thank you for your recent contribution</p>
                        <p><strong>Sep 10, 2024:</strong> Gift received - $500 donation</p>
                        <p><strong>Aug 22, 2024:</strong> Phone call - Discussed upcoming fundraising event</p>
                        <p><strong>Aug 15, 2024:</strong> Note added - Interested in volunteer opportunities</p>
                    </div>
                </div>
                
                <div class="profile-section">
                    <div class="section-header">
                        <i class="fas fa-users me-2"></i>
                        Relationships
                    </div>
                    <div class="section-content">
                        <p><strong>Spouse:</strong> Jane Smith (ID #74178536)</p>
                        <p><strong>Organization:</strong> Smith Foundation</p>
                        <p><strong>Fundraiser:</strong> Sarah Johnson</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Footer with Action Buttons -->
    <footer class="page-footer-fixed">
        <div class="footer-container">
            <div class="left-buttons">
                <a href="#" class="btn btn-primary-outline" title="Dupe - Checking & Merge">
                    <i class="fas fa-people-arrows"></i>
                </a>
                <a href="#" class="btn btn-primary" title="Relationships">
                    <i class="fas fa-users"></i>
                </a>
                <a href="#" class="btn btn-success" title="Save as a Business Card (vCard)">
                    <i class="fas fa-save"></i>
                </a>
            </div>
            <div class="right-buttons">
                <button type="button" class="btn btn-success">
                    <i class="fas fa-user-edit"></i>
                    Edit Full Record
                </button>
                <button type="button" class="btn btn-success">
                    <i class="fas fa-copy"></i>
                    Copy Record
                </button>
                <button type="button" class="btn btn-primary">
                    <i class="fas fa-file-search"></i>
                    Look up
                </button>
                <button type="button" class="btn btn-primary">
                    <i class="fas fa-print"></i>
                    Print Call Sheets
                </button>
            </div>
        </div>
    </footer>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
