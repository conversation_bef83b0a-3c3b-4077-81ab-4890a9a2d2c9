﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Data;
using System.Data.SqlClient;
using Ninject;
using Ninject.Web.Mvc;

using cmdiapp.n.core.Core.App_Start;
using cmdiapp.n.core.Areas.crm.Domain.Models;
using cmdiapp.n.core.Areas.crm.Domain.ViewModels;
using cmdiapp.n.core.Areas.crm.Domain.Repositories;
using cmdiapp.n.core.Domain.ViewModels;
using cmdiapp.n.core.Library;
using cmdiapp.n.core.Areas.crm.Domain.Data;
using cmdiapp.n.core.Core;
using cmdiapp.n.core.Domain.Models;

namespace cmdiapp.n.core.Areas.crm.Domain.Services
{
    public class packageService : IpackageService
    {
        private I___unitOfWork _unitOfWork;
        private I__DBFactory _dbFactory;
        private IpackageRepository _packageRepository;
        private IinitiativeTypeRepository _initiativeTypeRepository;
        private IprogramRepository _programRepository;
        private ISubprogramRepository _SubprogramRepository;
        supportMethods _supportMethods = new supportMethods();
        private I_entity_crm _entity_crm;
        private readonly int MAX_DOC_FILE_SIZE = 4 * 1024 * 1024;
        private readonly userSession _userSession = session.userSession;


        public packageService(I___unitOfWork unitOfWork, I__DBFactory dbFactory, IpackageRepository PackageRepository, IinitiativeTypeRepository InitiativeTypeRepository, IprogramRepository ProgramRepository, ISubprogramRepository SubProgramRepository,I_entity_crm entity_crm)
        {
            _unitOfWork = unitOfWork;
            _dbFactory = dbFactory;
            _packageRepository = PackageRepository;
            _initiativeTypeRepository = InitiativeTypeRepository;
            _programRepository = ProgramRepository;
            _SubprogramRepository = SubProgramRepository;
            _entity_crm = entity_crm;
        }

        public MessageBoard process_sql(string p_sql, string p_datasetName, MessageBoard _msgBoard)
        {
            try
            {
                //get the data
                DataSet _data = _supportMethods.GetDataSet(_dbFactory.getContext().Database.Connection.ConnectionString, p_sql, p_datasetName);
                //cehck what we have
                if (_data != null)
                {
                    _msgBoard.status = true;
                    _msgBoard.xmlresponse = _data.GetXml();
                }
                else
                {
                    _msgBoard.status = false;
                    _msgBoard.xmlresponse = "<error msg=\"error occurred during data retrieval\"/>";
                    _msgBoard.message = "Error in DB Process: Import Source Codes " + p_datasetName + " " + " : Dataset is null.";
                }

            }
            catch (System.Exception ex)
            {
                //we have some issue
                _msgBoard.status = false;
                _msgBoard.xmlresponse = "";
                _msgBoard.message = "Error in DB Process: Import Source Codes " + p_datasetName + " " + ex.InnerException.ToString();
            }
            //return response
            return _msgBoard;
        }

        //public DataSet GetDataSet(string ConnectionString, string SQL, string p_datasetName)
        //{
        //    SqlConnection conn = new SqlConnection(ConnectionString);
        //    SqlDataAdapter da = new SqlDataAdapter();
        //    SqlCommand cmd = conn.CreateCommand();
        //    cmd.CommandText = SQL;
        //    da.SelectCommand = cmd;
        //    DataSet ds = new DataSet();
        //    //Let us open the connection
        //    conn.Open();
        //    da.Fill(ds, p_datasetName);
        //    conn.Close();
        //    //get the Dataset
        //    return ds;
        //}

        public PACKAGE get_package(int pkgId)
        {
            return _entity_crm.Single<PACKAGE>(p => p.PKGEID == pkgId);
        }

        public byte[] PackageImage (int pkgId)
        {
            var q = _dbFactory.getContext().Database.SqlQuery<packageImage>(String.Format("SELECT PKGEID, docFileBinaries, docFileName FROM PACKAGE WHERE PKGEID = {0}", pkgId));
            packageImage _result = q.FirstOrDefault();
            if (_result != null)
                return q.FirstOrDefault().docFileBinaries;
            else
                return null;
        }

        public string PackageImageName(int pkgId)
        {
            var q = _dbFactory.getContext().Database.SqlQuery<packageImage>(String.Format("SELECT PKGEID, docFileBinaries, docFileName FROM PACKAGE WHERE PKGEID = {0}", pkgId));
            packageImage _result = q.FirstOrDefault();
            if (_result != null)
                return q.FirstOrDefault().docFileName;
            else
                return null;
        }

        public bool IsReferencedBySource(int pkgId)
        {
            return _entity_crm.All<SOURCE>().Where(s => s.PKGEID == pkgId).Any();
        }

        public genericResponse ManagePackage(PACKAGE package)
        {
            PACKAGE existingPkg = _entity_crm.Single<PACKAGE>(p => package.PKGEID == p.PKGEID);

            string errMessage = ValidatePackage(package);

            if (!string.IsNullOrEmpty(errMessage))
            {
                return new genericResponse
                {
                    success = false,
                    message = errMessage
                };
            }
            try
            {
                package.UPDATEDON = DateTime.Now;

                if (existingPkg == null)
                {
                    // new
                    _entity_crm.Add(package);
                }
                else
                {
                    // update
                    UpdatePackageProperties(package, existingPkg);
                    _entity_crm.Update(existingPkg);
                }

                _entity_crm.CommitChanges();

                return new genericResponse
                {
                    success = true,
                    message = $"Initiative {package.PKGECODE} successfully saved.",
                    UniqueId = package.PKGEID
                };

            }
            catch (Exception ex)
            {
                return new genericResponse
                {
                    success = false,
                    message = "Unable to save Initiative. An error occurred.",
                    messageKey = ex.Message
                };
            }
        }


        public genericResponse Delete(int packageId)
        {
            try
            {
                PACKAGE package = get_package(packageId);

                if (package == null)
                {
                    return new genericResponse
                    {
                        success = false,
                        message = $"There is no Initiaitive with ID {packageId}."
                    };
                }

                if (IsReferencedBySource(packageId))
                {
                    return new genericResponse
                    {
                        success = false,
                        message = "The Initiative is being referenced by a Source Code, so it cannot be deleted."
                    };
                }

                _entity_crm.Delete(package);
                _entity_crm.CommitChanges();

                return new genericResponse
                {
                    success = true,
                    message = "Initiative deleted successfully."
                };

            }
            catch (Exception ex)
            {
                return new genericResponse
                {
                    success = false,
                    message = "Unable to delete Initiative.  An error occurred.",
                    messageKey = ex.Message
                };
            }
        }

        private string ValidatePackage(PACKAGE package)
        {
            if (string.IsNullOrEmpty(package.PKGECODE))
            {
                return "Initiative Code is required.";
            }

            if (string.IsNullOrEmpty(package.PKGEDESC))
            {
                return "Initiative Description is required.";
            }

            if (HasDuplicateCode(package))
            {
                return "Initiative code already exists. Please enter a unique code and try again.";
            }

            if (package.PROGID == null)
            {
                return "Program is required.  Please select a program for this Initiative.";
            }

            if (package.dtPROGID == null)
            {
                package.dtPROGID = 0;
            }


            return "";
        }

        private void UpdatePackageProperties(PACKAGE newPackage, PACKAGE existingPackage)
        {
            var props = typeof(PACKAGE).GetProperties();
            foreach (var prop in props)
            {
                prop.SetValue(existingPackage, prop.GetValue(newPackage));
            }
        }

        private bool HasDuplicateCode(PACKAGE _package)
        {
            return _entity_crm.All<PACKAGE>().Where(p =>
                p.PKGECODE.Trim().ToUpper() == _package.PKGECODE.Trim().ToUpper()
                && p.PKGEID != _package.PKGEID).Any();
        }

        public genericResponse UploadPackageDocument(HttpPostedFileBase file, int packageId)
        {

            if (file.ContentLength > MAX_DOC_FILE_SIZE)
            {
                return new genericResponse
                {
                    success = false,
                    message = "Attachment exceeds the max file size of 4MB."
                };
            }

            PACKAGE package = get_package(packageId);
            if (package == null)
            {
                return new genericResponse
                {
                    success = false,
                    message = $"There is no Initiative with ID {packageId}."
                };
            }

            try
            {
                byte[] ByteArray = new byte[file.ContentLength];
                file.InputStream.Read(ByteArray, 0, file.ContentLength);

                package.docFileBinaries = ByteArray;
                package.docFileName = file.FileName;
                package.docFileDate = DateTime.Now;
                package.docFileBy = _userSession.UserId_i.ToString();

                _entity_crm.Update(package);
                _entity_crm.CommitChanges();

                return new genericResponse { success = true };

            }
            catch (Exception ex)
            {
                return new genericResponse
                {
                    success = false,
                    message = "An error occurred. Unable to save Initiative attachment.",
                    messageKey = ex.Message
                };
            }
        }

        //Read all Initiative Types
        public List<lkInitiativeType> get_InitiativeTypes()
        {
            var _allRecords = _initiativeTypeRepository.All();
            //return data
            return _allRecords.OrderBy(p => p.initiativeTypeId).ToList();
        }

        //Read all Packages
        public List<PACKAGE_ext> get_all_Packages(string sql)
        {
            var q = _dbFactory.getContext().Database.SqlQuery<PACKAGE_ext>(String.Format("EXEC dbo.get_allDataSet '{0}'", sql));

            return q.ToList();

        }

        //Read all Programs
        public List<PROGRAM> get_Programs()
        {
            var _allRecords = _programRepository.All();
            //return data
            return _allRecords.OrderBy(p => p.PROGID).ToList();
        }

        public List<dtPROG> get_SubPrograms()
        {
            var _allRecords = _SubprogramRepository.All();
            //return data
            return _allRecords.OrderBy(p => p.dtPROGID).ToList();
        }

        //Read all Source for the Program selected for Edit : SQL query includes where condition for the PackageId
        //public List<rSOURCE> get_Sources(string sql, string orderBy, int pageSize, int pageNo)
        //{

        //    string final_sql = String.Format("EXEC dbo.get_pagedDataSet '{0}', '{1}', {2}, {3}", sql, orderBy, pageSize, pageNo);
        //    var q = _dbFactory.getContext().Database.SqlQuery<rSOURCE>(final_sql);

        //    return q.ToList();
        //}


        public List<SOURCE_ext> get_Sources(string sql, string orderBy, int pageSize, int pageNo)
        {

            string final_sql = String.Format("EXEC dbo.get_pagedDataSet '{0}', '{1}', {2}, {3}", sql, orderBy, pageSize, pageNo);
            var q = _dbFactory.getContext().Database.SqlQuery<SOURCE_ext>(final_sql);
            return q.ToList();
        }

        public List<PACKAGE_ext> get_all_initiatives(string sql, string orderBy, int pageSize, int pageNo)
        {
            
            string final_sql = String.Format("EXEC dbo.get_pagedDataSet '{0}', '{1}', {2}, {3}", sql, orderBy, pageSize, pageNo);
            var q = _dbFactory.getContext().Database.SqlQuery<PACKAGE_ext>(final_sql);
           
            return q.ToList();
        }

        public List<initiativeLineCHARTdata> getInitiativeFundGraphData(string sSTARTdt, string sENDdt, int PkgeID)
        {
            const string _sql_base_line_chart =
                    @"
                        IF OBJECT_ID('tempdb..#SRCEBATCH') IS NOT NULL 
                            drop table #SRCEBATCH

                        IF OBJECT_ID('tempdb..#actualDONATION') IS NOT NULL 
                            drop table #actualDONATION

                        -- declare variables to hold the start and end date
                        DECLARE @StartDate datetime
                        DECLARE @EndDate datetime

                        --- assign values to the start date and end date we 
                        --- want our reports to cover
                        SET @StartDate = '{0}'
                        SET @EndDate = '{1}' 

                        SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED
                        SET NOCOUNT ON

                        -- GET SRCE & BATCH DATE INFO FIRST
                        SELECT S.SRCEID, M.BATCHDTE 
                        INTO #SRCEBATCH
                        FROM PACKAGE P, SOURCE S, MONY M
                        WHERE 
                        M.BATCHDTE >= @StartDate AND M.BATCHDTE < DateAdd(d, 1, @EndDate) 
                        AND P.PKGEID = {2}
                        AND P.PKGEID = S.PKGEID AND M.SRCEID = S.SRCEID AND M.COUNTER = 1 AND M.SOFTMONEY = 0
                        GROUP BY S.SRCEID, M.BATCHDTE

                        SELECT YEAR(BATCHDTE)*100 + DATEPART(WW,BATCHDTE) AS _week,
                        MAX(ISNULL(BATCHDTE,'1/1/1900')) AS _batchdte,
                        SUBSTRING(CONVERT(VARCHAR(9), MAX(ISNULL(BATCHDTE,'1/1/1900')), 6),8,2) + SUBSTRING(CONVERT(VARCHAR(9), MAX(ISNULL(BATCHDTE,'1/1/1900')), 6),4,3) + SUBSTRING(CONVERT(VARCHAR(9), MAX(ISNULL(BATCHDTE,'1/1/1900')), 6),1,2) AS _date,
                        CONVERT(VARCHAR(10), MAX(ISNULL(BATCHDTE,'1/1/1900')), 101) AS _date1,
                        COUNT(*) AS _donationCOUNT,
                        SUM(M.AMT) AS _donationDOLLAR
                        INTO #actualDONATION
                        FROM MONY M
                        WHERE M.COUNTER = 1 AND
                        M.SOFTMONEY = 0 AND
	                    M.BATCHDTE >=  @StartDate AND  M.BATCHDTE < DateAdd(d, 1, @EndDate) AND
	                    M.SRCEID IN (SELECT S.SRCEID FROM #SRCEBATCH S )
                        GROUP BY YEAR(BATCHDTE)*100 + DATEPART(WW,BATCHDTE) 
                        ORDER BY YEAR(BATCHDTE)*100 + DATEPART(WW,BATCHDTE)

                        SELECT _week, _batchdte, _date, _date1, ISNULL(_donationCOUNT,0) AS _donationCOUNT, ISNULL(_donationDOLLAR,0.00) AS _donationDOLLAR FROM #actualDONATION
                    ";

            string sqlText = String.Format(_sql_base_line_chart, sSTARTdt, sENDdt, PkgeID);
            return _dbFactory.getContext().Database.SqlQuery<initiativeLineCHARTdata>(sqlText).ToList();
        }

        public List<initiativePieCHARTdata> getInitiativeInteractPieData(string _sql)
        {
            return _dbFactory.getContext().Database.SqlQuery<initiativePieCHARTdata>(_sql).ToList();
        }

        public List<initiativescrollLineCHARTdata> getInitiativeInteractLineData(string _sql)
        {
            return _dbFactory.getContext().Database.SqlQuery<initiativescrollLineCHARTdata>(_sql).ToList();
        }

        public _donationTOTALSfinal get_donationTOTALSfinal(int PkgeID)
        {
            const string _sql_base_donation_totals =
                    @"
                       IF OBJECT_ID('tempdb..#donationTOTALSinitial') IS NOT NULL 
                            drop table #donationTOTALSinitial

                       IF OBJECT_ID('tempdb..#donationTOTALSfinal') IS NOT NULL 
                            drop table #donationTOTALSfinal

                        -- initial query for totals
                        SELECT MIN(S.FIRSTCAGE) AS FIRSTCAGE,
                        MAX(S.LASTCAGE) AS LASTCAGE,
                        SUM(ISNULL(S.SPEOPLE,0)) AS DONORS,
                        SUM(ISNULL(S.SMONY,0)) AS DONATIONS, 
                        SUM(ISNULL(S.SGROSS,0)) AS GROSS,
                        SUM((( ISNULL(S.COSTPROD1,0)+ISNULL(S.COSTPROD2,0)+ISNULL(S.COSTPROD3,0)+ISNULL(S.COSTPOSTG,0) )*ISNULL(S.SQTYMAIL,0))+( ISNULL(S.COSTRESP1,0)+ISNULL(S.COSTRESP2,0)+ISNULL(S.COSTRESP3,0) )*ISNULL(S.SMONY,0)) AS COST,
                        SUM(ISNULL(S.SGROSS,0))-SUM((( ISNULL(S.COSTPROD1,0)+ISNULL(S.COSTPROD2,0)+ISNULL(S.COSTPROD3,0)+ISNULL(S.COSTPOSTG,0) )*ISNULL(S.SQTYMAIL,0))+( ISNULL(S.COSTRESP1,0)+ISNULL(S.COSTRESP2,0)+ISNULL(S.COSTRESP3,0) )*S.SMONY) AS NET
                        INTO #donationTOTALSinitial
                        FROM PROGRAM P, PACKAGE K, SOURCE S
                        WHERE P.PROGID = K.PROGID AND K.PKGEID = S.PKGEID AND K.PKGEID={0}
                        GROUP BY P.PROGTYPE, P.DESCRIP, K.PKGECODE, K.PKGEDESC, CAST(K.MAILDTE AS BIGINT)*1000000 + K.PKGEID, K.LASTCAGE, S.SRCECODE, S.SRCEDESC, LISTNO, LISTNOG 
                        ORDER BY FIRSTCAGE

                        -- consolidate totals into one record
                        SELECT MIN(FIRSTCAGE) AS FIRSTCAGE,
                               MAX(LASTCAGE) AS LASTCAGE,
                               SUM(donations) AS _totalDonationCount,
                               SUM(gross) AS _totalDonationDollar,
                               SUM(cost) AS _totalDonationCost,
                               SUM(net) AS _totalDonationNet
                        INTO #donationTOTALSfinal
                        FROM #donationTOTALSinitial

                        -- query consolidated totals data
                        SELECT  ISNULL(FIRSTCAGE,'1/1/1900') AS _firstcage,
                                ISNULL(LASTCAGE,'1/1/1900') AS _lastcage,
                                ISNULL(_totalDonationCount,0) AS _totalDonationCount,
                                ISNULL(_totalDonationDollar,0.00) AS _totalDonationDollar,
                                ISNULL(_totalDonationCost,0.00) AS _totalDonationCost,
                                ISNULL(_totalDonationNet,0.00) AS _totalDonationNet
                        FROM #donationTOTALSfinal
                    ";
            string sqlText = String.Format(_sql_base_donation_totals, PkgeID);
            return _dbFactory.getContext().Database.SqlQuery<_donationTOTALSfinal>(sqlText).FirstOrDefault();
        }

        public initiativeSourceCounts getSourceCounts(int pkgeId)
        {
            return (_entity_crm.All<SOURCE>()
                .Where(s => s.PKGEID == pkgeId)
                .GroupBy(s => s.PKGEID)
                .Select(grp => new initiativeSourceCounts
                {
                    sourceCodeCount = grp.Count(),
                    telemSourceCodeCount = grp.Count(s => s.teleM.HasValue && s.teleM.Value)
                })
                .FirstOrDefault() ?? new initiativeSourceCounts { sourceCodeCount = 0, telemSourceCodeCount = 0 });
        }

        public initiativeSummary getInitiativeSummary(int pkgeId)
        {
            return (from src in _entity_crm.All<SOURCE>()
                    join pkg in _entity_crm.All<PACKAGE>() on src.PKGEID equals pkg.PKGEID
                    where pkg.PKGEID == pkgeId
                    group src by new { src.PKGEID, pkg.LASTCAGE } into srcGrp
                    select new initiativeSummary
                    {
                        lastCageDate = srcGrp.Key.LASTCAGE,
                        gross = srcGrp.Sum(src => (decimal)(src.sGROSS.HasValue ? src.sGROSS : 0)),
                        numberOfGifts = srcGrp.Sum(src => (int)(src.sMONY.HasValue ? src.sMONY : 0))
                    }).FirstOrDefault() ?? new initiativeSummary { gross = 0, lastCageDate = null, numberOfGifts = 0 };                    
                    
        }

        public IQueryable<Initiative> GetDisplayInitiativeQueryable()
        {
            return _entity_crm.All<PACKAGE>().Select(p => new Initiative()
            {
                PKGEID = p.PKGEID,
                PKGECODE = p.PKGECODE,
                PKGEDESC = p.PKGECODE + " - " + p.PKGEDESC
            });
        }
        public IQueryable<dtPROG> GetDisplaydtPROGQueryable()
        {
            return _entity_crm.All<dtPROG>();
        }

        public bool CheckIfInitiativeHasEventSourceCodes(int pkgeId)
        {
            return (from src in _entity_crm.All<SOURCE>()
                    where src.PKGEID == pkgeId
                    && src.SPCEVNTID != null
                    select src.SRCEID).Any();
        }

        public genericResponse LinkPackageSourceCodesToEvent(
            int eventId,
            int pkgeId,
            bool overwriteExisting)
        {
            if (!CanLinkEvent())
            {
                return new genericResponse()
                {
                    success = false,
                    message = "User access is denied."
                };
            }

            if (_entity_crm.Single<EventF>(evt => evt.SPCEVNTID == eventId) == null)
            {
                return new genericResponse()
                {
                    success = false,
                    message = "There is no Event with that ID."
                };
            }

            if (_entity_crm.Single<PACKAGE>(pkg => pkg.PKGEID == pkgeId) == null)
            {
                return new genericResponse()
                {
                    success = false,
                    message = "There is no Initiative with that ID."
                };
            }

            try
            {
                return _dbFactory.getContext().Database.SqlQuery<genericResponse>(
                    "EXEC zLinkPackageToEvent @eventId, @pkgeId, @overwriteExisting",
                    new SqlParameter("@eventId", eventId),
                    new SqlParameter("@pkgeId", pkgeId),
                    new SqlParameter("@overwriteExisting", overwriteExisting)).FirstOrDefault();
            }
            catch (Exception ex)
            {
                return new genericResponse()
                {
                    success = false,
                    message = "An error occurred. Unable to update Source Codes.",
                    messageKey = ex.Message
                };
            }
        }

        public bool CanLinkEvent()
        {
            // only for RNC and internal projects, for now
            return (session.currentDomain_project.internalProject ||
                (session.currentDomain_project?.code ?? "").Trim().ToUpper() == "RNC")
                && session.can_i_do("cmdiapp.dms.packages", "e");
        }
    }
}