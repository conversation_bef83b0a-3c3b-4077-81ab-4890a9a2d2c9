﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Data;
using System.Web.Mvc;
using cmdiapp.n.core.Domain.Models;
using cmdiapp.n.core.Areas.crm.Domain.Models;

namespace cmdiapp.n.core.Areas.crm.Core
{
    public class ImportUpdateStep5ViewModel
    {
        public string RECTYPE { get; set; }
        public int ID { get; set; }
        public int TXNNO { get; set; }
        public int RECNO { get; set; }
        public string STATUS { get; set; }
        public decimal? AMT { get; set; }
        public string SOURCE { get; set; }
        public DateTime? BATCHDTE { get; set; }
        public string BATCHNO { get; set; }
        public string FUNDCODE { get; set; }
        public string CENTERCODE { get; set; }
        public string MONYTYPE { get; set; }
        public string CKNO { get; set; }
        public DateTime? RECVDTE { get; set; }
        public int? mTRACKNO { get; set; }
        public decimal? TRACKAMT { get; set; }
        public string REFERENCEID { get; set; }
        public string EXECPTIONCODE { get; set; }
    }
}
   