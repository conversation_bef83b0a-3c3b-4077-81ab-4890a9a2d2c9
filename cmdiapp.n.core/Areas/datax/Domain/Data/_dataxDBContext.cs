﻿using cmdiapp.n.core._Domain.Models;
using cmdiapp.n.core.Areas.datax.Domain.Models;
using System.Data.Entity;

namespace cmdiapp.n.core.Areas.datax.Domain.Data
{
    public class _dataxDBContext : DbContext
    {
        public _dataxDBContext(string _appDBContext_connString_itself_or_name) 
            : base(_appDBContext_connString_itself_or_name)
        {
            Database.SetInitializer(new DataxDBInitializer());
        }

        protected override void OnModelCreating(DbModelBuilder modelBuilder)
        {
            modelBuilder.Conventions.Remove<System.Data.Entity.ModelConfiguration.Conventions.PluralizingTableNameConvention>();
        }

        public DbSet<Account> Account { get; set; }
        public DbSet<Caller> Caller { get; set; }
        public DbSet<CremirSendJobInfo> CremirSendJobInfo { get; set; }
        public DbSet<CremirJobLog> CremirJobLog { get; set; }        
    }
}