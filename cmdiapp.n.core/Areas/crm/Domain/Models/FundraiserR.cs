﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Data;
using System.Linq;
using System.Web.Mvc;
using System.Data.Linq;
using System.ComponentModel.DataAnnotations.Schema;
using System.Xml.Serialization;

using cmdiapp.n.core.Domain.ViewModels;
using cmdiapp.n.core.Areas.crm.Domain.Data;

namespace cmdiapp.n.core.Areas.crm.Domain.Models
{
    public class FundraiserR : _entityBase_crm
    {
        [XmlAttribute("TRACKNO")]
        public int TRACKNO { get; set; }

        [XmlAttribute("PID")]
        public int PID { get; set; }

        [XmlAttribute("name")]
        public string name { get; set; }

        [XmlAttribute("cRaised")]
        public string cRaised_ { get; set; }
        public decimal cRaised
        {
            get { return Convert.ToDecimal(!String.IsNullOrEmpty(cRaised_) ? cRaised_ : "0"); }
        }

        [XmlAttribute("cRollup")]
        public string cRollup_ { get; set; }
        public decimal cRollup
        {
            get { return Convert.ToDecimal(!String.IsNullOrEmpty(cRollup_) ? cRollup_ : "0"); }
        }

        public string totalRaised
        {
            get
            {
                return string.Format("{0:c0}", cRaised+cRollup);
            }
        }
        /*
        public string TITLE { get; set; }

        public int? RECRUITERNO { get; set; }

        public Decimal? COMMITMENT { get; set; }

        public Decimal? cREFERRED { get; set; }

        public Decimal? cPLEDGED { get; set; }

        public Decimal? cPLEDGEDREF { get; set; }

        public Decimal? cSHARED { get; set; }
        */

        [XmlAttribute("photoUrl")]
        public string photoUrl
        {
            get; set; 
            /*
            get
            {
                return string.Format("/crm/api/People/Photo/{0}/{1}", PID, System.Guid.NewGuid().ToString());
            }
            */
        }

        [XmlArray("recruits")]
        [XmlArrayItem("FundraiserR")]
        public List<FundraiserR> children { get; set; }
    }

    
}
