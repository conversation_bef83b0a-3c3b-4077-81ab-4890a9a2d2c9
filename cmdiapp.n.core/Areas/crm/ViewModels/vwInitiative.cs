﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using cmdiapp.n.core.Areas.crm.Domain.Models;

namespace cmdiapp.n.core.Domain.ViewModels
{
    public class vwInitiative
    {
        public PACKAGE package { get; set; }
        public IEnumerable<lkInitiativeType> lkInitiativeType { get; set; }
        public IEnumerable<PROGRAM> PROGRAM { get; set; }
        public IEnumerable<dtPROG> SUBPROGRAM { get; set; }
        public string pageWidth { get; set; }
        public string pageHeight { get; set; }
        public string SubReportConfig { get; set; }
        public initiativeSourceCounts sourceCounts { get; set; }

    }

    //for line chart
    public class initiativeLineCHARTdata:iItemType
    {
        public int _week { get; set; }
        public DateTime? _batchdte { get; set; }
        public string _date { get; set; }
        public string _date1 { get; set; }
        public int _donationCOUNT { get; set; }

        //public string _dCOUNTFormatted
        //{
        //    get
        //    {
        //        return (_donationCOUNT > 0 ? Convert.ToDouble(_donationCOUNT).ToString("N") : _donationCOUNT.ToString());

        //    }
        //}

        public decimal _donationDOLLAR { get; set; }
    }

    public class initiativescrollLineCHARTdata:iItemType
    {
        public DateTime? _date { get; set; }
        public string _monthYear { get; set; }
        public int _signUpCount { get; set; }
        //public int _activityCount { get; set; }
        public int _uniqActivityCount { get; set; }
    }

    //for pie chart
    public class initiativePieCHARTdata:iItemType
    {
        public Int16 _activityGROUPid { get; set; }
        public string _activityGROUP { get; set; }
        public int _activityCODEid { get; set; }
        public string _activityCODE { get; set; }
        public int _activityYvalue { get; set; }
        public string _activityXlabel { get; set; }
    }

    //for initiative graph
    public class _donationTOTALSfinal
    {
        public DateTime _lastcage { get; set; }
        public DateTime _firstcage { get; set; }
        public int _totalDonationCount { get; set; }
        public decimal _totalDonationDollar { get; set; }
        public decimal _totalDonationCost { get; set; }
        public decimal _totalDonationNet { get; set; }
    }

    public class initiativeSourceCounts
    {
        public int sourceCodeCount { get; set; }
        public int telemSourceCodeCount { get; set; }
    }

    public class initiativeSummary
    {
        public DateTime? lastCageDate { get; set; }
        public decimal gross { get; set; }
        public int numberOfGifts { get; set; }
    }
}