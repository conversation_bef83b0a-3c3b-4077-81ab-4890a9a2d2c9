﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Web;
using cmdiapp.n.core.Areas.crm.Domain.Data;
using cmdiapp.n.core.Domain.ViewModels;
using Org.BouncyCastle.Asn1.Mozilla;
using Org.BouncyCastle.Bcpg.OpenPgp;

namespace cmdiapp.n.core.Areas.crm.Domain.Models
{
    [Table("v_cagebatch_batch")]
    public class v_cagebatch_batch : _entityBase_crm
    {
        [Key]
        public int cagebatchhdrid { get; set; } 
        public int cageacctid { get; set; }
        public int batchid { get; set; }
        public DateTime batchdte {  get; set; }
        public string batchno { get; set; }
        public string fundcode { get; set; }
        public string acctcode { get; set; }
        public string srcecode { get; set; }
        public int batchcnt { get; set; }
        public decimal batchamt { get; set; }
        public string filepath { get; set; }

    }

    [Table("v_cagebatch_image")]
    public class v_cagebatch_image : _entityBase_crm
    {
        [Key]
        public int cagebatchimageid { get; set; }
        public int cagebatchhdrid { get; set; }
        public Int16 imagetypeid { get; set; }
        public string imagename { get; set; }
        public Int16 seq {  get; set; }
        public string filepath { get; set; }
    }

    [Table("v_cagebatch_checkdocimage")]
    public class v_cagebatch_checkdocimage : _entityBase_crm
    {
        [Key]
        public Int64 id { get; set; }
        public int cagebatchhdrid { get; set; }
        public Int16 seq { get; set; }
        public int cagebatchdtlid { get; set; }
        public int? checkfrontimageid { get; set; }
        public string checkfrontimage { get; set; }
        public int? checkbackimageid { get; set; }
        public string checkbackimage { get; set; }
        public int? docfrontimageid { get; set; }
        public string docfrontimage { get; set; }
        public int? docbackimageid { get; set; }
        public string docbackimage { get; set; }
    }

    public class ScanCheckImageId
    {
        public int MONYimageID { get; set; }
        public Int16 Seq { get; set;  }
        public int cagebatchdtlid { get; set; }
        public bool isLinked { get; set; }
    }
}