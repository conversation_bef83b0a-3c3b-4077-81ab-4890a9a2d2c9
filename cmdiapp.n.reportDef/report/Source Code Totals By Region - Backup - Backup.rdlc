﻿<?xml version="1.0" encoding="utf-8"?>
<Report xmlns="http://schemas.microsoft.com/sqlserver/reporting/2005/01/reportdefinition" xmlns:rd="http://schemas.microsoft.com/SQLServer/reporting/reportdesigner">
  <DataSources>
    <DataSource Name="Dexter">
      <rd:DataSourceID>b13f9efc-8d30-4d0a-8324-151bd0013aa2</rd:DataSourceID>
      <DataSourceReference>Dexter</DataSourceReference>
    </DataSource>
  </DataSources>

  <InteractiveHeight>11in</InteractiveHeight>
  <rd:DrawGrid>true</rd:DrawGrid>
  <InteractiveWidth>8.5in</InteractiveWidth>
  <rd:SnapToGrid>true</rd:SnapToGrid>
  <RightMargin>1in</RightMargin>
  <LeftMargin>1in</LeftMargin>
  <BottomMargin>1in</BottomMargin>
  <rd:ReportID>b3e1696e-3d96-4a0b-aaa6-4147f3f46997</rd:ReportID>
  <DataSets>
    <DataSet Name="MainData">
      <Fields>
        <Field Name="ROWID">
          <DataField>ROWID</DataField>
          <rd:TypeName>System.Int32</rd:TypeName>
        </Field>
        <Field Name="SRCECODE">
          <DataField>SRCECODE</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="AL">
          <DataField>AL</DataField>
          <rd:TypeName>System.Decimal</rd:TypeName>
        </Field>
        <Field Name="CT">
          <DataField>CT</DataField>
          <rd:TypeName>System.Decimal</rd:TypeName>
        </Field>
        <Field Name="DC">
          <DataField>DC</DataField>
          <rd:TypeName>System.Decimal</rd:TypeName>
        </Field>
        <Field Name="DE">
          <DataField>DE</DataField>
          <rd:TypeName>System.Decimal</rd:TypeName>
        </Field>
        <Field Name="FL">
          <DataField>FL</DataField>
          <rd:TypeName>System.Decimal</rd:TypeName>
        </Field>
        <Field Name="MA">
          <DataField>MA</DataField>
          <rd:TypeName>System.Decimal</rd:TypeName>
        </Field>
        <Field Name="MD">
          <DataField>MD</DataField>
          <rd:TypeName>System.Decimal</rd:TypeName>
        </Field>
        <Field Name="ME">
          <DataField>ME</DataField>
          <rd:TypeName>System.Decimal</rd:TypeName>
        </Field>
        <Field Name="MO">
          <DataField>MO</DataField>
          <rd:TypeName>System.Decimal</rd:TypeName>
        </Field>
        <Field Name="NH">
          <DataField>NH</DataField>
          <rd:TypeName>System.Decimal</rd:TypeName>
        </Field>
        <Field Name="NJ">
          <DataField>NJ</DataField>
          <rd:TypeName>System.Decimal</rd:TypeName>
        </Field>
        <Field Name="NY">
          <DataField>NY</DataField>
          <rd:TypeName>System.Decimal</rd:TypeName>
        </Field>
        <Field Name="PA">
          <DataField>PA</DataField>
          <rd:TypeName>System.Decimal</rd:TypeName>
        </Field>
        <Field Name="RI">
          <DataField>RI</DataField>
          <rd:TypeName>System.Decimal</rd:TypeName>
        </Field>
        <Field Name="VA">
          <DataField>VA</DataField>
          <rd:TypeName>System.Decimal</rd:TypeName>
        </Field>
        <Field Name="VT">
          <DataField>VT</DataField>
          <rd:TypeName>System.Decimal</rd:TypeName>
        </Field>
        <Field Name="WV">
          <DataField>WV</DataField>
          <rd:TypeName>System.Decimal</rd:TypeName>
        </Field>
        <Field Name="TOTAL___REGION_1">
          <DataField>TOTAL - REGION 1</DataField>
          <rd:TypeName>System.Decimal</rd:TypeName>
        </Field>
        <Field Name="AK">
          <DataField>AK</DataField>
          <rd:TypeName>System.Decimal</rd:TypeName>
        </Field>
        <Field Name="AZ">
          <DataField>AZ</DataField>
          <rd:TypeName>System.Decimal</rd:TypeName>
        </Field>
        <Field Name="CA">
          <DataField>CA</DataField>
          <rd:TypeName>System.Decimal</rd:TypeName>
        </Field>
        <Field Name="CO">
          <DataField>CO</DataField>
          <rd:TypeName>System.Decimal</rd:TypeName>
        </Field>
        <Field Name="HI">
          <DataField>HI</DataField>
          <rd:TypeName>System.Decimal</rd:TypeName>
        </Field>
        <Field Name="ID">
          <DataField>ID</DataField>
          <rd:TypeName>System.Decimal</rd:TypeName>
        </Field>
        <Field Name="IL">
          <DataField>IL</DataField>
          <rd:TypeName>System.Decimal</rd:TypeName>
        </Field>
        <Field Name="KS">
          <DataField>KS</DataField>
          <rd:TypeName>System.Decimal</rd:TypeName>
        </Field>
        <Field Name="NM">
          <DataField>NM</DataField>
          <rd:TypeName>System.Decimal</rd:TypeName>
        </Field>
        <Field Name="NV">
          <DataField>NV</DataField>
          <rd:TypeName>System.Decimal</rd:TypeName>
        </Field>
        <Field Name="OH">
          <DataField>OH</DataField>
          <rd:TypeName>System.Decimal</rd:TypeName>
        </Field>
        <Field Name="OK">
          <DataField>OK</DataField>
          <rd:TypeName>System.Decimal</rd:TypeName>
        </Field>
        <Field Name="OR">
          <DataField>OR</DataField>
          <rd:TypeName>System.Decimal</rd:TypeName>
        </Field>
        <Field Name="UT">
          <DataField>UT</DataField>
          <rd:TypeName>System.Decimal</rd:TypeName>
        </Field>
        <Field Name="WA">
          <DataField>WA</DataField>
          <rd:TypeName>System.Decimal</rd:TypeName>
        </Field>
        <Field Name="WY">
          <DataField>WY</DataField>
          <rd:TypeName>System.Decimal</rd:TypeName>
        </Field>
        <Field Name="TOTAL___REGION_2">
          <DataField>TOTAL - REGION 2</DataField>
          <rd:TypeName>System.Decimal</rd:TypeName>
        </Field>
        <Field Name="AR">
          <DataField>AR</DataField>
          <rd:TypeName>System.Decimal</rd:TypeName>
        </Field>
        <Field Name="GA">
          <DataField>GA</DataField>
          <rd:TypeName>System.Decimal</rd:TypeName>
        </Field>
        <Field Name="IA">
          <DataField>IA</DataField>
          <rd:TypeName>System.Decimal</rd:TypeName>
        </Field>
        <Field Name="IN">
          <DataField>IN</DataField>
          <rd:TypeName>System.Decimal</rd:TypeName>
        </Field>
        <Field Name="KY">
          <DataField>KY</DataField>
          <rd:TypeName>System.Decimal</rd:TypeName>
        </Field>
        <Field Name="LA">
          <DataField>LA</DataField>
          <rd:TypeName>System.Decimal</rd:TypeName>
        </Field>
        <Field Name="MI">
          <DataField>MI</DataField>
          <rd:TypeName>System.Decimal</rd:TypeName>
        </Field>
        <Field Name="MN">
          <DataField>MN</DataField>
          <rd:TypeName>System.Decimal</rd:TypeName>
        </Field>
        <Field Name="MS">
          <DataField>MS</DataField>
          <rd:TypeName>System.Decimal</rd:TypeName>
        </Field>
        <Field Name="MT">
          <DataField>MT</DataField>
          <rd:TypeName>System.Decimal</rd:TypeName>
        </Field>
        <Field Name="NC">
          <DataField>NC</DataField>
          <rd:TypeName>System.Decimal</rd:TypeName>
        </Field>
        <Field Name="ND">
          <DataField>ND</DataField>
          <rd:TypeName>System.Decimal</rd:TypeName>
        </Field>
        <Field Name="NE">
          <DataField>NE</DataField>
          <rd:TypeName>System.Decimal</rd:TypeName>
        </Field>
        <Field Name="SC">
          <DataField>SC</DataField>
          <rd:TypeName>System.Decimal</rd:TypeName>
        </Field>
        <Field Name="SD">
          <DataField>SD</DataField>
          <rd:TypeName>System.Decimal</rd:TypeName>
        </Field>
        <Field Name="TN">
          <DataField>TN</DataField>
          <rd:TypeName>System.Decimal</rd:TypeName>
        </Field>
        <Field Name="WI">
          <DataField>WI</DataField>
          <rd:TypeName>System.Decimal</rd:TypeName>
        </Field>
        <Field Name="TOTAL___REGION_3">
          <DataField>TOTAL - REGION 3</DataField>
          <rd:TypeName>System.Decimal</rd:TypeName>
        </Field>
        <Field Name="TX">
          <DataField>TX</DataField>
          <rd:TypeName>System.Decimal</rd:TypeName>
        </Field>
        <Field Name="TOTAL___REGION_4">
          <DataField>TOTAL - REGION 4</DataField>
          <rd:TypeName>System.Decimal</rd:TypeName>
        </Field>
      </Fields>
      <Query>
        <DataSourceName>Dexter</DataSourceName>
        <CommandText>--===== PREP ENVIRONMENT
SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED
IF OBJECT_ID('TEMPDB..#REGION') IS NOT NULL BEGIN DROP TABLE #REGION END
IF OBJECT_ID('TEMPDB..#REGIONS') IS NOT NULL BEGIN DROP TABLE #REGIONS END
IF OBJECT_ID('TEMPDB..#DONORS') IS NOT NULL BEGIN DROP TABLE #DONORS END
IF OBJECT_ID('TEMPDB..#DATES') IS NOT NULL BEGIN DROP TABLE #DATES END
IF OBJECT_ID('TEMPDB..#CALCULATOR') IS NOT NULL BEGIN DROP TABLE #CALCULATOR END
IF OBJECT_ID('TEMPDB..#OUTPUT') IS NOT NULL BEGIN DROP TABLE #OUTPUT END
IF OBJECT_ID('TEMPDB..#COLUMNS') IS NOT NULL BEGIN DROP TABLE #COLUMNS END
IF OBJECT_ID('TEMPDB..#SUB_TOTALS') IS NOT NULL BEGIN DROP TABLE #SUB_TOTALS END

--===== GET REGIONS
SELECT
      IDENTITY(INT,1,1) ROWID
      , CAST(('REGION ' + L.REGION) AS VARCHAR(35)) REGION 
      , CAST( L.STATE AS VARCHAR(35)) STATE
INTO #REGION
FROM lkREGION L 
WHERE 1 = 1 
ORDER BY CAST(L.REGION AS INTEGER) ASC

-- INSERT SUBTOTALS
SET IDENTITY_INSERT #REGION ON
INSERT INTO #REGION (ROWID,REGION,STATE) SELECT MAX(ROWID), 'TOTAL - ' + REGION,'TOTAL - ' + REGION FROM #REGION GROUP BY REGION
SET IDENTITY_INSERT #REGION OFF

-- SELECT MAX(ROWID), 'TOTAL - ' + REGION AS [REGION],'TOTAL - ' + REGION AS [STATE] INTO #TEMP FROM #REGION GROUP BY REGION
-- INSERT INTO #REGION
-- SELECT [REGION],[STATE] FROM #TEMP



-- RE-SORT FOR PROPER #COLUMNS CREATION
SELECT
      IDENTITY(INT,1,1) SEQID
      , REGION
      , STATE
INTO #REGIONS 
FROM #REGION 
ORDER BY #REGION.ROWID,REGION,STATE

-- SELECT * FROM #REGIONS

--===== GET DONOR POOL BASED ON REGION
SELECT SD.PID 
INTO #DONORS 
FROM SUMMARYD SD, ADDRESS A , #REGIONS R
WHERE SD.PID = A.PID AND A.PRIME = 1 AND A.STATE = R.STATE
CREATE INDEX IDX_DONORS_PID ON #DONORS (PID)

--===== GET BATCHDTEs FROM USER
SELECT L.DATEVALUE BATCHDTE
INTO #DATES
FROM lkDATES L 
WHERE L.DATEVALUE &gt;= '01/01/1990' AND L.DATEVALUE &lt;= CONVERT(VARCHAR,GETDATE(),101) 
-- AND &lt;&lt;[L.DATEVALUE&gt;Gift Date^D@*!99/99/0000;1; ]&gt;&gt;
AND L.DATEVALUE &gt;= '1/1/2011'
CREATE INDEX IDX_DATES_BATCHDTE ON #DATES (BATCHDTE)

--===== BUILD CALCULATION TABLE
SELECT 
        IDENTITY(INT,1,1) ROWID
      , PR.PROGID
      , PR.DESCRIP
      , S.SRCEID
      , S.SRCECODE
      , M.PID
      , M.AMT
      , A.STATE
        , R.REGION
INTO #CALCULATOR
FROM 
        PROGRAM PR
      , PACKAGE PK
      , SOURCE S
      , MONY M
      , ADDRESS A
      , DMFUND DM
      , #DONORS D
      , #DATES DA
      , #REGIONS R
WHERE 
      M.SRCEID = S.SRCEID AND
      S.PKGEID = PK.PKGEID AND
      PK.PROGID = PR.PROGID AND
      M.PID = A.PID AND
      M.PID = D.PID AND
      M.BATCHDTE = DA.BATCHDTE AND
      A.STATE = R.STATE AND
      M.FUNDID = DM.FUNDID AND
     M.COUNTER = 1 AND
      M.SOFTMONEY = 0 AND
      A.PRIME = 1 AND
      -- R.REGION LIKE 'REGION%' AND DM.FUNDCODE = 'R07'
      PR.PROGID = 32
CREATE INDEX IDX_CALCULATOR_STATE ON #CALCULATOR (STATE)
CREATE INDEX IDX_CALCULATOR_PROGID ON #CALCULATOR (PROGID)

--===== BUILD &amp; POPULATE OUTPUT INITIAL COLUMNS
SELECT 
    IDENTITY(INT,1,1) ROWID
      , SRCECODE
      , CAST(NULL AS VARCHAR) AS DONE
INTO #OUTPUT
FROM #CALCULATOR
GROUP BY SRCECODE
ORDER BY SRCECODE

--===== ADD COLUMNS TO OUTPUT TABLE AND CALCULATE
SELECT *    
INTO #COLUMNS
FROM #REGIONS
CREATE INDEX IDX_REGIONS_STATE ON #REGION (STATE)

--SELECT * FROM #CALCULATOR
--SELECT * FROM #COLUMNS
--SELECT * FROM #OUTPUT

--===== CALCULATE GROUP TOTALS
SELECT REGION, SRCECODE, SUM(AMT) AS SUBTOTAL_AMT
      INTO #SUB_TOTALS
      FROM #CALCULATOR
      GROUP BY REGION, SRCECODE

-------------------------------------------------------------------------------------------------------------------------------------------------

DECLARE @nHOLDTHIS_ROW INTEGER
DECLARE @nSTOP_ROW INTEGER

DECLARE @nHOLDTHIS_COLUMN INTEGER
DECLARE @nSTOP_COLUMN INTEGER
DECLARE @SQL VARCHAR(8000)
DECLARE @sSTATE VARCHAR(35)
DECLARE @sCOLUMN VARCHAR(50)
DECLARE @sSRCECODE VARCHAR(35)
DECLARE @sREGION VARCHAR(35)
DECLARE @sOLDREGION VARCHAR(35)
DECLARE @sCHKTOTAL VARCHAR(5)

SELECT @nSTOP_ROW = COUNT(*) FROM #OUTPUT
SELECT @nSTOP_COLUMN = COUNT(*) FROM #COLUMNS

SET @nHOLDTHIS_ROW = 1
WHILE @nHOLDTHIS_ROW &lt;= @nSTOP_ROW
BEGIN
      SET @nHOLDTHIS_COLUMN = 1       

      SELECT @sSRCECODE = O.SRCECODE FROM #OUTPUT O WHERE O.ROWID = @nHOLDTHIS_ROW

      WHILE @nHOLDTHIS_COLUMN &lt;= @nSTOP_COLUMN
      BEGIN
                  /*
                  IF ISNULL(@sOLDREGION,0)=0
                  BEGIN
                        SELECT @sOLDREGION=C.REGION FROM #COLUMNS C WHERE C.SEQID = @nHOLDTHIS_COLUMN
                  END
                  */

            SELECT @sREGION=C.REGION, @sSTATE = C.STATE, @sCOLUMN = QUOTENAME(C.STATE) FROM #COLUMNS C WHERE C.SEQID = @nHOLDTHIS_COLUMN
      
            IF @nHOLDTHIS_ROW = 1
            BEGIN
                  -- ADD COLUMN
                  SELECT @SQL = 'ALTER TABLE #OUTPUT ADD ' + @sCOLUMN + ' MONEY ' FROM #COLUMNS C WHERE C.SEQID = @nHOLDTHIS_COLUMN
                  EXEC(@SQL)
            END
   
            -- POPULATE COLUMN FOR LINE ITEM
            SELECT @SQL = 'UPDATE T SET ' + @sCOLUMN + ' = (SELECT SUM(C.AMT) FROM #CALCULATOR C WHERE C.STATE = ' + '''' + @sSTATE + '''' + ' AND C.SRCECODE = ' + '''' + @sSRCECODE + '''' + ') FROM #OUTPUT T WHERE T.SRCECODE = ' + '''' + @sSRCECODE + '''' + ''   
            EXEC(@SQL)

                  SET @sCHKTOTAL = ( SELECT SUBSTRING(@sCOLUMN,2,5) )
                  -- TAKE CARE OF SUB-TOTALS
                  IF @sCHKTOTAL  = 'TOTAL' 
                  BEGIN
                  SELECT @SQL = 'UPDATE T SET ' + @sCOLUMN + ' = ( SELECT SUBTOTAL_AMT FROM #SUB_TOTALS S WHERE S.REGION = ' + '''' + @sOLDREGION + '''' + ' AND S.SRCECODE = ' + '''' + @sSRCECODE + '''' + ') FROM #OUTPUT T WHERE T.SRCECODE = ' + '''' + @sSRCECODE + '''' + ''   
                    EXEC(@SQL)
                  END         

                 SELECT @sOLDREGION=C.REGION FROM #COLUMNS C WHERE C.SEQID = @nHOLDTHIS_COLUMN

            SET @nHOLDTHIS_COLUMN = @nHOLDTHIS_COLUMN + 1            
      END

      UPDATE O SET DONE = 'Y' FROM #OUTPUT O WHERE O.SRCECODE = @sSRCECODE 
      SET @nHOLDTHIS_ROW = @nHOLDTHIS_ROW + 1
END

SELECT * FROM #OUTPUT

--===== CLEAN UP PLEASE ...
SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED
IF OBJECT_ID('TEMPDB..#REGION') IS NOT NULL BEGIN DROP TABLE #REGION END
IF OBJECT_ID('TEMPDB..#REGIONS') IS NOT NULL BEGIN DROP TABLE #REGIONS END
IF OBJECT_ID('TEMPDB..#DONORS') IS NOT NULL BEGIN DROP TABLE #DONORS END
IF OBJECT_ID('TEMPDB..#DATES') IS NOT NULL BEGIN DROP TABLE #DATES END
IF OBJECT_ID('TEMPDB..#CALCULATOR') IS NOT NULL BEGIN DROP TABLE #CALCULATOR END
IF OBJECT_ID('TEMPDB..#OUTPUT') IS NOT NULL BEGIN DROP TABLE #OUTPUT END
IF OBJECT_ID('TEMPDB..#COLUMNS') IS NOT NULL BEGIN DROP TABLE #COLUMNS END
IF OBJECT_ID('TEMPDB..#SUB_TOTALS') IS NOT NULL BEGIN DROP TABLE #SUB_TOTALS END</CommandText>
        <rd:UseGenericDesigner>true</rd:UseGenericDesigner>
      </Query>
    </DataSet>
  </DataSets>
  <Width>57.375in</Width>
  <Body>
    <ReportItems>
      <Textbox Name="textbox1">
        <rd:DefaultName>textbox1</rd:DefaultName>
        <Width>57in</Width>
        <Style>
          <Color>SteelBlue</Color>
          <FontSize>14pt</FontSize>
          <FontWeight>700</FontWeight>
          <PaddingLeft>2pt</PaddingLeft>
          <PaddingRight>2pt</PaddingRight>
          <PaddingTop>2pt</PaddingTop>
          <PaddingBottom>2pt</PaddingBottom>
        </Style>
        <ZIndex>1</ZIndex>
        <CanGrow>true</CanGrow>
        <Height>0.36in</Height>
        <Value>Source Code Totals By Region Report</Value>
      </Textbox>
      <Table Name="table1">
        <DataSetName>MainData</DataSetName>
        <Top>0.36in</Top>
        <Width>57.25in</Width>
        <Details>
          <TableRows>
            <TableRow>
              <TableCells>
                <TableCell>
                  <ReportItems>
                    <Textbox Name="ROWID">
                      <rd:DefaultName>ROWID</rd:DefaultName>
                      <Style>
                        <BorderColor>
                          <Default>LightGrey</Default>
                        </BorderColor>
                        <BorderStyle>
                          <Default>Solid</Default>
                        </BorderStyle>
                        <FontFamily>Tahoma</FontFamily>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                      <ZIndex>56</ZIndex>
                      <CanGrow>true</CanGrow>
                      <Value>=Fields!ROWID.Value</Value>
                    </Textbox>
                  </ReportItems>
                </TableCell>
                <TableCell>
                  <ReportItems>
                    <Textbox Name="SRCECODE">
                      <rd:DefaultName>SRCECODE</rd:DefaultName>
                      <Style>
                        <BorderColor>
                          <Default>LightGrey</Default>
                        </BorderColor>
                        <BorderStyle>
                          <Default>Solid</Default>
                        </BorderStyle>
                        <FontFamily>Tahoma</FontFamily>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                      <ZIndex>55</ZIndex>
                      <CanGrow>true</CanGrow>
                      <Value>=Fields!SRCECODE.Value</Value>
                    </Textbox>
                  </ReportItems>
                </TableCell>
                <TableCell>
                  <ReportItems>
                    <Textbox Name="AL">
                      <rd:DefaultName>AL</rd:DefaultName>
                      <Style>
                        <BorderColor>
                          <Default>LightGrey</Default>
                        </BorderColor>
                        <BorderStyle>
                          <Default>Solid</Default>
                        </BorderStyle>
                        <FontFamily>Tahoma</FontFamily>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                        <Direction>RTL</Direction>
                      </Style>
                      <ZIndex>54</ZIndex>
                      <CanGrow>true</CanGrow>
                      <Value>=IIF(Fields!AL.Value&gt;0, FormatCurrency(ROUND(Fields!AL.Value,2)),"")</Value>
                    </Textbox>
                  </ReportItems>
                </TableCell>
                <TableCell>
                  <ReportItems>
                    <Textbox Name="CT">
                      <rd:DefaultName>CT</rd:DefaultName>
                      <Style>
                        <BorderColor>
                          <Default>LightGrey</Default>
                        </BorderColor>
                        <BorderStyle>
                          <Default>Solid</Default>
                        </BorderStyle>
                        <FontFamily>Tahoma</FontFamily>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                        <Direction>RTL</Direction>
                      </Style>
                      <ZIndex>53</ZIndex>
                      <CanGrow>true</CanGrow>
                      <Value>=IIF(Fields!CT.Value&gt;0, FormatCurrency(ROUND(Fields!CT.Value,2)),"")</Value>
                    </Textbox>
                  </ReportItems>
                </TableCell>
                <TableCell>
                  <ReportItems>
                    <Textbox Name="DC">
                      <rd:DefaultName>DC</rd:DefaultName>
                      <Style>
                        <BorderColor>
                          <Default>LightGrey</Default>
                        </BorderColor>
                        <BorderStyle>
                          <Default>Solid</Default>
                        </BorderStyle>
                        <FontFamily>Tahoma</FontFamily>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                        <Direction>RTL</Direction>
                      </Style>
                      <ZIndex>52</ZIndex>
                      <CanGrow>true</CanGrow>
                      <Value>=IIF(Fields!DC.Value&gt;0, FormatCurrency(ROUND(Fields!DC.Value,2)),"")</Value>
                    </Textbox>
                  </ReportItems>
                </TableCell>
                <TableCell>
                  <ReportItems>
                    <Textbox Name="DE">
                      <rd:DefaultName>DE</rd:DefaultName>
                      <Style>
                        <BorderColor>
                          <Default>LightGrey</Default>
                        </BorderColor>
                        <BorderStyle>
                          <Default>Solid</Default>
                        </BorderStyle>
                        <FontFamily>Tahoma</FontFamily>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                        <Direction>RTL</Direction>
                      </Style>
                      <ZIndex>51</ZIndex>
                      <CanGrow>true</CanGrow>
                      <Value>=IIF(Fields!DE.Value&gt;0, FormatCurrency(ROUND(Fields!DE.Value,2)),"")</Value>
                    </Textbox>
                  </ReportItems>
                </TableCell>
                <TableCell>
                  <ReportItems>
                    <Textbox Name="FL">
                      <rd:DefaultName>FL</rd:DefaultName>
                      <Style>
                        <BorderColor>
                          <Default>LightGrey</Default>
                        </BorderColor>
                        <BorderStyle>
                          <Default>Solid</Default>
                        </BorderStyle>
                        <FontFamily>Tahoma</FontFamily>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                        <Direction>RTL</Direction>
                      </Style>
                      <ZIndex>50</ZIndex>
                      <CanGrow>true</CanGrow>
                      <Value>=IIF(Fields!FL.Value&gt;0, FormatCurrency(ROUND(Fields!FL.Value,2)),"")</Value>
                    </Textbox>
                  </ReportItems>
                </TableCell>
                <TableCell>
                  <ReportItems>
                    <Textbox Name="MA">
                      <rd:DefaultName>MA</rd:DefaultName>
                      <Style>
                        <BorderColor>
                          <Default>LightGrey</Default>
                        </BorderColor>
                        <BorderStyle>
                          <Default>Solid</Default>
                        </BorderStyle>
                        <FontFamily>Tahoma</FontFamily>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                        <Direction>RTL</Direction>
                      </Style>
                      <ZIndex>49</ZIndex>
                      <CanGrow>true</CanGrow>
                      <Value>=IIF(Fields!MA.Value&gt;0, FormatCurrency(ROUND(Fields!MA.Value,2)),"")</Value>
                    </Textbox>
                  </ReportItems>
                </TableCell>
                <TableCell>
                  <ReportItems>
                    <Textbox Name="MD">
                      <rd:DefaultName>MD</rd:DefaultName>
                      <Style>
                        <BorderColor>
                          <Default>LightGrey</Default>
                        </BorderColor>
                        <BorderStyle>
                          <Default>Solid</Default>
                        </BorderStyle>
                        <FontFamily>Tahoma</FontFamily>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                        <Direction>RTL</Direction>
                      </Style>
                      <ZIndex>48</ZIndex>
                      <CanGrow>true</CanGrow>
                      <Value>=IIF(Fields!MD.Value&gt;0, FormatCurrency(ROUND(Fields!MD.Value,2)),"")</Value>
                    </Textbox>
                  </ReportItems>
                </TableCell>
                <TableCell>
                  <ReportItems>
                    <Textbox Name="ME">
                      <rd:DefaultName>ME</rd:DefaultName>
                      <Style>
                        <BorderColor>
                          <Default>LightGrey</Default>
                        </BorderColor>
                        <BorderStyle>
                          <Default>Solid</Default>
                        </BorderStyle>
                        <FontFamily>Tahoma</FontFamily>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                        <Direction>RTL</Direction>
                      </Style>
                      <ZIndex>47</ZIndex>
                      <CanGrow>true</CanGrow>
                      <Value>=IIF(Fields!ME.Value&gt;0, FormatCurrency(ROUND(Fields!ME.Value,2)),"")</Value>
                    </Textbox>
                  </ReportItems>
                </TableCell>
                <TableCell>
                  <ReportItems>
                    <Textbox Name="MO">
                      <rd:DefaultName>MO</rd:DefaultName>
                      <Style>
                        <BorderColor>
                          <Default>LightGrey</Default>
                        </BorderColor>
                        <BorderStyle>
                          <Default>Solid</Default>
                        </BorderStyle>
                        <FontFamily>Tahoma</FontFamily>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                        <Direction>RTL</Direction>
                      </Style>
                      <ZIndex>46</ZIndex>
                      <CanGrow>true</CanGrow>
                      <Value>=IIF(Fields!MO.Value&gt;0, FormatCurrency(ROUND(Fields!MO.Value,2)),"")</Value>
                    </Textbox>
                  </ReportItems>
                </TableCell>
                <TableCell>
                  <ReportItems>
                    <Textbox Name="NH">
                      <rd:DefaultName>NH</rd:DefaultName>
                      <Style>
                        <BorderColor>
                          <Default>LightGrey</Default>
                        </BorderColor>
                        <BorderStyle>
                          <Default>Solid</Default>
                        </BorderStyle>
                        <FontFamily>Tahoma</FontFamily>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                        <Direction>RTL</Direction>
                      </Style>
                      <ZIndex>45</ZIndex>
                      <CanGrow>true</CanGrow>
                      <Value>=IIF(Fields!NH.Value&gt;0, FormatCurrency(ROUND(Fields!NH.Value,2)),"")</Value>
                    </Textbox>
                  </ReportItems>
                </TableCell>
                <TableCell>
                  <ReportItems>
                    <Textbox Name="NJ">
                      <rd:DefaultName>NJ</rd:DefaultName>
                      <Style>
                        <BorderColor>
                          <Default>LightGrey</Default>
                        </BorderColor>
                        <BorderStyle>
                          <Default>Solid</Default>
                        </BorderStyle>
                        <FontFamily>Tahoma</FontFamily>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                        <Direction>RTL</Direction>
                      </Style>
                      <ZIndex>44</ZIndex>
                      <CanGrow>true</CanGrow>
                      <Value>=IIF(Fields!NJ.Value&gt;0, FormatCurrency(ROUND(Fields!NJ.Value,2)),"")</Value>
                    </Textbox>
                  </ReportItems>
                </TableCell>
                <TableCell>
                  <ReportItems>
                    <Textbox Name="NY">
                      <rd:DefaultName>NY</rd:DefaultName>
                      <Style>
                        <BorderColor>
                          <Default>LightGrey</Default>
                        </BorderColor>
                        <BorderStyle>
                          <Default>Solid</Default>
                        </BorderStyle>
                        <FontFamily>Tahoma</FontFamily>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                        <Direction>RTL</Direction>
                      </Style>
                      <ZIndex>43</ZIndex>
                      <CanGrow>true</CanGrow>
                      <Value>=IIF(Fields!NY.Value&gt;0, FormatCurrency(ROUND(Fields!NY.Value,2)),"")</Value>
                    </Textbox>
                  </ReportItems>
                </TableCell>
                <TableCell>
                  <ReportItems>
                    <Textbox Name="PA">
                      <rd:DefaultName>PA</rd:DefaultName>
                      <Style>
                        <BorderColor>
                          <Default>LightGrey</Default>
                        </BorderColor>
                        <BorderStyle>
                          <Default>Solid</Default>
                        </BorderStyle>
                        <FontFamily>Tahoma</FontFamily>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                        <Direction>RTL</Direction>
                      </Style>
                      <ZIndex>42</ZIndex>
                      <CanGrow>true</CanGrow>
                      <Value>=IIF(Fields!PA.Value&gt;0, FormatCurrency(ROUND(Fields!PA.Value,2)),"")</Value>
                    </Textbox>
                  </ReportItems>
                </TableCell>
                <TableCell>
                  <ReportItems>
                    <Textbox Name="RI">
                      <rd:DefaultName>RI</rd:DefaultName>
                      <Style>
                        <BorderColor>
                          <Default>LightGrey</Default>
                        </BorderColor>
                        <BorderStyle>
                          <Default>Solid</Default>
                        </BorderStyle>
                        <FontFamily>Tahoma</FontFamily>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                        <Direction>RTL</Direction>
                      </Style>
                      <ZIndex>41</ZIndex>
                      <CanGrow>true</CanGrow>
                      <Value>=IIF(Fields!RI.Value&gt;0, FormatCurrency(ROUND(Fields!RI.Value,2)),"")</Value>
                    </Textbox>
                  </ReportItems>
                </TableCell>
                <TableCell>
                  <ReportItems>
                    <Textbox Name="VA">
                      <rd:DefaultName>VA</rd:DefaultName>
                      <Style>
                        <BorderColor>
                          <Default>LightGrey</Default>
                        </BorderColor>
                        <BorderStyle>
                          <Default>Solid</Default>
                        </BorderStyle>
                        <FontFamily>Tahoma</FontFamily>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                        <Direction>RTL</Direction>
                      </Style>
                      <ZIndex>40</ZIndex>
                      <CanGrow>true</CanGrow>
                      <Value>=IIF(Fields!VA.Value&gt;0, FormatCurrency(ROUND(Fields!VA.Value,2)),"")</Value>
                    </Textbox>
                  </ReportItems>
                </TableCell>
                <TableCell>
                  <ReportItems>
                    <Textbox Name="VT">
                      <rd:DefaultName>VT</rd:DefaultName>
                      <Style>
                        <BorderColor>
                          <Default>LightGrey</Default>
                        </BorderColor>
                        <BorderStyle>
                          <Default>Solid</Default>
                        </BorderStyle>
                        <FontFamily>Tahoma</FontFamily>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                        <Direction>RTL</Direction>
                      </Style>
                      <ZIndex>39</ZIndex>
                      <CanGrow>true</CanGrow>
                      <Value>=IIF(Fields!VT.Value&gt;0, FormatCurrency(ROUND(Fields!VT.Value,2)),"")</Value>
                    </Textbox>
                  </ReportItems>
                </TableCell>
                <TableCell>
                  <ReportItems>
                    <Textbox Name="WV">
                      <rd:DefaultName>WV</rd:DefaultName>
                      <Style>
                        <BorderColor>
                          <Default>LightGrey</Default>
                        </BorderColor>
                        <BorderStyle>
                          <Default>Solid</Default>
                        </BorderStyle>
                        <FontFamily>Tahoma</FontFamily>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                        <Direction>RTL</Direction>
                      </Style>
                      <ZIndex>38</ZIndex>
                      <CanGrow>true</CanGrow>
                      <Value>=IIF(Fields!WV.Value&gt;0, FormatCurrency(ROUND(Fields!WV.Value,2)),"")</Value>
                    </Textbox>
                  </ReportItems>
                </TableCell>
                <TableCell>
                  <ReportItems>
                    <Textbox Name="TOTAL___REGION_1">
                      <rd:DefaultName>TOTAL___REGION_1</rd:DefaultName>
                      <Style>
                        <BorderColor>
                          <Default>LightGrey</Default>
                        </BorderColor>
                        <BorderStyle>
                          <Default>Solid</Default>
                        </BorderStyle>
                        <FontFamily>Tahoma</FontFamily>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                        <Direction>RTL</Direction>
                      </Style>
                      <ZIndex>37</ZIndex>
                      <CanGrow>true</CanGrow>
                      <Value>=IIF(Fields!TOTAL___REGION_1.Value&gt;0, FormatCurrency(ROUND(Fields!TOTAL___REGION_1.Value,2)),"")</Value>
                    </Textbox>
                  </ReportItems>
                </TableCell>
                <TableCell>
                  <ReportItems>
                    <Textbox Name="AK">
                      <rd:DefaultName>AK</rd:DefaultName>
                      <Style>
                        <BorderColor>
                          <Default>LightGrey</Default>
                        </BorderColor>
                        <BorderStyle>
                          <Default>Solid</Default>
                        </BorderStyle>
                        <FontFamily>Tahoma</FontFamily>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                        <Direction>RTL</Direction>
                      </Style>
                      <ZIndex>36</ZIndex>
                      <CanGrow>true</CanGrow>
                      <Value>=IIF(Fields!AK.Value&gt;0, FormatCurrency(ROUND(Fields!AK.Value,2)),"")</Value>
                    </Textbox>
                  </ReportItems>
                </TableCell>
                <TableCell>
                  <ReportItems>
                    <Textbox Name="AZ">
                      <rd:DefaultName>AZ</rd:DefaultName>
                      <Style>
                        <BorderColor>
                          <Default>LightGrey</Default>
                        </BorderColor>
                        <BorderStyle>
                          <Default>Solid</Default>
                        </BorderStyle>
                        <FontFamily>Tahoma</FontFamily>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                        <Direction>RTL</Direction>
                      </Style>
                      <ZIndex>35</ZIndex>
                      <CanGrow>true</CanGrow>
                      <Value>=IIF(Fields!AZ.Value&gt;0, FormatCurrency(ROUND(Fields!AZ.Value,2)),"")</Value>
                    </Textbox>
                  </ReportItems>
                </TableCell>
                <TableCell>
                  <ReportItems>
                    <Textbox Name="CA">
                      <rd:DefaultName>CA</rd:DefaultName>
                      <Style>
                        <BorderColor>
                          <Default>LightGrey</Default>
                        </BorderColor>
                        <BorderStyle>
                          <Default>Solid</Default>
                        </BorderStyle>
                        <FontFamily>Tahoma</FontFamily>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                        <Direction>RTL</Direction>
                      </Style>
                      <ZIndex>34</ZIndex>
                      <CanGrow>true</CanGrow>
                      <Value>=IIF(Fields!CA.Value&gt;0, FormatCurrency(ROUND(Fields!CA.Value,2)),"")</Value>
                    </Textbox>
                  </ReportItems>
                </TableCell>
                <TableCell>
                  <ReportItems>
                    <Textbox Name="CO">
                      <rd:DefaultName>CO</rd:DefaultName>
                      <Style>
                        <BorderColor>
                          <Default>LightGrey</Default>
                        </BorderColor>
                        <BorderStyle>
                          <Default>Solid</Default>
                        </BorderStyle>
                        <FontFamily>Tahoma</FontFamily>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                        <Direction>RTL</Direction>
                      </Style>
                      <ZIndex>33</ZIndex>
                      <CanGrow>true</CanGrow>
                      <Value>=IIF(Fields!CO.Value&gt;0, FormatCurrency(ROUND(Fields!CO.Value,2)),"")</Value>
                    </Textbox>
                  </ReportItems>
                </TableCell>
                <TableCell>
                  <ReportItems>
                    <Textbox Name="HI">
                      <rd:DefaultName>HI</rd:DefaultName>
                      <Style>
                        <BorderColor>
                          <Default>LightGrey</Default>
                        </BorderColor>
                        <BorderStyle>
                          <Default>Solid</Default>
                        </BorderStyle>
                        <FontFamily>Tahoma</FontFamily>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                        <Direction>RTL</Direction>
                      </Style>
                      <ZIndex>32</ZIndex>
                      <CanGrow>true</CanGrow>
                      <Value>=IIF(Fields!HI.Value&gt;0, FormatCurrency(ROUND(Fields!HI.Value,2)),"")</Value>
                    </Textbox>
                  </ReportItems>
                </TableCell>
                <TableCell>
                  <ReportItems>
                    <Textbox Name="ID">
                      <rd:DefaultName>ID</rd:DefaultName>
                      <Style>
                        <BorderColor>
                          <Default>LightGrey</Default>
                        </BorderColor>
                        <BorderStyle>
                          <Default>Solid</Default>
                        </BorderStyle>
                        <FontFamily>Tahoma</FontFamily>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                        <Direction>RTL</Direction>
                      </Style>
                      <ZIndex>31</ZIndex>
                      <CanGrow>true</CanGrow>
                      <Value>=IIF(Fields!ID.Value&gt;0, FormatCurrency(ROUND(Fields!ID.Value,2)),"")</Value>
                    </Textbox>
                  </ReportItems>
                </TableCell>
                <TableCell>
                  <ReportItems>
                    <Textbox Name="IL">
                      <rd:DefaultName>IL</rd:DefaultName>
                      <Style>
                        <BorderColor>
                          <Default>LightGrey</Default>
                        </BorderColor>
                        <BorderStyle>
                          <Default>Solid</Default>
                        </BorderStyle>
                        <FontFamily>Tahoma</FontFamily>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                        <Direction>RTL</Direction>
                      </Style>
                      <ZIndex>30</ZIndex>
                      <CanGrow>true</CanGrow>
                      <Value>=IIF(Fields!IL.Value&gt;0, FormatCurrency(ROUND(Fields!IL.Value,2)),"")</Value>
                    </Textbox>
                  </ReportItems>
                </TableCell>
                <TableCell>
                  <ReportItems>
                    <Textbox Name="KS">
                      <rd:DefaultName>KS</rd:DefaultName>
                      <Style>
                        <BorderColor>
                          <Default>LightGrey</Default>
                        </BorderColor>
                        <BorderStyle>
                          <Default>Solid</Default>
                        </BorderStyle>
                        <FontFamily>Tahoma</FontFamily>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                        <Direction>RTL</Direction>
                      </Style>
                      <ZIndex>29</ZIndex>
                      <CanGrow>true</CanGrow>
                      <Value>=IIF(Fields!KS.Value&gt;0, FormatCurrency(ROUND(Fields!KS.Value,2)),"")</Value>
                    </Textbox>
                  </ReportItems>
                </TableCell>
                <TableCell>
                  <ReportItems>
                    <Textbox Name="NM">
                      <rd:DefaultName>NM</rd:DefaultName>
                      <Style>
                        <BorderColor>
                          <Default>LightGrey</Default>
                        </BorderColor>
                        <BorderStyle>
                          <Default>Solid</Default>
                        </BorderStyle>
                        <FontFamily>Tahoma</FontFamily>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                        <Direction>RTL</Direction>
                      </Style>
                      <ZIndex>28</ZIndex>
                      <CanGrow>true</CanGrow>
                      <Value>=IIF(Fields!NM.Value&gt;0, FormatCurrency(ROUND(Fields!NM.Value,2)),"")</Value>
                    </Textbox>
                  </ReportItems>
                </TableCell>
                <TableCell>
                  <ReportItems>
                    <Textbox Name="NV">
                      <rd:DefaultName>NV</rd:DefaultName>
                      <Style>
                        <BorderColor>
                          <Default>LightGrey</Default>
                        </BorderColor>
                        <BorderStyle>
                          <Default>Solid</Default>
                        </BorderStyle>
                        <FontFamily>Tahoma</FontFamily>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                        <Direction>RTL</Direction>
                      </Style>
                      <ZIndex>27</ZIndex>
                      <CanGrow>true</CanGrow>
                      <Value>=IIF(Fields!NV.Value&gt;0, FormatCurrency(ROUND(Fields!NV.Value,2)),"")</Value>
                    </Textbox>
                  </ReportItems>
                </TableCell>
                <TableCell>
                  <ReportItems>
                    <Textbox Name="OH">
                      <rd:DefaultName>OH</rd:DefaultName>
                      <Style>
                        <BorderColor>
                          <Default>LightGrey</Default>
                        </BorderColor>
                        <BorderStyle>
                          <Default>Solid</Default>
                        </BorderStyle>
                        <FontFamily>Tahoma</FontFamily>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                        <Direction>RTL</Direction>
                      </Style>
                      <ZIndex>26</ZIndex>
                      <CanGrow>true</CanGrow>
                      <Value>=IIF(Fields!OH.Value&gt;0, FormatCurrency(ROUND(Fields!OH.Value,2)),"")</Value>
                    </Textbox>
                  </ReportItems>
                </TableCell>
                <TableCell>
                  <ReportItems>
                    <Textbox Name="OK">
                      <rd:DefaultName>OK</rd:DefaultName>
                      <Style>
                        <BorderColor>
                          <Default>LightGrey</Default>
                        </BorderColor>
                        <BorderStyle>
                          <Default>Solid</Default>
                        </BorderStyle>
                        <FontFamily>Tahoma</FontFamily>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                      <ZIndex>25</ZIndex>
                      <CanGrow>true</CanGrow>
                      <Value>=IIF(Fields!OK.Value&gt;0, FormatCurrency(ROUND(Fields!OK.Value,2)),"")</Value>
                    </Textbox>
                  </ReportItems>
                </TableCell>
                <TableCell>
                  <ReportItems>
                    <Textbox Name="OR">
                      <rd:DefaultName>OR</rd:DefaultName>
                      <Style>
                        <BorderColor>
                          <Default>LightGrey</Default>
                        </BorderColor>
                        <BorderStyle>
                          <Default>Solid</Default>
                        </BorderStyle>
                        <FontFamily>Tahoma</FontFamily>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                        <Direction>RTL</Direction>
                      </Style>
                      <ZIndex>24</ZIndex>
                      <CanGrow>true</CanGrow>
                      <Value>=IIF(Fields!OR.Value&gt;0, FormatCurrency(ROUND(Fields!OR.Value,2)),"")</Value>
                    </Textbox>
                  </ReportItems>
                </TableCell>
                <TableCell>
                  <ReportItems>
                    <Textbox Name="UT">
                      <rd:DefaultName>UT</rd:DefaultName>
                      <Style>
                        <BorderColor>
                          <Default>LightGrey</Default>
                        </BorderColor>
                        <BorderStyle>
                          <Default>Solid</Default>
                        </BorderStyle>
                        <FontFamily>Tahoma</FontFamily>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                        <Direction>RTL</Direction>
                      </Style>
                      <ZIndex>23</ZIndex>
                      <CanGrow>true</CanGrow>
                      <Value>=IIF(Fields!UT.Value&gt;0, FormatCurrency(ROUND(Fields!UT.Value,2)),"")</Value>
                    </Textbox>
                  </ReportItems>
                </TableCell>
                <TableCell>
                  <ReportItems>
                    <Textbox Name="WA">
                      <rd:DefaultName>WA</rd:DefaultName>
                      <Style>
                        <BorderColor>
                          <Default>LightGrey</Default>
                        </BorderColor>
                        <BorderStyle>
                          <Default>Solid</Default>
                        </BorderStyle>
                        <FontFamily>Tahoma</FontFamily>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                        <Direction>RTL</Direction>
                      </Style>
                      <ZIndex>22</ZIndex>
                      <CanGrow>true</CanGrow>
                      <Value>=IIF(Fields!WA.Value&gt;0, FormatCurrency(ROUND(Fields!WA.Value,2)),"")</Value>
                    </Textbox>
                  </ReportItems>
                </TableCell>
                <TableCell>
                  <ReportItems>
                    <Textbox Name="WY">
                      <rd:DefaultName>WY</rd:DefaultName>
                      <Style>
                        <BorderColor>
                          <Default>LightGrey</Default>
                        </BorderColor>
                        <BorderStyle>
                          <Default>Solid</Default>
                        </BorderStyle>
                        <FontFamily>Tahoma</FontFamily>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                        <Direction>RTL</Direction>
                      </Style>
                      <ZIndex>21</ZIndex>
                      <CanGrow>true</CanGrow>
                      <Value>=IIF(Fields!WY.Value&gt;0, FormatCurrency(ROUND(Fields!WY.Value,2)),"")</Value>
                    </Textbox>
                  </ReportItems>
                </TableCell>
                <TableCell>
                  <ReportItems>
                    <Textbox Name="TOTAL___REGION_2">
                      <rd:DefaultName>TOTAL___REGION_2</rd:DefaultName>
                      <Style>
                        <BorderColor>
                          <Default>LightGrey</Default>
                        </BorderColor>
                        <BorderStyle>
                          <Default>Solid</Default>
                        </BorderStyle>
                        <FontFamily>Tahoma</FontFamily>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                        <Direction>RTL</Direction>
                      </Style>
                      <ZIndex>20</ZIndex>
                      <CanGrow>true</CanGrow>
                      <Value>=IIF(Fields!TOTAL___REGION_2.Value&gt;0, FormatCurrency(ROUND(Fields!TOTAL___REGION_2.Value,2)),"")</Value>
                    </Textbox>
                  </ReportItems>
                </TableCell>
                <TableCell>
                  <ReportItems>
                    <Textbox Name="AR">
                      <rd:DefaultName>AR</rd:DefaultName>
                      <Style>
                        <BorderColor>
                          <Default>LightGrey</Default>
                        </BorderColor>
                        <BorderStyle>
                          <Default>Solid</Default>
                        </BorderStyle>
                        <FontFamily>Tahoma</FontFamily>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                        <Direction>RTL</Direction>
                      </Style>
                      <ZIndex>19</ZIndex>
                      <CanGrow>true</CanGrow>
                      <Value>=IIF(Fields!AR.Value&gt;0, FormatCurrency(ROUND(Fields!AR.Value,2)),"")</Value>
                    </Textbox>
                  </ReportItems>
                </TableCell>
                <TableCell>
                  <ReportItems>
                    <Textbox Name="GA">
                      <rd:DefaultName>GA</rd:DefaultName>
                      <Style>
                        <BorderColor>
                          <Default>LightGrey</Default>
                        </BorderColor>
                        <BorderStyle>
                          <Default>Solid</Default>
                        </BorderStyle>
                        <FontFamily>Tahoma</FontFamily>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                        <Direction>RTL</Direction>
                      </Style>
                      <ZIndex>18</ZIndex>
                      <CanGrow>true</CanGrow>
                      <Value>=IIF(Fields!GA.Value&gt;0, FormatCurrency(ROUND(Fields!GA.Value,2)),"")</Value>
                    </Textbox>
                  </ReportItems>
                </TableCell>
                <TableCell>
                  <ReportItems>
                    <Textbox Name="IA">
                      <rd:DefaultName>IA</rd:DefaultName>
                      <Style>
                        <BorderColor>
                          <Default>LightGrey</Default>
                        </BorderColor>
                        <BorderStyle>
                          <Default>Solid</Default>
                        </BorderStyle>
                        <FontFamily>Tahoma</FontFamily>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                      <ZIndex>17</ZIndex>
                      <CanGrow>true</CanGrow>
                      <Value>=IIF(Fields!IA.Value&gt;0, FormatCurrency(ROUND(Fields!IA.Value,2)),"")</Value>
                    </Textbox>
                  </ReportItems>
                </TableCell>
                <TableCell>
                  <ReportItems>
                    <Textbox Name="IN">
                      <rd:DefaultName>IN</rd:DefaultName>
                      <Style>
                        <BorderColor>
                          <Default>LightGrey</Default>
                        </BorderColor>
                        <BorderStyle>
                          <Default>Solid</Default>
                        </BorderStyle>
                        <FontFamily>Tahoma</FontFamily>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                        <Direction>RTL</Direction>
                      </Style>
                      <ZIndex>16</ZIndex>
                      <CanGrow>true</CanGrow>
                      <Value>=IIF(Fields!IN.Value&gt;0, FormatCurrency(ROUND(Fields!IN.Value,2)),"")</Value>
                    </Textbox>
                  </ReportItems>
                </TableCell>
                <TableCell>
                  <ReportItems>
                    <Textbox Name="KY">
                      <rd:DefaultName>KY</rd:DefaultName>
                      <Style>
                        <BorderColor>
                          <Default>LightGrey</Default>
                        </BorderColor>
                        <BorderStyle>
                          <Default>Solid</Default>
                        </BorderStyle>
                        <FontFamily>Tahoma</FontFamily>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                        <Direction>RTL</Direction>
                      </Style>
                      <ZIndex>15</ZIndex>
                      <CanGrow>true</CanGrow>
                      <Value>=IIF(Fields!KY.Value&gt;0, FormatCurrency(ROUND(Fields!KY.Value,2)),"")</Value>
                    </Textbox>
                  </ReportItems>
                </TableCell>
                <TableCell>
                  <ReportItems>
                    <Textbox Name="LA">
                      <rd:DefaultName>LA</rd:DefaultName>
                      <Style>
                        <BorderColor>
                          <Default>LightGrey</Default>
                        </BorderColor>
                        <BorderStyle>
                          <Default>Solid</Default>
                        </BorderStyle>
                        <FontFamily>Tahoma</FontFamily>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                        <Direction>RTL</Direction>
                      </Style>
                      <ZIndex>14</ZIndex>
                      <CanGrow>true</CanGrow>
                      <Value>=IIF(Fields!LA.Value&gt;0, FormatCurrency(ROUND(Fields!LA.Value,2)),"")</Value>
                    </Textbox>
                  </ReportItems>
                </TableCell>
                <TableCell>
                  <ReportItems>
                    <Textbox Name="MI">
                      <rd:DefaultName>MI</rd:DefaultName>
                      <Style>
                        <BorderColor>
                          <Default>LightGrey</Default>
                        </BorderColor>
                        <BorderStyle>
                          <Default>Solid</Default>
                        </BorderStyle>
                        <FontFamily>Tahoma</FontFamily>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                        <Direction>RTL</Direction>
                      </Style>
                      <ZIndex>13</ZIndex>
                      <CanGrow>true</CanGrow>
                      <Value>=IIF(Fields!MI.Value&gt;0, FormatCurrency(ROUND(Fields!MI.Value,2)),"")</Value>
                    </Textbox>
                  </ReportItems>
                </TableCell>
                <TableCell>
                  <ReportItems>
                    <Textbox Name="MN">
                      <rd:DefaultName>MN</rd:DefaultName>
                      <Style>
                        <BorderColor>
                          <Default>LightGrey</Default>
                        </BorderColor>
                        <BorderStyle>
                          <Default>Solid</Default>
                        </BorderStyle>
                        <FontFamily>Tahoma</FontFamily>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                        <Direction>RTL</Direction>
                      </Style>
                      <ZIndex>12</ZIndex>
                      <CanGrow>true</CanGrow>
                      <Value>=IIF(Fields!MN.Value&gt;0, FormatCurrency(ROUND(Fields!MN.Value,2)),"")</Value>
                    </Textbox>
                  </ReportItems>
                </TableCell>
                <TableCell>
                  <ReportItems>
                    <Textbox Name="MS">
                      <rd:DefaultName>MS</rd:DefaultName>
                      <Style>
                        <BorderColor>
                          <Default>LightGrey</Default>
                        </BorderColor>
                        <BorderStyle>
                          <Default>Solid</Default>
                        </BorderStyle>
                        <FontFamily>Tahoma</FontFamily>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                        <Direction>RTL</Direction>
                      </Style>
                      <ZIndex>11</ZIndex>
                      <CanGrow>true</CanGrow>
                      <Value>=IIF(Fields!MS.Value&gt;0, FormatCurrency(ROUND(Fields!MS.Value,2)),"")</Value>
                    </Textbox>
                  </ReportItems>
                </TableCell>
                <TableCell>
                  <ReportItems>
                    <Textbox Name="MT">
                      <rd:DefaultName>MT</rd:DefaultName>
                      <Style>
                        <BorderColor>
                          <Default>LightGrey</Default>
                        </BorderColor>
                        <BorderStyle>
                          <Default>Solid</Default>
                        </BorderStyle>
                        <FontFamily>Tahoma</FontFamily>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                      <ZIndex>10</ZIndex>
                      <CanGrow>true</CanGrow>
                      <Value>=IIF(Fields!MT.Value&gt;0, FormatCurrency(ROUND(Fields!MT.Value,2)),"")</Value>
                    </Textbox>
                  </ReportItems>
                </TableCell>
                <TableCell>
                  <ReportItems>
                    <Textbox Name="NC">
                      <rd:DefaultName>NC</rd:DefaultName>
                      <Style>
                        <BorderColor>
                          <Default>LightGrey</Default>
                        </BorderColor>
                        <BorderStyle>
                          <Default>Solid</Default>
                        </BorderStyle>
                        <FontFamily>Tahoma</FontFamily>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                        <Direction>RTL</Direction>
                      </Style>
                      <ZIndex>9</ZIndex>
                      <CanGrow>true</CanGrow>
                      <Value>=IIF(Fields!NC.Value&gt;0, FormatCurrency(ROUND(Fields!NC.Value,2)),"")</Value>
                    </Textbox>
                  </ReportItems>
                </TableCell>
                <TableCell>
                  <ReportItems>
                    <Textbox Name="ND">
                      <rd:DefaultName>ND</rd:DefaultName>
                      <Style>
                        <BorderColor>
                          <Default>LightGrey</Default>
                        </BorderColor>
                        <BorderStyle>
                          <Default>Solid</Default>
                        </BorderStyle>
                        <FontFamily>Tahoma</FontFamily>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                        <Direction>RTL</Direction>
                      </Style>
                      <ZIndex>8</ZIndex>
                      <CanGrow>true</CanGrow>
                      <Value>=IIF(Fields!ND.Value&gt;0, FormatCurrency(ROUND(Fields!ND.Value,2)),"")</Value>
                    </Textbox>
                  </ReportItems>
                </TableCell>
                <TableCell>
                  <ReportItems>
                    <Textbox Name="NE">
                      <rd:DefaultName>NE</rd:DefaultName>
                      <Style>
                        <BorderColor>
                          <Default>LightGrey</Default>
                        </BorderColor>
                        <BorderStyle>
                          <Default>Solid</Default>
                        </BorderStyle>
                        <FontFamily>Tahoma</FontFamily>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                        <Direction>RTL</Direction>
                      </Style>
                      <ZIndex>7</ZIndex>
                      <CanGrow>true</CanGrow>
                      <Value>=IIF(Fields!NE.Value&gt;0, FormatCurrency(ROUND(Fields!NE.Value,2)),"")</Value>
                    </Textbox>
                  </ReportItems>
                </TableCell>
                <TableCell>
                  <ReportItems>
                    <Textbox Name="SC">
                      <rd:DefaultName>SC</rd:DefaultName>
                      <Style>
                        <BorderColor>
                          <Default>LightGrey</Default>
                        </BorderColor>
                        <BorderStyle>
                          <Default>Solid</Default>
                        </BorderStyle>
                        <FontFamily>Tahoma</FontFamily>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                        <Direction>RTL</Direction>
                      </Style>
                      <ZIndex>6</ZIndex>
                      <CanGrow>true</CanGrow>
                      <Value>=IIF(Fields!SC.Value&gt;0, FormatCurrency(ROUND(Fields!SC.Value,2)),"")</Value>
                    </Textbox>
                  </ReportItems>
                </TableCell>
                <TableCell>
                  <ReportItems>
                    <Textbox Name="SD">
                      <rd:DefaultName>SD</rd:DefaultName>
                      <Style>
                        <BorderColor>
                          <Default>LightGrey</Default>
                        </BorderColor>
                        <BorderStyle>
                          <Default>Solid</Default>
                        </BorderStyle>
                        <FontFamily>Tahoma</FontFamily>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                        <Direction>RTL</Direction>
                      </Style>
                      <ZIndex>5</ZIndex>
                      <CanGrow>true</CanGrow>
                      <Value>=IIF(Fields!SD.Value&gt;0, FormatCurrency(ROUND(Fields!SD.Value,2)),"")</Value>
                    </Textbox>
                  </ReportItems>
                </TableCell>
                <TableCell>
                  <ReportItems>
                    <Textbox Name="TN">
                      <rd:DefaultName>TN</rd:DefaultName>
                      <Style>
                        <BorderColor>
                          <Default>LightGrey</Default>
                        </BorderColor>
                        <BorderStyle>
                          <Default>Solid</Default>
                        </BorderStyle>
                        <FontFamily>Tahoma</FontFamily>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                        <Direction>RTL</Direction>
                      </Style>
                      <ZIndex>4</ZIndex>
                      <CanGrow>true</CanGrow>
                      <Value>=IIF(Fields!TN.Value&gt;0, FormatCurrency(ROUND(Fields!TN.Value,2)),"")</Value>
                    </Textbox>
                  </ReportItems>
                </TableCell>
                <TableCell>
                  <ReportItems>
                    <Textbox Name="WI">
                      <rd:DefaultName>WI</rd:DefaultName>
                      <Style>
                        <BorderColor>
                          <Default>LightGrey</Default>
                        </BorderColor>
                        <BorderStyle>
                          <Default>Solid</Default>
                        </BorderStyle>
                        <FontFamily>Tahoma</FontFamily>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                        <Direction>RTL</Direction>
                      </Style>
                      <ZIndex>3</ZIndex>
                      <CanGrow>true</CanGrow>
                      <Value>=IIF(Fields!WI.Value&gt;0, FormatCurrency(ROUND(Fields!WI.Value,2)),"")</Value>
                    </Textbox>
                  </ReportItems>
                </TableCell>
                <TableCell>
                  <ReportItems>
                    <Textbox Name="TOTAL___REGION_3">
                      <rd:DefaultName>TOTAL___REGION_3</rd:DefaultName>
                      <Style>
                        <BorderColor>
                          <Default>LightGrey</Default>
                        </BorderColor>
                        <BorderStyle>
                          <Default>Solid</Default>
                        </BorderStyle>
                        <FontFamily>Tahoma</FontFamily>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                        <Direction>RTL</Direction>
                      </Style>
                      <ZIndex>2</ZIndex>
                      <CanGrow>true</CanGrow>
                      <Value>=IIF(Fields!TOTAL___REGION_3.Value&gt;0, FormatCurrency(ROUND(Fields!TOTAL___REGION_3.Value,2)),"")</Value>
                    </Textbox>
                  </ReportItems>
                </TableCell>
                <TableCell>
                  <ReportItems>
                    <Textbox Name="TX">
                      <rd:DefaultName>TX</rd:DefaultName>
                      <Style>
                        <BorderColor>
                          <Default>LightGrey</Default>
                        </BorderColor>
                        <BorderStyle>
                          <Default>Solid</Default>
                        </BorderStyle>
                        <FontFamily>Tahoma</FontFamily>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                        <Direction>RTL</Direction>
                      </Style>
                      <ZIndex>1</ZIndex>
                      <CanGrow>true</CanGrow>
                      <Value>=IIF(Fields!TX.Value&gt;0, FormatCurrency(ROUND(Fields!TX.Value,2)),"")</Value>
                    </Textbox>
                  </ReportItems>
                </TableCell>
                <TableCell>
                  <ReportItems>
                    <Textbox Name="TOTAL___REGION_4">
                      <rd:DefaultName>TOTAL___REGION_4</rd:DefaultName>
                      <Style>
                        <BorderColor>
                          <Default>LightGrey</Default>
                        </BorderColor>
                        <BorderStyle>
                          <Default>Solid</Default>
                        </BorderStyle>
                        <FontFamily>Tahoma</FontFamily>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                        <Direction>RTL</Direction>
                      </Style>
                      <CanGrow>true</CanGrow>
                      <Value>=IIF(Fields!TOTAL___REGION_4.Value&gt;0, FormatCurrency(ROUND(Fields!TOTAL___REGION_4.Value,2)),"")</Value>
                    </Textbox>
                  </ReportItems>
                </TableCell>
              </TableCells>
              <Height>0.21in</Height>
            </TableRow>
          </TableRows>
        </Details>
        <Header>
          <TableRows>
            <TableRow>
              <TableCells>
                <TableCell>
                  <ReportItems>
                    <Textbox Name="textbox2">
                      <rd:DefaultName>textbox2</rd:DefaultName>
                      <Style>
                        <Color>White</Color>
                        <BackgroundColor>LightSlateGray</BackgroundColor>
                        <BorderColor>
                          <Default>LightGrey</Default>
                        </BorderColor>
                        <BorderStyle>
                          <Default>Solid</Default>
                        </BorderStyle>
                        <FontFamily>Tahoma</FontFamily>
                        <FontSize>11pt</FontSize>
                        <FontWeight>700</FontWeight>
                        <TextAlign>Right</TextAlign>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                      <ZIndex>113</ZIndex>
                      <CanGrow>true</CanGrow>
                      <Value>ROWID</Value>
                    </Textbox>
                  </ReportItems>
                </TableCell>
                <TableCell>
                  <ReportItems>
                    <Textbox Name="textbox3">
                      <rd:DefaultName>textbox3</rd:DefaultName>
                      <Style>
                        <Color>White</Color>
                        <BackgroundColor>LightSlateGray</BackgroundColor>
                        <BorderColor>
                          <Default>LightGrey</Default>
                        </BorderColor>
                        <BorderStyle>
                          <Default>Solid</Default>
                        </BorderStyle>
                        <FontFamily>Tahoma</FontFamily>
                        <FontSize>11pt</FontSize>
                        <FontWeight>700</FontWeight>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                      <ZIndex>112</ZIndex>
                      <CanGrow>true</CanGrow>
                      <Value>SOURCE CODE</Value>
                    </Textbox>
                  </ReportItems>
                </TableCell>
                <TableCell>
                  <ReportItems>
                    <Textbox Name="textbox4">
                      <rd:DefaultName>textbox4</rd:DefaultName>
                      <Style>
                        <Color>White</Color>
                        <BackgroundColor>LightSlateGray</BackgroundColor>
                        <BorderColor>
                          <Default>LightGrey</Default>
                        </BorderColor>
                        <BorderStyle>
                          <Default>Solid</Default>
                        </BorderStyle>
                        <FontFamily>Tahoma</FontFamily>
                        <FontSize>11pt</FontSize>
                        <FontWeight>700</FontWeight>
                        <TextAlign>Right</TextAlign>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                      <ZIndex>111</ZIndex>
                      <CanGrow>true</CanGrow>
                      <Value>AL</Value>
                    </Textbox>
                  </ReportItems>
                </TableCell>
                <TableCell>
                  <ReportItems>
                    <Textbox Name="textbox5">
                      <rd:DefaultName>textbox5</rd:DefaultName>
                      <Style>
                        <Color>White</Color>
                        <BackgroundColor>LightSlateGray</BackgroundColor>
                        <BorderColor>
                          <Default>LightGrey</Default>
                        </BorderColor>
                        <BorderStyle>
                          <Default>Solid</Default>
                        </BorderStyle>
                        <FontFamily>Tahoma</FontFamily>
                        <FontSize>11pt</FontSize>
                        <FontWeight>700</FontWeight>
                        <TextAlign>Right</TextAlign>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                      <ZIndex>110</ZIndex>
                      <CanGrow>true</CanGrow>
                      <Value>CT</Value>
                    </Textbox>
                  </ReportItems>
                </TableCell>
                <TableCell>
                  <ReportItems>
                    <Textbox Name="textbox6">
                      <rd:DefaultName>textbox6</rd:DefaultName>
                      <Style>
                        <Color>White</Color>
                        <BackgroundColor>LightSlateGray</BackgroundColor>
                        <BorderColor>
                          <Default>LightGrey</Default>
                        </BorderColor>
                        <BorderStyle>
                          <Default>Solid</Default>
                        </BorderStyle>
                        <FontFamily>Tahoma</FontFamily>
                        <FontSize>11pt</FontSize>
                        <FontWeight>700</FontWeight>
                        <TextAlign>Right</TextAlign>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                      <ZIndex>109</ZIndex>
                      <CanGrow>true</CanGrow>
                      <Value>DC</Value>
                    </Textbox>
                  </ReportItems>
                </TableCell>
                <TableCell>
                  <ReportItems>
                    <Textbox Name="textbox7">
                      <rd:DefaultName>textbox7</rd:DefaultName>
                      <Style>
                        <Color>White</Color>
                        <BackgroundColor>LightSlateGray</BackgroundColor>
                        <BorderColor>
                          <Default>LightGrey</Default>
                        </BorderColor>
                        <BorderStyle>
                          <Default>Solid</Default>
                        </BorderStyle>
                        <FontFamily>Tahoma</FontFamily>
                        <FontSize>11pt</FontSize>
                        <FontWeight>700</FontWeight>
                        <TextAlign>Right</TextAlign>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                      <ZIndex>108</ZIndex>
                      <CanGrow>true</CanGrow>
                      <Value>DE</Value>
                    </Textbox>
                  </ReportItems>
                </TableCell>
                <TableCell>
                  <ReportItems>
                    <Textbox Name="textbox8">
                      <rd:DefaultName>textbox8</rd:DefaultName>
                      <Style>
                        <Color>White</Color>
                        <BackgroundColor>LightSlateGray</BackgroundColor>
                        <BorderColor>
                          <Default>LightGrey</Default>
                        </BorderColor>
                        <BorderStyle>
                          <Default>Solid</Default>
                        </BorderStyle>
                        <FontFamily>Tahoma</FontFamily>
                        <FontSize>11pt</FontSize>
                        <FontWeight>700</FontWeight>
                        <TextAlign>Right</TextAlign>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                      <ZIndex>107</ZIndex>
                      <CanGrow>true</CanGrow>
                      <Value>FL</Value>
                    </Textbox>
                  </ReportItems>
                </TableCell>
                <TableCell>
                  <ReportItems>
                    <Textbox Name="textbox9">
                      <rd:DefaultName>textbox9</rd:DefaultName>
                      <Style>
                        <Color>White</Color>
                        <BackgroundColor>LightSlateGray</BackgroundColor>
                        <BorderColor>
                          <Default>LightGrey</Default>
                        </BorderColor>
                        <BorderStyle>
                          <Default>Solid</Default>
                        </BorderStyle>
                        <FontFamily>Tahoma</FontFamily>
                        <FontSize>11pt</FontSize>
                        <FontWeight>700</FontWeight>
                        <TextAlign>Right</TextAlign>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                      <ZIndex>106</ZIndex>
                      <CanGrow>true</CanGrow>
                      <Value>MA</Value>
                    </Textbox>
                  </ReportItems>
                </TableCell>
                <TableCell>
                  <ReportItems>
                    <Textbox Name="textbox10">
                      <rd:DefaultName>textbox10</rd:DefaultName>
                      <Style>
                        <Color>White</Color>
                        <BackgroundColor>LightSlateGray</BackgroundColor>
                        <BorderColor>
                          <Default>LightGrey</Default>
                        </BorderColor>
                        <BorderStyle>
                          <Default>Solid</Default>
                        </BorderStyle>
                        <FontFamily>Tahoma</FontFamily>
                        <FontSize>11pt</FontSize>
                        <FontWeight>700</FontWeight>
                        <TextAlign>Right</TextAlign>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                      <ZIndex>105</ZIndex>
                      <CanGrow>true</CanGrow>
                      <Value>MD</Value>
                    </Textbox>
                  </ReportItems>
                </TableCell>
                <TableCell>
                  <ReportItems>
                    <Textbox Name="textbox11">
                      <rd:DefaultName>textbox11</rd:DefaultName>
                      <Style>
                        <Color>White</Color>
                        <BackgroundColor>LightSlateGray</BackgroundColor>
                        <BorderColor>
                          <Default>LightGrey</Default>
                        </BorderColor>
                        <BorderStyle>
                          <Default>Solid</Default>
                        </BorderStyle>
                        <FontFamily>Tahoma</FontFamily>
                        <FontSize>11pt</FontSize>
                        <FontWeight>700</FontWeight>
                        <TextAlign>Right</TextAlign>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                      <ZIndex>104</ZIndex>
                      <CanGrow>true</CanGrow>
                      <Value>ME</Value>
                    </Textbox>
                  </ReportItems>
                </TableCell>
                <TableCell>
                  <ReportItems>
                    <Textbox Name="textbox12">
                      <rd:DefaultName>textbox12</rd:DefaultName>
                      <Style>
                        <Color>White</Color>
                        <BackgroundColor>LightSlateGray</BackgroundColor>
                        <BorderColor>
                          <Default>LightGrey</Default>
                        </BorderColor>
                        <BorderStyle>
                          <Default>Solid</Default>
                        </BorderStyle>
                        <FontFamily>Tahoma</FontFamily>
                        <FontSize>11pt</FontSize>
                        <FontWeight>700</FontWeight>
                        <TextAlign>Right</TextAlign>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                      <ZIndex>103</ZIndex>
                      <CanGrow>true</CanGrow>
                      <Value>MO</Value>
                    </Textbox>
                  </ReportItems>
                </TableCell>
                <TableCell>
                  <ReportItems>
                    <Textbox Name="textbox13">
                      <rd:DefaultName>textbox13</rd:DefaultName>
                      <Style>
                        <Color>White</Color>
                        <BackgroundColor>LightSlateGray</BackgroundColor>
                        <BorderColor>
                          <Default>LightGrey</Default>
                        </BorderColor>
                        <BorderStyle>
                          <Default>Solid</Default>
                        </BorderStyle>
                        <FontFamily>Tahoma</FontFamily>
                        <FontSize>11pt</FontSize>
                        <FontWeight>700</FontWeight>
                        <TextAlign>Right</TextAlign>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                      <ZIndex>102</ZIndex>
                      <CanGrow>true</CanGrow>
                      <Value>NH</Value>
                    </Textbox>
                  </ReportItems>
                </TableCell>
                <TableCell>
                  <ReportItems>
                    <Textbox Name="textbox14">
                      <rd:DefaultName>textbox14</rd:DefaultName>
                      <Style>
                        <Color>White</Color>
                        <BackgroundColor>LightSlateGray</BackgroundColor>
                        <BorderColor>
                          <Default>LightGrey</Default>
                        </BorderColor>
                        <BorderStyle>
                          <Default>Solid</Default>
                        </BorderStyle>
                        <FontFamily>Tahoma</FontFamily>
                        <FontSize>11pt</FontSize>
                        <FontWeight>700</FontWeight>
                        <TextAlign>Right</TextAlign>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                      <ZIndex>101</ZIndex>
                      <CanGrow>true</CanGrow>
                      <Value>NJ</Value>
                    </Textbox>
                  </ReportItems>
                </TableCell>
                <TableCell>
                  <ReportItems>
                    <Textbox Name="textbox15">
                      <rd:DefaultName>textbox15</rd:DefaultName>
                      <Style>
                        <Color>White</Color>
                        <BackgroundColor>LightSlateGray</BackgroundColor>
                        <BorderColor>
                          <Default>LightGrey</Default>
                        </BorderColor>
                        <BorderStyle>
                          <Default>Solid</Default>
                        </BorderStyle>
                        <FontFamily>Tahoma</FontFamily>
                        <FontSize>11pt</FontSize>
                        <FontWeight>700</FontWeight>
                        <TextAlign>Right</TextAlign>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                      <ZIndex>100</ZIndex>
                      <CanGrow>true</CanGrow>
                      <Value>NY</Value>
                    </Textbox>
                  </ReportItems>
                </TableCell>
                <TableCell>
                  <ReportItems>
                    <Textbox Name="textbox16">
                      <rd:DefaultName>textbox16</rd:DefaultName>
                      <Style>
                        <Color>White</Color>
                        <BackgroundColor>LightSlateGray</BackgroundColor>
                        <BorderColor>
                          <Default>LightGrey</Default>
                        </BorderColor>
                        <BorderStyle>
                          <Default>Solid</Default>
                        </BorderStyle>
                        <FontFamily>Tahoma</FontFamily>
                        <FontSize>11pt</FontSize>
                        <FontWeight>700</FontWeight>
                        <TextAlign>Right</TextAlign>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                      <ZIndex>99</ZIndex>
                      <CanGrow>true</CanGrow>
                      <Value>PA</Value>
                    </Textbox>
                  </ReportItems>
                </TableCell>
                <TableCell>
                  <ReportItems>
                    <Textbox Name="textbox17">
                      <rd:DefaultName>textbox17</rd:DefaultName>
                      <Style>
                        <Color>White</Color>
                        <BackgroundColor>LightSlateGray</BackgroundColor>
                        <BorderColor>
                          <Default>LightGrey</Default>
                        </BorderColor>
                        <BorderStyle>
                          <Default>Solid</Default>
                        </BorderStyle>
                        <FontFamily>Tahoma</FontFamily>
                        <FontSize>11pt</FontSize>
                        <FontWeight>700</FontWeight>
                        <TextAlign>Right</TextAlign>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                      <ZIndex>98</ZIndex>
                      <CanGrow>true</CanGrow>
                      <Value>RI</Value>
                    </Textbox>
                  </ReportItems>
                </TableCell>
                <TableCell>
                  <ReportItems>
                    <Textbox Name="textbox18">
                      <rd:DefaultName>textbox18</rd:DefaultName>
                      <Style>
                        <Color>White</Color>
                        <BackgroundColor>LightSlateGray</BackgroundColor>
                        <BorderColor>
                          <Default>LightGrey</Default>
                        </BorderColor>
                        <BorderStyle>
                          <Default>Solid</Default>
                        </BorderStyle>
                        <FontFamily>Tahoma</FontFamily>
                        <FontSize>11pt</FontSize>
                        <FontWeight>700</FontWeight>
                        <TextAlign>Right</TextAlign>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                      <ZIndex>97</ZIndex>
                      <CanGrow>true</CanGrow>
                      <Value>VA</Value>
                    </Textbox>
                  </ReportItems>
                </TableCell>
                <TableCell>
                  <ReportItems>
                    <Textbox Name="textbox19">
                      <rd:DefaultName>textbox19</rd:DefaultName>
                      <Style>
                        <Color>White</Color>
                        <BackgroundColor>LightSlateGray</BackgroundColor>
                        <BorderColor>
                          <Default>LightGrey</Default>
                        </BorderColor>
                        <BorderStyle>
                          <Default>Solid</Default>
                        </BorderStyle>
                        <FontFamily>Tahoma</FontFamily>
                        <FontSize>11pt</FontSize>
                        <FontWeight>700</FontWeight>
                        <TextAlign>Right</TextAlign>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                      <ZIndex>96</ZIndex>
                      <CanGrow>true</CanGrow>
                      <Value>VT</Value>
                    </Textbox>
                  </ReportItems>
                </TableCell>
                <TableCell>
                  <ReportItems>
                    <Textbox Name="textbox20">
                      <rd:DefaultName>textbox20</rd:DefaultName>
                      <Style>
                        <Color>White</Color>
                        <BackgroundColor>LightSlateGray</BackgroundColor>
                        <BorderColor>
                          <Default>LightGrey</Default>
                        </BorderColor>
                        <BorderStyle>
                          <Default>Solid</Default>
                        </BorderStyle>
                        <FontFamily>Tahoma</FontFamily>
                        <FontSize>11pt</FontSize>
                        <FontWeight>700</FontWeight>
                        <TextAlign>Right</TextAlign>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                      <ZIndex>95</ZIndex>
                      <CanGrow>true</CanGrow>
                      <Value>WV</Value>
                    </Textbox>
                  </ReportItems>
                </TableCell>
                <TableCell>
                  <ReportItems>
                    <Textbox Name="textbox21">
                      <rd:DefaultName>textbox21</rd:DefaultName>
                      <Style>
                        <Color>White</Color>
                        <BackgroundColor>LightSlateGray</BackgroundColor>
                        <BorderColor>
                          <Default>LightGrey</Default>
                        </BorderColor>
                        <BorderStyle>
                          <Default>Solid</Default>
                        </BorderStyle>
                        <FontFamily>Tahoma</FontFamily>
                        <FontSize>11pt</FontSize>
                        <FontWeight>700</FontWeight>
                        <TextAlign>Right</TextAlign>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                      <ZIndex>94</ZIndex>
                      <CanGrow>true</CanGrow>
                      <Value>TOTAL REGION 1</Value>
                    </Textbox>
                  </ReportItems>
                </TableCell>
                <TableCell>
                  <ReportItems>
                    <Textbox Name="textbox22">
                      <rd:DefaultName>textbox22</rd:DefaultName>
                      <Style>
                        <Color>White</Color>
                        <BackgroundColor>LightSlateGray</BackgroundColor>
                        <BorderColor>
                          <Default>LightGrey</Default>
                        </BorderColor>
                        <BorderStyle>
                          <Default>Solid</Default>
                        </BorderStyle>
                        <FontFamily>Tahoma</FontFamily>
                        <FontSize>11pt</FontSize>
                        <FontWeight>700</FontWeight>
                        <TextAlign>Right</TextAlign>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                      <ZIndex>93</ZIndex>
                      <CanGrow>true</CanGrow>
                      <Value>AK</Value>
                    </Textbox>
                  </ReportItems>
                </TableCell>
                <TableCell>
                  <ReportItems>
                    <Textbox Name="textbox23">
                      <rd:DefaultName>textbox23</rd:DefaultName>
                      <Style>
                        <Color>White</Color>
                        <BackgroundColor>LightSlateGray</BackgroundColor>
                        <BorderColor>
                          <Default>LightGrey</Default>
                        </BorderColor>
                        <BorderStyle>
                          <Default>Solid</Default>
                        </BorderStyle>
                        <FontFamily>Tahoma</FontFamily>
                        <FontSize>11pt</FontSize>
                        <FontWeight>700</FontWeight>
                        <TextAlign>Right</TextAlign>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                      <ZIndex>92</ZIndex>
                      <CanGrow>true</CanGrow>
                      <Value>AZ</Value>
                    </Textbox>
                  </ReportItems>
                </TableCell>
                <TableCell>
                  <ReportItems>
                    <Textbox Name="textbox24">
                      <rd:DefaultName>textbox24</rd:DefaultName>
                      <Style>
                        <Color>White</Color>
                        <BackgroundColor>LightSlateGray</BackgroundColor>
                        <BorderColor>
                          <Default>LightGrey</Default>
                        </BorderColor>
                        <BorderStyle>
                          <Default>Solid</Default>
                        </BorderStyle>
                        <FontFamily>Tahoma</FontFamily>
                        <FontSize>11pt</FontSize>
                        <FontWeight>700</FontWeight>
                        <TextAlign>Right</TextAlign>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                      <ZIndex>91</ZIndex>
                      <CanGrow>true</CanGrow>
                      <Value>CA</Value>
                    </Textbox>
                  </ReportItems>
                </TableCell>
                <TableCell>
                  <ReportItems>
                    <Textbox Name="textbox25">
                      <rd:DefaultName>textbox25</rd:DefaultName>
                      <Style>
                        <Color>White</Color>
                        <BackgroundColor>LightSlateGray</BackgroundColor>
                        <BorderColor>
                          <Default>LightGrey</Default>
                        </BorderColor>
                        <BorderStyle>
                          <Default>Solid</Default>
                        </BorderStyle>
                        <FontFamily>Tahoma</FontFamily>
                        <FontSize>11pt</FontSize>
                        <FontWeight>700</FontWeight>
                        <TextAlign>Right</TextAlign>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                      <ZIndex>90</ZIndex>
                      <CanGrow>true</CanGrow>
                      <Value>CO</Value>
                    </Textbox>
                  </ReportItems>
                </TableCell>
                <TableCell>
                  <ReportItems>
                    <Textbox Name="textbox26">
                      <rd:DefaultName>textbox26</rd:DefaultName>
                      <Style>
                        <Color>White</Color>
                        <BackgroundColor>LightSlateGray</BackgroundColor>
                        <BorderColor>
                          <Default>LightGrey</Default>
                        </BorderColor>
                        <BorderStyle>
                          <Default>Solid</Default>
                        </BorderStyle>
                        <FontFamily>Tahoma</FontFamily>
                        <FontSize>11pt</FontSize>
                        <FontWeight>700</FontWeight>
                        <TextAlign>Right</TextAlign>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                      <ZIndex>89</ZIndex>
                      <CanGrow>true</CanGrow>
                      <Value>HI</Value>
                    </Textbox>
                  </ReportItems>
                </TableCell>
                <TableCell>
                  <ReportItems>
                    <Textbox Name="textbox27">
                      <rd:DefaultName>textbox27</rd:DefaultName>
                      <Style>
                        <Color>White</Color>
                        <BackgroundColor>LightSlateGray</BackgroundColor>
                        <BorderColor>
                          <Default>LightGrey</Default>
                        </BorderColor>
                        <BorderStyle>
                          <Default>Solid</Default>
                        </BorderStyle>
                        <FontFamily>Tahoma</FontFamily>
                        <FontSize>11pt</FontSize>
                        <FontWeight>700</FontWeight>
                        <TextAlign>Right</TextAlign>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                      <ZIndex>88</ZIndex>
                      <CanGrow>true</CanGrow>
                      <Value>ID</Value>
                    </Textbox>
                  </ReportItems>
                </TableCell>
                <TableCell>
                  <ReportItems>
                    <Textbox Name="textbox28">
                      <rd:DefaultName>textbox28</rd:DefaultName>
                      <Style>
                        <Color>White</Color>
                        <BackgroundColor>LightSlateGray</BackgroundColor>
                        <BorderColor>
                          <Default>LightGrey</Default>
                        </BorderColor>
                        <BorderStyle>
                          <Default>Solid</Default>
                        </BorderStyle>
                        <FontFamily>Tahoma</FontFamily>
                        <FontSize>11pt</FontSize>
                        <FontWeight>700</FontWeight>
                        <TextAlign>Right</TextAlign>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                      <ZIndex>87</ZIndex>
                      <CanGrow>true</CanGrow>
                      <Value>IL</Value>
                    </Textbox>
                  </ReportItems>
                </TableCell>
                <TableCell>
                  <ReportItems>
                    <Textbox Name="textbox29">
                      <rd:DefaultName>textbox29</rd:DefaultName>
                      <Style>
                        <Color>White</Color>
                        <BackgroundColor>LightSlateGray</BackgroundColor>
                        <BorderColor>
                          <Default>LightGrey</Default>
                        </BorderColor>
                        <BorderStyle>
                          <Default>Solid</Default>
                        </BorderStyle>
                        <FontFamily>Tahoma</FontFamily>
                        <FontSize>11pt</FontSize>
                        <FontWeight>700</FontWeight>
                        <TextAlign>Right</TextAlign>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                      <ZIndex>86</ZIndex>
                      <CanGrow>true</CanGrow>
                      <Value>KS</Value>
                    </Textbox>
                  </ReportItems>
                </TableCell>
                <TableCell>
                  <ReportItems>
                    <Textbox Name="textbox30">
                      <rd:DefaultName>textbox30</rd:DefaultName>
                      <Style>
                        <Color>White</Color>
                        <BackgroundColor>LightSlateGray</BackgroundColor>
                        <BorderColor>
                          <Default>LightGrey</Default>
                        </BorderColor>
                        <BorderStyle>
                          <Default>Solid</Default>
                        </BorderStyle>
                        <FontFamily>Tahoma</FontFamily>
                        <FontSize>11pt</FontSize>
                        <FontWeight>700</FontWeight>
                        <TextAlign>Right</TextAlign>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                      <ZIndex>85</ZIndex>
                      <CanGrow>true</CanGrow>
                      <Value>NM</Value>
                    </Textbox>
                  </ReportItems>
                </TableCell>
                <TableCell>
                  <ReportItems>
                    <Textbox Name="textbox31">
                      <rd:DefaultName>textbox31</rd:DefaultName>
                      <Style>
                        <Color>White</Color>
                        <BackgroundColor>LightSlateGray</BackgroundColor>
                        <BorderColor>
                          <Default>LightGrey</Default>
                        </BorderColor>
                        <BorderStyle>
                          <Default>Solid</Default>
                        </BorderStyle>
                        <FontFamily>Tahoma</FontFamily>
                        <FontSize>11pt</FontSize>
                        <FontWeight>700</FontWeight>
                        <TextAlign>Right</TextAlign>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                      <ZIndex>84</ZIndex>
                      <CanGrow>true</CanGrow>
                      <Value>NV</Value>
                    </Textbox>
                  </ReportItems>
                </TableCell>
                <TableCell>
                  <ReportItems>
                    <Textbox Name="textbox32">
                      <rd:DefaultName>textbox32</rd:DefaultName>
                      <Style>
                        <Color>White</Color>
                        <BackgroundColor>LightSlateGray</BackgroundColor>
                        <BorderColor>
                          <Default>LightGrey</Default>
                        </BorderColor>
                        <BorderStyle>
                          <Default>Solid</Default>
                        </BorderStyle>
                        <FontFamily>Tahoma</FontFamily>
                        <FontSize>11pt</FontSize>
                        <FontWeight>700</FontWeight>
                        <TextAlign>Right</TextAlign>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                      <ZIndex>83</ZIndex>
                      <CanGrow>true</CanGrow>
                      <Value>OH</Value>
                    </Textbox>
                  </ReportItems>
                </TableCell>
                <TableCell>
                  <ReportItems>
                    <Textbox Name="textbox33">
                      <rd:DefaultName>textbox33</rd:DefaultName>
                      <Style>
                        <Color>White</Color>
                        <BackgroundColor>LightSlateGray</BackgroundColor>
                        <BorderColor>
                          <Default>LightGrey</Default>
                        </BorderColor>
                        <BorderStyle>
                          <Default>Solid</Default>
                        </BorderStyle>
                        <FontFamily>Tahoma</FontFamily>
                        <FontSize>11pt</FontSize>
                        <FontWeight>700</FontWeight>
                        <TextAlign>Right</TextAlign>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                      <ZIndex>82</ZIndex>
                      <CanGrow>true</CanGrow>
                      <Value>OK</Value>
                    </Textbox>
                  </ReportItems>
                </TableCell>
                <TableCell>
                  <ReportItems>
                    <Textbox Name="textbox34">
                      <rd:DefaultName>textbox34</rd:DefaultName>
                      <Style>
                        <Color>White</Color>
                        <BackgroundColor>LightSlateGray</BackgroundColor>
                        <BorderColor>
                          <Default>LightGrey</Default>
                        </BorderColor>
                        <BorderStyle>
                          <Default>Solid</Default>
                        </BorderStyle>
                        <FontFamily>Tahoma</FontFamily>
                        <FontSize>11pt</FontSize>
                        <FontWeight>700</FontWeight>
                        <TextAlign>Right</TextAlign>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                      <ZIndex>81</ZIndex>
                      <CanGrow>true</CanGrow>
                      <Value>OR</Value>
                    </Textbox>
                  </ReportItems>
                </TableCell>
                <TableCell>
                  <ReportItems>
                    <Textbox Name="textbox35">
                      <rd:DefaultName>textbox35</rd:DefaultName>
                      <Style>
                        <Color>White</Color>
                        <BackgroundColor>LightSlateGray</BackgroundColor>
                        <BorderColor>
                          <Default>LightGrey</Default>
                        </BorderColor>
                        <BorderStyle>
                          <Default>Solid</Default>
                        </BorderStyle>
                        <FontFamily>Tahoma</FontFamily>
                        <FontSize>11pt</FontSize>
                        <FontWeight>700</FontWeight>
                        <TextAlign>Right</TextAlign>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                      <ZIndex>80</ZIndex>
                      <CanGrow>true</CanGrow>
                      <Value>UT</Value>
                    </Textbox>
                  </ReportItems>
                </TableCell>
                <TableCell>
                  <ReportItems>
                    <Textbox Name="textbox36">
                      <rd:DefaultName>textbox36</rd:DefaultName>
                      <Style>
                        <Color>White</Color>
                        <BackgroundColor>LightSlateGray</BackgroundColor>
                        <BorderColor>
                          <Default>LightGrey</Default>
                        </BorderColor>
                        <BorderStyle>
                          <Default>Solid</Default>
                        </BorderStyle>
                        <FontFamily>Tahoma</FontFamily>
                        <FontSize>11pt</FontSize>
                        <FontWeight>700</FontWeight>
                        <TextAlign>Right</TextAlign>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                      <ZIndex>79</ZIndex>
                      <CanGrow>true</CanGrow>
                      <Value>WA</Value>
                    </Textbox>
                  </ReportItems>
                </TableCell>
                <TableCell>
                  <ReportItems>
                    <Textbox Name="textbox37">
                      <rd:DefaultName>textbox37</rd:DefaultName>
                      <Style>
                        <Color>White</Color>
                        <BackgroundColor>LightSlateGray</BackgroundColor>
                        <BorderColor>
                          <Default>LightGrey</Default>
                        </BorderColor>
                        <BorderStyle>
                          <Default>Solid</Default>
                        </BorderStyle>
                        <FontFamily>Tahoma</FontFamily>
                        <FontSize>11pt</FontSize>
                        <FontWeight>700</FontWeight>
                        <TextAlign>Right</TextAlign>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                      <ZIndex>78</ZIndex>
                      <CanGrow>true</CanGrow>
                      <Value>WY</Value>
                    </Textbox>
                  </ReportItems>
                </TableCell>
                <TableCell>
                  <ReportItems>
                    <Textbox Name="textbox38">
                      <rd:DefaultName>textbox38</rd:DefaultName>
                      <Style>
                        <Color>White</Color>
                        <BackgroundColor>LightSlateGray</BackgroundColor>
                        <BorderColor>
                          <Default>LightGrey</Default>
                        </BorderColor>
                        <BorderStyle>
                          <Default>Solid</Default>
                        </BorderStyle>
                        <FontFamily>Tahoma</FontFamily>
                        <FontSize>11pt</FontSize>
                        <FontWeight>700</FontWeight>
                        <TextAlign>Right</TextAlign>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                      <ZIndex>77</ZIndex>
                      <CanGrow>true</CanGrow>
                      <Value>TOTAL REGION 2</Value>
                    </Textbox>
                  </ReportItems>
                </TableCell>
                <TableCell>
                  <ReportItems>
                    <Textbox Name="textbox39">
                      <rd:DefaultName>textbox39</rd:DefaultName>
                      <Style>
                        <Color>White</Color>
                        <BackgroundColor>LightSlateGray</BackgroundColor>
                        <BorderColor>
                          <Default>LightGrey</Default>
                        </BorderColor>
                        <BorderStyle>
                          <Default>Solid</Default>
                        </BorderStyle>
                        <FontFamily>Tahoma</FontFamily>
                        <FontSize>11pt</FontSize>
                        <FontWeight>700</FontWeight>
                        <TextAlign>Right</TextAlign>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                      <ZIndex>76</ZIndex>
                      <CanGrow>true</CanGrow>
                      <Value>AR</Value>
                    </Textbox>
                  </ReportItems>
                </TableCell>
                <TableCell>
                  <ReportItems>
                    <Textbox Name="textbox40">
                      <rd:DefaultName>textbox40</rd:DefaultName>
                      <Style>
                        <Color>White</Color>
                        <BackgroundColor>LightSlateGray</BackgroundColor>
                        <BorderColor>
                          <Default>LightGrey</Default>
                        </BorderColor>
                        <BorderStyle>
                          <Default>Solid</Default>
                        </BorderStyle>
                        <FontFamily>Tahoma</FontFamily>
                        <FontSize>11pt</FontSize>
                        <FontWeight>700</FontWeight>
                        <TextAlign>Right</TextAlign>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                      <ZIndex>75</ZIndex>
                      <CanGrow>true</CanGrow>
                      <Value>GA</Value>
                    </Textbox>
                  </ReportItems>
                </TableCell>
                <TableCell>
                  <ReportItems>
                    <Textbox Name="textbox41">
                      <rd:DefaultName>textbox41</rd:DefaultName>
                      <Style>
                        <Color>White</Color>
                        <BackgroundColor>LightSlateGray</BackgroundColor>
                        <BorderColor>
                          <Default>LightGrey</Default>
                        </BorderColor>
                        <BorderStyle>
                          <Default>Solid</Default>
                        </BorderStyle>
                        <FontFamily>Tahoma</FontFamily>
                        <FontSize>11pt</FontSize>
                        <FontWeight>700</FontWeight>
                        <TextAlign>Right</TextAlign>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                      <ZIndex>74</ZIndex>
                      <CanGrow>true</CanGrow>
                      <Value>IA</Value>
                    </Textbox>
                  </ReportItems>
                </TableCell>
                <TableCell>
                  <ReportItems>
                    <Textbox Name="textbox42">
                      <rd:DefaultName>textbox42</rd:DefaultName>
                      <Style>
                        <Color>White</Color>
                        <BackgroundColor>LightSlateGray</BackgroundColor>
                        <BorderColor>
                          <Default>LightGrey</Default>
                        </BorderColor>
                        <BorderStyle>
                          <Default>Solid</Default>
                        </BorderStyle>
                        <FontFamily>Tahoma</FontFamily>
                        <FontSize>11pt</FontSize>
                        <FontWeight>700</FontWeight>
                        <TextAlign>Right</TextAlign>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                      <ZIndex>73</ZIndex>
                      <CanGrow>true</CanGrow>
                      <Value>IN</Value>
                    </Textbox>
                  </ReportItems>
                </TableCell>
                <TableCell>
                  <ReportItems>
                    <Textbox Name="textbox43">
                      <rd:DefaultName>textbox43</rd:DefaultName>
                      <Style>
                        <Color>White</Color>
                        <BackgroundColor>LightSlateGray</BackgroundColor>
                        <BorderColor>
                          <Default>LightGrey</Default>
                        </BorderColor>
                        <BorderStyle>
                          <Default>Solid</Default>
                        </BorderStyle>
                        <FontFamily>Tahoma</FontFamily>
                        <FontSize>11pt</FontSize>
                        <FontWeight>700</FontWeight>
                        <TextAlign>Right</TextAlign>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                      <ZIndex>72</ZIndex>
                      <CanGrow>true</CanGrow>
                      <Value>KY</Value>
                    </Textbox>
                  </ReportItems>
                </TableCell>
                <TableCell>
                  <ReportItems>
                    <Textbox Name="textbox44">
                      <rd:DefaultName>textbox44</rd:DefaultName>
                      <Style>
                        <Color>White</Color>
                        <BackgroundColor>LightSlateGray</BackgroundColor>
                        <BorderColor>
                          <Default>LightGrey</Default>
                        </BorderColor>
                        <BorderStyle>
                          <Default>Solid</Default>
                        </BorderStyle>
                        <FontFamily>Tahoma</FontFamily>
                        <FontSize>11pt</FontSize>
                        <FontWeight>700</FontWeight>
                        <TextAlign>Right</TextAlign>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                      <ZIndex>71</ZIndex>
                      <CanGrow>true</CanGrow>
                      <Value>LA</Value>
                    </Textbox>
                  </ReportItems>
                </TableCell>
                <TableCell>
                  <ReportItems>
                    <Textbox Name="textbox45">
                      <rd:DefaultName>textbox45</rd:DefaultName>
                      <Style>
                        <Color>White</Color>
                        <BackgroundColor>LightSlateGray</BackgroundColor>
                        <BorderColor>
                          <Default>LightGrey</Default>
                        </BorderColor>
                        <BorderStyle>
                          <Default>Solid</Default>
                        </BorderStyle>
                        <FontFamily>Tahoma</FontFamily>
                        <FontSize>11pt</FontSize>
                        <FontWeight>700</FontWeight>
                        <TextAlign>Right</TextAlign>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                      <ZIndex>70</ZIndex>
                      <CanGrow>true</CanGrow>
                      <Value>MI</Value>
                    </Textbox>
                  </ReportItems>
                </TableCell>
                <TableCell>
                  <ReportItems>
                    <Textbox Name="textbox46">
                      <rd:DefaultName>textbox46</rd:DefaultName>
                      <Style>
                        <Color>White</Color>
                        <BackgroundColor>LightSlateGray</BackgroundColor>
                        <BorderColor>
                          <Default>LightGrey</Default>
                        </BorderColor>
                        <BorderStyle>
                          <Default>Solid</Default>
                        </BorderStyle>
                        <FontFamily>Tahoma</FontFamily>
                        <FontSize>11pt</FontSize>
                        <FontWeight>700</FontWeight>
                        <TextAlign>Right</TextAlign>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                      <ZIndex>69</ZIndex>
                      <CanGrow>true</CanGrow>
                      <Value>MN</Value>
                    </Textbox>
                  </ReportItems>
                </TableCell>
                <TableCell>
                  <ReportItems>
                    <Textbox Name="textbox47">
                      <rd:DefaultName>textbox47</rd:DefaultName>
                      <Style>
                        <Color>White</Color>
                        <BackgroundColor>LightSlateGray</BackgroundColor>
                        <BorderColor>
                          <Default>LightGrey</Default>
                        </BorderColor>
                        <BorderStyle>
                          <Default>Solid</Default>
                        </BorderStyle>
                        <FontFamily>Tahoma</FontFamily>
                        <FontSize>11pt</FontSize>
                        <FontWeight>700</FontWeight>
                        <TextAlign>Right</TextAlign>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                      <ZIndex>68</ZIndex>
                      <CanGrow>true</CanGrow>
                      <Value>MS</Value>
                    </Textbox>
                  </ReportItems>
                </TableCell>
                <TableCell>
                  <ReportItems>
                    <Textbox Name="textbox48">
                      <rd:DefaultName>textbox48</rd:DefaultName>
                      <Style>
                        <Color>White</Color>
                        <BackgroundColor>LightSlateGray</BackgroundColor>
                        <BorderColor>
                          <Default>LightGrey</Default>
                        </BorderColor>
                        <BorderStyle>
                          <Default>Solid</Default>
                        </BorderStyle>
                        <FontFamily>Tahoma</FontFamily>
                        <FontSize>11pt</FontSize>
                        <FontWeight>700</FontWeight>
                        <TextAlign>Right</TextAlign>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                      <ZIndex>67</ZIndex>
                      <CanGrow>true</CanGrow>
                      <Value>MT</Value>
                    </Textbox>
                  </ReportItems>
                </TableCell>
                <TableCell>
                  <ReportItems>
                    <Textbox Name="textbox49">
                      <rd:DefaultName>textbox49</rd:DefaultName>
                      <Style>
                        <Color>White</Color>
                        <BackgroundColor>LightSlateGray</BackgroundColor>
                        <BorderColor>
                          <Default>LightGrey</Default>
                        </BorderColor>
                        <BorderStyle>
                          <Default>Solid</Default>
                        </BorderStyle>
                        <FontFamily>Tahoma</FontFamily>
                        <FontSize>11pt</FontSize>
                        <FontWeight>700</FontWeight>
                        <TextAlign>Right</TextAlign>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                      <ZIndex>66</ZIndex>
                      <CanGrow>true</CanGrow>
                      <Value>NC</Value>
                    </Textbox>
                  </ReportItems>
                </TableCell>
                <TableCell>
                  <ReportItems>
                    <Textbox Name="textbox50">
                      <rd:DefaultName>textbox50</rd:DefaultName>
                      <Style>
                        <Color>White</Color>
                        <BackgroundColor>LightSlateGray</BackgroundColor>
                        <BorderColor>
                          <Default>LightGrey</Default>
                        </BorderColor>
                        <BorderStyle>
                          <Default>Solid</Default>
                        </BorderStyle>
                        <FontFamily>Tahoma</FontFamily>
                        <FontSize>11pt</FontSize>
                        <FontWeight>700</FontWeight>
                        <TextAlign>Right</TextAlign>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                      <ZIndex>65</ZIndex>
                      <CanGrow>true</CanGrow>
                      <Value>ND</Value>
                    </Textbox>
                  </ReportItems>
                </TableCell>
                <TableCell>
                  <ReportItems>
                    <Textbox Name="textbox51">
                      <rd:DefaultName>textbox51</rd:DefaultName>
                      <Style>
                        <Color>White</Color>
                        <BackgroundColor>LightSlateGray</BackgroundColor>
                        <BorderColor>
                          <Default>LightGrey</Default>
                        </BorderColor>
                        <BorderStyle>
                          <Default>Solid</Default>
                        </BorderStyle>
                        <FontFamily>Tahoma</FontFamily>
                        <FontSize>11pt</FontSize>
                        <FontWeight>700</FontWeight>
                        <TextAlign>Right</TextAlign>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                      <ZIndex>64</ZIndex>
                      <CanGrow>true</CanGrow>
                      <Value>NE</Value>
                    </Textbox>
                  </ReportItems>
                </TableCell>
                <TableCell>
                  <ReportItems>
                    <Textbox Name="textbox52">
                      <rd:DefaultName>textbox52</rd:DefaultName>
                      <Style>
                        <Color>White</Color>
                        <BackgroundColor>LightSlateGray</BackgroundColor>
                        <BorderColor>
                          <Default>LightGrey</Default>
                        </BorderColor>
                        <BorderStyle>
                          <Default>Solid</Default>
                        </BorderStyle>
                        <FontFamily>Tahoma</FontFamily>
                        <FontSize>11pt</FontSize>
                        <FontWeight>700</FontWeight>
                        <TextAlign>Right</TextAlign>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                      <ZIndex>63</ZIndex>
                      <CanGrow>true</CanGrow>
                      <Value>SC</Value>
                    </Textbox>
                  </ReportItems>
                </TableCell>
                <TableCell>
                  <ReportItems>
                    <Textbox Name="textbox53">
                      <rd:DefaultName>textbox53</rd:DefaultName>
                      <Style>
                        <Color>White</Color>
                        <BackgroundColor>LightSlateGray</BackgroundColor>
                        <BorderColor>
                          <Default>LightGrey</Default>
                        </BorderColor>
                        <BorderStyle>
                          <Default>Solid</Default>
                        </BorderStyle>
                        <FontFamily>Tahoma</FontFamily>
                        <FontSize>11pt</FontSize>
                        <FontWeight>700</FontWeight>
                        <TextAlign>Right</TextAlign>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                      <ZIndex>62</ZIndex>
                      <CanGrow>true</CanGrow>
                      <Value>SD</Value>
                    </Textbox>
                  </ReportItems>
                </TableCell>
                <TableCell>
                  <ReportItems>
                    <Textbox Name="textbox54">
                      <rd:DefaultName>textbox54</rd:DefaultName>
                      <Style>
                        <Color>White</Color>
                        <BackgroundColor>LightSlateGray</BackgroundColor>
                        <BorderColor>
                          <Default>LightGrey</Default>
                        </BorderColor>
                        <BorderStyle>
                          <Default>Solid</Default>
                        </BorderStyle>
                        <FontFamily>Tahoma</FontFamily>
                        <FontSize>11pt</FontSize>
                        <FontWeight>700</FontWeight>
                        <TextAlign>Right</TextAlign>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                      <ZIndex>61</ZIndex>
                      <CanGrow>true</CanGrow>
                      <Value>TN</Value>
                    </Textbox>
                  </ReportItems>
                </TableCell>
                <TableCell>
                  <ReportItems>
                    <Textbox Name="textbox55">
                      <rd:DefaultName>textbox55</rd:DefaultName>
                      <Style>
                        <Color>White</Color>
                        <BackgroundColor>LightSlateGray</BackgroundColor>
                        <BorderColor>
                          <Default>LightGrey</Default>
                        </BorderColor>
                        <BorderStyle>
                          <Default>Solid</Default>
                        </BorderStyle>
                        <FontFamily>Tahoma</FontFamily>
                        <FontSize>11pt</FontSize>
                        <FontWeight>700</FontWeight>
                        <TextAlign>Right</TextAlign>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                      <ZIndex>60</ZIndex>
                      <CanGrow>true</CanGrow>
                      <Value>WI</Value>
                    </Textbox>
                  </ReportItems>
                </TableCell>
                <TableCell>
                  <ReportItems>
                    <Textbox Name="textbox56">
                      <rd:DefaultName>textbox56</rd:DefaultName>
                      <Style>
                        <Color>White</Color>
                        <BackgroundColor>LightSlateGray</BackgroundColor>
                        <BorderColor>
                          <Default>LightGrey</Default>
                        </BorderColor>
                        <BorderStyle>
                          <Default>Solid</Default>
                        </BorderStyle>
                        <FontFamily>Tahoma</FontFamily>
                        <FontSize>11pt</FontSize>
                        <FontWeight>700</FontWeight>
                        <TextAlign>Right</TextAlign>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                      <ZIndex>59</ZIndex>
                      <CanGrow>true</CanGrow>
                      <Value>TOTAL REGION 3</Value>
                    </Textbox>
                  </ReportItems>
                </TableCell>
                <TableCell>
                  <ReportItems>
                    <Textbox Name="textbox57">
                      <rd:DefaultName>textbox57</rd:DefaultName>
                      <Style>
                        <Color>White</Color>
                        <BackgroundColor>LightSlateGray</BackgroundColor>
                        <BorderColor>
                          <Default>LightGrey</Default>
                        </BorderColor>
                        <BorderStyle>
                          <Default>Solid</Default>
                        </BorderStyle>
                        <FontFamily>Tahoma</FontFamily>
                        <FontSize>11pt</FontSize>
                        <FontWeight>700</FontWeight>
                        <TextAlign>Right</TextAlign>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                      <ZIndex>58</ZIndex>
                      <CanGrow>true</CanGrow>
                      <Value>TX</Value>
                    </Textbox>
                  </ReportItems>
                </TableCell>
                <TableCell>
                  <ReportItems>
                    <Textbox Name="textbox58">
                      <rd:DefaultName>textbox58</rd:DefaultName>
                      <Style>
                        <Color>White</Color>
                        <BackgroundColor>LightSlateGray</BackgroundColor>
                        <BorderColor>
                          <Default>LightGrey</Default>
                        </BorderColor>
                        <BorderStyle>
                          <Default>Solid</Default>
                        </BorderStyle>
                        <FontFamily>Tahoma</FontFamily>
                        <FontSize>11pt</FontSize>
                        <FontWeight>700</FontWeight>
                        <TextAlign>Right</TextAlign>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                      <ZIndex>57</ZIndex>
                      <CanGrow>true</CanGrow>
                      <Value>TOTAL REGION 4</Value>
                    </Textbox>
                  </ReportItems>
                </TableCell>
              </TableCells>
              <Height>0.22in</Height>
            </TableRow>
          </TableRows>
          <RepeatOnNewPage>true</RepeatOnNewPage>
        </Header>
        <TableColumns>
          <TableColumn>
            <Width>1in</Width>
          </TableColumn>
          <TableColumn>
            <Width>1.25in</Width>
          </TableColumn>
          <TableColumn>
            <Width>1in</Width>
          </TableColumn>
          <TableColumn>
            <Width>1in</Width>
          </TableColumn>
          <TableColumn>
            <Width>1in</Width>
          </TableColumn>
          <TableColumn>
            <Width>1in</Width>
          </TableColumn>
          <TableColumn>
            <Width>1in</Width>
          </TableColumn>
          <TableColumn>
            <Width>1in</Width>
          </TableColumn>
          <TableColumn>
            <Width>1in</Width>
          </TableColumn>
          <TableColumn>
            <Width>1in</Width>
          </TableColumn>
          <TableColumn>
            <Width>1in</Width>
          </TableColumn>
          <TableColumn>
            <Width>1in</Width>
          </TableColumn>
          <TableColumn>
            <Width>1in</Width>
          </TableColumn>
          <TableColumn>
            <Width>1in</Width>
          </TableColumn>
          <TableColumn>
            <Width>1in</Width>
          </TableColumn>
          <TableColumn>
            <Width>1in</Width>
          </TableColumn>
          <TableColumn>
            <Width>1in</Width>
          </TableColumn>
          <TableColumn>
            <Width>1in</Width>
          </TableColumn>
          <TableColumn>
            <Width>1in</Width>
          </TableColumn>
          <TableColumn>
            <Width>1in</Width>
          </TableColumn>
          <TableColumn>
            <Width>1in</Width>
          </TableColumn>
          <TableColumn>
            <Width>1in</Width>
          </TableColumn>
          <TableColumn>
            <Width>1in</Width>
          </TableColumn>
          <TableColumn>
            <Width>1in</Width>
          </TableColumn>
          <TableColumn>
            <Width>1in</Width>
          </TableColumn>
          <TableColumn>
            <Width>1in</Width>
          </TableColumn>
          <TableColumn>
            <Width>1in</Width>
          </TableColumn>
          <TableColumn>
            <Width>1in</Width>
          </TableColumn>
          <TableColumn>
            <Width>1in</Width>
          </TableColumn>
          <TableColumn>
            <Width>1in</Width>
          </TableColumn>
          <TableColumn>
            <Width>1in</Width>
          </TableColumn>
          <TableColumn>
            <Width>1in</Width>
          </TableColumn>
          <TableColumn>
            <Width>1in</Width>
          </TableColumn>
          <TableColumn>
            <Width>1in</Width>
          </TableColumn>
          <TableColumn>
            <Width>1in</Width>
          </TableColumn>
          <TableColumn>
            <Width>1in</Width>
          </TableColumn>
          <TableColumn>
            <Width>1in</Width>
          </TableColumn>
          <TableColumn>
            <Width>1in</Width>
          </TableColumn>
          <TableColumn>
            <Width>1in</Width>
          </TableColumn>
          <TableColumn>
            <Width>1in</Width>
          </TableColumn>
          <TableColumn>
            <Width>1in</Width>
          </TableColumn>
          <TableColumn>
            <Width>1in</Width>
          </TableColumn>
          <TableColumn>
            <Width>1in</Width>
          </TableColumn>
          <TableColumn>
            <Width>1in</Width>
          </TableColumn>
          <TableColumn>
            <Width>1in</Width>
          </TableColumn>
          <TableColumn>
            <Width>1in</Width>
          </TableColumn>
          <TableColumn>
            <Width>1in</Width>
          </TableColumn>
          <TableColumn>
            <Width>1in</Width>
          </TableColumn>
          <TableColumn>
            <Width>1in</Width>
          </TableColumn>
          <TableColumn>
            <Width>1in</Width>
          </TableColumn>
          <TableColumn>
            <Width>1in</Width>
          </TableColumn>
          <TableColumn>
            <Width>1in</Width>
          </TableColumn>
          <TableColumn>
            <Width>1in</Width>
          </TableColumn>
          <TableColumn>
            <Width>1in</Width>
          </TableColumn>
          <TableColumn>
            <Width>1in</Width>
          </TableColumn>
          <TableColumn>
            <Width>1in</Width>
          </TableColumn>
          <TableColumn>
            <Width>1in</Width>
          </TableColumn>
        </TableColumns>
      </Table>
    </ReportItems>
    <Height>0.79in</Height>
  </Body>
  <Language>en-US</Language>
  <TopMargin>1in</TopMargin>
</Report>