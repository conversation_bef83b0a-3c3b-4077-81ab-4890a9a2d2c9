﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

using Ninject;
using Ninject.Web.Mvc;

using cmdiapp.n.core.Core.App_Start;
using cmdiapp.n.core.Areas.crm.Domain.Models;
using cmdiapp.n.core.Areas.crm.Domain.ViewModels;
using cmdiapp.n.core.Areas.crm.Domain.Repositories;
using cmdiapp.n.core.Areas.query.Domain.Models;
using cmdiapp.n.core.Areas.crm.Domain.Data;
using System.Linq.Expressions;
using cmdiapp.n.core.Library;
using AutoMapper;
using cmdiapp.n.core.Domain.ViewModels;
using System.Data.SqlClient;

namespace cmdiapp.n.core.Areas.crm.Domain.Services
{
    public class ActionService : IActionService
    {
        private I___unitOfWork _unitOfWork;
        private I__DBFactory _dbFactory;
        private IcontactFlagRepository _contactFlagRepository;
        private I_entity_crm _entity_crm;

        public ActionService(I___unitOfWork unitOfWork, I__DBFactory dbFactory, IcontactFlagRepository ContactFlagRepository, I_entity_crm entity_crm)
        {
            _unitOfWork = unitOfWork;
            _dbFactory = dbFactory;
            _contactFlagRepository = ContactFlagRepository;
            _entity_crm = entity_crm;
        }

        public bool check_unqiuenessName(string s, int id)
        {
            string sql = "";
            int isZero = 0;
            if (id > 0)   // Existing Record
            {
                sql = String.Format("SELECT Count(*) as recordCount FROM Action WHERE name = @name AND id <> @id");
                isZero = _dbFactory.getContext().Database.SqlQuery<int>(sql, new SqlParameter("name", s),new SqlParameter("id", id)).FirstOrDefault();
            }
            else
            {
                sql = String.Format("SELECT Count(*) as recordCount FROM Action WHERE name = @name");
                isZero = _dbFactory.getContext().Database.SqlQuery<int>(sql, new SqlParameter("name", s)).FirstOrDefault();
            }            
            
            return (isZero == 0) ? true : false;
        }

        public bool check_unqiuenessName_ActionCategory(string s, int id)
        {
            string sql = "";
            int isZero = 0;
            if (id > 0)   // Existing Record
            {
                sql = String.Format("SELECT Count(*) as recordCount FROM ActionCategory WHERE name = @name AND id <> @id");
                isZero = _dbFactory.getContext().Database.SqlQuery<int>(sql, new SqlParameter("name", s), new SqlParameter("id", id)).FirstOrDefault();
            }
            else
            {
                sql = String.Format("SELECT Count(*) as recordCount FROM ActionCategory WHERE name = @name");
                isZero = _dbFactory.getContext().Database.SqlQuery<int>(sql, new SqlParameter("name", s)).FirstOrDefault();
            }
            return (isZero == 0) ? true : false;
        }

        public List<ActionCategory_ext> get_all_ActionCategory(string where, string orderBy, int pageSize, int pageNo)
        {
            string sql = (!string.IsNullOrEmpty(where) ? string.Format("SELECT * FROM ActionCategory WHERE {0}", where) : "SELECT * FROM ActionCategory");

            var q = _dbFactory.getContext().Database.SqlQuery<ActionCategory_ext>(String.Format("EXEC dbo.get_pagedDataSet '{0}', '{1}', {2}, {3}", sql, orderBy, pageSize, pageNo));

            return q.ToList();
        }
        public List<Action_ext> get_all_Actions(string where, string orderBy, int pageSize, int pageNo)
        {
            string sql = (!string.IsNullOrEmpty(where) ? string.Format("SELECT * FROM Action WHERE {0}", where) : "SELECT * FROM Action");

            var q = _dbFactory.getContext().Database.SqlQuery<Action_ext>(String.Format("EXEC dbo.get_pagedDataSet '{0}', '{1}', {2}, {3}", sql, orderBy, pageSize, pageNo));

            return q.ToList();
        }
        public List<ActionPeople_ext> GetActionPeopleWithCount(int pid, int page, int pageSize, QueryRuntimeSort sort, int? categoryId, string date)
        {
            string sortDir = sort?.dir ?? "desc";
            string sortField = sort?.field ?? "updatedAtUtc";
            // confirm that sort field is valid property
            if (new ActionPeople_ext().GetType().GetProperty(sortField) == null)
            {
                sortField = "updatedAtUtc";
            }
            string orderBy = sortField + " " + sortDir;
            string sql = string.Format(@"SELECT AP.*,A.[name] AS ActionName, AC.[name] AS CategoryName, S.SRCECODE AS srccode FROM ActionPeople AP INNER JOIN Action A ON A.id=AP.actionId INNER JOIN ActionCategory AC ON AC.id=A.categoryId LEFT OUTER JOIN SOURCE S ON AP.srceId=S.SRCEID WHERE AP.PID={0} ", pid);
            if (categoryId != null && categoryId != 0)
            {
                sql += " AND A.categoryId = " + categoryId.ToString();
            }
            string final_sql = String.Format("EXEC dbo.get_pagedDataSet '{0}', '{1}', {2}, {3}", sql, orderBy, pageSize, page);
            var q = _dbFactory.getContext().Database.SqlQuery<ActionPeople_ext>(final_sql);

            return q.ToList();
        }
        //public Tuple<int, List<ActionPeople>> GetActionPeopleWithCount(
        //    int pid, int page, int pageSize, QueryRuntimeSort sort, int? categoryId, string date)
        //{
        //    // start query
        //    var q = _entity_crm
        //        .All<ActionPeople>()
        //        .Where(APFilterCondition(pid, categoryId, date));
        //    // get total count
        //    int count = q.Count();
        //    string sortDir = sort?.dir ?? "desc";
        //    string sortField = sort?.field ?? "updatedAtUtc";
        //    // confirm that sort field is valid property
        //    if (new ActionPeople().GetType().GetProperty(sortField) == null)
        //    {
        //        sortField = "updatedAtUtc";
        //    }
        //    // do sorting and paging to get list
        //    List<ActionPeople> list = q.dynamicSortBy(sortField, sortDir == "desc")
        //        .AsEnumerable()
        //        .Skip((page - 1) * pageSize)
        //        .Take(pageSize)
        //        .ToList();

        //    return new Tuple<int, List<ActionPeople>>(count, list);
        //}
        public Tuple<int, List<ActionPeople_export>> GetActionPeopleWithCountForExport(
            int pid, int page, int pageSize, QueryRuntimeSort sort, int? categoryId, string date)
        {
            // start query
            var q = _entity_crm
                .All<ActionPeople>()
                .Where(APFilterCondition(pid, categoryId, date));
            // get total count
            int count = q.Count();
            string sortDir = sort?.dir ?? "desc";
            string sortField = sort?.field ?? "updatedAtUtc";
            // confirm that sort field is valid property
            if (new ActionPeople().GetType().GetProperty(sortField) == null)
            {
                sortField = "updatedAtUtc";
            }
            // do sorting and paging to get list
            List<ActionPeople> list = q.dynamicSortBy(sortField, sortDir == "desc")
                .AsEnumerable()
                .Skip((page - 1) * pageSize)
                .Take(pageSize)
                .ToList();

            Mapper.CreateMap<ActionPeople, ActionPeople_export>();
            List<ActionPeople_export> results = list.Select(a => Mapper.Map<ActionPeople, ActionPeople_export>(a)).ToList();
            
            return new Tuple<int, List<ActionPeople_export>>(count, results);
        }
        private Expression<Func<ActionPeople, bool>> APFilterCondition(int pid, int? categoryId, string date)
        {
            // build expression
            Expression<Func<ActionPeople, bool>> filterExpression =
               a => a.PID == pid
                   && (categoryId == null || a.Action.categoryId == categoryId);

            if (string.IsNullOrEmpty(date))
            {
                return filterExpression;
            }
            else
            {
                // get date expression from DateFilterFactory
                Expression<Func<ActionPeople, bool>> dateExpression =
                    new DateFilterFactory<ActionPeople>(date, "updatedAtUtc", false, "updatedAtUtc").Predicate;
                // conjoin lambdas and return
                return filterExpression.AndAlsoSafe(dateExpression);
            }
        }

        public string MassDeleteAction(int actionid, int UID, int maxAllowed)
        {
            var q = _dbFactory.getContext().Database.SqlQuery<SPResult>(String.Format("EXEC dbo.bt_remove_action_and_itsRelated {0},{1},{2}", actionid, UID, maxAllowed));
            return q.FirstOrDefault().RESULT;
        }
        public string MassDeleteActionCategory(int actionid, int UID, int maxAllowed)
        {
            var q = _dbFactory.getContext().Database.SqlQuery<SPResult>(String.Format("EXEC dbo.bt_remove_actioncategory_and_itsRelated {0},{1},{2}", actionid, UID, maxAllowed));
            return q.FirstOrDefault().RESULT;
        }
    }
}