﻿using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Web.Helpers;

namespace cmdiapp.n.core.Areas.quickdash.Domain.Models
{
    public class SingleSeriesConverter : IPanelDataConverter
    {
        public ConvertedPanelData Convert(dynamic data)
        {
            var dataTable = new DataTable();
            var formatting = new List<ExportFormatting>();
            dataTable.Columns.AddRange(
                new DataColumn[3]
                {
                    new DataColumn("Name", typeof(string)),
                    new DataColumn("Value", typeof(decimal)),
                    new DataColumn("SubLabel", typeof(string))
                });

            List<NameValue> results = GetNameValues(data);
            int valueColumnIndex = dataTable.Columns.IndexOf("Value");
            foreach (var item in results)
            {
                if (!string.IsNullOrEmpty(item?.Name) && item?.Value != null)
                {
                    dataTable.Rows.Add(new object[3] { item.Name, item.Value, item.SubLabel });

                    if (item.Format != ExportFormatType.None)
                    {
                        // Add one to row index to account for header row.
                        AddFormattedRow(formatting, valueColumnIndex, dataTable.Rows.Count, item.Format);
                    }
                }
            }

            return new ConvertedPanelData()
            {
                DataTable = dataTable,
                Formatting = formatting
            };
        }

        private List<NameValue> GetNameValues(dynamic data)
        {
            var list = new List<NameValue>();

            if (!ConverterUtil.HasProperty(data, "results"))
            {
                return list;
            }

            var results = data.results;
            IEnumerable<object> enumerableResults = new List<object>();

            if (results is DynamicJsonArray)
            {
                enumerableResults = (DynamicJsonArray)results;
            } else if (results is JArray)
            {
                enumerableResults = (JArray)results;
            }

            foreach(var item in enumerableResults)
            {
                var dynamicItem = (dynamic)item;
                var extra = ConverterUtil.HasProperty(dynamicItem, "extra") ? dynamicItem.extra : null;
                list.Add(new NameValue()
                {
                    Name = ConverterUtil.HasProperty(dynamicItem, "name") ? dynamicItem.name : "",
                    Value = ConverterUtil.HasProperty(dynamicItem, "value") ? dynamicItem.value : null,
                    SubLabel = ConverterUtil.HasProperty(extra, "sublabel") ? extra.sublabel : null,
                    Format = ConverterUtil.HasProperty(extra, "format")
                        ? GetExportFormat((string)extra.format)
                        : ExportFormatType.None
                });
            }

            return list;
        }

        private ExportFormatType GetExportFormat(string format)
        {
            switch (format)
            {
                case "n":
                    return ExportFormatType.Number;
                case "c":
                    return ExportFormatType.Money;
                case "d":
                    return ExportFormatType.Date;
                default:
                    return ExportFormatType.None;
            }
        }

        private void AddFormattedRow(
            List<ExportFormatting> formatting,
            int columnIndex,
            int rowIndex,
            ExportFormatType format)
        {
            var existingPreviousRow = formatting
                .Find(item => item.Format == format && item.RangeRowEnd == (rowIndex - 1));
            if (existingPreviousRow != null)
            {
                existingPreviousRow.RangeRowEnd++;
            }
            else
            {
                formatting.Add(new ExportFormatting()
                {
                    RangeRowStart = rowIndex,
                    RangeRowEnd = rowIndex,
                    RangeColumnStart = columnIndex,
                    RangeColumnEnd = columnIndex,
                    Format = format
                });
            }

        }

        private class NameValue
        {
            public string Name { get; set; }
            public dynamic Value { get; set; }
            public string SubLabel { get; set; }
            public ExportFormatType Format { get; set; }
        }
    }
}