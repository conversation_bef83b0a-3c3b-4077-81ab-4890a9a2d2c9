﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace cmdiapp.n.core.Domain.ViewModels
{
    public class rdmFLAG : iItemType
    {
        public int FLAGID { get; set; }

        public string FLAG { get; set; }

        public string FLAGDESC { get; set; }

        public string COMMENT { get; set; }

        public DateTime? LASTUSED { get; set; }

        public DateTime? UPDATEDON { get; set; }
        public bool? TOPFLAG { get; set; }
    }

    public class rdmFLAGTest : iItemType
    {
        public int FLAGID { get; set; }

        public string FLAG { get; set; }

        public string FLAGDESC { get; set; }

        public string COMMENT { get; set; }

        public DateTime? LASTUSED { get; set; }

        public DateTime? UPDATEDON { get; set; }

        public bool? Discontinued { get; set; }

        public int? CategoryID { get; set; }

        public virtual cmdiapp.n.core.Areas.crm.Domain.Models.Category Category { get; set; }
    }

    public class rlkEVNTSTATUS : iItemType
    {
        public short STATUS { get; set; }

        public string DESCRIP { get; set; }
    }

}