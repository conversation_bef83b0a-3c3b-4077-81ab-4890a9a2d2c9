﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.Mvc;
using System.Configuration;

using Ninject;
using Ninject.Web.Mvc;

using cmdiapp.n.core.Core.App_Start;
using cmdiapp.n.core.Areas.crm.Domain;
using cmdiapp.n.core.Areas.crm.Domain.Models;
using cmdiapp.n.core.Areas.crm.Domain.ViewModels;
using cmdiapp.n.core.Areas.crm.Domain.Repositories;
using cmdiapp.n.core.Areas.crm.Domain.Services;

namespace cmdiapp.n.core.Areas.crm.Presentation.Controllers
{
    public class testController : Controller
    {
        [Authorize]
        public ActionResult Index()
        {
            /*
            IcustomDataService _rec = NinjectMVC.kernel.Get<IcustomDataService>();
            IEnumerable<fundraiser_snapshot_weekly> test = _rec.get_fundraiser_snapshot_weekly(35000);
            List<fundraiser_snapshot_weekly> _fundraisers = test.ToList();
            return View();
             */

            return View();
        }

    }
}
