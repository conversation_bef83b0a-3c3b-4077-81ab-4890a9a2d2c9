﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Data.Linq;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

using cmdiapp.n.core.Areas.crm.Domain.Data;
using cmdiapp.n.core.Domain.ViewModels;

namespace cmdiapp.n.core.Areas.crm.Domain.Models
{
    [Table("lkPEOCODE")]
    public class PeopleCode : _entityBase_crm, iItemType
    {
        [Column]
        [Key]
        public short PEOCODEID { get; set; }
        
        [Column]
        public string PEOCODE { get; set; }
        
        [Column]
        public string DESCRIP { get; set; }
        
        [Column]
        public byte DEFFLAG { get; set; }

        [Column]
        public byte SYSTEM { get; set; }
    }    
}