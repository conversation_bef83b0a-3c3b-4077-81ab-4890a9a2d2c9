﻿using cmdiapp.n.core.Areas.query.Domain.Models;
using System.Collections.Generic;
using System.Data;

namespace cmdiapp.n.core.Areas.crm.Domain.Services.implementation
{
    public class QueryProxy : IQueryService
    {
        public DataTable ToDataTable(QueryInstanceInfo instanceInfo, QueryRuntimeParams runtimeParams)
        {
            return Query.runToDataTable(instanceInfo, runtimeParams);
        }

        public List<object> ToList(QueryInstanceInfo instanceInfo, QueryRuntimeParams runtimeParams)
        {
            return Query.run(instanceInfo, runtimeParams)?.results ?? new List<object>();
        }
    }
}