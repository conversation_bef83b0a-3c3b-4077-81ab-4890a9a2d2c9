﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

using Ninject;
using Ninject.Web.Mvc;

using cmdiapp.n.core.Core.App_Start;
using cmdiapp.n.core.Areas.crm.Domain.Models;
using cmdiapp.n.core.Areas.crm.Domain.ViewModels;
using cmdiapp.n.core.Areas.crm.Domain.Repositories;
namespace cmdiapp.n.core.Areas.crm.Domain.Services
{
    public class flagService : IflagService
    {
        private I___unitOfWork _unitOfWork;
        private I__DBFactory _dbFactory;
        private IflagRepository _flagRepository;
        
        public flagService(I___unitOfWork unitOfWork, I__DBFactory dbFactory, IflagRepository FlagRepository)
        {
            _unitOfWork = unitOfWork;
            _dbFactory = dbFactory;
            _flagRepository = FlagRepository;
        }

        public dmFLAG get_flag(int flagId)
        {
            string sql = string.Format("SELECT * FROM dmFLAG WHERE FLAGID={0}", flagId);
            var q = _dbFactory.getContext().Database.SqlQuery<dmFLAG>(sql);
            return q.FirstOrDefault();
        }

        public int DeleteTargetChannels(string channels, int FlagId)
        {
            string sql = string.Format("Delete from jtChannelSuppressionFlag where FlagId = {0} and channelId NOT IN ({1})", FlagId, channels);
            int q = _dbFactory.getContext().Database.ExecuteSqlCommand(sql);
            return q;
        }

        public int DeleteAllTargetChannels(int FlagId)
        {
            string sql = string.Format("Delete from jtChannelSuppressionFlag where FlagId = {0}", FlagId) ;
            int q = _dbFactory.getContext().Database.ExecuteSqlCommand(sql);
            return q;
        }
        
        public int getFlagId(string flag)
        {
            string sql = string.Format("SELECT * FROM dmFLAG WHERE UPPER(FLAG) = '{0}'", flag.ToUpper());
            var q = _dbFactory.getContext().Database.SqlQuery<dmFLAG>(sql);
            return q.FirstOrDefault().FLAGID;
        }


        public bool check_unqiueness(dmFLAG _dmFlag)
        {
            // take care of apostrophe
            string v = _dmFlag.FLAG.Replace("'", "''");
            string sql = "";

            if (_dmFlag.FLAGID > 0)   // Existing Record
                sql = String.Format("SELECT Count(*) as recordCount FROM dmFLAG WHERE FLAG = '{0}' AND FLAGID <> {1}", v, _dmFlag.FLAGID);
            else
                sql = String.Format("SELECT Count(*) as recordCount FROM dmFLAG WHERE FLAG = '{0}'", v);
            
            int isZero = _dbFactory.getContext().Database.SqlQuery<int>(sql).FirstOrDefault();
            return (isZero == 0) ? true : false;
        }

        public int getFlagID(string flagDesc)
        {
            // take care of apostrophe
            string sql = String.Format("SELECT * FROM dmFLAG WHERE UPPER(FLAG) = '{0}'", flagDesc.ToUpper());
            return _dbFactory.getContext().Database.SqlQuery<dmFLAG>(sql).FirstOrDefault().FLAGID;
            
        }

        public bool check_record_exists(int flagId)
        {
            //Check in jtFLAG Table
            string sql = string.Format("SELECT Count(*) as recordCount FROM jtFLAG WHERE FLAGID={0}", flagId);
            var q = _dbFactory.getContext().Database.SqlQuery<UniquenessCheck>(sql);
            int isZero = q.FirstOrDefault().recordCount;
            return (isZero > 0) ? true : false;

        }

        public void Add(dmFLAG _dmFLAG)
        {
            _flagRepository.Add(_dmFLAG);
            _unitOfWork.commit();
        }

        public void Update(dmFLAG _dmFLAG)
        {
            _flagRepository.Update(_dmFLAG);
            _unitOfWork.commit();
        }

        public void Delete(dmFLAG _dmFLAG)
        {
            _flagRepository.Delete(_dmFLAG);
            _unitOfWork.commit();
        }

        //Read all Flags
        public List<dmFLAG> read_all_flags()
        {
            return _flagRepository.All().ToList();
        }

        public List<dmFLAG_ext> get_all_flags(string where, string orderBy, int pageSize, int pageNo)
        {
            string sql = (!string.IsNullOrEmpty(where) ? string.Format("SELECT * FROM dmFLAG WHERE {0}", where) : "SELECT * FROM dmFLAG");

            var q = _dbFactory.getContext().Database.SqlQuery<dmFLAG_ext>(String.Format("EXEC dbo.get_pagedDataSet '{0}', '{1}', {2}, {3}", sql, orderBy, pageSize, pageNo));
            
            return q.ToList();
        }

        public List<dmFLAG_extTest> get_all_flagsAllTest()
        {
            string sql = "SELECT * FROM dmFLAG";

            var q = _dbFactory.getContext().Database.SqlQuery<dmFLAG_extTest>(sql);

            return q.ToList();
        }

        public string MassDeleteFlag(int FlagId, int UID, int maxAllowed)
        {
            var q = _dbFactory.getContext().Database.SqlQuery<SPResult>(String.Format("EXEC dbo.bt_remove_flag_and_itsRelated {0},{1},{2}", FlagId, UID, maxAllowed));
            return q.FirstOrDefault().RESULT;
        }


    }
}