using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Web.Http;
using Microsoft.AspNet.Identity;
using Microsoft.AspNet.Identity.EntityFramework;
using Microsoft.Owin.Host.SystemWeb;
using Microsoft.AspNet.Identity.Owin;

using Ninject;
using Ninject.Web.Mvc;
using System.Configuration;

using cmdiapp.n.core.Core;
using cmdiapp.n.core.Core.App_Start;
using cmdiapp.n.core.Domain.Data;
using cmdiapp.n.core.Domain.Models;
using cmdiapp.n.core.Library;

using cmdiapp.n.core.Areas.apix;

namespace cmdiapp.n.core.Areas.apix.Presentation.api
{
    public class accessController : ApiController
    {
        private ApplicationUserManager _userManager;

        public accessController()
        {
            _userManager = NinjectMVC.kernel.Get<ApplicationUserManager>();
        }

        [HttpGet, Route("v2/access/validateUser/{apiKey}/{refId}/{userName}/{pwd}")]
        public bool validateUser(string apiKey, string refId, string userName, string pwd)
        {   // eg. http://apix.cmdi2.com/v2/access/validateUser/22cb61cb94db3c8c285ae6a1a8aace73c1cf0d76/-1/userId/pwd

            #region [ 1. Validate AppKey(Token) and RefId(dataId) ]
            int refId_i;
            if (!int.TryParse(refId, out refId_i))
                return false;

            if (!Library.cl_apiCallToken.validToken(apiKey, refId_i))
                return false;
            #endregion

            #region [ 2. Validate user credential ]
            var user = _userManager.FindByName(userName);
            if (user == null && userName.IndexOf("@") >= 0)
                user = _userManager.FindByEmail(userName);

            if (user == null)
                return false;

            user = _userManager.Find(user.UserName, pwd);

            return (user != null);
            #endregion
        }

    }
}
