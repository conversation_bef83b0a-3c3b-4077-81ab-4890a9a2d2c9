﻿using System.Runtime.Serialization;

namespace cmdiapp.n.core.Areas.crm.ViewModels
{
    [DataContract]
    public class ExternalApiResult<TResult>
    {
        [DataMember(Name = "isSuccessful")]
        public bool IsSuccessful { get; set; }

        [DataMember(Name = "message")]
        public string Message { get; set; }

        [DataMember(Name = "result")]
        public TResult Result { get; set; }
    }
}