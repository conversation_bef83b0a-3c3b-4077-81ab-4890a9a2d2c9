﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Data;
using System.Linq;
using System.Web.Mvc;
using System.Data.Linq;
using System.ComponentModel.DataAnnotations.Schema;

using cmdiapp.n.core.Domain.ViewModels;
using cmdiapp.n.core.Areas.crm.Domain.Data;
using cmdiapp.n.core.Areas.crm.Core;

namespace cmdiapp.n.core.Areas.crm.Domain.Models
{
    // (view)v_people_1  -  Used for a single PEOPLE View ]]

    [Table("v_people_1")]
    public class PeopleR : _entityBase_crm, iItemType
    {
	    [Column] 
        [Key]
	    public int PID { get; set; }

        [Column]
        public string CHKDGT { get; set; }

        [Column]
        public string FECCMTEID { get; set; }

        [Column]
        public string PEOTYPE { get; set; }

        [Column]
        public string PEOCODE { get; set; }

        [Column]
        public string PEOCLASS { get; set; }

        [Column] 
        public string PREFIX { get; set; }  // 40

        [Column] 
        public string FNAME { get; set; }   // 30

        [Column] 
        public string MNAME { get; set; }   // 20

        [Column] 
        public string LNAME { get; set; }   // 80

        [Column]
        public string SUFFIX { get; set; }  // 20

        [Column]
        public string FULLNAME_ { get; set; }  // 20

        [Column]
        public string SPOUSENAME { get; set; }  // 50

        [Column]
        public string STR1 { get; set; } //75

        [Column]
        public string STR2 { get; set; } //75

        [Column]
        public int? NUM1 { get; set; } 
        
        [Column]
        public string EMPLOYER { get; set; }    // 70

        [Column]
        public string OCCUPATION { get; set; }   // 50

        [Column]
        public string TITLE { get; set; }

        [Column]
        public string ASSISTANT { get; set; }

        [Column]
        public int? TRACKNO { get; set; }

        [Column] 
        public string HPHONE { get; set; }   

        [Column]
        public string WPHONE { get; set; }   

        [Column]
        public string CPHONE { get; set; }   

        [Column]
        public string FAX { get; set; }    

        [Column]
        public string EMAIL { get; set; }

        [Column]
        public string FACEBOOKID { get; set; }

        [Column]
        public string TWITTERID { get; set; }

        [Column]
        public string LINKEDINID { get; set; }

        [Column]
        public string ADDR1 { get; set; }

        [Column]
        public string ADDR2 { get; set; }

        [Column]
        public string STREET { get; set; }

        [Column]
        public string CITY { get; set; }

        [Column]
        public string STATE { get; set; }

        [Column]
        public string ZIP { get; set; }

        [Column]
        public string PLUS4 { get; set; }

        [Column]
        public string Address_ { get; set; }

        [Column]
        public decimal? FGIFT { get; set; }

        [Column]
        public DateTime? FGIFTDTE { get; set; }

        [Column]
        public decimal? HPC { get; set; }

        [Column]
        public DateTime? HPCDTE { get; set; }

        [Column]
        public decimal? LGIFT { get; set; }

        [Column]
        public DateTime? LGIFTDTE { get; set; }

        [Column]
        public int? NOGIFTS { get; set; }

        [Column]
        public decimal? CUMTOT { get; set; }

        [Column]
        public string CTD_LABEL { get; set; }

        [Column]
        public decimal? CTD_AMT { get; set; }

        [Column]
        public int? CTD_NO { get; set; }

        [Column]
        public byte[] PICTURE { get; set; }

        [Column]
        public string BIO { get; set; }

        [Column]
        public decimal? LATITUDE { get; set; }

        [Column]
        public decimal? LONGITUDE { get; set; }

        [Column]
        public string COUNTRY { get; set; }
    }

    [NotMapped]
    public class NearbyFilter
    {
        public string Column { get; set; }
        public string Operator { get; set; }
        public string Type { get; set; }
        public dynamic Value { get; set; }
    }

    [NotMapped]
    public class NearbyExportParam
    {
        public decimal Latitude { get; set; }
        public decimal Longitude { get; set; }
        public int Miles { get; set; }
        public List<NearbyFilter> Filters { get; set; } 
    }

    [NotMapped]
    public class PeopleR_w_distance : PeopleR
    {
        public double? miles { get; set; }
        public string LGIFTACCTCODE { get; set; }
    }

    [NotMapped]
    public class v_people_1_ext1 : PeopleR
    {
        public int count_ { get; set; }

        public Int64 rowNo { get; set; }
    }

    [NotMapped]
    public class PeopleR2 : PeopleR
    {
        //show check digit for RNC
        public string PID_CHKDGT
        {
            get
            {
                if (crmSession.showCheckDigit() == "Y")
                {                    
                    return string.Format("{0}-{1}", PID, CHKDGT);
                }
                else
                {
                    return PID.ToString();
                }
            }
        }
        public RelationInfo spouse { get; set; }
        public string CONDUITID { get; set; }
        public string CONDUITNO { get; set; }

        public int no_of_flags { get; set; }
        public int no_of_keywords { get; set; }
    }

    public class conduitInfoPID
    {
        public string CONDUITID { get; set; }
        public string CONDUITNO { get; set; }
    }

    public class ContactConnection
    {
        public int TRACKNO { get; set; }
                
        public int LEVEL { get; set; }
                
        public int? RECRUITERNO  { get; set; }
                
        public string NAME { get; set; }
                
        public Decimal? GOAL { get; set; }
        
        public Decimal? RAISED { get; set; }

        public Decimal? RAISED_ROLLUP { get; set; }

        public Decimal? PLEDGED { get; set; }
                
    }

    [NotMapped]
    public class v_DupeR_ext1 : DupeR
    {
        public int count_ { get; set; }

        public Int64 rowNo { get; set; }
    }


    [Table("z_dedupe_dupe")]
    public class DupeR : _entityBase_crm, iItemType
    {
        [Column]
        [Key]
        public int dupeId { get; set; }

        [Column]
        public int KEYLINEID { get; set; }

        [Column]
        public int? KEYLINERANK { get; set; }

        [Column]
        public int?  MATCHED { get; set; }

        [Column]
        public int TOTALCOUNT { get; set; }

        [Column]
        public string DUPEKEYLINE { get; set; }

        [Column]
        public string MATCHTYPE { get; set; }

        [Column]
        public int PID { get; set; }

        [Column]
        public string PREFIX { get; set; }

        [Column]
        public string FNAME { get; set; }

        [Column]
        public string NAME { get; set; }

        [Column]
        public string MNAME { get; set; }

        [Column]
        public string LNAME { get; set; }

        [Column]
        public string SUFFIX { get; set; }

        [Column]
        public string OCCUPATION { get; set; }

        [Column]
        public string EMPLOYER { get; set; }

        [Column]
        public string STREET { get; set; }

        [Column]
        public string ADDR1 { get; set; }

        [Column]
        public string CELLPHONE { get; set; }

        [Column]
        public string HOMEPHONE { get; set; }

        [Column]
        public string BUSSPHONE { get; set; }

        [Column]
        public string ADDR2 { get; set; }

        [Column]
        public string CITY { get; set; }

        [Column]
        public string STATE { get; set; }
     
        [Column]
        public string ZIP { get; set; }

        [Column]
        public string PLUS4 { get; set; }

        [Column]
        public string EMAIL { get; set; }

        [Column]
        public decimal? CTDAMT { get; set; }

        [Column]
        public DateTime? LGIFTDTE { get; set; }

        [Column]
        public string cTITLE { get; set; }

        [Column]
        public Byte? KEEP { get; set; }

        [Column]
        public Byte? MERGE { get; set; }

        [Column]
        public bool KEEPR
        {
            get
            {
                return (KEEP == 1 ? true : false);
            }
        }

        [Column]
        public bool MERGER
        {
            get
            {
                return (MERGE == 1 ? true : false);
            }
        }

        [Column]
        public int? OCCURS { get; set; }

        [Column]
        public string SPOUSENAME { get; set; }
        [Column]
        public string FECCMTEID { get; set; }
        [Column]
        public string FACEBOOK { get; set; }
        [Column]
        public string TWITTER { get; set; }
        [Column]
        public string LINKEDIN { get; set; }

    }

    [NotMapped]
    public class PeopleSearchExport : _entityBase_crm, iItemType
    {
        public int PID { get; set; }
        
        public string PEOTYPE { get; set; }
        
        public string PEOCODE { get; set; }
        
        public string PREFIX { get; set; }  // 40
        
        public string FIRSTNAME { get; set; }   // 30
        
        public string MIDDLENAME { get; set; }   // 20

        public string LASTNAME { get; set; }   // 80
        
        public string SUFFIX { get; set; }  // 20
        
        public string TITLE { get; set; }  
        
        public string SPOUSENAME { get; set; }  // 50

        public string FULLSPOUSENAME { get; set; }  // 50
        
        public string EMPLOYER { get; set; }    // 70
    
        public string OCCUPATION { get; set; }   // 50
        
        public DateTime? DOB { get; set; }

        public string FORMSALUT { get; set; }

        public string INFSALUT { get; set; }

        public string MAILSALUT { get; set; }

        public string MAILNAME { get; set; }

        public string PRIMEMAIL { get; set; }

        public string ASSISTANT { get; set; }

        public string STREET { get; set; }

        public string ADDR1 { get; set; }

        public string ADDR2 { get; set; }

        public string CITY { get; set; }

        public string STATE { get; set; }
        public string ZIP { get; set; }
        public string PLUS4 { get; set; }
        public string REGION { get; set; }
        public string METROAREA { get; set; }
        
        public string HMPHN { get; set; }
        
        public string BSPHN { get; set; }
        
        public string CELL { get; set; }
        
        public string FAX { get; set; }
        
        public string EMAIL { get; set; }

        public string ASSISTANTPHN { get; set; }
        public string ASSISTANTEMAIL { get; set; }
        public string DIRECTPHN { get; set; }
        public int TRACKNO { get; set; }
        public string COUNTY { get; set; }
        public int CONDUITNO { get; set; }
        //public string STR1 { get; set; }
        //public string TBD { get; set; }
        public string CD { get; set; }
        public string SDCODE { get; set; }
        public string LDCODE { get; set; }
        public string FECID { get; set; }

        public string Contact_Prefix { get; set; }
        public string Contact_FirstName { get; set; }
        public string Contact_MiddleName { get; set; }
        public string Contact_LastName { get; set; }
        public string Contact_Suffix { get; set; }
        public string Contact_Street { get; set; }
        public string Contact_Address1 { get; set; }
        public string Contact_City { get; set; }
        public string Contact_State { get; set; }
        public string Contact_Zip { get; set; }
        public string Contact_Email { get; set; }
        public string Contact_CellPhone { get; set; }
        public string Contact_BusPhone { get; set; }
        public string Contact_HomePhone { get; set; }

        public string PLEDGEAMT { get; set; }
        public string FULFILLED { get; set; }
        public string OUTSTANDING { get; set; }


        public DateTime? FIRSTGIFTDATE { get; set; }
        public decimal? FIRSTGIFT { get; set; }
        public DateTime? LASTGIFTDATE { get; set; }
        public decimal? LASTGIFT { get; set; }
        public decimal? HIGHESTGIFT { get; set; }
        public DateTime? HIGHESTGIFTDTE { get; set; }
        public string NO_GIFTS { get; set; }
        public decimal? TOTALGIFT { get; set; }
        public decimal? YTDGIFT { get; set; }
        public decimal? CTDGIFT { get; set; }
        public decimal? PREV1YRAMT { get; set; }
        public decimal? PREV2YRAMT { get; set; }
        public decimal? PREV3YRAMT { get; set; }
        public decimal? PREV4YRAMT { get; set; }
        public decimal? PREV5YRAMT { get; set; }


        public decimal? CTDGIFTALL { get; set; }
        public decimal? DIRECTP { get; set; }
        public decimal? DIRECTG { get; set; }

        public string CLUB { get; set; }
        public string CLUBSTATUS { get; set; }
        public string CLUBRENEW { get; set; }
        public decimal? PREVCYCTOTAL { get; set; }
        public int PREVCYC_NOGIFTS { get; set; }

        public DateTime? FEC_LATESTREPORT { get; set; }
        public decimal? FEC_LATESTREPORT_CASHONHAND { get; set; }

        public string IdToken { get; set; }

    }
}
