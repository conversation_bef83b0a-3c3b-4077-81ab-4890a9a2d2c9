﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Data;
using System.Linq;
using System.Web.Mvc;
using System.Data.Linq;
using System.ComponentModel.DataAnnotations.Schema;

using cmdiapp.n.core.Domain.ViewModels;
using cmdiapp.n.core.Areas.crm.Domain.Data;

namespace cmdiapp.n.core.Areas.crm.Domain.Models
{
    // (view)v_people_sum  -  Used to list giving summaries per donor ]]

    [Table("v_people_sum")]
    public class SummaryR : _entityBase_crm, iItemType
    {
        [Column]
        public int PID { get; set; }

        [Column]
        public string FUNDCODE { get; set; }

        [Column]
        public decimal? CUMTOT { get; set; }

        [Column]
        public decimal? REMAIN { get; set; }
    }    
}
