﻿using cmdiapp.n.core.Areas.crm.Domain.Models;
using cmdiapp.n.core.Areas.datax.Domain.Models.ViewModels;
using cmdiapp.n.core.Areas.query.Domain.Models;
using cmdiapp.n.core.Domain.ViewModels;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace cmdiapp.n.core.Areas.datax.Domain.Services
{
    /// <summary>
    /// Provides methods for CRUD operations against
    /// Cremir email lists
    /// </summary>
    public interface ICremirEmailListService
    {
        /// <summary>
        /// Creates a new email list
        /// </summary>
        /// <param name="emailList">the email list to create</param>
        /// <param name="queryInstance">the query instance to generate SQL from for selecting records</param>
        /// <returns>a <see cref="Task"/> indicating success of creation</returns>
        /// <exception cref="ArgumentNullException">if <paramref name="emailList"/> is null</exception>
        Task<genericResponse> CreateAsync(CremirEmailList emailList, QueryInstanceInfo queryInstance);
        /// <summary>
        /// Returns a particular email list with the given account and list Ids
        /// </summary>
        /// <param name="accountId">the account Id of the email list</param>
        /// <param name="listId">the id of the email ist</param>
        /// <returns>a <see cref="Task"/> resulting in the email list</returns>
        Task<CremirEmailList> GetAsync(int accountId, string listId);
        /// <summary>
        /// Returns all email lists with the given account Id
        /// </summary>
        /// <param name="accountId">the account Id</param>
        /// <returns>a <see cref="Task"/> resulting in a list of email lists with the given account Id</returns>
        Task<List<CremirEmailList>> GetByAccountAsync(int accountId);
        /// <summary>
        /// Updates an email list
        /// </summary>
        /// <param name="emailList">the email list to update</param>
        /// <returns>a <see cref="Task"/> indicating success of the update</returns>
        /// <exception cref="ArgumentNullException">if <paramref name="emailList"/> is null</exception>
        Task<genericResponse> UpdateAsync(CremirEmailList emailList);
        /// <summary>
        /// Deletes an email list
        /// </summary>
        /// <param name="accountId">the account Id of the list</param>
        /// <param name="listId">the list Id of the list</param>
        /// <returns>a <see cref="Task"/> indicating success of deletion</returns>
        Task<genericResponse> DeleteAsync(int accountId, string listId);
        /// <summary>
        /// Gets available <see cref="EmailListCustomField"/>s
        /// </summary>
        /// <returns>list of available <see cref="EmailListCustomField"/>s</returns>
        List<EmailListCustomField> GetCustomFields();
    }
}
