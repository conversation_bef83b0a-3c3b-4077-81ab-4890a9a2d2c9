﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Data;
using System.Linq;
using System.Web.Mvc;
using System.Data.Linq;
using System.ComponentModel.DataAnnotations.Schema;

using cmdiapp.n.core.Domain.ViewModels;
using cmdiapp.n.core.Areas.crm.Domain.Data;

namespace cmdiapp.n.core.Areas.crm.Domain.Models
{
    // (view)v_people_mony  -  Used to list all gifts per donor ]]

    [Table("v_people_mony")]
    public class MonyR : _entityBase_crm, iItemType
    {
        [Column]
        public int PID { get; set; }

        [Column]
        [Key]
        public int MID { get; set; }

        [Column]
        public Nullable<DateTime> BATCHDTE { get; set; }

        [Column]
        public string BATCHNO { get; set; }

        [Column]
        public decimal? AMT { get; set; }

        [Column]
        public Nullable<int> TRACKNO { get; set; }

        [Column]
        public string MONYTYPE { get; set; }

        [Column]
        public string FUNDCODE { get; set; }

        [Column]
        public string SRCECODE { get; set; }

        [Column]
        public string SRCEDESC { get; set; }

        [Column]
        public string PKGECODE { get; set; }

        [Column]
        public string PKGEDESC { get; set; }

        [Column]
        public string PROGTYPE { get; set; }

        [Column]
        public string PROGDESC { get; set; }

        [Column]
        public string CENTERCODE { get; set; }

        [Column]
        public string CENTERDESC { get; set; }

        [Column]
        public string CAMPGNCODE { get; set; }

        [Column]
        public string CAMPGNDESC { get; set; }

        [Column]
        public string ADJTYPE { get; set; }

        [Column]
        public string ADJDESC { get; set; }

        [Column]
        public byte COUNTER { get; set; }

        [Column]
        public byte SOFTMONEY { get; set; }

        [Column]
        public string FUNDORGNAME { get; set; }

        [Column]
        public string FUNDORGADDR { get; set; }

        [Column]
        public string EXCEP { get; set; }

        [Column]
        public string GIFTTYPE { get; set; }

        [Column]
        public Int16? MONYCODE { get; set; }

    }

    public class MonyR_NP : _entityBase_crm, iItemType
    {
        [Column]
        public int PID { get; set; }

        [Column]
        [Key]
        public int MID { get; set; }

        [Column]
        public Nullable<DateTime> BATCHDTE { get; set; }

        [Column]
        public string BATCHNO { get; set; }

        [Column]
        public decimal? AMT { get; set; }

        [Column]
        public Nullable<int> TRACKNO { get; set; }

        [Column]
        public string MONYTYPE { get; set; }

        [Column]
        public string FUNDCODE { get; set; }

        [Column]
        public string SRCECODE { get; set; }

        [Column]
        public string SRCEDESC { get; set; }

        [Column]
        public string PKGECODE { get; set; }

        [Column]
        public string PKGEDESC { get; set; }

        [Column]
        public string PROGTYPE { get; set; }

        [Column]
        public string PROGDESC { get; set; }

        [Column]
        public string CENTERCODE { get; set; }

        [Column]
        public string CENTERDESC { get; set; }

        [Column]
        public string CAMPGNCODE { get; set; }

        [Column]
        public string CAMPGNDESC { get; set; }

        [Column]
        public string ADJTYPE { get; set; }

        [Column]
        public string ADJDESC { get; set; }

        [Column]
        public byte COUNTER { get; set; }

        [Column]
        public byte SOFTMONEY { get; set; }

        [Column]
        public string FUNDORGNAME { get; set; }

        [Column]
        public string FUNDORGADDR { get; set; }

        [Column]
        public string GIFTTYPE { get; set; }

        [Column]
        public Int16? MONYCODE { get; set; }
    }

    [Table("iGenStatement")]
    public class MonyStatement : _entityBase_crm, iItemType
    {
        [Column]
        public int PID { get; set; }

        [Column]
        public string ROOT { get; set; }

        [Column]
        public string DONOR { get; set; }

        [Column]
        [Key]
        public int MID { get; set; }

        [Column]
        public Nullable<DateTime> BATCHDTE { get; set; }

        [Column]
        public string ADJFLAG { get; set; }

        [Column]
        public string ADJDESC { get; set; }

        [Column]
        public DateTime? ADJDTE { get; set; }

        [Column]
        public string ACCTCODE { get; set; }

        [Column]
        public decimal? OAMT { get; set; }

        [Column]
        public decimal? ADJAMT { get; set; }

        [Column]
        public decimal? AMT { get; set; }

        [Column]
        public string MINE { get; set; }

        [Column]
        public string FUNDORGNAME { get; set; }

        [Column]
        public string FUNDORGADDR { get; set; }
        
        [Column]
        public int GROUPNO { get; set; }

        [Column]
        public int ORDERNO { get; set; }
    }

    [NotMapped]
    public class MonyR_ext1 : MonyR
    {
        public int count_ { get; set; }

        public Int64 rowNo { get; set; }
    }

    [NotMapped]
    public class MonyStatement_ext1 : MonyStatement
    {
        public int count_ { get; set; }

        public Int64 rowNo { get; set; }
    }

    public class v_donor_donation_list : iItemType
    {
        public int MID { get; set; }
        public int PID { get; set; }
        public string PREFIX { get; set; }
        public string FNAME { get; set; }
        public string MNAME { get; set; }
        public string LNAME { get; set; }
        public string SUFFIX { get; set; }
        public string FULLNAME { get; set; }
        public string EMPLOYER { get; set; }
        public string STREET { get; set; }
        public string ADDR1 { get; set; }
        public string ADDR2 { get; set; }
        public string CITY { get; set; }
        public string STATE { get; set; }
        public string ZIP { get; set; }
        public string CiStZip { get; set; }
        public string HMPH { get; set; }
        public string BUSPH { get; set; }
        public string CELLPH { get; set; }
        public string EMAIL { get; set; }
        public string PROGTYPE { get; set; }
        public string PROGRAM { get; set; }
        public string PKEGCODE { get; set; }
        public string PACKAGE { get; set; }
        public string SRCECODE { get; set; }
        public string SOURCE { get; set; }
        public string FUNDCODE { get; set; }
        public string FUND { get; set; }
        public string ACCOUNTCODE { get; set; }
        public DateTime BATCHDTE { get; set; }
        public decimal AMT { get; set; }
        public Nullable<int> TRACKNO { get; set; }
    }

    [NotMapped]
    public class v_donor_donation_list_ext : v_donor_donation_list
    {
        public int count_ { get; set; }

        public Int64 rowNo { get; set; }
    }

    public class MonyForAdj : iItemType
    {
        public int MID { get; set; }
        public DateTime BATCHDTE { get; set; }
        public string BATCHNO { get; set; }
        public decimal AMT { get; set; }
        public string FUNDCODE { get; set; }
        public string SRCECODE { get; set; }
        public string MONYTYPE { get; set; }
        public string EXCEP { get; set; }
        public bool? SELECTED { get; set; }
        public decimal? ADJAMT { get; set; }
    }
}
