﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Data;
using System.Linq;
using System.Web.Mvc;
using System.Data.Linq;
using System.ComponentModel.DataAnnotations.Schema;
using cmdiapp.n.core.Domain.ViewModels;
using cmdiapp.n.core.Areas.crm.Domain.Data;

namespace cmdiapp.n.core.Areas.crm.Domain.Models
{
    public class donateNow : _entityBase_crm, iItemType
    {
        [Key]
        [Column]
        public int PID { get; set; }
        [Column]
        public string PEOTYPE { get; set; }
        [Column]
        public string PEOCODE { get; set; }
        [Column]
        public int? TRACKNO { get; set; }
        [Column]
        public string PREFIX { get; set; }
        [Column]
        public string FNAME { get; set; }
        [Column]
        public string MNAME { get; set; }
        [Column]
        public string LNAME { get; set; }
        [Column]
        public string SUFFIX { get; set; }
        [Column]
        public string FULLNAME { get; set; }
        [Column]
        public string INDUSTRY { get; set; }
        [Column]
        public string EMPLOYER { get; set; }
        [Column]
        public string OCCUPATION { get; set; }
        [Column]
        public string SALUTATION { get; set; }
        [Column]
        public string INFSALUT { get; set; }
        [Column]
        public string PRESSALUT { get; set; }
        [Column]
        public string MAILNAME { get; set; }
        [Column]
        public string SPOUSENAME { get; set; }
        [Column]
        public string ASSISTANT { get; set; }
        [Column]
        public string TITLE { get; set; }
        [Column]
        public string FECCMTEID { get; set; }
        [Column]
        public Boolean? PRIMEMAIL { get; set; }
        [Column]
        public string ADDRTYPE { get; set; } // H: Home, B: Business
        [Column]
        public string STREET { get; set; }
        [Column]
        public string ADDR1 { get; set; }
        [Column]
        public string ADDR2 { get; set; }
        [Column]
        public string CITY { get; set; }
        [Column]
        public string STATE { get; set; }
        [Column]
        public string ZIP { get; set; }
        [Column]
        public string PLUS4 { get; set; }
        [Column]
        public string FULLADDR { get; set; }
        [Column]
        public string STATECOUNTY { get; set; }
        [Column]
        public string HMPH { get; set; }
        [Column]
        public string BUSPH { get; set; }
        [Column]
        public string CELLPH { get; set; }
        [Column]
        public string EMAIL { get; set; }
        [Column]
        public string URL { get; set; }
        [Column]
        public string FAX { get; set; }

        [Column("_updating_uid")]
        public Int16? updating_uid { get; set; }
        [Column]
        public string FACEBOOKID { get; set; }
        [Column]
        public string TWITTERID { get; set; }
        [Column]
        public string BIO { get; set; }

        [NotMapped]
        public DateTime giftdte { get; set; }
        [NotMapped]
        public Decimal? giftamt { get; set; }
        [NotMapped]
        public string cardno { get; set; }
        [NotMapped]
        public string expmo { get; set; }
        [NotMapped]
        public string expyr { get; set; }
        [NotMapped]
        public string cvv { get; set; }
        [NotMapped]
        public string srcecode { get; set; }
        [NotMapped]
        public string cctrackno { get; set; }
        [NotMapped]
        public int OneTime_Monthly { get; set; } //true = one time, false monthly
        [NotMapped]
        public string comment { get; set; }
        [NotMapped]
        public string fundcode { get; set; }

    }
    
}