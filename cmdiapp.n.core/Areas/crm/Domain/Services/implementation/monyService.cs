﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Reflection;
using System.Web;

using Ninject;
using Ninject.Web.Mvc;

using cmdiapp.n.core.Core.App_Start;
using cmdiapp.n.core.Areas.crm.Domain.Models;
using cmdiapp.n.core.Areas.crm.Domain.ViewModels;
using cmdiapp.n.core.Areas.crm.Domain.Repositories;
using cmdiapp.n.core.Areas.crm.Domain.Data;
using cmdiapp.n.core.Areas.query.Domain.Models;
using cmdiapp.n.core.Library;
using cmdiapp.n.core.Areas.crm.ViewModels;
using cmdiapp.n.core.Domain.ViewModels;
using System.Threading.Tasks;

namespace cmdiapp.n.core.Areas.crm.Domain.Services
{
    public class monyService : ImonyService
    {
        private I___unitOfWork _unitOfWork;
        private I__DBFactory _dbFactory;
        private IjtFNDRGRPRepository _jtFNDRGRPRepository;
        private I_entity_crm _entity_crm;

        public monyService(I___unitOfWork unitOfWork, I__DBFactory dbFactory, IjtFNDRGRPRepository JtFNDRGRPRepository, I_entity_crm entity_crm)
        {
            _unitOfWork = unitOfWork;
            _dbFactory = dbFactory;
            _jtFNDRGRPRepository = JtFNDRGRPRepository;
            _entity_crm = entity_crm;
        }

        public List<v_mony_1> get_v_mony_1(int pid)
        {
            string sql = string.Format("SELECT * FROM v_mony_1 WHERE PID={0}", pid);

            var q = _dbFactory.getContext().Database.SqlQuery<v_mony_1>(sql);

            return q.ToList();
        }

        public class UpdatedBatchInformation
        {
            public string BATCHNO { get; set; }


        }


        public string availableBatchNo(string date)
        {
            
            //string final_sql = String.Format("SELECT dbo.[get_NextAvailableBatchNumber] ('{0}') AS BATCHNO", (date != "1/1/0001" && !string.IsNullOrEmpty(date) ? date : null));
            //string final_sql = String.Format("SELECT dbo.[get_NextAvailableBatchNumber]() AS BATCHNO");
            string final_sql = String.Format("SELECT dbo.[get_NextAutoGenBatchNumber]() AS BATCHNO");

            var q = _dbFactory.getContext().Database.SqlQuery<UpdatedBatchInformation>(final_sql);
            return q.ToList().FirstOrDefault().BATCHNO;
            
        }
        
        #region [[ By  Bhavesh ]]
        public void Add(jtFNDRGRP _jtFNDRGRP)
        {
            _jtFNDRGRPRepository.Add(_jtFNDRGRP);
            _unitOfWork.commit();
            
            //_entity_crm.Add(_jtFNDRGRP);
            //_entity_crm.CommitChanges();
        }

        public void Delete(jtFNDRGRP _jtFNDRGRP)
        {
            _jtFNDRGRPRepository.Delete(_jtFNDRGRP);
            _unitOfWork.commit();
            
            //_entity_crm.Update(_jtFNDRGRP);
            //_entity_crm.CommitChanges();
        }
        
        public jtFNDRGRP get_jtfndgroup(int FUNDRAISEID, int FNDRGRPID)
        {
            string sql = string.Format("SELECT * FROM jtFNDRGRP WHERE FUNDRAISEID={0} and FNDRGRPID={1}", FUNDRAISEID, FNDRGRPID);
            var q = _dbFactory.getContext().Database.SqlQuery<jtFNDRGRP>(sql);
            return q.FirstOrDefault();
        }

        public lkFNDRGRP get_lkFNDRGRP(string FNDRGRP)
        {
            string sql = string.Format("SELECT * FROM lkFNDRGRP WHERE Upper(FNDRGRP) = '{0}'", FNDRGRP.Trim().ToUpper());
            var q = _dbFactory.getContext().Database.SqlQuery<lkFNDRGRP>(sql);
            return q.FirstOrDefault();
        }

        #endregion
        public Tuple<int, List<MonyR>> getMonyRWithCount(
            int pid, int page, int pageSize, QueryRuntimeSort sort, string fundCode, string srceCode, string progType, string date)
        {
            // start query
            var q = _entity_crm
                .All<MonyR>()
                .Where(monyRFilterCondition(pid, fundCode, srceCode, progType, date));
            // get total count
            int count = q.Count();
            string sortDir = sort?.dir ?? "desc";
            string sortField = sort?.field ?? "BATCHDTE";
            // confirm that sort field is valid property
            if (new MonyR().GetType().GetProperty(sortField) == null)
            {
                sortField = "BATCHDTE";
            }
            // do sorting and paging to get list
            List<MonyR> list = q.dynamicSortBy(sortField, sortDir == "desc")
                .AsEnumerable()
                .Skip((page - 1) * pageSize)
                .Take(pageSize)
                .ToList();

            return new Tuple<int, List<MonyR>>(count, list);            
        }

        private Expression<Func<MonyR, bool>> monyRFilterCondition(int pid, string fundCode, string srceCode, string progType, string date)
        {
            // split all multi-value filter inputs (separated by '|')
            List<string> fundSplit = fundCode?.Split('|')?.Select(f => f.Trim()).ToList() ?? new List<string>();
            List<string> srceSplit = srceCode?.Split('|')?.Select(s => s.Trim()).ToList() ?? new List<string>();
            List<string> progSplit = progType?.Split('|')?.Select(p => p.Trim()).ToList() ?? new List<string>();
            // build expression
            Expression<Func<MonyR, bool>> filterExpression = 
               m => m.PID == pid
                   && (string.IsNullOrEmpty(fundCode) || fundSplit.Contains(m.FUNDCODE))
                   && (string.IsNullOrEmpty(srceCode) || srceSplit.Contains(m.SRCECODE))
                   && (string.IsNullOrEmpty(progType) || progSplit.Contains(m.PROGTYPE));
            
            if (string.IsNullOrEmpty(date))
            {
                return filterExpression;
            }
            else
            {
                // get date expression from DateFilterFactory
                Expression<Func<MonyR, bool>> dateExpression = 
                    new DateFilterFactory<MonyR>(date, "BATCHDTE", false, "BATCHDTE").Predicate;
                // conjoin lambdas and return
                return filterExpression.AndAlsoSafe(dateExpression);
            }
        }

        public Tuple<int, List<MonyStatement>> getMonyStatement(
            int pid, int page, int pageSize, QueryRuntimeSort sort, string fundCode, string adjType, string batchDate, string adjDate)
        {
            // start query
            string sql = $"SELECT * FROM fn_GenStatement_v2({QzLib.sanitize(pid.ToString())})";
            var q = _dbFactory.getContext().Database.SqlQuery<MonyStatement>(sql)
                // group by GROUPNO since we want to select all gifts in group if one gift passes filter
                .GroupBy(m => m.GROUPNO)
                .AsQueryable()
                // apply filter
                .Where(monyStatementFilter(fundCode, adjType, batchDate, adjDate))
                .AsEnumerable()
                // flatten back into an IEnumerable<MonyStatement>
                .SelectMany(g => g)
                .AsQueryable();

            int count = q.Count();
            // get sort field and direction, with ORDERNO ASC as default
            string sortDir = sort?.dir ?? "asc";
            string sortField = sort?.field ?? "ORDERNO";
            // confirm that sort field is valid property
            if (new MonyStatement().GetType().GetProperty(sortField) == null)
            {
                sortField = "ORDERNO";
            }
            // do sorting and paging to get list
            List<MonyStatement> list = q.dynamicSortBy(sortField, sortDir == "desc")
                .Skip((page - 1) * pageSize)
                .Take(pageSize)
                .ToList();

            return new Tuple<int, List<MonyStatement>>(count, list);
        }
        
        private Expression<Func<IGrouping<int, MonyStatement>, bool>> monyStatementFilter(
            string fundCode, string adjType, string batchDate, string adjDate)
        {
            // split all multi-value filter inputs (separated by '|')
            IEnumerable<string> fundSplit = fundCode?.Split('|')?.Select(f => f.Trim()) ?? new List<string>();
            IEnumerable<string> adjSplit = adjType?.Split('|')?.Select(a => a.Trim()) ?? new List<string>();

            Expression<Func<IGrouping<int, MonyStatement>, bool>> expression =
                g => (string.IsNullOrEmpty(fundCode) 
                        || g.Any(m => 
                            !string.IsNullOrEmpty(m.ACCTCODE) 
                            && fundSplit.Contains(m.ACCTCODE.Substring(0, m.ACCTCODE.IndexOf('·')).Trim())))
                    && (string.IsNullOrEmpty(adjType) 
                        || g.Any(m => adjSplit.Contains(m.ADJDESC)));

            // conjoin date expressions if specified
            if (!string.IsNullOrEmpty(batchDate))
            {
                Expression<Func<IGrouping<int, MonyStatement>, bool>> batchDateExpression =                    
                    g => g.AsQueryable().Any(new DateFilterFactory<MonyStatement>(batchDate, "BATCHDTE", false, "BATCHDTE").Predicate);
                expression = expression.AndAlsoSafe(batchDateExpression);
            }

            if (!string.IsNullOrEmpty(adjDate))
            {
                Expression<Func<IGrouping<int, MonyStatement>, bool>> adjDateExpression =
                    g => g.AsQueryable().Any(new DateFilterFactory<MonyStatement>(adjDate, "ADJDTE", false, "ADJDTE").Predicate);
                expression = expression.AndAlsoSafe(adjDateExpression);
            }

            return expression;
        }

        public Tuple<int, List<BundlerGiftDisplay>> MonyByTrackNoCountAndResults(int trackNo, int page, int pageSize, QueryRuntimeSort sort)
        {
            var q = from mony in _entity_crm.All<MONY>()
                    join monyType in _entity_crm.All<lkMONYTYPE>() on mony.MONYTYPEID equals monyType.MONYTYPEID
                    join people in _entity_crm.All<people>() on mony.PID equals people.PID
                    join fund in _entity_crm.All<dmFUND>() on mony.FUNDID equals fund.FUNDID into fundGrp
                    from fund in fundGrp.DefaultIfEmpty()
                    join source in _entity_crm.All<SOURCE>() on mony.SRCEID equals source.SRCEID into sourceGrp
                    from source in sourceGrp.DefaultIfEmpty()
                    join pkg in _entity_crm.All<PACKAGE>() on source.PKGEID equals pkg.PKGEID into pkgGrp
                    from pkg in pkgGrp.DefaultIfEmpty()
                    where mony.SOFTMONEY == 0
                    && mony.COUNTER == 1
                    && mony.TRACKNO != null
                    && mony.TRACKNO == trackNo
                    select new BundlerGiftDisplay
                    {
                        TRACKNO = mony.TRACKNO,
                        MID = mony.MID,
                        PID = people.PID,
                        FNAME = people.FNAME,
                        LNAME = people.LNAME,
                        BATCHNO = mony.BATCHNO,
                        BATCHDTE = mony.BATCHDTE,
                        AMT = mony.AMT,
                        FUNDCODE = fund.FUNDCODE,
                        FUNDDESC = fund.FUNDDESC,
                        SRCECODE = source.SRCECODE,
                        SRCEDESC = source.SRCEDESC,
                        PKGECODE = pkg.PKGECODE,
                        PKGEDESC = pkg.PKGEDESC,
                        PAYMETHOD = monyType.DESCRIP
                    };
            var count = q.Count();
            string sortDir = sort?.dir ?? "desc";
            string sortField = sort?.field ?? "BATCHDTE";
            // confirm that sort field is valid property
            if (new BundlerGiftDisplay().GetType().GetProperty(sortField) == null)
            {
                sortField = "BATCHDTE";
            }
            // do sorting and paging and get list
            List<BundlerGiftDisplay> results = q.dynamicSortBy(sortField, sortDir == "desc")
                .Skip(pageSize * (page - 1))
                .Take(pageSize)
                .ToList();
            return new Tuple<int, List<BundlerGiftDisplay>>(count, results);
        }

        public genericResponse SaveFundraise(FUNDRAISE fundraise)
        {
            string errMessage = ValidateFundraise(fundraise);

            if (!string.IsNullOrEmpty(errMessage))
            {
                return new genericResponse()
                {
                    success = false,
                    message = errMessage
                };
            }

            try
            {
                FUNDRAISE existingRecord = _entity_crm
                    .Single<FUNDRAISE>(f => f.FUNDRAISEID == fundraise.FUNDRAISEID);
                if (existingRecord == null)
                {
                    _entity_crm.Add(fundraise);
                }
                else
                {
                    UpdateFundraiseFields(existingRecord, fundraise);
                    _entity_crm.Update(existingRecord);
                }
                _entity_crm.CommitChanges();
                return new genericResponse()
                {
                    success = true,
                    results = new List<iItemType>() { existingRecord ?? fundraise }
                };
            }
            catch (Exception)
            {
                return new genericResponse()
                {
                    success = false,
                    message = "An error occurred. Unable to save Bundler."
                };
            }
        }

        private void UpdateFundraiseFields(FUNDRAISE existing, FUNDRAISE changed)
        {
            // update only editable fields
            existing.TITLE = changed.TITLE;
            existing.RECRUITERNO = changed.RECRUITERNO;
            existing.STATE = changed.STATE;
            existing.COMMITMENT = changed.COMMITMENT;
            existing.LOBBYISTID = changed.LOBBYISTID;
            existing.VERIFIEDBY = changed.VERIFIEDBY;
            existing.VERIFIEDDTE = changed.VERIFIEDDTE;
            existing.cREFERRED = changed.cREFERRED;
        }

        private string ValidateFundraise(FUNDRAISE fundraise)
        {
            if (fundraise.RECRUITERNO == fundraise.TRACKNO)
            {
                return "Recruiter Track # cannot be the same as record's Track #.";
            }

            if (fundraise.RECRUITERNO != null)
            {
                // make sure recruiter track # exists
                if (!_entity_crm.All<FUNDRAISE>().Where(f => f.TRACKNO == fundraise.RECRUITERNO).Any())
                {
                    return "Recruiter Track # does not correspond to an existing record.";
                }
            }

            return "";
        }

        public IEnumerable<MonyGraphPoint> GetDonorGivingHistory(int pid)
        {
            try
            {
                return _entity_crm.All<MONY>()
                    .Where(mony => mony.PID == pid && mony.COUNTER == 1 && mony.SOFTMONEY == 0)
                    .GroupBy(mony => mony.BATCHDTE)
                    .Select(dateGroup => new MonyGraphPoint()
                    {
                        Amount = dateGroup.Sum(mony => mony.AMT ?? 0),
                        BatchDate = dateGroup.Key
                    });
            }
            catch (Exception)
            {
                return new List<MonyGraphPoint>();
            }
        }

        public ConsecutiveDonorYears  GetConsecutiveDonorYears(int pid)
        {
            try
            {
                var cycle = _entity_crm.All<dm_Cycle>()
                    .Where(c => c.cycleGroup == 1 && (c.cycleLabelShort_ == "FTD" || c.cycleLabelShort_ == "YTD"))
                    .FirstOrDefault();
                if (cycle == null)
                {
                    //cycle = new dm_Cycle()
                    //{
                    //    CYCLESTART = new DateTime(DateTime.UtcNow.Year, 1, 1),
                    //    CYCLEEND = new DateTime(DateTime.UtcNow.Year, 12, 31),
                    //    cycleLabelShort_ = "YTD"
                    //};
                    // [[[ new logic for ctd ]]]
                    string sql = "EXEC dbo.getConsecutiveCycleDataByPID {0}";
                    return _entity_crm.getContext().Database.SqlQuery<ConsecutiveDonorYears>(string.Format(sql, pid)).FirstOrDefault();
                }

                var yearlyGroups = _entity_crm.All<MONY>()
                    .Where(m => m.PID == pid && m.COUNTER == 1 && m.SOFTMONEY == 0)
                    .OrderByDescending(m => m.BATCHDTE)
                    .GroupBy(m => new
                    {
                        YearsAgo = ((DateTime)cycle.CYCLEEND).Year -
                            (m.BATCHDTE.Year +
                                ((m.BATCHDTE.Month < ((DateTime)cycle.CYCLEEND).Month
                                    || (m.BATCHDTE.Month == ((DateTime)cycle.CYCLEEND).Month
                                        && m.BATCHDTE.Day <= ((DateTime)cycle.CYCLEEND).Day))
                                ? 0 : 1))
                    }).ToList();

                var result = new ConsecutiveDonorYears() { CycleLabel = cycle.cycleLabelShort_ };
                foreach (var group in yearlyGroups)
                {
                    if (group.Key.YearsAgo == result.ConsecutiveYears) { result.ConsecutiveYears++; }
                    result.FirstYear = group.Last().BATCHDTE.Year;
                }

                return result;
            }
            catch (Exception)
            {
                return new ConsecutiveDonorYears();
            }
        }

        public List<MonyForAdj> GetMonyForAdjust(int pid)
        {
            string sql = string.Format(@"select MID,BATCHDTE,BATCHNO,AMT,MONYTYPE,FUNDCODE,SRCECODE,EXCEP, CAST(0 as bit) as SELECTED, 0.00 AS ADJAMT
            from v_people_mony where PID = {0} and COUNTER = 1 and ISNULL(ADJTYPE,'') != 'RD' ORDER BY BATCHDTE DESC ", pid);

            var q = _dbFactory.getContext().Database.SqlQuery<MonyForAdj>(sql);

            return q.ToList();
        }
        public bool bulkRedesignation(bulkRedesignationData data)
        {
            try
            {
                string sql = string.Format("exec z_bulk_adjustment_redesignation '{0}','{1}','{2}',{3},{4}", "RD", data.Mids, data.AdjDate, data.UID, data.FundId);
                var res = _dbFactory.getContext().Database.SqlQuery<int>(sql).FirstOrDefault();

                return res == 1;
            }
            catch(Exception ex)
            {
                return false;
            }
        }
    }
}