﻿using System.Web.Mvc;
using System.Web.Http;

namespace cmdiapp.n.core.Areas.apix
{
    public class apixAreaRegistration : AreaRegistration
    {
        public override string AreaName
        {
            get
            {
                return "apix";
            }
        }

        public override void RegisterArea(AreaRegistrationContext context)
        {
            context.Routes.MapMvcAttributeRoutes();
        }
    }
}
