﻿using cmdiapp.n.core.Areas.query.Domain.Models;
using System.Threading.Tasks;

namespace cmdiapp.n.core.Areas.query.Domain.Services.implementation
{
    public class SavedQueryService : ISavedQueryService
    {
        public int MaxRecordsToExport => Query.maxRowsToExport();

        public Task<SavedQueryInstance> GetQueryInstanceAsync(int id)
        {
            return QueryManager.GetQueryInstanceAsync(id);
        }

        public Task<SavedQueryInstance> GetQueryInstanceFromLogAsync(int qryLogId)
        {
            return QueryManager.GetQueryInstanceFromLogAsync(qryLogId);
        }

        public string GetSql(QueryInstanceInfo instance, QueryResultType resultType)
        {
            return new QueryRuntime(instance).sql(resultType);
        }
    }
}