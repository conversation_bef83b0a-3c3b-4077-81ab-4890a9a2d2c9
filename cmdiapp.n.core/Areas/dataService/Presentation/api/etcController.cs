﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Net;
using System.Web.Http;

using Ninject;
using Ninject.Web.Mvc;

using cmdiapp.n.core.Areas.dataService.Domain.Models;
using cmdiapp.n.core.Areas.dataService.Domain.Data;
using cmdiapp.n.core.Areas.dataService.Domain.Services;

namespace cmdiapp.n.core.Areas.dataService.Presentation.Controllers.api
{

    [Authorize]
    public class etcController : ApiController
    {
        private IdataService _service;

        public etcController()
        {
            _service = I_entityManager_ds.getService();
        }

        [HttpGet, Route("dataService/api/etc/nickNames/{FirstName}")]
        public List<string> nickNames(string FirstName)
        {
            string nickNames = _service.same_nickNames(FirstName);
            return nickNames.Split(',').ToList<string>().Where(a=>!string.IsNullOrWhiteSpace(a)).ToList();
        }

    }
}
