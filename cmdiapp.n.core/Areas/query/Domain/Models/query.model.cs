﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Runtime.Serialization;
using System.IO;
using System.Xml.Serialization;
using System.Web.Script.Serialization;
using System.Data;
using System.Data.SqlClient;
using System.Dynamic;
using System.Text.RegularExpressions;
using Newtonsoft.Json;

using cmdiapp.n.core.Core;
using cmdiapp.n.core.Library;
using cmdiapp.n.core.Areas.crm.Domain.Data;
using cmdiapp.n.core.Areas.crm;
using cmdiapp.n.core.Areas.crm.Core;
using System.Text;
using System.Threading.Tasks;

namespace cmdiapp.n.core.Areas.query.Domain.Models
{
    public static class Query
    {
        #region [[ Service Methods ]]

        public static QueryDef getDef(string defName)
        {
            return new QueryDef(defName);
        }

        #region [ Run ]
        public static QueryResult run(QueryInstanceInfo qInst, QueryRuntimeParams runParams)
        {
            runParams = runParams ?? Query.defaultRuntimeParams;

            QueryRuntime qr = new QueryRuntime(qInst, runParams.sqlOrderBy);

            return qr.execute(runParams);
        }

        public static DataTable runToDataTable(QueryInstanceInfo qInst, QueryRuntimeParams runParams)
        {
            runParams = runParams ?? Query.defaultRuntimeParams;

            QueryRuntime qr = new QueryRuntime(qInst, runParams.sqlOrderBy);

            return qr.execute(runParams).results as DataTable;
        }
        #endregion

        #region [ Open & Save Searches ]
        public static QueryInstanceInfo save(QueryInstanceInfo qInst)
        {
            return QueryManager.saveQuery(qInst);
        }

        public static bool checkIfUniqeName(string name, string key = "")
        {
            return QueryManager.checkIfUniqueName(name, key);
        }

        public static List<QueryInstanceInfo> myRecent(string defKey, int noSearches = 10)
        {
            return QueryManager.myRecentSearches(defKey, noSearches);
        }

        public static List<QueryInstanceInfo> mySaved(string defKey, string key, int noSearches = -1)
        {
            return QueryManager.mySavedSearches(defKey, key, noSearches);
        }

        #endregion

        #endregion

        #region [[ Shared Data by Query Models ]]
        public static string dbConnStr { get { return session.currentDomain_project._connectionString(); } }
        public static string dbConnStrLegislator { get { return System.Configuration.ConfigurationManager.ConnectionStrings["legislatorContext"].ConnectionString; } }
        public static string dbConnStrGateway { get { return System.Configuration.ConfigurationManager.ConnectionStrings["kernelContext"].ConnectionString; } }

        public static int commandTimeout = 1800000;  // 30 min.
        public static readonly QueryRuntimeParams defaultRuntimeParams = new QueryRuntimeParams() { resultType = QueryResultType.pagedView, pageSize = 10, page = 1 };
        public static int maxRowsToExport()
        {
            return session.userSession.is_sysAdmin ? 1_000_000 : 100_000;
        }
        public static string userConfigKey = "query-user-config";
        public static int maxAggregationDepth => 2;
        #endregion

    }

    #region  [[ Models ]]

    public class QueryDef
    {
        #region [constructors]
        public QueryDef() { }

        public QueryDef(string defName)
        {
            /*  ||||| Testing result after serialization/deserialization to/from memory and JSON            
            HttpRuntime.Cache["test"] = qd;
            QueryDef b = HttpRuntime.Cache["test"] as QueryDef;
            QueryDef qd = new QueryDef(defName); 
            var json = new JavaScriptSerializer().Serialize(qd);
            QueryDef c = new JavaScriptSerializer().Deserialize(json, typeof(QueryDef)) as QueryDef;
            |||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||*/

            string defCacheKey = session.get_cacheKey_for_appId_projectId_userGroupId("[qd]" + defName + "|");

            var qdCached = cacheManager.get(defCacheKey);

            QueryDef qd;

            if (qdCached != null)
                qd = qdCached as QueryDef;
            else
            {
                string dx = System.IO.File.ReadAllText(Library.util.fullPath(String.Format(@"{0}/{1}.xml", session.currentDomain_project.application.qDefPath, defName)));
                using (StringReader r = new StringReader(dx))
                {
                    XmlSerializer xs = new XmlSerializer(typeof(QueryDef), new XmlRootAttribute("qDef"));
                    qd = (QueryDef)xs.Deserialize(r);
                }

                cacheManager.insert(defCacheKey, qd, 43200); // 12 hours
            }

            foreach (var property in typeof(QueryDef).GetProperties().Where(a => a.CanWrite))
                property.SetValue(this, property.GetValue(qd));

            this.compile();
        }
        #endregion

        #region [c]
        [XmlAttribute("name")]
        public string key { get; set; }
        [XmlAttribute("caption")]
        public string label { get; set; }
        public string accessRight {
            get
            {
                if (string.IsNullOrEmpty(aElementIdentifier))
                {
                    /* 
                     * exception for memberSearch, which does not currently have
                     * accessElement associated with it.                     
                     */
                    return "e";
                }
                return session.what_can_i_do(this.aElementIdentifier);
            }
        }

        [XmlElement("runDefault")]
        public bool runDefault { get; set; } = false;

        [XmlElement("remoteQuery")]
        public bool remoteQuery { get; set; } = false;

        [XmlElement("tooltipIdentifier")]
        public string tooltipIdentifier { get; set; }

        [XmlElement("analysisDashboardId")]
        public string analysisDashboardId { get; set; }

        [IgnoreDataMember]
        [XmlElement("dbContext")]
        public string dbContext { get; set; } = "project";
        [IgnoreDataMember]
        public string dbConnectionString
        {
            get
            {
                if (dbContext == "legislator")
                {
                    return Query.dbConnStrLegislator;
                }
                if (dbContext == "gateway")
                {
                    return Query.dbConnStrGateway;
                }
                return Query.dbConnStr;
            }
        }

        [IgnoreDataMember]
        [XmlElement("saving")]
        public int _saving { get; set; } = 2;    // (2 for now) 0-No | 1-RecentOnly | 2-Recent+Save

        public int saving
        {
            get
            {
                return _saving; // Later, modify this to overwrite to 0 or 1 depending on how we agree what covers as "Premium"
            }
        }

        [XmlArray("qtsc")]
        [XmlArrayItem("c")]
        public QueryQuickSearchConfigItem[] quickSearchConfig { get; set; }

        [XmlArray("qsfg")]
        [XmlArrayItem("qs")]
        public QueryFilterGroup[] filterGroups { get; set; }

        [XmlArray("qo")]
        [XmlArrayItem("eg")]
        public QueryExportGroup[] exportGroups { get; set; }

        [XmlArray("qab")]
        [XmlArrayItem("ab", Type = typeof(QueryActionButton))]
        public QueryActionButton[] actionButtons { get; set; }

        [XmlArray("qtc")]
        [XmlArrayItem("c", Type = typeof(QueryTableColumn))]
        public QueryTableColumn[] tableColumns { get; set; }

        [XmlArray("viewAggregates")]
        [XmlArrayItem("viewAggregate", Type = typeof(QueryViewAggregate))]
        public QueryViewAggregate[] viewAggregates { get; set; }

        [XmlArray("qtb")]
        [XmlArrayItem("tb", Type = typeof(QueryTableButton))]
        public QueryTableButton[] tableButtons { get; set; }

        [XmlElement("aggregation")]
        public QueryAggregation aggregation { get; set; }

        [XmlElement("exportProcedureWrapper")]
        [IgnoreDataMember]
        public QueryExportProcedure exportProcedureWrapper { get; set; }

        #endregion

        #region [s]
        [IgnoreDataMember]
        [XmlElement("ch")]
        public string ch { get; set; }

        [IgnoreDataMember]
        [XmlElement("aElementIdentifier")]
        public string aElementIdentifier { get; set; }
        [IgnoreDataMember]
        [XmlElement("x")]
        public string defaultExportFormat { get; set; }
        [IgnoreDataMember]
        [XmlElement("sq_uniqId")]
        public string sq_uniqId { get; set; }
        [IgnoreDataMember]
        [XmlElement("qf")]
        public QueryDataSource dataSource { get; set; }
        #endregion

        #region [m]
        public void compile()
        {
            populateDbLookup();
            performCustomHandle();
            handleRestrictionOnFilters();
            checkButtonsAccess();
        }

        private void populateDbLookup()
        {
            this.filterGroups.SelectMany(a => a.filters).Where(b => !string.IsNullOrEmpty(b.ldb)).ToList().ForEach(s => s.l = s.l ?? getDbLookup(s.ldb));
        }

        private QueryCustomOperators getDbLookup(string lookupInfo)
        {
            string _luk = "";
            System.Data.DataSet sqlSelect = QzLib.runSql(lookupInfo, "result", 0, 0, dbConnectionString);
            if (sqlSelect != null && sqlSelect.Tables.Count > 0 && sqlSelect.Tables[0].Rows.Count > 0)
            {
                for (int rowcount = 0; rowcount < sqlSelect.Tables[0].Rows.Count; rowcount++)
                    _luk = _luk + sqlSelect.Tables[0].Rows[rowcount][0].ToString();
            }

            QueryCustomOperators qo;
            using (StringReader r = new StringReader(_luk))
            {
                XmlSerializer xs = new XmlSerializer(typeof(QueryCustomOperators), new XmlRootAttribute("l"));
                qo = (QueryCustomOperators)xs.Deserialize(r);
            }

            return qo;
        }

        private void performCustomHandle()
        {
            this.label = customHandleLabel(this.ch, this.label);

            this.filterGroups.ToList().ForEach(f => f.label = customHandleLabel(f.ch, f.label));
            this.filterGroups.SelectMany(a => a.filters).Where(b => !string.IsNullOrEmpty(b.ch)).ToList().ForEach(s => s.label = customHandleLabel(s.ch, s.label));
            this.filterGroups.ToList().ForEach(f => f.filters.RemoveAll(x => !string.IsNullOrEmpty(x.ch) && string.IsNullOrEmpty(x.label)));

            this.exportGroups.SelectMany(a => a.options).Where(b => !string.IsNullOrEmpty(b.ch)).ToList().ForEach(s => s.label = customHandleLabel(s.ch, s.label));
            this.exportGroups.ToList().ForEach(f => f.options.RemoveAll(x => !string.IsNullOrEmpty(x.ch) && string.IsNullOrEmpty(x.label)));
            this.exportGroups.ToList().RemoveAll(x => x.options.Count == 0);

            this.exportGroups.SelectMany(a => a.options)
                .Where(b => b.cfh?.Any() ?? false)
                .ToList()
                .ForEach(exportOption =>
                {
                    string newFields = exportOption.fields;
                    foreach (var customFieldHandleXml in exportOption.cfh)
                    {
                        newFields = newFields.Replace(customFieldHandleXml, customFieldHandle(customFieldHandleXml));
                    }
                    exportOption.fields = newFields;
                });

            this.tableColumns.Where(b => !string.IsNullOrEmpty(b.ch)).ToList().ForEach(s => s.label = customHandleLabel(s.ch, s.label));
            //this.tableColumns.ToArray().RemoveAll(x => !string.IsNullOrEmpty(x.ch) && string.IsNullOrEmpty(x.label));
            this.tableColumns = this.tableColumns.Where(x => !(!string.IsNullOrEmpty(x.ch) && string.IsNullOrEmpty(x.label))).ToArray();

            if (aggregation?.groupByFields?.Any() ?? false)
            {
                aggregation.groupByFields = aggregation.groupByFields
                    .Where(field => string.IsNullOrEmpty(field.customHandle)
                        || !string.IsNullOrEmpty(customHandleLabel(field.customHandle, field.label)))
                    .ToArray();
            }
        }

        private string customFieldHandle(string cfh)
        {
            return QzLib.handleRepl(cfh, dbConnectionString);
        }

        private string customHandleLabel(string ch, string label)
        {
            if (String.IsNullOrEmpty(ch))
                return label;

            String[] _o = ch.Split('|');

            switch (_o[0])
            {
                case "v":
                    if (_o[1].Split(',').Any(a => session.currentDomain_project.projectType.appVersion.Contains(a)))
                    {
                        if (_o[3] != null && !String.IsNullOrEmpty(_o[3]))
                            return _o[3];
                        else
                            return label;
                    }
                    // It's to change the caption, not Access check (Y or <empty>)
                    else if (_o[2].ToUpper() == "C")
                        return label;

                    break;
                case "c":
                    string _cv = applica.getConfigVal(_o[1], session.currentDomain_project.appId, session.currentDomain_projectId);
                    if ((_o[2] == "!NULL" && !String.IsNullOrEmpty(_cv)) || _cv == _o[2] || (_o[2] == "NULL" && String.IsNullOrEmpty(_cv)))
                    {
                        if (_o[3] == "true")
                            label = _cv;

                        return label;
                    }
                    break;
                case "a":
                    string _ac = session.what_can_i_do(_o[1]);
                    if (_o[2].Split(',').Any(a => _ac.Contains(a)))
                        return label;
                    break;
            }

            return "";
        }

        private void handleRestrictionOnFilters()
        {
            var af = this.filterGroups.SelectMany(a => a.filters);

            var l = from f in af
                    join r in session.myQRestrictions on f.fieldName equals r.fieldName
                    select f;

            foreach (QueryFilter i in l.ToList())
            {
                q_restriction restrictedItem = session.myQRestrictions.FirstOrDefault(a => a.fieldName == i.fieldName);
                if (!string.IsNullOrEmpty(restrictedItem.fieldName))
                {
                    i.locked = true;
                    i.operatorIdx = restrictedItem.operatorIdx;
                    i.values = new string[] { restrictedItem.value };
                }
            }
        }

        private void checkButtonsAccess()
        {
            if (!string.IsNullOrEmpty(aElementIdentifier))
            {
                // use global query access element if button's is null or empty
                // only System Users can Recalculate Allocation and Batch Delete Treasury
                actionButtons = actionButtons
                    .Where(btn => (string.IsNullOrEmpty(btn.accessLevel)
                    || session.can_i_do(
                        (string.IsNullOrEmpty(btn.accessElement) ? aElementIdentifier : btn.accessElement),
                        btn.accessLevel
                        ))
                    && (btn.action != "calcAllocation" || session.currentDomainAuthorizedProject_userGroupId == 5)
                    && (btn.action != "deleteTxns" || session.currentDomainAuthorizedProject_userGroupId == 5)).ToArray();
            }

            tableButtons = tableButtons.Where(btn =>
                    (btn.action != "edit" || "vead".Contains(accessRight))
                    && (btn.action != "delete" || accessRight == "d")
                    && (btn.action != "responseAnalysis" || session.can_i_do("ResponseAnalysisReport", "v"))
                    && (btn.action != "batchDelete" || session.can_i_do("DeleteBatch", "d"))
                    && (btn.action != "addCongressMemberPayee" || session.can_i_do("cmdiapp.dms.Entity_Browser", "a")))
                    .ToArray();
        }

        #endregion
    }
    #region [ QueryDef Child Classes ]
    public class QueryQuickSearchConfigItem
    {
        [XmlElement("e")]
        public string regExp { get; set; }
        [XmlElement("co")]
        public bool isColonOp { get; set; }
        [XmlElement("cl")]
        public string[] colonOpLabel { get; set; }
        [XmlElement("cc")]
        public string[] colonOpChar { get; set; }
        [XmlArray("fs")]
        [XmlArrayItem("f")]
        public QueryQuickSearchConfigItemFilterInfo[] filtersArray { get; set; }
    }

    public class QueryQuickSearchConfigItemFilterInfo
    {
        [XmlAttribute("key")]
        public string key { get; set; }
        [XmlAttribute("idx")]
        public int operatorIdx { get; set; }
    }

    public class QueryFilterGroup
    {
        #region [c]
        [XmlAttribute("name")]
        public string key { get; set; }
        [XmlAttribute("caption")]
        public string label { get; set; }

        #region [ sub-query ]
        [XmlElement("subquery")]
        public bool subquery { get; set; }
        [XmlElement("forceSuppress")]
        public bool forceSuppress { get; set; }
        [XmlElement("andSuppress")]
        public bool andSuppress { get; set; }
        #endregion

        [XmlElement("q")]
        public List<QueryFilter> filters { get; set; }

        #endregion

        #region [s]
        [IgnoreDataMember]
        [XmlElement("ch")]
        public string ch { get; set; }

        [IgnoreDataMember]
        [XmlElement("sq_uniqId")]
        public string sq_uniqId { get; set; }
        [IgnoreDataMember]
        [XmlElement("sq_from")]
        public string sq_from { get; set; }
        [IgnoreDataMember]
        [XmlElement("sq_groupBy")]
        public string sq_groupBy { get; set; }
        #endregion
    }

    public class QueryFilter
    {
        #region [c]
        [XmlElement("i")]
        public string key { get; set; }
        [XmlElement("t")]
        public string fieldType { get; set; }
        [XmlElement("c")]
        public string label { get; set; }
        [XmlElement("d")]
        public string description { get; set; }
        private string[] _values;
        [XmlElement("v")]
        public string[] values { get { return _values ?? new string[] { }; } set { _values = value; } }
        [XmlElement("o")]
        public int operatorIdx { get; set; }
        [XmlElement("h")]
        public bool hide { get; set; }
        public bool locked { get; set; } = false;

        public List<QueryCustomOperator> operatorOptions
        {
            get
            {
                return (l == null ? null : l.operatorOptions);
            }
        }
        [XmlElement("optionsSearch")]
        public QueryFilterOptionsSearchConfig optionsSearch { get; set; }

        #endregion 

        #region [s]
        [XmlElement("l")]
        [IgnoreDataMember]
        public QueryCustomOperators l { get; set; }

        [XmlElement("lookup")]
        [IgnoreDataMember]
        public string ldb { get; set; }

        //(??? May not need it any more) public string parentPanelName { get; set; }
        //(??? May not need it any more) public q_whereItem_panel_S parentPanel { get; set; }
        [IgnoreDataMember]
        [XmlElement("n")]
        public string fieldName { get; set; }
        [IgnoreDataMember]
        [XmlAttribute("groupbyHaving")]
        public bool groupbyHaving { get; set; }

        [IgnoreDataMember]
        [XmlElement("ch")]
        public string ch { get; set; }

        [IgnoreDataMember]
        [XmlElement("p")]
        public string p { get; set; }

        //(+++ Need to implement) public searchItem_S filter { get; set; } // For SQL-Run
        #endregion
    }

    public class QueryFilterOptionsSearchConfig
    {
        [XmlElement("url")]
        public string url { get; set; }

        [XmlElement("isQueryable")]
        public bool isQueryable { get; set; } = false;

        [XmlElement("valueField")]
        public string valueField { get; set; }

        [XmlElement("nameField")]
        public string nameField { get; set; }

        [XmlElement("valueFieldType")]
        public string valueFieldType { get; set; }
    }

    public class QueryCustomOperators
    {
        [XmlElement("i")]
        public List<QueryCustomOperator> operatorOptions { get; set; }
    }

    public class QueryCustomOperator
    {
        [XmlElement("s")]
        public string value { get; set; }
        [XmlElement("r")]
        public string label { get; set; }
    }

    public class QueryExportGroup
    {
        [XmlAttribute("name")]
        public string key { get; set; }
        [XmlElement("t")]
        public bool isTemplate { get; set; }
        [XmlElement("g")]
        public List<QueryExportOption> options { get; set; }
        [XmlElement("x")]
        public string exportFormat { get; set; }
        [XmlElement("sys")]
        public bool isSystem { get; set; } = false;

        //(+++ Need to implement) public searchItem_S filter { get; set; } // For SQL-Run

    }

    public class QueryExportOption
    {
        [XmlElement("i")]
        public string key { get; set; }
        [XmlElement("c")]
        public string label { get; set; }
        [XmlElement("d")]
        public string description { get; set; }
        [XmlElement("s")]
        public bool selected { get; set; }
        [IgnoreDataMember]
        [XmlElement("f")]
        public string fields { get; set; }
        [XmlElement("w")]
        public QueryTableDefaultWhere defaultWhere { get; set; }

        [IgnoreDataMember]
        public List<string> cfh
        {
            get
            {
                var value = new List<string>();
                if (string.IsNullOrEmpty(fields)) { return value; }

                var regex = new Regex(@"<r>[\s\S]*?<\/r>");
                var matches = regex.Matches(fields);

                if ((matches?.Count ?? 0) < 1) { return value; }

                foreach (Match match in matches)
                {
                    value.Add(match.Value);
                }
                return value;
            }
        }

        public bool visible { get; set; } = true;
        [IgnoreDataMember]
        [XmlElement("ch")]
        public string ch { get; set; }
    }

    public class QueryDataSource
    {
        [XmlElement("w")]
        public QueryTableDefaultWhere defaultWhere { get; set; }
        [XmlElement("f")]
        public List<QueryTableJoin> tableJoins { get; set; }
    }

    public class QueryTableJoin
    {
        [XmlElement("c")]
        public bool core { get; set; }
        [XmlElement("d")]
        public string depends { get; set; }
        [XmlElement("t")]
        public string table { get; set; }
        public string tableJoin
        {
            get
            {
                string _replTag = Library.util.regExp_match(table, @"<r>[\s\S]*?<\/r>");
                if (string.IsNullOrEmpty(_replTag))
                    return table;

                string _replTableJoin = QzLib.handleRepl(_replTag, Query.dbConnStr);

                return table.Replace(_replTag, _replTableJoin);
            }
        }

        [XmlElement("w")]
        public QueryTableDefaultWhere defaultWhere { get; set; }
    }

    public class QueryTableDefaultWhere
    {
        [XmlAttribute("f")]
        public string fieldToCheck { get; set; }
        [XmlText]
        public string where { get; set; }
        public string whereCondition
        {
            get
            {
                string _replTag = Library.util.regExp_match(where, @"<r>[\s\S]*?<\/r>");
                if (string.IsNullOrEmpty(_replTag))
                    return where;

                string _replWhere = QzLib.handleRepl(_replTag, Query.dbConnStr);

                return where.Replace(_replTag, _replWhere);
            }
        }
    }

    public class QueryTableButton
    {
        [XmlAttribute("caption")]
        public string label { get; set; }
        [XmlElement("a")]
        public string action { get; set; }
        [XmlElement("k")]
        public string argKey { get; set; }
        [IgnoreDataMember]
        [XmlArray("g")]
        [XmlArrayItem("i", Type = typeof(QueryActionButtonArgKeyValue))]
        public QueryActionButtonArgKeyValue[] argsKeyValue { get; set; }

        [XmlIgnore]
        public Dictionary<string, string> args
        {
            get
            {
                if (argsKeyValue == null)
                    return null;

                Dictionary<string, string> _args = new Dictionary<string, string>();

                argsKeyValue.ToList().ForEach(a => _args.Add(a.key, a.value));

                return _args;
            }
        }

    }

    public class QueryActionButton
    {
        [XmlAttribute("caption")]
        public string label { get; set; }
        [XmlAttribute("type")]
        public string type { get; set; }
        [XmlAttribute("position")]
        public string position { get; set; } = "footer"; // header | footer (Default if not specified)
        [IgnoreDataMember]
        [XmlAttribute("accessElement")]
        public string accessElement { get; set; } // if not set but accessLevel is, will use global query access element
        [IgnoreDataMember]
        [XmlAttribute("accessLevel")]
        public string accessLevel { get; set; }

        [XmlElement("a")]
        public string action { get; set; }
        [IgnoreDataMember]
        [XmlArray("g")]
        [XmlArrayItem("i", Type = typeof(QueryActionButtonArgKeyValue))]
        public QueryActionButtonArgKeyValue[] argsKeyValue { get; set; }

        [XmlIgnore]
        public Dictionary<string, string> args
        {
            get
            {
                if (argsKeyValue == null)
                    return null;

                Dictionary<string, string> _args = new Dictionary<string, string>();

                argsKeyValue.ToList().ForEach(a => _args.Add(a.key, a.value));

                return _args;
            }
        }

    }

    public class QueryActionButtonArgKeyValue
    {
        [XmlAttribute("k")]
        public string key { get; set; }
        [XmlAttribute("v")]
        public string value { get; set; }
    }

    public class QueryActionButtonArg : DynamicObject
    {
        private readonly Dictionary<string, object> dictionary;

        public QueryActionButtonArg(Dictionary<string, object> dictionary)
        {
            this.dictionary = dictionary;
        }


        public override bool TryGetMember(GetMemberBinder binder, out object result)
        {
            return dictionary.TryGetValue(binder.Name, out result);
        }

        public override bool TrySetMember(SetMemberBinder binder, object value)
        {
            dictionary[binder.Name] = value;
            return true;
        }
    }

    public class QueryTableColumn
    {
        [XmlAttribute("key")]
        public string key { get; set; }
        [XmlAttribute("label")]
        public string label { get; set; }
        [XmlAttribute("format")]
        public string format { get; set; }
        [XmlAttribute("hidden")]
        public bool hidden { get; set; }
        [XmlAttribute("order")]
        public string order { get; set; }   // empty(null) | asc | desc
        [IgnoreDataMember]
        [XmlElement("f")]
        public string fieldName { get; set; }
        [IgnoreDataMember]
        [XmlElement("ch")]
        public string ch { get; set; }
    }

    public class QueryViewAggregate
    {
        [IgnoreDataMember]
        [XmlElement("predicate")]
        public string predicate { get; set; }

        [IgnoreDataMember]
        [XmlElement("viewColumnToAggregate")]
        public string viewColumnToAggregate { get; set; }

        [IgnoreDataMember]
        [XmlElement("aggregateFunction")]
        public string aggregateFunction { get; set; } = "SUM";

        [XmlElement("key")]
        public string key { get; set; }

        [XmlElement("label")]
        public string label { get; set; }

        [XmlElement("format")]
        public string format { get; set; } = "n";
    }

    public class QueryAggregation
    {
        [XmlArray("aggregateColumns")]
        [XmlArrayItem("aggregateColumn", Type = typeof(QueryAggregateColumn))]
        public QueryAggregateColumn[] aggregateColumns { get; set; } = new QueryAggregateColumn[0];

        [XmlArray("groupByFields")]
        [XmlArrayItem("groupByField", Type = typeof(QueryAggregateGroupByField))]
        public QueryAggregateGroupByField[] groupByFields { get; set; } = new QueryAggregateGroupByField[0];
    }

    public class QueryAggregateColumn
    {
        [XmlElement("column")]
        [IgnoreDataMember]
        public string column { get; set; }

        [XmlElement("function")]
        [IgnoreDataMember]
        public string function { get; set; }

        [XmlElement("label")]
        public string label { get; set; }

        [XmlElement("format")]
        public string format { get; set; }

        [XmlElement("key")]
        public string key { get; set; }

        [XmlElement("isOrderingColumn")]
        [IgnoreDataMember]
        public bool isOrderingColumn { get; set; } = false;
    }

    public class QueryAggregateGroupByField
    {
        [XmlElement("key")]
        public string key { get; set; }

        [XmlElement("column")]
        [IgnoreDataMember]
        public string column { get; set; }

        [XmlElement("descriptionSelect")]
        [IgnoreDataMember]
        public string descriptionSelect { get; set; }

        [XmlElement("customHandle")]
        [IgnoreDataMember]
        public string customHandle { get; set; }

        [XmlElement("label")]
        public string label { get; set; }

        [XmlElement("format")]
        public string format { get; set; }
    }

    public class QueryExportProcedure
    {
        [XmlElement("procedureName")]
        public string ProcedureName { get; set; }
        [XmlArray("parameters")]
        [XmlArrayItem("parameter")]
        public List<QueryExportProcedureParameter> Parameters { get; set; }
    }

    public class QueryExportProcedureParameter
    {
        [XmlElement("useQuerySql")]
        public bool UseQuerySql { get; set; }
        /// <summary>
        /// If used, this should be the id of the filter to get value from.
        /// </summary>
        [XmlElement("useFilterValue")]
        public string UseFilterValue { get; set; }
    }

    #endregion


    public class QueryInstanceInfo
    {
        public QueryInstanceInfo() { }

        public QueryInstanceInfo(string testData)
        {
            string sample = System.IO.File.ReadAllText(Library.util.fullPath(String.Format(@"/tmp/{0}.json", testData)));

            QueryInstanceInfo _instance = new JavaScriptSerializer().Deserialize(sample, typeof(QueryInstanceInfo)) as QueryInstanceInfo;
            foreach (var property in typeof(QueryInstanceInfo).GetProperties().Where(a => a.CanWrite))
                property.SetValue(this, property.GetValue(_instance));
        }

        public string key { get; set; }
        public string name { get; set; }
        public string defKey { get; set; }
        public List<QueryFilterInstance> filters { get; set; }
        public List<QueryExportOptionInstance> exportOptions { get; set; }
        public List<QueryAggregateGroupByFieldInstance> aggregateFields { get; set; }

        public DateTime? createDate { get; set; }
        public DateTime? lastModifiedDate { get; set; }
        public DateTime? lastRunDate { get; set; }
        public int? remoteQueryProjId { get; set; }
    }
    #region [ QueryInstanceInfo Child Classes ]
    public class QueryFilterInstance
    {
        public string key { get; set; }
        private string[] values_;
        public string[] values {
            get { return values_; }
            set
            {
                for (int i = 0; i < value.Count(); i++)
                    value[i] = QzLib.sanitize(value[i]);

                values_ = value;
            }
        }
        public int operatorIdx { get; set; }
        public bool askAtRuntime { get; set; }
    }

    public class QueryExportOptionInstance
    {
        public string key { get; set; }
        public string groupKey { get; set; }
    }

    public class QueryAggregateGroupByFieldInstance
    {
        public string key { get; set; }
    }

    #endregion


    public class QueryRuntime
    {
        #region (constructor)
        public QueryRuntime(QueryInstanceInfo qInst, string orderBy = "")
        {
            this.qDef = new QueryDef(qInst.defKey);
            this.qIns = qInst;
            this.qOrderBy = orderBy;

            var f = from d in qDef.filterGroups.Where(q => !q.subquery).SelectMany(a => a.filters)
                    join i in qIns.filters on d.key equals i.key
                    where hasFilterValue(d.fieldType, d.fieldName, i.operatorIdx, d.operatorOptions != null, i.values)
                    select new QueryRuntimeFilter(d.key, i.values, i.operatorIdx, d.fieldType, d.fieldName, d.operatorOptions != null, d.p, qDef.dbConnectionString);
            filters = f.ToList();

            var s = from d in qDef.filterGroups.Where(q => q.subquery).SelectMany(a => a.filters)
                    join i in qIns.filters on d.key equals i.key
                    where hasFilterValue(d.fieldType, d.fieldName, i.operatorIdx, d.operatorOptions != null, i.values)
                    select new QueryRuntimeFilter(d.key, i.values, i.operatorIdx, d.fieldType, d.fieldName, d.operatorOptions != null, d.p, qDef.dbConnectionString);
            sqFilters = s.ToList();

            var vs = qDef.tableColumns.Select(a => string.Format("\t{0}", a.fieldName));
            viewSelects = vs.ToArray();

            var es = from d in qDef.exportGroups.SelectMany(a => a.options)
                     join i in qIns.exportOptions on d.key equals i.key
                     select string.Format("\n\t\t/* {0} */{1}", d.label, d.fields);
            exportSelects = es.ToArray();
        }
        #endregion

        #region ( private var/func )
        // May not need these later > REMOVE
        private QueryDef qDef;
        private QueryInstanceInfo qIns;
        private string qOrderBy;

        private List<QueryRuntimeFilter> filters;
        private List<QueryRuntimeFilter> sqFilters;
        private string[] viewSelects;
        private string[] exportSelects;



        private static bool hasFilterValue(string fieldType, string fieldName, int operatorIdx, bool hasCustomOperators, string[] values)
        {
            return !string.IsNullOrEmpty(fieldName)
                                   && (QzLib.NoValSymbols.Contains(QzLib.getOpSymbol(fieldType, operatorIdx, hasCustomOperators))
                                       || (values != null && values.Count() > 0 && !string.IsNullOrEmpty(values[0]))
                                       || ("nBTW|cBTW|dBTW|sBTW".Contains(QzLib.getOpSymbol(fieldType, operatorIdx, hasCustomOperators)) && values != null && values.Count() > 1 && !string.IsNullOrEmpty(values[0]) && !string.IsNullOrEmpty(values[1]))
                                      );
        }
        #endregion

        #region ( SQL )
        private string sqlWhere
        {
            get
            {
                string _sqlWhere = string.Join("\n\tAND ", filters.Select(a => a.sqlWhere).ToArray());

                if (qDef.dataSource.defaultWhere != null && !_sqlWhere.Contains(qDef.dataSource.defaultWhere.fieldToCheck))
                    _sqlWhere = "\t" + qDef.dataSource.defaultWhere.whereCondition + (!string.IsNullOrEmpty(_sqlWhere) ? "\n\tAND " : "") + _sqlWhere;

                string _sqSqlWhere = string.Join("\n\tAND ", sqlWhereSubQuery());

                _sqlWhere += (!string.IsNullOrEmpty(_sqSqlWhere) ? "\n\tAND " + _sqSqlWhere : "");

                var _outputWithWhere = from d in qDef.exportGroups.SelectMany(a => a.options)
                                       join i in qIns.exportOptions on d.key equals i.key
                                       where d.defaultWhere != null && !string.IsNullOrEmpty(d.defaultWhere.whereCondition) && !_sqlWhere.Contains(d.defaultWhere.fieldToCheck)
                                       select new { d.defaultWhere.whereCondition };
                _outputWithWhere.ToList().ForEach(a => _sqlWhere += "\n\tAND " + a.whereCondition);

                return _sqlWhere;
            }
        }

        private string[] sqlWhereSubQuery()
        {
            var _fg = qDef.filterGroups
                       .Where(a => a.subquery && a.filters.ToList().Where(b => sqFilters.Select(c => c.Key).ToList().Contains(b.key)).Count() > 0)
                       .ToList();

            string[] _subQueries = new string[_fg.Count];

            int k = 0;
            foreach (QueryFilterGroup fgroup in _fg)
            {
                var _where = from d in fgroup.filters
                             join i in sqFilters on d.key equals i.Key
                             where !d.groupbyHaving
                             select i.sqlWhere;
                string _sqlWhere = string.Join(String.Format("\n\t\t\t{0} ", (fgroup.forceSuppress ? "OR" : "AND")), _where.ToArray());

                var _groupByAgg = from d in fgroup.filters
                                  join i in sqFilters on d.key equals i.Key
                                  where d.groupbyHaving
                                  select i.sqlWhere;

                string _sqlGroupByWhere = string.Join("\n\t\t\tAND ", _groupByAgg.ToArray());

                string _subQuery = @"{0} {1} (SELECT {2} FROM {3}{4}{5}{6})";
                _subQueries[k] = string.Format(_subQuery,
                        qDef.sq_uniqId,
                        (fgroup.forceSuppress || fgroup.andSuppress ? "NOT IN" : "IN"),
                        fgroup.sq_uniqId,
                        fgroup.sq_from,
                        (!string.IsNullOrEmpty(_sqlWhere) ? "\n\t\tWHERE\n\t\t\t" + _sqlWhere : ""),
                        (!string.IsNullOrEmpty(fgroup.sq_groupBy) ? "\n\t\tGROUP BY " + fgroup.sq_uniqId : ""),
                        (!string.IsNullOrEmpty(_sqlGroupByWhere) ? "\n\t\tHAVING\n\t\t\t" + _sqlGroupByWhere : ""));
                k++;
            }

            return _subQueries;
        }

        private string sqlFrom(QueryResultType resultType = QueryResultType.pagedView)
        {
            bool viewOnly = resultType == QueryResultType.pagedView || resultType == QueryResultType.viewAggregate;
            #region (l) Required tables
            var core = qDef.dataSource.tableJoins.Where(a => a.core)
                        .Select(a => new { tableAlias = QzLib.tableAlias(a.tableJoin) }).Distinct();

            var filt = filters.Select(a => new { tableAlias = QzLib.tableAlias(a.sqlWhere) }).Distinct();
            var l = core.Union(filt).ToList().SelectMany(a => a.tableAlias).Distinct();

            string[] selects = new string[0];
            if (resultType == QueryResultType.aggregationExport || resultType == QueryResultType.pagedAggregation)
            {
                selects = GetAggregationReferenceSelects();
            }
            else
            {
                selects = viewOnly ? viewSelects : exportSelects;
            }
            var sele = selects.Select(a => new { tableAlias = QzLib.tableAlias(a) }).SelectMany(b => b.tableAlias).Distinct();
            l = l.Union(sele).ToArray().Distinct();

            var depe = qDef.dataSource.tableJoins.Where(a => !string.IsNullOrEmpty(a.depends) &&
                                                                    QzLib.tableAlias(a.tableJoin).Intersect(l).Count() > 0)
                                                        .Select(b => b.depends.Split(',')).SelectMany(c => c).Distinct();

            l = l.Union(depe).ToArray().Distinct();
            #endregion

            var q = qDef.dataSource.tableJoins.Where(a => QzLib.tableAlias(a.tableJoin).Intersect(l).Count() > 0)
                            .Select(b => b.defaultWhere != null
                                            && !sqlWhere.Contains(b.defaultWhere.fieldToCheck)
                                            && !selects.Any(select => select.Contains(b.defaultWhere.fieldToCheck))
                                        ? b.tableJoin + " AND " + b.defaultWhere.whereCondition
                                        : b.tableJoin.Trim());

            return string.Join("\n\t", q.ToArray());

        }

        private string sqlSelect(bool viewOnly = true)
        {
            return string.Join(",\n", (viewOnly ? viewSelects : exportSelects));
        }

        public string sql(
            QueryResultType resultType = QueryResultType.pagedView,
            List<QueryAggregationGroupByFieldPaging> groupByPaging = null)
        {
            string _sqlWhere = sqlWhere;
            bool viewOnly = resultType == QueryResultType.pagedView || resultType == QueryResultType.viewAggregate;

            string fromClause = sqlFrom(resultType);
            string whereClause = !string.IsNullOrEmpty(_sqlWhere) ? "WHERE\n" + _sqlWhere : "";

            if (resultType == QueryResultType.pagedAggregation)
            {
                return GetSqlForPagedAggregation(fromClause, whereClause, groupByPaging);
            }

            if (resultType == QueryResultType.aggregationExport)
            {
                return GetSqlForAggregationExport(fromClause, whereClause);
            }
            int _maxRowsToExport = Query.maxRowsToExport();
            if (qDef.key == "newDistribution")
            {
                _maxRowsToExport *= 2;
            }
            string _sql = String.Format(@"
SELECT DISTINCT {0}
{1}
FROM
{2}
{3}
            ", (!viewOnly && _maxRowsToExport > 0 ? "TOP " + _maxRowsToExport.ToString() + " " : ""), sqlSelect(viewOnly), fromClause, whereClause);

            if (resultType == QueryResultType.viewAggregate)
            {
                return GetSqlForViewAggregate(qDef.viewAggregates, "ORIGINAL", _sql);
            }

            return _sql;
        }

        private string GetSqlForPagedAggregation(
            string from,
            string where,
            List<QueryAggregationGroupByFieldPaging> groupByPaging)
        {
            string noOpSql = "SELECT 0 AS TOTAL";
            if (!(qDef.aggregation?.aggregateColumns?.Any() ?? false))
            {
                return noOpSql;
            }

            var groupByFields = GetInstanceGroupByFields();

            if (!groupByFields.Any())
            {
                // If no grouping fields, SQL will be equivalent to
                // aggregate export SQL, which will be a simple aggregation.
                // There should only be one row, so no paging is needed.
                return GetSqlForAggregationExport(from, where);
            }

            if (groupByFields.Count > Query.maxAggregationDepth)
            {
                throw new InvalidOperationException(
                    $"Aggregation depth cannot be greater than {Query.maxAggregationDepth}.");
            }

            var rankingColumn = qDef.aggregation.aggregateColumns
                .FirstOrDefault(col => col.isOrderingColumn)
                ?? qDef.aggregation.aggregateColumns.FirstOrDefault();

            if (rankingColumn == null)
            {
                throw new InvalidOperationException(
                    "Could not find valid aggregation column to order by.");
            }

            string subQuery = GetAggregateSubQuery(groupByFields, qDef.aggregation.aggregateColumns, from, where);

            var fieldsSeen = new List<CompiledAggregateGroupByField>();
            foreach (var field in groupByFields)
            {
                var compiled = new CompiledAggregateGroupByField()
                {
                    Field = field,
                    DerivedColumnNameWithoutTableAlias = $"{field.key}",
                    AggregateAliases = qDef.aggregation.aggregateColumns
                        .Select(agg => GetGroupedAggregationAlias(field.key, agg.key))
                        .ToList(),
                    RankAlias = GetGroupedAggregationRankAlias(field.key),
                    JoinAlias = GetGroupedAggregationJoinAlias(field.key),
                    DescriptionAlias = !string.IsNullOrEmpty(field.descriptionSelect)
                        ? GetGroupedAggregationDescriptionAlias(field.key)
                        : ""
                };

                var selectsOfPrevious = GetSelectsOfPreviouslySeenAggregateGroups(fieldsSeen);
                var selects = selectsOfPrevious.ToList();

                string subAlias = GetGroupedAggregationSubQueryAlias(field.key);

                string partitionExpression = "";
                if (fieldsSeen.Any())
                {
                    var seenColumns = fieldsSeen.Select(seenField =>
                        $"[{seenField.JoinAlias}].{seenField.DerivedColumnNameWithoutTableAlias}");
                    partitionExpression = $"PARTITION BY {string.Join(",", seenColumns)}";
                }
                string rankingExpression = $"ORDER BY {rankingColumn.function}" +
                    $"([{subAlias}].{rankingColumn.key}) DESC";
                selects.Add(
                    $"ROW_NUMBER() OVER ({partitionExpression} {rankingExpression}) AS [{compiled.RankAlias}]");

                selects.Add($"[{subAlias}].{field.key} AS [{compiled.DerivedColumnNameWithoutTableAlias}]");

                if (!string.IsNullOrEmpty(compiled.DescriptionAlias))
                {
                    selects.Add($"[{subAlias}].{compiled.DescriptionAlias} AS [{compiled.DescriptionAlias}]");
                }

                for (int i = 0; i < qDef.aggregation.aggregateColumns.Length; i++)
                {
                    var aggColumn = qDef.aggregation.aggregateColumns[i];
                    var alias = compiled.AggregateAliases[i];
                    selects.Add($"{aggColumn.function}([{subAlias}].{aggColumn.key}) as [{alias}]");
                }

                string joinPreviousStatement = "";
                if (fieldsSeen.Any())
                {
                    joinPreviousStatement = GetJoinClauseForLastGroupByField(fieldsSeen.Last(), subAlias);
                }

                var groupColumns = selectsOfPrevious.Append($"[{subAlias}].{field.key}").ToList();
                if (!string.IsNullOrEmpty(field.descriptionSelect))
                {
                    groupColumns.Add($"[{subAlias}].{GetGroupedAggregationDescriptionAlias(field.key)}");
                }
                string groupByClause = $"GROUP BY {string.Join(",", groupColumns)}";

                compiled.CompiledQueryAtThisPoint = $"SELECT DISTINCT {string.Join(",", selects)} " +
                    $"FROM ({subQuery}) [{subAlias}] {joinPreviousStatement} {groupByClause}";
                fieldsSeen.Add(compiled);
            }

            var innerQuery = fieldsSeen.Last()?.CompiledQueryAtThisPoint
                ?? throw new InvalidOperationException("Missing compiled grouping query.");
            string innerAlias = "AGG_INNER";

            return $"SELECT {innerAlias}.* FROM ({innerQuery}) {innerAlias} " +
                $"WHERE {GetAggregatePagingConditions(groupByPaging, fieldsSeen, innerAlias)}";
        }

        private string GetJoinClauseForLastGroupByField(CompiledAggregateGroupByField lastField, string subQueryAlias)
        {
            return $"JOIN ({lastField.CompiledQueryAtThisPoint}) [{lastField.JoinAlias}] " +
                $"ON [{subQueryAlias}].{lastField.Field.key} = " +
                $"[{lastField.JoinAlias}].{lastField.DerivedColumnNameWithoutTableAlias} " +
                // Allow for joining on null values for first group's column.  Without this,
                // these are excluded from results.
                $"OR ([{subQueryAlias}].{lastField.Field.key} IS NULL AND " +
                $"[{lastField.JoinAlias}].{lastField.DerivedColumnNameWithoutTableAlias} IS NULL)";
        }

        private string GetAggregateSubQuery(
            IEnumerable<QueryAggregateGroupByField> groupByFields,
            IEnumerable<QueryAggregateColumn> aggregateColumns,
            string from,
            string where)
        {
            var selects = new List<string>();
            foreach (var field in groupByFields)
            {
                selects.Add($"{field.column} AS [{field.key}]");
                if (!string.IsNullOrEmpty(field.descriptionSelect))
                {
                    selects.Add($"{field.descriptionSelect} AS [{GetGroupedAggregationDescriptionAlias(field.key)}]");
                }
            }

            foreach (var aggColumn in aggregateColumns)
            {
                selects.Add($"{aggColumn.column} AS [{aggColumn.key}]");
            }

            return $"SELECT DISTINCT {string.Join(",", selects)} FROM {from} {where}";
        }

        private class CompiledAggregateGroupByField
        {
            public QueryAggregateGroupByField Field { get; set; }
            public string DerivedColumnNameWithoutTableAlias { get; set; }
            public List<string> AggregateAliases { get; set; }
            public string RankAlias { get; set; }
            public string JoinAlias { get; set; }
            public string DescriptionAlias { get; set; }
            public string CompiledQueryAtThisPoint { get; set; }
        }

        private List<string> GetSelectsOfPreviouslySeenAggregateGroups(List<CompiledAggregateGroupByField> fieldsSeen)
        {
            var seenColumns = fieldsSeen.Select(seenField =>
                    $"[{seenField.JoinAlias}].{seenField.DerivedColumnNameWithoutTableAlias}");
            var seenAggregates = fieldsSeen.SelectMany(seenField =>
                seenField.AggregateAliases.Select(alias => $"[{seenField.JoinAlias}].{alias}"));
            var seenRanks = fieldsSeen.Select(seenField => $"[{seenField.JoinAlias}].{seenField.RankAlias}");
            var seenDescriptions = fieldsSeen
                .Where(seenField => !string.IsNullOrEmpty(seenField.DescriptionAlias))
                .Select(seenField => $"[{seenField.JoinAlias}].{seenField.DescriptionAlias}");

            return seenColumns.Concat(
                    seenAggregates.Concat(seenRanks.Concat(seenDescriptions))).ToList();
        }

        private string GetAggregatePagingConditions(
            List<QueryAggregationGroupByFieldPaging> paging,
            List<CompiledAggregateGroupByField> compiledFields,
            string innerQueryAlias)
        {
            // This only works for up to 2 group by fields.
            int defaultPageSize = 5;
            int defaultPage = 1;
            var conditions = new List<string>();
            for (int i = 0; i < compiledFields.Count; i++)
            {
                var field = compiledFields[i];
                var pagingSpecs = paging.Where(p => p.fieldIndex == i);
                string rankColumn = $"{innerQueryAlias}.{field.RankAlias}";
                var fieldConditions = new List<string>();
                if (!(pagingSpecs?.Any() ?? false))
                {
                    fieldConditions.Add(
                        $"{PagingToBooleanCondition(defaultPage, defaultPageSize, rankColumn)}");
                }
                else if (i == 0)
                {
                    // If first group, there will be no paging based on values.
                    // There can only be one effective spec, so pick first one.
                    var spec = pagingSpecs.First();
                    fieldConditions.Add(
                        $"{PagingToBooleanCondition(spec.page, spec.pageSize, rankColumn)}");
                }
                else
                {
                    var previousField = compiledFields[i - 1];
                    string previousColumn = $"{innerQueryAlias}.{previousField.DerivedColumnNameWithoutTableAlias}";
                    var values = new List<string>();
                    foreach (var spec in pagingSpecs)
                    {
                        var pagingCondition = PagingToBooleanCondition(spec.page, spec.pageSize, rankColumn);
                        string formattedValue = FormatPreviousGroupValue(
                            spec.previousFieldValue,
                            spec.previousFieldFormat);
                        values.Add(formattedValue);
                        string op = string.IsNullOrEmpty(spec.previousFieldValue)
                            && spec.previousFieldFormat != "s" ? "IS" : "=";
                        fieldConditions.Add($"({previousColumn} {op} {formattedValue} AND {pagingCondition})");
                    }
                    // Add condition for default paging for all other previous group values.
                    var complementPagingCondition = PagingToBooleanCondition(defaultPage, defaultPageSize, rankColumn);
                    // Since "column NOT IN (NULL)" will always be false.
                    var nonNullValues = values.Where(v => v != "NULL").ToList();
                    var complementValueCondition = nonNullValues.Any()
                        ? $"{previousColumn} NOT IN ({string.Join(",", nonNullValues)})"
                        : "1=1";
                    if (nonNullValues.Count < values.Count)
                    {
                        complementValueCondition += $" AND {previousColumn} IS NOT NULL";
                    }
                    fieldConditions.Add($"({complementValueCondition} AND {complementPagingCondition})");
                }
                conditions.Add(
                    $"({string.Join(" OR ", fieldConditions)})");
            }

            return string.Join(" AND ", conditions);
        }

        private string PagingToBooleanCondition(int page, int pageSize, string rankColumn)
        {
            return $"{rankColumn} BETWEEN {((page - 1) * pageSize) + 1} AND {page * pageSize}";
        }

        private string FormatPreviousGroupValue(string value, string format)
        {
            if (format == "s")
            {
                return string.IsNullOrEmpty(value) ? "''" : $"'{QzLib.sanitize(value)}'";
            }

            if (string.IsNullOrEmpty(value))
            {
                return "NULL";
            }

            return QzLib.sanitize(value);
        }

        private string GetGroupedAggregationColumnAlias(string groupByKey, string aggregationKey)
        {
            return GetGroupedAggregationAlias(groupByKey, aggregationKey);
        }

        private string GetGroupedAggregationRankAlias(string groupByKey)
        {
            return GetGroupedAggregationAlias(groupByKey, "RANK");
        }

        private string GetGroupedAggregationJoinAlias(string groupByKey)
        {
            return GetGroupedAggregationAlias(groupByKey, "JOIN");
        }

        private string GetGroupedAggregationDescriptionAlias(string groupByKey)
        {
            return GetGroupedAggregationAlias(groupByKey, "DESCRIPTION");
        }

        private string GetGroupedAggregationSubQueryAlias(string groupByKey)
        {
            return GetGroupedAggregationAlias(groupByKey, "SUB");
        }

        private string GetGroupedAggregationAlias(string groupByKey, string modifier)
        {
            return $"{groupByKey}_{modifier}"
                .Replace("[", "")
                .Replace("]", "");
        }

        private string GetSqlForAggregationExport(string from, string where)
        {
            string noOpSql = "";
            if (!(qDef.aggregation?.aggregateColumns?.Any() ?? false))
            {
                return noOpSql;
            }

            var groupByFields = GetInstanceGroupByFields();
            string subQuery = GetAggregateSubQuery(groupByFields, qDef.aggregation.aggregateColumns, from, where);
            // Will not be creating joined sub queries per group. Only one sub query is needed, thus only one alias.
            string subAlias = GetGroupedAggregationSubQueryAlias("EXPORT");

            var aggregationSelects = qDef.aggregation.aggregateColumns
                .Select(col => $"{col.function}([{subAlias}].{col.key}) AS [{col.key}]");

            var selects = new List<string>();
            var groupings = new List<string>();
            var havings = new List<string>();
            foreach (var field in groupByFields)
            {
                string fieldColumn = $"[{subAlias}].{field.key}";
                selects.Add($"{fieldColumn} AS [{field.key}]");
                groupings.Add($"{fieldColumn}");
                if (!string.IsNullOrEmpty(field.descriptionSelect))
                {
                    var descriptionColumn = $"[{subAlias}].{GetGroupedAggregationDescriptionAlias(field.key)}";
                    selects.Add($"{descriptionColumn} AS [{field.key}Description]");
                    groupings.Add(descriptionColumn);
                    havings.Add($"GROUPING({fieldColumn}) = GROUPING({descriptionColumn})");
                }
            }

            string selectStatement = $"SELECT DISTINCT TOP {Query.maxRowsToExport()}" +
                $" {string.Join(",", selects.Concat(aggregationSelects))}";

            string groupingStatement = "";
            if (groupings.Any())
            {
                groupingStatement = $"GROUP BY {string.Join(",", groupings)} WITH ROLLUP";
            }

            string havingStatement = "";
            if (havings.Any())
            {
                havingStatement = $"HAVING {string.Join(" AND ", havings)}";
            }

            string orderStatement = "";
            if (groupings.Any())
            {
                orderStatement = $"ORDER BY {string.Join(",", groupings)}";
            }

            return $"{selectStatement} FROM ({subQuery}) [{subAlias}] {groupingStatement} " +
                $"{havingStatement} {orderStatement}";
        }

        private string[] GetAggregationReferenceSelects()
        {
            var aggregationColumns = qDef.aggregation?.aggregateColumns
                ?.Select(col => col.column) ?? new List<string>();
            var instanceGroupByFields = GetInstanceGroupByFields().Select(col => col.column);
            return aggregationColumns.Concat(instanceGroupByFields).ToArray();
        }

        private List<QueryAggregateGroupByField> GetInstanceGroupByFields()
        {
            return (from instField in qIns.aggregateFields ?? new List<QueryAggregateGroupByFieldInstance>()
                    join defField in qDef.aggregation?.groupByFields ?? new QueryAggregateGroupByField[0]
                        on instField.key equals defField.key
                    select defField)
                     ?.Take(Query.maxAggregationDepth)
                     ?.ToList() ?? new List<QueryAggregateGroupByField>();
        }

        private string GetSqlForViewAggregate(
            QueryViewAggregate[] aggregates,
            string originalQueryAlias,
            string originalSql)
        {
            string noOpSql = "SELECT 0 AS AGGREGATE";
            if (string.IsNullOrEmpty(originalSql)
                || string.IsNullOrEmpty(originalQueryAlias)
                || !(aggregates?.Any() ?? false))
            {
                return noOpSql;
            }

            string aggregateColumns = string.Join(
                ",",
                aggregates
                    .Select(agg => CompileViewAggregate(agg, originalQueryAlias))
                    .Where(compiled => !string.IsNullOrEmpty(compiled)));

            if (string.IsNullOrEmpty(aggregateColumns))
            {
                return noOpSql;
            }

            return $"SELECT DISTINCT {aggregateColumns} FROM ({originalSql}) [{originalQueryAlias}]";
        }

        private string CompileViewAggregate(QueryViewAggregate aggregate, string originalQueryAlias)
        {
            if (string.IsNullOrEmpty(originalQueryAlias))
            {
                throw new ArgumentException("Original query alias was null or empty.", nameof(originalQueryAlias));
            }
            if (!ViewAggregateIsCompilable(aggregate)) { return ""; }

            string valueToAggregate = "1";
            if (!string.IsNullOrEmpty(aggregate.viewColumnToAggregate))
            {
                valueToAggregate = $"[{originalQueryAlias}].[{aggregate.viewColumnToAggregate}]";
            }

            return $"{aggregate.aggregateFunction}(CASE WHEN {aggregate.predicate} THEN {valueToAggregate} ELSE 0 END)" +
                $" AS {aggregate.key}";
        }

        private bool ViewAggregateIsCompilable(QueryViewAggregate aggregate)
        {
            return aggregate != null
                && !string.IsNullOrEmpty(aggregate.predicate)
                && !string.IsNullOrEmpty(aggregate.aggregateFunction)
                && !string.IsNullOrEmpty(aggregate.key)
                && (string.IsNullOrEmpty(aggregate.viewColumnToAggregate)
                    || qDef.tableColumns.Any(col => col.key == aggregate.viewColumnToAggregate));
        }
        #endregion

        #region [[ Remote Query }}
        private string getremoteConnstr(int projectid)
        {

            using (var connection = new SqlConnection(Query.dbConnStrGateway))
            {
                string commandText = string.Format("select 'data source=' + db_datasource + ';Initial Catalog=' + db_initialcatalog + ';Persist Security Info=True;User ID=' + db_userid + ';Password=' + db_password + ';' from dbo.project where projectid = {0}", projectid);
                var command = new SqlCommand(commandText, connection) { CommandTimeout = Query.commandTimeout };

                try
                {
                    connection.Open();
                    return (string)command.ExecuteScalar();
                }
                catch { return ""; }
                finally
                { connection.Close(); }
            }
        }

        #endregion

        #region ( Execute )
        public QueryResult execute(QueryRuntimeParams runParams = null)
        {
            if (runParams == null)
            {
                runParams = Query.defaultRuntimeParams;
            }

            bool _viewOnly = runParams.resultType == QueryResultType.pagedView
                || runParams.resultType == QueryResultType.viewAggregate;
            bool isViewAggregate = runParams.resultType == QueryResultType.viewAggregate;
            string sql = this.sql(runParams.resultType, runParams.aggregatePaging);

            // ViewOnly AND No Sort Order BUT default order.  
            if (_viewOnly
                && !isViewAggregate
                && String.IsNullOrEmpty(qOrderBy)
                && this.qDef.tableColumns.Count(a => !String.IsNullOrEmpty(a.order)) > 0)
            {
                String _order = "";
                this.qDef.tableColumns.Where(a => !String.IsNullOrEmpty(a.order)).ToList().ForEach(f =>
                    _order += (!String.IsNullOrEmpty(_order) ? ", " : "")
                                + (f.fieldName.IndexOf("].") >= 0
                                     ? f.fieldName.Substring(f.fieldName.IndexOf("].") + 2, f.fieldName.Length - f.fieldName.IndexOf("].") - 2)
                                     : f.fieldName)
                                + " " + f.order
                );

                qOrderBy = " " + _order;
            }

#if DEBUG
            string sqlFilePath = @"c:/#tmp/test-sql.txt";
            if (File.Exists(sqlFilePath))
                File.WriteAllText(sqlFilePath, sql);
#endif

            try
            {
                System.Diagnostics.Stopwatch stopWatch = new System.Diagnostics.Stopwatch();
                stopWatch.Start();

                dynamic data;
                if (runParams.resultType == QueryResultType.pagedView
                    || runParams.resultType == QueryResultType.allDataset
                    || isViewAggregate
                    || runParams.resultType == QueryResultType.pagedAggregation
                    || runParams.resultType == QueryResultType.pagedExport)
                {
                    if (this.qDef.remoteQuery && this.qIns.remoteQueryProjId > 0)
                    {
                        // for query that runs on a different database
                        string remoteconnstr = getremoteConnstr((int)this.qIns.remoteQueryProjId);
                        data = QzLib.runSqlToList(
                                runParams.resultType,
                                sql,
                                remoteconnstr,
                                qOrderBy,
                                runParams.pageSize,
                                runParams.page,
                                qDef.sq_uniqId);
                    }
                    else
                    {
                        data = QzLib.runSqlToList(
                            runParams.resultType,
                            sql,
                            qDef.dbConnectionString,
                            qOrderBy,
                            runParams.pageSize,
                            runParams.page,
                            qDef.sq_uniqId);
                    }
                }
                else
                {
                    if (runParams.resultType == QueryResultType.allDatasetExport
                        && qDef.exportProcedureWrapper != null)
                    {
                        sql = WrapWithExportProcedure(sql);
                    }

                    if (this.qDef.remoteQuery && this.qIns.remoteQueryProjId > 0)
                    {
                        // for query that runs on a different database
                        string remoteconnstr = getremoteConnstr((int)this.qIns.remoteQueryProjId);
                        data = QzLib.runSql(sql, "result", 0, 0, remoteconnstr).Tables[0];
                    }
                    else
                    {
                        data = QzLib.runSql(sql, "result", 0, 0, qDef.dbConnectionString).Tables[0];
                    }
                }

                QueryResult qResult = new QueryResult() { success = true };
                if (data != null)
                {
                    if (runParams.resultType == QueryResultType.pagedView)
                    {
                        List<dynamic> dataL = data as List<dynamic>;
                        var firstRow = (IDictionary<string, object>)dataL?.FirstOrDefault();
                        qResult.__count = firstRow != null && !String.IsNullOrEmpty(Convert.ToString(firstRow["count_"]))
                            ? Convert.ToInt32(firstRow["count_"]) : 0;
                        qResult.results = dataL;
                    }
                    else if (
                        runParams.resultType == QueryResultType.allDataset
                        || isViewAggregate
                        || runParams.resultType == QueryResultType.pagedAggregation
                        || runParams.resultType == QueryResultType.pagedExport)
                    {
                        List<dynamic> dataL = data as List<dynamic>;
                        qResult.__count = dataL?.Count ?? 0;
                        qResult.results = dataL;
                    }
                    else // runParams.resultType == QueryResultType.allDatasetExport
                    {
                        qResult.__count = data.Rows.Count;
                        qResult.results = data as DataTable;
                    }

                }

                string resultTypeLabel = GetResultTypeLabel(runParams.resultType);

                qResult.logId = QueryManager.create_a_sqlLog(
                    sql.Trim(),
                    $"{qDef.label} {resultTypeLabel}",
                    sqlWhere,
                    crmSession.UID().Value,
                    qResult.__count,
                    sqlSelect(runParams.resultType == QueryResultType.pagedView),
                    (runParams.resultType == QueryResultType.pagedView ? "" : "HTTP"),
                     Convert.ToSingle(stopWatch.ElapsedMilliseconds / 1000.0));
                stopWatch.Stop();

                qResult.queryLogId = QueryManager.logSearch(qIns)?.QueryLogId ?? 0;

                return qResult;
            }
            catch (Exception ex)
            {
                return new QueryResult()
                {
                    success = false,
                    __count = 0,
                    results = null,
                    exceptionMessage = ex.Message
                };
            }
        }

        private string GetResultTypeLabel(QueryResultType resultType)
        {
            switch (resultType)
            {
                case QueryResultType.pagedView:
                    return "(View)";
                case QueryResultType.allDatasetExport:
                case QueryResultType.allDataset:
                    return "(Export)";
                case QueryResultType.viewAggregate:
                    return "(ViewAggregate)";
                case QueryResultType.pagedAggregation:
                    return "(PagedAggregation)";
                case QueryResultType.aggregationExport:
                    return "(AggregationExport)";
                case QueryResultType.pagedExport:
                    return "(PagedExport)";
                default:
                    throw new ArgumentException($"Invalid {nameof(QueryResultType)}.", nameof(resultType));
            }
        }
        #endregion

        private string WrapWithExportProcedure(string querySql)
        {
            if (string.IsNullOrEmpty(querySql))
            {
                throw new ArgumentException("Query SQL was null or empty", nameof(querySql));
            }

            var procedureName = qDef?.exportProcedureWrapper?.ProcedureName;
            if (string.IsNullOrEmpty(procedureName))
            {
                throw new InvalidOperationException("Export procedure name was null or empty.");
            }

            var parameterValues = qDef.exportProcedureWrapper.Parameters
                ?.Select(parameter =>
                {
                    if (parameter.UseQuerySql)
                    {
                        return querySql.Replace("'", "''");
                    }

                    if (!string.IsNullOrEmpty(parameter.UseFilterValue))
                    {
                        var filter = qIns.filters
                            .Where(f => f.key == parameter.UseFilterValue)
                            .FirstOrDefault();
                        return filter?.values?[0] ?? "";
                    }

                    return "";
                })
                .Select(parameterValue => $"'{parameterValue ?? ""}'")
                ?.ToList() ?? new List<string>();

            return $"EXEC {procedureName} {string.Join(",", parameterValues)}";
        }
    }
    #region [ QueryRun Child Classes ]
    public class QueryRuntimeFilter
    {
        public QueryRuntimeFilter(string key_, string[] values_, int operatorIdx_, string fieldType_, string fieldName_, bool hasCustomOperators_, string reqdProcData_, string connectionString_)
        {
            key = key_;
            connectionString = connectionString_;
            opSymbol = QzLib.getOpSymbol(fieldType_, operatorIdx_, hasCustomOperators_);
            values = values_.Select(a => enclose(fieldType_, opSymbol, handleReqdProc(reqdProcData_, QzLib.sanitize(a), opSymbol, "value", connectionString))).ToArray();
            fieldType = fieldType_;
            fieldName = (!string.IsNullOrEmpty(reqdProcData_) && reqdProcData_.Split('|')[2] == "field"
                               ? handleReqdProc(reqdProcData_, values[0], opSymbol, "field", connectionString).Trim()
                               : fieldName_);
        }

        #region ( private var/func )
        private string key;
        private string fieldType;
        private string fieldName;
        private string opSymbol;
        private string[] values;
        private string connectionString;

        #region (Put comma if it's a list; Enclose w/ a quote if it's a string or date)
        private static string enclose(string fieldType, string opSymbol, string value)
        {
            value = value.Trim().Replace("'", "''");

            if ((fieldType == "s") && "IN|!IN".Contains(opSymbol))
            {
                value = value.Replace("|", ",");
                string[] values = value.Split(',').Where(a => !string.IsNullOrEmpty(a)).ToArray(); ;
                values = values.Select(a => "'" + a.Trim() + "'").ToArray();
                value = string.Join(",", values);
            }
            else if (fieldType == "n" && "IN|!IN".Contains(opSymbol))
            {
                value = value.Replace("|", ",");
                string[] values = value.Split(',').Where(a => !string.IsNullOrEmpty(a)).ToArray();
                values = values.Select(a => a.Trim()).ToArray();
                value = string.Join(",", values);
            }
            else if (fieldType == "s" && !string.IsNullOrEmpty(value))
                value = string.Format("'{0}{1}{2}'", opSymbol.StartsWith("%L") ? "%" : "", value.Trim(), opSymbol.EndsWith("E%") ? "%" : "");
            else if (fieldType == "d")
                value = "'" + value.Trim() + "'";

            return value;
        }
        #endregion

        #region (Handle a required processing for updating field/value)
        public static string handleReqdProc(string data, string value, string opSymbol, string replProperty, string connectionString)
        {
            if (String.IsNullOrEmpty(data))
                return value;

            String[] _o = data.Split('|');

            // Simple Replacement
            if (_o[0] == "n" && (string.IsNullOrEmpty(_o[3]) || _o[3] == opSymbol) && !string.IsNullOrEmpty(_o[1]) && _o[2] == replProperty)
            {
                string repl = Library.util.regExp_match(_o[1], @"<r>[\s\S]*?<\/r>");
                if (!string.IsNullOrEmpty(repl))
                {
                    repl = _o[1].Replace(repl, value);
                    return repl;
                }
                else
                    return value;
            }

            // Replacement with DB-processed
            if (_o[0] == "d" && (string.IsNullOrEmpty(_o[3]) || _o[3] == opSymbol) && !string.IsNullOrEmpty(_o[1]) && _o[2] == replProperty)
            {
                string valueToPass = value.Replace("'", "''").Replace("|", ",");

                string sql = _o[1];
                string repl = Library.util.regExp_match(_o[1], @"<r>[\s\S]*?<\/r>");
                if (!string.IsNullOrEmpty(repl))
                    sql = sql.Replace(repl, valueToPass);

                string _dv = "";
                System.Data.DataSet sqlSelect = QzLib.runSql(sql, "result", 0, 0, connectionString);
                if (sqlSelect != null && sqlSelect.Tables.Count > 0 && sqlSelect.Tables[0].Rows.Count > 0)
                {
                    for (int rowcount = 0; rowcount < sqlSelect.Tables[0].Rows.Count; rowcount++)
                        _dv = _dv + sqlSelect.Tables[0].Rows[rowcount][0].ToString();
                }
                return _dv;
            }
            return value;
        }
        #endregion
        #endregion

        public string Key { get { return key; } }
        #region [ (prop)sqlWhere ]
        public string sqlWhere
        {
            get
            {
                switch (opSymbol)
                {
                    case "TRUE":
                    case "HAVINGALL":
                        return fieldName;

                    case "eLIKE":
                    case "LIKE%":
                    case "%LIKE":
                    case "%LIKE%":
                        return string.Format("{0} LIKE {1}", fieldName, values[0]);
                    case "NULL":
                    case "!NULL":
                        if (fieldType == "s")
                            return string.Format("ISNULL({0},''){1}''", fieldName, opSymbol.StartsWith("!") ? "<>" : "=");
                        else
                            return string.Format("{0} IS{1}NULL", fieldName, opSymbol.StartsWith("!") ? " NOT " : " ");
                    case "IN":
                    case "!IN":
                        return string.Format("{0}{1}IN ({2})", fieldName, opSymbol.StartsWith("!") ? " NOT " : " ", values[0]);

                    case ":":
                    case ":$":
                    case "On":
                        return string.Format("{0} = {1}", fieldName, values[0]);
                    case ">=":
                    case "<=":
                    case ">=$":
                    case "<=$":
                    case "On/Before":
                    case "On/After":
                        return string.Format("{0} {1} {2}", fieldName, opSymbol.Replace("$", "").Replace("On/After", ">=").Replace("On/Before", "<="), values[0]);
                    case "!=":
                    case "!=$":
                        return string.Format("{0} <> {1}", fieldName, values[0]);
                    case "nBTW":
                    case "cBTW":
                    case "dBTW":
                        return string.Format("{0} BETWEEN {1} AND {2}", fieldName, values[0], values[1]);
                    case "sBTW":
                        return fieldName.Replace("values[0]", values[0]).Replace("values[1]", values[1]);
                    case "FX-today":
                        return String.Format("{0} >= '{1}' AND {0} <= '{2}'", fieldName, String.Format("{0:MM/dd/yyyy HH:mm:ss}", System.DateTime.Today.Date), String.Format("{0:MM/dd/yyyy HH:mm:ss}", System.DateTime.Today.Date.AddSeconds(86399)));
                    case "FX-yesterday":
                        return String.Format("{0} >= '{1}' AND {0} <= '{2}'", fieldName, String.Format("{0:MM/dd/yyyy HH:mm:ss}", System.DateTime.Today.Date.AddDays(-1)), String.Format("{0:MM/dd/yyyy HH:mm:ss}", System.DateTime.Today.Date.AddDays(-1).AddSeconds(86399)));
                    case "FX-wk":
                        return String.Format("{0} BETWEEN '{1}' AND '{2}'", fieldName, String.Format("{0:MM/dd/yyyy HH:mm:ss}", Library.util.getFirstDayOfWeek(System.DateTime.Today)), String.Format("{0:MM/dd/yyyy HH:mm:ss}", Library.util.getLastDayOfWeek(System.DateTime.Today).Date.AddSeconds(86399)));
                    case "FX-mo":
                        return String.Format("{0} BETWEEN '{1}' AND '{2}'", fieldName, String.Format("{0:MM/dd/yyyy HH:mm:ss}", Library.util.getFirstDayOfMonth(System.DateTime.Today)), String.Format("{0:MM/dd/yyyy HH:mm:ss}", Library.util.getLastDayOfMonth(System.DateTime.Today).Date.AddSeconds(86399)));
                    case "FX-qt":
                        return String.Format("{0} BETWEEN '{1}' AND '{2}'", fieldName, String.Format("{0:MM/dd/yyyy HH:mm:ss}", Library.util.getFirstDayOfQuarter(System.DateTime.Today)), String.Format("{0:MM/dd/yyyy HH:mm:ss}", Library.util.getLastDayOfQuarter(System.DateTime.Today).Date.AddSeconds(86399)));
                    case "FX-yr":
                        return String.Format("{0} BETWEEN '{1}' AND '{2}'", fieldName, String.Format("{0:MM/dd/yyyy HH:mm:ss}", Library.util.getFirstDayOfYear(System.DateTime.Today)), String.Format("{0:MM/dd/yyyy HH:mm:ss}", Library.util.getLastDayOfYear(System.DateTime.Today).Date.AddSeconds(86399)));
                    case "FX-lwk":
                        return String.Format("{0} BETWEEN '{1}' AND '{2}'", fieldName, String.Format("{0:MM/dd/yyyy HH:mm:ss}", Library.util.getFirstDayOfWeek(System.DateTime.Today.AddDays(-7))), String.Format("{0:MM/dd/yyyy HH:mm:ss}", Library.util.getLastDayOfWeek(System.DateTime.Today.AddDays(-7)).Date.AddSeconds(86399)));
                    case "FX-lmo":
                        return String.Format("{0} BETWEEN '{1}' AND '{2}'", fieldName, String.Format("{0:MM/dd/yyyy HH:mm:ss}", Library.util.getFirstDayOfMonth(System.DateTime.Today.AddMonths(-1))), String.Format("{0:MM/dd/yyyy HH:mm:ss}", Library.util.getLastDayOfMonth(System.DateTime.Today.AddMonths(-1)).Date.AddSeconds(86399)));
                    case "FX-lqt":
                        return String.Format("{0} BETWEEN '{1}' AND '{2}'", fieldName, String.Format("{0:MM/dd/yyyy HH:mm:ss}", Library.util.getFirstDayOfQuarter(System.DateTime.Today.AddMonths(-3))), String.Format("{0:MM/dd/yyyy HH:mm:ss}", Library.util.getLastDayOfQuarter(System.DateTime.Today.AddMonths(-3)).Date.AddSeconds(86399)));
                    case "FX-lyr":
                        return String.Format("{0} BETWEEN '{1}' AND '{2}'", fieldName, String.Format("{0:MM/dd/yyyy HH:mm:ss}", Library.util.getFirstDayOfYear(System.DateTime.Today.AddYears(-1))), String.Format("{0:MM/dd/yyyy HH:mm:ss}", Library.util.getLastDayOfYear(System.DateTime.Today.AddYears(-1)).Date.AddSeconds(86399)));
                    case "FX-l7d":
                        return String.Format("{0} BETWEEN '{1}' AND '{2}'", fieldName, String.Format("{0:MM/dd/yyyy HH:mm:ss}", System.DateTime.Today.AddDays(-7).Date), String.Format("{0:MM/dd/yyyy HH:mm:ss}", System.DateTime.Today.Date.AddSeconds(86399)));
                    case "FX-l30d":
                        return String.Format("{0} BETWEEN '{1}' AND '{2}'", fieldName, String.Format("{0:MM/dd/yyyy HH:mm:ss}", System.DateTime.Today.AddDays(-30).Date), String.Format("{0:MM/dd/yyyy HH:mm:ss}", System.DateTime.Today.Date.AddSeconds(86399)));
                    case "FX-l3m":
                        return String.Format("{0} BETWEEN '{1}' AND '{2}'", fieldName, String.Format("{0:MM/dd/yyyy HH:mm:ss}", System.DateTime.Today.AddMonths(-3).Date), String.Format("{0:MM/dd/yyyy HH:mm:ss}", System.DateTime.Today.Date.AddSeconds(86399)));
                    case "FX-l6m":
                        return String.Format("{0} BETWEEN '{1}' AND '{2}'", fieldName, String.Format("{0:MM/dd/yyyy HH:mm:ss}", System.DateTime.Today.AddMonths(-6).Date), String.Format("{0:MM/dd/yyyy HH:mm:ss}", System.DateTime.Today.Date.AddSeconds(86399)));
                    case "FX-l1y":
                        return String.Format("{0} BETWEEN '{1}' AND '{2}'", fieldName, String.Format("{0:MM/dd/yyyy HH:mm:ss}", System.DateTime.Today.AddYears(-1).Date), String.Format("{0:MM/dd/yyyy HH:mm:ss}", System.DateTime.Today.Date.AddSeconds(86399)));
                    case "FX-ftd":
                        return String.Format("{0} BETWEEN '{1}' AND '{2}'", fieldName, String.Format("{0:MM/dd/yyyy HH:mm:ss}", (crmSession.ftdCycleStart() == null ? util.getFirstDayOfYear(System.DateTime.Today) : crmSession.ftdCycleStart())), String.Format("{0:MM/dd/yyyy HH:mm:ss}", (crmSession.ftdCycleEnd() == null ? util.getLastDayOfYear(System.DateTime.Today).Date.AddSeconds(86399) : ((DateTime)crmSession.ftdCycleEnd()).AddSeconds(86399))));
                    case "FX-lftd":
                        return String.Format("{0} BETWEEN '{1}' AND '{2}'", fieldName, String.Format("{0:MM/dd/yyyy HH:mm:ss}", (crmSession.ftdCycleStart() == null ? util.getFirstDayOfYear(System.DateTime.Today.AddYears(-1)) : ((DateTime)crmSession.ftdCycleStart()).AddYears(-1).Date)), String.Format("{0:MM/dd/yyyy HH:mm:ss}", (crmSession.ftdCycleEnd() == null ? util.getLastDayOfYear(System.DateTime.Today.AddYears(-1)).Date.AddSeconds(86399) : ((DateTime)crmSession.ftdCycleEnd()).AddYears(-1).Date.AddSeconds(86399))));


                }
                return "";
            }
        }
        #endregion

    }

    public class QueryRuntimeSort
    {
        public string field { get; set; }
        public string dir { get; set; } = "asc";   // asc | desc
    }

    public class QueryRuntimeParams
    {
        public QueryResultType resultType { get; set; } = QueryResultType.pagedView;
        public int pageSize { get; set; } = 10;
        public int page { get; set; } = 1;
        public QueryRuntimeSort[] sort { get; set; }
        public List<QueryAggregationGroupByFieldPaging> aggregatePaging { get; set; }
            = new List<QueryAggregationGroupByFieldPaging>();

        public string sqlOrderBy
        {
            get
            {
                if (sort != null && sort.Count() > 0)
                {
                    string s = "";
                    foreach (QueryRuntimeSort item in sort)
                        s += (!string.IsNullOrEmpty(item.field) ? " " + QzLib.sanitize(item.field) + (item.dir == "desc" ? " DESC" : "") : "");

                    return s;
                }
                else
                    return "";
            }
        }
    }

    public class QueryRunViewParams
    {
        public QueryInstanceInfo qInstance { get; set; }
        public int page { get; set; }
        public int pageSize { get; set; }
        public QueryRuntimeSort[] sort { get; set; }
    }

    public class QueryRunAggregateParams
    {
        public QueryInstanceInfo qInstance { get; set; }
        public List<QueryAggregationGroupByFieldPaging> paging { get; set; }
    }

    public class QueryAggregationGroupByFieldPaging
    {
        public int fieldIndex { get; set; }
        public string previousFieldValue { get; set; }
        public string previousFieldFormat { get; set; } = "s";
        public int page { get; set; } = 1;
        public int pageSize { get; set; } = 5;
    }

    #endregion

    public enum QueryResultType
    {
        pagedView,
        allDataset,
        allDatasetExport,
        viewAggregate,
        pagedAggregation,
        aggregationExport,
        pagedExport
    }

    public class QueryResult
    {
        public bool success { get; set; }
        public int __count { get; set; }
        public string message { get; set; }
        public dynamic results { get; set; } // IEnumerable<object> | DataTable
        public string exceptionMessage { get; set; }
        public int logId { get; set; }
        public int queryLogId { get; set; }
    }

    public class queryLookUp
    {
        public string queryName { get; set; }
        public string caption { get; set; }
        public string aElementIdentifier { get; set; }
        public bool haveAccess { get; set; }
        public bool quickSearch { get; set; }
        public string configKey { get; set; }
        public string configVal { get; set; }
        public string version { get; set; }
    }

    #region [ QueryManager - Open/Save/Log ]
    public static class QueryManager
    {
        #region ( log a query/export in its Crimson DB )
        public static int create_a_sqlLog(string p_sql, string p_calledFrom, string p_filtering, int p_userId, int p_count, string p_columnNames, string p_savedPath, Single? duration = null)
        {

            #region [ Create a SQL log ]
            using (var connection = new SqlConnection(Query.dbConnStr))
            {
                string logIdColumn = "sqlLogId";
                string sql = $"INSERT INTO ssSQLLOG(" +
                    $"[UID], calledFROM, RUNTIME, [SQL], noRECS, SAVED, SAVEDPATH, outputFIELDS, filtering, duration)" +
                    $" VALUES({p_userId},'{p_calledFrom}','{DateTime.Now:yyyy-MM-dd HH:mm:ss}'," +
                    $"'{p_sql.Replace("'", "''")}',{p_count},{(!string.IsNullOrEmpty(p_savedPath) ? 1 : 0)}," +
                    $"'{p_savedPath}','{p_columnNames.Replace("'", "''")}'," +
                    $"'{p_filtering.Replace("'", "''")}',{duration});" +
                    $" SELECT CONVERT(INT, ISNULL(SCOPE_IDENTITY(),0)) AS {logIdColumn}";

                var command = new SqlCommand(sql, connection) { CommandTimeout = Query.commandTimeout };
                int sqlLogId = 0;

                try
                {
                    connection.Open();
                    using (var reader = command.ExecuteReader())
                    {
                        if (reader.Read())
                        {
                            var logId = reader[logIdColumn];
                            if (logId is int)
                            {
                                sqlLogId = (int)logId;
                            }
                        }
                    }
                }
                catch (Exception ex)
                {
                    util.recordInErrorLog(
                        $"Encountered while creating ssSQLLOG record: {ex.GetType().Name} - {ex.Message}");
                }
                finally
                {
                    connection.Close();
                }

                return sqlLogId;
            }
            #endregion
        }
        #endregion

        #region ( Check if a search name is unique to be used )
        public static bool checkIfUniqueName(string name, string key = "")
        {
            using (var connection = new SqlConnection(Query.dbConnStrGateway))
            {
                string commandText = string.Format("SELECT dbo.[qryIfCheckUniqueName]({0}, '{1}', '{2}','{3}')",
                            session.currentDomain_projectId,
                            session.userSession.UserId,
                            QzLib.sanitize(key).Replace("'", "''"),
                            QzLib.sanitize(name).Replace("'", "''"));
                var command = new SqlCommand(commandText, connection) { CommandTimeout = Query.commandTimeout };

                try
                {
                    connection.Open();
                    return (bool)command.ExecuteScalar();
                }
                catch { return false; }
                finally
                { connection.Close(); }
            }
        }
        #endregion

        #region ( Save a query instance info )
        public static QueryInstanceInfo saveQuery(QueryInstanceInfo qInst)
        {
            #region Required updates
            if (string.IsNullOrEmpty(qInst.key))
            {
                qInst.key = System.Guid.NewGuid().ToString().ToLower();
                qInst.createDate = System.DateTime.Now;
                qInst.lastModifiedDate = qInst.createDate;
            }
            else
                qInst.lastModifiedDate = System.DateTime.Now;
            qInst.lastRunDate = qInst.lastModifiedDate;
            #endregion

            string qInstStr = new JavaScriptSerializer().Serialize(qInst);
            using (var connection = new SqlConnection(Query.dbConnStrGateway))
            {
                string commandText = string.Format("EXEC dbo.qryUpsert {0}, '{1}', '{2}', '{3}', '{4}', '{5:yyyy-MM-dd HH:mm:ss}', '{6:yyyy-MM-dd HH:mm:ss}', '{7:yyyy-MM-dd HH:mm:ss}', N'{8}'",
                            session.currentDomain_projectId, session.userSession.UserId,
                            qInst.key, qInst.defKey,
                            QzLib.sanitize(qInst.name).Replace("'", "''"),
                            qInst.createDate, qInst.lastModifiedDate, qInst.lastRunDate,
                            qInstStr.Replace("'", "''"));
                var command = new SqlCommand(commandText, connection) { CommandTimeout = Query.commandTimeout };

                try
                {
                    connection.Open();
                    bool successful = (bool)command.ExecuteScalar();
                    if (successful)
                        return qInst;
                    else
                        return null;
                }
                catch { return null; }
                finally
                { connection.Close(); }
            }
        }
        #endregion

        #region ( Log a query run )
        public static QueryLogResult logSearch(QueryInstanceInfo qInst)
        {
            qInst.createDate = null;
            qInst.lastModifiedDate = null;
            qInst.lastRunDate = null;

            string qInstStr = new JavaScriptSerializer().Serialize(qInst);
            using (var connection = new SqlConnection(Query.dbConnStrGateway))
            {
                string commandText = string.Format("EXEC dbo.qryLogIt {0}, '{1}', '{2}', '{3}', '{4}', N'{5}'",
                            session.currentDomain_projectId, session.userSession.UserId,
                            qInst.key, qInst.defKey,
                            QzLib.sanitize(qInst.name)?.Replace("'", "''"),
                            qInstStr.Replace("'", "''"));
                var command = new SqlCommand(commandText, connection)
                {
                    CommandTimeout = Query.commandTimeout
                };

                try
                {
                    var result = new QueryLogResult();
                    connection.Open();
                    using (var reader = command.ExecuteReader())
                    {
                        if (reader.Read())
                        {
                            result.IsSuccessful = reader.GetBoolean(0);
                            result.QueryLogId = reader.GetInt32(1);
                        }
                    }
                    return result;
                }
                catch
                {
                    return new QueryLogResult { IsSuccessful = false };
                }
                finally
                {
                    connection.Close();
                }
            }
        }

        public class QueryLogResult
        {
            public bool IsSuccessful { get; set; }
            public int QueryLogId { get; set; }
        }
        #endregion

        #region ( Open Recent Searches )
        public static List<QueryInstanceInfo> myRecentSearches(string defKey, int noSearches = 10)
        {
            using (var connection = new SqlConnection(Query.dbConnStrGateway))
            {
                string commandText = string.Format("SELECT * FROM dbo.[qryMyRecentSearches]({0}, '{1}', '{2}', {3})",
                            session.currentDomain_projectId, session.userSession.UserId,
                            defKey, noSearches);
                var command = new SqlCommand(commandText, connection) { CommandTimeout = Query.commandTimeout };

                try
                {
                    connection.Open();
                    using (var reader = command.ExecuteReader())
                    {
                        List<QueryInstanceInfo> list = new List<QueryInstanceInfo>();
                        while (reader.Read())
                        {
                            string qInstStr = reader.GetString(0);
                            DateTime runDate = reader.GetDateTime(1);
                            QueryInstanceInfo qInst = new JavaScriptSerializer().Deserialize<QueryInstanceInfo>(qInstStr);
                            qInst.lastRunDate = runDate;
                            list.Add(qInst);
                        }
                        return list;
                    }
                }
                catch { return new List<QueryInstanceInfo>(); }
                finally
                { connection.Close(); }
            }
        }
        #endregion

        public static Task<SavedQueryInstance> GetQueryInstanceAsync(int id)
        {
            return GetQueryInstanceFromTableAsync(id, "qrySaved", "qrySaveId");
        }

        public static Task<SavedQueryInstance> GetQueryInstanceFromLogAsync(int id)
        {
            return GetQueryInstanceFromTableAsync(id, "qryLog", "qryLogId");
        }

        private static async Task<SavedQueryInstance> GetQueryInstanceFromTableAsync(
            int recordId,
            string sourceTable,
            string idColumn)
        {
            using (var connection = new SqlConnection(Query.dbConnStrGateway))
            using (var command = connection.CreateCommand())
            {
                try
                {
                    string parameterName = "@id";
                    command.CommandText = $"SELECT projectId, qInst FROM [{sourceTable}] WHERE [{idColumn}] = {parameterName}";
                    command.Parameters.AddWithValue(parameterName, recordId);
                    await connection.OpenAsync();
                    using (var reader = await command.ExecuteReaderAsync())
                    {
                        // Should be no more than 1 record.
                        await reader.ReadAsync();
                        return new SavedQueryInstance
                        {
                            ProjectId = reader.GetInt32(0),
                            QueryInstance = JsonConvert.DeserializeObject<QueryInstanceInfo>(reader.GetString(1))
                        };
                    }
                }
                catch (Exception ex)
                {
                    util.recordInErrorLog(
                        $"Encountered exception when attempting to retrieve record {recordId} " +
                        $"from {sourceTable} table. {ex.GetType().Name}: {ex.Message}");
                    return null;
                }
                finally
                {
                    connection.Close();
                }
            }
        }

        #region ( Open Saved Searches )
        public static List<QueryInstanceInfo> mySavedSearches(string defKey, string key, int noSearches = -1)
        {
            using (var connection = new SqlConnection(Query.dbConnStrGateway))
            {
                string commandText = string.Format("SELECT * FROM dbo.[qryMySavedSearches]({0}, '{1}', '{2}', '{3}', {4})",
                            session.currentDomain_projectId, session.userSession.UserId,
                            defKey, key, noSearches);
                var command = new SqlCommand(commandText, connection) { CommandTimeout = Query.commandTimeout };

                try
                {
                    connection.Open();
                    using (var reader = command.ExecuteReader())
                    {
                        List<QueryInstanceInfo> list = new List<QueryInstanceInfo>();
                        while (reader.Read())
                        {
                            QueryInstanceInfo qInst = new JavaScriptSerializer().Deserialize<QueryInstanceInfo>(reader.GetString(0));
                            list.Add(qInst);
                        }
                        return list;
                    }
                }
                catch { return new List<QueryInstanceInfo>(); }
                finally
                { connection.Close(); }
            }
        }
        #endregion

        #region ( Delete a search )
        public static bool deleteSearch(string key)
        {
            using (var connection = new SqlConnection(Query.dbConnStrGateway))
            {
                string commandText = string.Format("DELETE qrySaved WHERE projectId={0} AND userId='{1}' AND [key]='{2}'; SELECT CAST(CASE WHEN @@ROWCOUNT>0 THEN 1 ELSE 0 END AS BIT)",
                            session.currentDomain_projectId, session.userSession.UserId, key);
                var command = new SqlCommand(commandText, connection) { CommandTimeout = Query.commandTimeout };

                try
                {
                    connection.Open();
                    return (bool)command.ExecuteScalar();
                }
                catch { return false; }
                finally
                { connection.Close(); }
            }

        }
        #endregion
    }
    #endregion

    #region [ Library ]
    public static class QzLib {

        #region ( Operator Symbols ) 
        #region (Operator Symbols) Sequence & Representation should be same as query-operators.ts
        public static Dictionary<string, string> _Symbols = new Dictionary<string, string>
        {
            {"b:0",  "[[ TRUE ]]" },

            {"a:0",  "[[ HAVINGALL ]]"},

            {"x:0",  "[[ HAVINGALL ]]"},

            {"f:0",  "[[ sBTW ]]"},

            {"s:0", "[[ eLIKE ]]"},
            {"s:1", "[[ LIKE% ]]"},
            {"s:2", "[[ %LIKE ]]"},
            {"s:3", "[[ %LIKE% ]]"},
            {"s:4", "[[ NULL ]]"},
            {"s:5", "[[ !NULL ]]"},
            {"s:6", "[[ IN ]]"},
            {"s:7", "[[ !IN ]]"},

            {"n:0", "[[: ]]"},
            {"n:1", "[[ >= ]]"},
            {"n:2", "[[ <= ]]"},
            {"n:3", "[[ != ]]"},
            {"n:4", "[[ nBTW ]]"},

            {"c:0", "[[:$ ]]"},
            {"c:1", "[[ >=$ ]]"},
            {"c:2", "[[ <=$ ]]"},
            {"c:3", "[[ !=$ ]]"},
            {"c:4", "[[ cBTW ]]"},

            {"d:0", "[[ dYET ]]"},
            {"d:1", "[[ On ]]"},
            {"d:2", "[[ On/After ]]"},
            {"d:3", "[[ On/Before ]]"},
            {"d:4", "[[ dBTW ]]"},
            {"d:5", "[[ FX-today ]]"},
            {"d:6", "[[ FX-yesterday ]]"},
            {"d:7", "[[ FX-wk ]]"},
            {"d:8", "[[ FX-mo ]]"},
            {"d:9", "[[ FX-qt ]]"},
            {"d:10", "[[ FX-yr ]]"},
            {"d:11", "[[ FX-ftd ]]"},
            {"d:12", "[[ FX-lwk ]]"},
            {"d:13", "[[ FX-lmo ]]"},
            {"d:14", "[[ FX-lqt ]]"},
            {"d:15", "[[ FX-lyr ]]"},
            {"d:16", "[[ FX-lftd ]]"},
            {"d:17", "[[ FX-l7d ]]"},
            {"d:18", "[[ FX-l30d ]]"},
            {"d:19", "[[ FX-l3m ]]"},
            {"d:20", "[[ FX-l6m ]]"},
            {"d:21", "[[ FX-l1y ]]"}
        };
        #endregion

        #region (Operator Symbols w/o values)
        public const string NoValSymbols = @"
            [[ NULL ]]
            [[ !NULL ]]
            [[ FX-today ]]
            [[ FX-yesterday ]]
            [[ FX-wk ]]
            [[ FX-mo ]]
            [[ FX-qt ]]
            [[ FX-yr ]]
            [[ FX-lwk ]]
            [[ FX-lmo ]]
            [[ FX-lqt ]]
            [[ FX-lyr ]]
            [[ FX-l7d ]]
            [[ FX-l30d ]]
            [[ FX-l3m ]]
            [[ FX-l6m ]]
            [[ FX-l1y ]]
            [[ FX-ftd ]]
            [[ FX-lftd ]]
        ";
        #endregion
        public static string getOpSymbol(string fieldType, int operatorIdx, bool hasCustomOperators)
        {
            if (hasCustomOperators)
                return "IN";

            string symbol;
            if (_Symbols.TryGetValue(fieldType + ":" + operatorIdx.ToString(), out symbol))
                return symbol.Replace("[", "").Replace("]", "").Trim();
            else
                return "";
        }
        #endregion

        #region ( Handle <r/> tag )
        public static string handleRepl(string replXmlTag, string connectionString)
        {
            if (string.IsNullOrEmpty(replXmlTag))
                return "";

            String[] _o = replXmlTag.Replace(@"<r>", "").Replace(@"</r>", "").Split('|');

            switch (_o[0])
            {
                case "c":
                    string _cv = applica.getConfigVal(_o[1], session.currentDomain_project.appId, session.currentDomain_projectId);
                    return (string.IsNullOrEmpty(_cv) ? _o[2] : _cv.Replace(" ", "").Replace("(", "").Replace(")", "").Replace("/", ""));
                case "d":
                    string _dv = "";
                    System.Data.DataSet sqlSelect = QzLib.runSql(_o[1], "result", 0, 0, connectionString);
                    if (sqlSelect != null && sqlSelect.Tables.Count > 0 && sqlSelect.Tables[0].Rows.Count > 0)
                    {
                        for (int rowcount = 0; rowcount < sqlSelect.Tables[0].Rows.Count; rowcount++)
                            _dv = _dv + sqlSelect.Tables[0].Rows[rowcount][0].ToString();
                    }
                    return _dv;
                case "s":
                    switch (_o[1])
                    {
                        case "projectId":
                            return session.currentDomain_projectId.ToString();
                        case "UID":
                            return crmSession.UID().ToString();
                    }
                    return "";
            }
            return "";
        }
        #endregion

        #region ( Remove SQL Black-list Words )
        public static string sanitize(string stringValue)
        {
            if (stringValue == null)
                return stringValue;
            else
            {
                string sanitizedString = Regex.Replace(Regex.Replace(Regex.Replace(stringValue,
                         "-{2,}", "-"),               // transforms multiple --- in - use to comment in sql scripts
                                                      //@"[*/]+", string.Empty),      // removes / and * used also to comment in sql scripts
                                                      // To resolve a problem of above skipping '/' from a date value.
                        @"(\/\*|\*\/)+", string.Empty),      // removes / and * used also to comment in sql scripts
                        @"(;|\s)(exec|execute|select|insert|update|delete|create|alter|drop|rename|truncate|backup|restore)\s", string.Empty, RegexOptions.IgnoreCase);

                return sanitizedString;
            }
        }
        #endregion

        #region ( List of table alias )
        public static string[] tableAlias(string s)
        {
            return Regex.Matches(s, @"\[\w+\]").Cast<Match>().Select(a => a.Value).ToArray();
        }
        #endregion

        #region ( Run SQL )
        public static DataSet runSql(string p_sql, string p_datasetName, int p_startIndex, int p_maxRow, string connectionString)
        {
            try
            {
                using (var _dbConn = new SqlConnection(connectionString))
                {
                    DataSet _dataset = new System.Data.DataSet();
                    SqlDataAdapter _adapter;

                    _adapter = new SqlDataAdapter(p_sql, _dbConn);
                    _adapter.SelectCommand.CommandTimeout = Query.commandTimeout;

                    if (p_startIndex == 0 && p_maxRow == 0)
                        _adapter.Fill(_dataset, p_datasetName);
                    else
                        _adapter.Fill(_dataset, p_startIndex, p_maxRow, p_datasetName);

                    _adapter.Dispose();

                    return _dataset;
                }
            }
            catch (System.Exception e)
            {
                // This will need to be rewritten to return more detail message
                return null;
            }
        }

        public static List<dynamic> runSqlToList(QueryResultType resultType, string sql,
            string connectionString, string sort = "", int pageSize = 10, int pageNo = 1, string uniqueIdField = "")
        {
            string commandText = sql ?? "";
            if (resultType == QueryResultType.pagedView || resultType == QueryResultType.pagedExport)
            {
                commandText = string.Format(
                    "EXEC dbo.get_pagedDataSet_v2 '{0}', '{1}', {2}, {3}, '{4}'",
                    commandText.Trim().Replace("'", "''"),
                    sort, pageSize, pageNo, uniqueIdField);
            }

            List<dynamic> results = new List<dynamic>();
            using (var connection = new SqlConnection(connectionString))
            {
                using (var command = new SqlCommand(commandText, connection) { CommandTimeout = Query.commandTimeout })
                {
                    connection.Open();
                    using (var reader = command.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            results.Add(getDynamicData(reader));
                        }
                    }
                    connection.Close();
                }
            }

            return results;
        }

        private static dynamic getDynamicData(SqlDataReader reader)
        {
            var expandoObject = new ExpandoObject() as IDictionary<string, object>;
            for (int i = 0; i < reader.FieldCount; i++)
            {
                expandoObject.Add(reader.GetName(i), reader[i]);
            }
            return expandoObject;
        }
        #endregion

        public static QueryInstanceInfo TransformInstanceToExportByChannelTarget(QueryInstanceInfo instance)
        {
            var deserializeSettings =
                new JsonSerializerSettings { ObjectCreationHandling = ObjectCreationHandling.Replace };
            QueryInstanceInfo instanceCopy = JsonConvert.DeserializeObject<QueryInstanceInfo>(
                    JsonConvert.SerializeObject(instance), deserializeSettings);
            // replace export groups with system ExportByChannelGroup
            instanceCopy.exportOptions = new List<QueryExportOptionInstance> {
                new QueryExportOptionInstance {
                    key = "pseg_exportByChannel", groupKey = "pseg_exportByChannelSysGrp"
                }
            };

            return instanceCopy;
        }

    }
    #endregion

    #endregion


    #region [[ giving statement ]]
    public class gsData
    {
        public string orgName { get; set; }
        public string EIN { get; set; }
        public string addrLine1 { get; set; }
        public string addrLine2 { get; set; }
        public string giftPeriod { get; set; }
        public string orgStatement { get; set; }

        public string sqlKey { get; set; }
        public QueryInstanceInfo qInstance { get; set; }
    }
    #endregion



}

