﻿using cmdiapp.n.core.Areas.legislator.Domain.Models;
using cmdiapp.n.core.Areas.legislator.Domain.Services;
using cmdiapp.n.core.Core.App_Start;
using Ninject;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Web.Http;

namespace cmdiapp.n.core.Areas.legislator.Presentation.api
{
    [RoutePrefix("legislator/api/Committee")]
    public class CommitteeController : ApiController
    {
        private ICommitteeService _service;

        public CommitteeController()
        {
            _service = NinjectMVC.kernel.Get<ICommitteeService>();
        }

        [HttpGet, Route("{id}")]
        [Authorize]
        public Committee Get(string id)
        {
            return _service.GetCommittee(id);
        }

        [HttpGet, Route("{id}/Members")]
        [Authorize]
        public List<CommitteeMemberView> GetMembers(string id)
        {
            return _service.GetCommitteeMembers(id);
        }

        [HttpGet, Route("{id}/Subcommittees")]
        [Authorize]
        public List<Committee> GetSubcommittees(string id)
        {
            return _service.GetSubcommittees(id);
        }

    }
}