﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Data;
using System.Linq;
using System.Web.Mvc;
using System.Data.Linq;
using System.ComponentModel.DataAnnotations.Schema;
using cmdiapp.n.core.Areas.crm.Domain.Data;

namespace cmdiapp.n.core.Areas.crm.Domain.Models
{
    [Table("dm_CYCLE")]
    public class dm_Cycle : _entityBase_crm
    {
        [Column]
        [Key]
        public Int16 CYCLE { get; set; }

        [Column]
        public string CYCLENAME { get; set; }

        [Column]
        public DateTime? CYCLESTART { get; set; }

        [Column]
        public DateTime? CYCLEEND { get; set; }

        [Column]
        public bool? include_in_report { get; set; }

        [Column]
        public Int16? cycleGroup { get; set; }

        [Column]
        public string cycleLabelShort_ { get; set; }

        [Column]
        public string cycleLabelLong_ { get; set; }
    }
}