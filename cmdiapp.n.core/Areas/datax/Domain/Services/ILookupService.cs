﻿using cmdiapp.n.core.Areas.datax.Domain.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace cmdiapp.n.core.Areas.datax.Domain.Services
{
    /// <summary>
    /// Provides methods for getting general look up data
    /// </summary>
    public interface ILookupService
    {
        /// <summary>
        /// Returns list of availabel time zones
        /// </summary>
        /// <returns></returns>
        List<SqlTimeZone> GetTimeZones();
    }
}
