﻿using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Web;
using System.Web.Helpers;

namespace cmdiapp.n.core.Areas.quickdash.Domain.Models
{
    public class TitleValueFormatItemsConverter : IPanelDataConverter
    {
        public ConvertedPanelData Convert(dynamic data)
        {
            var dataTable = new DataTable();
            var formatting = new List<ExportFormatting>();
            dataTable.Columns.AddRange(
                new DataColumn[3]
                {
                    new DataColumn("Title", typeof(string)),
                    new DataColumn("Value", typeof(decimal)),
                    new DataColumn("SubTitle", typeof(string))
                });

            List<TitleValue> results = GetTitleValues(data);

            int valueColumnIndex = dataTable.Columns.IndexOf("Value");
            foreach (var item in results)
            {
                if (!string.IsNullOrEmpty(item?.Title) && item?.Value != null)
                {
                    dataTable.Rows.Add(new object[3] { item.Title, item.Value, item.SubTitle });

                    if (item.Format != ExportFormatType.None)
                    {
                        // Add 1 to row index to account for header row.
                        AddFormattedRow(formatting, valueColumnIndex, dataTable.Rows.Count, item.Format);
                    }
                }
            }

            return new ConvertedPanelData()
            {
                DataTable = dataTable,
                Formatting = formatting
            };
        }

        private List<TitleValue> GetTitleValues(dynamic data)
        {
            var list = new List<TitleValue>();
            if (!ConverterUtil.HasProperty(data, "items"))
            {
                return list;
            }

            var items = data.items;
            IEnumerable<object> enumerableItems = new List<object>();

            if (items is DynamicJsonArray)
            {
                enumerableItems = (DynamicJsonArray)items;
            } else if (items is JArray)
            {
                enumerableItems = (JArray)items;
            }

            foreach (var item in enumerableItems)
            {
                var dynamicItem = (dynamic)item;
                list.Add(new TitleValue()
                {
                    Title = ConverterUtil.HasProperty(dynamicItem, "title") ? dynamicItem.title : "",
                    Value = ConverterUtil.HasProperty(dynamicItem, "value") ? dynamicItem.value : null,
                    SubTitle = ConverterUtil.HasProperty(dynamicItem, "subtitle") ? dynamicItem.subtitle : "",
                    Format = ConverterUtil.HasProperty(dynamicItem, "format")
                        ? GetExportFormat((string)dynamicItem.format)
                        : ExportFormatType.None
                });
            }

            return list;
        }

        private void AddFormattedRow(
            List<ExportFormatting> formatting,
            int columnIndex,
            int rowIndex,
            ExportFormatType format)
        {
            var existingPreviousRow = formatting
                .Find(item => item.Format == format && item.RangeRowEnd == (rowIndex - 1));
            if (existingPreviousRow != null)
            {
                existingPreviousRow.RangeRowEnd++;
            }
            else
            {
                formatting.Add(new ExportFormatting()
                {
                    RangeRowStart = rowIndex,
                    RangeRowEnd = rowIndex,
                    RangeColumnStart = columnIndex,
                    RangeColumnEnd = columnIndex,
                    Format = format
                });
            }
            
        }

        private ExportFormatType GetExportFormat(string format)
        {
            switch (format)
            {
                case "n":
                    return ExportFormatType.Number;
                case "c":
                    return ExportFormatType.Money;
                case "d":
                    return ExportFormatType.Date;
                default:
                    return ExportFormatType.None;
            }
        }

        private class TitleValue
        {
            public string Title { get; set; }
            public dynamic Value { get; set; }
            public string SubTitle { get; set; }
            public ExportFormatType Format { get; set; }
        }
    }
}