﻿using cmdiapp.n.core.Areas.datax.Domain.Data;
using cmdiapp.n.core.Areas.datax.Domain.Models;
using cmdiapp.n.core.Areas.query.Domain.Models;
using cmdiapp.n.core.Domain.ViewModels;
using cmdiapp.n.core.Library;
using System;
using System.Collections.Generic;
using System.Data.Entity;
using System.Linq;
using System.Threading.Tasks;
using System.Web;

namespace cmdiapp.n.core.Areas.datax.Domain.Services.implementation
{
    /// <summary>
    /// Implementation of <see cref="ICremirJobService"/>
    /// </summary>
    public class CremirJobService : ICremirJobService
    {
        private readonly I_entity_datax _entity;

        public CremirJobService(I_entity_datax entity)
        {
            _entity = entity;
        }

        public Task<List<CremirJobLog>> GetJobLogsAsync(int jobId)
        {
            return _entity.All<CremirJobLog>()
                .Where(log => log.JobId == jobId)
                .OrderByDescending(log => log.CreatedOn)
                .ToListAsync();
        }

        public Task<List<CremirSendJobInfo>> GetJobsByAccountAsync(int accountId, int skip = 0, int take = 10)
        {
            return _entity.All<CremirSendJobInfo>()
                .Where(job => job.AccountId == accountId)
                .OrderByDescending(job => job.CreatedOn)
                .Skip(skip)
                .Take(take)
                .ToListAsync();
        }

        public async Task<genericResponse> GetJobsByAccountAsync(
            int accountId,
            int page,
            int pageSize,
            QueryRuntimeSort sort,
            string listId,
            string jobTypeCode,
            string jobStatusCode)
        {
            var query = from job in _entity.All<CremirSendJobInfo>()
                        where job.AccountId == accountId
                        && (string.IsNullOrEmpty(listId) || job.ListId == listId)
                        && (string.IsNullOrEmpty(jobTypeCode) || job.JobTypeCode == jobTypeCode)
                        && (string.IsNullOrEmpty(jobStatusCode) || job.JobStatusCode == jobStatusCode)
                        select job;

            string sortDir = sort?.dir ?? "desc";
            string sortField = string.IsNullOrEmpty(sort?.field)
                ? nameof(CremirSendJobInfo.CreatedOn)
                : sort.field[0].ToString().ToUpper() + sort.field.Substring(1);
            if (typeof(CremirSendJobInfo).GetProperty(sortField) == null)
            {
                sortField = nameof(CremirSendJobInfo.CreatedOn);
            }

            int skip = (page - 1) * pageSize;

            try
            {
                int count = await query.CountAsync();
                List<CremirSendJobInfo> results = await query.dynamicSortBy(sortField, sortDir == "desc")
                    .Skip(skip)
                    .Take(pageSize)
                    .ToListAsync();

                return new genericResponse()
                {
                    success = true,
                    __count = count,
                    results = results.ToList<iItemType>()
                };
            }
            catch (Exception ex)
            {
                return new genericResponse()
                {
                    success = false,
                    message = $"An error occurred. Unable to retrieve jobs for account {accountId}.",
                    messageKey = ex.Message
                };
            }
        }
    }
}