﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace cmdiapp.n.core.Areas.crm.Core
{
    public static class crmConstants
    {
        #region [[ Constants ]]
        public const string DEFAULT_LAYOUT_w_HEADER_FOOTER = "~/Views/Shared/_Layout.cshtml";
        public const string DEFAULT_LAYOUT_BLANK = "~/Views/Shared/_LayoutBlank.cshtml";
        #endregion

        #region [[ Size Limit ]]
        public const int MAX_FILESIZE_for_PROFILE_PHOTO = 5242880; // 5MB
        #endregion

        #region [[ CACHE KEY ]]
        public const string CACHEKEY_YTDTOTAL_PREFIX = "ytdTotal";
        public const string CACHEKEY_YTDBYPROGRAM_PREFIX = "ytdByProgram";
        public const string CACHEKEY_PEOPLECOUNT_PREFIX = "peopleCount";
        public const string CACHEKEY_BREADKDOWNBYPROGRAM_PREFIX = "breakdownByProgram";
        public const string CACHEKEY_BREAKDOWNBYMONYTYPE_PREFIX = "breakdownMonyType";
        public const string CACHEKEY_BREAKDOWNBYFUND_PREFIX = "breakdownFund";
        public const string CACHEKEY_DAILYTOTALS_PREFIX = "dailyTotals";
        public const string CACHEKEY_DBD_NUMBERS_PREFIX = "dbd_numbers";
        public const string CACHEKEY_DBD_COMPLIANCE = "dbd_Compl";
        #endregion

        #region [[ const - Configuration Key ]]
        public const string exactTarget_loginParams = "ExactTarget Login Parameters";
        public const string facebook_pageId_key = "Facebook Page ID";
        public const string twitter_screenName_key = "Twitter User Screen Name";
        public const string default_map_type = "Default Map (Type)";
        public const string default_map_stateFips = "Default Map (State FIPS)";
        public const string default_map_stateName = "Default Map (State Name)";

        public const string cmdi_cc_processing_key = "CMDI CC Processing";
        public const string cmdi_cc_processing_default_fundCode = "CMDI CC Processing - Default Fund";
        public const string cmdi_cc_processing_default_srceCode = "CMDI CC Processing - Default Source";
        public const string cmdi_cc_processing_default_batchno = "CMDI CC Processing - Default Batch#";
        public const string cmdi_cc_processing_recur_option = "CMDI CC Processing - Recur Option";
        public const string cmdi_cc_processing_default_account = "CMDI CC Processing - Default Account";
        public const string cmdi_cc_processing_multi_fund = "CMDI CC processing - Multi Fund options";          // trifs 17 Mar 2011 10:53am

        public const string is_fls_voter_tables_available = "FLS Voter Data";
        public const string fls_dataConnect_clientCode = "FLS DataConnect Client";

        public const string fundraiser_downlines_reportName = "Downlines Report";   // If not exist, default=>"Downlines" 

        // from DMS Database
        //public const string dms_show_accounting_code = "viACCTCODE";  // YES or NO

        public const string is_shared_trackno_available = "Shared Track#"; // "Y" or "N"

        public const string fundraise_Trackno_range = "Fundraise Track# Range";
        public const string fundraise_Previous_Cycle = "Fundraise Previous Cycle";  // "Y" or "N"

        //Club Status 
        public const string People_Club_Status = "People Club Status";  // "Y" or "N"       
        //Club Availability in Pledges
        public const string Club_Availability_inPledge = "Club Availability for Pledge";
        //Wealth Engine UserId and pass
        public const string WealthEng_Uname_Pass = "Wealth Engine - User name and password";
        //Inclusion of Prev Cycle Totals in People_Search Output
        public const string Output_Prev_Cycle = "Output Prev Cycle";

        public const string fls_client_code = "fls_client_code";

        // Custom Profile Reports
        public const string profile_full = "profile_full";
        public const string profile_brief = "profile_brief";
        public const string profile_pocket = "profile_pocket";
        public const string profile_PAC = "profile_PAC";
        public const string profile_extended = "profile_extended";

        // FLS DataConnect JumpTo links
        public const string fls_dataConnect = "FLS DataConnect Client";
        //public const string fls_dataConnect_jumptoHome = "default.aspx";  // eg. http://www.flsdataconnect.com/freestrongamerica/default.aspx
        //public const string fls_dataConnect_jumptoCount = "counts/counts.aspx"; // eg. http://www.flsdataconnect.com/freestrongamerica/counts/counts.aspx
        //public const string fls_dataConnect_jumptoAdvCount = "counts/advancedcounts.aspx"; // eg.  http://www.flsdataconnect.com/freestrongamerica/counts/advancedcounts.aspx
        public const string fls_dataConnect_jumptoProfile = "recordlookup/recordmaintenance.aspx?id={voterId}"; // eg. http://www.flsdataconnect.com/freestrongamerica/recordlookup/recordmaintenance.aspx?id=6743708~1

        // Non-profit
        public const string nonProfit = "NonProfit"; // (Y/N)

        //JFC Availability - eliminated on 8/17/2021
        //public const string Jfc_Availability = "JFC Availability"; // (Y/N)

        // Include 
        public const string PS_YTDbyFund = "People Search - YTD by Fund";  // (Y/N)

        //Rpm Campaign Id
        public const string rpmCampaignId = "RPM Id";

        //Rpm Campaign Name if Rpm Capaign Id exists
        public const string rpmCampaignName = "RPM Name";

        //Restriction of 1 club in People
        public const string SingleClubStatus = "Single Club Status"; //(Y/N)

        //JFC databaseId
        public const string jfc_databaseId = "jfc_databaseId"; // integer

        //Statement View Default
        public const string Money_StatementView = "Money Statement View";

        //Fundraise - Raised towards S&S
        public const string Fundraise_RaisedTowardsSandS = "Fundraise Raised towards S & S";

        //Voter Mapping Customer Id & User Credential
        public const string vm_customerId = "Voter Mapping CustomerID";
        public const string vm_userCredential = "Voter Mapping UserAccount Credential";

        //For Unit Field in Event
        public const string event_unit_field = "Event Unit Field";
        //People Class Field
        public const string people_class_field = "People Class Field";

        //People STR1 Field
        public const string people_str1_field = "People STR1 Field";

        //People STR2 Field
        public const string people_str2_field = "People STR2 Field";

        //People STR2 Field
        public const string people_ACCTNO_field = "people.acctno";

        //People NUM1 Field
        public const string people_num1_field = "People NUM1 Field";

        //Show Received Date
        public const string show_received_date = "Show Received Date";

        //Donate Now Disclaimer
        public const string donateNow_disclaimer = "Donate Now - Disclaimer";

        // EventBrite Data
        public const string EventBrite = "EventBrite Data";

        // Club labels
        public const string club_solicitor_label = "Club Solicitor label";
        public const string club_candidate_label = "Club Candidate label";

        public const string import_threshold = "Threshold for Import";

        public const string peopleSearch_SortOption = "peopleSearch_SortOption";

        // Conduit
        public const string ConduitModule = "Conduit Module";

        //Note & Task -Ask field
        public const string ask_field = "Ask Field";

        public const string RAbySub = "RAbySub";
        
        //Gift - Custom Referenceid field
        public const string Money_custom_Reference_Id = "Money custom Reference Id";

        // MonyAddi - Misc Text 
        public const string MiscText_field = "Misc Text";

        // Crimson ID Token
        public const string crimsonIdToken = "Crimson ID Token";

        // Search/Output on FEC downloaded data 
        public const string fecSearch = "FEC Search";

        // Whether to display CheckDigit as a part of PID
        public const string showCheckDigit = "Show Check-Digit";

        // Whether to default "Subtotal by List#" in Response Analysis Report
        public const string ra_defaultByList = "RA-Default:SubTotal by List#";

        // Wether to send a notification when a donation is adjusted (Y/N)
        public const string NotifiOnAdj = "NotifiOnAdj";

        //chapter & use of long batch number (9 bytes)
        public const string chapter = "chapter"; //(label)
        public const string long_batchno = "long-batch#"; //(Y/N)
        public const string chapter_asgn_sp = "chapter-asgn-sp";

        // Data Entry Finder start# and length
        public const string finder_startno = "viFINDER";
        public const string finder_length = "FNDER_LEN";

        // Verify if came thru a specified Inbound IP address(es)
        public const string inbound_ip = "inbound-ip";

        //Account Code Label
        public const string acctcodeLabel = "Center";

        //Campgn Code Label
        public const string campgncodeLabel = "dmCampaign";

        //Alf non-profit
        public const string alf_nonprofit = "ALF nonprofit";

        public const string earmark_queue_import = "Earmark Queue Import";
        
        //New Gift By Date And Source Code
        public const string import_gift_tally = "import-gift-tally";

        // Caging Config
        public const string caging_config = "caging-config";
        #endregion
    }
}