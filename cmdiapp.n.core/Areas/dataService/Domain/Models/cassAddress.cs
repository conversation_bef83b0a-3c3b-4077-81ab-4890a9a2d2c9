﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace cmdiapp.n.core.Areas.dataService.Domain.Models
{
    public class cassAddress
    {
        public string RECID { get; set; }
        public string ADDR1 { get; set; }
        public string ADDR2 { get; set; }
        public string SUITE { get; set; }
        public string CITY { get; set; }
        public string STATE { get; set; }
        public string ZIP { get; set; }
        public string PLUS4 { get; set; }
        public string ADDRTYPE { get; set; }
        public string STNUM1 { get; set; }
        public string STPREDIR { get; set; }
        public string STNAME { get; set; }
        public string STSUFFIX { get; set; }
        public string STPOSDIR { get; set; }
        public string STUNIT { get; set; }
        public string STNUM2 { get; set; }
        public string URB { get; set; }
        public string STATEFIPS { get; set; }
        public string COUNTYFIPS { get; set; }
        public string COUNTYNAME { get; set; }
        public string CD { get; set; }
        public string SD { get; set; }
        public string LD { get; set; }
        public string DPBC { get; set; }
        public string CRRT { get; set; }
        public string LACS { get; set; }
        public string DPV { get; set; }
        public string ZIP4CODE { get; set; }
        public string ERRMSG { get; set; }

        public string POSTNET { get; set; }
        public string LATITUDE { get; set; }
        public string LONGITUDE { get; set; }
        public string LATLEVEL { get; set; }
    }
}