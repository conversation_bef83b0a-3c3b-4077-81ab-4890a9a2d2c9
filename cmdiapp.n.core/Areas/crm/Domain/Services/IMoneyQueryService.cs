﻿using cmdiapp.n.core.Areas.crm.Domain.Models;
using cmdiapp.n.core.Areas.crm.ViewModels;
using System.Collections.Generic;
using System.Data;
using System.Threading;
using System.Threading.Tasks;

namespace cmdiapp.n.core.Areas.crm.Domain.Services
{
    public interface IMoneyQueryService
    {
        Task<PagedExternalApiResult<List<object>>> GetMoneyRecordsAsync(
            ExternalApiSpecification specification,
            CancellationToken cancellationToken);
    }
}
