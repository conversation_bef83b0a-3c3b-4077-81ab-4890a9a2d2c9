﻿using cmdiapp.n.core._Domain.Models;
using cmdiapp.n.core.Areas.crm.Domain.Data;
using cmdiapp.n.core.Domain.Data;
using cmdiapp.n.core.Domain.ViewModels;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Web;

namespace cmdiapp.n.core.Areas.crm.Domain.Models
{
    public class CagingAccount 
    {
        [JsonProperty("cageacctid")]
        public int cageacctid { get; set; }

        [JsonProperty("projectid")]
        public int projectid { get; set; }

        [JsonProperty("acctname")]
        public string acctname { get; set; }

        [JsonProperty("fundcodes")]
        public List<Dictionary<string, int>> fundcodes { get; set; }

        [JsonProperty("notifiedusers")]
        public List<Dictionary<string, string>> notifiedusers { get; set; }

        [JsonProperty("bankname")]
        public string bankname { get; set; }

        [JsonProperty("bankacctname")]
        public string bankacctname { get; set; }

        [JsonProperty("bankroutenumber")]
        public string bankroutenumber { get; set; }

        [JsonProperty("bankacctnumber")]
        public string bankacctnumber { get; set; }

        [JsonProperty("banktrancode")]
        public string banktrancode { get; set; }

        [JsonProperty("destroutename")]
        public string destroutename { get; set; }

        [JsonProperty("destroutenumber")]
        public string destroutenumber { get; set; }

        [JsonProperty("origroutename")]
        public string origroutename { get; set; }

        [JsonProperty("origroutenumber")]
        public string origroutenumber { get; set; }

        [JsonProperty("assigncompanyid")]
        public string assigncompanyid { get; set; }

        [JsonProperty("bankfilename")]
        public string bankfilename { get; set; }

        [JsonProperty("contactname")]
        public string contactname { get; set; }

        [JsonProperty("phonenumber")]
        public string phonenumber { get; set; }

        [JsonProperty("bofdindicator")]
        public string bofdindicator { get; set; }

        [JsonProperty("cashletterid")]
        public string cashletterid { get; set; }

        [JsonProperty("returnindicator")]
        public string returnindicator { get; set; }

        [JsonProperty("ticketdetailaddendum")]
        public bool ticketdetailaddendum { get; set; }

        [JsonProperty("returnlocationroutenumber")]
        public string returnlocationroutenumber { get; set; }

        [JsonProperty("multibundled")]
        public bool multibundled { get; set; }

        [JsonProperty("checkdetailaddendum")]
        public bool checkdetailaddendum { get; set; }

        public CagingAccount(string accountName)
        {
            cageacctid = 0;
            projectid = 0;
            acctname = accountName;
            ticketdetailaddendum = false;
            multibundled = false;
        }
    }

    public class CagingConfig
    {
        [JsonProperty("enabled")]
        public bool enabled { get; set; }

        [JsonProperty("accounts")]
        public List<CagingAccount> accounts { get; set; }

        public CagingConfig()
        {
            enabled = false;
            accounts = new List<CagingAccount>();
        }
    }

    public class CagingClient
    {
        public int projectid { get; set; }
        public string name { get; set; }
    }

    public class CagingTray
    {
        public int? cagetrayid { get; set; }
        public int? cageacctid { get; set; }
        public string acctname { get; set; }
        public DateTime? receiveddte { get; set; }
        public int? traycount { get; set; }
        public int? piececount { get; set; }
    }
    public class caging_fundlist
    {
        public Int16? fundid { get; set; }
        public string fundcode { get; set; }
        public string funddesc { get; set; }
    }

    public class caging_acctlist
    {
        public Int16? centerid { get; set; }
        public string centercode { get; set; }
        public string descrip { get; set; }
    }

    [NotMapped]
    public class CagingBatch : caging_batch_log
    {
        public bool? loaded { get; set; }
    }

    public class sp_caging_result
    {
        public int BATCHID { get; set; }
        public string MESSAGE { get; set; }
    }

    public class cagingdailytraycounts
    {
        public DateTime receiveddte { get; set; }
        public int traycount { get; set; }
        public int piececount { get; set; }
    }

    public class cagingdailybatchcounts
    {
        public DateTime batchdte { get; set; }
        public int totbatch { get; set; }
        public decimal totbatchamt { get; set; }
        public int totnondonor { get; set; }
    }

    public class cagingsummarytraycounts
    {
        public int seq { get; set; }
        public string title { get; set; }
        public int totaltray { get; set; }
        public int completedtray { get; set; }
    }

    public class cagingsummarybatchcounts
    {
        public int seq { get; set; }
        public string title { get; set; }
        public int totalbatch { get; set; }
        public int completedbatch { get; set; }
    }
}