﻿using cmdiapp.n.core.Areas.crm.Domain.Models;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Runtime.Serialization;
using System.Web;

namespace cmdiapp.n.core.Areas.datax.Domain.Models.ViewModels
{
    /// <summary>
    /// Represents a Cremir email list record
    /// </summary>
    [DataContract]
    public class CremirEmailList
    {
        private List<EmailListCustomField> _customFields;

        /// <summary>
        /// <see cref="Account.Id"/>
        /// </summary>
        [DataMember(Name = "accountId")]
        public int AccountId { get; set; }
        /// <summary>
        /// The frequency type (P - Periodic, O - One-time).
        /// </summary>
        [DataMember(Name = "frequencyType")]
        public string FrequencyType { get; set; }
        /// <summary>
        /// The identifier of the list.
        /// </summary>
        [DataMember(Name = "listId")]
        public string ListId { get; set; }
        /// <summary>
        /// The ID used by the email service provider
        /// to identify the list, if it exists.
        /// </summary>
        [DataMember(Name = "providerListId")]
        public string ProviderListId { get; set; }
        /// <summary>
        /// The name of the list.
        /// </summary>
        [DataMember(Name = "listName")]
        public string ListName { get; set; }
        /// <summary>
        /// For P type lists, how often to update the list.
        /// Should be stored as string representation of <see cref="TimeSpan"/>.
        /// [d.]hh:mm[:ss]
        /// see https://docs.microsoft.com/en-us/dotnet/api/system.timespan.parse?view=netcore-2.1
        /// </summary>
        [DataMember(Name = "updateInterval")]
        public string UpdateInterval { get; set; }
        /// <summary>
        /// UTC date of last attempted update
        /// </summary>
        [DataMember(Name = "lastUpdateOn")]
        public DateTime? LastUpdateOn { get; set; } = null;
        /// <summary>
        /// The ID of the Crimson user that created the list
        /// </summary>
        [DataMember(Name = "crimsonUserId")]
        public string CrimsonUserId { get; set; }
        /// <summary>
        /// Serialized JSON of custom fields
        /// </summary>
        [DataMember(Name = "customFieldsJson")]
        public string CustomFieldsJson { get; set; }
        /// <summary>
        /// De-serialized custom fields
        /// </summary>
        [DataMember(Name = "customFields")]
        public List<EmailListCustomField> CustomFields
        {
            get
            {
                if (string.IsNullOrEmpty(CustomFieldsJson)) { return null; }
                if (_customFields == null)
                {
                    var serializer = JsonSerializer.CreateDefault();
                    using (var stringReader = new StringReader(CustomFieldsJson))
                    using (var jsonReader = new JsonTextReader(stringReader))
                    {
                        return serializer.Deserialize<List<EmailListCustomField>>(jsonReader);
                    }
                }
                return _customFields;
            }
            set
            {
                _customFields = value;
                if (_customFields == null)
                {
                    CustomFieldsJson = null;
                    return;
                }

                var serializer = JsonSerializer.CreateDefault();
                using (var writer = new StringWriter())
                {
                    serializer.Serialize(writer, _customFields);
                    CustomFieldsJson = writer.ToString();
                }
            }
        }
        /// <summary>
        /// Whether the list has been deleted.
        /// </summary>
        [DataMember(Name = "isDeleted")]
        public bool IsDeleted { get; set; }
    }
}