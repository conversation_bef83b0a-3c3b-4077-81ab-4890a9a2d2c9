﻿using System.Collections.Generic;
using System.Web.Mvc;

using cmdiapp.n.core.Areas.crm.Domain.Models;
using cmdiapp.n.core.Areas.crm.Domain.ViewModels;
using cmdiapp.n.core.Areas.crm.Domain.Repositories;

namespace cmdiapp.n.core.Areas.crm.Domain.Services
{
    public interface IlkChannelService
    {
        List<lkChannel> get_all_channels();
        List<lkChannel_ext> get_all_channel_exts(string where);
        bool check_unqiueness_descrip(lkChannel _channel);
        bool check_unqiueness_channel(lkChannel _channel); 
        void Update(lkChannel _lkChannel);
        void Add(lkChannel _lkChannel);
        void Delete(lkChannel _lkChannel);
        lkChannel get_channel(int channelId);
        bool check_record_linked(int channelId);
        
    }

    public interface IlkTargetChannelService
    {
        List<lkTargetChannel> get_all_targetchannels();

    }

}