﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web.Http;
using System.IO;
using cmdiapp.n.core.Core;
using cmdiapp.n.core.Areas.crm.Core;
using cmdiapp.n.core.Areas.crm.Domain.Models;
using cmdiapp.n.core.Areas.rpm.Domain.Models;
using cmdiapp.n.core.Areas.rpm.Domain.Services;
using cmdiapp.n.core.Areas.rpm.Domain.Data;
using cmdiapp.n.core.Areas.crm.Domain.Data;
using cmdiapp.n.core.Presentation.Controllers;
using System.Data.SqlClient;

namespace cmdiapp.n.core.Areas.crm.Presentation.Controllers.api
{

    [Authorize]
    public class FundraiserController : ApiController
    {
        private I_entity_crm _entity_crm;
        private IrpmService _rpmService;

        public FundraiserController()
        {
            _entity_crm = I_entityManager_crm.getEntity();
            _rpmService = I_entityManager_rpm.getService();
        }

        [HttpGet, Route("crm/api/Fundraiser/RPMAccountDetail")]
        public RPMAccountDetail RPMAccountDetail(int pid, int trackNo)
        {
            PeopleR peopleRecord = _entity_crm.Single<PeopleR>(p => p.PID == pid);
            RPMAccountDetail rpmAcct = new RPMAccountDetail
            {
                TRACKNO = trackNo,
                EMAIL = peopleRecord.EMAIL,
                FNAME = peopleRecord.FNAME,
                LNAME = peopleRecord.LNAME,
                STATE = peopleRecord.STATE,
                REQUESTEDBY = session.userSession.Email,
                CAMPAIGNID = Convert.ToInt32(crmSession.rpmCampaignId())
            };
            try
            {
                rpmAcct.ACCOUNTEXISTS = Convert.ToInt32(_rpmService
                    .does_userAccount_exist_with(rpmAcct.CAMPAIGNID, trackNo, rpmAcct.EMAIL)
                    .FirstOrDefault().value) > 0;
            } catch (Exception e)
            {
                rpmAcct.ACCOUNTEXISTS = false;
            }
            return rpmAcct;          

        }

        #region [[ crm/fundraiser/downline ]] 
        [HttpGet, Route("crm/api/fundraiser/downline/{id}/{depth}")]
        [apiAuthorize(AccessElement = "People/Fundraiser Info", AccessLevel = "e")]
        public List<FundraiserR> downline(int? id, int? depth=-1)   // depth is not implemented yet.
        {
            if (id == null || !id.HasValue)
                return null;

            /*
            FundraiserR _data = new FundraiserR() { TRACKNO = 1, PID = ********, name = "Joseph Banks" };
            _data.children = new List<FundraiserR>();
            _data.children.Add(new FundraiserR() { TRACKNO = 12, PID = ********, name = "Marina Smith" });
            _data.children.Add(new FundraiserR() { TRACKNO = 13, PID = ********, name = "James Bond" });
            _data.children[1].children = new List<FundraiserR>();
            _data.children[1].children.Add(new FundraiserR() { TRACKNO = 131, PID = ********, name = "Laura Simpson" });
            _data.children[1].children.Add(new FundraiserR() { TRACKNO = 132, PID = ********, name = "Tim McCauley" });
            _data.children.Add(new FundraiserR() { TRACKNO = 14, PID = ********, name = "Ashley Beltx" });
            _data.children[2].children = new List<FundraiserR>();
            _data.children[2].children.Add(new FundraiserR() { TRACKNO = 141, PID = ********, name = "Terri Lee" });
            _data.children[2].children.Add(new FundraiserR() { TRACKNO = 142, PID = ********, name = "Pamela Fickes" });
            _data.children[2].children.Add(new FundraiserR() { TRACKNO = 143, PID = 74001684, name = "Wilfredo Sanicola" });

            List<FundraiserR> _list = new List<FundraiserR>();
            _list.Add(_data);
            return _list;
            //return _entity_crm.getContext().Database.SqlQuery<AddressR>(string.Format(sql, id)).ToList().FirstOrDefault();
             */

            string downline_inXml = _entity_crm.getContext().Database.SqlQuery<string>(String.Format("SELECT dbo.[fundraiser_downline]({0})", id.Value)).FirstOrDefault();
            System.Xml.Serialization.XmlSerializer xmlSerializer = new System.Xml.Serialization.XmlSerializer(typeof(FundraiserR));
            StringReader strReader = new StringReader(downline_inXml);
            FundraiserR obj = (FundraiserR)xmlSerializer.Deserialize(strReader);

            List<FundraiserR> list = new List<FundraiserR>();
            list.Add(obj);

            return list;
             
        }

        #endregion

        [HttpGet, Route("crm/api/Fundraiser/FundSummary/{trackNo}")]
        [apiAuthorize(AccessElement = "People/Fundraiser Info", AccessLevel = "v")]
        public object GetFundSummary(int trackNo)
        {
            var q = from mony in _entity_crm.All<MONY>()
                    join track in _entity_crm.All<MONYTRACK>() on mony.MID equals track.MID into t
                    from track in t.DefaultIfEmpty()
                    join fund in _entity_crm.All<dmFUND>()
                        .Where(f => f.ACTIVE ?? false) on mony.FUNDID equals fund.FUNDID
                    where (mony.TRACKNO == trackNo || track.TRACKNO == trackNo) && mony.COUNTER == 1
                    select new {
                        amount = mony.TRACKNO == trackNo ? (mony.TRACKAMT ?? mony.AMT) : track.TRACKAMT,
                        fundCode = fund.FUNDCODE
                    };
            return q.ToList().GroupBy(x => x.fundCode)
                .Select(grp => new {
                    total = grp.Sum(m => m.amount),
                    count = grp.Count(),
                    fundCode = grp.FirstOrDefault().fundCode
                }).ToArray();
        }

        [HttpGet, Route("crm/api/Fundraiser/YearlySummary/{trackNo}/{maxYearDelta}")]
        [apiAuthorize(AccessElement = "People/Fundraiser Info", AccessLevel = "v")]
        public object GetYearlySummary(int trackNo, int maxYearDelta)
        {
            int currentYear = DateTime.Now.Year;
            var q = from mony in _entity_crm.All<MONY>()
                    join track in _entity_crm.All<MONYTRACK>() on mony.MID equals track.MID into t
                    from track in t.DefaultIfEmpty()
                    where (mony.TRACKNO == trackNo || track.TRACKNO == trackNo) && mony.COUNTER == 1
                    && (maxYearDelta < 0 || (currentYear - mony.BATCHDTE.Year) <= maxYearDelta)
                    select new {
                        amount = mony.TRACKNO == trackNo ? (mony.TRACKAMT ?? mony.AMT) : track.TRACKAMT,
                        yearDelta = currentYear - mony.BATCHDTE.Year
                    };
            return q.ToList() // evaluating query q first seems to improve performance by 10x for some reason
                .GroupBy(x => x.yearDelta)
                .Select(grp => new
                {
                    total = grp.Sum(m => m.amount),
                    count = grp.Count(),
                    yearDelta = grp.FirstOrDefault().yearDelta
                });
        }

        [HttpGet, Route("crm/api/Fundraiser/PledgeFundSummary/{trackNo}")]
        [apiAuthorize(AccessElement = "People/Fundraiser Info", AccessLevel = "v")]
        public object GetPledgeFundSummary(int trackNo)
        {
            var q = from pledge in _entity_crm.All<PLEDGE>()
                    join track in _entity_crm.All<PLEDGETRACK>() on pledge.PLEDGEID equals track.PLEDGEID into t
                    from track in t.DefaultIfEmpty()
                    join fund in _entity_crm.All<dmFUND>()
                        .Where(f => f.ACTIVE ?? false) on pledge.FUNDID equals fund.FUNDID
                    where pledge.TRACKNO == trackNo || track.TRACKNO == trackNo
                    select new {
                        amount = pledge.TRACKNO == trackNo ? (pledge.TRACKAMT ?? pledge.PLEDGEAMT) : track.TRACKAMT,
                        fundCode = fund.FUNDCODE
                    };
            return q.ToList().GroupBy(x => x.fundCode)
                .Select(grp => new {
                    total = grp.Sum(p => p.amount),
                    count = grp.Count(),
                    fundCode = grp.FirstOrDefault().fundCode
                }).ToArray();
        }

        #region [[ Gift Officer ]]
        [HttpGet, Route("crm/api/Fundraiser/RecordAssigned/{trackNo}")]
        [apiAuthorize(AccessElement = "People/Fundraiser Info", AccessLevel = "v")]
        public object RecordAssigned(int trackNo)
        {
            var q = from jtFUNDRAISE in _entity_crm.All<jtFUNDRAISE>()
                    join people in _entity_crm.All<people>() on jtFUNDRAISE.PID equals people.PID
                    where (jtFUNDRAISE.TRACKNO == trackNo)
                    select jtFUNDRAISE;
            if (q.Count() == 0)
                return new { count = 0, trackno = trackNo };
            else 
                return q.ToList().GroupBy(x => x.TRACKNO)
                    .Select(grp => new
                    {
                        count = grp.Count(),
                        trackno = grp.FirstOrDefault().TRACKNO
                    }).FirstOrDefault();
        }

        [HttpGet, Route("crm/api/Fundraiser/AssignedDonor/{pid}")]
        [apiAuthorize(AccessElement = "People/Fundraiser Info", AccessLevel = "v")]
        public object AssignedDonor(int pid)
        {
            Int16 currentUser = (Int16)crmSession.UID();
            var q = from FUNDRAISE in _entity_crm.All<FUNDRAISE>()
                    join jtFUNDRAISE in _entity_crm.All<jtFUNDRAISE>() on FUNDRAISE.TRACKNO equals jtFUNDRAISE.TRACKNO  
                    where (FUNDRAISE.UID == currentUser && jtFUNDRAISE.PID == pid)
                    select new
                    {
                        pid = jtFUNDRAISE.PID,
                        trackno = jtFUNDRAISE.TRACKNO
                    };
            /*
            var q = from FUNDRAISE in _entity_crm.All<FUNDRAISE>()
                    where (FUNDRAISE.UID.HasValue )
                    select new { USERID = (Int16)FUNDRAISE.UID, TRACKNO = FUNDRAISE.TRACKNO } into t
                    join jtFUNDRAISE in _entity_crm.All<jtFUNDRAISE>() on t.TRACKNO equals jtFUNDRAISE.TRACKNO
                    where (t.USERID == currentUser)
                    select new
                    {
                        count = 1,
                        trackno = t.TRACKNO
                    };
            */
            return q.FirstOrDefault();
        }

        #endregion
    }
}
