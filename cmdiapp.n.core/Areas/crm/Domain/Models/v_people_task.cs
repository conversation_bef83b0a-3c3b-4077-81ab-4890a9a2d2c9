﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Data;
using System.Linq;
using System.Web.Mvc;
using System.Data.Linq;
using System.ComponentModel.DataAnnotations.Schema;
using cmdiapp.n.core.Domain.ViewModels;
using cmdiapp.n.core.Areas.crm.Domain.Data;

namespace cmdiapp.n.core.Areas.crm.Domain.Models
{
    public class v_people_task : _entityBase_crm, iItemType
    {
        [Column]
        [Key]
        public int ACTID { get; set; }

        [Column]
        public int PID { get; set; }

        [Column]
        public DateTime? ACTDATE { get; set; }

        public string ACTDATEs
        {
            get
            {
                return (ACTDATE == null ? "" : string.Format("{0:MM/dd/yyyy}", ACTDATE));
            }
        }
        [Column]
        public string ACTTYPE { get; set; }
        [Column]
        public string PURPOSE { get; set; }
        [Column]
        public string PRIORITY { get; set; }
        [Column]
        public string SCHEDFOR { get; set; }
        [Column]
        public string SCHEDBY { get; set; }

        [Column]
        public DateTime? SCHEDON { get; set; }
        public string SCHEDONs
        {
            get
            {
                return (SCHEDON == null ? "" : string.Format("{0:MM/dd/yyyy}", SCHEDON));
            }
        }

        [Column]
        public Byte DONE { get; set; }
        [Column]
        public string DONEBY { get; set; }

        [Column]
        public DateTime? DONEON { get; set; }
        public string DONEONs
        {
            get
            {
                string a = (DONEON == null ? "" : string.Format("{0:MM/dd/yyyy}", DONEON));
                if (a != "")
                { 

                }
                return a;
            }
        }

        [Column]
        public string SUBJECT { get; set; }
        //[Column]
        //public string ASK { get; set; }
        [Column]
        public string SHORTNOTE { get; set; } 

        [NotMapped]
        public Int16 ACTTYPEID	{ get; set; }
        [NotMapped]
        public Int16 PURPOSEID { get; set; }
        [NotMapped]
        public Int16 PRIORITYID { get; set; }
        [NotMapped]
        public Int16? SCHEDBY_UID { get; set; }
        [NotMapped]
        public Int16? SCHEDFOR_UID { get; set; }
        [NotMapped]
        public Int16? DONEBY_UID { get; set; }

        [NotMapped]
        public Int16? CurrentUSER_UID { get; set; }

        [NotMapped]
        public Byte KeepAsHistory { get; set; }

    }    

    public class lkACTTYPE : _entityBase_crm, iItemType
    {
        [Column]
        [Key]
        public Int16 ACTTYPEID	{ get; set; }
        [Column]
        public string ACTTYPE{ get; set; }
        [Column]
        public string DESCRIP{ get; set; }
        [Column]
        public Byte DEFFLAG { get; set; }
        [Column]
        public Byte SYSTEM { get; set; }
    }

    public class lkPURPOSE : _entityBase_crm, iItemType
    {
        [Column]
        [Key]
        public Int16 PURPOSEID { get; set; }
        [Column]
        public string PURPOSECD { get; set; }
        [Column]
        public string DESCRIP { get; set; }
        [Column]
        public Byte DEFFLAG { get; set; }
        [Column]
        public Byte SYSTEM { get; set; }
    }

    public class lkPRIORITY : _entityBase_crm
    {
        [Column]
        [Key]
        public Int16 PRIORITYID { get; set; }
        [Column]
        public string PRIORITY { get; set; }
        [Column]
        public string DESCRIP { get; set; }
        [Column]
        public Byte DEFFLAG { get; set; }
        [Column]
        public Byte SYSTEM { get; set; }
    }

    public class v_user : _entityBase_crm
    {
        [Column]
        [Key]
        public Int16 UID { get; set; }
        [Column]
        public int UGROUPID { get; set; }
        [Column]
        public string FULLNAME { get; set; }
        [Column]
        public Boolean ACTIVE { get; set; }
        [Column]
        public string UGROUPNAME { get; set; }
        [Column]
        public string UGROUPDESC { get; set; }
        [Column]
        public string eMail { get; set; }
        [Column]
        public DateTime? LASTLOGON { get; set; }
        [NotMapped]
        public string LASTLOGONDTE
        {
            set => LASTLOGON = Convert.ToDateTime(value);
            get => LASTLOGON == null ? "" : ((DateTime)LASTLOGON).ToShortDateString();
        }

    }
}