﻿<?xml version="1.0" encoding="utf-8"?>
<Report xmlns="http://schemas.microsoft.com/sqlserver/reporting/2008/01/reportdefinition" xmlns:rd="http://schemas.microsoft.com/SQLServer/reporting/reportdesigner">
  <AutoRefresh>0</AutoRefresh>
  <DataSources>
    <DataSource Name="DataSource">
      <DataSourceReference>Dexter</DataSourceReference>
      <rd:SecurityType>None</rd:SecurityType>
      <rd:DataSourceID>7f10d137-98e8-429e-a2c6-5b9871cd1bb9</rd:DataSourceID>
    </DataSource>
    <DataSource Name="DataSource1">
      <DataSourceReference>Dexter</DataSourceReference>
      <rd:SecurityType>None</rd:SecurityType>
      <rd:DataSourceID>c81826d1-8a67-424c-b487-9814f66fe288</rd:DataSourceID>
    </DataSource>
  </DataSources>
  <DataSets>
    <DataSet Name="iGetProfile">
      <Query>
        <DataSourceName>DataSource1</DataSourceName>
        <CommandText>exec callsheet_data__rslc_byFund 77034020,21,'','RLCC'</CommandText>
        <rd:UseGenericDesigner>true</rd:UseGenericDesigner>
      </Query>
      <Fields>
        <Field Name="IMGPATH">
          <DataField>IMGPATH</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="NOTE">
          <DataField>NOTE</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="PID">
          <DataField>PID</DataField>
          <rd:TypeName>System.Int32</rd:TypeName>
        </Field>
        <Field Name="ASK">
          <DataField>ASK</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="CNAME">
          <DataField>CNAME</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="BSPHN">
          <DataField>BSPHN</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="CELL">
          <DataField>CELL</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="NAME">
          <DataField>NAME</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="PRE1YEAR">
          <DataField>PRE1YEAR</DataField>
          <rd:TypeName>System.Decimal</rd:TypeName>
        </Field>
        <Field Name="CLUBCODE">
          <DataField>CLUBCODE</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="CLUBSTATUS">
          <DataField>CLUBSTATUS</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="outsideGiving">
          <DataField>outsideGiving</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="EMAIL">
          <DataField>EMAIL</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="Notes">
          <DataField>Notes</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="givingHistoryByFund">
          <DataField>givingHistoryByFund</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="ADDR1">
          <DataField>ADDR1</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="ADDR2">
          <DataField>ADDR2</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="STREET">
          <DataField>STREET</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="CITY">
          <DataField>CITY</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="STATE">
          <DataField>STATE</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="ZIP">
          <DataField>ZIP</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="BIO">
          <DataField>BIO</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="TITLE">
          <DataField>TITLE</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="YTDAMT">
          <DataField>YTDAMT</DataField>
          <rd:TypeName>System.Decimal</rd:TypeName>
        </Field>
        <Field Name="FUNDCODE">
          <DataField>FUNDCODE</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="PICTURE">
          <DataField>PICTURE</DataField>
          <rd:TypeName>System.Byte[]</rd:TypeName>
        </Field>
      </Fields>
    </DataSet>
  </DataSets>
      <Body>
        <ReportItems>
          <Tablix Name="List1">
            <TablixBody>
              <TablixColumns>
                <TablixColumn>
                  <Width>7.76042in</Width>
                </TablixColumn>
              </TablixColumns>
              <TablixRows>
                <TablixRow>
                  <Height>7.66666in</Height>
                  <TablixCells>
                    <TablixCell>
                      <CellContents>
                        <Rectangle Name="List1_Contents">
                          <ReportItems>
                            <Image Name="image1">
                              <Source>External</Source>
                              <Value>=Fields!IMGPATH.Value</Value>
                              <MIMEType>image/bmp</MIMEType>
                              <Sizing>Fit</Sizing>
                              <Top>0.17708in</Top>
                              <Left>5.86111in</Left>
                              <Height>0.9375in</Height>
                              <Width>1.77431in</Width>
                              <Style>
                                <Border>
                                  <Style>None</Style>
                                </Border>
                              </Style>
                            </Image>
                            <Line Name="line8">
                              <Top>1.20833in</Top>
                              <Left>0.14758in</Left>
                              <Height>0in</Height>
                              <Width>7.48958in</Width>
                              <ZIndex>1</ZIndex>
                              <Style>
                                <Border>
                                  <Style>Solid</Style>
                                </Border>
                              </Style>
                            </Line>
                            <Textbox Name="tbSubject">
                              <CanGrow>true</CanGrow>
                              <KeepTogether>true</KeepTogether>
                              <Paragraphs>
                                <Paragraph>
                                  <TextRuns>
                                    <TextRun>
                                      <Value>Call Sheet</Value>
                                      <Style>
                                        <FontWeight>Bold</FontWeight>
                                      </Style>
                                    </TextRun>
                                  </TextRuns>
                                  <Style />
                                </Paragraph>
                              </Paragraphs>
                              <Top>0.29167in</Top>
                              <Left>0.26216in</Left>
                              <Height>0.25in</Height>
                              <Width>4.59201in</Width>
                              <ZIndex>2</ZIndex>
                              <Style>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                            </Textbox>
                            <Textbox Name="tbName">
                              <CanGrow>true</CanGrow>
                              <KeepTogether>true</KeepTogether>
                              <Paragraphs>
                                <Paragraph>
                                  <TextRuns>
                                    <TextRun>
                                      <Value>CALL WITH:</Value>
                                      <Style>
                                        <FontSize>12pt</FontSize>
                                        <FontWeight>Bold</FontWeight>
                                      </Style>
                                    </TextRun>
                                  </TextRuns>
                                  <Style />
                                </Paragraph>
                              </Paragraphs>
                              <Top>1.38194in</Top>
                              <Left>0.26216in</Left>
                              <Height>0.25in</Height>
                              <Width>2.125in</Width>
                              <ZIndex>3</ZIndex>
                              <Style>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                            </Textbox>
                            <Textbox Name="tbPID">
                              <CanGrow>true</CanGrow>
                              <KeepTogether>true</KeepTogether>
                              <Paragraphs>
                                <Paragraph>
                                  <TextRuns>
                                    <TextRun>
                                      <Value>=Fields!CNAME.Value</Value>
                                      <Style />
                                    </TextRun>
                                  </TextRuns>
                                  <Style>
                                    <TextAlign>Left</TextAlign>
                                  </Style>
                                </Paragraph>
                              </Paragraphs>
                              <Top>1.38194in</Top>
                              <Left>2.38716in</Left>
                              <Height>0.25in</Height>
                              <Width>1.70486in</Width>
                              <ZIndex>4</ZIndex>
                              <Style>
                                <Border>
                                  <Style>None</Style>
                                </Border>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                            </Textbox>
                            <Textbox Name="tbOccupationAndEmployer">
                              <CanGrow>true</CanGrow>
                              <KeepTogether>true</KeepTogether>
                              <Paragraphs>
                                <Paragraph>
                                  <TextRuns>
                                    <TextRun>
                                      <Value>=Fields!TITLE.Value</Value>
                                      <Style />
                                    </TextRun>
                                  </TextRuns>
                                  <Style />
                                </Paragraph>
                              </Paragraphs>
                              <Top>1.38194in</Top>
                              <Left>4.16146in</Left>
                              <Height>0.25in</Height>
                              <Width>3.4757in</Width>
                              <ZIndex>5</ZIndex>
                              <Style>
                                <Border>
                                  <Style>None</Style>
                                </Border>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                            </Textbox>
                            <Textbox Name="tbBackgroundLabel">
                              <CanGrow>true</CanGrow>
                              <KeepTogether>true</KeepTogether>
                              <Paragraphs>
                                <Paragraph>
                                  <TextRuns>
                                    <TextRun>
                                      <Value>527 POLITICAL GIVING HISTORY:</Value>
                                      <Style>
                                        <FontSize>12pt</FontSize>
                                        <FontWeight>Bold</FontWeight>
                                      </Style>
                                    </TextRun>
                                  </TextRuns>
                                  <Style />
                                </Paragraph>
                              </Paragraphs>
                              <Top>4.44444in</Top>
                              <Left>0.26216in</Left>
                              <Height>0.25in</Height>
                              <Width>4.06944in</Width>
                              <ZIndex>6</ZIndex>
                              <Style>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                            </Textbox>
                            <Textbox Name="tbSubject2">
                              <CanGrow>true</CanGrow>
                              <KeepTogether>true</KeepTogether>
                              <Paragraphs>
                                <Paragraph>
                                  <TextRuns>
                                    <TextRun>
                                      <Value>=Fields!NAME.Value</Value>
                                      <Style>
                                        <FontSize>16pt</FontSize>
                                      </Style>
                                    </TextRun>
                                  </TextRuns>
                                  <Style />
                                </Paragraph>
                              </Paragraphs>
                              <Top>0.67188in</Top>
                              <Left>0.26215in</Left>
                              <Height>0.30208in</Height>
                              <Width>4.59202in</Width>
                              <ZIndex>7</ZIndex>
                              <Style>
                                <Border>
                                  <Style>None</Style>
                                </Border>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                            </Textbox>
                            <Textbox Name="tbName2">
                              <CanGrow>true</CanGrow>
                              <KeepTogether>true</KeepTogether>
                              <Paragraphs>
                                <Paragraph>
                                  <TextRuns>
                                    <TextRun>
                                      <Value>CONTACT:</Value>
                                      <Style>
                                        <FontSize>12pt</FontSize>
                                        <FontWeight>Bold</FontWeight>
                                      </Style>
                                    </TextRun>
                                  </TextRuns>
                                  <Style />
                                </Paragraph>
                              </Paragraphs>
                              <Top>1.77257in</Top>
                              <Left>0.26216in</Left>
                              <Height>0.25in</Height>
                              <Width>2.125in</Width>
                              <ZIndex>8</ZIndex>
                              <Style>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                            </Textbox>
                            <Textbox Name="tbPID2">
                              <CanGrow>true</CanGrow>
                              <KeepTogether>true</KeepTogether>
                              <Paragraphs>
                                <Paragraph>
                                  <TextRuns>
                                    <TextRun>
                                      <Value>Work Phone</Value>
                                      <Style />
                                    </TextRun>
                                  </TextRuns>
                                  <Style>
                                    <TextAlign>Left</TextAlign>
                                  </Style>
                                </Paragraph>
                              </Paragraphs>
                              <Top>1.77257in</Top>
                              <Left>2.38716in</Left>
                              <Height>0.25in</Height>
                              <Width>1.70486in</Width>
                              <ZIndex>9</ZIndex>
                              <Style>
                                <Border>
                                  <Style>None</Style>
                                </Border>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                            </Textbox>
                            <Textbox Name="tbOccupationAndEmployer2">
                              <CanGrow>true</CanGrow>
                              <KeepTogether>true</KeepTogether>
                              <Paragraphs>
                                <Paragraph>
                                  <TextRuns>
                                    <TextRun>
                                      <Value>=Fields!BSPHN.Value</Value>
                                      <Style />
                                    </TextRun>
                                  </TextRuns>
                                  <Style />
                                </Paragraph>
                              </Paragraphs>
                              <Top>1.77257in</Top>
                              <Left>4.16146in</Left>
                              <Height>0.25in</Height>
                              <Width>3.4757in</Width>
                              <ZIndex>10</ZIndex>
                              <Style>
                                <Border>
                                  <Style>None</Style>
                                </Border>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                            </Textbox>
                            <Textbox Name="tbPID3">
                              <CanGrow>true</CanGrow>
                              <KeepTogether>true</KeepTogether>
                              <Paragraphs>
                                <Paragraph>
                                  <TextRuns>
                                    <TextRun>
                                      <Value>Cell Phone</Value>
                                      <Style />
                                    </TextRun>
                                  </TextRuns>
                                  <Style>
                                    <TextAlign>Left</TextAlign>
                                  </Style>
                                </Paragraph>
                              </Paragraphs>
                              <Top>2.03646in</Top>
                              <Left>2.38715in</Left>
                              <Height>0.25in</Height>
                              <Width>1.70486in</Width>
                              <ZIndex>11</ZIndex>
                              <Style>
                                <Border>
                                  <Style>None</Style>
                                </Border>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                            </Textbox>
                            <Textbox Name="tbOccupationAndEmployer3">
                              <CanGrow>true</CanGrow>
                              <KeepTogether>true</KeepTogether>
                              <Paragraphs>
                                <Paragraph>
                                  <TextRuns>
                                    <TextRun>
                                      <Value>=Fields!CELL.Value</Value>
                                      <Style />
                                    </TextRun>
                                  </TextRuns>
                                  <Style />
                                </Paragraph>
                              </Paragraphs>
                              <Top>2.03646in</Top>
                              <Left>4.16145in</Left>
                              <Height>0.25in</Height>
                              <Width>3.4757in</Width>
                              <ZIndex>12</ZIndex>
                              <Style>
                                <Border>
                                  <Style>None</Style>
                                </Border>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                            </Textbox>
                            <Textbox Name="tbName4">
                              <CanGrow>true</CanGrow>
                              <KeepTogether>true</KeepTogether>
                              <Paragraphs>
                                <Paragraph>
                                  <TextRuns>
                                    <TextRun>
                                      <Value>ASK:</Value>
                                      <Style>
                                        <FontSize>12pt</FontSize>
                                        <FontWeight>Bold</FontWeight>
                                      </Style>
                                    </TextRun>
                                  </TextRuns>
                                  <Style />
                                </Paragraph>
                              </Paragraphs>
                              <Top>2.69704in</Top>
                              <Left>0.26215in</Left>
                              <Height>0.25in</Height>
                              <Width>2.125in</Width>
                              <ZIndex>13</ZIndex>
                              <Style>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                            </Textbox>
                            <Textbox Name="tbPID4">
                              <CanGrow>true</CanGrow>
                              <KeepTogether>true</KeepTogether>
                              <Paragraphs>
                                <Paragraph>
                                  <TextRuns>
                                    <TextRun>
                                      <Value>=Fields!ASK.Value</Value>
                                      <Style />
                                    </TextRun>
                                  </TextRuns>
                                  <Style>
                                    <TextAlign>Left</TextAlign>
                                  </Style>
                                </Paragraph>
                              </Paragraphs>
                              <Top>2.69704in</Top>
                              <Left>2.38715in</Left>
                              <Height>0.25in</Height>
                              <Width>5.25in</Width>
                              <ZIndex>14</ZIndex>
                              <Style>
                                <Border>
                                  <Style>None</Style>
                                </Border>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                            </Textbox>
                            <Textbox Name="tbName5">
                              <CanGrow>true</CanGrow>
                              <KeepTogether>true</KeepTogether>
                              <Paragraphs>
                                <Paragraph>
                                  <TextRuns>
                                    <TextRun>
                                      <Value>=Fields!CLUBCODE.Value + " MEMBERSHIP:"</Value>
                                      <Style>
                                        <FontSize>12pt</FontSize>
                                        <FontWeight>Bold</FontWeight>
                                      </Style>
                                    </TextRun>
                                  </TextRuns>
                                  <Style />
                                </Paragraph>
                              </Paragraphs>
                              <Top>3.10851in</Top>
                              <Left>0.26216in</Left>
                              <Height>0.25in</Height>
                              <Width>2.125in</Width>
                              <ZIndex>15</ZIndex>
                              <Style>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                            </Textbox>
                            <Textbox Name="tbPID5">
                              <CanGrow>true</CanGrow>
                              <KeepTogether>true</KeepTogether>
                              <Paragraphs>
                                <Paragraph>
                                  <TextRuns>
                                    <TextRun>
                                      <Value>=Fields!CLUBSTATUS.Value</Value>
                                      <Style />
                                    </TextRun>
                                  </TextRuns>
                                  <Style>
                                    <TextAlign>Left</TextAlign>
                                  </Style>
                                </Paragraph>
                              </Paragraphs>
                              <Top>3.10851in</Top>
                              <Left>2.38716in</Left>
                              <Height>0.25in</Height>
                              <Width>5.25in</Width>
                              <ZIndex>16</ZIndex>
                              <Style>
                                <Border>
                                  <Style>None</Style>
                                </Border>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                            </Textbox>
                            <Textbox Name="tbName6">
                              <CanGrow>true</CanGrow>
                              <KeepTogether>true</KeepTogether>
                              <Paragraphs>
                                <Paragraph>
                                  <TextRuns>
                                    <TextRun>
                                      <Value>HEADQUARTERS:</Value>
                                      <Style>
                                        <FontSize>12pt</FontSize>
                                        <FontWeight>Bold</FontWeight>
                                      </Style>
                                    </TextRun>
                                  </TextRuns>
                                  <Style />
                                </Paragraph>
                              </Paragraphs>
                              <Top>3.49913in</Top>
                              <Left>0.26216in</Left>
                              <Height>0.25in</Height>
                              <Width>2.125in</Width>
                              <ZIndex>17</ZIndex>
                              <Style>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                            </Textbox>
                            <Textbox Name="tbPID6">
                              <CanGrow>true</CanGrow>
                              <KeepTogether>true</KeepTogether>
                              <Paragraphs>
                                <Paragraph>
                                  <TextRuns>
                                    <TextRun>
                                      <Value>=Fields!STREET.Value</Value>
                                      <Style />
                                    </TextRun>
                                  </TextRuns>
                                  <Style>
                                    <TextAlign>Left</TextAlign>
                                  </Style>
                                </Paragraph>
                              </Paragraphs>
                              <Top>3.49913in</Top>
                              <Left>2.38716in</Left>
                              <Height>0.25in</Height>
                              <Width>5.25in</Width>
                              <ZIndex>18</ZIndex>
                              <Style>
                                <Border>
                                  <Style>None</Style>
                                </Border>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                            </Textbox>
                            <Textbox Name="tbPID7">
                              <CanGrow>true</CanGrow>
                              <KeepTogether>true</KeepTogether>
                              <Paragraphs>
                                <Paragraph>
                                  <TextRuns>
                                    <TextRun>
                                      <Value>Email</Value>
                                      <Style />
                                    </TextRun>
                                  </TextRuns>
                                  <Style>
                                    <TextAlign>Left</TextAlign>
                                  </Style>
                                </Paragraph>
                              </Paragraphs>
                              <Top>2.30035in</Top>
                              <Left>2.38715in</Left>
                              <Height>0.25in</Height>
                              <Width>1.70486in</Width>
                              <ZIndex>19</ZIndex>
                              <Style>
                                <Border>
                                  <Style>None</Style>
                                </Border>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                            </Textbox>
                            <Textbox Name="tbOccupationAndEmployer7">
                              <CanGrow>true</CanGrow>
                              <KeepTogether>true</KeepTogether>
                              <Paragraphs>
                                <Paragraph>
                                  <TextRuns>
                                    <TextRun>
                                      <Value>=Fields!EMAIL.Value</Value>
                                      <Style />
                                    </TextRun>
                                  </TextRuns>
                                  <Style />
                                </Paragraph>
                              </Paragraphs>
                              <Top>2.30035in</Top>
                              <Left>4.16145in</Left>
                              <Height>0.25in</Height>
                              <Width>3.4757in</Width>
                              <ZIndex>20</ZIndex>
                              <Style>
                                <Border>
                                  <Style>None</Style>
                                </Border>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                            </Textbox>
                            <Textbox Name="tbPID8">
                              <CanGrow>true</CanGrow>
                              <KeepTogether>true</KeepTogether>
                              <Paragraphs>
                                <Paragraph>
                                  <TextRuns>
                                    <TextRun>
                                      <Value>=Fields!ADDR1.Value</Value>
                                      <Style />
                                    </TextRun>
                                  </TextRuns>
                                  <Style>
                                    <TextAlign>Left</TextAlign>
                                  </Style>
                                </Paragraph>
                              </Paragraphs>
                              <Top>3.76302in</Top>
                              <Left>2.38715in</Left>
                              <Height>0.25in</Height>
                              <Width>5.25in</Width>
                              <ZIndex>21</ZIndex>
                              <Style>
                                <Border>
                                  <Style>None</Style>
                                </Border>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                            </Textbox>
                            <Textbox Name="tbPID9">
                              <CanGrow>true</CanGrow>
                              <KeepTogether>true</KeepTogether>
                              <Paragraphs>
                                <Paragraph>
                                  <TextRuns>
                                    <TextRun>
                                      <Value>=Fields!CITY.Value+" " +Fields!STATE.Value+" "+Fields!ZIP.Value</Value>
                                      <Style />
                                    </TextRun>
                                  </TextRuns>
                                  <Style>
                                    <TextAlign>Left</TextAlign>
                                  </Style>
                                </Paragraph>
                              </Paragraphs>
                              <Top>4.02691in</Top>
                              <Left>2.38715in</Left>
                              <Height>0.25in</Height>
                              <Width>5.25in</Width>
                              <ZIndex>22</ZIndex>
                              <Style>
                                <Border>
                                  <Style>None</Style>
                                </Border>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                            </Textbox>
                            <Textbox Name="BIO2">
                              <CanGrow>true</CanGrow>
                              <KeepTogether>true</KeepTogether>
                              <Paragraphs>
                                <Paragraph>
                                  <TextRuns>
                                    <TextRun>
                                      <Value>=Fields!outsideGiving.Value</Value>
                                      <Style />
                                    </TextRun>
                                  </TextRuns>
                                  <Style />
                                </Paragraph>
                              </Paragraphs>
                              <rd:DefaultName>BIO</rd:DefaultName>
                              <Top>5.80035in</Top>
                              <Left>0.26215in</Left>
                              <Height>0.20833in</Height>
                              <Width>7.375in</Width>
                              <ZIndex>23</ZIndex>
                              <Style>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                            </Textbox>
                            <Textbox Name="tbBackgroundLabel2">
                              <CanGrow>true</CanGrow>
                              <KeepTogether>true</KeepTogether>
                              <Paragraphs>
                                <Paragraph>
                                  <TextRuns>
                                    <TextRun>
                                      <Value>Outside Giving:</Value>
                                      <Style>
                                        <FontSize>12pt</FontSize>
                                        <FontWeight>Bold</FontWeight>
                                      </Style>
                                    </TextRun>
                                  </TextRuns>
                                  <Style />
                                </Paragraph>
                              </Paragraphs>
                              <Top>5.52257in</Top>
                              <Left>0.26215in</Left>
                              <Height>0.25in</Height>
                              <Width>1.375in</Width>
                              <ZIndex>24</ZIndex>
                              <Style>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                            </Textbox>
                            <Textbox Name="tbName7">
                              <CanGrow>true</CanGrow>
                              <KeepTogether>true</KeepTogether>
                              <Paragraphs>
                                <Paragraph>
                                  <TextRuns>
                                    <TextRun>
                                      <Value>Latest Gift Sum:</Value>
                                      <Style>
                                        <FontSize>12pt</FontSize>
                                        <FontWeight>Bold</FontWeight>
                                      </Style>
                                    </TextRun>
                                  </TextRuns>
                                  <Style />
                                </Paragraph>
                              </Paragraphs>
                              <Top>4.76389in</Top>
                              <Left>0.57466in</Left>
                              <Height>0.25in</Height>
                              <Width>2.125in</Width>
                              <ZIndex>25</ZIndex>
                              <Style>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                            </Textbox>
                            <Textbox Name="tbName8">
                              <CanGrow>true</CanGrow>
                              <KeepTogether>true</KeepTogether>
                              <Paragraphs>
                                <Paragraph>
                                  <TextRuns>
                                    <TextRun>
                                      <Value>=Fields!FUNDCODE.Value+" YTD:"</Value>
                                      <Style>
                                        <FontSize>12pt</FontSize>
                                        <FontWeight>Bold</FontWeight>
                                      </Style>
                                    </TextRun>
                                  </TextRuns>
                                  <Style />
                                </Paragraph>
                              </Paragraphs>
                              <Top>5.08333in</Top>
                              <Left>0.57466in</Left>
                              <Height>0.25in</Height>
                              <Width>2.125in</Width>
                              <ZIndex>26</ZIndex>
                              <Style>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                            </Textbox>
                            <Textbox Name="tbPID10">
                              <CanGrow>true</CanGrow>
                              <KeepTogether>true</KeepTogether>
                              <Paragraphs>
                                <Paragraph>
                                  <TextRuns>
                                    <TextRun>
                                      <Value>=Fields!PRE1YEAR.Value</Value>
                                      <Style>
                                        <Format>'$'0.00;('$'0.00)</Format>
                                      </Style>
                                    </TextRun>
                                  </TextRuns>
                                  <Style>
                                    <TextAlign>Left</TextAlign>
                                  </Style>
                                </Paragraph>
                              </Paragraphs>
                              <Top>4.76389in</Top>
                              <Left>2.90973in</Left>
                              <Height>0.25in</Height>
                              <Width>1.94444in</Width>
                              <ZIndex>27</ZIndex>
                              <Style>
                                <Border>
                                  <Style>None</Style>
                                </Border>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                                <rd:FormatSymbolCulture>en-US</rd:FormatSymbolCulture>
                              </Style>
                            </Textbox>
                            <Textbox Name="tbPID11">
                              <CanGrow>true</CanGrow>
                              <KeepTogether>true</KeepTogether>
                              <Paragraphs>
                                <Paragraph>
                                  <TextRuns>
                                    <TextRun>
                                      <Value>=Fields!YTDAMT.Value</Value>
                                      <Style>
                                        <Format>'$'0.00;('$'0.00)</Format>
                                      </Style>
                                    </TextRun>
                                  </TextRuns>
                                  <Style>
                                    <TextAlign>Left</TextAlign>
                                  </Style>
                                </Paragraph>
                              </Paragraphs>
                              <Top>5.08333in</Top>
                              <Left>2.90973in</Left>
                              <Height>0.25in</Height>
                              <Width>1.94444in</Width>
                              <ZIndex>28</ZIndex>
                              <Style>
                                <Border>
                                  <Style>None</Style>
                                </Border>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                                <rd:FormatSymbolCulture>en-US</rd:FormatSymbolCulture>
                              </Style>
                            </Textbox>
                            <Textbox Name="BIO3">
                              <CanGrow>true</CanGrow>
                              <KeepTogether>true</KeepTogether>
                              <Paragraphs>
                                <Paragraph>
                                  <TextRuns>
                                    <TextRun>
                                      <Value>=Fields!BIO.Value</Value>
                                      <Style />
                                    </TextRun>
                                  </TextRuns>
                                  <Style />
                                </Paragraph>
                              </Paragraphs>
                              <rd:DefaultName>BIO</rd:DefaultName>
                              <Top>6.32812in</Top>
                              <Left>0.26216in</Left>
                              <Height>0.20833in</Height>
                              <Width>7.375in</Width>
                              <ZIndex>29</ZIndex>
                              <Style>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                            </Textbox>
                            <Textbox Name="tbBackgroundLabel3">
                              <CanGrow>true</CanGrow>
                              <KeepTogether>true</KeepTogether>
                              <Paragraphs>
                                <Paragraph>
                                  <TextRuns>
                                    <TextRun>
                                      <Value>Background:</Value>
                                      <Style>
                                        <FontSize>12pt</FontSize>
                                        <FontWeight>Bold</FontWeight>
                                      </Style>
                                    </TextRun>
                                  </TextRuns>
                                  <Style />
                                </Paragraph>
                              </Paragraphs>
                              <Top>6.07812in</Top>
                              <Left>0.26216in</Left>
                              <Height>0.25in</Height>
                              <Width>1.375in</Width>
                              <ZIndex>30</ZIndex>
                              <Style>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                            </Textbox>
                            <Textbox Name="BIO4">
                              <CanGrow>true</CanGrow>
                              <KeepTogether>true</KeepTogether>
                              <Paragraphs>
                                <Paragraph>
                                  <TextRuns>
                                    <TextRun>
                                      <Value>=Fields!Notes.Value</Value>
                                      <Style />
                                    </TextRun>
                                  </TextRuns>
                                  <Style />
                                </Paragraph>
                              </Paragraphs>
                              <rd:DefaultName>BIO</rd:DefaultName>
                              <Top>6.8559in</Top>
                              <Left>0.26216in</Left>
                              <Height>0.1875in</Height>
                              <Width>7.375in</Width>
                              <ZIndex>31</ZIndex>
                              <Style>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                            </Textbox>
                            <Textbox Name="tbBackgroundLabel4">
                              <CanGrow>true</CanGrow>
                              <KeepTogether>true</KeepTogether>
                              <Paragraphs>
                                <Paragraph>
                                  <TextRuns>
                                    <TextRun>
                                      <Value>Notes:</Value>
                                      <Style>
                                        <FontSize>12pt</FontSize>
                                        <FontWeight>Bold</FontWeight>
                                      </Style>
                                    </TextRun>
                                  </TextRuns>
                                  <Style />
                                </Paragraph>
                              </Paragraphs>
                              <Top>6.6059in</Top>
                              <Left>0.26216in</Left>
                              <Height>0.25in</Height>
                              <Width>1.375in</Width>
                              <ZIndex>32</ZIndex>
                              <Style>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                            </Textbox>
                            <Textbox Name="BIO5">
                              <CanGrow>true</CanGrow>
                              <KeepTogether>true</KeepTogether>
                              <Paragraphs>
                                <Paragraph>
                                  <TextRuns>
                                    <TextRun>
                                      <Value>=Fields!givingHistoryByFund.Value</Value>
                                      <Style />
                                    </TextRun>
                                  </TextRuns>
                                  <Style />
                                </Paragraph>
                              </Paragraphs>
                              <rd:DefaultName>BIO</rd:DefaultName>
                              <Top>7.36285in</Top>
                              <Left>0.26216in</Left>
                              <Height>0.1875in</Height>
                              <Width>7.375in</Width>
                              <ZIndex>33</ZIndex>
                              <Style>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                            </Textbox>
                            <Textbox Name="tbBackgroundLabel5">
                              <CanGrow>true</CanGrow>
                              <KeepTogether>true</KeepTogether>
                              <Paragraphs>
                                <Paragraph>
                                  <TextRuns>
                                    <TextRun>
                                      <Value>="Contribution History ("+Fields!FUNDCODE.Value+"):"</Value>
                                      <Style>
                                        <FontSize>12pt</FontSize>
                                        <FontWeight>Bold</FontWeight>
                                      </Style>
                                    </TextRun>
                                  </TextRuns>
                                  <Style />
                                </Paragraph>
                              </Paragraphs>
                              <Top>7.11285in</Top>
                              <Left>0.26216in</Left>
                              <Height>0.25in</Height>
                              <Width>4.06944in</Width>
                              <ZIndex>34</ZIndex>
                              <Style>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                            </Textbox>
                          </ReportItems>
                          <KeepTogether>true</KeepTogether>
                          <Style />
                        </Rectangle>
                      </CellContents>
                    </TablixCell>
                  </TablixCells>
                </TablixRow>
              </TablixRows>
            </TablixBody>
            <TablixColumnHierarchy>
              <TablixMembers>
                <TablixMember />
              </TablixMembers>
            </TablixColumnHierarchy>
            <TablixRowHierarchy>
              <TablixMembers>
                <TablixMember>
                  <Group Name="list1_PID">
                    <GroupExpressions>
                      <GroupExpression>=Fields!PID.Value</GroupExpression>
                    </GroupExpressions>
                    <PageBreak>
                      <BreakLocation>Between</BreakLocation>
                    </PageBreak>
                  </Group>
                  <DataElementOutput>Output</DataElementOutput>
                  <KeepTogether>true</KeepTogether>
                </TablixMember>
              </TablixMembers>
            </TablixRowHierarchy>
            <DataSetName>iGetProfile</DataSetName>
            <PageBreak>
              <BreakLocation>End</BreakLocation>
            </PageBreak>
            <Height>7.66666in</Height>
            <Width>7.76042in</Width>
            <Style>
              <FontFamily>Tahoma</FontFamily>
              <FontSize>14pt</FontSize>
              <Color>SlateGray</Color>
            </Style>
          </Tablix>
        </ReportItems>
        <Height>7.66666in</Height>
        <Style />
      </Body>
      <Width>7.76042in</Width>
      <Page>
        <PageFooter>
          <Height>0.38542in</Height>
          <PrintOnFirstPage>true</PrintOnFirstPage>
          <PrintOnLastPage>true</PrintOnLastPage>
          <Style />
        </PageFooter>
        <LeftMargin>0.5in</LeftMargin>
        <RightMargin>0.2in</RightMargin>
        <TopMargin>0.5in</TopMargin>
        <BottomMargin>0.5in</BottomMargin>
        <Style />
      </Page>
  <Language>en-US</Language>
  <ConsumeContainerWhitespace>true</ConsumeContainerWhitespace>
  <rd:ReportUnitType>Inch</rd:ReportUnitType>
  <rd:ReportID>b273b6bf-bd8b-481f-9f49-ef6634214383</rd:ReportID>
</Report>