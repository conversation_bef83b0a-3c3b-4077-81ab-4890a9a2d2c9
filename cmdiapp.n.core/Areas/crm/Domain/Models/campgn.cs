﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Data;
using System.Linq;
using System.Web.Mvc;
using System.Data.Linq;
using System.ComponentModel.DataAnnotations.Schema;

using cmdiapp.n.core.Domain.ViewModels;
using cmdiapp.n.core.Areas.crm.Domain.Data;

namespace cmdiapp.n.core.Areas.crm.Domain.Models
{
    [Table("dmCAMPGN")]
    public class dmCAMPGN : _entityBase_crm, iItemType
    {
        [Column]
        [Key]
        public Int16 CAMPGNID { get; set; }

        [Column]
        public string CAMPGNCODE { get; set; }

        [Column]
        public string DESCRIP { get; set; }

        public string CODEDESCRIP
        {
            get
            {
                return string.Format("{0} - {1}", CAMPGNCODE, DESCRIP);
            }
        }

        [Column]
        public DateTime UPDATEDON { get; set; }
    }
}