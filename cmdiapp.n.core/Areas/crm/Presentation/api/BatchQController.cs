﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Net;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Web.Http;
using System.Web.Script.Serialization;
using System.Drawing;
using System.IO;
using System.Runtime.Serialization.Json;

using Ninject;
using Ninject.Web.Mvc;

using AutoMapper;

using cmdiapp.n.core.Core;
using cmdiapp.n.core.Core.App_Start;
using cmdiapp.n.core.Library;
using cmdiapp.n.core.Domain.ViewModels;
using cmdiapp.n.core.Presentation.Controllers;
using cmdiapp.n.core.Areas.crm.Core;
using cmdiapp.n.core.Areas.crm.Domain.Models;
using cmdiapp.n.core.Areas.crm.Domain.Data;
using cmdiapp.n.core.Areas.crm.Domain.Services;
using cmdiapp.n.core.Areas.query.Domain.Models;
using cmdiapp.n.core.Areas.query;
using cmdiapp.n.core.Areas.dataService.Domain.Data;
using cmdiapp.n.core.Areas.dataService.Domain.Models;
using cmdiapp.n.core.Areas.dataService.Domain.Services;

namespace cmdiapp.n.core.Areas.crm.Presentation.Controllers.api
{
    ///  (controller) BatchQ

    [Authorize]
    [apiAuthorize(AccessElement = "cmdiapp.dms.batch_qq", AccessLevel = "v")]
    public class BatchQController : ApiController
    {
        #region [[ Declaration ]]
        private I_entity_crm _entity_crm;
        private IdataService _dataService;
        #endregion

        #region [[ (constructor) BatchQController ]]
        public BatchQController()
        {
            _entity_crm = I_entityManager_crm.getEntity();
            _dataService = I_entityManager_ds.getService();
        }
        #endregion


        #region [[ sub routines for Search & Export ]]

        #region [ (private-List<BatchQR_ext1>) Quick Text Search ]
        private List<BatchQR_ext1> quickTextSearch(string searchText, int pageSize, int pageNo, string sortOptions, string qDefName = "crm_batchSearch")
        {

            string _where = "";
            DateTime temp;

            #region [[ Search by ID if numeric ]]
            int id;
            if (int.TryParse(searchText, out id))
            {
                _where = string.Format("F.FUNDID = DF.FUNDID AND F.BATTYPEID = LB.BATTYPEID  AND F.BATCHID = {0}", id);
            }
            #endregion

            #region [[ Otherwise, Search by String ]]
            else if (!string.IsNullOrEmpty(searchText))
            {

                #region [ Split Search Text > string1, string2 & string3 and create "Where" statement ]
                char[] delimiterChars = { ' ', ',', '.', ';', ':', '|', '\t' };

                string[] words = searchText.Split(delimiterChars);
                               

                if (words.Count() > 0)
                {
                    string string1 = words[0];
                    string string2 = (words.Count() > 1 ? words[1] : "");
                    string string3 = (words.Count() > 2 ? words[2] : "");

                    #region [ Where Clause ]
                    switch (words.Count())
                    {
                        case 1: // Batch Number or Batch Date
                            if (DateTime.TryParse(string1, out temp))
                            {
                                _where = "AND F.BATCHDTE >= " + "''" + string1.Trim() + " 00:00:00''";
                                _where = _where + " AND F.BATCHDTE <= " + "''" + string1.Trim() + " 23:59:59''";

                            }
                            else
                            {
                                _where = string.Format("AND F.BATCHNO = ''{0}''", string1);
                            }
                            break;
                        case 2: // Batch Date or/and Batch #
                            if (DateTime.TryParse(string1, out temp))
                            {
                                _where = string.Format("AND (F.BATCHNO = ''{0}''", string2);
                                _where = _where + " OR (F.BATCHDTE >= " + "''" + string1.Trim() + " 00:00:00''";
                                _where = _where + " AND F.BATCHDTE <= " + "''" + string1.Trim() + " 23:59:59''";
                                _where = _where + "))";
                            }
                            else if (DateTime.TryParse(string2, out temp))
                            {
                                _where = string.Format("AND (F.BATCHNO = ''{0}''", string1);
                                _where = _where + " OR (F.BATCHDTE >= " + "''" + string2.Trim() + " 00:00:00''";
                                _where = _where + " AND F.BATCHDTE <= " + "''" + string2.Trim() + " 23:59:59''";
                                _where = _where + "))";

                            }
                            else
                            {
                                _where = string.Format("AND F.BATCHNO = ''{0}''", string1);
                            }
                            break;

                        default:
                            break;
                    }
                    #endregion

                }
                else
                    return new List<BatchQR_ext1>();
                #endregion

            }
            #endregion

            // No search text is given, return an empty dataset
            else
                return new List<BatchQR_ext1>();

            #region [[ Run a Query ]]
            if (string.IsNullOrEmpty(sortOptions)) sortOptions = "BATCHID DESC";

            // SQL Definition
            q_def_S _def = q_library.get_q_def_fr_cache(qDefName);

            // Compose SQL
            string sql = string.Format(@"
                            SELECT DISTINCT 
                            {0}
                            FROM {1}
                            WHERE F.FUNDID = DF.FUNDID AND F.BATTYPEID = LB.BATTYPEID  AND {2}
                        ", _def.sq_fieldsV, _def.sq_from, _where);

            sql = sql.Trim().Replace("'", "''");

            System.Diagnostics.Stopwatch stopWatch = new System.Diagnostics.Stopwatch();
            stopWatch.Start();
            var q = _entity_crm.getContext().Database.SqlQuery<BatchQR_ext1>(String.Format("EXEC dbo.get_pagedDataSet '{0}', '{1}', {2}, {3}", sql, sortOptions, pageSize, pageNo));

            List<BatchQR_ext1> result = q.ToList();
            stopWatch.Stop();

            cl_query.create_a_sqlLog(
                        sql.Trim(),
                        "crm/api/BatchQ/Search(Adv)",
                        util.serialize_toXmlString(_where),
                        crmSession.UID().Value,
                        (result != null && result.Count() > 0 ? result.FirstOrDefault().count_ : 0),
                        "Batch Search - View Columns",
                        "",
                        _entity_crm,
             Convert.ToSingle(stopWatch.ElapsedMilliseconds / 1000.0));

            return result;
            #endregion
        }
        #endregion

        #region [ (private) Advanced Search - SQL Filtering ]

            #region [ func.string.get_sqlFIELDs ] Enable optional OUTPUT fields if used.
            private string get_sqlFIELDs(string p_sql_where, string p_default_fields)
            {

                #region if Political (In other words, Not a Non-profit), include
                if (!string.IsNullOrEmpty(session.userSession.getConfigVal(crmConstants.nonProfit))
                    || session.userSession.getConfigVal(crmConstants.nonProfit).ToUpper() != "Y")
                {
                    p_default_fields = p_default_fields.Replace("/*ADDR-PoliticalOnly", "");
                    p_default_fields = p_default_fields.Replace("ADDR-PoliticalOnly*/", "");
                }
                #endregion

                #region if Political (GIfts), include
                if (!string.IsNullOrEmpty(session.userSession.getConfigVal(crmConstants.nonProfit))
                    || session.userSession.getConfigVal(crmConstants.nonProfit).ToUpper() != "Y")
                {
                    p_default_fields = p_default_fields.Replace("/*GIFT-PoliticalOnly", "");
                    p_default_fields = p_default_fields.Replace("GIFT-PoliticalOnly*/", "");
                }
                #endregion

                return p_default_fields;
            }
            #endregion

            #endregion

        #region [ (private-List<BatchQR_ext1>) Advanced Search ]
        private List<BatchQR_ext1> sqlSearch(List<searchItem> filters, int pageSize, int pageNo, string sortOptions, string qDefName = "crm_batchSearch")
        {
            if (string.IsNullOrEmpty(sortOptions)) sortOptions = "BATCHID DESC";

            // SQL Definition
            q_def_S _def = q_library.get_q_def_fr_cache(qDefName);

            System.Diagnostics.Stopwatch stopWatch = new System.Diagnostics.Stopwatch();
            stopWatch.Start();
            // Compose SQL
            string sql = q_library.getSQL(_def, filters, session.currentDomain_project._connectionString(), null, get_sqlFIELDs, true);

            sql = sql.Replace("WHERE", " WHERE F.FUNDID = DF.FUNDID AND F.BATTYPEID = LB.BATTYPEID  AND ");

            string sqlParam = sql.Trim().Replace("'", "''");
            var q = _entity_crm.getContext().Database.SqlQuery<BatchQR_ext1>(String.Format("EXEC dbo.get_pagedDataSet '{0}', '{1}', {2}, {3}", sqlParam, sortOptions, pageSize, pageNo));

            List<BatchQR_ext1> result = q.ToList();
            stopWatch.Stop();

            cl_query.create_a_sqlLog(
                        sql.Trim(),
                        "crm/api/BatchQ/Search(Adv)",
                        util.serialize_toXmlString(filters),
                        crmSession.UID().Value,
                        (result != null && result.Count() > 0 ? result.FirstOrDefault().count_ : 0),
                        "Batch Search - View Columns",
                        "",
                        _entity_crm,
             Convert.ToSingle(stopWatch.ElapsedMilliseconds / 1000.0));

            return result;
        }
        #endregion

        #region [ (private-System.Data.DataTable) Advanced Search - Export ]

        //private System.Data.DataTable sqlSearch_forExport__dataset(List<searchItem> filters)
        private System.Data.DataSet sqlSearch_forExport__dataset(List<searchItem> filters)
        {

            System.Diagnostics.Stopwatch stopWatch = new System.Diagnostics.Stopwatch();
            stopWatch.Start();
            // SQL Definition
            q_def_S _def = q_library.get_q_def_fr_cache("crm_batchSearch");

            // Compose SQL
            string sql = q_library.getSQL(_def, filters, session.currentDomain_project._connectionString(), null, get_sqlFIELDs, false);
            //This will limit the data...
            sql = sql.Replace("WHERE", " WHERE F.FUNDID = DF.FUNDID AND F.BATTYPEID = LB.BATTYPEID  AND ");
            //we only need to pull 100k.....
            sql = sql.Replace("SELECT DISTINCT", "SELECT DISTINCT TOP 100000"); // ############ LIMIT TO 100K FOR NOW)
            //Get Dataset...
            System.Data.DataSet _dataSet = q_library.get_dataset_w_sql__single(session.currentDomain_project._connectionString(), sql, "result", 0, 0);
            stopWatch.Stop();


            //Create a log...
            cl_query.create_a_sqlLog(
            sql.Trim(),
            "crm/api/BatchQ/Export",
            util.serialize_toXmlString(filters),
            crmSession.UID().Value,
            (_dataSet != null && _dataSet.Tables.Count > 0 && _dataSet.Tables[0].Rows.Count > 0 ? _dataSet.Tables[0].Rows.Count : 0),
            "Batch Search - Export Columns",
            "HTTP",     // Saved Path: HTTP Flush 
            _entity_crm,
             Convert.ToSingle(stopWatch.ElapsedMilliseconds / 1000.0));

            //return _dataSet.Tables[0];
            return _dataSet;
        }

        private List<BatchQR_ext1> sqlSearch_forExport__list(List<searchItem> filters)
        {
            // SQL Definition
            q_def_S _def = q_library.get_q_def_fr_cache("crm_batchSearch");

            // Compose SQL
            string sql = q_library.getSQL(_def, filters, session.currentDomain_project._connectionString(), null, get_sqlFIELDs, false);
            sql = sql.Replace("SELECT DISTINCT", "SELECT DISTINCT TOP 100000"); // ############ LIMIT TO 100K FOR NOW)

            var q = _entity_crm.getContext().Database.SqlQuery<BatchQR_ext1>(String.Format("{0}", sql));
            return q.ToList();
        }
        #endregion

        #endregion

        #region [[ (genericResponse) Search[GET] - crm/api/BatchQ/Search ]]
        [HttpGet, Route("crm/api/BatchQ/Search")]
        [apiAuthorize(AccessElement = "cmdiapp.dms.batch_qq", AccessLevel = "v")]
        public genericResponse Search(string searchText, int? page, int? pageSize, string qDefName = "crm_batchSearch")
        {
            #region [ Retrieve Input ]
            string _searchText = Library.util.kill_sqlBlacklistWord((searchText ?? "").Trim().Replace("'", "''''"));

            if (string.IsNullOrEmpty(_searchText))
                return new genericResponse() { __count = 0, success = true };

            int _pageSize = (pageSize == null || pageSize.Value == 0 ? 10 : pageSize.Value);
            int _pageNo = (page == null || page.Value == 0 ? 1 : page.Value);

            string sortField = HttpContext.Current.Request.QueryString["sort[0][field]"];
            string sortDir = HttpContext.Current.Request.QueryString["sort[0][dir]"];
            string _sortOptions = "";
            if (!string.IsNullOrEmpty(sortField))
                _sortOptions = sortField;
            if (!string.IsNullOrEmpty(sortField) && !string.IsNullOrEmpty(sortDir))
                _sortOptions = _sortOptions + " " + sortDir;
            #endregion

            #region [ Quick Text Search ]
            if (!string.IsNullOrEmpty(_searchText))
            {
                List<BatchQR_ext1> _list = quickTextSearch(_searchText, _pageSize, _pageNo, _sortOptions, qDefName);
                if (_list != null && _list.Count() > 0)
                {
                    Mapper.CreateMap<BatchQR_ext1, BatchQR>();
                    IEnumerable<iItemType> results = _list.Select(a => Mapper.Map<BatchQR_ext1, BatchQR>(a)).ToList();
                    return new genericResponse() { success = true, __count = _list.FirstOrDefault().count_, results = results.ToList() };
                }
            }
            #endregion

            return new genericResponse() { success = true, __count = 0 };
        }
        #endregion

        #region [[ (genericResponse) Search[POST] - crm/api/BatchQ/Search ]]
        [HttpPost, Route("crm/api/BatchQ/Search")]
        [apiAuthorize(AccessElement = "cmdiapp.dms.batch_qq", AccessLevel = "v")]
        public genericResponse Search(searchParam _param, string qDefName = "crm_batchSearch")
        {
            #region [ Retrieve Input ]
            string _searchText = _param.searchText_;

            if (string.IsNullOrEmpty(_searchText)
                && (_param.searchData == null || _param.searchData.Count() == 0 || string.IsNullOrEmpty(_param.searchData[0].valuef)))
                return new genericResponse() { __count = 0, success = true };

            int _pageSize = _param.pageSize_;
            int _pageNo = _param.page_;
            string _sortOptions = _param.sortOption1_;
            #endregion

            #region [ HTTPGET - Retrieve "Sort" options ]
            /*
            string sortField = HttpContext.Current.Request.QueryString["sort[0][field]"];
            string sortDir = HttpContext.Current.Request.QueryString["sort[0][dir]"];
            */
            #endregion

            List<BatchQR_ext1> _list;

            #region [ 1. Quick Text Search ]
            if (!string.IsNullOrEmpty(_searchText))
                _list = quickTextSearch(_searchText, _pageSize, _pageNo, _sortOptions);
            #endregion

            #region [ 2. Search Data ]
            else
                _list = sqlSearch(_param.searchData, _pageSize, _pageNo, _sortOptions, qDefName);
            #endregion

            if (_list != null && _list.Count() > 0)
            {
                Mapper.CreateMap<BatchQR_ext1, BatchQR>();
                IEnumerable<iItemType> results = _list.Select(a => Mapper.Map<BatchQR_ext1, BatchQR>(a)).ToList();
                return new genericResponse() { success = true, __count = _list.FirstOrDefault().count_, results = results.ToList() };
            }

            return new genericResponse() { success = true, __count = 0 };
        }
        #endregion

        #region [[ Export ]]
        [HttpPost, Route("crm/api/BatchQ/ExportKey")]
        [apiAuthorize(AccessElement = "cmdiapp.dms.batch_qq", AccessLevel = "e")]
        public string ExportKey(searchParam param)
        {
            string key_ = System.Guid.NewGuid().ToString();
            HttpRuntime.Cache.Insert(key_, param, null, DateTime.UtcNow.Add(new TimeSpan(1, 0, 1)), System.Web.Caching.Cache.NoSlidingExpiration);

            return key_;
        }

        [HttpGet, Route("crm/api/BatchQ/Export")]
        [apiAuthorize(AccessElement = "cmdiapp.dms.batch_qq", AccessLevel = "e")]
        //public bool Export(string key)
        public string Export(string key)
        {
            key = key.Replace("\"", "").Trim();
            
            searchParam _param = (searchParam)HttpRuntime.Cache[key];

            //if (_param == null)
            //    return false;

            //System.Data.DataTable _dt = sqlSearch_forExport__dataset(_param.searchData);
            //if (_dt != null)
            //{
            //    CreateExcelFile.CreateExcelDocument(_dt.Copy(), "Crimson - Batch Search", HttpContext.Current.Response);
            //}

            //return true;

            System.Data.DataSet _ds = sqlSearch_forExport__dataset(_param.searchData);

            if (_ds != null)
            {
                string key_ = System.Guid.NewGuid().ToString();
                HttpRuntime.Cache.Insert(key_, _ds, null, DateTime.UtcNow.Add(new TimeSpan(1, 0, 1)), System.Web.Caching.Cache.NoSlidingExpiration);
                return key_;
            }
            else
            {
                return null;
            }

        }
        #endregion
    }
}
