﻿using System;
using System.ComponentModel.DataAnnotations;
using System.Data;
using System.Linq;
using System.Web.Mvc;
using System.Data.Linq;

namespace cmdiapp.n.core.Areas.crm.Domain.ViewModels
{
    // SQL Function (db.iGetChartAcctSummary)
    public class rChartAcctSummary
    {
        public string FUNDCODE { get; set; }

        public DateTime BEGINDTE { get; set; }

        public DateTime ENDDTE { get; set; }

        public string CATEGORY { get; set; }

        public string ACTTYPE { get; set; }

        public string CHARTACCT { get; set; }

        public decimal TOTAL { get; set; }

        public decimal PROFIT { get; set; }
    }
}