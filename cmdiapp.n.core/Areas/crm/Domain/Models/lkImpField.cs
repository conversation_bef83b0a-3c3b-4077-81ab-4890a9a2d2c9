﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Data;
using System.Linq;
using System.Web.Mvc;
using System.Data.Linq;
using System.ComponentModel.DataAnnotations.Schema;
using cmdiapp.n.core.Domain.ViewModels;
using cmdiapp.n.core.Areas.crm.Domain.Data;

namespace cmdiapp.n.core.Areas.crm.Domain.Models
{
    /* 
    [Table("crm_lkImpField")]
    public class lkIMPFLD : _entityBase_crm, iItemType
    {
        [Column]
        [Key]
        public Int16 IMPFLDID { get; set; }

        [Column]
        public string IMPTYPE { get; set; }

        [Column]
        public string FLDNAME { get; set; }

        [Column]
        public string DISNAME { get; set; }

        [Column]
        public Int16 SEQ { get; set; }

        [Column]
        public string COLUMNTYPE { get; set; }

        [Column]
        public Int16 COLUMNLENGTH { get; set; }

        [Column]
        public int? IMPLISTID { get; set; }
    }
    */

    [Table("lkIMPFIELD")] 
    public class lkImpField : _entityBase_crm, iItemType
    {
        [Column]
        [Key]
        public Int16 IMPFLDID { get; set; }

        [Column]
        public string IMPTYPE { get; set; }

        [Column]
        public string FLDNAME { get; set; }

        [Column]
        public string DISNAME { get; set; }

        [Column]
        public Int16 SEQ { get; set; }

        [Column]
        public string COLUMNTYPE { get; set; }

        [Column]
        public Int16 COLUMNLENGTH { get; set; }

        [Column]
        public int? IMPLISTID { get; set; }

    }
}