﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Data;
using System.Linq;
using System.Web.Mvc;
using System.Data.Linq;

namespace cmdiapp.n.core.Areas.crm.Domain.Models
{
    public class ytdTotal
    {
        public DateTime RUNTIME { get; set; }
        
        public string YEAR_LABEL { get; set; }
        public decimal YTD_TOTAL { get; set; }
        public decimal QTD_TOTAL { get; set; }
        public decimal MTD_TOTAL { get; set; }
        public decimal WTD_TOTAL { get; set; }
        public decimal TOTAL_PLEDGE { get; set; }
        public decimal TOTAL_INHOUSE { get; set; }
        public decimal TOTAL_FULFILLED { get; set; }
        public decimal TOTAL_OUTSTANDING { get; set; }

        public bool UP_FROM_PREVQTR { get; set; }
        public bool UP_FROM_PREVMONTH { get; set; }
        public bool UP_FROM_PREVWEEK { get; set; }
    }
}