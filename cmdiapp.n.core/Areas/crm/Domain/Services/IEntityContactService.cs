﻿using cmdiapp.n.core.Areas.crm.Domain.Models;
using cmdiapp.n.core.Areas.crm.ViewModels;
using cmdiapp.n.core.Domain.ViewModels;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace cmdiapp.n.core.Areas.crm.Domain.Services
{
    public interface IEntityContactService
    {
        List<EntityContact> GetByEntityId(int entityId);

        EntityContact GetById(int id);

        genericResponse Save(EntityContact contact);

        genericResponse Delete(int id);

        List<rEntityContactFlag> GetFlagsByEntityId(int entityId);

        List<rEntityContactFlag> GetFlagsByContactId(int contactId);

        genericResponse AddFlag(jtENTITYCONTFLAG flag);

        genericResponse DeleteFlag(int jtEntityContFlagId);
    }
}
