﻿using System;
using System.Collections.Generic;
using cmdiapp.n.filedrop;
using cmdiapp.n.core.Domain.ViewModels;

namespace cmdiapp.n.core.Areas.crm.Domain.Models
{
    #region [[[  data model  ]]]
    public class tile
    {
        public string id { get; set; }
        public List<int> projectIds { get; set; }
        public int configKeyId { get; set; }
        public int accountId { get; set; }
        public int? callerId { get; set; }
        public string callerKeyName { get; set; }
        public string appKey { get; set; }
        public string title { get; set; }
        public string logoUrl { get; set; }
        public string backgroundColor { get; set; }
        public string jsonSchemaFileName { get; set; }
        public string containerName { get; set; }
        public string storedProcedureName { get; set; }
        public string httpEndPoint { get; set; }
        public string accept { get; set; }
        public int chunkSize { get; set; }
        public fileObj fileObj { get; set; }
    }
    public class fileUploaded: iItemType
    {
        public string fileName { get; set; }
        public string message { get; set; }
        public bool uploaded { get; set; }
    }
    public class batchData: iItemType
    {
        public string date { get; set; }
        public string batchno { get; set; }
        public decimal? amount { get; set; }
        public int nogift { get; set; }

    }
    #endregion
}