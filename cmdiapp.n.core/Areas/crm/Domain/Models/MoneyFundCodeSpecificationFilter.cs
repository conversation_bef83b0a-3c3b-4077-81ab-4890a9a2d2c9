﻿using cmdiapp.n.core.Areas.query.Domain.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace cmdiapp.n.core.Areas.crm.Domain.Models
{
    public class MoneyFundCodeSpecificationFilter : IExternalApiSpecificationFilter
    {
        private const string FundCodeFilterId = "msg_fundcode";
        private const int StringEqualToIndex = 0;

        public string FundCode { get; }

        public MoneyFundCodeSpecificationFilter(string fundCode)
        {
            if (string.IsNullOrEmpty(fundCode))
            {
                throw new ArgumentException($"{nameof(FundCode)} cannot be null or empty.", nameof(fundCode));
            }
            FundCode = fundCode;
        }

        public QueryFilterInstance ConvertToQueryInstanceFilter()
        {
            return new QueryFilterInstance
            {
                key = FundCodeFilterId,
                operatorIdx = StringEqualToIndex,
                values = new string[1] { FundCode }
            };
        }
    }
}