﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Net;
using System.Web;
using System.Web.Hosting;
using System.Web.Http;

using cmdiapp.n.core.Domain.Data;
using cmdiapp.n.core.Domain.Models;
using cmdiapp.n.core.Library;

using cmdiapp.n.core.Core;
using cmdiapp.n.core.Areas.query.Domain.Models;
using System.Net.Http;
using System.Net.Http.Headers;
using System.IO;
using cmdiapp.n.core.Domain.ViewModels;
using System.Dynamic;
using Newtonsoft.Json;
using cmdiapp.n.core._Presentation.Attributes;
using cmdiapp.n.core.Presentation.Controllers;
using System.Threading.Tasks;
using cmdiapp.n.core._Domain.Models;
using cmdiapp.n.core._Domain.ViewModels;

namespace cmdiapp.n.core.Areas.query.Presentation.Controllers
{
    [RoutePrefix("query/api")]
    public class definitionController : ApiController
    {
        #region [[ Old Routines ]]
        private I_entity _entity;

        public definitionController()
        {
            _entity = I_entityManager.getEntity();
        }

        #region [ (Default Action) Get Query Definition Data (/query/api/def?name=) ]
        [HttpGet, Route("def")]
        [Authorize]
        public q_def_C def(string name) // eg. "crm/peopleSearch" for "crm_peopleSearch"
        {
            string cacheKey = session.get_cacheKey_for_appId_projectId_userGroupId("qDef-S-" + name, 1, session.currentDomain_projectId, session.currentDomainAuthorizedProject_userGroupId);
            q_def_S def = (q_def_S)HttpRuntime.Cache[cacheKey];

            #region [ Run Report ]
            if (name.StartsWith("report") && name.Length > 7)
            {
                int reportId = int.Parse(name.Substring(7, name.Length - 7));
                if (reportId > 0)
                {
                    q_def_S _def = q_library.get_q_def_fr_cache_report(reportId);
                    if (_def != null)
                    {
                        //Crimson ID Token available for all clients for reports
                        _def.reportScript = _def.reportScript.Replace("##projectIdForToken##", session.currentDomain_projectId.ToString());
                        q_def_C _def_C = session.get_a_cache(session.get_cacheKey_for_appId_projectId_userGroupId("qDef-C-report-" + reportId.ToString()),
                                                            session.appCacheDuration,
                                                            null,
                                                            q_library.clone_q_def_S,
                                                            _def) as q_def_C;

                        return _def_C;
                    }
                }
                else
                    return new q_def_C();
            }
            #endregion

            #region [ Regular Advanced Search ]
            else
            {
                q_def_S _def = q_library.get_q_def_fr_cache(name);

                if (_def != null)
                {
                    q_def_C _def_C = session.get_a_cache(session.get_cacheKey_for_appId_projectId_userGroupId("qDef-C-" + name),
                                                        session.appCacheDuration,
                                                        null,
                                                        q_library.clone_q_def_S,
                                                        _def) as q_def_C;

                    return _def_C;
                }

            }
            #endregion

            return new q_def_C();
        }
        #endregion


        #region [[[ ############# INCOMPLETE.  In order to work, need to re-write q_library.get_q_def module to handle "User Access & Config" ]]]
        #region [ Cache Query Definition (/query/api/cache_qDef/{name}/{appId}/{projectId}/{userGroupId}) ]
        [HttpGet, Route("query/api/cache_qDef/{name}/{appId}/{projectId}/{userGroupId}")]
        [Authorize]
        public bool cache_qDef(string name, int appid, int projectId, int userGroupId) // eg. [name] "crm_peopleSearch" 
        {               //eg. https://crimson.cmdi.com/query/api/cache_qDef/crm_peopleSearch/1/1/1
            try
            {
                name = name.ToString().Replace('_', '/');
                project _project = _entity.Single<project>(a => a.projectId == projectId && a.active);
                if (_project == null)
                    return false;

                q_def_S _def = q_library.get_q_def_fr_cache(name, _project._connectionString(), appid, projectId, userGroupId);

                if (_def != null)
                {
                    q_def_C _def_C = session.get_a_cache(session.get_cacheKey_for_appId_projectId_userGroupId("qDef-C-" + name, appid, projectId, userGroupId),
                                                        session.appCacheDuration,
                                                        null,
                                                        q_library.clone_q_def_S,
                                                        _def) as q_def_C;

                    return true;
                }

                return false;
            }
            catch
            {
                return false;
            }
        }
        #endregion

        #region [ Cache All Query Definition (/query/api/cache_qDefAll/{appKey}/{appId}) ]
        private class project_n_userGroup_Ids
        {
            public int projectId { get; set; }
            public int userGroupId { get; set; }
        }
        [HttpGet, Route("query/api/cache_qDefAll/{appKey}/{appId}")]
        public bool cache_qDefAll(string appKey, int appId) // eg. [name] "crm_peopleSearch" -> "crm/peopleSearch"
        {   // eg. (Old) https://crimson.cmdi.com/query/api/cache_qDefAll/a9732172-3252-4466-a6ff-85fc4109cef3/1
            // eg. https://crimson.cmdi.com/query/api/cache_qDefAll/01a17cdbd34c6e0cbfeeae4ec68b25d6b7bbb5e6/1

            #region [ Token Validation (Old) ]
            /*
            if (appKey != session.cmdiAppKey)
            {
                util.throw_notFound();
                return false;
            }
            */
            #endregion

            apiCallToken _token = cl_apiCallToken.validateToken(appKey, appId);
            if (_token == null || _token.invalidToken || _token.expired || _token.overLimit || _token.endPoint != "/query/api/cache_qDefAll")
            {
                util.throw_notFound();
                return false;
            }

            try
            {
                string sql = string.Format("SELECT projectId, userGroupId, MAX(lastLogon) AS lastLogon FROM [dbo].[v_projectsAuthorized] WHERE appId = {0} GROUP BY projectId, userGroupId HAVING MAX(lastLogon) IS NOT NULL ORDER BY lastLogon DESC", appId);
                List<project_n_userGroup_Ids> IDs = _entity.getContext().Database.SqlQuery<project_n_userGroup_Ids>(sql).ToList();

                //int i = 0;
                foreach (project_n_userGroup_Ids item in IDs)
                {
                    /*
                    i++;
                    if (i > 1)
                        break;
                    */
                    if (!cache_qDef("crm_peopleSearch", appId, item.projectId, item.userGroupId))
                        return false;
                    if (!cache_qDef("crm_moneySearch", appId, item.projectId, item.userGroupId))
                        return false;
                    if (!cache_qDef("crm_vendorSearch", appId, item.projectId, item.userGroupId))
                        return false;
                    if (!cache_qDef("crm_receiptSearch", appId, item.projectId, item.userGroupId))
                        return false;
                    if (!cache_qDef("crm_disbursmentSearch", appId, item.projectId, item.userGroupId))
                        return false;
                }
                return true;
            }
            catch
            {
                return false;
            }
        }
        #endregion
        #endregion

        #endregion



        #region [ Get Query Definition  ]
        [HttpGet, Route("definition/{name}")]  // eg. /query/api/definition/peopleSearch
        [Authorize]
        [CacheMaxAge(86400)]
        public QueryDef definition(string name)
        {   // eg http://demo1.cmdi2.com/query/api/definition/peopleSearch
            QueryDef def = Query.getDef(name);
            // remove any system exportGroups
            def.exportGroups = def.exportGroups.Where(grp => !grp.isSystem).ToArray();

            //For ToolTip Identifier Info..
            tooltipDef _record = _entity.Single<tooltipDef>(a => a.qDefName.ToUpper().Contains(name.ToUpper()));
            if (_record != null && _record.tooltipIdentifier != null)
                def.tooltipIdentifier = _record.tooltipIdentifier;

            return def;
        }
        #endregion

        #region [ queryLookUp  ]
        [HttpGet, Route("definition/my/searches")]  // eg. /query/api/definition/my/searches
        [Authorize]
        public List<queryLookUp> mySearches()
        {
            List<queryLookUp> result = queryLookUps.FindAll(q => q.quickSearch);
            foreach (queryLookUp q in result)
            {
                if (!string.IsNullOrEmpty(q.configKey))
                {
                    q.haveAccess = (applica.getConfigVal(q.configKey) ?? "") == q.configVal;
                }
                else if (!string.IsNullOrEmpty(q.version))
                {
                    q.haveAccess = q.version.Contains(session.currentDomain_project.projectType.appVersion);
                }
                else
                {
                    q.haveAccess = session.can_i_do(q.aElementIdentifier, "v");
                }
            }
            return result
                .Where(q => q.haveAccess)
                .OrderBy(q => q.caption)
                .ToList();
        }

        private List<queryLookUp> queryLookUps = new List<queryLookUp>() {
                //new queryLookUp() { queryName = "batchSearch", caption = "Batch Search", aElementIdentifier = "cmdiapp.dms.batch_qq", quickSearch = true,haveAccess=false },
                new queryLookUp() { queryName = "bestEffort", caption = "Best Effort", aElementIdentifier = "cmdiapp.dms.BestEfforts", quickSearch = false,haveAccess=false },
                new queryLookUp() { queryName = "bundlerSearch", caption = "Bundler Search", aElementIdentifier = "cmdiapp.dms.bundler_qq", quickSearch = true,haveAccess=false,version="Federal,State" },
                new queryLookUp() { queryName = "bundlerSearch", caption = "Gift Officer Search", aElementIdentifier = "cmdiapp.dms.bundler_qq", quickSearch = true,haveAccess=false,version="Nonprofit" },
                new queryLookUp() { queryName = "memberSearch", caption = "Congress Member Search", aElementIdentifier = "", quickSearch = true, haveAccess = false, configKey="cong-memb-track", configVal = "Y" },
                new queryLookUp() { queryName = "dataentry2Search", caption = "Data Entry Search", aElementIdentifier = "/crm/DataEntry", quickSearch = true,haveAccess=false },
                new queryLookUp() { queryName = "dataentryDESearch", caption = "Data Entry Search (Adv)", aElementIdentifier = "/crm/DataEntryDE", quickSearch = true,haveAccess=false },
                new queryLookUp() { queryName = "disburseSearch", caption = "Expenditure Search", aElementIdentifier = "cmdiapp.dms.entity_qq(Disbursements)", quickSearch = true,haveAccess=false },
                new queryLookUp() { queryName = "eventSearch", caption = "Event Search", aElementIdentifier = "cmdiapp.dms.event", quickSearch = true,haveAccess=false },
                new queryLookUp() { queryName = "invoiceSearch", caption = "Invoice Search", aElementIdentifier = "InvoiceModule", quickSearch = true,haveAccess=false },
                new queryLookUp() { queryName = "moneySearch", caption = "Money Search", aElementIdentifier = "cmdiapp.dms.money_qq", quickSearch = true,haveAccess=false },
                new queryLookUp() { queryName = "mypayrequestSearch", caption = "My Payment Request Search", aElementIdentifier = "/crm/PayRequest/MyPaymentRequests", quickSearch = true,haveAccess=false },
                new queryLookUp() { queryName = "newConduitDistrib", caption = "New Conduit Distribution", aElementIdentifier = "Conduit", quickSearch = false,haveAccess=false },
                new queryLookUp() { queryName = "newDistribution", caption = "New Distribution", aElementIdentifier = "cmdiapp.dms.NewDistribution", quickSearch = false,haveAccess=false },
                new queryLookUp() { queryName = "payrequestSearch", caption = "Payment Request Search", aElementIdentifier = "/crm/PayRequest/PaymentRequests", quickSearch = true,haveAccess=false },
                new queryLookUp() { queryName = "peopleSearch", caption = "People Search", aElementIdentifier = "cmdiapp.dms.people_qq", quickSearch = true,haveAccess=false },
                new queryLookUp() { queryName = "receiptSearch", caption = "Receipt Search", aElementIdentifier = "cmdiapp.dms.entity_qq(Receipts)", quickSearch = true,haveAccess=false },
                new queryLookUp() { queryName = "thankYou", caption = "Thank You", aElementIdentifier = "cmdiapp.dms.ThankYou", quickSearch = false,haveAccess=false },
                new queryLookUp() { queryName = "vendorSearch", caption = "Payee Search", aElementIdentifier = "cmdiapp.dms.entity_qq(Vendors)", quickSearch = true,haveAccess=false },
                new queryLookUp() { queryName = "sourceSearch", caption = "Source Code Search", aElementIdentifier = "cmdiapp.dms.sources", quickSearch = true,haveAccess=false },
                new queryLookUp() { queryName = "initiativeSearch", caption = "Initiative Search", aElementIdentifier = "cmdiapp.dms.packages", quickSearch = true,haveAccess=false },
                new queryLookUp() { queryName = "batchEntry", caption = "Batch Entry", aElementIdentifier = "/crm/BatchEntry", quickSearch = true,haveAccess=false },
                new queryLookUp() { queryName = "pledgeSearch", caption = "Pledge Search", aElementIdentifier = "cmdiapp.dms.People_Pledge", quickSearch = true, haveAccess=false },
                new queryLookUp() { queryName = "moveSearch", caption = "Move Management Search", aElementIdentifier = "Move Management", quickSearch = true,haveAccess=false },
                new queryLookUp() { queryName = "adjustmentSearch", caption = "Adjustment Search", aElementIdentifier = "cmdiapp.dms.money_qq", quickSearch = false,haveAccess=false },

            };

        #endregion

        #region [ Run Query ]
#if DEBUG
        [HttpGet, Route("test-run/{testFileName}")]  // eg. /query/api/run
        [Authorize]
        public QueryResult testRun(string testFileName)
        {   // eg. http://demo1.cmdi2.com/query/api/test-run/QueryInstanceInfo-Sample1/
            return Query.run(new QueryInstanceInfo(testFileName), new QueryRuntimeParams() { resultType = QueryResultType.allDataset, sort = new QueryRuntimeSort[] { new QueryRuntimeSort() { field = "LGIFTDTE", dir = "desc" } } });
        }
#endif

        [HttpPost, Route("run")]  // eg. /query/api/run
        [Authorize]
        public QueryResult run(QueryRunViewParams runViewParams)
        {
            QueryRuntimeParams runtimeParams = new QueryRuntimeParams
            {
                resultType = QueryResultType.pagedView,
                page = runViewParams.page,
                pageSize = runViewParams.pageSize,
                sort = runViewParams.sort
            };
            return Query.run(runViewParams.qInstance, runtimeParams);
        }

        [HttpPost, Route("RunViewAggregate")]
        [Authorize]
        public QueryResult RunViewAggregate(QueryInstanceInfo queryInstance)
        {
            QueryRuntimeParams runtimeParams = new QueryRuntimeParams()
            {
                resultType = QueryResultType.viewAggregate
            };
            return Query.run(queryInstance, runtimeParams);
        }

        [HttpPost, Route("RunAggregate")]
        [Authorize]
        public QueryResult RunAggregate(QueryRunAggregateParams aggregateParams)
        {
            var runtimeParams = new QueryRuntimeParams()
            {
                resultType = QueryResultType.pagedAggregation,
                aggregatePaging = aggregateParams?.paging
            };
            return Query.run(aggregateParams?.qInstance, runtimeParams);
        }
        #endregion

        #region [ Save, Open Searches ]
        [HttpPost, Route("Save")]
        [Authorize]
        public QueryInstanceInfo save(QueryInstanceInfo qInstance)
        {
            return Query.save(qInstance);
        }

        [HttpGet, Route("CheckIfUniqueName")]
        [Authorize]
        public bool CheckIfUniqueName (string name, string key="")
        {   // eg. http://demo1.cmdi2.com/query/api/CheckIfUniqueName?name=Statinlistof(VA,MD,DC)&key=6d9a9d0f-9558-4ef0-a2f6-8bb14f39dc6d
            return QueryManager.checkIfUniqueName(name, key);
        }

        [HttpGet, Route("Open/{key}")]
        [Authorize]
        public QueryInstanceInfo Open(string key)
        {   // eg. http://demo1.cmdi2.com/query/api/Saved/6d9a9d0f-9558-4ef0-a2f6-8bb14f39dc6d

            List<QueryInstanceInfo> queries = Query.mySaved("", key, 1);

            return (queries != null && queries.Count > 0 ? queries.FirstOrDefault() : null);
        }

        [HttpGet, Route("Delete/{key}")]
        [Authorize]
        public bool Delete(string key)
        {   // eg. http://demo1.cmdi2.com/query/api/Delete/6d9a9d0f-9558-4ef0-a2f6-8bb14f39dc6d

            return QueryManager.deleteSearch(key);
        }

        [HttpGet, Route("MySaved")]
        [Authorize]
        public List<QueryInstanceInfo> MyQueries(string defKey = "", int? noSearches = -1)
        {   // eg. http://demo1.cmdi2.com/query/api/MySearches/peopleSearch

            return Query.mySaved(defKey, "", noSearches.Value);
        }

        [HttpGet, Route("MyRecent/{defKey}/{noSearches:int?}")]
        [Authorize]
        public List<QueryInstanceInfo> MyRecent(string defKey, int? noSearches = 10)
        {   // eg. http://demo1.cmdi2.com/query/api/MyRecent/peopleSearch/5

            return Query.myRecent(defKey, noSearches.Value);
        }
        #endregion

        #region [ Export ]
        // POST query/api/export
        [HttpPost, Route("export")]
        [Authorize]
        [NotLaunchpadApiAuthorize]
        public object export(QueryInstanceInfo qInstance)
        {

            // run query
            DataTable result = Query.runToDataTable(qInstance, new QueryRuntimeParams { resultType = QueryResultType.allDatasetExport });
            int _maxRowsToExport = Query.maxRowsToExport();
            if (qInstance.defKey == "newDistribution")
            {
                _maxRowsToExport *= 2;
            }
            if (result.Rows.Count > _maxRowsToExport)
            {
                result = result.Rows.Cast<DataRow>().Take(_maxRowsToExport).CopyToDataTable();
            }

            // get export groupKey
            /*
             * We only need the first groupKey because:
                only one template can be selected and a template cannot be selected with
                custom output options.  Therefore, we can assume that either 
                    1. one template has been selected or
                    2. one or more custom output options have been selected
                in case 1, we just need the one template group export format and
                in case 2, all custom group export formats are null, so we will use default
            */
            string exportGrpKey = qInstance.exportOptions.FirstOrDefault().groupKey;
            // get export format from qdef
            QueryDef qDef = new QueryDef(qInstance.defKey);
            string exportFormat = qDef.exportGroups
                .SingleOrDefault(grp => grp.key == exportGrpKey).exportFormat 
                ?? qDef.defaultExportFormat;
            // use factory to generate correct QExportFormat class
            IQExportFormat qExport;
            try
            {
                qExport = new QExportFormatFactory().GetQExportFormat(exportFormat);
            }
            catch (ArgumentException ex)
            {
                qExport = new QExportFormatFactory().GetQExportFormat();
            }
            // call QExportFormat's ToFile method, which saves file and returns file names
            Tuple<string, string> fileNames = qExport.ToFile(result, qDef.label);
            // send file names back to client
            return new { serverFileName = fileNames.Item1, clientFileName = fileNames.Item2 };
        }

        [HttpPost, Route("ExportAggregate")]
        [Authorize]
        [NotLaunchpadApiAuthorize]
        public object ExportAggregate(QueryInstanceInfo instance)
        {
            DataTable result = Query.runToDataTable(
                instance,
                new QueryRuntimeParams { resultType = QueryResultType.aggregationExport });

            var qExport = new QExportFormatFactory().GetQExportFormat();
            var fileNames = qExport.ToFile(result, $"SearchAggregates");

            return new { serverFileName = fileNames.Item1, clientFileName = fileNames.Item2 };
        }

        [HttpGet, Route("getFile/{serverFileName}/{clientFileName}")]
        [Authorize]
        public IHttpActionResult getFile(string serverFileName, string clientFileName)
        {
            string _serverFileName = serverFileName.Replace("/", "").Replace("\\", "");
            string path = HostingEnvironment.MapPath($"~/tmp/{_serverFileName}");            
            
            return new FileDownloadResult(path, clientFileName);
        }
        #endregion

        [HttpGet, Route("getUserQueryConfig/{queryName}")]
        [Authorize]
        public UserQueryConfig getColumnSequenceConfig(string queryName)
        {
            string configVal = session.userSession.configItems.SingleOrDefault(config =>
                config.Key == Query.userConfigKey
                && config.projectId == session.currentDomain_projectId)?.Val ?? "";
            if (string.IsNullOrEmpty(configVal))
            {
                return new UserQueryConfig(queryName);
            }

            UserQueryConfig[] queryConfigs = JsonConvert.DeserializeObject<UserQueryConfig[]>(configVal);
            UserQueryConfig queryConfig = queryConfigs
                .SingleOrDefault(qc => qc.QueryName == queryName) ?? new UserQueryConfig(queryName);
            return queryConfig;
        }

        [HttpPost, Route("saveUserQueryConfig")]
        [Authorize]
        public genericResponse saveColumnSequenceConfig([FromBody] UserQueryConfig config)
        {
            try
            {
                string configVal = session.userSession.configItems.SingleOrDefault(configItem =>
                    configItem.Key == Query.userConfigKey
                    && configItem.projectId == session.currentDomain_projectId)?.Val ?? "";
                List<UserQueryConfig> queryConfigs;
                if (string.IsNullOrEmpty(configVal))
                {
                    queryConfigs = new List<UserQueryConfig>()
                    {
                        config
                    };
                }
                else
                {
                    queryConfigs = JsonConvert.DeserializeObject<List<UserQueryConfig>>(configVal);
                    UserQueryConfig existingConfig = queryConfigs
                        .SingleOrDefault(qc => qc.QueryName == config.QueryName);
                    if (existingConfig == null)
                    {
                        queryConfigs.Add(config);
                    }
                    else
                    {
                        existingConfig.ColumnSequence = config.ColumnSequence;
                        existingConfig.HiddenColumns = config.HiddenColumns;
                    }
                }

                session.upsert_ConfigVal(Query.userConfigKey, JsonConvert.SerializeObject(queryConfigs), true);

                return new genericResponse
                {
                    success = true
                };

            }
            catch (Exception ex)
            {
                return new genericResponse
                {
                    success = false,
                    message = $"Unable to save column preferences. An error occurred."
                };
            }
        }

        [HttpGet, Route("SavedSearches")]
        [Authorize]
        public IEnumerable<SavedQueryDisplay> GetSavedSearches(string defKey = "", int? noSearches = 0)
        {
            int projectId = session.currentDomain_projectId;
            string userId = session.userSession.UserId;

            return (from query in _entity.All<SavedQuery>()
                    join emailReport in _entity.All<EmailReport>()
                        on query.Id equals emailReport.CompilationSourceId into emailReports
                    from emailReport in emailReports.DefaultIfEmpty()
                    join sourceType in _entity.All<EmailReportCompilationSourceType>()
                        on emailReport.CompilationSourceTypeId equals sourceType.Id into sourceTypes
                    from sourceType in sourceTypes.DefaultIfEmpty()                         
                    where query.ProjectId == projectId
                        && query.UserId == userId
                        && (defKey == "" || defKey == null || query.DefinitionKey == defKey)
                        && (sourceType == null || sourceType.Code == EmailReportCompilationSourceTypeCode.Search)
                    orderby query.LastRunOn descending
                    select new
                    {
                        query.Id,
                        EmailReport = emailReport == null ? null : new EmailReportDisplay
                        {
                            Id = emailReport.Id,
                            CompilationSourceTypeCode = sourceType == null ? null : sourceType.Code,
                            CompilationSourceId = emailReport.CompilationSourceId,
                            Name = query.Name,
                            StartAt = emailReport.StartAt,
                            IntervalInHours = emailReport.IntervalInHours,
                            CreatedAt = emailReport.CreatedAt,
                            LastModifiedAt = emailReport.LastModifiedAt,
                            LastStartedAt = emailReport.LastStartedAt,
                            LastCompletedAt = emailReport.LastCompletedAt
                        },
                        query.QueryInstanceJson
                    })
                    .Take(noSearches < 1 || noSearches == null ? int.MaxValue : (noSearches ?? 0))
                    .ToList()
                    .Select(serialized => new SavedQueryDisplay
                    {
                        Id = serialized.Id,
                        EmailReport = serialized.EmailReport,
                        QueryInstance = JsonConvert.DeserializeObject<QueryInstanceInfo>(serialized.QueryInstanceJson)
                    });
        }
    }
}