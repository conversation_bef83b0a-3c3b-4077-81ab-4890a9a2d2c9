﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Data;
using System.Web.Mvc;
using cmdiapp.n.core.Domain.Models;
using cmdiapp.n.core.Areas.crm.Domain.Models;

namespace cmdiapp.n.core.Areas.crm.Core
{
    public class ImportInitiativeViewModel
    {
        public IList<SelectListImpStep2> IntTypefields { get; set; }

        public ImportInitiativeViewModel()
        {
        }

        public ImportInitiativeViewModel(IQueryable<PACKAGE_ext> IntTypes)
        {
            //List of Initiatives - Packages
            IntTypefields = new List<SelectListImpStep2>();
            foreach (PACKAGE_ext f in IntTypes)
            {
                SelectListImpStep2 s = new SelectListImpStep2();
                s.ValueInt = f.PKGEID;
                s.Text = f.PKGECODE.Trim()  + " - " + f.PKGEDESC.Trim();
                IntTypefields.Add(s);
            }
        }
    }
    
    public class SelectListImpStep2
    {
        public string Value { get; set; }
        public string Text { get; set; }
        public int ValueInt { get; set; }
    }
}