﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Net;
using System.Web.Http;

using Ninject;
using Ninject.Web.Mvc;

using cmdiapp.n.core.Areas.dataService.Domain.Models;
using cmdiapp.n.core.Areas.dataService.Domain.Data;
using cmdiapp.n.core.Areas.dataService.Domain.Services;

namespace cmdiapp.n.core.Areas.dataService.Presentation.Controllers.api
{

    [Authorize]
    public class AddressSvcController : ApiController
    {
        private I_entity_ds _entity;
        private IdataService _service;

        public AddressSvcController()
        {
            _entity = I_entityManager_ds.getEntity();
            _service = I_entityManager_ds.getService();
        }

        [HttpPost, Route("dataService/api/AddressSvc/CASS")]
        public cassAddress CASS(cassAddress _addr)
        {
            /*
            var cassTest = jsonDataCaller.syncCall("/dataService/api/AddressSvc/CASS", {ADDR1:'7704 leesburg pike', CITY:'Falls Church', STATE:'VA'}, 'POST');
            var result = JSON.stringify(cassTest)
            alert(result);              
             */
            return _service.verifyAddress(_addr);
        }

        [HttpGet, Route("dataService/api/AddressSvc/rectangularArea_from_centerOf/{loc_latitude}/{loc_longitude}/{distance_in_miles}")]
        public rectangularArea get_rectangularArea_from_centerOf(decimal? loc_latitude, decimal? loc_longitude, int? distance_in_miles)
        {
            if (loc_latitude == null || loc_longitude == null || distance_in_miles == null)
                return new rectangularArea();
            else
                return _service.get_rectangularArea(loc_latitude.Value, loc_longitude.Value, distance_in_miles.Value);
        }

    }
}
