﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Data;
using System.Web.Mvc;
using cmdiapp.n.core.Domain.Models;
using cmdiapp.n.core.Areas.crm.Domain.Models;
 
namespace cmdiapp.n.core.Areas.crm.Core
{
    public class ColumnMapViewModel
    {
        public IList<ColumnMap> ColumnMaps { get; set; }
        public IList<SelectListItemImp> Impfields { get; set; }
        public IList<SelectListItemImp> MiscImpfields { get; set; }
        public IList<SelectListItemImp> MappingPreffields { get; set; }

        public ColumnMapViewModel(DataTable dt, string impType, IQueryable<lkImpField> lkimpfld, IQueryable<lkImpField> misclkimpfld, IQueryable<webIMPPREF> mappingPreffield)
        {
            //Fields based on Import Type
            Impfields = new List<SelectListItemImp>();
            Impfields.Add(new SelectListItemImp() { Value = "", Text = "" });
            foreach (lkImpField f in lkimpfld)
            {
                SelectListItemImp s = new SelectListItemImp();
                s.Value = f.FLDNAME;
                s.Text = f.DISNAME;
                Impfields.Add(s);
            }
            //Misc Fields
            MiscImpfields = new List<SelectListItemImp>();
            MiscImpfields.Add(new SelectListItemImp() { Value = "", Text = "" });
            foreach (lkImpField f in misclkimpfld)
            {
                SelectListItemImp s = new SelectListItemImp();
                s.Value = f.FLDNAME;
                s.Text = f.DISNAME;
                MiscImpfields.Add(s);
            }
            //Columns in the File
            ColumnMaps = new List<ColumnMap>();
            foreach (DataColumn dc in dt.Columns)
            {
                ColumnMap map = new ColumnMap();
                map.FileColumn = dc.ColumnName;
                map.SqlColumn = ((lkimpfld.FirstOrDefault(f => f.FLDNAME == dc.ColumnName) == null) ? "" : dc.ColumnName);
                ColumnMaps.Add(map);
            }
            //For Mapping Web Pref
            MappingPreffields = new List<SelectListItemImp>();
            foreach (webIMPPREF f in mappingPreffield)
            {
                SelectListItemImp s = new SelectListItemImp();
                s.Value = f.IMPTYPE.ToString();
                s.Text = f.PREFNAME;
                MappingPreffields.Add(s);
            }
        }
    }

    public class ColumnMap
    {
        public string FileColumn { get; set; }
        public string SqlColumn { get; set; }
    }

    public class SelectListItemImp
    {
        public string Value { get; set; }
        public string Text { get; set; }
    }

    public class MappingRow
    {
        public string FILECOLUMNNAME { get; set; }
        public string MAPPEDCOLUMNNAME { get; set; }
    }
    public class MappingData
    {
        public int IMPTYPEID;
        public string PREFNAME;
        public List<MappingRow> MAPPING;
    }

    public class ImportTypeData
    {
        public string UPLOADEDFILENAME;

        public int IMPTYPEID; //Import Type
        public int FUNDID; //Fund Code        
        public string FUNDIDS;//for jfc vetting, allows multiple fundid
        //New Gift
        public string BATCHNO;
        public DateTime BATCHDATE;
        public int NOOFGIFTS;
        public decimal TOTALAMOUNT;

        //New Contact
        public int PEOCODEID;

        //Interactions New Non-Donor
        //Interactions Append to existing records (ID is required)
        public DateTime INTERACTIONDATE;
        public int CHANNELID;
        public string SOURCECD;

        //Event
        public string EVENTCD;

        //Append to existing records (ID is required)
        public bool ISOVERWRITE;

        //Attribution
        public int ORIGMID;

        //Expenditure
        public int TXNTYPEID;
        public int FECTXNTYPEID;
        public string LINE;
        public int CENTERID;
        public string CENTERCODE;
        public string FUNDCODE;
        public string TXNTYPE;

        //IMPORT Source Code
        public int PKGEID;
        public string PKGECODE;
        public string PKGEDESC;
        public bool OVERWRITESRCCODE;
        public bool OVERWRITESRCCODE2;

        //mass adjust
        public int ADJTYPEID;

        //Mapping
        public List<MappingRow> MAPPING;

        //Mapping for Telecom Fields
        public List<MappingRow> MappingRows;

        //ImportTableName
        public string IMPORTTABLENAME;

        //Worksheet
        public int WORKSHEET;
        public bool? DONTOTOPTION;

        //Queue start time
        public DateTime? QUEUESTART;

        //FOR New Gift By Date And Source Code
        public string MonyType;
    }


}