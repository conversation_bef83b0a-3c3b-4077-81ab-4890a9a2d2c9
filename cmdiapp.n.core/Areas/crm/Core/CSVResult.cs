﻿using System;
using System.Web.Mvc;
using System.Data;
using System.Text;

namespace cmdiapp.n.core.Areas.crm.Core
{
    public class CSVResult : ActionResult
    {
        public string fileName { get; set; }
        public string filePath { get; set; }
        public string sqlStatement { get; set; }
        public string sheetName { get; set; }
        public string connectionSring { get; set; }
        public string clientsidefileName { get; set; }
        public bool includeHeader { get; set; }
        public string customize { get; set; }
        public DataSet rds { get; set; }

        public override void ExecuteResult(ControllerContext context)
        {

            DataSet dset = new DataSet();
            if (string.IsNullOrEmpty(sqlStatement) && rds != null)
            {
                dset = rds;
            }

            DataTable reportsTable = dset.Tables[0];
            string csvResp = "";
            if (customize == "NY") csvResp = ToCSV_NYFile(reportsTable, ",", includeHeader);
            else csvResp = ToCSVFile(reportsTable, ",", includeHeader);
            //get bytes
            var bytes = Encoding.GetEncoding("iso-8859-1").GetBytes(csvResp);
            //flush it out
            System.Web.HttpContext.Current.Response.Buffer = true;
            System.Web.HttpContext.Current.Response.Clear();
            System.Web.HttpContext.Current.Response.ContentType = "text/csv";
            System.Web.HttpContext.Current.Response.AddHeader("content-disposition", "attachment;  filename=" + clientsidefileName);
            System.Web.HttpContext.Current.Response.BinaryWrite(bytes);
            System.Web.HttpContext.Current.Response.End();

        }

        public string ToCSVFile(DataTable table, string delimiter, bool includeHeader)
        {
            var result = new StringBuilder();

            if (includeHeader)
            {
                foreach (DataColumn column in table.Columns)
                {
                    result.Append("\"" + column.ColumnName + "\"");
                    result.Append(delimiter);
                }

                result.Remove(--result.Length, 0);
                result.Append(Environment.NewLine);
            }

            foreach (DataRow row in table.Rows)
            {
                foreach (object item in row.ItemArray)
                {
                    if (item is DBNull)
                        result.Append(delimiter);
                    else
                    {
                        string itemAsString = item.ToString().Trim();

                        itemAsString = System.Text.RegularExpressions.Regex.Replace(itemAsString, @"(\r\n)|\n|\r", "");

                        #region [[ Formating ]]
                        //Take care of DateFormat
                        //DateTime dDate;
                        //if (DateTime.TryParse(itemAsString, out dDate))
                        //{
                        //    itemAsString = String.Format("{0:MM/dd/yyyy}", dDate);
                        //}
                        //Int32 dInt;
                        //if (Int32.TryParse(itemAsString, out dInt))
                        //{
                        //    itemAsString = dInt.ToString("#,##0");
                        //}

                        //Decimal dDecm;
                        //if (Decimal.TryParse(itemAsString, out dDecm))
                        //{
                        //    itemAsString = string.Format("{0:C}", dDecm);
                        //}
                        #endregion

                        // Double up all embedded double quotes
                        itemAsString = itemAsString.Replace("\"", "\"\"");

                        // To keep things simple, always delimit with double-quotes
                        // so we don't have to determine in which cases they're necessary
                        // and which cases they're not.
                        itemAsString = "\"" + itemAsString + "\"";

                        result.Append(itemAsString + delimiter);
                    }
                }

                result.Remove(--result.Length, 0);
                result.Append(Environment.NewLine);
            }

            return result.ToString();
        }

        public string ToCSV_NYFile(DataTable table, string delimiter, bool includeHeader)
        {
            var result = new StringBuilder();

            if (includeHeader)
            {
                foreach (DataColumn column in table.Columns)
                {
                    result.Append(column.ColumnName.Replace("\"", ""));
                    result.Append(delimiter);
                }

                result.Remove(--result.Length, 0);
                result.Append(Environment.NewLine);
            }

            foreach (DataRow row in table.Rows)
            {
                foreach (object item in row.ItemArray)
                {
                    if (item is DBNull)
                        result.Append(delimiter);
                    else
                    {
                        string itemAsString = item.ToString().Trim();

                        itemAsString = System.Text.RegularExpressions.Regex.Replace(itemAsString, @"(\r\n)|\n|\r", "");

                        #region [[ Formating ]]
                        //Take care of DateFormat
                        //DateTime dDate;
                        //if (DateTime.TryParse(itemAsString, out dDate))
                        //{
                        //    itemAsString = String.Format("{0:MM/dd/yyyy}", dDate);
                        //}
                        //Int32 dInt;
                        //if (Int32.TryParse(itemAsString, out dInt))
                        //{
                        //    itemAsString = dInt.ToString("#,##0");
                        //}

                        //Decimal dDecm;
                        //if (Decimal.TryParse(itemAsString, out dDecm))
                        //{
                        //    itemAsString = string.Format("{0:C}", dDecm);
                        //}
                        #endregion

                        // remove all embedded double quotes
                        itemAsString = itemAsString.Replace("\"", "");

                        result.Append(itemAsString + delimiter);
                    }
                }
                ////remove all ending ","
                ////while (result[result.Length-1] == ',') result.Remove(--result.Length, 0);
                result.Remove(--result.Length, 0);
                result.Append(Environment.NewLine);
            }

            return result.ToString();
        }
    }
}