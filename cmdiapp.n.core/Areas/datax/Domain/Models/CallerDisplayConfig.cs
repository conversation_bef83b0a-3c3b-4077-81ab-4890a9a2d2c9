﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.Serialization;
using System.Web;

namespace cmdiapp.n.core.Areas.datax.Domain.Models
{
    /// <summary>
    /// A deserialized class of <see cref="Caller.displayConfigJson"/>.
    ///  Defines configuration for displaying caller.
    /// </summary>
    [DataContract]
    public class CallerDisplayConfig
    {
        [DataMember(Name = "name")]
        public string name { get; set; }

        [DataMember(Name = "helpUrl")]
        public string helpUrl { get; set; }

        [DataMember(Name = "faviconSrc")]
        public string faviconSrc { get; set; }

        [DataMember(Name = "userNameLabel")]
        public string userNameLabel { get; set; }

        [DataMember(Name = "passKeyLabel")]
        public string passKeyLabel { get; set; }

        [DataMember(Name = "color")]
        public string color { get; set; }

        [DataMember(Name = "authType")]
        public string authType { get; set; }

        [DataMember(Name = "accountFormDeviations")]
        public AccountFormDeviation[] accountFormDeviations { get; set; }
    }
}