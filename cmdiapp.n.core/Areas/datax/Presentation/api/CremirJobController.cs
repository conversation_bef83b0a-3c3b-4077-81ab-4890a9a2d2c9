﻿using cmdiapp.n.core.Areas.datax.Domain.Models;
using cmdiapp.n.core.Areas.datax.Domain.Services;
using cmdiapp.n.core.Areas.query.Domain.Models;
using cmdiapp.n.core.Core.App_Start;
using cmdiapp.n.core.Domain.ViewModels;
using cmdiapp.n.core.Presentation.Controllers;
using Ninject;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Web;
using System.Web.Http;

namespace cmdiapp.n.core.Areas.datax.Presentation.api
{
    [apiAuthorize]
    [RoutePrefix("datax/api/CremirJob")]
    public class CremirJobController : ApiController
    {
        private readonly ICremirJobService _service;

        public CremirJobController()
        {
            _service = NinjectMVC.kernel.Get<ICremirJobService>();
        }

        [Route("ByAccount/{accountId}")]
        [HttpGet]
        public Task<genericResponse> GetJobsByAccountAsync(
            int accountId,
            int? page = 1,
            int? pageSize = 10,
            string listId = "",
            string jobTypeCode = "",
            string jobStatusCode = "")
        {
            string sortField = HttpContext.Current.Request.QueryString["sort[0][field]"];
            string sortDir = HttpContext.Current.Request.QueryString["sort[0][dir]"];
            QueryRuntimeSort sort = new QueryRuntimeSort { dir = sortDir, field = sortField };
            return _service.GetJobsByAccountAsync(
                accountId,
                page ?? 1,
                pageSize ?? 10,
                sort,
                listId ?? "",
                jobTypeCode ?? "",
                jobStatusCode ?? "");
        }

        [Route("Logs/{jobId}")]
        [HttpGet]
        public Task<List<CremirJobLog>> GetJobLogsAsync(int jobId)
        {
            return _service.GetJobLogsAsync(jobId);
        }
    }
}