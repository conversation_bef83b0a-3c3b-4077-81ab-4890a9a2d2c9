﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Data;
using System.Linq;
using System.Web.Mvc;
using System.Data.Linq;
using System.ComponentModel.DataAnnotations.Schema;
using cmdiapp.n.core.Areas.crm.Domain.Data;
using cmdiapp.n.core.Domain.ViewModels;

namespace cmdiapp.n.core.Areas.crm.Domain.Models
{
    [Table("ssSystem")]
    public class ssSystem : _entityBase_crm
    {
        [Column]
        [Key]
        public string ClientCode { get; set; }

        [Column]
        public string ClientName { get; set; }

        [Column]
        public string rs_url { get; set; }

        [Column]
        public string rs_auth_path { get; set; }

        [Column]
        public string rs_folder { get; set; }

        [Column]
        public string rs_report { get; set; }

        [Column]
        public decimal? annualSpendingBudget { get; set; }

    }

    [Table("zLoadLog")]
    public class zLoadLog : _entityBase_crm, iItemType
    {
        [Column]
        [Key]
        public int Id { get; set; }

        [Column]
        public string loadSource { get; set; }
        [Column]
        public int? loadId { get; set; }
        [Column]
        public string loadName { get; set; }
        [Column]
        public int? noRecords { get; set; }
        [Column]
        public decimal? totalAmount { get; set; }
        [Column]
        public int? noLoaded { get; set; }
        [Column]
        public int? noUnloaded { get; set; }
        [Column]
        public int? status { get; set; }
        [Column]
        public int? noNotified { get; set; }
        [Column]
        public string notiEmailTo { get; set; }
        [Column]
        public DateTime? queuedAt { get; set; }
        [Column]
        public DateTime? startedAt { get; set; }
        [Column]
        public DateTime? finishedAt { get; set; }
        [Column]
        public DateTime? updatedAt { get; set; }
    }

    public class zLoadLog_ext
    {
        public int Id { get; set; }

        public string loadSource { get; set; }
        public int? loadId { get; set; }
        public string loadName { get; set; }
        public int? noRecords { get; set; }
        public decimal? totalAmount { get; set; }
        public int? noLoaded { get; set; }
        public int? noUnloaded { get; set; }
        public int? status { get; set; }
        public int? noNotified { get; set; }
        public string notiEmailTo { get; set; }
        public DateTime? queuedAt { get; set; }
        public DateTime? startedAt { get; set; }
        public DateTime? finishedAt { get; set; }
        public DateTime? updatedAt { get; set; }
        public int count_ { get; set; }
        public Int64 rowNo { get; set; }
    }
}