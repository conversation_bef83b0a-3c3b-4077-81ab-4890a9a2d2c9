﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Data;
using System.Linq;
using System.Web.Mvc;
using System.Data.Linq;
using System.ComponentModel.DataAnnotations.Schema;

namespace cmdiapp.n.core.Areas.crm.Domain.Models
{
    public class v_mony_1
    {
	    [Column] 
        [Key]
	    public int MID { get; set; }

        [Column]
        public int PID { get; set; }
    
        [Column] 
        public DateTime BATCHDTE { get; set; }
        public string BATCHDTEs
        {
            get
            {
                return string.Format("{0:MM/dd/yyyy}", BATCHDTE);
            }
        }

        [Column] 
        public string BATCHNO { get; set; } 

        [Column] 
        public decimal? AMT { get; set; }
        public string AMTs { get { return string.Format("{0:c2}", AMT); } }

        [Column] 
        public string FUNDCODE { get; set; } 

        [Column]
        public string FUNDDESC { get; set; }  

        [Column]
        public string SRCECODE { get; set; } 

        [Column]
        public string SRCEDESC { get; set; } 

        [Column]
        public string PKGECODE { get; set; }  

        [Column]
        public string PKGEDESC { get; set; }   

        [Column]
        public string PROGTYPE { get; set; }

        [Column] 
        public string PROGRAM { get; set; }   

        [Column]
        public string MONYTYPE { get; set; }   

        [Column]
        public string MONYTYPEDESC { get; set; }   

    }
}
