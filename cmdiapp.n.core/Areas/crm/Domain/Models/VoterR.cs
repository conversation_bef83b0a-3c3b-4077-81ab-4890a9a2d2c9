﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Data;
using System.Linq;
using System.Web.Mvc;
using System.Data.Linq;
using System.ComponentModel.DataAnnotations.Schema;

using cmdiapp.n.core.Domain.ViewModels;
using cmdiapp.n.core.Areas.crm.Domain.Data;

namespace cmdiapp.n.core.Areas.crm.Domain.Models
{
    public class VoterR : _entityBase_crm, iItemType
    {
        [Key]
        public Int64 voterSeq { get; set; }

        public string voterStateId { get; set; }
        
        public string FNAME { get; set; }
        public string MNAME { get; set; }
        public string LNAME { get; set; }
        public string SUFFIX { get; set; }

        public string ADDR1 { get; set; }
        public string ADDR2 { get; set; }
        public string CITY { get; set; }
        public string STATE { get; set; }
        public string ZIP { get; set; }
        public string County { get; set; }
        public string Precinct { get; set; }

        public string TELEPHONE { get; set; }
        public byte? TELEPHONEConfidenceCode { get; set; }
        public bool? TELEPHONECellFlag { get; set; }

        public string Gender { get; set; }
        public string Gender_ { get { return (Gender == "M" ? "Male" : (Gender == "F" ? "Female" : "")); } }

        public DateTime? DOB { get; set; }
        public string DOB_ { get { return string.Format("(born {0:MM/dd/yyyy})", DOB); } }

        public int? Age { get; set; }
        public string Age_ { get { return (Age != null && Age.Value > 0 ? "Age " + Age.Value.ToString() : ""); } }

        public DateTime? RegistrationDate { get; set; }

        public string Party { get; set; }

        public int? VoteFrequency { get; set; }

        public string MaritalStatus { get; set; }

        public string Ethnic_Description { get; set; }

        public string Religion { get; set; }

        public string DwellingType { get; set; }

        public string EstimatedIncome { get; set; }

        public string Education { get; set; }

        public string OccupationGroup { get; set; }

        public string Occupation { get; set; }

        public byte? HHCount { get; set; }
        
        public string HHGender { get; set; }

        public string HHParties { get; set; }

        public string PresenceOfChildren { get; set; }
    }

    public class VoterCounterData : _entityBase_crm, iItemType
    {
        public string CounterInfo { get; set; }

        public int CounterSeq { get; set; }

        public string Category { get; set; }

    }

    public class VoterCounterBank : _entityBase_crm, iItemType
    {
        public List<VoterCounterData> mainCollection { get; set; }

        public List<VoterCounterData> allCollection { get; set; }
    }


    [NotMapped]
    public class VoterR_ext1 : VoterR
    {
        public int count_ { get; set; }

        public Int64 rowNo { get; set; }
    }

}
