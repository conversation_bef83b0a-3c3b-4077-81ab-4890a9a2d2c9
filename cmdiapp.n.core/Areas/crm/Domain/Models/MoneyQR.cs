﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Data;
using System.Linq;
using System.Web.Mvc;
using System.Data.Linq;
using System.ComponentModel.DataAnnotations.Schema;

using cmdiapp.n.core.Domain.ViewModels;
using cmdiapp.n.core.Areas.crm.Domain.Data;

namespace cmdiapp.n.core.Areas.crm.Domain.Models
{
    public class MoneyQR : _entityBase_crm, iItemType
    {
        public int MID { get; set; }

        public int PID { get; set; }

        public string RECORD_TYPE { get; set; }

        public string PREFIX { get; set; }
         
        public string FNAME { get; set; }  
         
        public string MNAME { get; set; }   
         
        public string LNAME { get; set; }  
        
        public string SUFFIX { get; set; }

        public string EMPLOYER { get; set; }

        public string OCCUPATION { get; set; }

        public string BATCHNO { get; set; }  

        public DateTime? BATCHDTE { get; set; }

        public DateTime? ENTRYDTE { get; set; }

        public DateTime? RECVDTE { get; set; }
               
        public int? TRACKNO { get; set; }
        public string BUNDLER { get; set; }
        public int? scTRACKNO1 { get; set; }
        public string scBUNDLER1 { get; set; }
        public int? scTRACKNO2 { get; set; }
        public string scBUNDLER2 { get; set; }

        public string FUNDCODE { get; set; }
        public string SRCECODE { get; set; }
        public decimal? AMT { get; set; }
        public string FUND { get; set; }
        public string PAYMETHOD { get; set; }
        public string PROGRAM { get; set; }
        public string PKGECODE { get; set; }
        public string PKGEDESC { get; set; }
        public string SRCEDESC { get; set; }
        public string ACCTCODE { get; set; }
        public string CKNO { get; set; }
        public string DESCRIP { get; set; }

        public string HOMEPHONE { get; set; }           
        public string WORKPHONE { get; set; }   
        public string CELLPHONE { get; set; }           
        public string EMAIL { get; set; }
        
        public string ADDR1 { get; set; }        
        public string ADDR2 { get; set; }
        public string STREET { get; set; }
        public string CITY { get; set; }
        public string STATE { get; set; }
        public string ZIP { get; set; }
        public string PLUS4 { get; set; }
        public string REGION { get; set; }
        public string METROAREA { get; set; }
        public string COUNTY { get; set; }
        
        public string CDCODE { get; set; }
        public string SDCODE { get; set; }
        public string LDCODE { get; set; }
        public string MISCTEXT { get; set; }
    }

    public class MoneyQR_ext1 : MoneyQR
    {
        public int count_ { get; set; }

        public Int64 rowNo { get; set; }
    }

    public class MoneySearchExport : _entityBase_crm, iItemType
    {
        public int MID { get; set; }

        public int PID { get; set; }

        public string RECORD_TYPE { get; set; }

        public string PREFIX { get; set; }

        public string FNAME { get; set; }

        public string MNAME { get; set; }

        public string LNAME { get; set; }

        public string SUFFIX { get; set; }

        public string EMPLOYER { get; set; }

        public string OCCUPATION { get; set; }

        public string BATCHNO { get; set; }

        public DateTime? BATCHDTE { get; set; }

        public DateTime? ENTRYDTE { get; set; }

        public DateTime? RECVDTE { get; set; }

        public string MEMBERASSIGNED { get; set; }

        public int? TRACKNO { get; set; }
        public string BUNDLER { get; set; }
        public int? scTRACKNO1 { get; set; }
        public string scBUNDLER1 { get; set; }
        public int? scTRACKNO2 { get; set; }
        public string scBUNDLER2 { get; set; }

        public string FUNDCODE { get; set; }
        public string SRCECODE { get; set; }
        public decimal? AMT { get; set; }
        public string FUND { get; set; }
        public string PAYMETHOD { get; set; }
        public string PROGRAM { get; set; }
        public string PKGECODE { get; set; }
        public string PKGEDESC { get; set; }
        public string SRCEDESC { get; set; }

        public string ACCTCODE { get; set; }
        public string CKNO { get; set; }
        public string CHANNEL { get; set; }

        public string EXCEPCODE { get; set; }
        public string FECMEMOTXT { get; set; }

        public string HOMEPHONE { get; set; }
        public string WORKPHONE { get; set; }
        public string CELLPHONE { get; set; }
        public string EMAIL { get; set; }

        public string ADDR1 { get; set; }
        public string ADDR2 { get; set; }
        public string STREET { get; set; }
        public string CITY { get; set; }
        public string STATE { get; set; }
        public string ZIP { get; set; }
        public string PLUS4 { get; set; }
        public string REGION { get; set; }
        public string METROAREA { get; set; }
        public string COUNTY { get; set; }

        public string CDCODE { get; set; }
        public string SDCODE { get; set; }
        public string LDCODE { get; set; }
        public string MISCTEXT { get; set; }

        public string MCOMMENT { get; set; }
        public Int16 MONYCODE { get; set; }
        public string CAMPGNCODE { get; set; }
        public byte SOFTMONEY { get; set; }
        public bool RECURRED { get; set; }
        public string REFERENCEID { get; set; }
        public string CCAUTHCODE { get; set; }
    }

}
