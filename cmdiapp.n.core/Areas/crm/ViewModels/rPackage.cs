﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace cmdiapp.n.core.Domain.ViewModels
{
    public class rPackage : iItemType
    {
        public int CNT { get; set; }
        public int PKGEID { get; set; }
        public Int16 PROGID { get; set; }
        public string DESCRIP { get; set; }
        public string <PERSON><PERSON><PERSON>CODE { get; set; }
        public string PKGEDESC { get; set; }
        public int MAILQTY { get; set; }
        public DateTime? MAILDTE { get; set; }
        public DateTime? FIRSTCAGE { get; set; }
        public DateTime? LASTCAGE { get; set; }
        public int NOGIFT { get; set; }
        public Decimal GROSS { get; set; }
        public Decimal COST { get; set; }
        public Decimal NET { get; set; }
        public byte SENDACKW { get; set; }
        public byte SENDRECV { get; set; }
        public string COMMENT { get; set; }
        public DateTime UPDATEDON { get; set; }
        public Int16 INITIATIVETYPEID { get; set; }
        public DateTime? LASTACTI { get; set; }
        public int SACTIVITY { get; set; }   
    }
}