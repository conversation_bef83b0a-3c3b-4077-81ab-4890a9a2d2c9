﻿using System;
using System.Text.RegularExpressions;
using System.Data;
using System.Linq;
using System.Text;
using System.Web;
using System.Web.Mvc;

namespace cmdiapp.n.core.Areas.crm.Core
{
    public class TxtResult : ActionResult
    {
        public DataSet Dataset { get; set; }
        public string FileName { get; set; }
        public TxtFormatParams FormatParams { get; set; }

        public TxtResult(DataSet dataset, string fileName, TxtFormatParams formatParams)
        {
            Dataset = dataset;
            FileName = Regex.IsMatch(fileName, @"\.txt$") ? fileName : fileName + ".txt";
            FormatParams = formatParams;
        }

        public override void ExecuteResult(ControllerContext context)
        {
            HttpResponseBase response = context.HttpContext.Response;
            string fileString = GetFileString(Dataset);
            response.Clear();
            response.Buffer = true;
            response.ContentType = "text/plain";
            response.AddHeader("content-disposition", $"attachment;  filename={FileName}");
            response.BinaryWrite(Encoding.ASCII.GetBytes(fileString));
            response.End();
        }

        private string GetFileString(DataSet dataset)
        {
            return string.Join(FormatParams.RowDelimiter,
                dataset.Tables[0].Rows.OfType<DataRow>()
                    .Select(row => string.Join(FormatParams.ColDelimiter, 
                        row.ItemArray.Select(val => FormatValue(val)))));
        }

        private string FormatValue(object value)
        {
            string formattedResult;
            if (value.GetType().Name == "DateTime")
            {
                formattedResult = String.Format("{0:MM/dd/yyyy}", value);
            }
            else
            {
                formattedResult = value?.ToString() ?? "";
            }

            formattedResult = formattedResult.Replace(FormatParams.RowDelimiter, "");
            formattedResult = EscapeAnyColDelimiters(formattedResult);

            return formattedResult;

        }

        private string EscapeAnyColDelimiters(string str)
        {
            string result = str;
            if (FormatParams.QuoteFieldsWithColDelim 
                && result.Contains(FormatParams.ColDelimiter))
            {
                // double any quotes within field
                result = 
                    result.Replace(
                        FormatParams.QuoteChar.ToString(), 
                        FormatParams.QuoteChar.ToString() + FormatParams.QuoteChar.ToString());
                // add quotes
                result = FormatParams.QuoteChar.ToString() + result + FormatParams.QuoteChar.ToString();                    
            }

            if (FormatParams.RemoveColDelim && result.Contains(FormatParams.ColDelimiter))
            {
                // remove any col delimiters within field
                result = result.Replace(FormatParams.ColDelimiter, "");
            }

            return result;
        }
        
    }
}