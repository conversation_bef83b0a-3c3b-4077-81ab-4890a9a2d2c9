﻿using System.Runtime.Serialization;

namespace cmdiapp.n.core.Areas.quickdash.Domain.Models
{
    [DataContract]
    public class PanelExportOptions
    {
        [DataMember(Name = "dataConverterType")]
        public PanelDataConverterType DataConverterType { get; set; }

        [DataMember(Name = "sheetName")]
        public string SheetName { get; set; } = "Dashboard Panel";

        [DataMember(Name = "parameterOverrides")]
        public Parameter[] ParameterOverrides { get; set; }
    }
}