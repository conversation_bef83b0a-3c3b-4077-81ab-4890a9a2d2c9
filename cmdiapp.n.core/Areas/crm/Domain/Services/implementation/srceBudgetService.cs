﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

using Ninject;
using Ninject.Web.Mvc;

using cmdiapp.n.core.Core.App_Start;
using cmdiapp.n.core.Areas.crm.Domain.Models;
using cmdiapp.n.core.Areas.crm.Domain.ViewModels;
using cmdiapp.n.core.Areas.crm.Domain.Repositories;
using cmdiapp.n.core.Domain.ViewModels;
using cmdiapp.n.core.Areas.crm.Domain.Data;

namespace cmdiapp.n.core.Areas.crm.Domain.Services
{
    public class srceBudgetService : IsrceBudgetService
    {
        private I_entity_crm _entity;

        public srceBudgetService(I_entity_crm entity)
        {
            _entity = entity;
        }

        public SRCEBUDGET Get(int budgetId)
        {
            return _entity.Single<SRCEBUDGET>(b => b.SRCEBUDGETID == budgetId);
        }

        public List<SRCEBUDGET> GetBudgetsForSource(int sourceId)
        {
            return _entity.All<SRCEBUDGET>()
                .Where(b => b.SRCEID == sourceId)
                .OrderBy(b => b.SRCEYEAR)
                .ToList();
        }
        
        public genericResponse Save(SRCEBUDGET budget)
        {
            string errMessage = ValidateBudget(budget);
            if (!string.IsNullOrEmpty(errMessage))
            {
                return new genericResponse
                {
                    success = false,
                    message = errMessage
                };
            }

            SRCEBUDGET existingBudget = _entity.Single<SRCEBUDGET>(b => b.SRCEBUDGETID == budget.SRCEBUDGETID);
            bool isNew = existingBudget == null;
            try
            {
                if (isNew)
                {
                    // add
                    _entity.Add(budget);
                }
                else
                {
                    // update
                    MapBudgetPropertiesForUpdate(budget, existingBudget);
                    _entity.Update(existingBudget);
                }
                _entity.CommitChanges();
                return new genericResponse
                {
                    success = true,
                    results = new List<iItemType>() { (isNew ? budget : existingBudget) }
                };
            }
            catch (Exception ex)
            {
                return new genericResponse
                {
                    success = false,
                    message = "An error occurred. Unable to save Source Budget.",
                    messageKey = ex.Message                    
                };
            }

        }

        private void MapBudgetPropertiesForUpdate(SRCEBUDGET newBudget, SRCEBUDGET existingBudget)
        {
            List<string> exceptions = new List<string> { "SRCEBUDGETID", "SRCEID" };
            foreach (var prop in existingBudget.GetType().GetProperties())
            {
                if (!exceptions.Contains(prop.Name))
                {
                    prop.SetValue(existingBudget, prop.GetValue(newBudget));
                }
            }
        }


        // returns error message if budget is not
        // fit to be added or updated
        private string ValidateBudget(SRCEBUDGET budget)
        {
            // check if budget already exists for this year and source code
            SRCEBUDGET duplicateYear = _entity.Single<SRCEBUDGET>(b =>
                b.SRCEYEAR == budget.SRCEYEAR
                && b.SRCEID == budget.SRCEID
                && b.SRCEBUDGETID != budget.SRCEBUDGETID);

            if (duplicateYear != null)
            {
                return "A budget with this year already exists for this Source Code.";
            }

            return "";
        }

        public genericResponse Delete(int budgetId)
        {
            SRCEBUDGET budget = _entity.Single<SRCEBUDGET>(b => b.SRCEBUDGETID == budgetId);
            if (budget == null)
            {
                return new genericResponse
                {
                    success = false,
                    message = $"There is no Source Budget with ID {budgetId}"
                };
            }

            try
            {
                _entity.Delete(budget);
                _entity.CommitChanges();
                return new genericResponse
                {
                    success = true
                };
            }
            catch (Exception ex)
            {
                return new genericResponse
                {
                    success = false,
                    message = $"An error occurred. Unable to delete Source Budget",
                    messageKey = ex.Message
                };
            }

        }
    }
}