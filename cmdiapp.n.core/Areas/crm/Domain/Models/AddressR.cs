using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Data;
using System.Linq;
using System.Web.Mvc;
using System.Data.Linq;
using System.ComponentModel.DataAnnotations.Schema;

using cmdiapp.n.core.Domain.ViewModels;
using cmdiapp.n.core.Areas.crm.Domain.Data;

namespace cmdiapp.n.core.Areas.crm.Domain.Models
{
    public class AddressR : _entityBase_crm, iItemType
    {
        public int ADDRESSID { get; set; }

        public int PID { get; set; }

        public short ADDRTYPEID { get; set; }
        public string ADDRTYPE { get; set; }
        public string ADDRTYPEDESC { get; set; }

        public string STREET { get; set; }

        public string ADDR1 { get; set; }

        public string ADDR2 { get; set; }

        public string CITY { get; set; }

        public string STATE { get; set; }

        public string ZIP { get; set; }

        public string PLUS4 { get; set; }

        public string CiStZip { get; set; }

        public string FIPS { get; set; }
        public string COUNTY { get; set; }

        public string CDCODE { get; set; }

        public string SDCODE { get; set; }

        public string LDCODE { get; set; }

        public decimal? LATITUDE { get; set; }
        
        public decimal? LONGITUDE { get; set; }

        public bool? PRIMARY { get; set; }

        public Nullable<System.DateTime> UPDATEDON { get; set; }
    }
}
