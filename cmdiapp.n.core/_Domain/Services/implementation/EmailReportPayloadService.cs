﻿using cmdiapp.n.core._Domain.Models;
using cmdiapp.n.core._Domain.ViewModels;
using cmdiapp.n.core.Domain.Data;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Data.Entity;
using System.Linq;
using System.Threading.Tasks;
using System.Web;

namespace cmdiapp.n.core._Domain.Services.implementation
{
    public class EmailReportPayloadService : IEmailReportPayloadService
    {
        private readonly I_entity _entity;

        public EmailReportPayloadService(I_entity entity)
        {
            _entity = entity;
        }

        public GenericSuccessResponse<int> AddPayload<T>(T payload, int emailReportId)
        {
            try
            {
                var payloadEntity = new EmailReportProcessPayloadEntity
                {
                    PayloadJson = JsonConvert.SerializeObject(payload),
                    EmailReportId = emailReportId,
                    CreatedAt = DateTime.Now
                };
                _entity.Add(payloadEntity);
                _entity.CommitChanges();
                return new GenericSuccessResponse<int>
                {
                    Success = true,
                    Results = payloadEntity.Id
                };
            }
            catch (Exception ex)
            {
                return new GenericSuccessResponse<int>
                {
                    Success = false,
                    Message = $"Encountered exception when adding payload: {ex.GetType().Name}: {ex.Message}"
                };
            }
        }

        public async Task<EmailReportProcessPayload<T>> GetPayloadAsync<T>(int id)
        {
            var payloadEntity = await _entity.All<EmailReportProcessPayloadEntity>()
                .SingleOrDefaultAsync(entity => entity.Id == id);

            if (payloadEntity == null)
            {
                return null;
            }

            return new EmailReportProcessPayload<T>(payloadEntity);
        }
    }
}