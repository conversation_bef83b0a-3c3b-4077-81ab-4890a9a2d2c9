﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Data;
using System.Linq;
using System.Web.Mvc;
using System.Data.Linq;
using System.ComponentModel.DataAnnotations.Schema;
using cmdiapp.n.core.Areas.crm.Domain.Data;

using cmdiapp.n.core.Domain.ViewModels;

namespace cmdiapp.n.core.Areas.crm.Domain.Models
{   
    [Table("dmTEMPLATE")]
    public class dmTEMPLATE : _entityBase_crm, iItemType
    {
        [Column]
        [Key]
        public Int16 TEMPLATEID { get; set; }

        [Column]
        public string TEMPCODE { get; set; }

        [Column]
        public string TEMPDESC { get; set; }

        [Column]
        public string CONTENT { get; set; }

        [Column]
        public DateTime? LASTUSED { get; set; }

        [Column]
        public DateTime? UPDATEDON { get; set; }

        [Column]
        public string WordDocFilename { get; set; }

        [Column]
        public byte[] WordDoc { get; set; }

        [NotMapped]
        public string TextField
        {
            get
            {
                return TEMPCODE + " - " + TEMPDESC + " - " + WordDocFilename;
            }
        }

    }
    public class dmTEMPLATE_ext
    {
        public Int16 TEMPLATEID { get; set; }
        
        public string TEMPCODE { get; set; }
        
        public string TEMPDESC { get; set; }
        
        public string CONTENT { get; set; }
        
        public DateTime? LASTUSED { get; set; }
        
        public DateTime? UPDATEDON { get; set; }
        
        public string WordDocFilename { get; set; }
        
        public byte[] WordDoc { get; set; }

        public int count_ { get; set; }

        public Int64 rowNo { get; set; }
    }
}