﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace cmdiapp.n.core.Areas.query.Domain.Models
{
    public interface IQExportFormat
    {
        string FileFormat { get; set; }
        bool IncludeHeader { get; set; }
        /// returns tuple of server file name [0] and client file name [1]
        Tuple<string, string> ToFile(DataTable result, string sheetname);
    }
}
