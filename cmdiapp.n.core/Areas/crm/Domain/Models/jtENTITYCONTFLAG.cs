﻿using cmdiapp.n.core.Areas.crm.Domain.Data;
using cmdiapp.n.core.Domain.ViewModels;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Web;

namespace cmdiapp.n.core.Areas.crm.Domain.Models
{
    [Table("jtENTITYCONTFLAG")]
    public class jtENTITYCONTFLAG : _entityBase_crm, iItemType
    {
        [Key]
        [Column]
        public int jtENTITYCONTFLAGID { get; set; }

        [Column]
        public int ENTITYCONTACTID { get; set; }

        [Column]
        public short CONTFLAGID { get; set; }

        [Column]
        public DateTime? UPDATEDON { get; set; }
    }
}