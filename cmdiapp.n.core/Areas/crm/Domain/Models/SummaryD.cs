﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.ComponentModel.DataAnnotations.Schema;

using cmdiapp.n.core.Areas.crm.Domain.Data;
using cmdiapp.n.core.Domain.ViewModels;

namespace cmdiapp.n.core.Areas.crm.Domain.Models
{
    [Table("SUMMARYD")]
    public class SummaryD: _entityBase_crm, iItemType
    {
        [Column]
        public int SUMMARYDID { get; set; }
        [Column]
        public int? PID { get; set; }
        [Column]
        public Int16? MONYCODE { get; set; }
        [Column]
        public decimal? FGIFT { get; set; }
        [Column]
        public DateTime? FGIFTDTE { get; set; }
	    [Column]
        public int? FGIFTID { get; set; }
	    [Column]
        public decimal? LGIFT { get; set; }
	    [Column]
        public DateTime? LGIFTDTE { get; set; }
	    [Column]
        public int? LGIFTID { get; set; }
	    [Column]
        public decimal? HPC { get; set; }
	    [Column]
        public DateTime? HPCDTE { get; set; }
	    [Column]
        public int? HPCID { get; set; }
	    [Column]
        public decimal? CUMTOT { get; set; }
	    [Column]
        public int? NOGIFTS { get; set; }
	    [Column]
        public decimal? YTDAMT { get; set; }
	    [Column]
        public int? YTDNO { get; set; }
	    [Column]
        public decimal? CTDAMT { get; set; }
	    [Column]
        public int? CTDNO { get; set; }
	    [Column]
        public decimal? MO6 { get; set; }
	    [Column]
        public int? NUM6 { get; set; }
	    [Column]
        public decimal? MO12 { get; set; }
	    [Column]
        public int? NUM12 { get; set; }
	    [Column]
        public decimal? MO18 { get; set; }
	    [Column]
        public int? NUM18 { get; set; }
	    [Column]
        public decimal? MO24 { get; set; }
	    [Column]
        public int? NUM24  { get; set; }
	    [Column]
        public decimal? L6  { get; set; }
	    [Column]
        public decimal? L12 { get; set; }
	    [Column]
        public decimal? L18 { get; set; }
	    [Column]
        public decimal? L24  { get; set; }
	    [Column]
        public decimal? LST6 { get; set; }
	    [Column]
        public decimal? LST12 { get; set; }
	    [Column]
        public decimal? LST18 { get; set; }
	    [Column]
        public decimal? LST24 { get; set; }
	    [Column]
        public DateTime? LST6DTE { get; set; }
	    [Column]
        public DateTime? LST12DTE { get; set; }
	    [Column]
        public DateTime? LST18DTE { get; set; }
	    [Column]
        public DateTime? LST24DTE { get; set; }
	    [Column]
        public DateTime? UPDATEDON { get; set; }
	    [Column]
        public decimal? CTDAMT_SOFT { get; set; }
	    [Column]
        public decimal? PREV1YRAMT { get; set; }
	    [Column]
        public decimal? PREV2YRAMT { get; set; }
	    [Column]
        public decimal? PREV3YRAMT { get; set; }
	    [Column]
        public decimal? PREV4YRAMT { get; set; }
	    [Column]
        public decimal? PREV5YRAMT { get; set; }
	    [Column]
        public decimal? LCAMT { get; set; }
	    [Column]
        public decimal? JFCAMT { get; set; }
	    [Column]
        public decimal? PCYCLE_TOTAL { get; set; }
	    [Column]
        public int? PCYCLE_NO { get; set; }
	    [Column]
        public decimal? CTDNETAMT { get; set; }
	    [Column]
        public decimal? CTDAMT_P { get; set; }
	    [Column]
        public decimal? CTDAMT_G { get; set; }
        public int? LFTDNO { get; set; }
        [Column]
        public decimal? LFTDAMT { get; set; }
        public int? L2FTDNO { get; set; }
        [Column]
        public decimal? L2FTDAMT { get; set; }
        [Column]
        public int? L3FTDNO { get; set; }
        [Column]
        public decimal? L3FTDAMT { get; set; }
    }
}