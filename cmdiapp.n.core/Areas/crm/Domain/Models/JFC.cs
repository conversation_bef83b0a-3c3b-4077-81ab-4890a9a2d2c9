﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Data;
using System.Linq;
using System.Web.Mvc;
using System.Data.Linq;
using System.ComponentModel.DataAnnotations.Schema;

using cmdiapp.n.core.Domain.ViewModels;
using cmdiapp.n.core.Areas.crm.Domain.Data;

namespace cmdiapp.n.core.Areas.crm.Domain.Models
{
    [Table("v_JFC_batch")]
    public class v_JFC_batch : _entityBase_crm, iItemType
    {
        [Column]
        [Key]
        public int DISTRIBID { get; set; }
        [Column]
        public string DISTRIBNO { get; set; }
        [Column]
        public DateTime? DISTRIBDTE { get; set; }
        [Column]
        public string FUNDCODE { get; set; }
        [Column]
        public string STATUS { get; set; }
        [Column]
        public int TOTALNO { get; set; }
        [Column]
        public decimal? TOTALAMT { get; set; }

    }

    [NotMapped]
    public class v_JFC_batch_ext : v_JFC_batch
    {
        public int count_ { get; set; }

        public Int64 rowNo { get; set; }
    }


    public class ContributionList : _entityBase_crm, iItemType
    {
        public int DISTRIBID { get; set; }

        public int PID { get; set; }
        
        public string TYPE { get; set; }

        public string PREFIX { get; set; }

        public string FNAME { get; set; }

        public string MNAME { get; set; }

        public string LNAME { get; set; }

        public string SUFFIX { get; set; }

        public string STREET { get; set; }

        public string ADDR1 { get; set; }

        public string ADDR2 { get; set; }

        public string CITY { get; set; }

        public string STATE { get; set; }

        public string ZIP { get; set; }

        public string HMPHN { get; set; }

        public string EMAIL { get; set; }

        public int MID { get; set; }
    }

    [NotMapped]
    public class ContributionList_ext1 : ContributionList
    {
        public int count_ { get; set; }

        public Int64 rowNo { get; set; }
    }

    public class DistribTotalsList
    {
        public string CMTENAME { get; set; }

        public decimal? AMT { get; set; }
        
    }

    public class DistribTotalsSummary
    {
        public int? TOTNO { get; set; }

        public decimal? TOTAMT { get; set; }

        public decimal? DISAMT { get; set; }

        public decimal? IKAMT { get; set; }

        public decimal? PTAMT { get; set; }

        public decimal? DIFF
        {
            get
            {
                return TOTAMT - DISAMT;
            }
        }
        
    }

    [Table("lkDISTRIBSTATUS")]
    public class lkDISTRIBSTATUS : _entityBase_crm, iItemType
    {
        [Column]
        [Key]
        public short STATUSID { get; set; }
        [Column]
        public string STATUS { get; set; }
    }

    public class jfc_distrib_mid
    {
        public Int16 jfccmteid { get; set; }

        public string cmtename { get; set; }

        public Int16 seq { get; set; }

        public Nullable<int> id { get; set; }

        public Nullable<int> mid { get; set; }

        public bool active { get; set; }
    }

    [Table("jtJFCDISTRIBMID")]
    public class jtJFCDISTRIBMID : _entityBase_crm, iItemType
    {
        [Column]
        [Key]
        public int ID { get; set; }
        [Column]
        public int DISTRIBID { get; set; }
        [Column]
        public int JFCCMTEID { get; set; }
        [Column]
        public Nullable<int> MID { get; set; }
    }

    [Table("lkJFCCMTE")]
    public class lkJFCCMTE : _entityBase_crm, iItemType
    {
        [Column]
        [Key]
        public short JFCCMTEID { get; set; }
        [Column]
        public string CMTENAME { get; set; }
        [Column]
        public short? PRIORITY { get; set; }
        [Column]
        public decimal? LIMIT { get; set; }
        [Column]
        public short? SEQ { get; set; }
        [Column]
        public short? FUNDID { get; set; }
        [Column]
        public short? PKGEID { get; set; }
        [Column]
        public decimal? LIMIT_P { get; set; }
        [Column]
        public short? FEC_FUNDID { get; set; }
        [Column]
        public string FEC_ELECTCD { get; set; }
        [Column]
        public string FEC_ELECTYR { get; set; }
        [Column]
        public decimal? LIMIT_C { get; set; }
        [Column]
        public decimal? LIMIT_O { get; set; }
        [Column]
        public decimal? RATIO { get; set; }
        [Column]
        public bool? ACTIVE { get; set; }
        [Column]
        public bool? isYTD { get; set; }
        [Column]
        public int dmJFCCMTEID { get; set; } 
    }

    public class jfc_batch_validate
    {
        public string ErrMsg { get; set; } 
    }

    public class jfc_Distrib : iItemType {
        public int PID { get; set; }
        public string PEOPLE_TYPE { get; set; }
        public string PREFIX { get; set; }
        public string FNAME{ get; set; }
        public string MNAME { get; set; }
        public string LNAME { get; set; }
        public string SUFFIX { get; set; }
        public string EMPLOYER { get; set; }
        public string OCCUPATION { get; set; }
        public string STREET { get; set; }
        public string ADDR1 { get; set; }
        public string ADDR2 { get; set; }
        public string CITY { get; set; }
        public string STATE { get; set; }
        public string ZIP { get; set; }
        public string HMPHN { get; set; }
        public string BSPHN { get; set; }
        public string CPPHN { get; set; }
        public string FAX { get; set; }
        public string EMAIL { get; set; }
        public int MID  { get; set; }
        public string BATCNO { get; set; }
        public DateTime BATCHDTE { get; set; }
        public decimal AMT { get; set; }
        public string FUNDCODE { get; set; }
        public string SRCECODE { get; set; }
        public string PROGTYPE { get; set; }
        public string TRACKNO { get; set; }
        public string EXCEPTION_CODE { get; set; }
        public string JFC_CODE { get; set; }
        public decimal PRI_CTD { get; set; }
        public decimal GEN_CTD { get; set; }
        public decimal JFC_CTD { get; set; }
        public decimal DISTRIB_AMT { get; set; }
    }

    [NotMapped]
    public class jfc_Distrib_ext : jfc_Distrib
    {
        public int count_ { get; set; }

        public Int64 rowNo { get; set; }
    }

    public class jfc_AwaitDistrib : iItemType
    {
        public int PID { get; set; }
        public string PEOPLE_TYPE { get; set; }
        public string PREFIX { get; set; }
        public string FNAME { get; set; }
        public string MNAME { get; set; }
        public string LNAME { get; set; }
        public string SUFFIX { get; set; }
        public string EMPLOYER { get; set; }
        public string OCCUPATION { get; set; }
        public string STREET { get; set; }
        public string ADDR1 { get; set; }
        public string ADDR2 { get; set; }
        public string CITY { get; set; }
        public string STATE { get; set; }
        public string ZIP { get; set; }
        public string HMPHN { get; set; }
        public string BSPHN { get; set; }
        public string CPPHN { get; set; }
        public string FAX { get; set; }
        public string EMAIL { get; set; }
        public int MID { get; set; }
        public string BATCNO { get; set; }
        public DateTime BATCHDTE { get; set; }
        public decimal AMT { get; set; }
        public string FUNDCODE { get; set; }
        public string SRCECODE { get; set; }
        public string PROGTYPE { get; set; }
        public string TRACKNO { get; set; }
        public string EXCEPTION_CODE { get; set; }
        public string JFC_CODE { get; set; }
        public decimal PRI_CTD { get; set; }
        public decimal GEN_CTD { get; set; }
        public decimal JFC_CTD { get; set; }
        public decimal PENDING_AMT { get; set; }
    }

    [NotMapped]
    public class jfc_AwaitDistrib_ext : jfc_AwaitDistrib
    {
        public int count_ { get; set; }

        public Int64 rowNo { get; set; }
    }

    public class jfc_Alloc : iItemType
    {
        public int PID { get; set; }
        public string PEOPLE_TYPE { get; set; }
        public string PREFIX { get; set; }
        public string FNAME { get; set; }
        public string MNAME { get; set; }
        public string LNAME { get; set; }
        public string SUFFIX { get; set; }
        public string EMPLOYER { get; set; }
        public string OCCUPATION { get; set; }
        public string STREET { get; set; }
        public string ADDR1 { get; set; }
        public string ADDR2 { get; set; }
        public string CITY { get; set; }
        public string STATE { get; set; }
        public string ZIP { get; set; }
        public string HMPHN { get; set; }
        public string BSPHN { get; set; }
        public string CPPHN { get; set; }
        public string FAX { get; set; }
        public string EMAIL { get; set; }
        public int MID { get; set; }
        public string BATCNO { get; set; }
        public DateTime BATCHDTE { get; set; }
        public decimal AMT { get; set; }
        public string FUNDCODE { get; set; }
        public string SRCECODE { get; set; }
        public string PROGTYPE { get; set; }
        public string TRACKNO { get; set; }
        public string EXCEPTION_CODE { get; set; }
        public string JFC_CODE { get; set; }
        public decimal PRI_CTD { get; set; }
        public decimal GEN_CTD { get; set; }
        public decimal JFC_CTD { get; set; }
        public decimal ALLOC_AMT { get; set; }
    }

    [NotMapped]
    public class jfc_Alloc_ext : jfc_Alloc
    {
        public int count_ { get; set; }

        public Int64 rowNo { get; set; }
    }

    [Table("dmJFCCMTE")]
    public class dmJFCCMTE : _entityBase_crm, iItemType
    {
        [Column]
        [Key]
        public int dmJFCCMTEID { get; set; }
        [Column]
        public short? FUNDID { get; set; }
        [Column]
        public string CODE { get; set; }
        [Column]
        public string DESCRIP { get; set; }
        [Column]
        public bool PRIME { get; set; }
        [Column]
        public DateTime UPDATEDON { get; set; }
    }
}



