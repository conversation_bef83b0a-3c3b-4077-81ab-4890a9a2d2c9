﻿using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

using cmdiapp.n.core.Areas.legislator.Domain.Data;
using cmdiapp.n.core.Domain.ViewModels;

namespace cmdiapp.n.core.Areas.legislator.Domain.Models
{
    [Table("CommitteeMember")]
    public class CommitteeMember: _entityBase_legislator, iItemType
    {
        [Column(Order = 0), Key, MaxLength(20)]
        public string committeeId { get; set; }

        [Column(Order = 1), Key, MaxLength(25)]
        public string legislatorId { get; set; }

        [Column, <PERSON><PERSON>ength(250)]
        public string legislatorName { get; set; }

        [Column, Max<PERSON>ength(50)]
        public string party { get; set; }

        [Column]
        public int? rank { get; set; }

        [Column, MaxLength(100)]
        public string title { get; set; }

        [Column]
        public bool active { get; set; }

        [Column]
        public DateTime? createdAt { get; set; }

        [Column]
        public DateTime? updatedAt { get; set; }

        [Column]
        public DateTime? inactivatedAt { get; set; }
    }
}