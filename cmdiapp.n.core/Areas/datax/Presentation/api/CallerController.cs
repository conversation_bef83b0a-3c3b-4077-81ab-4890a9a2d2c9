﻿using cmdiapp.n.core.Areas.datax.Domain.Models.ViewModels;
using cmdiapp.n.core.Areas.datax.Domain.Services;
using cmdiapp.n.core.Core.App_Start;
using cmdiapp.n.core.Library;
using cmdiapp.n.core.Presentation.Controllers;
using Ninject;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Web.Http;

namespace cmdiapp.n.core.Areas.datax.Presentation.api
{
    [RoutePrefix("datax/api/Caller")]
    public class CallerController : ApiController
    {
        private ICallerService _service;

        public CallerController()
        {
            _service = NinjectMVC.kernel.Get<ICallerService>();
        }

        [HttpGet, Route("All")]
        [apiAuthorize]
        public List<CallerDisplay> GetAll()
        {
            try
            {
                return _service.GetCallers();
            }
            catch (Exception ex)
            {
                util.recordInErrorLog(
                        $"Encountered exception getting all callers" +
                        $"{ex.GetType().Name}: {ex.Message}{Environment.NewLine}{ex.StackTrace}");
                return new List<CallerDisplay>();
            }
            
        }

        [HttpGet, Route("Anedot")]
        [apiAuthorize]
        public CallerDisplay GetAnedot()
        {
            return _service.GetCallerAnedot();
        }
    }
}
