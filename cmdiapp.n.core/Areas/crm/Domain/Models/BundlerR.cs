﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Data;
using System.Linq;
using System.Web.Mvc;
using System.Data.Linq;
using System.ComponentModel.DataAnnotations.Schema;

using cmdiapp.n.core.Domain.ViewModels;
using cmdiapp.n.core.Areas.crm.Domain.Data;

namespace cmdiapp.n.core.Areas.crm.Domain.Models
{
    
    public class BundlerR : _entityBase_crm, iItemType
    {
	    public int PID { get; set; }

        public int TRACKNO { get; set; }

        public string FNAME { get; set; }

        public string MNAME { get; set; }   

        public string LNAME { get; set; }

        public string EMAIL { get; set; }
                
        public string CITY { get; set; }
                
        public string STATE { get; set; }
                
        public decimal? CTDGIFT { get; set; }

        public decimal? COMMITMENT { get; set; }

        public decimal? cRaised { get; set; }
                
        public decimal? cSHARED { get; set; }

        public decimal? cREFERRED { get; set; }
                
    }
    
    public class rBundlerR : iItemType
    {
        public int PID { get; set; }

        public int TRACKNO { get; set; }

        public string FNAME { get; set; }

        public string MNAME { get; set; }   
                        
        public string LNAME { get; set; }

        public string EMAIL { get; set; }

        public string CITY { get; set; }

        public string STATE { get; set; }

        public decimal? CTDGIFT { get; set; }

        public decimal? COMMITMENT { get; set; }

        public decimal? cRaised { get; set; }
        
        public decimal? cSHARED { get; set; }

        public decimal? cREFERRED { get; set; }
    }
    
    [NotMapped]
    public class BundlerR_ext1 : BundlerR
    {
        public int count_ { get; set; }

        public Int64 rowNo { get; set; }
    }
    [NotMapped]
    public class BundlerTopNData : iItemType
    {
        public int? PID { get; set; }
        public string FNAME { get; set; }
        public string LNAME { get; set; }
        public string STREET { get; set; }
        public string ADDR1 { get; set; }
        public string CITY { get; set; }
        public string STATE { get; set; }
        public string ZIP { get; set; }
        public string PLUS4 { get; set; }
        public int? TRACKNO { get; set; }
        public decimal? TOTALRAISED { get; set; }
        public decimal? cRAISED { get; set; }
        public decimal? cRollup { get; set; }
        public decimal? cPLEDGED { get; set; }
        public decimal? COMMITMENT { get; set; }
        public decimal? cREFERRED { get; set; }
        public decimal? cSHARED { get; set; }
        public string LOBBYIST { get; set; }
        public string Field { get; set; }

        public int count_ { get; set; }
        public Int64 rowNo { get; set; }
    }
}
