<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Crimson CRM - People Profile Development Preview (Accurate)</title>

    <!-- Bootstrap StarUI CSS Framework (Actual Crimson Framework) -->
    <link href="contents/_lib/bootstrap-starUI/css/lib/bootstrap/bootstrap.min.css" rel="stylesheet">
    <link href="contents/_lib/bootstrap-starUI/css/main.source.altered.css" rel="stylesheet">
    <link href="contents/_lib/bootstrap-starUI/css/separate/pages/profile.min.css" rel="stylesheet">
    <link href="contents/_lib/bootstrap-starUI/css/separate/pages/widgets.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">

    <style>
        /* Actual Crimson CRM Color System */
        .backgroundcolor-darkgray { background-color: #6c7a86 !important; }
        .backgroundcolor-faintgray { background-color: #f8f9fa !important; }
        .backgroundcolor-primary { background-color: #007bff !important; }
        .backgroundcolor-white { background-color: #fff !important; }
        .backgroundcolor-transluscentblack { background-color: rgba(0,0,0,0.7) !important; }

        .bordercolor-darkgray { border-color: #6c7a86 !important; }
        .bordercolor-primary { border-color: #007bff !important; }

        .color-primary { color: #007bff !important; }
        .color-hover-lightprimary:hover { color: #0056b3 !important; }
        .color-black { color: #000 !important; }
        .color-darkgray { color: #6c7a86 !important; }
        .color-white { color: #fff !important; }
        .color-crimson-dark-gray { color: #6c7a86 !important; }
        .color-hover-primary:hover { color: #007bff !important; }

        .font-inverse { color: #fff !important; }

        /* Box Typical - Standard Crimson Container */
        .box-typical {
            background: #fff;
            border: 1px solid #e1e5eb;
            border-radius: 3px;
            margin-bottom: 20px;
            box-shadow: 0 1px 1px rgba(0,0,0,0.05);
        }
        
        /* Dashboard Layout System - Actual Crimson Layout */
        .root-panel-container {
            display: flex;
            flex-flow: row wrap;
            margin-bottom: 65px;
        }

        .root-panel-container.layout-one > * {
            flex-basis: 100%;
            margin: 5px;
        }

        .root-panel-container.layout-one > *:first-child {
            flex-grow: 1;
            flex-basis: 160px; /* Profile Overview */
        }

        .root-panel-container.layout-one > *:nth-child(2) {
            flex-grow: 1;
            flex-basis: 80px; /* Timeline */
        }

        .root-panel-container.layout-one > *:nth-last-child(3) {
            flex-grow: 4;
            flex-basis: 360px; /* Main Content */
        }

        /* Profile Overview Styles - Exact from component */
        .overview-container {
            display: flex;
            flex-flow: column nowrap;
            height: 490px;
            overflow: hidden;
        }
        .top-card {
            display: flex;
            flex-flow: column nowrap;
            justify-content: center;
            align-items: center;
            text-align: center;
            padding: 28px 15px 23px;
        }
        .picture-container {
            width: 110px;
            height: 110px;
            margin: 0 auto .5rem;
            position: relative;
        }
        .picture-container img {
            display: block;
            width: 100%;
            height: 100%;
            border-radius: 50%;
            object-fit: cover;
        }
        #fileInputLabel {
            margin: 0 auto 0.5rem;
            cursor: pointer;
            position: absolute;
            margin-left: auto;
            margin-right: auto;
            left: 50%;
            bottom: -15px;
            transform: translate(-50%, -50%);
        }
        #fileInputLabel > span {
            position: relative;
            font-size: 0.75em;
            background-color: transparent;
            color: transparent;
            display: inline-block;
            border-radius: 3px;
            padding: 8px;
        }
        .picture-container:hover #fileInputLabel > span {
            background-color: rgba(0,0,0, 1);
            color: white;
        }
        .name {
            font-weight: 600;
            margin-bottom: 5px;
        }
        .id {
            font-size: .72rem;
            color: #6c7a86;
        }
        .bottom-card {
            padding-bottom: 15px;
            display: flex;
            flex-flow: column nowrap;
            justify-content: space-between;
            height: 50%;
        }
        .bottom-card > * {
            font-size: 0.8rem;
            width: 80%;
            padding: 15px 15px 0px 15px;
        }
        .bottom-card > div.buttons-footer {
            width: 100%;
        }
        .divider {
            border-top: solid 1px;
            margin: 0 0 11px;
            padding: 0;
            height: 0;
        }
        .address-marker-container {
            display: flex;
            flex-flow: row nowrap;
        }
        .address-marker-container a {
            width: 21px;
        }
        .address-marker-container a.disable {
            pointer-events: none;
            cursor: default;
        }
        .address-container {
            display: flex;
            flex-flow: column nowrap;
        }
        .contact-container {
            display: flex;
            flex-flow: column nowrap;
        }
        .contact-container > div > i {
            width: 17px;
        }
        .buttons-footer {
            display: flex;
            flex-flow: row wrap;
            justify-content: space-between;
        }
        .buttons-footer a {
            font-size: 1rem;
        }
        .buttons-footer .action-button a {
            font-size: 1.5rem;
        }
        .social-buttons {
            display: flex;
            flex-flow: row wrap;
            justify-content: flex-start;
            align-items: center;
        }
        .social-buttons > * {
            margin-right: 5px;
        }
        .action-button {
            display: flex;
            flex-flow: row wrap;
            justify-content: flex-end;
        }
        .action-button > *:first-child {
            margin-right: 5px;
        }
        
        /* Widget System - Actual Crimson Widgets */
        .widget {
            background: #fff;
            border: 1px solid #e1e5eb;
            border-radius: 3px;
            margin-bottom: 20px;
            box-shadow: 0 1px 1px rgba(0,0,0,0.05);
        }

        .widget.clickable {
            cursor: pointer;
        }

        .widget.selected {
            border-color: #007bff;
        }

        .widget-header {
            padding: 15px 20px;
            border-bottom: 1px solid #e1e5eb;
            font-weight: 600;
            position: relative;
        }

        .widget-activity {
            min-height: 200px;
        }

        .widget-activity-item {
            padding: 15px 20px;
        }

        /* Timeline Styles - Exact from component */
        .exp-timeline {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .exp-timeline-item {
            position: relative;
            padding-left: 40px;
            padding-bottom: 20px;
            border-left: 2px solid #e1e5eb;
        }

        .exp-timeline-item:last-child {
            border-left: none;
        }

        .dot {
            position: absolute;
            left: -8px;
            top: 0;
            width: 16px;
            height: 16px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            background-color: #007bff;
        }

        .dot i {
            font-size: 8px;
            color: #fff;
        }

        .tbl {
            display: table;
            width: 100%;
        }

        .tbl-row {
            display: table-row;
        }

        .tbl-cell {
            display: table-cell;
            cursor: pointer;
        }

        .exp-timeline-range {
            font-size: 0.9rem;
            margin-bottom: 5px;
        }

        .exp-timeline-status {
            font-size: 0.85rem;
            font-weight: 500;
        }

        /* Dropdown for Timeline */
        .addDropDown {
            position: absolute;
            right: 15px;
            top: 50%;
            transform: translateY(-50%);
        }

        .addDropDownMenu {
            min-width: 120px;
        }
        
        /* Main Content Container */
        .main-container {
            background: #fff;
            border: 1px solid #e1e5eb;
            border-radius: 3px;
            margin-bottom: 20px;
            box-shadow: 0 1px 1px rgba(0,0,0,0.05);
        }

        .with-title {
            padding-top: 0;
        }

        .main-title {
            padding: 15px 20px;
            border-bottom: 1px solid #e1e5eb;
            font-weight: 600;
        }
        
        /* Footer Styles - Actual Footer Component */
        .page-footer-fixed {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            z-index: 1000;
            background: #fff;
            border-top: 1px solid #e1e5eb;
            padding: 10px 20px;
            box-shadow: 0 -1px 3px rgba(0,0,0,0.1);
        }

        .footer-container {
            width: 100%;
            display: flex;
            flex-flow: row wrap;
            justify-content: space-between;
        }

        .left-buttons {
            display: flex;
            flex-flow: row wrap;
            justify-content: flex-start;
        }

        .left-buttons > button,
        .left-buttons > a {
            width: 75px;
            margin-right: 5px;
        }

        .right-buttons {
            display: flex;
            flex-flow: row wrap;
            justify-content: flex-end;
        }

        .right-buttons > button,
        .right-buttons > .btn-group {
            margin-left: 3px;
        }

        .lookupDiv {
            margin-right: 3px;
            margin-left: 3px;
        }

        .callSheetDiv {
            margin-left: 3px;
        }

        .callSheetDropdown {
            width: 100%;
        }

        /* Button Styles */
        .btn-inline {
            display: inline-block;
            margin: 2px;
        }

        .btn-primary-outline {
            border-color: #007bff;
            background-color: #fff;
            color: #007bff;
        }

        .btn-primary-outline:hover {
            background-color: #007bff;
            color: #fff;
        }

        /* Mobile Responsive */
        @media (max-width: 768px) {
            .root-panel-container.layout-one {
                flex-direction: column;
            }

            .root-panel-container.layout-one > * {
                flex-basis: auto;
                margin: 5px 0;
            }

            .overview-container {
                height: auto;
                min-height: 300px;
            }
        }
    </style>
</head>
<body>
    <!-- Dashboard Container - Actual Crimson Structure -->
    <div class="title-spinner-container" style="padding: 20px 20px 0;">
        <div class="title-container">
            <h5 class="title">John Doe <span>Profile</span></h5>
            <p class="subtitle">ID #12345 - Last Updated: Sep 24, 2025</p>
        </div>
    </div>

    <!-- Root Panel Container with Layout-One (Actual Dashboard Layout) -->
    <div class="root-panel-container layout-one">
        <!-- Panel 1: Profile Overview (Left Sidebar) -->
        <section class="box-typical overview-container bordercolor-darkgray">
            <div class="top-card backgroundcolor-faintgray bordercolor-darkgray">
                <div class="picture-container">
                    <img src="https://via.placeholder.com/110x110/007bff/ffffff?text=JD" alt="Profile Photo" />
                    <label for="fileInput" id="fileInputLabel">
                        <span>
                            <span>Change</span>
                        </span>
                    </label>
                    <input name="fileInput" type="file" id="fileInput" style="display: none;" accept="image/*" />
                </div>
                <div class="name">John Doe</div>
                <div class="id">ID #12345</div>
            </div>
            <div class="divider bordercolor-darkgray"></div>
            <div class="bottom-card">
                <div class="address-marker-container">
                    <a href="#" class="color-primary color-hover-lightprimary" title="Map Address">
                        <i class="fas fa-map-marker-alt fa-lg fa-2x"></i>
                    </a>
                    <div class="address-container">
                        <i class="fal fa-home"></i>
                        <div>123 Main Street</div>
                        <div>Suite 100</div>
                        <div>New York, NY</div>
                        <div>10001</div>
                        <div>United States</div>
                    </div>
                </div>
                <div class="contact-container">
                    <div>
                        <i class="fal fa-envelope"></i>
                        <a class="color-primary color-hover-lightprimary" href="mailto:<EMAIL>"><EMAIL></a>
                    </div>
                    <div>
                        <i class="fal fa-home"></i>
                        <span>(555) 123-4567</span>
                    </div>
                    <div>
                        <i class="fal fa-building"></i>
                        <span>(555) 987-6543</span>
                    </div>
                    <div>
                        <i class="fal fa-tablet"></i>
                        <span>(555) 555-5555</span>
                    </div>
                </div>
                <div class="buttons-footer">
                    <div class="social-buttons">
                        <a href="#" class="color-darkgray color-hover-lightprimary" target="_blank">
                            <i class="fab fa-facebook-square"></i>
                        </a>
                        <a href="#" class="color-darkgray color-hover-lightprimary" target="_blank">
                            <i class="fab fa-twitter-square"></i>
                        </a>
                        <a href="#" class="color-darkgray color-hover-lightprimary" target="_blank">
                            <i class="fab fa-linkedin-square"></i>
                        </a>
                    </div>
                    <div class="action-button">
                        <a href="#" class="color-primary color-hover-lightprimary" title="Address Book">
                            <i class="fal fa-address-book"></i>
                        </a>
                        <a href="#" class="color-primary color-hover-lightprimary" title="Phone Book">
                            <i class="fal fa-phone-square"></i>
                        </a>
                    </div>
                </div>
            </div>
        </section>

        <!-- Panel 2: Timeline (Middle Column) -->
        <section class="widget widget-activity bordercolor-darkgray selected">
            <header class="widget-header backgroundcolor-primary bordercolor-primary font-inverse">
                Timeline
                <div class="btn-group dropdown addDropDown" data-toggle="tooltip">
                    <button type="button" class="btn btn-primary-outline btn-md dropdown-toggle" data-toggle="dropdown">
                        <i class="fa fa-plus"></i><span class="caret"></span>
                    </button>
                    <div class="dropdown-menu addDropDownMenu">
                        <a class="dropdown-item" href="#">Money</a>
                        <a class="dropdown-item" href="#">Pledge</a>
                        <a class="dropdown-item" href="#">Event</a>
                        <a class="dropdown-item" href="#">Note</a>
                        <a class="dropdown-item" href="#">Task</a>
                        <a class="dropdown-item" href="#">Action</a>
                    </div>
                </div>
            </header>
            <div class="widget-activity-item">
                <ul class="exp-timeline">
                    <li class="exp-timeline-item">
                        <div class="dot color-primary">
                            <i class="fas fa-dollar-sign backgroundcolor-white"></i>
                        </div>
                        <div class="tbl">
                            <div class="tbl-row">
                                <div class="tbl-cell">
                                    <div class="exp-timeline-range color-crimson-dark-gray color-hover-primary">Aug 01, 2026</div>
                                    <div class="exp-timeline-status color-black color-hover-primary">Donation - $500.00</div>
                                </div>
                            </div>
                        </div>
                    </li>
                    <li class="exp-timeline-item">
                        <div class="dot color-primary">
                            <i class="fas fa-calendar backgroundcolor-white"></i>
                        </div>
                        <div class="tbl">
                            <div class="tbl-row">
                                <div class="tbl-cell">
                                    <div class="exp-timeline-range color-crimson-dark-gray color-hover-primary">Aug 18, 2025</div>
                                    <div class="exp-timeline-status color-black color-hover-primary">Event Attendance - Annual Gala</div>
                                </div>
                            </div>
                        </div>
                    </li>
                    <li class="exp-timeline-item">
                        <div class="dot color-primary">
                            <i class="fas fa-sticky-note backgroundcolor-white"></i>
                        </div>
                        <div class="tbl">
                            <div class="tbl-row">
                                <div class="tbl-cell">
                                    <div class="exp-timeline-range color-crimson-dark-gray color-hover-primary">Jul 15, 2025</div>
                                    <div class="exp-timeline-status color-black color-hover-primary">Note - Follow up call scheduled</div>
                                </div>
                            </div>
                        </div>
                    </li>
                    <li class="exp-timeline-item">
                        <div class="dot color-primary">
                            <i class="fas fa-phone backgroundcolor-white"></i>
                        </div>
                        <div class="tbl">
                            <div class="tbl-row">
                                <div class="tbl-cell">
                                    <div class="exp-timeline-range color-crimson-dark-gray color-hover-primary">Jun 20, 2025</div>
                                    <div class="exp-timeline-status color-black color-hover-primary">Phone Call - Discussed upcoming event</div>
                                </div>
                            </div>
                        </div>
                    </li>
                </ul>
            </div>
        </section>

        <!-- Panel 3: Main Content Area (Right Side) -->
        <section class="main-container with-title">
            <header class="main-title">
                Individual Profile Details
            </header>
            <div style="padding: 20px;">
                <!-- Tab Navigation (Actual Crimson Style) -->
                <ul class="nav nav-tabs" role="tablist">
                    <li class="nav-item">
                        <a class="nav-link active" data-toggle="tab" href="#individual" role="tab">Individual</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" data-toggle="tab" href="#giving-history" role="tab">Giving History</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" data-toggle="tab" href="#codes" role="tab">Codes</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" data-toggle="tab" href="#moves" role="tab">Moves Management</a>
                    </li>
                </ul>

                <!-- Tab Content -->
                <div class="tab-content" style="margin-top: 20px;">
                    <!-- Individual Tab -->
                    <div class="tab-pane fade show active" id="individual" role="tabpanel">
                        <div class="row">
                            <div class="col-md-6">
                                <h6>Personal Information</h6>
                                <table class="table table-sm">
                                    <tr>
                                        <td><strong>Full Name:</strong></td>
                                        <td>John Michael Doe</td>
                                    </tr>
                                    <tr>
                                        <td><strong>Preferred Name:</strong></td>
                                        <td>John</td>
                                    </tr>
                                    <tr>
                                        <td><strong>Title:</strong></td>
                                        <td>Mr.</td>
                                    </tr>
                                    <tr>
                                        <td><strong>Date of Birth:</strong></td>
                                        <td>01/15/1980</td>
                                    </tr>
                                    <tr>
                                        <td><strong>Gender:</strong></td>
                                        <td>Male</td>
                                    </tr>
                                </table>
                            </div>
                            <div class="col-md-6">
                                <h6>Contact Preferences</h6>
                                <table class="table table-sm">
                                    <tr>
                                        <td><strong>Preferred Contact:</strong></td>
                                        <td>Email</td>
                                    </tr>
                                    <tr>
                                        <td><strong>Do Not Mail:</strong></td>
                                        <td>No</td>
                                    </tr>
                                    <tr>
                                        <td><strong>Do Not Email:</strong></td>
                                        <td>No</td>
                                    </tr>
                                    <tr>
                                        <td><strong>Do Not Call:</strong></td>
                                        <td>No</td>
                                    </tr>
                                </table>
                            </div>
                        </div>
                    </div>

                    <!-- Giving History Tab -->
                    <div class="tab-pane fade" id="giving-history" role="tabpanel">
                        <div class="row">
                            <div class="col-md-3">
                                <div class="widget">
                                    <div class="widget-header">Total Giving</div>
                                    <div style="padding: 20px; text-align: center;">
                                        <div style="font-size: 2rem; font-weight: bold; color: #007bff;">$994,948.01</div>
                                        <div style="color: #6c7a86;">Lifetime Total</div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="widget">
                                    <div class="widget-header">This Year</div>
                                    <div style="padding: 20px; text-align: center;">
                                        <div style="font-size: 2rem; font-weight: bold; color: #28a745;">$12,500.00</div>
                                        <div style="color: #6c7a86;">2025 Total</div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="widget">
                                    <div class="widget-header">Last Gift</div>
                                    <div style="padding: 20px; text-align: center;">
                                        <div style="font-size: 2rem; font-weight: bold; color: #ffc107;">$500.00</div>
                                        <div style="color: #6c7a86;">Aug 01, 2026</div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="widget">
                                    <div class="widget-header">Average Gift</div>
                                    <div style="padding: 20px; text-align: center;">
                                        <div style="font-size: 2rem; font-weight: bold; color: #17a2b8;">$607.00</div>
                                        <div style="color: #6c7a86;">Per Transaction</div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="widget" style="margin-top: 20px;">
                            <div class="widget-header">Recent Transactions</div>
                            <div style="padding: 0;">
                                <table class="table table-sm mb-0">
                                    <thead>
                                        <tr>
                                            <th>Date</th>
                                            <th>MID</th>
                                            <th>Amount</th>
                                            <th>Type</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td>Aug 01, 2026</td>
                                            <td>123456</td>
                                            <td style="color: #28a745; font-weight: bold;">$500.00</td>
                                            <td>Donation</td>
                                        </tr>
                                        <tr>
                                            <td>Jul 15, 2026</td>
                                            <td>123455</td>
                                            <td style="color: #28a745; font-weight: bold;">$250.00</td>
                                            <td>Donation</td>
                                        </tr>
                                        <tr>
                                            <td>Jun 30, 2026</td>
                                            <td>123454</td>
                                            <td style="color: #28a745; font-weight: bold;">$1,000.00</td>
                                            <td>Major Gift</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>

                    <!-- Codes Tab -->
                    <div class="tab-pane fade" id="codes" role="tabpanel">
                        <div class="widget">
                            <div class="widget-header">Donor Codes</div>
                            <div style="padding: 20px;">
                                <span class="badge bg-danger me-2">BIG DONOR</span>
                                <span class="badge bg-warning me-2">LAPSED</span>
                                <span class="badge bg-primary me-2">MAJOR GIFT</span>
                                <span class="badge bg-success me-2">MONTHLY DONOR</span>
                            </div>
                        </div>
                    </div>

                    <!-- Moves Management Tab -->
                    <div class="tab-pane fade" id="moves" role="tabpanel">
                        <div class="widget">
                            <div class="widget-header">Moves Management</div>
                            <div style="padding: 20px;">
                                <p>Moves management content would be displayed here...</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </div>

    <!-- Actual Crimson Footer Component -->
    <footer class="page-footer-fixed bottom-bar">
        <div class="footer-container">
            <div class="left-buttons">
                <a class="btn btn-inline btn-primary-outline btn-md" title="Dupe - Checking & Merge">
                    <i class="fas fa-people-arrows"></i>
                </a>
                <a class="btn btn-inline btn-primary btn-md" title="Relationships">
                    <i class="fas fa-users"></i>
                </a>
                <a href="#" target="_blank" class="btn btn-inline btn-success btn-md" title="Save as a Business Card (vCard)">
                    <i class="fas fa-save"></i>
                </a>
            </div>
            <div class="right-buttons">
                <button type="button" class="btn btn-inline btn-success btn-md">
                    <i class="fas fa-user-edit"></i>
                    Edit Full Record
                </button>
                <button type="button" class="btn btn-inline btn-success btn-md">
                    <i class="fas fa-copy"></i>
                    Copy Record
                </button>
                <div class="btn-group dropup lookupDiv" title="Look up" data-toggle="tooltip">
                    <button type="button" class="btn btn-primary btn-md dropdown-toggle" data-toggle="dropdown">
                        <i class="fas fa-file-search"></i>
                        Look up&nbsp;&nbsp;<span class="caret"></span>
                    </button>
                    <div class="dropdown-menu" role="menu">
                        <a class="dropdown-item" href="#">Google</a>
                        <a class="dropdown-item" href="#">LinkedIn</a>
                        <a class="dropdown-item" href="#">Google Map</a>
                        <a class="dropdown-item" href="#">Zillow</a>
                        <a class="dropdown-item" href="#">FEC.gov</a>
                        <a class="dropdown-item" href="#">Wealth Engine</a>
                        <a class="dropdown-item" href="#">Reverse Address</a>
                    </div>
                </div>
                <div class="btn-group dropup callSheetDiv" data-toggle="tooltip">
                    <button type="button" class="btn btn-primary btn-md dropdown-toggle" data-toggle="dropdown">
                        <i class="far fa-print"></i>&nbsp;Print Call Sheets&nbsp;<span class="caret"></span>
                    </button>
                    <div class="dropdown-menu callSheetDropdown">
                        <a class="dropdown-item" href="#">Full Profile</a>
                        <a class="dropdown-item" href="#">Brief Profile</a>
                        <a class="dropdown-item" href="#">Pocket Profile</a>
                        <a class="dropdown-item" href="#">Extended Profile</a>
                        <a class="dropdown-item" href="#">PAC Profile</a>
                        <a class="dropdown-item" href="#">Email Full Profile</a>
                    </div>
                </div>
            </div>
        </div>
    </footer>

    <!-- Bootstrap StarUI JS Framework (Actual Crimson Framework) -->
    <script src="contents/_lib/bootstrap-starUI/js/lib/jquery/jquery.min.js"></script>
    <script src="contents/_lib/bootstrap-starUI/js/lib/bootstrap/bootstrap.min.js"></script>
    <script src="contents/_lib/bootstrap-starUI/js/plugins.js"></script>
    <script src="contents/_lib/bootstrap-starUI/js/app.js"></script>

    <script>
        // Initialize tooltips and dropdowns
        $(document).ready(function() {
            $('[data-toggle="tooltip"]').tooltip();
            $('[data-toggle="dropdown"]').dropdown();

            // Tab functionality
            $('a[data-toggle="tab"]').on('click', function (e) {
                e.preventDefault();
                $(this).tab('show');
            });

            // Widget selection
            $('.widget.clickable').on('click', function() {
                $('.widget').removeClass('selected');
                $(this).addClass('selected');
            });
        });
    </script>
</body>
</html>
