﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Data;
using System.Linq;
using System.Web.Mvc;
using System.Data.Linq;
using System.ComponentModel.DataAnnotations.Schema;

using cmdiapp.n.core.Domain.ViewModels;
using cmdiapp.n.core.Areas.crm.Domain.Data;

namespace cmdiapp.n.core.Areas.crm.Domain.Models
{
    public class ThankYou : _entityBase_crm, iItemType
    {
        public int PID { get; set; }
        public string TYPE { get; set; }
        public string PREFIX { get; set; }
        public string FNAME { get; set; }
        public string MNAME { get; set; }
        public string LNAME { get; set; }
        public string SUFFIX { get; set; }
        public string NAME { get; set; }
        public string SALUTATION { get; set; }
        public string EMPLOYER { get; set; }
        public string OCCUPATION { get; set; }
        public string SPOUSENAME { get; set; }
        public string MAILNAME { get; set; }
        public string MAILSALUTATION { get; set; }
        public bool PRIMEMAIL { get; set; }
        public int MID { get; set; }
        public string FUNDCODE { get; set; }
        public string SRCECODE { get; set; }
        public string BATCHNO { get; set; }
        public DateTime? BATCHDTE { get; set; }
        public decimal? AMT { get; set; }
        public string CKNO { get; set; }
        public string STREET { get; set; }
        public string ADDR1 { get; set; }
        public string ADDR2 { get; set; }
        public string CITY { get; set; }
        public string STATE { get; set; }
        public string ZIP { get; set; }
        public string PLUS4 { get; set; }
        public string HMPHN { get; set; }
        public string BSPHN { get; set; }
        public string CEPHN { get; set; }
        public string FAXPHN { get; set; }
        public string EMAIL { get; set; }
        public decimal? HPC { get; set; }
        public decimal? PRICTD { get; set; }
        public decimal? GENCTD { get; set; }
        public decimal? YTDAMT { get; set; }
        public string COMMENT { get; set; }
        public string CONTACT { get; set; }
        public string CTITLE { get; set; }
        public string CPREFIX { get; set; }
        public string CFNAME { get; set; }
        public string CMNAME { get; set; }
        public string CLNAME { get; set; }
        public string CSUFFIX { get; set; }
        public string CSTREET { get; set; }
        public string CADDR1 { get; set; }
        public string CADDR2 { get; set; }
        public string CCITY { get; set; }
        public string CSTATE { get; set; }
        public string CZIP { get; set; }
        public string CPLUS4 { get; set; }
        public string CHMPHN { get; set; }
        public string CBSPHN { get; set; }
        public string CEMAIL { get; set; }
        
    }

    public class ThankYou_ext1 : ThankYou
    {
        public int count_ { get; set; }

        public Int64 rowNo { get; set; }
    }
     
}
