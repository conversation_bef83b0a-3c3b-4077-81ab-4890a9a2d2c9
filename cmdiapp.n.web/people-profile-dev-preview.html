<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Crimson CRM - People Profile Development Preview</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    
    <style>
        /* Import actual Crimson styles */
        .box-typical {
            background: #fff;
            border: 1px solid #e1e5eb;
            border-radius: 3px;
            margin-bottom: 20px;
        }
        
        .bordercolor-darkgray { border-color: #6c7a86 !important; }
        .backgroundcolor-faintgray { background-color: #f8f9fa !important; }
        .color-primary { color: #007bff !important; }
        .color-hover-lightprimary:hover { color: #0056b3 !important; }
        .color-black { color: #000 !important; }
        .color-darkgray { color: #6c7a86 !important; }
        .backgroundcolor-primary { background-color: #007bff !important; }
        .color-white { color: #fff !important; }
        
        /* Profile Overview Styles - From actual component */
        .overview-container {
            display: flex;
            flex-flow: column nowrap;
            height: 490px;
            overflow: hidden;
        }
        .top-card {
            display: flex;
            flex-flow: column nowrap;
            justify-content: center;
            align-items: center;
            text-align: center;
            padding: 28px 15px 23px;
        }
        .picture-container {
            width: 110px;
            height: 110px;
            margin: 0 auto .5rem;
            position: relative;
        }
        .picture-container img {
            display: block;
            width: 100%;
            height: 100%;
            border-radius: 50%;
            object-fit: cover;
        }
        .name {
            font-weight: 600;
            margin-bottom: 5px;
        }
        .id {
            font-size: .72rem;
            color: #6c7a86;
        }
        .bottom-card {
            padding-bottom: 15px;
            display: flex;
            flex-flow: column nowrap;
            justify-content: space-between;
            height: 50%;
        }
        .bottom-card > * {
            font-size: 0.8rem;
            width: 80%;
            padding: 15px 15px 0px 15px;
        }
        .divider {
            border-top: solid 1px;
            margin: 0 0 11px;
            padding: 0;
            height: 0;
        }
        .address-marker-container {
            display: flex;
            flex-flow: row nowrap;
        }
        .address-marker-container a {
            width: 21px;
        }
        .contact-container {
            display: flex;
            flex-flow: column nowrap;
        }
        .contact-container > div > i {
            width: 17px;
        }
        .buttons-footer {
            display: flex;
            flex-flow: row wrap;
            justify-content: space-between;
        }
        .social-buttons {
            display: flex;
            flex-flow: row wrap;
            justify-content: flex-start;
            align-items: center;
        }
        .action-button {
            display: flex;
            flex-flow: row wrap;
            justify-content: flex-end;
        }
        
        /* Timeline Styles */
        .widget {
            background: #fff;
            border: 1px solid #e1e5eb;
            border-radius: 3px;
            margin-bottom: 20px;
        }
        .widget-header {
            padding: 15px 20px;
            border-bottom: 1px solid #e1e5eb;
            font-weight: 600;
        }
        .exp-timeline {
            list-style: none;
            padding: 0;
            margin: 0;
        }
        .exp-timeline-item {
            position: relative;
            padding-left: 40px;
            padding-bottom: 20px;
            border-left: 2px solid #e1e5eb;
        }
        .exp-timeline-item:last-child {
            border-left: none;
        }
        .dot {
            position: absolute;
            left: -8px;
            top: 0;
            width: 16px;
            height: 16px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .dot i {
            font-size: 8px;
        }
        
        /* Layout */
        .layout-one {
            display: grid;
            grid-template-columns: 280px 1fr;
            gap: 20px;
            height: 100vh;
            padding: 20px;
        }
        
        .main-content {
            display: flex;
            flex-direction: column;
        }
        
        .tabs-container {
            border-bottom: 1px solid #e1e5eb;
            margin-bottom: 20px;
        }
        
        .nav-tabs .nav-link {
            border: none;
            border-bottom: 2px solid transparent;
            color: #6c7a86;
        }
        
        .nav-tabs .nav-link.active {
            border-bottom-color: #007bff;
            color: #007bff;
        }
        
        /* Giving History Styles */
        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .metric-card {
            text-align: center;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 8px;
        }
        
        .metric-value {
            font-size: 2rem;
            font-weight: bold;
            color: #007bff;
        }
        
        .metric-label {
            color: #6c7a86;
            font-size: 0.9rem;
        }
        
        .recent-gifts {
            background: white;
            border: 1px solid #e1e5eb;
            border-radius: 8px;
            overflow: hidden;
        }
        
        .recent-gifts-header {
            background: #f8f9fa;
            padding: 15px 20px;
            border-bottom: 1px solid #e1e5eb;
            font-weight: 600;
        }
        
        .gift-item {
            padding: 15px 20px;
            border-bottom: 1px solid #f0f0f0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .gift-item:last-child {
            border-bottom: none;
        }
        
        .gift-date {
            color: #6c7a86;
            font-size: 0.9rem;
        }
        
        .gift-amount {
            font-weight: 600;
            color: #28a745;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row mb-3">
            <div class="col-12">
                <h2>🎯 Crimson CRM - People Profile Development Preview</h2>
                <p class="text-muted">This preview shows the actual Angular components and styling from the live Crimson CRM system.</p>
            </div>
        </div>
        
        <div class="layout-one">
            <!-- Left Sidebar - Profile Overview -->
            <div class="profile-sidebar">
                <section class="box-typical overview-container bordercolor-darkgray">
                    <div class="top-card backgroundcolor-faintgray bordercolor-darkgray">
                        <div class="picture-container">
                            <img src="https://via.placeholder.com/110x110/007bff/ffffff?text=JD" alt="Profile Photo" />
                        </div>
                        <div class="name">John Doe</div>
                        <div class="id">ID #12345</div>
                    </div>
                    <div class="divider bordercolor-darkgray"></div>
                    <div class="bottom-card">
                        <div class="address-marker-container">
                            <a href="#" class="color-primary color-hover-lightprimary" title="Map Address">
                                <i class="fas fa-map-marker-alt fa-lg fa-2x"></i>
                            </a>
                            <div class="address-container">
                                <div>123 Main Street</div>
                                <div>Suite 100</div>
                                <div>New York, NY</div>
                                <div>10001</div>
                                <div>United States</div>
                            </div>
                        </div>
                        <div class="contact-container">
                            <div>
                                <i class="fal fa-envelope"></i>
                                <a class="color-primary color-hover-lightprimary" href="mailto:<EMAIL>"><EMAIL></a>
                            </div>
                            <div>
                                <i class="fal fa-home"></i>
                                <span>(555) 123-4567</span>
                            </div>
                            <div>
                                <i class="fal fa-building"></i>
                                <span>(555) 987-6543</span>
                            </div>
                            <div>
                                <i class="fal fa-tablet"></i>
                                <span>(555) 555-5555</span>
                            </div>
                        </div>
                        <div class="buttons-footer">
                            <div class="social-buttons">
                                <a href="#" class="color-darkgray color-hover-lightprimary" target="_blank">
                                    <i class="fab fa-facebook-square"></i>
                                </a>
                                <a href="#" class="color-darkgray color-hover-lightprimary" target="_blank">
                                    <i class="fab fa-twitter-square"></i>
                                </a>
                                <a href="#" class="color-darkgray color-hover-lightprimary" target="_blank">
                                    <i class="fab fa-linkedin-square"></i>
                                </a>
                            </div>
                            <div class="action-button">
                                <a href="#" class="color-primary color-hover-lightprimary" title="Address Book">
                                    <i class="fal fa-address-book"></i>
                                </a>
                                <a href="#" class="color-primary color-hover-lightprimary" title="Phone Book">
                                    <i class="fal fa-phone-square"></i>
                                </a>
                            </div>
                        </div>
                    </div>
                </section>
            </div>
            
            <!-- Main Content Area -->
            <div class="main-content">
                <!-- Tab Navigation -->
                <div class="tabs-container">
                    <ul class="nav nav-tabs" id="profileTabs" role="tablist">
                        <li class="nav-item" role="presentation">
                            <button class="nav-link active" id="timeline-tab" data-bs-toggle="tab" data-bs-target="#timeline" type="button" role="tab">Timeline</button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="individual-tab" data-bs-toggle="tab" data-bs-target="#individual" type="button" role="tab">Individual</button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="giving-history-tab" data-bs-toggle="tab" data-bs-target="#giving-history" type="button" role="tab">Giving History</button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="codes-tab" data-bs-toggle="tab" data-bs-target="#codes" type="button" role="tab">Codes</button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="moves-tab" data-bs-toggle="tab" data-bs-target="#moves" type="button" role="tab">Moves Management</button>
                        </li>
                    </ul>
                </div>
                
                <!-- Tab Content -->
                <div class="tab-content" id="profileTabContent">
                    <!-- Timeline Tab -->
                    <div class="tab-pane fade show active" id="timeline" role="tabpanel">
                        <section class="widget widget-activity bordercolor-darkgray">
                            <header class="widget-header bordercolor-darkgray backgroundcolor-faintgray">
                                Timeline
                                <div class="btn-group dropdown float-end">
                                    <button type="button" class="btn btn-primary btn-sm dropdown-toggle" data-bs-toggle="dropdown">
                                        <i class="fa fa-plus"></i>
                                    </button>
                                    <ul class="dropdown-menu">
                                        <li><a class="dropdown-item" href="#">Money</a></li>
                                        <li><a class="dropdown-item" href="#">Pledge</a></li>
                                        <li><a class="dropdown-item" href="#">Event</a></li>
                                        <li><a class="dropdown-item" href="#">Note</a></li>
                                        <li><a class="dropdown-item" href="#">Task</a></li>
                                        <li><a class="dropdown-item" href="#">Action</a></li>
                                    </ul>
                                </div>
                            </header>
                            <div class="widget-activity-item p-3">
                                <ul class="exp-timeline">
                                    <li class="exp-timeline-item">
                                        <div class="dot color-primary">
                                            <i class="fas fa-dollar-sign backgroundcolor-white"></i>
                                        </div>
                                        <div class="tbl">
                                            <div class="tbl-row">
                                                <div class="tbl-cell">
                                                    <div class="exp-timeline-range">Aug 01, 2026</div>
                                                    <div class="exp-timeline-status">Donation - $500.00</div>
                                                </div>
                                            </div>
                                        </div>
                                    </li>
                                    <li class="exp-timeline-item">
                                        <div class="dot color-primary">
                                            <i class="fas fa-calendar backgroundcolor-white"></i>
                                        </div>
                                        <div class="tbl">
                                            <div class="tbl-row">
                                                <div class="tbl-cell">
                                                    <div class="exp-timeline-range">Aug 18, 2025</div>
                                                    <div class="exp-timeline-status">Event Attendance - Annual Gala</div>
                                                </div>
                                            </div>
                                        </div>
                                    </li>
                                    <li class="exp-timeline-item">
                                        <div class="dot color-primary">
                                            <i class="fas fa-sticky-note backgroundcolor-white"></i>
                                        </div>
                                        <div class="tbl">
                                            <div class="tbl-row">
                                                <div class="tbl-cell">
                                                    <div class="exp-timeline-range">Jul 15, 2025</div>
                                                    <div class="exp-timeline-status">Note - Follow up call scheduled</div>
                                                </div>
                                            </div>
                                        </div>
                                    </li>
                                </ul>
                            </div>
                        </section>
                    </div>
                    
                    <!-- Individual Tab -->
                    <div class="tab-pane fade" id="individual" role="tabpanel">
                        <div class="row">
                            <div class="col-md-3">
                                <div class="card">
                                    <div class="card-header">Occupation</div>
                                    <div class="card-body">
                                        <p>Software Engineer</p>
                                        <p>Tech Corp Inc.</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card">
                                    <div class="card-header">Current Date</div>
                                    <div class="card-body">
                                        <p>Sep 24, 2025</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card">
                                    <div class="card-header">Most Recent</div>
                                    <div class="card-body">
                                        <p>Last Gift: $500</p>
                                        <p>Aug 01, 2026</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card">
                                    <div class="card-header">Flags</div>
                                    <div class="card-body">
                                        <span class="badge bg-danger">BIG DONOR</span>
                                        <span class="badge bg-warning">LAPSED</span>
                                        <span class="badge bg-primary">MAJOR GIFT</span>
                                        <span class="badge bg-success">MONTHLY DONOR</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Giving History Tab -->
                    <div class="tab-pane fade" id="giving-history" role="tabpanel">
                        <div class="metrics-grid">
                            <div class="metric-card">
                                <div class="metric-value">$994,948.01</div>
                                <div class="metric-label">All-Time Total</div>
                            </div>
                            <div class="metric-card">
                                <div class="metric-value">$125,000</div>
                                <div class="metric-label">Soft Credits</div>
                            </div>
                            <div class="metric-card">
                                <div class="metric-value">$50,000</div>
                                <div class="metric-label">Pledges</div>
                            </div>
                            <div class="metric-card">
                                <div class="metric-value">1,639</div>
                                <div class="metric-label">Total Gifts</div>
                            </div>
                            <div class="metric-card">
                                <div class="metric-value">15</div>
                                <div class="metric-label">Consecutive Gifts</div>
                            </div>
                        </div>
                        
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <h5>Recent Gifts</h5>
                            <div class="btn-group">
                                <button type="button" class="btn btn-primary btn-sm">Add Gift</button>
                                <button type="button" class="btn btn-outline-primary btn-sm dropdown-toggle" data-bs-toggle="dropdown">All</button>
                                <ul class="dropdown-menu">
                                    <li><a class="dropdown-item" href="#">View</a></li>
                                    <li><a class="dropdown-item" href="#">Export in Background</a></li>
                                </ul>
                                <button type="button" class="btn btn-outline-primary btn-sm dropdown-toggle" data-bs-toggle="dropdown">Statement</button>
                                <ul class="dropdown-menu">
                                    <li><a class="dropdown-item" href="#">View</a></li>
                                    <li><a class="dropdown-item" href="#">Export in Background</a></li>
                                </ul>
                                <button type="button" class="btn btn-outline-primary btn-sm">Mail History</button>
                            </div>
                        </div>
                        
                        <div class="recent-gifts">
                            <div class="recent-gifts-header">Recent Transactions</div>
                            <div class="gift-item">
                                <div>
                                    <div><strong>MID: 123456</strong></div>
                                    <div class="gift-date">Aug 01, 2026</div>
                                </div>
                                <div class="gift-amount">$500.00</div>
                            </div>
                            <div class="gift-item">
                                <div>
                                    <div><strong>MID: 123455</strong></div>
                                    <div class="gift-date">Jul 15, 2026</div>
                                </div>
                                <div class="gift-amount">$250.00</div>
                            </div>
                            <div class="gift-item">
                                <div>
                                    <div><strong>MID: 123454</strong></div>
                                    <div class="gift-date">Jun 30, 2026</div>
                                </div>
                                <div class="gift-amount">$1,000.00</div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Other tabs -->
                    <div class="tab-pane fade" id="codes" role="tabpanel">
                        <p>Codes content would go here...</p>
                    </div>
                    
                    <div class="tab-pane fade" id="moves" role="tabpanel">
                        <p>Moves Management content would go here...</p>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Bottom Toolbar -->
        <div class="fixed-bottom bg-light border-top p-2">
            <div class="container-fluid">
                <div class="d-flex justify-content-between">
                    <div class="btn-group">
                        <button type="button" class="btn btn-outline-secondary btn-sm">Chart</button>
                        <button type="button" class="btn btn-outline-secondary btn-sm">Add</button>
                        <button type="button" class="btn btn-outline-secondary btn-sm">Download</button>
                    </div>
                    <div class="btn-group">
                        <button type="button" class="btn btn-outline-secondary btn-sm">Edit Full Record</button>
                        <button type="button" class="btn btn-outline-secondary btn-sm">Copy Record</button>
                        <button type="button" class="btn btn-outline-secondary btn-sm">Look up</button>
                        <button type="button" class="btn btn-outline-secondary btn-sm">Print Call Sheets</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
