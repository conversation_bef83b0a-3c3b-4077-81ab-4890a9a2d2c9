﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

using Ninject;
using Ninject.Web.Mvc;

using cmdiapp.n.core.Core.App_Start;
using cmdiapp.n.core.Areas.crm.Domain.Models;
using cmdiapp.n.core.Areas.crm.Domain.ViewModels;
using cmdiapp.n.core.Areas.crm.Domain.Repositories;

namespace cmdiapp.n.core.Areas.crm.Domain.Services
{
    public class PeopleCodeService : IPeoplecodeService
    {
        private I___unitOfWork _unitOfWork;
        private I__DBFactory _dbFactory;
        private IPeoplecodeRepository _PeopleCodeRepository;

        public PeopleCodeService(I___unitOfWork unitOfWork, I__DBFactory dbFactory, IPeoplecodeRepository PeopleCodeRepository)
        {
            _unitOfWork = unitOfWork;
            _dbFactory = dbFactory;
            _PeopleCodeRepository = PeopleCodeRepository;
        }
               

        //Read all People Code
        public List<PeopleCode> get_all_peoplecode()
        {
            var _allRecords = _PeopleCodeRepository.All();
            //return data
            return _allRecords.OrderBy(p => p.PEOCODEID).ToList();
        }
       
    }
}