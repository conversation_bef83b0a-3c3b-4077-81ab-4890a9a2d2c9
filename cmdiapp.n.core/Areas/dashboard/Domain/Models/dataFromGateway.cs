﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Dynamic;
using System.Data.Sql;
using System.Data.SqlClient;
using cmdiapp.n.core.Areas.crm.Core;
using cmdiapp.n.core.Areas.dashboard.Domain.Models;

using cmdiapp.n.core.Core;
using Microsoft.CSharp.RuntimeBinder;

namespace cmdiapp.n.core.Areas.dashboard.Domain.Models
{
    public static class dataFromGateway
    {
        public static dynamic daysTo
        {
            get
            {
                int days_ = session.currentDomain_project.days_to_target;
                string caption_ = session.currentDomain_project.target_date_caption;

                dynamic dsdN = new ExpandoObject();
                dsdN.title = (days_ > 0 ? caption_ : "Days Passed");
                dsdN.number = Math.Abs(days_);
                dsdN.format = "n";

                return dsdN;
            }
        }

        #region [[ Reports & My Searches]]
        private static dynamic getReports(string parameters, string sqlFuncName)
        {
            dynamic dataRead = cmdiapp.n.core.Areas.dashboard.Domain.Models.DashboardLib.runSqlXmlFunc(
                    System.Configuration.ConfigurationManager.ConnectionStrings["kernelContext"].ConnectionString,
                    sqlFuncName,
                    parameters);
            if (session.currentDomain_project.projectType.appVersion?.ToLower().Trim() == "nonprofit")
            {
                try
                {
                    if (dataRead != null
                        && dataRead.data != null
                        && !string.IsNullOrEmpty(dataRead.data.backgroundColor))
                    {
                        dataRead.data.backgroundColor = "primary";
                    }
                }
                catch (RuntimeBinderException)
                {
                }
            }
            else
            {
                dataRead.data.backgroundColor = "reportbackgroundColor";
            }
            return dataRead;
        }

        public static dynamic myReports(string parameters = "10")
        {
                string _parameters = String.Format("{0}.{1}.{2}.{3}",
                            "user",
                            session.userSession.UserId_i,
                            "MyReports",
                            parameters);
                return getReports(_parameters, "dbo.[dsb_homeTopNReports]");
        }
        public static dynamic allReports(string parameters = "10")
        {
            string _parameters = String.Format("{0}.{1}.{2}.{3}",
                        "AllReports",
                        "0",
                        "AllReports",
                        parameters);
            return getReports(_parameters, "dbo.[dsb_homeTopNReports]");
        }
        public static dynamic groupReports(string parameters = "10")
        {
            string _parameters = String.Format("{0}.{1}.{2}.{3}",
                            "userGroup",
                            session.currentDomainAuthorizedProject_userGroupId,
                            "GroupReports",
                            parameters);
            return getReports(_parameters, "dbo.[dsb_homeTopNReports]");
        }

        public static dynamic customReports(string parameters = "10")
        {
            string _parameters = String.Format("{0}.{1}.{2}.{3}",
                            "project",
                            session.currentDomain_projectId,
                            "CustomReports",
                            10);
            return getReports(_parameters, "dbo.[dsb_homeTopNReports]");
        }

        public static dynamic getSectionReports(string parameters)
        {
            var splitParameters = parameters?.Split('.') ?? new string[0];
            int splitParametersLength = splitParameters.Length;
            string scopeItsId = splitParametersLength > 0 ? splitParameters[0] : "";
            string section = splitParametersLength > 1 ? splitParameters[1] : "";
            string number = splitParameters.Length > 2 ? splitParameters[2] : "10";
            string _parameters = String.Format("{0}.{1}.{2}.{3}", "section", scopeItsId, section, number);
            return getReports(_parameters, "dbo.[dsb_homeTopNReports]");
        }

        public static dynamic mySearches(string parameters)
        {
            string _parameters = String.Format("{0}.{1}.{2}", 
                        session.currentDomain_project.projectId, 
                        session.userSession.UserId,
                        parameters);

            dynamic dataRead = cmdiapp.n.core.Areas.dashboard.Domain.Models.DashboardLib.runSqlXmlFunc(
                    System.Configuration.ConfigurationManager.ConnectionStrings["kernelContext"].ConnectionString,
                    "dbo.[dsb_homeTopNSearches]",
                    _parameters);

            if (session.currentDomain_project.projectType.appVersion?.ToLower().Trim() != "nonprofit")
            {
                dataRead.data.backgroundColor = "reportbackgroundColor";
            }
            return dataRead;
        }

        public static bool returnTrue
        {
            get { return true; }
        }

        public static bool returnTrueWithParam(string parameters)
        {
            return true; 
        }

        public static dynamic bundlerPanel(string parameters)
        {
            string rpmId = crmSession.rpmCampaignId() != "" ? crmSession.rpmCampaignId() : "null";
            string _params = $"{parameters}.{rpmId}";
            return DashboardLib.runSqlXmlFunc(
                        session.currentDomain_project._connectionString(),
                        "dbo.[dsb_peopleProfileBundler]",
                        _params);
        }
        #endregion

        public static int currentFiscalYear
        {
            get
            {
                return DashboardLib.runSqlXmlFunc(
                    session.currentDomain_project._connectionString(),
                    "[dbo].[getCurrentFiscalYear]",
                    null);
            }
        }
    }
}