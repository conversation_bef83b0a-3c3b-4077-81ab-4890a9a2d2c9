﻿using System.Runtime.Serialization;

namespace cmdiapp.n.core.Areas.datax.Domain.Models
{
    /// <summary>
    /// Represents deviation from default display and function of form for editing <see cref="Account"/>
    /// </summary>
    [DataContract]
    public class AccountFormDeviation
    {
        /// <summary>
        /// The key of the field in the form
        /// </summary>
        [DataMember(Name = "fieldKey")]
        public string fieldKey { get; set; }

        /// <summary>
        /// The type of deviation
        /// </summary>
        [DataMember(Name = "type")]
        public AccountFormDeviationType type { get; set; }

        /// <summary>
        /// New value
        /// </summary>
        [DataMember(Name = "value")]
        public string value { get; set; }
    }
}