﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Data;
using System.Linq;
using System.Web.Mvc;
using System.Data.Linq;
using System.ComponentModel.DataAnnotations.Schema;
using cmdiapp.n.core.Areas.crm.Domain.Data;

using cmdiapp.n.core.Domain.ViewModels;

namespace cmdiapp.n.core.Areas.crm.Domain.Models
{

    [Table("HISTORY")]
    public class History : iItemType
    {
        [Column]
        [Key]
        public int HISTORYID { get; set; }
        [Column]
        public DateTime? HSTDATE { get; set; }
        [Column]
        public String TABLENAME { get; set; }
        [Column]
        public String FIELDNAME { get; set; }
        [Column]
        public String OLDVALUE { get; set; }
        [Column]
        public String NEWVALUE { get; set; }

        //[NotMapped]
        public String USERNAME { get; set; }
        //[NotMapped]
        public String ACTION { get; set; }
        //[NotMapped]
        public String UPDDESC { get; set; }
        //[NotMapped]
        public String HSTDATEs { get; set; }
    }
    [Table("lkUPDTYPE")]
    public class lkUPDTYPE : _entityBase_crm, iItemType
    {
        [Column]
        [Key]
        public Int16? UPDTYPEID { get; set; }
        
        [Column]
        public String UPDTYPE { get; set; }

        [Column]
        public String DESCRIP { get; set; }
    }
    public class History2
    {
        public int AUDITID { get; set; }
        public string UPDDESC { get; set; }
        public string TABLENAME { get; set; }
        public DateTime? HSTDATE { get; set; }
        public string HSTDATEs { get; set; }
        public string USERNAME { get; set; }
        public string ACTION { get; set; }
        public string CHANGEDESC { get; set; }
    }
}