﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Data.Linq;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

using cmdiapp.n.core.Areas.crm.Domain.Data;

namespace cmdiapp.n.core.Areas.crm.Domain.Models
{
    [Table("lkPEOTYPE")]
    public class PeopleType : _entityBase_crm
    {
        [Column]
        [Key]
        public short PEOTYPEID { get; set; }
        
        [Column]
        public string PEOTYPE { get; set; }
        
        [Column]
        public string DESCRIP { get; set; }
        
        [Column]
        public byte DEFFLAG { get; set; }

        [Column]
        public byte SYSTEM { get; set; }
    }    
}