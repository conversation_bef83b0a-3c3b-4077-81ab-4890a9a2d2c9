﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

using cmdiapp.n.core.Domain.ViewModels;
using cmdiapp.n.core.Areas.crm.Domain.Data;

namespace cmdiapp.n.core.Areas.crm.Domain.Models
{
    public class SettingsTab
    {


        public string key { get; set; }

        public string label { get; set; }

        public string url { get; set; }

        public List<SettingsButtons> buttons { get; set; }

        public SettingsFieldFormat[] fieldFormat { get; set; }

        public string[] searchFields { get; set; }

        public string addGetUrl { get; set; }

        public string tooltipIdentifier { get; set; }

        public string exportUrl { get; set; }

        public bool canAdd { get; set; }

        public managePriorityConfig managePriority { get; set; }

        public SettingsTab(string Key, 
            string Label, 
            string Url = "",
            List<SettingsButtons> Buttons = null, 
            SettingsFieldFormat[] FieldFormat = null, 
            string[] SearchFields = null,
            bool CanAdd = false,
            string AddGetUrl="",
            string TooltipIdentifier="",
            string ExportUrl="", managePriorityConfig ManagePriority = null)
        {
            key = Key;
            label = Label;
            url = Url;
            buttons = Buttons;
            fieldFormat = FieldFormat;
            searchFields = SearchFields;
            addGetUrl = AddGetUrl;
            tooltipIdentifier = TooltipIdentifier;
            exportUrl = ExportUrl;
            canAdd = CanAdd;
            managePriority = ManagePriority;
        }

    }

    public class SettingsFieldFormat
    {
        public string key { get; set; }
        public string label { get; set; }
        public string format { get; set; }

        public SettingsFieldFormat(string KEY, string LABEL, string FORMAT = null)
        {
            key = KEY;
            label = LABEL;
            format = FORMAT;
        }
    }

    public class SettingsButtons
    {
        public string action { get; set; }
        public bool bindFuncToDblClick { get; set; }
        public string argKey { get; set; }
        public string argKeyPath { get; set; }
        public string url { get; set; }
        public List<EditField> editFields { get; set; }
        public string postUrl { get; set; }

        public SettingsButtons(string _action, string _argKey, string _argKeyPath = null, bool _bindFuncToDblClick = false, string _url = null, List<EditField> EditFields=null, string PostUrl=null)
        {
            action = _action;
            bindFuncToDblClick = _bindFuncToDblClick;
            argKey = _argKey;
            argKeyPath = _argKeyPath;
            url = _url;
            editFields = EditFields;
            postUrl = PostUrl;
        }
    }

    public class EditField
    {
        public string keyPath { get; set; }
        public string type { get; set; }
        public string displayName { get; set; }
        public SelectDataSourceConfig selectDataSourceConfig { get; set; }
        public bool required { get; set; }
        public int maxLength { get; set; }
        public string regex { get; set; }
        public int? maxValue { get; set; }
        public string cssClass { get; set; }

        public EditField(
            string KeyPath, 
            string Type, 
            string DisplayName, 
            SelectDataSourceConfig SelectDataSourceConfig = null,
            bool Required = false,
            int MaxLength = 250,
            string Regex = null,
            int? MaxValue = null,
            string CssClass = null
            )
        {
            string[] types = { "text", "text-number", "text-money", "text-date", "select", "checkbox", "select-multiple", "textarea", "text-fundlimit" };
            keyPath = KeyPath;
            type = types.Contains<string>(Type) ? Type : "text";
            displayName = DisplayName;
            selectDataSourceConfig = SelectDataSourceConfig;
            required = Required;
            maxLength = MaxLength;
            regex = Regex;
            maxValue = MaxValue;
            cssClass = CssClass;
        }
    }

    public class SelectDataSourceConfig
    {
        public string sourceType { get; set; }
        public string sourceKeyOrUrl { get; set; }
        public string valueKey { get; set; }
        public string displayKey { get; set; }
        public string multiSelectionArrayKey { get; set; }

        public SelectDataSourceConfig(string SourceType, string SourceKeyOrUrl, string ValueKey, string DisplayKey, string MultiSelectionArrayKey=null)
        {
            string[] sourceTypes = { "local", "server" };
            sourceType = sourceTypes.Contains<string>(SourceType) ? SourceType : "local";
            sourceKeyOrUrl = SourceKeyOrUrl;
            valueKey = ValueKey;
            displayKey = DisplayKey;
            multiSelectionArrayKey = MultiSelectionArrayKey;
        }
    }

    public class SelectOption
    {
        public string key { get; set; }
        public dynamic value { get; set; }

        public SelectOption(string Key, dynamic Value)
        {
            key = Key;
            value = Value;
        }
    }
    
    public class priorityModel
    {
        public int id { get; set; }
        public string name { get; set; }
        public int priority { get; set; }
    }

    public class managePriorityConfig
    {
        public string urlToGetModel { get; set; }
        public string urlToUpdateModel { get; set; }
    }
}