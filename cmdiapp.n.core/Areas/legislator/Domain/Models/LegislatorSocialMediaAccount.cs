﻿using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

using cmdiapp.n.core.Areas.legislator.Domain.Data;
using cmdiapp.n.core.Domain.ViewModels;

namespace cmdiapp.n.core.Areas.legislator.Domain.Models
{
    [Table("LegislatorSocialMediaAccount")]
    public class LegislatorSocialMediaAccount : _entityBase_legislator, iItemType
    {
        [Column, Key, MaxLength(25)]
        public string legislatorId { get; set; }

        [Column, MaxLength(100)]
        public string facebook { get; set; }

        [Column, MaxLength(150)]
        public string facebookUrl { get; set; }

        [Column, MaxLength(100)]
        public string twitter { get; set; }

        [Column, MaxLength(150)]
        public string twitterUrl { get; set; }

        [Column, MaxLength(100)]
        public string instagram { get; set; }

        [<PERSON>um<PERSON>, <PERSON><PERSON>ength(150)]
        public string instagramUrl { get; set; }

        [Column, MaxLength(100)]
        public string youtube { get; set; }

        [<PERSON>um<PERSON>, <PERSON><PERSON>ength(100)]
        public string youtube_id { get; set; }

        [<PERSON>um<PERSON>, <PERSON><PERSON>ength(150)]
        public string youtubeUrl { get; set; }

        [Column]
        public bool active { get; set; }

        [Column]
        public DateTime? createdAt { get; set; }

        [Column]
        public DateTime? updatedAt { get; set; }

        [Column]
        public DateTime? inactivatedAt { get; set; }
    }
}