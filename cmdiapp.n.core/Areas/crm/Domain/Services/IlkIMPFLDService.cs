﻿using System.Collections.Generic;
using System.Web.Mvc;
 
using cmdiapp.n.core.Areas.crm.Domain.Models;
using cmdiapp.n.core.Areas.crm.Domain.ViewModels;
using cmdiapp.n.core.Areas.crm.Domain.Repositories;

namespace cmdiapp.n.core.Areas.crm.Domain.Services
{
    public interface IlkIMPFLDService
    {
        List<lkImpField> get_all_impfields(string impType);
        List<lkImpField> get_contact_impfields(string impType);
        List<lkImpField> get_interaction_impfields(string impType);
        List<lkImpField> get_allevent_impfields(string impType);
    }
}