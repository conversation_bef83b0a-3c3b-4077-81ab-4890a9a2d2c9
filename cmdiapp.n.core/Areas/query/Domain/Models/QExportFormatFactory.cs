﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

using cmdiapp.n.core.Areas.crm.Core;

namespace cmdiapp.n.core.Areas.query.Domain.Models
{
    public class QExportFormatFactory
    {
        public IQExportFormat GetQExportFormat(string formatCode="xlsx")
        {
            switch (formatCode)
            {                
                case "xlsx":
                    return new QExportExcel();
                case "csv":
                    return new QExportCsv();
                case "pipe":
                    TxtFormatParams formatParams = new TxtFormatParams(
                            Environment.NewLine,
                            "|",
                            true,
                            '\"',
                            false
                        );
                    return new QExportTxt(formatParams);
                default:
                    throw new ArgumentException($"{formatCode} is not a valid format code.");
            }
        }
    }
}