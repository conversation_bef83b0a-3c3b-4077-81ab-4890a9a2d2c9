﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.Serialization;
using System.Web;

namespace cmdiapp.n.core.Areas.datax.Domain.Models.ViewModels
{
    /// <summary>
    /// Represents the status of a Cremir account setup
    /// </summary>
    [DataContract]
    public class CremirSetupStatus
    {
        /// <summary>
        /// Id of the status
        /// </summary>
        [DataMember(Name = "status")]
        public int StatusId { get; set; }
        /// <summary>
        /// Name of the status--will be 'Failed', 'Pending', or 'Success'
        /// </summary>
        [DataMember(Name = "statusName")]
        public string Status { get; set; }
        /// <summary>
        /// Optional message describing the status
        /// </summary>
        [DataMember(Name = "message")]
        public string Message { get; set; }

        [DataMember(Name = "accountId")]
        public int? AccountId { get; set; }
    }
}