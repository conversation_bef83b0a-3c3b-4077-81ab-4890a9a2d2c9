﻿using cmdiapp.n.core.Areas.datax.Domain.Models;
using cmdiapp.n.core.Areas.datax.Domain.Models.ViewModels;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace cmdiapp.n.core.Areas.datax.Domain.Services
{
    /// <summary>
    /// Provides methods for retrieving and sending data to/from data integration partners.
    /// </summary>
    public interface IIntegrationService
    {
        /// <summary>
        /// Retrieves accounts from partner.
        /// </summary>
        /// <param name="credentials">Credentials for user account associated with partner</param>
        /// <param name="callerId"><see cref="Caller.Id"/></param>
        /// <returns><see cref="Task{TResult}"/> of <see cref="List{T}"/> of <see cref="CallerAccountDisplay"/>s--will be empty if request fails</returns>
        Task<List<CallerAccountDisplay>> GetCallerAccountsAsync(CallerCredentials credentials, int callerId);

        Task<string> AnedotccProcessUrl(int fundid, string batchno, DateTime batchdte);

        Task<string> AnedotccProcessOneUrl(int fundid, string batchno, DateTime batchdte, int pid, string refno, string srcecode, decimal amt,
                                string chapcode, string campgncode, int? trackno, string comment);

        bool setupAnedot(int fundid);
    }
}
