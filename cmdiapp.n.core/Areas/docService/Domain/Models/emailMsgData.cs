﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.ComponentModel.DataAnnotations;
//using cmdiapp.n.core.Areas.crm.Domain.Data;
using cmdiapp.n.core.Domain.ViewModels;
using cmdiapp.n.core.Areas.docService.Domain.Data;
using System.ComponentModel.DataAnnotations.Schema;

namespace cmdiapp.n.core.Areas.docService.Domain.Models
{
    [Table("emailMsgData")]
    public class emailMsgData : _entityBase_docService, iItemType
    {
        [Column]
        [Key]
        public int MSGDATAID { get; set; }

        //[Column]
        public String CLIENTCODE { get; set; }
        
        public String PROCSTATUS { get; set; }
        public String SERVER { get; set; }
        public String ACCOUNT { get; set; }
        public String MBOX { get; set; }
        public DateTime RUNTIME { get; set; }
        public String MSGKEY { get; set; }
        public String MSGID { get; set; }
        public String FROMADDR { get; set; }
        public String FROMEMAIL { get; set; }
        public String FROMNAME { get; set; }
        public String TOADDR { get; set; }
        public DateTime MSGDATE { get; set; }
        public String SUBJECT { get; set; }
        public String MSGTXT { get; set; }
        public String MSGHTML { get; set; }
        public int NFILES { get; set; }
        public String FILE_1 { get; set; }
        public Byte[] FILE_1_DATA { get; set; }
        public String FILE_2 { get; set; }
        public Byte[] FILE_2_DATA { get; set; }
        public String FILE_3 { get; set; }
        public Byte[] FILE_3_DATA { get; set; }
        public String FILE_4 { get; set; }
        public Byte[] FILE_4_DATA { get; set; }
        public String FILE_5 { get; set; }
        public Byte[] FILE_5_DATA { get; set; }
        public String FILE_6 { get; set; }
        public Byte[] FILE_6_DATA { get; set; }
        public String FILE_7 { get; set; }
        public Byte[] FILE_7_DATA { get; set; }
        public String FILE_8 { get; set; }
        public Byte[] FILE_8_DATA { get; set; }
        public String FILE_9 { get; set; }
        public Byte[] FILE_9_DATA { get; set; }
        public String FILE_10 { get; set; }
        public Byte[] FILE_10_DATA { get; set; }
        
    }
}
