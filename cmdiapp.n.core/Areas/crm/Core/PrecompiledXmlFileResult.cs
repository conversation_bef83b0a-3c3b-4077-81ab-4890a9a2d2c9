﻿using System;
using System.Data;
using System.Linq;
using System.Text;
using System.Web;
using System.Web.Mvc;

namespace cmdiapp.n.core.Areas.crm.Core
{
    /// <summary>
    /// <see cref="ActionResult"/> that creates XML
    /// file from <see cref="DataSet"/> containing
    /// one table with one column containing rows of batched XML that has
    /// already been generated (e.g., from a SQL query).
    /// </summary>
    public class PrecompiledXmlFileResult : ActionResult
    {
        private readonly DataSet _dataSet;
        private readonly string _fileName;

        public PrecompiledXmlFileResult(DataSet dataSet, string fileName)
        {
            _dataSet = dataSet ?? throw new ArgumentNullException(nameof(dataSet));
            if (string.IsNullOrEmpty(fileName))
            {
                throw new ArgumentException($"{fileName} cannot be null or empty.", nameof(fileName));
            }
            _fileName = fileName;
        }

        public override void ExecuteResult(ControllerContext context)
        {
            HttpResponseBase response = context.HttpContext.Response;
            var fileBytes = GetBytesFromData(_dataSet);
            response.Clear();
            response.Buffer = true;
            response.ContentType = "text/xml";
            response.AddHeader("content-disposition", $"attachment;  filename={_fileName}");
            response.BinaryWrite(fileBytes);
            response.End();
        }

        private byte[] GetBytesFromData(DataSet dataSet)
        {
            // Assumes that there is only one table with one column containing XML result.
            if (dataSet.Tables.Count < 1) { return new byte[0]; }
            var table = dataSet.Tables[0];

            if ((table?.Rows?.Count ?? 0) < 1 || (table?.Columns?.Count ?? 0) < 1)
            {
                return new byte[0];
            }
            var rows = table.Rows.OfType<DataRow>();
            var xmlString = new StringBuilder($"<?xml version=\"1.0\" encoding=\"utf-8\"?>{Environment.NewLine}");
            if (_fileName.Contains("ILState_")) xmlString = new StringBuilder("");
            foreach (var row in rows)
            {
                xmlString.Append(row[0]?.ToString() ?? "");
            }
            return Encoding.UTF8.GetBytes(xmlString.ToString());
        }
    }
}