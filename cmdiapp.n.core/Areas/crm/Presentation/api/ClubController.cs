﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Net;
using System.Web.Http;

using Ninject;
using Ninject.Web.Mvc;

using cmdiapp.n.core.Domain.ViewModels;
using cmdiapp.n.core.Areas.crm.Core;
using cmdiapp.n.core.Areas.crm.Domain.Models;
using cmdiapp.n.core.Areas.crm.Domain.ViewModels;
using cmdiapp.n.core.Areas.crm.Domain.Data;
using cmdiapp.n.core.Areas.crm.Domain.Repositories;
using System.Text.RegularExpressions;
using cmdiapp.n.core.Library;
using cmdiapp.n.core.Presentation.Controllers;
using cmdiapp.n.core.Core.App_Start;
using System.Web.Http.OData;

namespace cmdiapp.n.core.Areas.crm.Presentation.Controllers.api
{
    ///  (controller)Club - Club
 
    [Authorize]
    [apiAuthorize(AccessElement = "cmdiapp.dms.People_Status", AccessLevel = "v")]
    public class ClubController : ApiController
    {
        private I_entity_crm _entity_crm;
        private I__DBFactory _dbFactory;

        public ClubController()
        {
            _entity_crm = I_entityManager_crm.getEntity();
            _dbFactory = NinjectMVC.kernel.Get<I__DBFactory>();
        }

        #region [ crm/api/ClubR ] GetBy PID
        //[HttpGet, Route("crm/api/ClubR/Get/{id}")]  // PID (Read)
        //public List<ClubR> ClubRbyPID(int id)
        //{
        //    string sql = "SELECT * FROM v_people_status WHERE PID = {0} ORDER BY PRIME DESC, RNEWDTE DESC";
        //    return _entity_crm.getContext().Database.SqlQuery<ClubR>(string.Format(sql, id)).ToList();
        //}

        [HttpGet, Route("crm/api/ClubR/Get")]  // PID (Read)
        [apiAuthorize(AccessElement = "cmdiapp.dms.People_Status", AccessLevel = "v")]
        public genericResponse ClubRbyPID(int id, int? page, int? pageSize, string filter = "All")
        {
            int _pageSize = (pageSize == null || pageSize.Value == 0 ? 10 : pageSize.Value);
            int _pageNo = (page == null || page.Value == 0 ? 1 : page.Value);
            string sortField = HttpContext.Current.Request.QueryString["sort[0][field]"];
            string sortDir = HttpContext.Current.Request.QueryString["sort[0][dir]"];
            string _sortOptions = "RNEWDTE DESC";
            if (!string.IsNullOrEmpty(sortField))
                _sortOptions = sortField;
            if (!string.IsNullOrEmpty(sortField) && !string.IsNullOrEmpty(sortDir))
                _sortOptions = _sortOptions + " " + sortDir;

            string sql = "SELECT * FROM v_people_status WHERE PID = {0} ";
            sql = string.Format(sql, id);

            if (filter == "Active")
            {
                sql += " AND CLOSED =0 ";
            }
            else if(filter == "Closed")
            {
                sql += " AND CLOSED =1 ";
            }
            else if (filter == "Primary")
            {
                sql += " AND PRIME =1 ";
            }

            var q = _dbFactory.getContext().Database.SqlQuery<ClubR_ext1>(String.Format("EXEC dbo.get_pagedDataSet '{0}', '{1}', {2}, {3}", sql, _sortOptions, _pageSize, _pageNo));

            IEnumerable<iItemType> results = q.Select(a => AutoMapper.Mapper.Map<ClubR_ext1, ClubR>(a)).ToList();

            if (results.Count() > 0)
            {
                return new genericResponse() { success = true, __count = q.FirstOrDefault().count_, results = results.ToList() };
            }
            else
            {
                return new genericResponse() { success = true, __count = 0 };
            }
        }
        #endregion

        #region [ crm/api/primaryClub ] GetBy PID
        [HttpGet, Route("crm/api/primaryClub/Get/{id}")]  // PID (Read)
        public ClubR primaryClubbyPID(int id)
        {
            string sql = "SELECT top 1 * FROM v_people_status WHERE PID = {0} AND PRIME = 1 AND CLOSED = 0 ORDER BY RNEWDTE DESC";
            return _entity_crm.getContext().Database.SqlQuery<ClubR>(string.Format(sql, id)).FirstOrDefault();
        }
        #endregion

        #region [ crm/api/Club ] GetBy jtClubid
        [HttpGet, Route("crm/api/Club/Get/{id}")]  // jtClub (Read)
        public jtCLUB GetByjtClubid(int id)
        {
            return _entity_crm.Single<jtCLUB>(a => a.JTCLUBID == id);
        }
        #endregion

        #region [ Retrieve list of club status based on clubid ]
        [HttpGet, Route("crm/api/ClubStatus")]
        public List<lkCLUBSTATR> ClubStatus(int? clubid)
        {
            string sql = "SELECT * FROM lkCLUBSTAT WHERE CLUBID = {0} ORDER BY DESCRIP";
            return _entity_crm.getContext().Database.SqlQuery<lkCLUBSTATR>(string.Format(sql, clubid)).ToList();
        }
        #endregion

        #region [ Retrieve list of ClubLabels ]
        [HttpGet, Route("crm/api/ClubLabels")]
        public List<string> ClubLabels()
        {
            List<string> result = new List<string>();
            result.Add(crmSession.club_solicitor_label());
            result.Add(crmSession.club_candidate_label());
            return result;
        }
        #endregion

        #region [ get clubSummary ]
        [HttpGet, Route("crm/api/clubSummary/{PID}")]
        public clubSummary getClubSummary(int PID)
        {
            string sql = "exec [dbo].[iGetClubSummaryByPid] " + PID.ToString();
            return _entity_crm.getContext().Database.SqlQuery<clubSummary>(sql).FirstOrDefault();
        }
        #endregion

        #region [ get clubSummary ]
        [HttpGet, Route("crm/api/getClubBySOLICITOR/{PID}")]
        public List<clubBySOLICITOR> getClubBySOLICITOR(int PID)
        {
            string sql = string.Format("SELECT * FROM [dbo].[clubBySOLICITORSummary] ({0}) ORDER BY [COUNT] DESC ", PID.ToString());
            return _entity_crm.getContext().Database.SqlQuery<clubBySOLICITOR>(sql).ToList();
        }
        #endregion

        #region [ get clubSummary ]
        [HttpGet, Route("crm/api/getClubByCANDIDATE/{PID}")]
        public List<clubByCANDIDATE> getClubByCANDIDATE(int PID)
        {
            string sql = string.Format("SELECT * FROM [dbo].[clubByCANDIDATESummary] ({0})  ORDER BY [COUNT] DESC ", PID.ToString());
            return _entity_crm.getContext().Database.SqlQuery<clubByCANDIDATE>(sql).ToList();
        }
        #endregion

        #region [ Save club record ]
        [HttpPost, Route("crm/api/Club/Save")]
        [apiAuthorize(AccessElement = "cmdiapp.dms.People_Status", AccessLevel = "e")]
        public genericResponse Save(jtCLUB _club)
        {
            try
            {
                int _jtclubid = _club.JTCLUBID;
                jtCLUB _record = _entity_crm.Single<jtCLUB>(a => a.JTCLUBID == _jtclubid);

                if (_record != null && _jtclubid != 0)
                {
                    // existing record
                    _record.updating_uid = crmSession.UID();
                    _record.PID = _club.PID;
                    _record.CLUBID = _club.CLUBID;
                    _record.PRIME = _club.PRIME;
                    _record.CLOSED = _club.CLOSED;
                    _record.cSTATUS = _club.cSTATUS;
                    _record.SOLICITOR = _club.SOLICITOR;
                    _record.CANDIDATE = _club.CANDIDATE;
                    _record.OVERRIDE = _club.OVERRIDE;
                    // Updated by Lydia on 11/6/2013
                    if (_club.RNEWDTE == null)
                        _record.RNEWDTE = null;
                    else
                        _record.RNEWDTE = Convert.ToDateTime(Convert.ToDateTime(_club.RNEWDTE).ToShortDateString());
                    _entity_crm.Update(_record);
                }
                else
                {
                    // new record
                    _record = new jtCLUB();
                    _record.updating_uid = crmSession.UID();
                    _record.PID = _club.PID;
                    _record.CLUBID = _club.CLUBID;
                    _record.PRIME = _club.PRIME;
                    _record.CLOSED = _club.CLOSED;
                    _record.cSTATUS = _club.cSTATUS;
                    _record.SOLICITOR = _club.SOLICITOR;
                    _record.CANDIDATE = _club.CANDIDATE;
                    _record.OVERRIDE = _club.OVERRIDE;
                    // Updated by Lydia on 11/6/2013
                    if (_club.RNEWDTE == null)
                        _record.RNEWDTE = null;
                    else
                        _record.RNEWDTE = Convert.ToDateTime(Convert.ToDateTime(_club.RNEWDTE).ToShortDateString());
                    _entity_crm.Add(_record);
                }
                _entity_crm.CommitChanges();
                _jtclubid = _record.JTCLUBID;

                if (_jtclubid <= 0)
                {
                    List<iItemType> _dataset = new List<iItemType>();
                    _dataset.Add(_club);

                    genericResponse _response = new genericResponse() { success = false, __count = _dataset.Count, results = _dataset.ToList() };
                    return _response;
                }
                else
                {
                    _club = _entity_crm.Single<jtCLUB>(a => a.JTCLUBID == _jtclubid);

                    List<iItemType> _dataset = new List<iItemType>();
                    _dataset.Add(_club);

                    genericResponse _response = new genericResponse() { success = true, __count = _dataset.Count, results = _dataset.ToList() };
                    return _response;
                }
            }
            catch
            {
                genericResponse _response = new genericResponse() { success = false, __count = 0 };
                return _response;
            }
        }
        #endregion

        #region [ crm/api/Club ] Delete
        [HttpPost, Route("crm/api/Club/Delete")]
        [apiAuthorize(AccessElement = "cmdiapp.dms.People_Status", AccessLevel = "d")]
        public genericResponse Delete(jtCLUB _jtclub)
        {
            try
            {
                int _jtclubid = _jtclub.JTCLUBID;

                // log for history
                string _sql = "Insert into zWebAppLog (UPDTYPE,TABLENAME,KEYFIELD,KEYVALUE,UID) values " +
                    "('D','jtCLUB','jtCLUBID', {0}, {1})";
                _entity_crm.getContext().Database.ExecuteSqlCommand(_sql, _jtclubid, crmSession.UID());

                _entity_crm.Delete<jtCLUB>(a => a.JTCLUBID == _jtclubid);
                _entity_crm.CommitChanges();

                genericResponse _response = new genericResponse() { success = true, __count = 0 };
                return _response;
            }
            catch
            {
                genericResponse _response = new genericResponse() { success = false, __count = 0 };
                return _response;
            }
        }
        #endregion

        #region [ Retrieve list of club codes ]
        [HttpGet, Route("crm/api/ClubCodes")]
        [EnableQuery]
        public IQueryable<pmCLUB> ClubCodes()
        {
            return _entity_crm.All<pmCLUB>().AsQueryable();
        }
        #endregion
    }
}
