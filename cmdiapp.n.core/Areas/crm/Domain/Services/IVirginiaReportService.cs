﻿using cmdiapp.n.core.Areas.crm.Domain.Models;
using cmdiapp.n.core.Areas.crm.ViewModels;
using cmdiapp.n.core.Domain.ViewModels;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace cmdiapp.n.core.Areas.crm.Domain.Services
{
    public interface IVirginiaReportService
    {
        Task<List<VirginiaReportDisplay>> GetAllAsync(int filerId);

        Task<List<VirginiaReportType>> GetReportTypesAsync();

        genericResponse Delete(int id);
    }
}
