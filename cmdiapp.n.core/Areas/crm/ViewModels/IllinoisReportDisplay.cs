﻿using System;
using System.Runtime.Serialization;

namespace cmdiapp.n.core.Areas.crm.ViewModels
{
    [DataContract]
    public class IllinoisReportDisplay
    {
        [DataMember(Name = "id")]
        public int Id { get; set; }

        [DataMember(Name = "filerInfoId")]
        public int FilerInfoId { get; set; }

        [DataMember(Name = "filingDate")]
        public DateTime FilingDate { get; set; }

        [DataMember(Name = "reportPeriodStart")]
        public DateTime ReportPeriodStart { get; set; }

        [DataMember(Name = "reportPeriodEnd")]
        public DateTime ReportPeriodEnd { get; set; }

        [DataMember(Name = "TotalReceipts")]
        public decimal? TotalReceipts { get; set; }

        [DataMember(Name = "TotalInkind")]
        public decimal? TotalInkind { get; set; }

        [DataMember(Name = "TotalExpenditures")]
        public decimal TotalExpenditures { get; set; }

        [DataMember(Name = "TotalDebts")]
        public decimal TotalDebts { get; set; }

        [DataMember(Name = "Invest")]
        public decimal Invest { get; set; }

        [DataMember(Name = "EndFundsAvail")]
        public decimal EndFundsAvail { get; set; }

        [DataMember(Name = "isAmendment")]
        public bool isAmendment { get; set; }

        [DataMember(Name = "reasonForAmendment")]
        public string reasonForAmendment { get; set; }

        [DataMember(Name = "createdOn")]
        public DateTime CreatedOn { get; set; }

        [DataMember(Name = "reportType")]
        public string ReportType { get; set; }

        [DataMember(Name = "reportTypeCode")]
        public string ReportTypeCode { get; set; }
    }
}