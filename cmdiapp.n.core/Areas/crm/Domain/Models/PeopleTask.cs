﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Data;
using System.Linq;
using System.Web.Mvc;
using System.Data.Linq;
using System.ComponentModel.DataAnnotations.Schema;

using cmdiapp.n.core.Domain.ViewModels;
using cmdiapp.n.core.Areas.crm.Domain.Data;

namespace cmdiapp.n.core.Areas.crm.Domain.Models
{
    public class ScheduleBy : _entityBase_crm, iItemType
    {
        public Int16 uid { get; set; }

        public string name { get; set; }

        public string userid { get; set; }

        public bool IsSelected { get; set; }
        
    }

    public class ScheduleFor : _entityBase_crm, iItemType
    {
        public Int16 uid { get; set; }

        public string name { get; set; }

        public string name2 { get; set; }

        public string userid { get; set; }

        public bool IsSelected { get; set; }

    }

    public class ReassinToUsers : _entityBase_crm, iItemType
    {
        public Int16 ssusruid { get; set; }

        public string name { get; set; }

        public string name2 { get; set; }

        public string userid { get; set; }

    }

    public class UserGroupDescription : _entityBase_crm, iItemType
    {
        public string GROUPDESC { get; set; }
    }
        
    public class TaskSearch
    {
        public List<ScheduleBy> scheduleByUsers { get; set; }

        public List<ScheduleFor> scheduleForUsers { get; set; }

         public bool IsDueDate { get; set; }

         public bool IsIncludeAllDueDates { get; set; }

         public bool IsIncludeAllCompDates { get; set; }

         public bool IsIncludeAllOpenTasks { get; set; }

         public bool IsIncludeAllCompTasks { get; set; }

        public DateTime? DUEDTEFR { get; set; }

        public DateTime? DUEDTETO { get; set; }

        public DateTime? COMPDTEFR { get; set; }

        public DateTime? COMPDTETO { get; set; }

        public string groupType { get; set; }

        public string pVersion { get; set; }

        public string[] SELFLAGS { get; set; }

        public string STATENAME { get; set; }

        public string[] SELKWRDS { get; set; }
    }
    
    public class ToBeCompleted
    {
        public string[] selectedActIds { get; set; }
    }

    public class TaskSearchResult : _entityBase_crm, iItemType
    {
        public int ACTID { get; set; }

        public DateTime? DUEBY { get; set; }

        //public string DUEBYs
        //{
        //    get
        //    {
        //        return string.Format("{0:MM/dd/yyyy}", DUEBY);
        //    }
        //}

        public string SCHEDFOR { get; set; }

        public string TASKTYPE { get; set; }
                
        public string NOTE { get; set; }

        public string SUBJECT { get; set; }
        
        public int PID { get; set; }

        public string NAME { get; set; }

        public string PREFIX { get; set; }

        public string FNAME { get; set; }

        public string MNAME { get; set; }

        public string LNAME { get; set; }
        
        public string SUFFIX { get; set; }

        public string STREET { get; set; }

        public string ADDR1 { get; set; }

        public string ADDR2 { get; set; }

        public string CITY { get; set; }

        public string STATE { get; set; }

        public string ZIP { get; set; }

        public string HMPHN { get; set; }

        public string BSPHN { get; set; }

        public string CELL { get; set; }

        public DateTime? SCHEDON { get; set; }

        //public string SCHEDONs
        //{
        //    get
        //    {
        //        return string.Format("{0:MM/dd/yyyy}", SCHEDON);
        //    }
        //}

        public string SCHEDBY { get; set; }

        public bool DONE { get; set; }

        public DateTime? DONEON { get; set; }

        //public string DONEONs
        //{
        //    get
        //    {
        //        return string.Format("{0:MM/dd/yyyy}", DONEON);
        //    }
        //}

    }
    
    public class UserTasks : _entityBase_crm, iItemType
    {
        public int ACTID { get; set; }

        public DateTime? DUEBY { get; set; }
                
        public string SCHEDFOR { get; set; }

        public string TASKTYPE { get; set; }

        public string NOTE { get; set; }

        public string SUBJECT { get; set; }

        public int PID { get; set; }

        public string NAME { get; set; }

        public string CITY { get; set; }

        public string STATE { get; set; }

        public string HMPHN { get; set; }

        public string BSPHN { get; set; }

        public string CELL { get; set; }

        public DateTime? SCHEDON { get; set; }

        public DateTime? NEXTDAY { get; set; }

        public string SCHEDBY { get; set; }

        public Byte DONE { get; set; }
        
    }

    public class genericResponseTask
    {
        public bool success { get; set; }
        public string message { get; set; }
        public string returneddata { get; set; }
        public string uidBy { get; set; }
        public string uidFor { get; set; }

        public string SELFLAGS { get; set; }
        public string STATENAME { get; set; }
        public string SELKWRDS { get; set; }
        
        public bool IsIncludeAllOpenTasks { get; set; }
        public bool IsIncludeAllCompTasks { get; set; }
        public bool IsDueDate { get; set; }
        public string groupType { get; set; }

        public DateTime? DUEDTEFR { get; set; }
        public DateTime? DUEDTETO { get; set; }
        public DateTime? COMPDTEFR { get; set; }
        public DateTime? COMPDTETO { get; set; }

        public int __count { get; set; }
        public List<iItemType> results { get; set; }
    }


    [NotMapped]
    public class TaskSearchResult_ext1 : TaskSearchResult
    {
        public int count_ { get; set; }

        public Int64 rowNo { get; set; }
    }

    [NotMapped]
    public class UserTasks_ext1 : UserTasks
    {
        public int count_ { get; set; }

        public Int64 rowNo { get; set; }
    }

}