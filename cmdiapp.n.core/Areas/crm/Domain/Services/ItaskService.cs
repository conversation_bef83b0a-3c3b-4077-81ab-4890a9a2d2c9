﻿using System.Collections.Generic;
using System.Web.Mvc;

using cmdiapp.n.core.Areas.crm.Domain.Models;
using cmdiapp.n.core.Areas.crm.Domain.ViewModels;
using cmdiapp.n.core.Areas.crm.Domain.Repositories;

namespace cmdiapp.n.core.Areas.crm.Domain.Services
{
    public interface ItaskService
    {
        List<TaskSearchResult_ext1> get_allTasks(string where, string orderBy, int pageSize, int pageNo);
        List<UserTasks_ext1> get_MyTasks(string where, string orderBy, int pageSize, int pageNo);
        List<UserTasks> get_allMyTasks(string where);
        
        string get_GroupDescription(short? userAppUID);
        int reassign_TaskItems(string _sql);
        int completeMassTasks(string _sql);
    }
}