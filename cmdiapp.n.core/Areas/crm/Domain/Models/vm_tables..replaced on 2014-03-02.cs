﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Data;
using System.Linq;
using System.Web.Mvc;
using System.Data.Linq;
using System.ComponentModel.DataAnnotations.Schema;

namespace cmdiapp.n.core.Areas.crm.Domain.Models
{
    [Table("x_vm_demographic")]
    public class vm_Demographic
    {
        [Column]
        [Key]
        public int voterSeq { get; set; }

        [Column]
        public string familyID { get; set; }

        [Column]
        public string mailingFamilyID { get; set; }

        [Column]
        public string TELEPHONE { get; set; }

        [Column]
        public byte TELEPHONEConfidenceCode { get; set; }

        [Column]
        public bool TELEPHONECellFlag { get; set; }

        [Column]
        public DateTime DOB { get; set; }

        [Column]
        public int Voters_Age { get; set; }

        [Column]
        public DateTime RegistrationDate { get; set; }

        [Column]
        public string Ethnic_Description { get; set; }

        [Column]
        public string Religion { get; set; }

        [Column]
        public string Party { get; set; }

        [Column]
        public string Gender { get; set; }

        [Column]
        public string MaritalStatus { get; set; }

        [Column]
        public byte HHCount { get; set; }

        [Column]
        public string HHParties { get; set; }

        [Column]
        public string HHGender { get; set; }

        [Column]
        public byte mailingHHCount { get; set; }

        [Column]
        public string mailingHHParties { get; set; }

        [Column]
        public string mailingHHGender { get; set; }

        [Column]
        public string CD2001 { get; set; }

        [Column]
        public string CD2011 { get; set; }

        [Column]
        public string SD2001 { get; set; }

        [Column]
        public string SD2011 { get; set; }

        [Column]
        public string LD2001 { get; set; }

        [Column]
        public string LS2011 { get; set; }

        [Column]
        public string County { get; set; }

        [Column]
        public string Precinct { get; set; }

        [Column]
        public int VoteFrequency { get; set; }

        [Column]
        public string PresenceOfChildrenCode { get; set; }

        [Column]
        public string ISPSA { get; set; }

        [Column]
        public string DwellingType { get; set; }

        [Column]
        public string EstimatedIncome { get; set; }

        [Column]
        public string Education { get; set; }

        [Column]
        public string OccupationGroup { get; set; }

        [Column]
        public string Occupation { get; set; }

        [Column]
        public string ReligiousContributorInHome { get; set; }

        [Column]
        public string PoliticalContributerInHome { get; set; }
    }

    [Table("x_vm_voter")]
    public class vm_Voter
    {
        [Column]
        [Key]
         public int voterSeq { get; set; }

        [Column]
        public string voterId { get; set; }

        [Column]
        public string voterStateId { get; set; }

        [Column]
        public string FNAME { get; set; }
         
        [Column]
        public string MNAME { get; set; }
         
        [Column]
        public string LNAME { get; set; }
         
        [Column]
        public string SUFFIX { get; set; }
         
        [Column]
        public string CountyFIPS { get; set; }

        [Column]
        public string ADDR1 { get; set; }

        [Column]
        public string ADDR2 { get; set; }
         
        [Column]
        public string CITY { get; set; }

        [Column]
        public string STATE { get; set; }

        [Column]
        public string ZIP { get; set; }

        [Column]
        public string censusTract { get; set; }

        [Column]
        public string censusBlockGroup { get; set; }

        [Column]
        public string censusBlock { get; set; }
         
        [Column]
        public string mailingADDR1 { get; set; }

        [Column]
        public string mailingADDR2 { get; set; }

        [Column]
        public string mailingCITY { get; set; }

        [Column]
        public string mailingSTATE { get; set; }

        [Column]
        public string mailingZIP { get; set; }

    }
    
    [Table("x_vm_listInfo")]
    public class vm_ListInfo
    {
        [Column]
        [Key]
        public int listId { get; set; }

        [Column]
        public int recCnt { get; set; }

        [Column]
        public DateTime fileRecvDate { get; set; }

        [Column]
        public DateTime statusDate { get; set; }

        [Column]
        public string status { get; set; }

        [Column]
        public string metaData { get; set; }

        [Column]
        public string LandL_listId { get; set; }

        [Column]
        public string LandL_uuId { get; set; }

        [Column]
        public string custName { get; set; }

        [Column]
        public string listName { get; set; }

        [Column]
        public string userName { get; set; }

        [Column]
        public int recCount_shouldBe { get; set; }

        [Column]
        public int recPurchased { get; set; }

        [Column]
        public decimal amountDue { get; set; }
    }
    
    [Table("x_vm_voteHistory")]
    public class vm_VoteHistory
    {
        [Column]
        [Key]
        public int voterSeq { get; set; }

        [Column]
        [Key]
        public string ElectionType { get; set; }
        
        [Column]
        [Key]
        public DateTime ElectionDate { get; set; }

        [Column]
        public string Voting { get; set; }
        
    }

    [Table("x_vm_jt_list_voter")]
    public class vm_jt_Listvoter
    {
        [Column]
        [Key]
        public int listId { get; set; }
        [ForeignKey("listId")]
        public virtual vm_ListInfo vm_ListInfo { get; set; }

        [Column]
        [Key]
        public int voterSeq { get; set; }
        [ForeignKey("voterSeq")]
        public virtual vm_Voter vm_Voter { get; set; }

        [Column]
        public int Walking_List_Sequence { get; set; }

    }
    
}