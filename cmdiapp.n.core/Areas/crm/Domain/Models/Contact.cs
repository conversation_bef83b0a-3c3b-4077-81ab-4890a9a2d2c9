﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Data;
using System.Linq;
using System.Web.Mvc;
using System.Data.Linq;
using System.ComponentModel.DataAnnotations.Schema;

using cmdiapp.n.core.Domain.ViewModels;
using cmdiapp.n.core.Areas.crm.Domain.Data;

namespace cmdiapp.n.core.Areas.crm.Domain.Models
{
    [Table("Contact")]
    public class Contact : _entityBase_crm, iItemType
    {
        [Column]
        [Key]
        public int CONTACTID{get;set;}

        [Column]
        public int PID	{get;set;}

        [Column]
        public string PREFIX{get;set;}

        [Column]
        public string FNAME	{get;set;}

        [Column]
        public string MNAME	{get;set;}

        [Column]
        public string LNAME	{get;set;}

        [Column]
        public string SUFFIX	{get;set;}

        [Column]
        public string ORGANIZATION	{get;set;}

        [Column]
        public string TITLE	{get;set;}

        [Column]
        public string STREET	{get;set;}

        [Column]
        public string ADDR1	{get;set;}

        [Column]
        public string ADDR2	{get;set;}

        [Column]
        public string CITY	{get;set;}

        [Column]
        public string STATE	{get;set;}

        [Column]
        public string ZIP	{get;set;}

        [Column]
        public string PLUS4	{get;set;}

        [Column]
        public string EMAIL	{get;set;}

        [Column]
        public string HMPHN	{get;set;}

        [Column]
        public string BSPHN	{get;set;}

        [Column]
        public string FAX	{get;set;}

        [Column]
        public string CELL	{get;set;}

        [Column]
        public string COMMENT	{get;set;}

        [Column]
        public  Boolean PRIME	{get;set;}

        [Column]
        public Nullable<DateTime> UPDATEDON { get; set; }

        [Column]
        public string INFSALUT	{get;set;}

        [Column]
        public string ASSISTANT	{get;set;}

        [Column("_updating_uid")]
        public Nullable<short> updating_uid { get; set; }

        [Column("UID")]
        public Nullable<short> UID { get; set; }

        [Column]
        public Boolean INACTIVE { get; set; }

        [Column]
        public DateTime? INACTIVEDTE { get; set; }
    }
    
    public class ContactFlags
    {
        public int ContactID { get; set; }
        public List<string> ContactFlag { get; set; }
    }
}