﻿using System;
using System.Data.Entity;

namespace cmdiapp.n.core.Areas.datax.Domain.Data
{
    public class DataxDBInitializer : IDatabaseInitializer<_dataxDBContext>
    {
        public void InitializeDatabase(_dataxDBContext context)
        {
            if (!context.Database.Exists())
            {
                throw new NotSupportedException("The datax database does not exist.");
            }
        }
    }
}