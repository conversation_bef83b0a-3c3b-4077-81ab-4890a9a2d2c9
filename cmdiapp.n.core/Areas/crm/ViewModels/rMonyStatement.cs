﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Data;
using System.Linq;
using System.Web.Mvc;
using System.Data.Linq;

namespace cmdiapp.n.core.Areas.crm.Domain.ViewModels
{
    // SQL Function (db.iGenStatement)
    public class rMonyStatement
    {
        public int PID { get; set; }

        public string NAME { get; set; }

        public string ADDRESS { get; set; }

        public int GROUPNO { get; set; }

        public int ORDERNO { get; set; }

        public string ROOT { get; set; }

        public string DONOR { get; set; }

        [Key]
        public int MID { get; set; }

        public Nullable<DateTime> BATCHDTE { get; set; }

        public string ADJFLAG { get; set; }

        public string ADJDESC { get; set; }

        public DateTime? ADJDTE { get; set; }

        public string ACCTCODE { get; set; }

        public decimal? OAMT { get; set; }

        public decimal? ADJAMT { get; set; }

        public decimal? AMT { get; set; }

        public string MINE { get; set; }
    }
}