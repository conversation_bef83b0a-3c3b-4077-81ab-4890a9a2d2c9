﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Data;
using System.Linq;
using System.Web.Mvc;
using System.Data.Linq;
using System.ComponentModel.DataAnnotations.Schema;
using cmdiapp.n.core.Domain.ViewModels;
using cmdiapp.n.core.Areas.crm.Domain.Data;

namespace cmdiapp.n.core.Areas.crm.Domain.Models
{
    public class donateNow : _entityBase_crm, iItemType
    {
        [Key]
        [Column]
        public int PID { get; set; }
        [Column]
        public string PEOTYPE { get; set; }
        [Column]
        public string PEOCODE { get; set; }
        [Column]
        public int? TRACKNO { get; set; }
        [Column]
        public string PREFIX { get; set; }
        [Column]
        public string FNAME { get; set; }
        [Column]
        public string MNAME { get; set; }
        [Column]
        public string LNAME { get; set; }
        [Column]
        public string SUFFIX { get; set; }
        [Column]
        public string FULLNAME { get; set; }
        [Column]
        public string INDUSTRY { get; set; }
        [Column]
        public string EMPLOYER { get; set; }
        [Column]
        public string OCCUPATION { get; set; }
        [Column]
        public string SALUTATION { get; set; }
        [Column]
        public string INFSALUT { get; set; }
        [Column]
        public string PRESSALUT { get; set; }
        [Column]
        public string MAILNAME { get; set; }
        [Column]
        public string SPOUSENAME { get; set; }
        [Column]
        public string ASSISTANT { get; set; }
        [Column]
        public string TITLE { get; set; }
        [Column]
        public string FECCMTEID { get; set; }
        [Column]
        public Boolean? PRIMEMAIL { get; set; }
        [Column]
        public string ADDRTYPE { get; set; } // H: Home, B: Business
        [Column]
        public string STREET { get; set; }
        [Column]
        public string ADDR1 { get; set; }
        [Column]
        public string ADDR2 { get; set; }
        [Column]
        public string CITY { get; set; }
        [Column]
        public string STATE { get; set; }
        [Column]
        public string ZIP { get; set; }
        [Column]
        public string PLUS4 { get; set; }
        [Column]
        public string FULLADDR { get; set; }
        [Column]
        public string STATECOUNTY { get; set; }
        [Column]
        public string HMPH { get; set; }
        [Column]
        public string BUSPH { get; set; }
        [Column]
        public string CELLPH { get; set; }
        [Column]
        public string EMAIL { get; set; }
        [Column]
        public string URL { get; set; }
        [Column]
        public string FAX { get; set; }

        [Column("_updating_uid")]
        public Int16? updating_uid { get; set; }
        [Column]
        public string FACEBOOKID { get; set; }
        [Column]
        public string TWITTERID { get; set; }
        [Column]
        public string BIO { get; set; }

        [NotMapped]
        public DateTime giftdte { get; set; }
        [NotMapped]
        public Decimal? giftamt { get; set; }
        [NotMapped]
        public string cardno { get; set; }
        [NotMapped]
        public string expmo { get; set; }
        [NotMapped]
        public string expyr { get; set; }
        [NotMapped]
        public string cvv { get; set; }
        [NotMapped]
        public string srcecode { get; set; }

        [NotMapped]
        public string SRCECODE { get; set; }
        
        [NotMapped]
        public string cctrackno { get; set; }
        [NotMapped]
        //public Nullable<int> OneTime_Monthly { get; set; } //true = one time, false monthly
        public string RECURFREQ { get; set; }
        [NotMapped]
        public string COMMENT { get; set; }
        [NotMapped]
        public string fundcode { get; set; }
        [NotMapped]
        public Nullable<short> status { get; set; }
        [NotMapped]
        public string token { get; set; }
        [NotMapped]
        public string callercode { get; set; }
        [NotMapped]
        public Nullable<int> logid { get; set; }
        [NotMapped]
        public Nullable<int> webgiftid { get; set; }
        [NotMapped]
        public string TYPEID { get; set; }
        [NotMapped]
        public Nullable<DateTime> RECURENDDTE { get; set; }
        [NotMapped]
        public string profileid { get; set; }
        [NotMapped]
        public string BATCHNO { get; set; }
        [NotMapped]
        public DateTime BATCHDTE { get; set; }
        [NotMapped]
        public string CCRESPMSG { get; set; }
        [NotMapped]
        public string CCAUTHCODE { get; set; }
        [NotMapped]
        public string CCREFNO { get; set; }
    }

    public class webGIFTLOG : _entityBase_crm, iItemType
    {
        [Key]
        [Column]
        public int LOGID { get; set; }

        [Column]
        public int RECID { get; set; }

        [Column]
        public Int16 TYPEID { get; set; }

        [Column]
        public string CALLERCODE { get; set; }

        [Column]
        public string TOKEN { get; set; }

        [Column]
        public string PREFIX { get; set; }

        [Column]
        public string FNAME { get; set; }

        [Column]
        public string MNAME { get; set; }

        [Column]
        public string LNAME { get; set; }

        [Column]
        public string SUFFIX { get; set; }

        [Column]
        public string EMPLOYER { get; set; }

        [Column]
        public string OCCUPATION { get; set; }

        [Column]
        public string HMPHN { get; set; }

        [Column]
        public string WKPHN { get; set; }

        [Column]
        public string CELLPHN { get; set; }

        [Column]
        public string EMAIL { get; set; }

        [Column]
        public string ADDRTYPE { get; set; }

        [Column]
        public string STREET { get; set; }

        [Column]
        public string ADDR1 { get; set; }

        [Column]
        public string ADDR2 { get; set; }

        [Column]
        public string CITY { get; set; }

        [Column]
        public string STATE { get; set; }

        [Column]
        public string ZIP { get; set; }

        [Column]
        public string FUNDCODE { get; set; }

        [Column]
        public string SRCECODE { get; set; }

        [Column]
        public Nullable<int> TRACKNO { get; set; }

        [Column]
        public string COMMENT { get; set; }

        [Column]
        public string RECURFREQ { get; set; }

        [Column]
        public Nullable<DateTime> GIFTDTE { get; set; }

        [Column]
        public Nullable<decimal> GIFTAMT { get; set; }

        [Column]
        public string CARDNO { get; set; }

        [Column]
        public string EXPMO { get; set; }

        [Column]
        public string EXPYR { get; set; }

        [Column]
        public Nullable<int> WEBGIFTID { get; set; }

        [Column]
        public Nullable<Int16> STATUSID { get; set; }

        [Column]
        public Nullable<Int16> UID { get; set; }

        [Column]
        public Nullable<DateTime> CREATEDON { get; set; }

        [Column]
        public Nullable<DateTime> RECURENDDTE { get; set; }

        [Column]
        public string PROFILEID { get; set; }

        [Column]
        public string APVCODE { get; set; }

        [Column]
        public string RESPMSG { get; set; }

        [Column]
        public string AVSRESP { get; set; }

        [Column]
        public string IDSTAMP { get; set; }
    }

    public class DECLINED : _entityBase_crm, iItemType
    {
        [Key]
        [Column]
        public int DECLINEDID { get; set; }
        [Column]
        public string BATCHNO { get; set; }
        [Column]
        public string BATCHDTE { get; set; }
        [Column]
        public string SRCECODE { get; set; }
        [Column]
        public decimal AMT { get; set; }
        [Column]
        public string CARDNO { get; set; }
        [Column]
        public string EXPMO { get; set; }
        [Column]
        public string EXPYR { get; set; }
        [Column]
        public string RESPMSG { get; set; }
        [Column]
        public byte RECEVIED { get; set; }
        [Column]
        public int MID { get; set; }
        [Column]
        public int FUNDID { get; set; }
    }

    public class DonateViewModel
    {
        public int logid { get; set; }
        public string ticket { get; set; }
        public string token { get; set; }
        public string callercode { get; set; }
        public int pid { get; set; }
        public string prefix { get; set; }
        public string fname { get; set; }
        public string mname { get; set; }
        public string lname { get; set; }
        public string suffix { get; set; }
        public string addrtype { get; set; }
        public string street { get; set; }
        public string addr1 { get; set; }
        public string city { get; set; }
        public string state { get; set; }
        public string zip { get; set; }
        public decimal amt { get; set; }
        public string cardno { get; set; }
        public string expmo { get; set; }
        public string expyr { get; set; }
        public string cvv { get; set; }
        public string fundcode { get; set; }
        public string srcecode { get; set; }
        public string trackno { get; set; }
        public string comment { get; set; }
        public string recurfreq { get; set; }
        public string recurenddte { get; set; }
        public string cancel_url { get; set; }
        public string return_url { get; set; }
    }
}