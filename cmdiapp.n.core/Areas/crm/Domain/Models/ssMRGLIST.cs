﻿/*
 *  [2013-09-16 by <PERSON><PERSON>]
 */
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using cmdiapp.n.core.Domain.ViewModels;

namespace cmdiapp.n.core.Areas.crm.Domain.Models
{
    public class ssMRGLIST : iItemType
    {
        public string ID { get; set; }
        public string TABLEDESC { get; set; }
        public Int16 MRGTYPE { get; set; }
        public Boolean SHOWLIST { get; set; }
        public Boolean ALLWMOVE { get; set; }
        public Boolean ALLWDELETE { get; set; }
        public string TABLENAME { get; set; }
        public string PIDFIELD { get; set; }
        public string UNQIDFIELD { get; set; }
        public string IDFIELD { get; set; }
        public string DISPSQL { get; set; }

    }
    public class ssMRGLIST_ext : ssMRGLIST
    {
        public int recordCountForPID1 { get; set; }
        public int recordCountForPID2 { get; set; }

    }
    public class SP_response
    {
        //output of MOVEIT [stored procedure]
        public Boolean SUCCEED { get; set; }
        //output of iCANADDFLAG[stored procedure]
        public Boolean YES { get; set; }
        public string MSG { get; set; }
    }
    public class PEOCODETYPEID
    {
        public int PID { get; set; }
        public string PEOCODE { get; set; }
        public string PEOTYPE { get; set; }
    }
}