﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Web.Http;
using System.IO;

using cmdiapp.n.core.Core;
using cmdiapp.n.core.Domain.Data;
using cmdiapp.n.core.Domain.Models;

using cmdiapp.n.core.Areas.crm.Core;
using cmdiapp.n.core.Areas.crm.Domain.Models;
using cmdiapp.n.core.Areas.crm.Domain.Data;
using cmdiapp.n.core.Library;

namespace cmdiapp.n.core.Areas.apix.Presentation.api
{
    public class crmPeopleController : ApiController
    {
        public crmPeopleController()
        {
        }

        #region [[ People Photo ]] v2/crm/People/{pid}/Photo/{token}/{dataId}
        // eg. https://apix.cmdi.com/v2/crm/people/74178535/photo/c8ce6c91a96211fa0c43cf716593658a7895efd6/1
        [HttpGet, Route("v2/crm/People/{pid}/Photo/{token}/{dataId}")]
        public HttpResponseMessage Photo(int pid, string token, int dataId)
        {
            #region [ Validation ]
            // Input validation
            if (string.IsNullOrEmpty(token) || dataId<=0 || pid<=0)
                throw new HttpResponseException(HttpStatusCode.BadRequest); // 400

            // Token validation
            if (!cl_apiCallToken.validToken(token, dataId))
                throw new HttpResponseException(HttpStatusCode.BadRequest); // 400

            // Crimson DB Context
            System.Data.Entity.DbContext _dbContext = cl_apiCallToken.crmDBContext(dataId);
            if (_dbContext == null)
                throw new HttpResponseException(HttpStatusCode.BadRequest); // 400

            // If a matching PEOPLE exists
            var q = _dbContext.Database.SqlQuery<peoplePicture>(String.Format("SELECT TOP 1 pid, picture FROM PEOPLE WHERE PID={0}", pid));
            peoplePicture _result = q.FirstOrDefault();
            if (_result == null )
                throw new HttpResponseException(HttpStatusCode.BadRequest); // 400
            #endregion

            HttpResponseMessage response = new HttpResponseMessage(HttpStatusCode.OK);

            #region [ ### Has Picture ### ]
            if (_result.PICTURE != null)
            {
                var memoryStream = new MemoryStream(_result.PICTURE);
                response.Content = new StreamContent(memoryStream);
            }
            #endregion

            #region [ ### No Picture ### ]
            else
            {
                string anonymousPic_path = Path.Combine(System.Web.Hosting.HostingEnvironment.MapPath(@"~/contents/_shared/image/Anonymous-Male.jpg"));

                var fileStream = new System.IO.FileStream(anonymousPic_path, System.IO.FileMode.Open, FileAccess.Read, FileShare.ReadWrite);
                response.Content = new StreamContent(fileStream);
            }
            #endregion

            #region [ Content Type and Cache ]
            response.Content.Headers.ContentType = new System.Net.Http.Headers.MediaTypeHeaderValue("image/jpeg");
            response.Headers.CacheControl = new System.Net.Http.Headers.CacheControlHeaderValue
            {
                MustRevalidate = true,
                Private = true,
                MaxAge = TimeSpan.FromHours(1),
            };
            #endregion

            return response;
        }
        #endregion

        #region [[ QR Code Generator ]] v2/token/QR
        // eg. http://apix.cmdi2.com/v2/crm/people/QRCode/0x01000000f6713c4b65b1c0575717a673165fec6951ca88fb0f374057efd79e94918b42efffec73adc00493424e46d8716b07c566636ed594e5dfecca9a5a4f76282d52b725fd5034e69db3ed5dc4085ef3fb98373265fb00c7f98f97dff17b67618c86e7daa68cdf5f955dbebda9344b56d3dc1a59de2191ecd4d0e61aecb1b4859b951c48aaf432d578da8fbde2698a42943f3ca7493fdc62db40db8afac4d172f6b15f6624fee6d9d2f6128dba16b5ae8a91e1a35322bd732175347f9e3a0aa6fb5d23c07e39ee8a128383332d2dd43cd43fb6ac8b551debba0fded9c915c34c8e989b2b80290e3b1c226006021f00eec7a096f6a34b5a23316afaf548c1d29a74ba7345c23d78559e8ccb802bcc0ec67ed6c9f4b376acc44457e17b61dfef662c90fce235282ebb6c2663fb4a2e34f9fb77cfac9807bbe969d9a4aa1740a624875c8dd2640e62b073fa81bbb18e23d4cf2b7871fb2672142e74efd3735b5d9dc3882d
        [HttpGet, Route("v2/crm/People/QRCode/{dataToken}")]
        public HttpResponseMessage QRCode(string dataToken)
        {
            var response = Request.CreateResponse(HttpStatusCode.Found);
            string uri = string.Format("http://chart.apis.google.com/chart?cht=qr&chs=200x200&chl={0}", dataToken);
            response.Headers.Location = new Uri(uri);
            return response;
        }
        #endregion

        #region [[ Get Profile from Id Token ]]
        [HttpGet, Route("v2/crm/People/token/i/profile/{apikey}/{idToken}")]
        public profileA getProfileFromIdToken(string apiKey, string idToken)
        {   // eg. http://apix.cmdi2.com/v2/crm/People/token/i/profile/a927c078309eb4ed40888ec66357044fe83a6192/43af5f4a-10c0-c171-a088-0f8539eb86d7
            #region [ 1. Verify the API Key ]
            apiCallToken _token = cl_apiCallToken.validateToken(apiKey, -1);

            if (_token.invalidToken)
                throw new HttpResponseException(HttpStatusCode.Unauthorized);
            #endregion

            #region [ 2. Retrieve IDs ]
            int[] Ids = Library.crimsonUtil.crmPeopleTokenDecode1(idToken);
            if (!(Ids.Count() == 2 && Ids[0] > 0 && Ids[1] > 0))
                throw new HttpResponseException(HttpStatusCode.Unauthorized);
            #endregion

            #region [ 3. Get CrimsonDB by ProjectId ]
            System.Data.Entity.DbContext _dbContext = cl_apiCallToken.crmDBContext(Ids[0]);
            if (_dbContext == null)
                throw new HttpResponseException(HttpStatusCode.BadRequest); // 400
            #endregion

            #region [ 4. Get Profile by PID ]
            return _dbContext.Database.SqlQuery<profileA>(string.Format("SELECT * FROM dbo.profile_base({0})", Ids[1])).FirstOrDefault();
            #endregion
        }
        #endregion

        #region [[ Find matching Crimson records ]]
        [HttpGet, Route("v2/crm/People/Find")]
        public List<profile> Find(string apiKey, int dataId,
                        string fname = "", string mname = "", string lname = "", string suffix = "",
                        string email = "", string hphone = "", string cphone = "",
                        string addr1 = "", string addr2 = "", string city = "", string state = "", string zip = ""
            )
                        // City & State are NOT currently utilized
        {
            // http://apix.cmdi2.com/v2/crm/People/find?apiKey=c8ce6c91a96211fa0c43cf716593658a7895efd6&dataId=1?fname=Rebecca&lname=Amerson&email=<EMAIL>&hphone=8389286750&cphone=2983836436&addr1=7704LeesburgPike&zip=06468

            #region [ 1. Verify the API Key ]
            apiCallToken _token = cl_apiCallToken.validateToken(apiKey, dataId);

            if (_token.invalidToken)
                throw new HttpResponseException(HttpStatusCode.Unauthorized);
            #endregion

            #region [ 2. Get CrimsonDB by ProjectId ]
            System.Data.Entity.DbContext _dbContext = cl_apiCallToken.crmDBContext(dataId);
            if (_dbContext == null)
                throw new HttpResponseException(HttpStatusCode.BadRequest); // 400
            #endregion

            #region [ 3. Find matches ]
            return _dbContext.Database.SqlQuery<profile>(string.Format(
                    "SELECT * FROM dbo.findMatches(null,'{0}','{1}','{2}','{3}','{4}','{5}','{6}','{7}','{8}','{9}')", 
                            fname,mname,lname,suffix,email,hphone,cphone,addr1,addr2,zip)).ToList();
            #endregion
        }
        #endregion
    }
}