﻿using System;
using System.Collections.Generic;

using cmdiapp.n.core.Areas.crm.Domain.Models;
using cmdiapp.n.core.Areas.crm.ViewModels;
using cmdiapp.n.core.Areas.query.Domain.Models;
using cmdiapp.n.core.Domain.ViewModels;
namespace cmdiapp.n.core.Areas.crm.Domain.Services
{
    public interface IpeopleService
    {
        PeopleR get_v_people_1(int pid);
        List<v_people_1_ext1> get_v_people_1(string where, string orderBy, int pageSize, int pageNo);
        byte[] pictureOf(int pid);
        List<PeopleMatch> findMatch(int pid);
        List<PeopleMatch> findMatch(string fname, string mname, string lname, string suffix, string email, string hmph, string cellph, string street, string zip);
        int savePeople(PeopleF _peopleF);
        int addPeople(PeopleF _peopleF);
        int markDonorActive(int pid);
        int availableTrackNo();
        List<PeopleR_w_distance> nearbyRecords(decimal ctr_latitude, decimal ctr_longitude,
                                                decimal rect_latitude1, decimal rect_latitude2, decimal rect_longitude1, decimal rect_longitude2,
                                                int recLimit, int idExcl);
        List<PeopleR_w_distance> filterNearbyRecords(List<PeopleR_w_distance> nearbyRecords, List<NearbyFilter> filters);

        List<ssMRGLIST_ext> GetMRGlistMenuByPIDs(int pid1, int pid2);
        List<ssMRGLIST> get_ssMRGLIST();
        ssMRGLIST get_ssMRGLIST(string id);
        List<object> get_GridDataFromSQL(string SQL, string PID, Dictionary<string, string> _dictionary);
        Dictionary<string, string> getDictionary(string _sql);
        SP_response execute_SP(string _sql);
        int executeDelete(string _sql);
        PEOCODETYPEID getPEOcodetype(string PID);
        MessageBoard process_sql(string p_sql, string p_datasetName, MessageBoard msgBoard);
        
        int Add(jtRELATE _jtRELATE);
        int Update(jtRELATE _jtRELATE);
        genericResponse DeleteRelationship(int jtRelateId);

        List<lkRELATETYPE> GetRelateTypes();

        List<RelationNode> GetRelationNodes(int parentPID, int parentRelateId);

        bool HasRelationNodes(int parentPID, int parentRelateId);

        List<RelatePlusNames> GetRelationGraphLinks(int parentPID, int limit = 0);

        #region [[ For Bundler ]]
        List<BundlerR_ext1> get_Bundlers(string selFields, string sqlFrom, string where, string orderBy, int pageSize, int pageNo);
        List<BundlerListDisplay> GetBundlersList(string searchText, int page, int pageSize);
        #endregion

        #region [[ For Treeview Feature ]]
        object GetFundraiserDownlineByTrackNo(int trackNo);
        ContactConnection FetchContactConnectionsByTrackingNumber(int trackingNumber);
        #endregion

        List<peopleTimeLine_ext> getProfileTimeLine(
            int pid, int page, int pageSize , string searchTxt, string type, string date);
                
    }
}
