﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace cmdiapp.n.core.Domain.ViewModels
{
    public class rV_people_1 : iItemType
    {
        public int PID { get; set; }

        public string PREFIX { get; set; }

        public string FNAME { get; set; }

        public string MNAME { get; set; }

        public string LNAME { get; set; }

        public string SUFFIX { get; set; }

        public string FULLNAME_ { get; set; } 

        public string SPOUSENAME { get; set; }
               
        public string STR1 { get; set; } //75
               
        public string STR2 { get; set; } //75
               
        public int? NUM1 { get; set; } 
        
        public string EMPLOYER { get; set; }    

        public string OCCUPATION { get; set; }

        public string ASSISTANT { get; set; }

        public string HPHONE { get; set; }

        public string WPHONE { get; set; }

        public string CPHONE { get; set; }

        public string FAX { get; set; }

        public string EMAIL { get; set; }

        public string Address_ { get; set; }

        public string ADDR1 { get; set; }

        public string ADDR2 { get; set; }

        public string STREET { get; set; }

        public string CITY { get; set; }

        public string STATE { get; set; }

        public string ZIP { get; set; }

        public string PLUS4 { get; set; }

        public decimal? LGIFT { get; set; }

        public DateTime? LGIFTDTE { get; set; }

        public decimal? FGIFT { get; set; }

        public DateTime? FGIFTDTE { get; set; }

        public decimal? HPC { get; set; }

        public DateTime? HPCDTE { get; set; }

        public int? NOGIFTS { get; set; }

        public decimal? CUMTOT { get; set; }

        public string CTD_LABEL { get; set; }

        public decimal? CTD_AMT { get; set; }
    }
	
	public class rEvent : iItemType
    {
        public int SPCEVNTID { get; set; }

        public int? EventCount { get; set; }

        public string EVNTCODE { get; set; }

        public string EVNTDESC { get; set; }

        public string EVNTFULL { get; set; }

        public DateTime? STARTDTE { get; set; }

        public DateTime? ENDDTE { get; set; }

    }

    public class rEventDonors : iItemType
    {
        public int PID { get; set; }
                
        public DateTime? DATE { get; set; }
                
        public DateTime? UPDATEDON { get; set; }
                
        public string STATUS { get; set; }
                
        public string FNAME { get; set; }
                
        public string MNAME { get; set; }
                
        public string LNAME { get; set; }
                
        public string STREET { get; set; }
                
        public string ADDR1 { get; set; }
                
        public string ADDR2 { get; set; }
                
        public string CITY { get; set; }
                
        public string STATE { get; set; }
                
        public string ZIP { get; set; }
                
        public string HMPHN { get; set; }
                
        public string BSPHN { get; set; }

    }

    public class rEventDocs : iItemType
    {
        public int SPCEVNTDOCID { get; set; }

        public int SPCEVNTID { get; set; }

        public string FILENAME { get; set; }

        public Byte[] CONTENT { get; set; }
    }


    public class rEventDocsSpl : iItemType
    {
        public int SPCEVNTDOCID { get; set; }

        public int SPCEVNTID { get; set; }

        public string FILENAME { get; set; }

        public int count_ { get; set; }

        public Int64 rowNo { get; set; }

    }


    public class rTaskSearchResult : iItemType
    {
        public int ACTID { get; set; }

        public DateTime? DUEBY { get; set; }

        public string SCHEDFOR { get; set; }

        public string TASKTYPE { get; set; }

        public string NOTE { get; set; }

        public string SUBJECT { get; set; }

        public int PID { get; set; }

        public string NAME { get; set; }

        public string PREFIX { get; set; }

        public string FNAME { get; set; }

        public string MNAME { get; set; }

        public string LNAME { get; set; }

        public string SUFFIX { get; set; }

        public string STREET { get; set; }

        public string ADDR1 { get; set; }

        public string ADDR2 { get; set; }

        public string CITY { get; set; }

        public string STATE { get; set; }

        public string ZIP { get; set; }

        public string HMPHN { get; set; }

        public string BSPHN { get; set; }

        public string CELL { get; set; }

        public DateTime? SCHEDON { get; set; }

        public string SCHEDBY { get; set; }

        public bool DONE { get; set; }

        public DateTime? DONEON { get; set; }

    }

    public class rUserTasks : iItemType
    {
        public int ACTID { get; set; }

        public DateTime? DUEBY { get; set; }

        public string SCHEDFOR { get; set; }

        public string TASKTYPE { get; set; }

        public string NOTE { get; set; }

        public string SUBJECT { get; set; }

        public int PID { get; set; }

        public string NAME { get; set; }

        public string CITY { get; set; }

        public string STATE { get; set; }

        public string HMPHN { get; set; }

        public string BSPHN { get; set; }

        public string CELL { get; set; }

        public DateTime? SCHEDON { get; set; }

        public DateTime? NEXTDAY { get; set; }

        public string SCHEDBY { get; set; }

        public Byte DONE { get; set; }

    }
}