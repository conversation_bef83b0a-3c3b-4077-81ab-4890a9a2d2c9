﻿using cmdiapp.n.core.Areas.crm.Domain.Data;
using cmdiapp.n.core.Domain.ViewModels;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Runtime.Serialization;
using System.Web;

namespace cmdiapp.n.core.Areas.crm.Domain.Models
{
    /// <summary>
    /// Represents a custom field for an integrated email list
    /// </summary>
    [Table("x_es_customFields")]
    [DataContract]
    public class EmailListCustomField : _entityBase_crm, iItemType
    {
        /// <summary>
        /// The primary key of the custom field
        /// </summary>
        [Column("Id")]
        [Key]
        [DataMember(Name = "id")]
        public int Id { get; set; }
        /// <summary>
        /// The unique name for the field used by Cremir
        /// </summary>
        [Column("name")]
        [DataMember(Name = "name")]
        public string Name { get; set; }
        /// <summary>
        /// The label to display in UI
        /// </summary>
        [Column("label")]
        [DataMember(Name = "label")]
        public string Label { get; set; }
        /// <summary>
        /// The column selector in the SQL query
        /// </summary>
        [Column("selector")]
        [IgnoreDataMember]
        public string Selector { get; set; }
        /// <summary>
        /// The join clause that's the origin of the <see cref="Selector"/>
        /// </summary>
        [Column("from")]
        [IgnoreDataMember]
        public string From { get; set; }
        /// <summary>
        /// The alias for the table from
        /// which the <see cref="Selector"/> originates
        /// </summary>
        [Column("tableAlias")]
        [IgnoreDataMember]
        public string TableAlias { get; set; }
        /// <summary>
        /// The type of the field value
        /// </summary>
        [Column("dataType")]
        [DataMember(Name = "dataType")]
        public string DataType { get; set; }
    }
}