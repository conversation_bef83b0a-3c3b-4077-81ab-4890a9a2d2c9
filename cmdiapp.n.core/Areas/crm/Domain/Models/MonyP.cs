﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Data;
using System.Linq;
using System.Web.Mvc;
using System.Data.Linq;
using System.ComponentModel.DataAnnotations.Schema;

using cmdiapp.n.core.Domain.ViewModels;
using cmdiapp.n.core.Areas.crm.Domain.Data;

namespace cmdiapp.n.core.Areas.crm.Domain.Models
{

    // Money Class for Printing
    public class MoneyP : _entityBase_crm
    {
	    public int MID { get; set; }

	    public int PID { get; set; }

	    public string giftInfo { get; set; }
	
        public byte[] checkImage { get; set; }
    }

}
