﻿using cmdiapp.n.core.Areas.legislator.Domain.Data;
using cmdiapp.n.core.Areas.legislator.Domain.Models;
using cmdiapp.n.core.Core;
using cmdiapp.n.core.Domain.ViewModels;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace cmdiapp.n.core.Areas.legislator.Domain.Services.implementation
{
    public class ContactService : IContactService
    {
        private I_entity_legislator _entity;

        public ContactService(I_entity_legislator entity)
        {
            _entity = entity;
        }

        public Contact Get(int id)
        {
            return _entity.Single<Contact>(c => c.Id == id && !c.deleted);
        }

        public List<Contact> GetByLegislator(string legislatorId, bool includeDeleted = false)
        {
            return _entity.All<Contact>()
                .Where(c => c.legislatorId == legislatorId && (includeDeleted || !c.deleted)).ToList();
        }

        public genericResponse HandleChange(
            ContactChange change, ContactChangeStatus oldStatus, ContactChangeStatus newStatus)
        {
            // get change type
            ContactChangeType changeType = _entity.Single<ContactChangeType>(t => t.Id == change.typeId);
            // acceptable change type codes
            string[] acceptableTypes = new string[3] { "I", "U", "D" };
            if (changeType == null || !acceptableTypes.Contains(changeType.code))
            {
                return new genericResponse
                {
                    success = false,
                    message = $"There is no valid Contact Change Type with ID {change.typeId}"
                };
            }

            if (NoChangeRequired(oldStatus.code, newStatus.code))
            {
                return new genericResponse
                {
                    success = true,
                    message = "Change status updated. No change to Contacts required."
                };
            }

            if (ReversalRequired(oldStatus.code, newStatus.code))
            {
                if (CanReverseChange(change.contactId ?? 0, change.Id))
                {
                    return CommitChangeReversal(change, changeType);
                }

                return new genericResponse
                {
                    success = false,
                    message =
                        "It is recommended to only undo the last change for this contact. Please submit a new change or undo previous ones."
                };
            }

            return CommitChange(change, changeType);
        }

        private bool NoChangeRequired(string oldStatusCode, string newStatusCode)
        {
            // if Pending => Rejected or Rejected => Pending,
            // don't need to change Contact table
            string[] pOrR = new string[2] { "P", "R" };
            return pOrR.Contains(oldStatusCode) && pOrR.Contains(newStatusCode);
        }

        private bool ReversalRequired(string oldStatusCode, string newStatusCode)
        {
            // if Accepted => Pending or Accepted => Rejected,
            // need to reverse change out of Contact table
            return oldStatusCode == "A" && (newStatusCode == "R" || newStatusCode == "P");
        }

        private bool CanReverseChange(int contactId, int changeId)
        {
            // we are only going to allow reversals for the last change made to that contact
            // so check that this change is latest accepted change
            return (GetLastApprovedChangeForContact(contactId)?.Id ?? 0) == changeId;
        }

        public bool CanUpdateStatusTo(
            int contactId, int changeId, string changeTypeCode, string oldStatusCode, string newStatusCode)
        {
            var contactExists = _entity.All<Contact>().Any(c => c.Id == contactId && !c.deleted);
            return ((contactExists || newStatusCode != "A") || 
                        (changeTypeCode == "I" || (changeTypeCode == "D" && oldStatusCode == "A")))
                && (!ReversalRequired(oldStatusCode, newStatusCode) || CanReverseChange(contactId, changeId));
        }

        private genericResponse CommitChange(ContactChange change, ContactChangeType changeType)
        {
            // get contact record
            Contact contact = _entity.Single<Contact>(c => c.Id == change.contactId && !c.deleted);
            try
            {
                // insert
                if (changeType.code == "I")
                {
                    // contact should not exist
                    if (contact != null)
                    {
                        return GetContactExistenceErrorResponse(changeType.type, false);
                    }

                    // check if contact already exists as deleted
                    contact = _entity.Single<Contact>(c => c.Id == change.contactId && c.deleted);
                    if (contact == null)
                    {
                        // contact is really new
                        contact = new Contact();
                        // map to new contact record
                        MapChangeToContact(change, contact);
                        SetContactMetaData(contact);
                        // add it
                        _entity.Add(contact);
                    }
                    else
                    {
                        // contact already exists as deleted, so just reinstate it
                        contact.deleted = false;
                        SetContactMetaData(contact);
                        _entity.Update(contact);
                    }

                }

                // update
                if (changeType.code == "U")
                {
                    // contact should exist
                    if (contact == null)
                    {
                        return GetContactExistenceErrorResponse(changeType.type, true);
                    }
                    // map to contact record
                    MapChangeToContact(change, contact);
                    SetContactMetaData(contact);
                    _entity.Update(contact);
                }

                // delete
                if (changeType.code == "D")
                {
                    // contact should exist
                    if (contact == null)
                    {
                        return GetContactExistenceErrorResponse(changeType.type, true);
                    }

                    contact.deleted = true;
                    SetContactMetaData(contact);
                    _entity.Update(contact);
                }

                _entity.CommitChanges();

                CleanUpChangeRecordPostHoc(change, contact, changeType);

                return new genericResponse
                {
                    success = true,
                    message = $"Change committed.  Contact {changeType.type} was successful."
                };

            }
            catch (Exception ex)
            {
                return new genericResponse
                {
                    success = false,
                    message = $"Unable to {changeType.type} Contact. An error occurred.",
                    messageKey = ex.Message
                };
            }
        }

        private genericResponse CommitChangeReversal(ContactChange change, ContactChangeType changeType)
        {
            // get contact record
            Contact contact = _entity.Single<Contact>(c => c.Id == change.contactId && !c.deleted);
            try
            {
                // undo insert => delete
                if (changeType.code == "I")
                {
                    // contact should exist
                    if (contact == null)
                    {
                        return GetContactExistenceErrorResponse(changeType.type, true, true);
                    }

                    contact.deleted = true;
                    SetContactMetaData(contact);
                    _entity.Update(contact);
                    
                }
                // undo update => update
                if (changeType.code == "U")
                {
                    // contact should exist
                    if (contact == null)
                    {
                        return GetContactExistenceErrorResponse(changeType.type, true, true);
                    }

                    // we need last approved change
                    ContactChange lastApprovedChange = GetLastApprovedChangeForContact(contact.Id, change.Id);
                    if (lastApprovedChange == null)
                    {
                        return new genericResponse
                        {
                            success = false,
                            message = $"There is no earlier approved change for this contact. Cannot undo {changeType.type}."
                        };
                    }

                    MapChangeToContact(lastApprovedChange, contact);
                    SetContactMetaData(contact);
                    _entity.Update(contact);
                }
                // undo delete => insert
                if (changeType.code == "D")
                {
                    // contact should not "exist", i.e. deleted should be true
                    if (contact != null)
                    {
                        return GetContactExistenceErrorResponse(changeType.type, false, true);
                    }

                    // need to find deleted record
                    contact = _entity.Single<Contact>(c => c.Id == change.contactId && c.deleted);
                    if (contact == null)
                    {
                        return new genericResponse
                        {
                            success = false,
                            message = "Could not recover deleted record.  Please submit a new change to Insert it."
                        };
                    }
                    contact.deleted = false;
                    SetContactMetaData(contact);
                    _entity.Update(contact);
                }

                _entity.CommitChanges();

                CleanUpChangeRecordPostHoc(change, contact, changeType, true);

                return new genericResponse
                {
                    success = true,
                    message = $"Undo Contact {changeType.type} was successful."
                };
            }
            catch (Exception ex)
            {
                return new genericResponse
                {
                    success = false,
                    message = $"Unable to undo Contact {changeType.type}. An error occurred.",
                    messageKey = ex.Message
                };
            }
        }

        private void CleanUpChangeRecordPostHoc(
            ContactChange change, Contact contact, ContactChangeType changeType, bool wasReversal = false)
        {
            if (!wasReversal)
            {
                if (changeType.code == "I")
                {
                    // since we inserted, need to update change record with new Contact's Id
                    change.contactId = contact.Id;
                    _entity.Update(change);
                    _entity.CommitChanges();
                }
                                
            }
        }

        private ContactChange GetLastApprovedChangeForContact(int contactId, int exceptionId = 0)
        {
            return (from change in _entity.All<ContactChange>()
                    join status in _entity.All<ContactChangeStatus>() on change.statusId equals status.Id
                    where change.contactId == contactId
                        && status.code == "A"
                        && (exceptionId == 0 || change.Id != exceptionId)
                    orderby change.UPDATEDON descending
                    select change).FirstOrDefault();   
        }

        private void MapChangeToContact(ContactChange change, Contact contact)
        {
            List<string> exceptions = new List<string>() { "Id" };
            Type changeType = change.GetType();
            foreach (var prop in contact.GetType().GetProperties())
            {
                var changeProp = changeType.GetProperty(prop.Name);
                if (!exceptions.Contains(prop.Name) && changeProp != null)
                {
                    prop.SetValue(contact, changeProp.GetValue(change));
                }
            }            
        }

        private void SetContactMetaData(Contact contact)
        {
            contact.updatingUserId_i = session.userSession.UserId_i;
            contact.UPDATEDON = DateTime.Now;
        }

        private genericResponse GetContactExistenceErrorResponse(
            string changeType, bool shouldExist, bool isReversal = false)
        {
            string undoString = isReversal ? (" undo a" + (changeType == "Delete" ? "" : "n")) : "";
            string existString = "the contact " + (shouldExist ? "does not exist" : "already exists");
            string message = $"You are trying to{undoString} {changeType}, but {existString}";
            return new genericResponse
            {
                success = false,
                message = message
            };
        }
    }
}