﻿using Ninject;
using cmdiapp.n.core.Core.App_Start;

namespace cmdiapp.n.core.Areas.legislator.Domain.Data
{
    public static class entityManager_legislator
    {
        public static I_entity_legislator getEntity()
        {
            return NinjectMVC.kernel.Get<I_entity_legislator>();
        }

        public static I_entity_legislator getEntity(string connectionString)
        {
            return NinjectMVC.kernel.Get<I_entity_legislator>(new Ninject.Parameters.ConstructorArgument("connectionString", connectionString, true));
        }
    }    
}
