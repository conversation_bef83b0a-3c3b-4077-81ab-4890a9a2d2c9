﻿using cmdiapp.n.core.Areas.crm.Domain.Data;
using System;
using System.ComponentModel.DataAnnotations.Schema;
using System.Runtime.Serialization;

namespace cmdiapp.n.core.Areas.crm.Domain.Models
{
    [Table("x_va_reports")]
    [DataContract]
    public class VirginiaReport : _entityBase_crm
    {
        [Column("id")]
        [DataMember(Name = "id")]
        public int Id { get; set; }

        [Column("filerId")]
        [DataMember(Name = "filerId")]
        public int FilerId { get; set; }

        [Column("electionCycle")]
        [DataMember(Name = "electionCycle")]
        public string ElectionCycle { get; set; }

        [Column("filingDate")]
        [DataMember(Name = "filingDate")]
        public DateTime FilingDate { get; set; }

        [Column("reportPeriodStart")]
        [DataMember(Name = "reportPeriodStart")]
        public DateTime ReportPeriodStart { get; set; }

        [Column("reportPeriodEnd")]
        [DataMember(Name = "reportPeriodEnd")]
        public DateTime ReportPeriodEnd { get; set; }

        [Column("receiptsThisPeriod")]
        [DataMember(Name = "receiptsThisPeriod")]
        public decimal? ReceiptsThisPeriod { get; set; }

        [Column("disbursementsThisPeriod")]
        [DataMember(Name = "disbursementsThisPeriod")]
        public decimal? DisbursementsThisPeriod { get; set; }

        [Column("totalReceiptsThisElectionCycle")]
        [DataMember(Name = "totalReceiptsThisElectionCycle")]
        public decimal TotalReceiptsThisElectionCycle { get; set; }

        [Column("totalDisbursementsThisElectionCycle")]
        [DataMember(Name = "totalDisbursementsThisElectionCycle")]
        public decimal TotalDisbursementsThisElectionCycle { get; set; }

        [Column("loanBalance")]
        [DataMember(Name = "loanBalance")]
        public decimal LoanBalance { get; set; }

        [Column("endingBalance")]
        [DataMember(Name = "endingBalance")]
        public decimal EndingBalance { get; set; }

        [Column("isAmendment")]
        [DataMember(Name = "isAmendment")]
        public bool IsAmendment { get; set; }

        [Column("amendmentNumber")]
        [DataMember(Name = "amendmentNumber")]
        public int AmendmentNumber { get; set; }

        [Column("createdOn")]
        [DataMember(Name = "createdOn")]
        public DateTime CreatedOn { get; set; }

        [Column("reportTypeId")]
        [DataMember(Name = "reportTypeId")]
        public int ReportTypeId { get; set; }
    }
}