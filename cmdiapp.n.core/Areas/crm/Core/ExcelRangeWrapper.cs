﻿using OfficeOpenXml;
using System;
using System.Data;

namespace cmdiapp.n.core.Areas.crm.Core
{
    public class ExcelRangeWrapper : IExcelRange
    {
        private readonly ExcelRange _range;

        public ExcelRangeWrapper(ExcelRange range)
        {
            _range = range ?? throw new ArgumentNullException(nameof(range));
        }

        public IExcelRange this[string address] => new ExcelRangeWrapper(_range[address]);

        public IExcelRange this[int row, int col] => new ExcelRangeWrapper(_range[row, col]);

        public IExcelRange this[int fromRow, int fromCol, int toRow, int toCol] =>
            new ExcelRangeWrapper(_range[fromRow, fromCol, toRow, toCol]);

        public string NumberFormat
        {
            get => _range.Style.Numberformat.Format;
            set
            {
                _range.Style.Numberformat.Format = value;
            }
        }

        public void AutoFitColumns()
        {
            _range.AutoFitColumns();
        }

        public void LoadFromDataTable(DataTable dataTable, bool printHeaders)
        {
            _range.LoadFromDataTable(dataTable, printHeaders);
        }
    }
}