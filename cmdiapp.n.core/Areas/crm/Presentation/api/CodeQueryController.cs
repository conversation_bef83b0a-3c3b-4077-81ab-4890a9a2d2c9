﻿using cmdiapp.n.core._Domain.ViewModels;
using cmdiapp.n.core.Areas.crm.Domain.Services;
using cmdiapp.n.core.Areas.crm.ViewModels;
using cmdiapp.n.core.Core.App_Start;
using cmdiapp.n.core.Presentation.Controllers;
using Ninject;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Threading.Tasks;
using System.Web.Http;

namespace cmdiapp.n.core.Areas.crm.Presentation.api
{
    [apiAuthorize]
    [RoutePrefix("crm/api/CodeQuery")]
    public class CodeQueryController : ApiController
    {
        private readonly ICodeQueryService _service;

        public CodeQueryController()
        {
            _service = NinjectMVC.kernel.Get<ICodeQueryService>();
        }

        [HttpGet, Route("")]
        public Task<GenericSuccessResponse<IEnumerable<CodeDisplay>>> SearchCodesAsync(
            string searchText = "",
            int page = 1,
            int pageSize = 10)
        {
            return _service.SearchCodesAsync(searchText, page, pageSize);
        }
    }
}