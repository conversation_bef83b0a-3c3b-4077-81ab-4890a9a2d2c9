﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Text.RegularExpressions;

namespace cmdiapp.n.core.Areas.query.Domain.Models
{
    #region [[[ class.q_whereItem_def ]]] Retreive/objectize a "q_whereItem" definition.
    /*
            string q_whereItem_def_xml = @"
                <q>
                    <t>n</t>                            -> fieldtype (Required.  (s)tring · (n)umeric · (d)ate) 
                    <c>People Type</c>                  -> fieldCaption
                    <d>Enter People Record Type</d>     -> fieldCaptionDetails (Optional)
                    <n>KT.PEOTYPEID</n>                 -> fieldName
                    <v>1</v>                            -> default_fieldValue (Optional. Leave <empty> otherwise) 
                    <o>1</o>                            -> default_operatorIdx (Optional. Leave 0 otherwise) 
                    <l>                                 -> Custom Operators (Optional)
                        <i>                             ->  Single Operator
                            <s>1</s>                    ->  Symbol
                            <r>Individual</r>           ->  Description
                            <p></p>                     ->  Detail Description (Optional) 
                        </i>                             
                        <i>                             ->  Single Operator
                            <s>2</s>                    ->  Symbol
                            <r>Organization</r>         ->  Description
                            <p></p>                     ->  Detail Description (Optional) 
                        </i>                             
                    </l>
                </q>
            ";
            string q_where_def_xml_flatLine = @"
            <q> <t>n</t> <c>People Type</c> <d>Enter People Record Type</d> <n>KT.PEOTYPEID</n> <v>1</v> <o>1</o>      
                    <l> <i> <s>1</s> <r>Individual</r> <p></p> </i>  <i> <s>2</s> <r>Organization</r> <p></p> </i> </l> </q>
            ";
     */
    public class q_whereItem
    {
        public string id { get; set; }                  // Unique ID
        public string fieldType { get; set; }           // <t/> (s)tring | (d)ate | (n)umeric | (c)urrency | (b)oolean
        public string fieldCaption { get; set; }        // <c/> IMPORTANT should be UNIQUE when used in CQ
        public string fieldCaptionDetails { get; set; } // <d/>
        public string default_fieldValue { get; set; }  // <v/>
        public int default_operatorIdx { get; set; }    // <o/>

        public List<qOperator> lOperators { get; set; }

        public bool locked { get; set; }                // Restricted Item

        /* Disabled on Dec 26, 2013
        public List<qOperator> Operators
        {
            get
            {
                List<qOperator> _operators = new List<qOperator>();

                #region [ Operators for String ]
                if (fieldType == "s")
                {
                    _operators.Add(new qOperator() { symbol = "[[ eLIKE ]]", label = "exactly like", details = "whole string MUST match" });
                    _operators.Add(new qOperator() { symbol = "[[ LIKE% ]]", label = "starts with", details = "the beginning matches the specified string" });
                    _operators.Add(new qOperator() { symbol = "[[ %LIKE ]]", label = "ends with", details = "the end matches the specified string" });
                    _operators.Add(new qOperator() { symbol = "[[ %LIKE% ]]", label = "contains", details = "the specified string is within" });
                    _operators.Add(new qOperator() { symbol = "[[ NULL ]]", label = "is empty", details = "blank space or not assigned yet" });
                    _operators.Add(new qOperator() { symbol = "[[ !NULL ]]", label = "is NOT empty", details = "has anything other than blank space" });
                    _operators.Add(new qOperator() { symbol = "[[ IN ]]", label = "in list of", details = "is one of the list items specified (Use a comma to separate strings)" });
                    _operators.Add(new qOperator() { symbol = "[[ !IN ]]", label = "NOT in list", details = "has anything other than specified in the list" });
                }
                #endregion

                #region [ Operators for Numeric ]
                else if (fieldType == "n")
                {
                    _operators.Add(new qOperator() { symbol = "[[ = ]]", label = "=", details = "Equal to" });
                    _operators.Add(new qOperator() { symbol = "[[ >= ]]", label = ">=", details = "Greater than/Equal to" });
                    _operators.Add(new qOperator() { symbol = "[[ <= ]]", label = "<=", details = "Less than/Equal to" });
                    _operators.Add(new qOperator() { symbol = "[[ != ]]", label = "<>", details = "Not Equal to" });
                    _operators.Add(new qOperator() { symbol = "[[ nBTW ]]", label = "Between", details = "Between two numbers" });
                }
                #endregion

                #region [ Operators for Date ]
                else if (fieldType == "d")
                {
                    _operators.Add(new qOperator() { symbol = "[[ dYET ]]", label = "(Specify)", details = "" });
                    _operators.Add(new qOperator() { symbol = "[[ On ]]", label = "On", details = "" });
                    _operators.Add(new qOperator() { symbol = "[[ On/After ]]", label = "On/After", details = "" });
                    _operators.Add(new qOperator() { symbol = "[[ On/Before ]]", label = "On/Before", details = "" });
                    _operators.Add(new qOperator() { symbol = "[[ dBTW ]]", label = "Between", details = "" });
                }
                #endregion

                return _operators;
            }
        }
         */
    }

    public class q_whereItem_S : q_whereItem
    {
        public string parentPanelName { get; set; }
        public q_whereItem_panel_S parentPanel { get; set; }
        public string fieldName { get; set; }
        public bool groupbyHaving { get; set; }

        public searchItem_S filter { get; set; } // For SQL-Run
    }

    #endregion

    #region [[[ class.qOperator ]]]
    public class qOperator
    {
        public string symbol { get; set; }      // Operator Symbol (For Custom Operators, it's Key for Lookup, Custom Operator (eg. PEOPLE.PEOTYPEID or lkPEOTYPE.PEOTYPE)
        public string label { get; set; }       // Caption.  (For Custom Operators, it's Lookup Description.  (eg. lkPEOTYPE.DESCRIP)
        public string details { get; set; }     // Details (will be used as Tooltip)
    }
    #endregion

}