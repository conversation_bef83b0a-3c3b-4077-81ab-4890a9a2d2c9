﻿using cmdiapp.n.core.Areas.legislator.Domain.Models;
using cmdiapp.n.core.Areas.legislator.Domain.Services;
using cmdiapp.n.core.Core.App_Start;
using Ninject;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Web.Http;

namespace cmdiapp.n.core.Areas.legislator.Presentation.api
{
    [RoutePrefix("legislator/api/Contact")]
    public class LegislatorContactController : ApiController
    {
        private IContactService _service;
        private IContactChangeService _changeService;

        public LegislatorContactController()
        {
            _service = NinjectMVC.kernel.Get<IContactService>();
            _changeService = NinjectMVC.kernel.Get<IContactChangeService>();
        }

        [HttpGet, Route("{id}")]
        [Authorize]
        public Contact Get(int id)
        {
            return _service.Get(id);
        }

        [HttpGet, Route("{id}/Changes/{pending}")]
        [Authorize]
        public List<ContactChange> GetChanges(int id, bool pending = false)
        {
            return _changeService.GetByContact(id, pending);
        }
    }
}