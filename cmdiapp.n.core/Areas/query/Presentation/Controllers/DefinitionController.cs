﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.Mvc;

namespace cmdiapp.n.core.Areas.query.Presentation.Controllers
{
    /*
     * ################################################################
     * ############################# NO LONGER USED
     * ################################################################
     * 
     

    [Authorize]
    public class DefinitionController : Controller
    {
        [OutputCache(Duration = 86400, VaryByParam = "qDefName")]
        public JsonResult Data(string qDefName) // eg. "crm/peopleSearch"
        {
            string qDef_filePath = Library.util.fullPath(String.Format(@"/Areas/query/resource/{0}.xml",qDefName));
            return J<PERSON>(q_library.get_q_def(qDef_filePath), JsonRequestBehavior.AllowGet);            
        }
    }
    */
}
