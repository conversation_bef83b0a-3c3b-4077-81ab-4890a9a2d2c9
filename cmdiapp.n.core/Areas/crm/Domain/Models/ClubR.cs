﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Data;
using System.Linq;
using System.Web.Mvc;
using System.Data.Linq;
using System.ComponentModel.DataAnnotations.Schema;

using cmdiapp.n.core.Domain.ViewModels;
using cmdiapp.n.core.Areas.crm.Domain.Data;

namespace cmdiapp.n.core.Areas.crm.Domain.Models
{
    // (view)v_people_status  -  Used to list all clubs per donor ]]
    // Updated by Lydia on 11/4/2013
    [Table("v_people_status")]
    public class ClubR : _entityBase_crm, iItemType
    {
        [Column]
        [Key]
        public int jtCLUBID { get; set; }

        [Column]
        public int PID { get; set; }

        [Column]
        public string CLUBCODE { get; set; }

        [Column]
        public string DESCRIP { get; set; }

        [Column]
        public string SOLICITOR { get; set; }

        [Column]
        public string CANDIDATE { get; set; }

        [Column]
        public DateTime? RNEWDTE { get; set; }

        [Column]
        public byte? PRIME { get; set; }

        [Column]
        public byte CLOSED { get; set; }
    }

    [NotMapped]
    public class ClubR_ext1 : ClubR
    {
        public int count_ { get; set; }

        public Int64 rowNo { get; set; }
    }

    public class clubSummary
    {
        public int? UPCOMINGJTCLUBID { get; set; }
        public DateTime? UPCOMINGRNEWDTE { get; set; }
        public string UPCOMINGCLUBCODE { get; set; }
        public int ACTIVECOUNT { get; set; }
        public int PRIMECOUNT { get; set; }
        public int CLOSEDCOUNT { get; set; }
    }
    public class clubBySOLICITOR
    {
        public string SOLICITOR { get; set; }
        public int COUNT { get; set; }
    }
    public class clubByCANDIDATE
    {
        public string CANDIDATE { get; set; }
        public int COUNT { get; set; }
    }
}
