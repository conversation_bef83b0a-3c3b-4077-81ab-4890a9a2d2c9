﻿using System.Data.Entity;

using cmdiapp.n.core.Areas.legislator.Domain.Models;

namespace cmdiapp.n.core.Areas.legislator.Domain.Data
{
    public class _legislatorDBContext : DbContext
    {
        public _legislatorDBContext(string _appDBContext_connString_itself_or_name) 
            : base(_appDBContext_connString_itself_or_name)
        {
            Database.SetInitializer(new LegislatorDBInitializer());
        }

        protected override void OnModelCreating(DbModelBuilder modelBuilder)
        {
            modelBuilder.Conventions.Remove<System.Data.Entity.ModelConfiguration.Conventions.PluralizingTableNameConvention>();
        }

        public DbSet<Committee> Committe { get; set; }
        public DbSet<CommitteeMember> CommitteeMember { get; set; }
        public DbSet<Legislator> Legislator { get; set; }
        public DbSet<LegislatorDistrictAddress> LegislatorDistrictAddress { get; set; }
        public DbSet<LegislatorPhoto> LegislatorPhoto { get; set; }
        public DbSet<LegislatorSocialMediaAccount> LegislatorSocialMediaAccount { get; set; }
        public DbSet<Contact> Contact { get; set; }
        public DbSet<ContactChangeStatus> ContactChangeStatus { get; set; }
        public DbSet<ContactChangeType> ContactChangeType { get; set; }
        public DbSet<ContactChange> ContactChange { get; set; }
        public DbSet<ContactChangeStatusHistory> ContactChangeHistory { get; set; }
    }
}