﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Linq.Expressions;
using System.IO;
using System.Data;
using System.Data.Entity;

using Ninject;
using Ninject.Web.Mvc;

using cmdiapp.n.core.Core;
using cmdiapp.n.core.Core.App_Start;
using cmdiapp.n.core.Areas.docService.Domain.Services;

namespace cmdiapp.n.core.Areas.docService.Domain.Data
{
    public interface I_entity_docService
    {
        DbContext getContext();

        IQueryable<TSource> All<TSource>() where TSource : _entityBase_docService, new();

        void Add<TSource>(IEnumerable<TSource> items) where TSource : _entityBase_docService, new();
        void Add<TSource>(TSource item) where TSource : _entityBase_docService, new();

        void CommitChanges();

        void Delete<TSource>(Expression<Func<TSource, bool>> expression) where TSource : _entityBase_docService, new();
        void Delete<TSource>(TSource item) where TSource : _entityBase_docService, new();

        void Dispose();

        TSource Single<TSource>(Expression<Func<TSource, bool>> expression) where TSource : _entityBase_docService, new();

        void Update<TSource>(TSource item) where TSource : _entityBase_docService, new();

        void Refresh<TSource>(TSource item) where TSource : _entityBase_docService, new();
    }

    public static class I_entityManager_docService
    {
        public static I_entity_docService getEntity()
        {
            return NinjectMVC.kernel.Get<I_entity_docService>();
        }

        public static I_entity_docService getEntity(string connectionString)
        {
            return NinjectMVC.kernel.Get<I_entity_docService>(new Ninject.Parameters.ConstructorArgument("connectionString", connectionString, true));
        }

        public static IdocService getService()
        {
            return NinjectMVC.kernel.Get<IdocService>();
        }
    }
}