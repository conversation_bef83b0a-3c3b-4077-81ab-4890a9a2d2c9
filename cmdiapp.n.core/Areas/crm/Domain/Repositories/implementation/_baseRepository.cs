﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Data.Linq;
using System.Text;
using System.Data;
using System.Data.Entity;

using cmdiapp.n.core.Areas.crm.Domain.Models;

namespace cmdiapp.n.core.Areas.crm.Domain.Repositories
{
    public abstract class _baseRepository<T> where T : class
    {
        #region [[ private - _db & _dbset ]]
        private DbContext _db;
        private readonly IDbSet<T> _dbset; 
        #endregion

        #region [[ protected DatabaseFactory & Database ]]
        protected I__DBFactory DatabaseFactory
        {
            get;
            private set;
        }

        protected DbContext Database
        {
            get { return _db ?? (_db = DatabaseFactory.getContext()); }
        }
        #endregion

        #region [[ Constructors ]]
        protected _baseRepository(I__DBFactory databaseFactory)
        {
            DatabaseFactory = databaseFactory;
            _dbset = Database.Set<T>();
        }
        #endregion


        #region [[[ Datasets - Direct access layer to tables ]]
        /*
        public IQueryable<user> users
        { get { return (_db as _appDBContext).users; } }
        */

        #endregion


        #region [[ Basic CRUD ]]

        public virtual IQueryable<T> All()
        {
            return _dbset.AsQueryable();
        }

        public virtual IQueryable<T> AllReadOnly()
        {
            return _dbset.AsNoTracking();
        }

        public virtual T GetById(int id)
        {
            return _dbset.Find(id);
        }

        public virtual bool Add(T entity)
        {
            try
            {
                _dbset.Add(entity);
                return true;
            }
            catch
            {
                return false;
            }
        }

        public virtual bool Delete(T entity)
        {
            try
            {
                //Attach Entity if it is Detached - Added on 4/2/2013
                if (_db.Entry(entity).State == EntityState.Detached)
                {
                    _dbset.Attach(entity);
                }
                //Now Perform remove operation
                _dbset.Remove(entity);
                return true;
            }
            catch
            {
                return false;
            }
        }

        public virtual bool Update(T entity)
        {
            try
            {
                _db.Entry(entity).State = EntityState.Modified;
                return true;
            }
            catch
            {
                return false;
            }
        }
        #endregion
    }
}
