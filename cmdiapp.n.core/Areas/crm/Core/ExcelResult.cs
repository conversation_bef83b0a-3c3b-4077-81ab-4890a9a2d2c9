﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.Mvc;
using System.Data;
using System.Data.SqlClient;
using System.Data.OleDb;
using System.IO;
using System.Text;

using Newtonsoft.Json;
using OfficeOpenXml;
using OfficeOpenXml.Drawing.Chart;
using OfficeOpenXml.Style;
using System.Drawing;


namespace cmdiapp.n.core.Areas.crm.Core
{
    public class ExcelResult : ActionResult
    {
        public string fileName { get; set; }
        public string filePath { get; set; }
        public string sqlStatement { get; set; }
        public string sheetName { get; set; }
        public string connectionSring { get; set; }
        public string clientsidefileName { get; set; }
        public DataSet rds { get; set; }

        public override void ExecuteResult(ControllerContext context)
        {
            if (sheetName.Contains("INFILING_"))
            {
                ExecINFiling();
            }
            else
            {
                DataSet dset = new DataSet();
                if (string.IsNullOrEmpty(sqlStatement) && rds != null)
                {
                    dset = rds;
                }
                else
                {
                    string connStr = connectionSring;
                    SqlToDataSet sqlDataset = new SqlToDataSet(sqlStatement, connStr, sheetName);
                    dset = sqlDataset.results;
                }

                DataTable reportsTable = dset.Tables[0];
                using (ExcelPackage p = new ExcelPackage())
                {
                    //Set the Document properties
                    p.Workbook.Properties.Author = "Crimson Web Application";
                    p.Workbook.Properties.Title = sheetName;

                    //Create a sheet
                    p.Workbook.Worksheets.Add(sheetName);
                    ExcelWorksheet ws = p.Workbook.Worksheets[1];
                    //ws.Name = "Sample Worksheet"; //name the sheet as needed
                    ws.Cells.Style.Font.Size = 11; //Default font size for whole sheet
                    ws.Cells.Style.Font.Name = "Calibri"; //Default Font name for whole sheet
                    ws.View.ShowGridLines = true;

                    //Load the data - Take care of Date columns
                    var rowCount = reportsTable.Rows.Count;
                    //var worksheet = package.Workbook.Worksheets.Add(repName + "_" + (i + 1));
                    ws.Cells["A1"].LoadFromDataTable(reportsTable, true);
                    //if empty set to null
                    foreach (var cell in ws.Cells)
                    {
                        if (cell.Value.ToString() == "")
                        {
                            cell.Value = null;
                        }
                    }

                    if (dset.Tables[0].Rows.Count > 0)
                    {
                        #region [[ For Formatting : Date and $ Values ]]
                        //For Date Columns
                        var dateColumns = from DataColumn d in reportsTable.Columns
                                              //where d.DataType == typeof(DateTime) || d.ColumnName.Contains("Date")
                                          where d.DataType == typeof(DateTime)
                                          select d.Ordinal + 1;

                        foreach (var dc in dateColumns)
                        {
                            //int test = dc;
                            ws.Cells[2, dc, rowCount + 1, dc].Style.Numberformat.Format = "mm/dd/yyyy";
                        }

                        //For Decimal Columns
                        var decimalColumns = from DataColumn q in reportsTable.Columns
                                             where q.DataType == typeof(Decimal)
                                             select q.Ordinal + 1;

                        foreach (var dci in decimalColumns)
                        {
                            int test = dci;
                            ws.Cells[2, dci, rowCount + 1, dci].Style.Numberformat.Format = "$#,##0.00";
                        }
                        #endregion
                    }
                    #region [[ For Reference ]]
                    //Helpful Links
                    //http://epplus.codeplex.com/discussions/261602
                    //https://gist.github.com/SamWM/1031793
                    //http://epplus.codeplex.com/wikipage?title=FAQ&referringTitle=Documentation
                    //Load the data - NO formating but this shows number for the Date column
                    //ws.Cells["A1"].LoadFromDataTable(reportsTable, true);
                    #endregion

                    /*Bordor style
                    ws.Cells[1, 1, reportsTable.Rows.Count + 1, reportsTable.Columns.Count].Style.Border.Left.Style = ExcelBorderStyle.Thin;
                    ws.Cells[1, 1, reportsTable.Rows.Count + 1, reportsTable.Columns.Count].Style.Border.Top.Style = ExcelBorderStyle.Thin;
                    ws.Cells[1, 1, reportsTable.Rows.Count + 1, reportsTable.Columns.Count].Style.Border.Right.Style = ExcelBorderStyle.Thin;
                    ws.Cells[1, 1, reportsTable.Rows.Count + 1, reportsTable.Columns.Count].Style.Border.Bottom.Style = ExcelBorderStyle.Thin;
                    */
                    //Header to Bold
                    ws.Cells[1, 1, 1, reportsTable.Columns.Count].Style.Font.Bold = true;
                    //Header to color
                    ws.Cells[1, 1, 1, reportsTable.Columns.Count].Style.Font.Color.SetColor(Color.Black);
                    //Data row back color
                    //ws.Cells[2, 1, reportsTable.Rows.Count + 1, reportsTable.Columns.Count].Style.Fill.PatternType = ExcelFillStyle.Solid;
                    //ws.Cells[2, 1, reportsTable.Rows.Count + 1, reportsTable.Columns.Count].Style.Fill.BackgroundColor.SetColor(Color.White);

                    //Autofit columns
                    ws.Cells[ws.Dimension.Address.ToString()].AutoFitColumns();
                    ws.Column(1).AutoFit();
                    //For sending XML back
                    System.Web.HttpContext.Current.Response.Clear();
                    System.Web.HttpContext.Current.Response.ContentType = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";
                    //System.Web.HttpContext.Current.Response.AddHeader("content-disposition", "attachment;  filename=" + reportName + ".xlsx");
                    System.Web.HttpContext.Current.Response.AddHeader("content-disposition", "attachment;  filename=" + clientsidefileName);

                    System.Web.HttpContext.Current.Response.BinaryWrite(p.GetAsByteArray());
                    System.Web.HttpContext.Current.Response.End();
                }
            }
            
            
        }

        public class SqlToDataSet
        {
            public DataSet results;

            public SqlToDataSet(string sqlStatement, string connectionString, string sheetName)
            {

                try
                {
                    SqlConnection sqlConnection = new SqlConnection(connectionString);
                    SqlCommand cmd = new SqlCommand();
                    cmd.CommandText = sqlStatement;
                    cmd.CommandType = CommandType.Text;
                    cmd.Connection = sqlConnection;
                    cmd.CommandTimeout = 600;

                    sqlConnection.Open();

                    SqlDataAdapter adp = new SqlDataAdapter();
                    DataSet dset = new DataSet();
                    adp.SelectCommand = cmd;
                    adp.Fill(dset, sheetName);

                    sqlConnection.Close();
                    results = dset;
                }
                catch (Exception ex)
                {
                    results = null;
                }
            }
        }
        public void ExecINFiling()
        {
            string[] sheetNames = sheetName.Replace("INFILING_", "").Split('_');
            string ReportId = rds.Tables[0].Rows[0]["ReportId"].ToString();
            using (ExcelPackage p = new ExcelPackage())
            {
                //Set the Document properties
                p.Workbook.Properties.Author = "Crimson Web Application";
                p.Workbook.Properties.Title = sheetName;
                for(int i = 0; i < sheetNames.Length; i++)
                {
                    //Create a sheet
                    p.Workbook.Worksheets.Add(sheetNames[i]);
                    ExcelWorksheet ws = p.Workbook.Worksheets[i + 1];
                    //ws.Name = "Sample Worksheet"; //name the sheet as needed
                    ws.Cells.Style.Font.Size = 11; //Default font size for whole sheet
                    ws.Cells.Style.Font.Name = "Calibri"; //Default Font name for whole sheet
                    ws.View.ShowGridLines = true;
                    //get data
                    DataSet dset = new DataSet();
                    string _sqlStatement = "select * from INFILING_" + sheetNames[i] + "_" + ReportId;
                    string connStr = connectionSring;
                    SqlToDataSet sqlDataset = new SqlToDataSet(_sqlStatement, connStr, sheetNames[i]);
                    dset = sqlDataset.results;

                    DataTable reportsTable = dset.Tables[0];
                    //Load the data - Take care of Date columns
                    var rowCount = reportsTable.Rows.Count;
                    //var worksheet = package.Workbook.Worksheets.Add(repName + "_" + (i + 1));
                    ws.Cells["A1"].LoadFromDataTable(reportsTable, true);
                    //if empty set to null
                    foreach (var cell in ws.Cells)
                    {
                        if (cell.Value.ToString() == "")
                        {
                            cell.Value = null;
                        }
                    }

                    if (dset.Tables[0].Rows.Count > 0)
                    {
                        #region [[ For Formatting : Date and $ Values ]]
                        //For Date Columns
                        var dateColumns = from DataColumn d in reportsTable.Columns
                                              //where d.DataType == typeof(DateTime) || d.ColumnName.Contains("Date")
                                          where d.DataType == typeof(DateTime)
                                          select d.Ordinal + 1;

                        foreach (var dc in dateColumns)
                        {
                            //int test = dc;
                            ws.Cells[2, dc, rowCount + 1, dc].Style.Numberformat.Format = "mm/dd/yyyy";
                        }

                        //For Decimal Columns
                        var decimalColumns = from DataColumn q in reportsTable.Columns
                                             where q.DataType == typeof(Decimal)
                                             select q.Ordinal + 1;

                        foreach (var dci in decimalColumns)
                        {
                            int test = dci;
                            ws.Cells[2, dci, rowCount + 1, dci].Style.Numberformat.Format = "$#,##0.00";
                        }
                        #endregion
                    }
                    #region [[ For Reference ]]
                    //Helpful Links
                    //http://epplus.codeplex.com/discussions/261602
                    //https://gist.github.com/SamWM/1031793
                    //http://epplus.codeplex.com/wikipage?title=FAQ&referringTitle=Documentation
                    //Load the data - NO formating but this shows number for the Date column
                    //ws.Cells["A1"].LoadFromDataTable(reportsTable, true);
                    #endregion

                    /*Bordor style
                    ws.Cells[1, 1, reportsTable.Rows.Count + 1, reportsTable.Columns.Count].Style.Border.Left.Style = ExcelBorderStyle.Thin;
                    ws.Cells[1, 1, reportsTable.Rows.Count + 1, reportsTable.Columns.Count].Style.Border.Top.Style = ExcelBorderStyle.Thin;
                    ws.Cells[1, 1, reportsTable.Rows.Count + 1, reportsTable.Columns.Count].Style.Border.Right.Style = ExcelBorderStyle.Thin;
                    ws.Cells[1, 1, reportsTable.Rows.Count + 1, reportsTable.Columns.Count].Style.Border.Bottom.Style = ExcelBorderStyle.Thin;
                    */
                    //Header to Bold
                    ws.Cells[1, 1, 1, reportsTable.Columns.Count].Style.Font.Bold = true;
                    //Header to color
                    ws.Cells[1, 1, 1, reportsTable.Columns.Count].Style.Font.Color.SetColor(Color.Black);
                    //Data row back color
                    //ws.Cells[2, 1, reportsTable.Rows.Count + 1, reportsTable.Columns.Count].Style.Fill.PatternType = ExcelFillStyle.Solid;
                    //ws.Cells[2, 1, reportsTable.Rows.Count + 1, reportsTable.Columns.Count].Style.Fill.BackgroundColor.SetColor(Color.White);

                    //Autofit columns
                    ws.Cells[ws.Dimension.Address.ToString()].AutoFitColumns();
                    ws.Column(1).AutoFit();
                }
                
                //For sending XML back
                System.Web.HttpContext.Current.Response.Clear();
                System.Web.HttpContext.Current.Response.ContentType = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";
                //System.Web.HttpContext.Current.Response.AddHeader("content-disposition", "attachment;  filename=" + reportName + ".xlsx");
                System.Web.HttpContext.Current.Response.AddHeader("content-disposition", "attachment;  filename=" + clientsidefileName);

                System.Web.HttpContext.Current.Response.BinaryWrite(p.GetAsByteArray());
                System.Web.HttpContext.Current.Response.End();
            }
        }
    }    

    public class ExcelResultForVAStateFiling: ExcelResult
    {
        public override void ExecuteResult(ControllerContext context)
        {
            using (ExcelPackage p = new ExcelPackage())
            {
                //Set the Document properties
                p.Workbook.Properties.Author = "Crimson Web Application";
                p.Workbook.Properties.Title = sheetName;
                try
                {
                    using (SqlConnection sqlConnection = new SqlConnection(connectionSring))
                    {
                        DataSet results;
                        SqlCommand cmd = new SqlCommand();
                        cmd.CommandType = CommandType.Text;
                        cmd.Connection = sqlConnection;
                        cmd.CommandTimeout = 600;

                        SqlDataAdapter adp = new SqlDataAdapter();
                        DataSet dset = new DataSet();
                        
                        //prepare data
                        cmd.CommandText = string.Format(sqlStatement + ", '{0}'", "Prepare Data");

                        sqlConnection.Open();
                        adp.SelectCommand = cmd;
                        adp.Fill(dset, "PrepareData");
                        results = dset;
                        if (results.Tables[0].Rows[0]["SUCCESS"].ToString() == "1")
                        {
                            //CONTINUE
                            for (int i = 0; i < commands.Count; i++)
                            {
                                //Create a sheet
                                p.Workbook.Worksheets.Add(commands[i]);
                                ExcelWorksheet ws = p.Workbook.Worksheets[i + 1];
                                //ws.Name = "Sample Worksheet"; //name the sheet as needed
                                ws.Cells.Style.Font.Size = 11; //Default font size for whole sheet
                                ws.Cells.Style.Font.Name = "Calibri"; //Default Font name for whole sheet
                                ws.View.ShowGridLines = true;
                                //get data
                                dset = new DataSet();
                                cmd.CommandText = string.Format(sqlStatement + ", '{0}'", commands[i]);
                                adp.SelectCommand = cmd;
                                adp.Fill(dset, commands[i]);
                                if (dset == null || dset.Tables.Count == 0) continue;
                                DataTable reportsTable = dset.Tables[0];
                                //Load the data - Take care of Date columns
                                var rowCount = reportsTable.Rows.Count;
                                //var worksheet = package.Workbook.Worksheets.Add(repName + "_" + (i + 1));
                                ws.Cells["A1"].LoadFromDataTable(reportsTable, true);
                                //if empty set to null
                                foreach (var cell in ws.Cells)
                                {
                                    if (cell.Value.ToString() == "")
                                    {
                                        cell.Value = null;
                                    }
                                }

                                if (dset.Tables[0].Rows.Count > 0)
                                {
                                    #region [[ For Formatting : Date and $ Values ]]
                                    //For Date Columns
                                    var dateColumns = from DataColumn d in reportsTable.Columns
                                                          //where d.DataType == typeof(DateTime) || d.ColumnName.Contains("Date")
                                                      where d.DataType == typeof(DateTime)
                                                      select d.Ordinal + 1;

                                    foreach (var dc in dateColumns)
                                    {
                                        //int test = dc;
                                        ws.Cells[2, dc, rowCount + 1, dc].Style.Numberformat.Format = "mm/dd/yyyy";
                                    }

                                    //For Decimal Columns
                                    var decimalColumns = from DataColumn q in reportsTable.Columns
                                                         where q.DataType == typeof(Decimal)
                                                         select q.Ordinal + 1;

                                    foreach (var dci in decimalColumns)
                                    {
                                        int test = dci;
                                        ws.Cells[2, dci, rowCount + 1, dci].Style.Numberformat.Format = "$#,##0.00";
                                    }
                                    #endregion
                                }

                                //Header to Bold
                                ws.Cells[1, 1, 1, reportsTable.Columns.Count].Style.Font.Bold = true;
                                //Header to color
                                ws.Cells[1, 1, 1, reportsTable.Columns.Count].Style.Font.Color.SetColor(Color.Black);

                                //Autofit columns
                                ws.Cells[ws.Dimension.Address.ToString()].AutoFitColumns();
                                ws.Column(1).AutoFit();
                            }

                            //DELETE TEMP TABLES
                            dset = new DataSet();
                            cmd.CommandText = string.Format(sqlStatement + ", '{0}'", "delete");
                            adp.SelectCommand = cmd;
                            adp.Fill(dset, "delete");
                            results = dset;
                            if (results.Tables[0].Rows[0]["SUCCESS"].ToString() == "1")
                            {
                                //delete success
                            }
                            sqlConnection.Close();
                        }
                        else
                        {
                            sqlConnection.Close();
                            throw new Exception("Error preparing data.");
                        }                        
                    }
                }
                catch(Exception ex)
                {
                    p.Workbook.Worksheets.Add("NoData");
                }                    

                //For sending XML back
                System.Web.HttpContext.Current.Response.Clear();
                System.Web.HttpContext.Current.Response.ContentType = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";
                //System.Web.HttpContext.Current.Response.AddHeader("content-disposition", "attachment;  filename=" + reportName + ".xlsx");
                System.Web.HttpContext.Current.Response.AddHeader("content-disposition", "attachment;  filename=" + clientsidefileName);

                System.Web.HttpContext.Current.Response.BinaryWrite(p.GetAsByteArray());
                System.Web.HttpContext.Current.Response.End();
            }
        }
        private List<string> commands = new List<string>
        {
            "Header",
            "scheduleA",
            "scheduleB",
            "scheduleC",
            "scheduleD",
            "scheduleE",
            "scheduleF",
            "scheduleG",
            "scheduleH",
            "scheduleI"
        };
    }
}