﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Data;
using System.Linq;
using System.Web.Mvc;
using System.Data.Linq;
using System.ComponentModel.DataAnnotations.Schema;

using cmdiapp.n.core.Areas.crm.Domain.Data;
using cmdiapp.n.core.Domain.ViewModels;
namespace cmdiapp.n.core.Areas.crm.Domain.Models
{
    [Table("dmFLAG")]
    public class dmFLAG : _entityBase_crm
    {
        [Column]
        [Key]
        public int FLAGID { get; set; }

        [Column]
        [Required(ErrorMessage = "The Flag is required.")]
        public string FLAG { get; set; }

        [Column]
        [Required(ErrorMessage = "The Description is required.")]
        public string FLAGDESC { get; set; }

        [Column]
        public short? PRIORITY { get; set; }

        [Column]
        public string COMMENT { get; set; }
        
        [Column]
        public DateTime? LASTUSED { get; set; }

        [Column]
        public DateTime? UPDATEDON { get; set; }
        [Column]
        public bool? TOPFLAG { get; set; }
    }

    [Table("jtFLAG")]
    public class jtFLAG : _entityBase_crm, iItemType
    {
        [Column]
        [Key]
        public int jtFLAGID { get; set; }

        [Column]
        public int PID { get; set; }

        [Column]
        public int FLAGID { get; set; }
        [ForeignKey("FLAGID")]
        public virtual dmFLAG dmFLAG { get; set; }

        [Column]
        public DateTime? UPDATEDON { get; set; }
        public string UPDATEDONs
        {
            get
            {
                return (UPDATEDON == null ? "" : string.Format("{0:MM/dd/yyyy htt}", UPDATEDON));
            }
        }

        [Column("_updating_uid")]
        public short? updating_uid { get; set; }
    }



    [Table("lkCONTFLAG")]
    public class lkCONTFLAG : _entityBase_crm
    {
        [Column]
        [Key]
        public Int16 CONTFLAGID { get; set; }

        [Column]
        [Required(ErrorMessage = "The Contact Flag is required.")]
        [StringLength(5, ErrorMessage = "Contact Flag cannot be longer than 5 characters.")]
        public string CONTFLAG { get; set; }

        [Column]
        [Required(ErrorMessage = "The Description is required.")]
        [StringLength(50, ErrorMessage = "Description cannot be longer than 50 characters.")]
        public string DESCRIP { get; set; }

        [Column]
        public Byte DEFFLAG { get; set; }

        [Column]
        public Byte SYSTEM { get; set; }

    }

    public class dmFLAG_ext
    {
        public int FLAGID { get; set; }

        public string FLAG { get; set; }

        public string FLAGDESC { get; set; }

        public string COMMENT { get; set; }

        public DateTime? LASTUSED { get; set; }

        public DateTime? UPDATEDON { get; set; }
        public bool? TOPFLAG { get; set; }        

        public int count_ { get; set; }

        public Int64 rowNo { get; set; }
    }

    public class Category
    {
        public int? CategoryID { get; set; }

        public string CategoryName { get; set; }
    }


    public class dmFLAG_extTest
    {
        public int FLAGID { get; set; }

        public string FLAG { get; set; }

        public string FLAGDESC { get; set; }

        public string COMMENT { get; set; }

        public DateTime? LASTUSED { get; set; }

        public DateTime? UPDATEDON { get; set; }

        public int count_ { get; set; }

        public Int64 rowNo { get; set; }

        public bool? Discontinued { get; set; }

        public int? CategoryID { get; set; }

        public virtual Category Category { get; set; }
    }

    public class lkCONTFLAG_ext
    {
        public Int16 CONTFLAGID { get; set; }

        public string CONTFLAG { get; set; }

        public string DESCRIP { get; set; }

        public Byte DEFFLAG { get; set; }

        public Byte SYSTEM { get; set; }

        public int count_ { get; set; }

        public Int64 rowNo { get; set; }

    }

    public class SPResult
    {
        public string RESULT { get; set; }
    }

    [Table("jtCONTFLAG")]
    public class jtCONTFLAG : _entityBase_crm, iItemType
    {
        [Column]
        [Key]
        public int jtCONTFLAGID { get; set; }

        [Column]
        public int CONTACTID { get; set; }

        [Column]
        public Int16 CONTFLAGID { get; set; }

        [ForeignKey("CONTFLAGID")]
        public virtual lkCONTFLAG lkCONTFLAG { get; set; }

        [Column]
        public DateTime? UPDATEDON { get; set; }
        public string UPDATEDONs
        {
            get
            {
                return (UPDATEDON == null ? "" : string.Format("{0:MM/dd/yyyy htt}", UPDATEDON));
            }
        }
    }
}