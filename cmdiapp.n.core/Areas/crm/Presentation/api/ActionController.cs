﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Net;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Web.Http;
using System.Web.Script.Serialization;
using System.Drawing;
using System.IO;
using System.Data.SqlClient;
using System.Collections.ObjectModel;
using System.Xml.Linq;

using Ninject;
using Ninject.Web.Mvc;

using AutoMapper;
using cmdiapp.n.core.Core;
using cmdiapp.n.core.Core.App_Start;
using cmdiapp.n.core.Domain.ViewModels;
using cmdiapp.n.core.Areas.crm.Core;
using cmdiapp.n.core.Areas.crm.Domain.Models;
using cmdiapp.n.core.Areas.crm.Domain.ViewModels;
using cmdiapp.n.core.Areas.crm.Domain.Data;
using cmdiapp.n.core.Areas.crm.Domain.Repositories;
using cmdiapp.n.core.Areas.crm.Domain.Services;
using cmdiapp.n.core.Areas.dataService.Domain.Data;
using cmdiapp.n.core.Areas.dataService.Domain.Models;
using cmdiapp.n.core.Areas.dataService.Domain.Services;
using cmdiapp.n.core.Presentation.Controllers;
using cmdiapp.n.core.Areas.query.Domain.Models;
using cmdiapp.n.core.Domain.Models;
using System.Data;
using cmdiapp.n.core.Library;

namespace cmdiapp.n.core.Areas.crm.Presentation.Controllers.api
{
    [Authorize]
    public class ActionController : ApiController
    {
        #region [[ Declaration ]]
        private I_entity_crm _entity_crm;
        private readonly IActionService _ActionService;
        private readonly IReportDataService _reportService;
        private userSession _userSession;
       
        #endregion
        
        #region [[ (constructor) SettingsController ]]
        public ActionController()
        {
            _entity_crm = I_entityManager_crm.getEntity();
            _ActionService = NinjectMVC.kernel.Get<IActionService>();
            _reportService = NinjectMVC.kernel.Get<IReportDataService>();
            _userSession = session.userSession;
        }
        #endregion

        #region [[ SQL Query]]
        const string _sql_exportAction = @"select [name],fullNameC AS fullName,note From Action ";
        const string _sql_exportActionCategory = @"select [name],labeling,note From ActionCategory";
        #endregion

        #region [[ CRUD ]]
        [HttpGet, Route("crm/api/Action/GetActionById/{id}")]
        [apiAuthorize(AccessElement = "People/Settings/Actions", AccessLevel = "v")]
        public Action_ GetActionById(int id)
        {
            return _entity_crm.Single<Action_>(a => a.id == id);
        }

        [HttpPost, Route("crm/api/Action/updateAction")]
        [apiAuthorize(AccessElement = "People/Settings/Actions", AccessLevel = "v")]
        public genericResponse updateAction(Action_ _action)
        {
            if (_action.id <= 0)
            {
                return NewAction(_action);
            }
            genericResponse res = new genericResponse();
            Action_ _act= _entity_crm.Single<Action_>(a => a.id == _action.id);
            if (_act != null)
            {
                try
                {
                    #region [[ check name ]]
                    bool uname = _ActionService.check_unqiuenessName(_action.name, _action.id);
                    if (!uname)
                    {
                        res.success = false;
                        res.message = "Action aleady exists.";
                        return res;
                    }
                    #endregion
                    _act.categoryId = _action.categoryId;
                    _act.name = _action.name;
                    _act.defaultSourceId = _action.defaultSourceId;
                    _act.note = _action.note;
                    _act.customizedColumns = _action.customizedColumns;
                    //_act.fromOtherDataInfo = _action.fromOtherDataInfo;
                    _act.updatedAtUtc = DateTime.UtcNow;
                    _entity_crm.Update(_act);
                    _entity_crm.CommitChanges();

                    res.success = true;
                    res.message = "Action is updated successfully.";
                }
                catch(Exception ex)
                {
                    res.success = false;
                    res.message = ex.Message;
                }
            }
            else
            {
                res.success = false;
                res.message = "Action not exists, please contact customer service.";
            }
            return res;
        }

        [HttpPost, Route("crm/api/Action/NewAction")]
        [apiAuthorize(AccessElement = "People/Settings/Actions", AccessLevel = "v")]
        public genericResponse NewAction(Action_ _action)
        {
            genericResponse res = new genericResponse();
            if (_action.id > 0)
            {
                res.success = false;
                res.message = "Invalid action.";
            }
            else
            {
                try
                {
                    #region [[ check name ]]
                    bool uname = _ActionService.check_unqiuenessName(_action.name, _action.id);
                    if (!uname)
                    {
                        res.success = false;
                        res.message = "Action aleady exists.";
                        return res;
                    }
                    #endregion
                    _action.createdAtUtc = DateTime.UtcNow;
                    _action.updatedAtUtc = DateTime.UtcNow;
                    _entity_crm.Add(_action);
                    _entity_crm.CommitChanges();

                    res.UniqueId = _entity_crm.Single<Action_>(a => a.name == _action.name).id;
                    res.success = true;
                    res.message = "Action is added successfully.";
                }
                catch (Exception ex)
                {
                    res.success = false;
                    res.message = ex.Message;
                }
            }
            return res;
        }

        [HttpPost, Route("crm/api/Action/DeleteActionById/{id}")]
        [apiAuthorize(AccessElement = "People/Settings/Actions", AccessLevel = "v")]
        public genericResponse DeleteActionById(int id)
        {
            genericResponse res = new genericResponse();
            try
            {
                //CHECK linked people records
                int recordcount= _entity_crm.All<ActionPeople>().Where(a => a.actionId == id).Count();
                if (recordcount > 0)
                {
                    res.success = false;
                    res.message = "Cannot delete action with one or more people records linked.";
                    return res;
                }
                Action_ _act = _entity_crm.Single<Action_>(a => a.id == id);
                _entity_crm.Delete(_act);
                _entity_crm.CommitChanges();
                res.success = true;
                res.message = "Action deleted.";
            }
            catch(Exception ex)
            {
                res.success = false;
                res.message = ex.Message;
            }
            return res;
        }


        [HttpGet, Route("crm/api/ActionCategory/GetActionCategoryById/{id}")]
        [apiAuthorize(AccessElement = "People/Settings/Actions", AccessLevel = "v")]
        public ActionCategory GetActionCategoryById(int id)
        {
            return _entity_crm.Single<ActionCategory>(a => a.id == id);
        }

        [HttpPost, Route("crm/api/ActionCategory/updateActionCategory")]
        [apiAuthorize(AccessElement = "People/Settings/Actions", AccessLevel = "v")]
        public genericResponse updateActionCategory(ActionCategory _action)
        {
            if (_action.id <= 0)
            {
                return NewActionCategory(_action);
            }
            genericResponse res = new genericResponse();
            ActionCategory _act = _entity_crm.Single<ActionCategory>(a => a.id == _action.id);
            if (_act != null)
            {
                try
                {
                    #region [[ check name ]]
                    bool uname = _ActionService.check_unqiuenessName_ActionCategory(_action.name, _action.id);
                    if (!uname)
                    {
                        res.success = false;
                        res.message = "Action Category aleady exists.";
                        return res;
                    }
                    #endregion
                    _act.name = _action.name;
                    _act.labeling = _action.labeling;
                    _act.customizedColumns = _action.customizedColumns;
                    _act.note = _action.note;
                    _act.updatedAtUtc = DateTime.UtcNow;
                    _entity_crm.Update(_act);
                    _entity_crm.CommitChanges();

                    res.success = true;
                    res.message = "Action Category is updated successfully.";
                }
                catch (Exception ex)
                {
                    res.success = false;
                    res.message = ex.Message;
                }
            }
            else
            {
                res.success = false;
                res.message = "Action Category not exists, please contact customer service.";
            }
            return res;
        }

        [HttpPost, Route("crm/api/ActionCategory/NewActionCategory")]
        [apiAuthorize(AccessElement = "People/Settings/Actions", AccessLevel = "v")]
        public genericResponse NewActionCategory(ActionCategory _action)
        {
            genericResponse res = new genericResponse();
            if (_action.id > 0)
            {
                res.success = false;
                res.message = "Invalid Action Category.";
            }
            else
            {
                try
                {
                    #region [[ check name ]]
                    bool uname = _ActionService.check_unqiuenessName_ActionCategory(_action.name, _action.id);
                    if (!uname)
                    {
                        res.success = false;
                        res.message = "Action Category aleady exists.";
                        return res;
                    }
                    #endregion
                    _action.createdAtUtc = DateTime.UtcNow;
                    _action.updatedAtUtc = DateTime.UtcNow;
                    _entity_crm.Add(_action);
                    _entity_crm.CommitChanges();

                    res.success = true;
                    res.message = "Action Category is added successfully.";
                }
                catch (Exception ex)
                {
                    res.success = false;
                    res.message = ex.Message;
                }
            }
            return res;
        }

        [HttpPost, Route("crm/api/ActionCategory/DeleteActionCategoryById/{id}")]
        [apiAuthorize(AccessElement = "People/Settings/Actions", AccessLevel = "v")]
        public genericResponse DeleteActionCategoryById(int id)
        {
            genericResponse res = new genericResponse();
            try
            {
                //CHECK linked Action
                int recordcount = _entity_crm.All<Action_>().Where(a => a.categoryId == id).Count();
                if (recordcount > 0)
                {
                    res.success = false;
                    res.message = "Cannot delete action category with one or more action records linked.";
                    return res;
                }
                ActionCategory _act = _entity_crm.Single<ActionCategory>(a => a.id == id);
                _entity_crm.Delete(_act);
                _entity_crm.CommitChanges();
                res.success = true;
                res.message = "Action deleted.";
            }
            catch (Exception ex)
            {
                res.success = false;
                res.message = ex.Message;
            }
            return res;
        }
        

        #region [[  Action PEOPLE   ]]
        
        [HttpGet, Route("crm/api/ActionPeople/GetActionPeopleByPID/{pid}")]
        [apiAuthorize(AccessElement = "People/Profile/Actions", AccessLevel = "v")]
        public List<ActionPeople> GetActionPeopleByPID(int pid)
        {
            List<ActionPeople> allap = _entity_crm.All<ActionPeople>().Where(a => a.PID == pid).OrderBy(a => a.Action.name).ToList();
            return allap;
        }

        #region [ crm/api/ActionPeople ]
        [HttpPost, Route("crm/api/ActionPeople/Add")]
        [apiAuthorize(AccessElement = "People/Profile/Actions", AccessLevel = "a")]
        public genericResponse Add(ActionPeople _ap)
        {
            if (_ap.actionId > 0 && _ap.PID > 0)
            {
                try
                {
                    int _actionId = _ap.actionId;
                    int _pid = _ap.PID;
                    ActionPeople _record = _entity_crm.Single<ActionPeople>(a => a.actionId == _actionId && a.PID == _pid);

                    // new record
                    _record = new ActionPeople();

                    _record.PID = _ap.PID;
                    _record.actionDate = _ap.actionDate;
                    _record.actionId = _ap.actionId;
                    _record.srceId = _ap.srceId;
                    _record.note = _ap.note;
                    _record.customizedData = _ap.customizedData;
                    _record.updatedAtUtc = DateTime.UtcNow;

                    _entity_crm.Add(_record);
                    _entity_crm.CommitChanges();
                    _actionId = _record.actionId;

                    if (_actionId <= 0)
                    {
                        genericResponse _response = new genericResponse() { success = false, UniqueId = _record.id };
                        return _response;
                    }
                    else
                    {
                        genericResponse _response = new genericResponse() { success = true, UniqueId = _record.id };
                        return _response;
                    }
                }

                catch(Exception ex)
                {
                    genericResponse _response = new genericResponse() { success = false, UniqueId = 0 };
                    return _response;
                }
            }
            else
            {
                genericResponse _response = new genericResponse() { success = false, UniqueId = 0 };
                return _response;
            }
        }

        [HttpGet, Route("crm/api/ActionPeople/getbyId/{id}")]
        [apiAuthorize(AccessElement = "People/Profile/Actions", AccessLevel = "v")]
        public ActionPeople GetActionPeople(int id)
        {
            try
            {
                ActionPeople record = _entity_crm.Single<ActionPeople>(ap => ap.id == id);
                return record;
            }
            catch (Exception ex)
            {
                return new ActionPeople();
            }
        }
        [HttpPost, Route("crm/api/ActionPeople/Update")]
        [apiAuthorize(AccessElement = "People/Profile/Actions", AccessLevel = "e")]
        public genericResponse UpdateActionPeople(ActionPeople _ap)
        {
            try
            {
                ActionPeople record = _entity_crm.Single<ActionPeople>(ap => ap.id == _ap.id);
                if (record != null)
                {
                    record.actionDate = _ap.actionDate;
                    record.actionId = _ap.actionId;
                    record.srceId = _ap.srceId;
                    record.note = _ap.note;
                    record.customizedData = _ap.customizedData;
                    record.updatedAtUtc = DateTime.UtcNow;

                    _entity_crm.Update(record);
                    _entity_crm.CommitChanges();

                    genericResponse _response = new genericResponse() { success = true, __count = 0 };
                    return _response;
                }
                else
                {
                    return new genericResponse() { success = false, message = "Cannot find the record." };
                }

            }
            catch(Exception ex)
            {
                genericResponse _response = new genericResponse() { success = false, message = ex.Message };
                return _response;
            }
        }
        [HttpPost, Route("crm/api/ActionPeople/Delete")]
        [apiAuthorize(AccessElement = "People/Profile/Actions", AccessLevel = "d")]
        public genericResponse DeleteActionPeople(ActionPeople _ap)
        {
            try
            {
                int _actionId = _ap.actionId;
                int _pid = _ap.PID;
                int id = _ap.id;
                if (id != null && id > 0)
                {
                    // log for history
                    string _sql = "Insert into zWebAppLog (UPDTYPE,TABLENAME,KEYFIELD,KEYVALUE,UID) " +
                        "select 'D','ActionPeople','id', id, {1} from ActionPeople WHERE id = {0}";
                    _entity_crm.getContext().Database.ExecuteSqlCommand(_sql, id, crmSession.UID());

                    // delete joined move action records
                    _entity_crm.Delete<jtMOVEACTION>(a => a.ID == id);
                    _entity_crm.CommitChanges();

                    // delete file attached
                    _entity_crm.Delete<ACTIONPEOPLEDOC>(a => a.ACTIONPEOPLEID == id);
                    _entity_crm.CommitChanges();

                    _entity_crm.Delete<ActionPeople>(a => a.id == id);
                }
                else
                {
                    // log for history
                    string _sql = "Insert into zWebAppLog (UPDTYPE,TABLENAME,KEYFIELD,KEYVALUE,UID) " +
                        "select 'D','ActionPeople','id', id, {2} from ActionPeople WHERE actionId = {0} AND PID = {1}";
                    _entity_crm.getContext().Database.ExecuteSqlCommand(_sql, _actionId, _pid, crmSession.UID());

                    // delete joined move action records
                    _sql = "delete j from jtMOVEACTION j inner join ActionPeople a on j.ID = a.id where a.actionId = {0} AND a.PID = {1}";
                    _entity_crm.getContext().Database.ExecuteSqlCommand(_sql, _actionId, _pid, crmSession.UID());

                    _entity_crm.Delete<ActionPeople>(a => a.actionId == _actionId && a.PID == _pid);
                }
                
                _entity_crm.CommitChanges();

                genericResponse _response = new genericResponse() { success = true, __count = 0 };
                return _response;
            }
            catch
            {
                genericResponse _response = new genericResponse() { success = false, __count = 0 };
                return _response;
            }
        }

        [HttpGet, Route("crm/api/ActionPeople/GetActionPeopleListByPid")]
        [apiAuthorize(AccessElement = "People/Profile/Actions", AccessLevel = "v")]
        public genericResponse GetActionPeopleListByPid(
            int id,
            int? page = null,
            int? pageSize = null,
            int? categoryId = null,
            string date = "")
        {
            try
            {
                int _pageSize = (pageSize == null || pageSize.Value == 0 ? 10 : pageSize.Value);
                int _pageNo = (page == null || page.Value == 0 ? 1 : page.Value);

                string sortField = HttpContext.Current.Request.QueryString["sort[0][field]"];
                string sortDir = HttpContext.Current.Request.QueryString["sort[0][dir]"];
                QueryRuntimeSort sort = new QueryRuntimeSort { dir = sortDir, field = sortField };
                List<ActionPeople_ext> countAndResults = _ActionService.GetActionPeopleWithCount(
                        id,
                        _pageNo,
                        _pageSize,
                        sort,
                        categoryId,
                        date
                    );

                return new genericResponse()
                {
                    success = true,
                    __count = countAndResults.Count > 0 ? countAndResults[0].count_ : 0,
                    results = countAndResults.ToList<iItemType>()
                };

            }
            catch (Exception ex)
            {
                return new genericResponse()
                {
                    success = false,
                    message = "An error occurred. Unable to get results.",
                    messageKey = (ex.Message ?? "") + (ex.InnerException?.Message ?? "")
                };
            }


        }

        [HttpPost, Route("crm/api/ActionPeople/Export")]
        [apiAuthorize(AccessElement = "People/Profile/Actions", AccessLevel = "v")]
        public object ActionPeopleExport(
            int id,
            int? categoryId = null,
            string date = "")
        {
            try
            {
                QueryRuntimeSort sort = new QueryRuntimeSort { dir = "updatedAtUtc", field = "desc" };
                Tuple<int, List<ActionPeople_export>> countAndResults = _ActionService.GetActionPeopleWithCountForExport(
                        id,
                        1,
                        Query.maxRowsToExport(),
                        sort,
                        categoryId,
                        date
                    );
                DataTable table = countAndResults.Item2.ToDataTable();

                if (table != null && table.Rows.Count > 0)
                {

                    Tuple<string, string> filenames = new QExportExcel().ToFile(table, $"Donor{id}");
                    return new
                    {
                        success = true,
                        serverFileName = filenames.Item1,
                        clientFileName = filenames.Item2
                    };
                }
                else
                {
                    return new
                    {
                        success = false,
                        message = "The query returned no results."
                    };
                }
            }
            catch (Exception ex)
            {
                return new genericResponse
                {
                    success = false,
                    message = "An error occurred.  Unable to export results.",
                    messageKey = (ex.Message ?? "") + (ex.InnerException?.Message ?? "")
                };
            }

        }
        #endregion

        #region [[ actionpeople attachment ]]
        [HttpPost, Route("crm/api/ACTIONPEOPLEDOC/RemoveAttach/{id}")]
        [apiAuthorize(AccessElement = "People/Profile/Actions", AccessLevel = "v")]
        public genericResponse RemoveAttach(int id)
        {
            try
            {
                ACTIONPEOPLEDOC _record = _entity_crm.Single<ACTIONPEOPLEDOC>(a => a.ACTIONPEOPLEDOCID == id);

                if (_record != null)
                {
                    _entity_crm.Delete(_record);
                    _entity_crm.CommitChanges();
                }

                genericResponse _response = new genericResponse() { success = true };
                return _response;
            }
            catch
            {
                genericResponse _response = new genericResponse() { success = false };
                return _response;
            }
        }

        [HttpGet, Route("crm/api/ACTIONPEOPLEFileNames/{id}")]
        [apiAuthorize(AccessElement = "People/Profile/Actions", AccessLevel = "v")]
        public List<ACTIONPEOPLEDOC> AttachFileName(int id)
        {
            try
            {
                List<ACTIONPEOPLEDOC> res = _entity_crm.All<ACTIONPEOPLEDOC>().Where(a => a.ACTIONPEOPLEID == id).ToList();
                return res;
            }
            catch
            {
                return null;
            }
        }
        #endregion   

        [HttpGet, Route("crm/api/ActionPeople/Filters/{pid}")]
        [apiAuthorize(AccessElement = "People/Profile/Actions", AccessLevel = "v")]
        public List<DataTableFilter> GetDataTableFiltersForActionPeople(int pid)
        {
            var aps = _entity_crm.All<ActionPeople>().Where(m => m.PID == pid);

            var acs = _entity_crm.All<ActionCategory>()
                .Where(ac => aps.Select(a => a.Action.categoryId).Distinct().ToList().Contains(ac.id));

            List<DataTableFilter> filters = new List<DataTableFilter>
            {
                new DataTableFilter
                {
                    label = "Category",
                    options = acs
                        .Select(f => new DataTableFilterOption { label = f.name, value = f.id.ToString() }).ToArray(),
                    paramName = "categoryId",
                    multiValue = false
                },
                DateFilterFactory<ActionPeople>.GetDataTableFilter("Date", "date"),
                new DataTableFilter
                {
                    label = "Active",
                    options = new DataTableFilterOption[2]{
                        new DataTableFilterOption(){label="true",value="true" },
                        new DataTableFilterOption(){label="false",value="false" }},
                    paramName = "active",
                    multiValue = false
                }
            };
            return filters;
        }
        #endregion
        #endregion

        #region[PEOPLE->SETTINGS->Action]
        [HttpGet, Route("crm/api/ActionCategory/labelingList")]
        [apiAuthorize(AccessElement = @"People/Settings/Actions", AccessLevel = "v")]
        public List<labelingList> labelingList()
        {
            return new List<labelingList>()
            {
                new labelingList("C","Category and Action (e.g. Conference Attendee)"),
                new labelingList("P","Action and Category (e.g. Runner Participant)"),
                new labelingList("H","Category - Action (e.g. Volunteer - Booth)"),
                new labelingList("O","Action Only (e.g. Other)")
            };
        }

        [HttpPost, Route("crm/api/Action/{categoryId}/ExportActionKey")]
        [apiAuthorize(AccessElement = "People/Settings/Actions", AccessLevel = "v")]
        public string ExportActionKey(int categoryId, string searchText)
        {
            if (string.IsNullOrEmpty(searchText)) searchText = "";

            searchParam param = new searchParam();
            param.searchText = searchText;

            string key_ = System.Guid.NewGuid().ToString();
            HttpRuntime.Cache.Insert(key_, param, null, DateTime.UtcNow.Add(new TimeSpan(1, 0, 1)), System.Web.Caching.Cache.NoSlidingExpiration);

            return key_;

        }

        [HttpGet, Route("crm/api/Action/{categoryId}/ExportAction")]
        [apiAuthorize(AccessElement = "People/Settings/Actions", AccessLevel = "v")]
        public string ExportAction(int categoryId, string key)
        {
            searchParam _param = (searchParam)HttpRuntime.Cache[key];
            string sToday = String.Format("{0:yyyyMMMdd__hh_mm_ss tt}", DateTime.Now);
            string moduleName = "Action";
            string fileName = moduleName + "_" + System.Guid.NewGuid() + "_" + _userSession.UserName + "_" + sToday;

            DataSet ds = null;
            if (string.IsNullOrEmpty(_param.searchText))
            {
                string sql = _sql_exportAction;
                if (categoryId > 0)
                {
                    sql += " where categoryId = " + categoryId.ToString()+ " ORDER BY categoryId ";
                }
                ds = _reportService.get_dataset_w_sql__single(sql, "Action");
            }
            else
            {
                _param.searchText = Library.util.kill_sqlBlacklistWord(_param.searchText);

                string _where = "";

                if (!string.IsNullOrEmpty(_param.searchText))
                {
                    #region [ Split Search Text > string1, string2 & string3 and create "Where" statement ]
                    char[] delimiterChars = { ' ', ',', '.', ';', ':', '|', '\t' };

                    string[] words = _param.searchText.Split(delimiterChars);
                    if (words.Count() > 0)
                    {
                        string string1 = words[0];
                        string string2 = (words.Count() > 1 ? words[1] : "");
                        string string3 = (words.Count() > 2 ? words[2] : "");

                        #region [ Where Clause ]
                        switch (words.Count())
                        {
                            case 1: 
                                _where = string.Format(" fullNameC LIKE ''%{0}%'' OR fullNameC LIKE ''%{0}%'' ", string1);
                                break;

                            case 2: 
                                _where = string.Format(" fullNameC LIKE ''%{0}%'' or fullNameC LIKE ''%{1}%'' ", string1, string2);
                                break;

                            default:
                                break;
                        }
                        #endregion
                    }

                    #endregion
                }
                //We need SQL with Where condition
                string sql = _sql_exportAction + " Where ({0}) AND categoryId = " + categoryId.ToString() + " ORDER BY categoryId ";
                //Compose the SQL
                sql = String.Format(sql, _where);

                ds = _reportService.get_dataset_w_sql__single(sql, "Action");

            }

            if (ds != null)
            {
                //HttpResponse _resp = CreateExcelFile.CreateExcelDocument(_dt.Copy(), fileName, HttpContext.Current.Response);
                string key_ = System.Guid.NewGuid().ToString();
                HttpRuntime.Cache.Insert(key_, ds, null, DateTime.UtcNow.Add(new TimeSpan(1, 0, 1)), System.Web.Caching.Cache.NoSlidingExpiration);
                //return the Key
                return key_;
            }
            else
            {
                return null;
            }
        }

        [HttpPost, Route("crm/api/ActionCategory/ExportActionCategoryKey")]
        [apiAuthorize(AccessElement = "People/Settings/Actions", AccessLevel = "v")]
        public string ExportActionCategoryKey(string searchText)
        {
            if (string.IsNullOrEmpty(searchText)) searchText = "";

            searchParam param = new searchParam();
            param.searchText = searchText;

            string key_ = System.Guid.NewGuid().ToString();
            HttpRuntime.Cache.Insert(key_, param, null, DateTime.UtcNow.Add(new TimeSpan(1, 0, 1)), System.Web.Caching.Cache.NoSlidingExpiration);

            return key_;

        }

        [HttpGet, Route("crm/api/ActionCategory/ExportActionCategory")]
        [apiAuthorize(AccessElement = "People/Settings/Actions", AccessLevel = "v")]
        public string ExportActionCategory(string key)
        {
            searchParam _param = (searchParam)HttpRuntime.Cache[key];
            string sToday = String.Format("{0:yyyyMMMdd__hh_mm_ss tt}", DateTime.Now);
            string moduleName = "ActionCategory";
            string fileName = moduleName + "_" + System.Guid.NewGuid() + "_" + _userSession.UserName + "_" + sToday;

            DataSet ds = null;
            if (string.IsNullOrEmpty(_param.searchText))
            {
                ds = _reportService.get_dataset_w_sql__single(_sql_exportActionCategory+ " order by name  ", "ActionCategory");
            }
            else
            {
                _param.searchText = Library.util.kill_sqlBlacklistWord(_param.searchText);

                string _where = "";

                if (!string.IsNullOrEmpty(_param.searchText))
                {
                    #region [ Split Search Text > string1, string2 & string3 and create "Where" statement ]
                    char[] delimiterChars = { ' ', ',', '.', ';', ':', '|', '\t' };

                    string[] words = _param.searchText.Split(delimiterChars);
                    if (words.Count() > 0)
                    {
                        string string1 = words[0];
                        string string2 = (words.Count() > 1 ? words[1] : "");
                        string string3 = (words.Count() > 2 ? words[2] : "");

                        #region [ Where Clause ]
                        switch (words.Count())
                        {
                            case 1: 
                                _where = string.Format("name LIKE ''%{0}%'' ", string1);
                                break;

                            case 2: 
                                _where = string.Format("name LIKE ''%{0}%'' OR name LIKE ''%{1}%''", string1, string2);
                                break;

                            default:
                                break;
                        }
                        #endregion
                    }

                    #endregion
                }
                //We need SQL with Where condition
                string sql = _sql_exportActionCategory + " Where {0} ORDER BY name ";
                //Compose the SQL
                sql = String.Format(sql, _where);

                ds = _reportService.get_dataset_w_sql__single(sql, "ActionCategory");

            }

            if (ds != null)
            {
                //HttpResponse _resp = CreateExcelFile.CreateExcelDocument(_dt.Copy(), fileName, HttpContext.Current.Response);
                string key_ = System.Guid.NewGuid().ToString();
                HttpRuntime.Cache.Insert(key_, ds, null, DateTime.UtcNow.Add(new TimeSpan(1, 0, 1)), System.Web.Caching.Cache.NoSlidingExpiration);
                //return the Key
                return key_;
            }
            else
            {
                return null;
            }
        }

        [HttpGet, Route("crm/api/ActionCategory/GetActionCategory")]
        [apiAuthorize(AccessElement = "People/Settings/Actions", AccessLevel = "v")]
        public genericResponse GetActionCategory(string searchText, int? page, int? pageSize)
        {
            #region [ Retrieve Input ]
            string _searchText = Library.util.kill_sqlBlacklistWord((searchText ?? "").Trim().Replace("'", "''''"));

            int _pageSize = (pageSize == null || pageSize.Value == 0 ? 10 : pageSize.Value);
            int _pageNo = (page == null || page.Value == 0 ? 1 : page.Value);

            string sortField = HttpContext.Current.Request.QueryString["sort[0][field]"];
            string sortDir = HttpContext.Current.Request.QueryString["sort[0][dir]"];
            string _sortOptions = "";
            if (!string.IsNullOrEmpty(sortField))
                _sortOptions = sortField;
            if (!string.IsNullOrEmpty(sortField) && !string.IsNullOrEmpty(sortDir))
                _sortOptions = _sortOptions + " " + sortDir;
            #endregion

            List<ActionCategory_ext> _list = new List<ActionCategory_ext>();
            genericResponse _response;

            if (!string.IsNullOrEmpty(_searchText))
            {
                _searchText = Library.util.kill_sqlBlacklistWord(_searchText);

                //int keywordid;

                if (string.IsNullOrEmpty(_sortOptions))
                    _sortOptions = " name";

                string _where = "";

                if (!string.IsNullOrEmpty(_searchText))
                {
                    #region [ Split Search Text > string1, string2 & string3 and create "Where" statement ]
                    char[] delimiterChars = { ' ', ',', '.', ';', ':', '|', '\t' };

                    string[] words = _searchText.Split(delimiterChars);

                    if (words.Count() > 0)
                    {
                        _where = string.Format(" [name] LIKE ''%{0}%'' ", words[0]);
                        for (int i = 1; i < words.Length; i++)
                        {
                            _where = _where + string.Format(" OR [name] LIKE ''%{0}%'' ", words[i]);
                        }

                        if (string.IsNullOrEmpty(_sortOptions)) _sortOptions = " name ASC";
                        _list = _ActionService.get_all_ActionCategory(_where, _sortOptions, _pageSize, _pageNo);
                    }
                    #endregion
                }

            }
            else
            {
                //Coming here when SearchText is null
                if (string.IsNullOrEmpty(_sortOptions)) _sortOptions = " name ASC";
                _list = _ActionService.get_all_ActionCategory("", _sortOptions, _pageSize, _pageNo);
            }

            Mapper.CreateMap<ActionCategory_ext, ActionCategoryView>();
            IEnumerable<iItemType> results = _list.Select(a => Mapper.Map<ActionCategory_ext, ActionCategoryView>(a)).ToList();
            if (results.Count() > 0)
            {
                _response = new genericResponse() { success = true, __count = _list.FirstOrDefault().count_, results = results.ToList() };
            }
            else
            {
                _response = new genericResponse() { success = true, __count = 0 };
            }
            return _response;
        }

        [HttpGet, Route("crm/api/Action/GetAction")]
        [apiAuthorize(AccessElement = "People/Settings/Actions", AccessLevel = "v")]
        public genericResponse GetAction(int categoryId, string searchText, int? page, int? pageSize)
        {
            #region [ Retrieve Input ]
            string _searchText = Library.util.kill_sqlBlacklistWord((searchText ?? "").Trim().Replace("'", "''''"));

            int _pageSize = (pageSize == null || pageSize.Value == 0 ? 10 : pageSize.Value);
            int _pageNo = (page == null || page.Value == 0 ? 1 : page.Value);

            string sortField = HttpContext.Current.Request.QueryString["sort[0][field]"];
            string sortDir = HttpContext.Current.Request.QueryString["sort[0][dir]"];
            string _sortOptions = "";
            if (!string.IsNullOrEmpty(sortField))
                _sortOptions = sortField;
            if (!string.IsNullOrEmpty(sortField) && !string.IsNullOrEmpty(sortDir))
                _sortOptions = _sortOptions + " " + sortDir;
            #endregion

            List<Action_ext> _list = new List<Action_ext>();
            genericResponse _response;

            if (!string.IsNullOrEmpty(_searchText))
            {
                _searchText = Library.util.kill_sqlBlacklistWord(_searchText);

                //int keywordid;

                if (string.IsNullOrEmpty(_sortOptions))
                    _sortOptions = " name";

                string _where = "";

                if (!string.IsNullOrEmpty(_searchText))
                {
                    #region [ Split Search Text > string1, string2 & string3 and create "Where" statement ]
                    char[] delimiterChars = { ' ', ',', '.', ';', ':', '|', '\t' };

                    string[] words = _searchText.Split(delimiterChars);

                    if (words.Count() > 0)
                    {
                        int __id;
                        bool success = Int32.TryParse(words[0], out __id);
                        if (success)
                        {
                            _where = string.Format(" id={1} OR [name] LIKE ''%{0}%'' OR fullNameC LIKE ''%{0}%'' ", words[0], __id);
                        }
                        else
                        {
                            _where = string.Format(" [name] LIKE ''%{0}%'' OR fullNameC LIKE ''%{0}%'' ", words[0]);
                        }
                        for (int i = 1; i < words.Length; i++)
                        {
                            _where = _where + string.Format(" OR [name] LIKE ''%{0}%'' ", words[i]);
                        }
                        if (categoryId > 0)
                            _where = string.Format(" categoryId= {0} AND ({1})", categoryId.ToString(), _where);
                        if (string.IsNullOrEmpty(_sortOptions)) _sortOptions = " categoryId ASC";
                        _list = _ActionService.get_all_Actions(_where, _sortOptions, _pageSize, _pageNo);
                    }
                    #endregion
                }

            }
            else
            {
                //Coming here when SearchText is null
                string _where = "";
                if (categoryId > 0)
                    _where = " categoryId=" + categoryId.ToString();
                if (string.IsNullOrEmpty(_sortOptions)) _sortOptions = " categoryId ASC";
                _list = _ActionService.get_all_Actions(_where, _sortOptions, _pageSize, _pageNo);
            }

            Mapper.CreateMap<Action_ext, ActionView>();
            IEnumerable<iItemType> results = _list.Select(a => Mapper.Map<Action_ext, ActionView>(a)).ToList();
            if (results.Count() > 0)
            {
                _response = new genericResponse() { success = true, __count = _list.FirstOrDefault().count_, results = results.ToList() };
            }
            else
            {
                _response = new genericResponse() { success = true, __count = 0 };
            }
            return _response;
        }


        [HttpPost, Route("crm/api/Action/MassDestroyAction/{actid}")]
        [sysAdminapiAuthorizeAttribute]
        public genericResponse MassDestroyAttribute(int actid)
        {
            try
            {
                //Let us call SP to Perform the task....
                string resp = _ActionService.MassDeleteAction(actid, Convert.ToInt32(crmSession.UID()), 2000);
                //success
                if (resp.ToUpper().Contains("SUCCESS"))
                {
                    return new genericResponse() { success = true, message = "Attribute Mass Delete completed successfully." };
                }
                //error
                if (resp.ToUpper().Contains("ERROR"))
                {
                    return new genericResponse() { success = false, message = resp };
                }
                //something not working
                return new genericResponse() { success = false, message = "There is a problem processing your request. Please try again later." };
            }
            catch (Exception ex)
            {
                //To do: Log the Exception here
                return new genericResponse() { success = false, message = "There is a problem processing your request. Please try again later." };
            }

        }

        [HttpPost, Route("crm/api/ActionCategory/MassDestroyActionCategory/{actcatid}")]
        [sysAdminapiAuthorizeAttribute]
        public genericResponse MassDeleteActionCategory(int actcatid)
        {
            try
            {
                //Let us call SP to Perform the task....
                string resp = _ActionService.MassDeleteActionCategory(actcatid, Convert.ToInt32(crmSession.UID()), 2000);
                //success
                if (resp.ToUpper().Contains("SUCCESS"))
                {
                    return new genericResponse() { success = true, message = "Attribute Category Mass Delete completed successfully." };
                }
                //error
                if (resp.ToUpper().Contains("ERROR"))
                {
                    return new genericResponse() { success = false, message = resp };
                }
                //something not working
                return new genericResponse() { success = false, message = "There is a problem processing your request. Please try again later." };
            }
            catch (Exception ex)
            {
                //To do: Log the Exception here
                return new genericResponse() { success = false, message = "There is a problem processing your request. Please try again later." };
            }

        }
        #endregion
    }
}
