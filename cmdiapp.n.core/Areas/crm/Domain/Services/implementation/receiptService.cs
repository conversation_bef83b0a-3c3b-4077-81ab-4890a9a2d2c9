﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

using Ninject;
using Ninject.Web.Mvc;

using cmdiapp.n.core.Core;
using cmdiapp.n.core.Domain.Models;
using cmdiapp.n.core.Core.App_Start;
using cmdiapp.n.core.Areas.crm.Core;
using cmdiapp.n.core.Areas.crm.Domain.Models;
using cmdiapp.n.core.Areas.crm.Domain.ViewModels;
using cmdiapp.n.core.Areas.crm.Domain.Repositories;
using cmdiapp.n.core.Areas.crm.Domain.Data;

using System.Reflection.Emit;
using System.Reflection;


namespace cmdiapp.n.core.Areas.crm.Domain.Services
{
    public class receiptService : IreceiptService
    {
        private I___unitOfWork _unitOfWork;
        private I__DBFactory _dbFactory;
        private I_entity_crm _entity_crm;

        public receiptService(I___unitOfWork unitOfWork, I__DBFactory dbFactory, I_entity_crm entity_crm)
        {
            _unitOfWork = unitOfWork;
            _dbFactory = dbFactory;
            _entity_crm = entity_crm;
        }
       
        public List<v_receipts_ext> get_v_receipts(string from, string where, string orderBy, int pageSize, int pageNo)
        {
            string sql = "";

            if (String.IsNullOrEmpty(where))
            {
                sql = string.Format("SELECT * FROM {0}", from);
            }
            else
            {
                sql = string.Format("SELECT * FROM {0} WHERE {1}", from, where);
            }
            
            var q = _dbFactory.getContext().Database.SqlQuery<v_receipts_ext>(String.Format("EXEC dbo.get_pagedDataSet '{0}', '{1}', {2}, {3}", sql, orderBy, pageSize, pageNo));

            return q.ToList();
        }

        public List<v_receipts_ext> get_v_receipts_unlink_memo(string where, string orderBy, int pageSize, int pageNo)
        {
            string sql = "";

            sql = string.Format("SELECT * FROM v_txn_unlink_memo WHERE {0}", where);

            var q = _dbFactory.getContext().Database.SqlQuery<v_receipts_ext>(String.Format("EXEC dbo.get_pagedDataSet '{0}', '{1}', {2}, {3}", sql, orderBy, pageSize, pageNo));

            return q.ToList();
        }

        public int addReceipt(Txn _txn)
        {
            _txn.COUNTER = 1;
            _txn.ADJTYPEID = null;
            _txn.CREATEDON = DateTime.Today;
            _entity_crm.Add(_txn);
            _entity_crm.CommitChanges();
            return _txn.TXNID;
        }

        public int saveReceipt(Txn _txn)
        {
            _entity_crm.Update(_txn);
            _entity_crm.CommitChanges();
            return _txn.TXNID;
        }

        public int deleteReceipt(string _sql)
        {
            var q = _dbFactory.getContext().Database.SqlQuery<Int32>(_sql);
            return q.FirstOrDefault();
        }
    }
}