﻿using System;
using System.Linq;

using cmdiapp.n.core.Areas.legislator.Domain.Data;
using cmdiapp.n.core.Areas.legislator.Domain.Models;
using System.Data.SqlClient;
using System.Web.Script.Serialization;
using cmdiapp.n.core.Areas.crm.Domain.Services;
using cmdiapp.n.core.Areas.crm.Domain.Models;
using cmdiapp.n.core.Domain.ViewModels;
using System.Collections.Generic;
using cmdiapp.n.core.Areas.crm.ViewModels;

namespace cmdiapp.n.core.Areas.legislator.Domain.Services.implementation
{
    public class LegislatorService : ILegislatorService
    {
        private I_entity_legislator _entity;
        private ICrmLegislatorService _crmLegislatorService;

        public LegislatorService(I_entity_legislator entity, ICrmLegislatorService crmLegislatorService)
        {
            _entity = entity;
            _crmLegislatorService = crmLegislatorService;
        }

        public Legislator GetLegislator(string legislatorId)
        {
            return _entity.Single<Legislator>(l => l.Id == legislatorId && l.active);
        }

        public LegislatorSocialMediaAccount GetSocialMedia(string legislatorId)
        {
            return _entity.Single<LegislatorSocialMediaAccount>(sma => 
                sma.legislatorId == legislatorId && sma.active);
        }

        public CommitteeMembership[] GetCommitteeMemberships(string legislatorId)
        {
            try
            {
                string sql = "SELECT dbo.getLegislatorCommitteesJSON(@legislatorId)";
                // query returns JSON array of CommitteeMemberships
                var jsonResult = _entity.getContext()
                    .Database
                    .SqlQuery<string>(sql, new SqlParameter("@legislatorId", legislatorId))
                    .FirstOrDefault();
                // deserialize JSON
                JavaScriptSerializer jsonSerializer = new JavaScriptSerializer();
                CommitteeMembership[] memberships = 
                    jsonSerializer.Deserialize<CommitteeMembership[]>(jsonResult);

                return memberships;
            }
            catch (Exception ex)
            {
                return new CommitteeMembership[] { };
            }
        }

        public LegislatorPhoto GetPhoto(string legislatorId)
        {
            return _entity.Single<LegislatorPhoto>(p => p.legislatorId == legislatorId && p.active);
        }

        public LegislatorInfo GetCrmLegislatorInfo(string legislatorId)
        {
            return _crmLegislatorService.GetLegislatorInfo(legislatorId);
        }

        public genericResponse SaveCrmLegislatorInfo(LegislatorInfo info)
        {
            return _crmLegislatorService.SaveLegislatorInfo(info);
        }

        public bool MemberTrackingIsEnabled()
        {
            return _crmLegislatorService.MemberTrackingIsEnabled();
        }

        public List<LegislatorDirectContributionTotal> GetLegislatorDirectContributionTotals(string legislatorId)
        {
            return _crmLegislatorService.GetLegislatorDirectContributionTotals(legislatorId);
        }

        public List<Txn> GetTxnDetails(
            int entityId, int electionCodeId, string electionYear)
        {
            return _crmLegislatorService.GetTxnDetails(entityId, electionCodeId, electionYear);
        }

        public List<LegislatorContributionTotal> GetLegislatorPersonalContributionTotals(string legislatorId)
        {
            return _crmLegislatorService.GetLegislatorPersonalContributionTotals(legislatorId);
        }

        public List<PersonalContribLegislatorDisplay> GetPersonalContribs(int entityId, int year)
        {
            return _crmLegislatorService.GetPersonalContribs(entityId, year);
        }

        public List<LegislatorAutocompleteDisplay> GetLegislatorsWithContactChanges(string searchText, int take)
        {
            string _searchText = searchText?.Trim().ToLower() ?? "";
            string[] searchSplits = _searchText.Split(' ');
            return (from legislator in _entity.All<Legislator>()
                    join change in _entity.All<ContactChange>() on legislator.Id equals change.legislatorId
                    where (_searchText == "" || searchSplits.All(s =>
                        legislator.fn.ToLower().Contains(s)
                        || legislator.mn.ToLower().Contains(s)
                        || legislator.ln.ToLower().Contains(s)
                        || legislator.suffix.ToLower().Contains(s)))
                    orderby legislator.ln, legislator.fn, legislator.mn
                    group legislator by new { Id = legislator.Id, name = legislator.name } into grp
                    select new LegislatorAutocompleteDisplay
                    {
                        Id = grp.Key.Id,
                        name = grp.Key.name
                    }).Take(take).ToList();
        }
    }
}