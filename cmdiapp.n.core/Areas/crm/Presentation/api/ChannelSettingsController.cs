﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Net;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Web.Http;
using System.IO;
using System.Data.SqlClient;
using System.Collections.ObjectModel;
using System.Xml.Linq;

using Ninject;
using Ninject.Web.Mvc;

using AutoMapper;
using cmdiapp.n.core.Core;
using cmdiapp.n.core.Core.App_Start;
using cmdiapp.n.core.Domain.ViewModels;
using cmdiapp.n.core.Areas.crm.Core;
using cmdiapp.n.core.Areas.crm.Domain.Models;
using cmdiapp.n.core.Areas.crm.Domain.ViewModels;
using cmdiapp.n.core.Areas.crm.Domain.Data;
using cmdiapp.n.core.Areas.crm.Domain.Repositories;
using cmdiapp.n.core.Areas.crm.Domain.Services;
using cmdiapp.n.core.Areas.dataService.Domain.Data;
using cmdiapp.n.core.Areas.dataService.Domain.Models;
using cmdiapp.n.core.Areas.dataService.Domain.Services;
using cmdiapp.n.core.Presentation.Controllers;
using cmdiapp.n.core.Areas.query.Domain.Models;
using cmdiapp.n.core.Domain.Models;

using System.Data;
using cmdiapp.n.core.Library;

namespace cmdiapp.n.core.Areas.crm.Presentation.Controllers.api
{
    [Authorize]
    public class ChannelSettingsController : ApiController
    {
        #region [[ Declaration ]]
        private I_entity_crm _entity_crm;
        private readonly IlkChannelService _channelService;
        private readonly IReportDataService _reportService;
        private userSession _userSession;

        #endregion

        #region [[ (constructor) SettingsController ]]
        public ChannelSettingsController()
        {
            _entity_crm = I_entityManager_crm.getEntity();
            _channelService = NinjectMVC.kernel.Get<IlkChannelService>();
            _reportService = NinjectMVC.kernel.Get<IReportDataService>();
            _userSession = session.userSession;
        }
        #endregion

        #region [[ SQL Query]]
        const string _sql_exportChannel = @"SELECT ISNULL(C.channel,null) AS Channel, ISNULL(C.descrip,null) AS Description,ISNULL(isDefault,0) AS isDefault ,ISNULL(isSystem,0) AS isSystem FROM lkChannel C";
        #endregion

        #region[MONEY->SETTINGS->CHANNEL]
        [HttpPost, Route("crm/api/ChannelSettings/ExportChannelKey")]
        [apiAuthorize(AccessElement = "cmdiapp.dms.Channel", AccessLevel = "v")]
        public string ExportChannelKey(string searchText)
        {
            if (string.IsNullOrEmpty(searchText)) searchText = "";

            searchParam param = new searchParam();
            param.searchText = searchText;

            string key_ = System.Guid.NewGuid().ToString();
            HttpRuntime.Cache.Insert(key_, param, null, DateTime.UtcNow.Add(new TimeSpan(1, 0, 1)), System.Web.Caching.Cache.NoSlidingExpiration);

            return key_;

        }

        [HttpGet, Route("crm/api/ChannelSettings/ExportChannel")]
        [apiAuthorize(AccessElement = "cmdiapp.dms.Channel", AccessLevel = "v")]
        public string ExportChannel(string key)
        {
            searchParam _param = (searchParam)HttpRuntime.Cache[key];
            string sToday = String.Format("{0:yyyyMMMdd__hh_mm_ss tt}", DateTime.Now);
            string moduleName = "Channels";
            string fileName = moduleName + "_" + System.Guid.NewGuid() + "_" + _userSession.UserName + "_" + sToday;

            DataSet ds = null;

            if (string.IsNullOrEmpty(_param.searchText))
            {
                ds = _reportService.get_dataset_w_sql__single(_sql_exportChannel, "Channels");
            }
            else
            {
                _param.searchText = Library.util.kill_sqlBlacklistWord(_param.searchText);

                string _where = "";

                if (!string.IsNullOrEmpty(_param.searchText))
                {
                    #region [ Split Search Text > string1, string2 & string3 and create "Where" statement ]
                    char[] delimiterChars = { ' ', ',', '.', ';', ':', '|', '\t' };

                    string[] words = _param.searchText.Split(delimiterChars);

                    if (words.Count() > 0)
                    {
                        _where = string.Format(" C.channel LIKE '%{0}%' OR C.descrip LIKE '%{0}%' ", words[0]);
                        for (int i = 1; i < words.Length; i++)
                        {
                            _where = _where + string.Format(" OR C.channel LIKE '%{0}%' OR C.descrip LIKE '%{0}%' ", words[i]);
                        }
                    }
                    #endregion
                }
                //We need SQL with Where condition
                string sql = _sql_exportChannel + " Where {0}";
                //Compose the SQL
                sql = String.Format(sql, _where);

                ds = _reportService.get_dataset_w_sql__single(sql, "Channels");
            }

            if (ds != null)
            {
                string key_ = System.Guid.NewGuid().ToString();
                HttpRuntime.Cache.Insert(key_, ds, null, DateTime.UtcNow.Add(new TimeSpan(1, 0, 1)), System.Web.Caching.Cache.NoSlidingExpiration);
                return key_;
            }
            else
            {
                return null;
            }
        }
        
        [HttpGet, Route("crm/api/ChannelSettings/GetChannels")]
        [apiAuthorize(AccessElement = "cmdiapp.dms.Channel", AccessLevel = "v")]
        public genericResponse GetChannels(string searchText)
        {

            searchText = Library.util.kill_sqlBlacklistWord((searchText ?? "").Trim());

            #region [[ Work with the Data ]]

            List<lkChannel_ext> _list = new List<lkChannel_ext>();

            if (!string.IsNullOrEmpty(searchText))
            {
                string _where = "";
                #region [ Split Search Text > string1, string2 & string3 and create "Where" statement ]
                char[] delimiterChars = { ' ', ',', '.', ';', ':', '|', '\t' };

                string[] words = searchText.Split(delimiterChars);

                if (words.Count() > 0)
                {
                    _where = string.Format(" channel LIKE '%{0}%' OR descrip LIKE '%{0}%' ", words[0]);
                    for (int i = 1; i < words.Length; i++)
                    {
                        _where = _where + string.Format(" OR channel LIKE '%{0}%' OR descrip LIKE '%{0}%' ", words[i]);
                    }

                    _list = _channelService.get_all_channel_exts(_where);
                }
                #endregion

            }
            else
            {
                _list = _channelService.get_all_channel_exts("");
            }

            IEnumerable<iItemType> results = _list.ToList();

            #endregion

            if (results.Count() > 0)
            {
                return new genericResponse() { success = true, __count = _list.FirstOrDefault().count_, results = results.ToList() };
            }
            else
            {
                return new genericResponse() { success = true, __count = 0 };
            }

        }



        [HttpPost, Route("crm/api/ChannelSettings/saveChannel")]
        [apiAuthorize(AccessElement = "cmdiapp.dms.Channel", AccessLevel = "a")]
        public genericResponse saveChannel(lkChannel _channel)
        {

            try
            {
                //check for Uniqueness of the channel
                if (!_channelService.check_unqiueness_channel(_channel))
                {
                    //return 
                    return new genericResponse() { success = false, message = "Channel already exists. Please enter unique Channel and try again." };
                }
                else //check for Uniqueness of the description
                if (!_channelService.check_unqiueness_descrip(_channel))
                {
                    //return 
                    return new genericResponse() { success = false, message = "Channel already exists. Please enter unique Description and try again." };
                }
                else
                {
                    if (_channel.channelId >= 0)
                    {
                        _channelService.Update(_channel);
                        return new genericResponse() { success = true, message = "Channel " + _channel.channel + " updated successfully." };
                    }
                    else
                    {                        
                        _channelService.Add(_channel);
                        return new genericResponse() { success = true, message = "Channel " + _channel.channel + " added successfully." };
                    }

                }

            }
            catch(Exception ex)
            {
                //To Do: Log the Exception here
                return new genericResponse() { success = false, message = "There is a problem processing your request. Please try again later." };
            }
        }


        [HttpPost, Route("crm/api/ChannelSettings/DeleteChannel/{channelid}")]
        [apiAuthorize(AccessElement = "cmdiapp.dms.Channel", AccessLevel = "d")]
        public genericResponse DeleteChannel(int channelid)
        {
            try
            {
                var viewModel = _channelService.get_channel(channelid);
                if (!_channelService.check_record_linked(viewModel.channelId))
                {
                    //Now Proceed for the Channel delete..
                    _channelService.Delete(viewModel);
                    return new genericResponse() { success = true, message = "Channel deleted successfully." };
                }
                else
                {
                    //Can not delete as this is being used
                    return new genericResponse() { success = false, message = "The channel is being referenced so it can not be deleted." };
                }

            }
            catch
            {
                //To do: Log the Exception here
                return new genericResponse() { success = false, message = "There is a problem processing your request. Please try again later." };
            }

        }



        [HttpGet, Route("crm/api/ChannelSettings/getChannel/{_channelId}")]
        [apiAuthorize(AccessElement = @"cmdiapp.dms.Channel", AccessLevel = "v")]
        public lkChannel getChannel(int _channelId)
        {
            if (_channelId < 0)//meaning new exception code
                return new lkChannel() { channelId = -1 };
            //meaning edit
            return _entity_crm.All<lkChannel>().Where(ch => ch.channelId == _channelId).FirstOrDefault();
        }
        #endregion

    }
}
