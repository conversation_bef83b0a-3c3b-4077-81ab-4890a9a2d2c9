﻿using System.Collections.Generic;
using System.Web.Mvc;

using cmdiapp.n.core.Areas.crm.Domain.Models;
using cmdiapp.n.core.Areas.crm.Domain.ViewModels;
using cmdiapp.n.core.Areas.crm.Domain.Repositories;
using cmdiapp.n.core.Domain.ViewModels;

namespace cmdiapp.n.core.Areas.crm.Domain.Services
{
    public interface IsrceBudgetService
    {
        genericResponse Save(SRCEBUDGET budget);
        genericResponse Delete(int budgetId);

        /*
         * get budget details for a specific sourceid
         */
        SRCEBUDGET Get(int budgetId);

        List<SRCEBUDGET> GetBudgetsForSource(int sourceId);
    }
}