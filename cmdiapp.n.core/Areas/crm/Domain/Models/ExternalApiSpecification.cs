﻿using System;
using System.Collections.Generic;

namespace cmdiapp.n.core.Areas.crm.Domain.Models
{
    public class ExternalApiSpecification
    {
        private int pageSize = 100;
        private int pageNumber = 1;
        private const int MaxPageSize = 1000;
        public List<IExternalApiSpecificationFilter> Filters { get; } =
            new List<IExternalApiSpecificationFilter>();

        public int PageSize
        {
            get => pageSize;
            set
            {
                if (value < 0)
                {
                    throw new ExternalApiSpecificationException("Page size cannot be less than 0.");
                }
                if (value > MaxPageSize)
                {
                    throw new ExternalApiSpecificationException($"Page size cannot be greater than {MaxPageSize:n0}");
                }
                pageSize = value;
            }
        }

        public int PageNumber
        {
            get => pageNumber;
            set
            {
                if (value < 1)
                {
                    throw new ExternalApiSpecificationException("Page number cannot be less than 1.");
                }
                pageNumber = value;
            }
        }

        // List of Export Groups and their choice (ID) (e.g. "pseg_nameGroup,pseg_name","pseg_addressGroup,pseg_addressPrimary",...)
        public string[] ExportGroupsAndIds  { get; set; }   
    }

    [Serializable]
    public class ExternalApiSpecificationException : Exception
    {
        public ExternalApiSpecificationException(string message) : base(message)
        {
        }
    }
}