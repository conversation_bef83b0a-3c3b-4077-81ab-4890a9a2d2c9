﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Data;
using System.Linq;
using System.Web.Mvc;
using System.Data.Linq;
using System.ComponentModel.DataAnnotations.Schema;
using cmdiapp.n.core.Areas.crm.Domain.Data;

using cmdiapp.n.core.Domain.ViewModels;

namespace cmdiapp.n.core.Areas.crm.Domain.Models
{
    [Table("ACTIVITY")]
    public class ACTIVITY : _entityBase_crm, iItemType
    {
        [Column]
        [Key]
        public int ACTID { get; set; }

        [Column]
        public int PID { get; set; }
        [Column]
        public int? CID { get; set; }
        [Column]
        public string SUBJECT { get; set; }
        [Column]
        public Int16 ACTTYPEID { get; set; }
        [Column]
        public Int16 PURPOSEID { get; set; }
        [Column]
        public Int16 PRIORITYID { get; set; }
        [Column]
        public int? ACTHISTID { get; set; }
        [Column]
        public DateTime? ACTDATE { get; set; }
        [Column]
        public Byte REMIND { get; set; }
        [Column]
        public Int16? SCHEDBY { get; set; }
        [Column]
        public Int16? SCHEDFOR { get; set; }
        [Column]
        public DateTime? SCHEDON { get; set; }
        [Column]
        public Byte DONE { get; set; }
        [Column]
        public Int16? DONEBY { get; set; }
        [Column]
        public DateTime? DONEON { get; set; }
        [Column]
        public string NOTE { get; set; }
        [Column]
        public string ASKMEMO { get; set; } 
        [Column]
        public Byte KEEPHIST { get; set; }
        [Column]
        public DateTime UPDATEDON { get; set; }
        [Column]
        public int? MOVEID { get; set; }
        [Column("_updating_uid")]
        public Nullable<short> updating_uid { get; set; }
        [Column]
        public bool? recurring { get; set; }
        [Column]
        public string recurringFreq { get; set; }
        [Column]
        public DateTime? recurringStartDate { get; set; }
        [Column]
        public DateTime? recurringEndDate { get; set; }
        [Column]
        public Int16? PIPELINEID { get; set; }
        [NotMapped]
        public short? CurrentUSER_UID { get; set; }
    }

    public class TASK : _entityBase_crm, iItemType
    {
        [Column]
        [Key]
        public int ACTID { get; set; }

        [Column]
        public int PID { get; set; }

        [Column]
        public DateTime? ACTDATE { get; set; }

        public string ACTDATEs
        {
            get
            {
                return (ACTDATE == null ? "" : string.Format("{0:MM/dd/yyyy}", ACTDATE));
            }
        }
        [Column]
        public string ACTTYPE { get; set; }
        [Column]
        public string PURPOSE { get; set; }
        [Column]
        public string PRIORITY { get; set; }
        [Column]
        public string SCHEDFOR { get; set; }
        [Column]
        public string SCHEDBY { get; set; }

        [Column]
        public DateTime? SCHEDON { get; set; }
        public string SCHEDONs
        {
            get
            {
                return (SCHEDON == null ? "" : string.Format("{0:MM/dd/yyyy}", SCHEDON));
            }
        }

        [Column]
        public Byte DONE { get; set; }
        [Column]
        public string DONEBY { get; set; }

        [Column]
        public DateTime? DONEON { get; set; }
        public string DONEONs
        {
            get
            {
                string a = (DONEON == null ? "" : string.Format("{0:MM/dd/yyyy}", DONEON));
                if (a != "")
                {

                }
                return a;
            }
        }

        [Column]
        public string SUBJECT { get; set; }
        [Column]
        public string ASKMEMO { get; set; }
        [Column]
        public string SHORTNOTE { get; set; }
        [Column]
        public Int16? MOVEID { get; set; }
        [Column]
        public Int16? PIPELINEID { get; set; }

        [NotMapped]
        public Int16 ACTTYPEID { get; set; }
        [NotMapped]
        public Int16 PURPOSEID { get; set; }
        [NotMapped]
        public Int16 PRIORITYID { get; set; }
        [NotMapped]
        public Int16? SCHEDBY_UID { get; set; }
        [NotMapped]
        public Int16? SCHEDFOR_UID { get; set; }
        [NotMapped]
        public Int16? DONEBY_UID { get; set; }

        [NotMapped]
        public Int16? CurrentUSER_UID { get; set; }

        [NotMapped]
        public Byte KeepAsHistory { get; set; }
        [NotMapped]
        public bool? recurring { get; set; }
        [NotMapped]
        public string recurringFreq { get; set; }
        [NotMapped]
        public DateTime? recurringStartDate { get; set; }
        [NotMapped]
        public DateTime? recurringEndDate { get; set; }


    }


    [Table("ACTIVITYDOC")]
    public class ACTIVITYDOC : _entityBase_crm, iItemType
    {
        [Column]
        public int ACTIVITYDOCID { get; set; }

        [Column]
        public int? ACTID { get; set; }

        [Column]
        public string FILENAME { get; set; }

        [Column]
        public byte[] CONTENT { get; set; }

        [Column("_updating_uid")]
        public short? updating_uid { get; set; }
    }
}