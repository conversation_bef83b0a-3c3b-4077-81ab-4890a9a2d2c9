﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Data.Linq;
using System.Web;
using System.Data.Entity;
using cmdiapp.n.core.Areas.crm.Domain.Models;
using cmdiapp.n.core.Areas.docService.Domain.Models;

namespace cmdiapp.n.core.Areas.docService.Domain.Data
{
    public class _docServiceDBContext : DbContext
    {
        public _docServiceDBContext(string _appDBContext_connString_itself_or_name)
            : base(_appDBContext_connString_itself_or_name)
        {
            Database.SetInitializer<_docServiceDBContext>(new DBInitializer());
        }
        
        public DbSet<emailMsgData> eMsgData { get; set; }
        
        protected override void OnModelCreating(DbModelBuilder modelBuilder)
        {
            modelBuilder.Conventions.Remove<System.Data.Entity.ModelConfiguration.Conventions.PluralizingTableNameConvention>();
        }
    }
    

    public class DBInitializer : IDatabaseInitializer<_docServiceDBContext>
    {
        public void InitializeDatabase(_docServiceDBContext context)
        {
            if (!context.Database.Exists())
            {
                throw new NotSupportedException("The database does not exist.");
            }
        }
    }

}