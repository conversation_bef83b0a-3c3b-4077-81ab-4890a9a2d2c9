﻿using cmdiapp.n.core.Domain.ViewModels;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace cmdiapp.n.core.Areas.crm.ViewModels
{
    /// <summary>
    /// Abridged People record with TrackNo for Bundler autocomplete
    /// </summary>
    public class BundlerListDisplay : iItemType
    {
        public int PID { get; set; }
        public string Name { get; set; }
        public int TrackNo { get; set; }
        public string Description
        {
            get
            {
                return $"{Name} - {TrackNo}";
            }
        }
    }
}