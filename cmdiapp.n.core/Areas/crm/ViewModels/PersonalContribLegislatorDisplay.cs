﻿using cmdiapp.n.core.Areas.crm.Domain.Models;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Web;

namespace cmdiapp.n.core.Areas.crm.ViewModels
{
    [NotMapped]
    public class PersonalContribLegislatorDisplay : PersonalContrib
    {
        public string prefix { get; set; }
        public string fname { get; set; }
        public string mname { get; set; }
        public string lname { get; set; }
        public string suffix { get; set; }
    }
}