﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Data;
using System.Linq;
using System.Web.Mvc;
using System.Data.Linq;
using System.ComponentModel.DataAnnotations.Schema;
using cmdiapp.n.core.Areas.crm.Domain.Data;

using cmdiapp.n.core.Domain.ViewModels;

namespace cmdiapp.n.core.Areas.crm.Domain.Models
{   
    [Table("x_gs_org")]
    public class x_gs_org : _entityBase_crm, iItemType
    {
        [Column]
        [Key]
        public int Id { get; set; }

        [Column]
        public string orgName { get; set; }
        [Column]
        public string EIN { get; set; }
        [Column]
        public string street { get; set; }
        [Column]
        public string city_state_zip { get; set; }
        [Column]
        public string orgStatement { get; set; }

        [Column]
        public byte[] imageContent { get; set; }
    }
    public class x_gs_org_ext
    {
        public int Id { get; set; }
        public string orgName { get; set; }
        public string EIN { get; set; }
        public string street { get; set; }
        public string city_state_zip { get; set; }
        public string orgStatement { get; set; }
        public int count_ { get; set; }
        public Int64 rowNo { get; set; }
    }

}