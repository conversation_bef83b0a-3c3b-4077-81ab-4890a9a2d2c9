﻿using cmdiapp.n.core.Areas.datax.Domain.Models.ViewModels;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.Serialization;
using System.Web;

namespace cmdiapp.n.core.Areas.crm.ViewModels
{
    /// <summary>
    /// Contains data for displaying a section of
    /// <see cref="FileDropAccountPanel"/>s for a given <see cref="datax.Domain.Models.Caller"/>
    /// </summary>
    [DataContract]
    public class FileDropCallerSection
    {
        /// <summary>
        /// Gets or sets display information for the <see cref="datax.Domain.Models.Caller"/>
        /// </summary>
        [DataMember(Name = "caller")]
        public CallerDisplay Caller { get; set; }
        /// <summary>
        /// Gets or sets the displayed file drop panels for each account for this caller
        /// </summary>
        [DataMember(Name = "accountPanels")]
        public List<FileDropAccountPanel> AccountPanels { get; set; }
    }
}