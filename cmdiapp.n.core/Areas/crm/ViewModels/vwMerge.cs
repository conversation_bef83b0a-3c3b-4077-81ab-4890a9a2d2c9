﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using cmdiapp.n.core.Areas.crm.Domain.Models;
using cmdiapp.n.core.Domain.Models;

namespace cmdiapp.n.core.Domain.ViewModels
{
    public class DDItem
    {
        public string Value { get; set; }
        public string Text { get; set; }
    }
    public class vwMerge : iItemType
    {
        public PeopleR _people_left { get; set; }
        public PeopleR _people_right { get; set; }
        public List<ssMRGLIST> _ssMRGLIST { get; set; }
        public pageInfo _pageInfo { get; set; }
    }
}