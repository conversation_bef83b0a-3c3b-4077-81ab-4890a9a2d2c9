<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Crimson CRM - People Profile Preview</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #f5f5f5;
            margin: 0;
            padding: 0;
            font-size: 13px;
        }

        .main-container {
            display: flex;
            height: 100vh;
            background-color: #f5f5f5;
        }

        /* Left Sidebar - Profile Card */
        .profile-sidebar {
            width: 280px;
            background: white;
            border-right: 1px solid #ddd;
            display: flex;
            flex-direction: column;
        }

        .profile-card {
            padding: 20px;
            text-align: center;
            border-bottom: 1px solid #eee;
        }

        .profile-photo {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            margin: 0 auto 10px;
            background: #007bff;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 24px;
            font-weight: bold;
        }

        .profile-name {
            font-size: 16px;
            font-weight: 600;
            color: #333;
            margin-bottom: 5px;
        }

        .profile-id {
            font-size: 12px;
            color: #666;
            margin-bottom: 15px;
        }

        .profile-details {
            padding: 15px 20px;
            font-size: 12px;
            line-height: 1.4;
        }

        .profile-details .detail-group {
            margin-bottom: 15px;
        }

        .profile-details .detail-group:last-child {
            margin-bottom: 0;
        }

        .profile-details .detail-item {
            display: flex;
            align-items: center;
            margin-bottom: 5px;
            color: #333;
        }

        .profile-details .detail-item i {
            width: 16px;
            margin-right: 8px;
            color: #666;
            font-size: 11px;
        }

        .profile-details .detail-item a {
            color: #007bff;
            text-decoration: none;
        }

        .profile-details .social-links {
            display: flex;
            gap: 8px;
            margin-top: 10px;
        }

        .profile-details .social-links a {
            color: #666;
            font-size: 16px;
        }

        /* Main Content Area */
        .main-content {
            flex: 1;
            display: flex;
            flex-direction: column;
            background: white;
        }

        /* Tab Navigation */
        .tab-navigation {
            display: flex;
            border-bottom: 1px solid #ddd;
            background: #f8f9fa;
        }

        .tab-item {
            padding: 12px 20px;
            border-right: 1px solid #ddd;
            background: #f8f9fa;
            color: #666;
            text-decoration: none;
            font-size: 13px;
            font-weight: 500;
            position: relative;
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .tab-item.active {
            background: white;
            color: #333;
            border-bottom: 2px solid #007bff;
        }

        .tab-item:hover {
            background: #e9ecef;
            color: #333;
        }

        .tab-badge {
            background: #dc3545;
            color: white;
            border-radius: 10px;
            padding: 2px 6px;
            font-size: 10px;
            margin-left: 5px;
        }

        /* Tab Content */
        .tab-content {
            flex: 1;
            overflow-y: auto;
            padding: 0;
        }

        /* Giving History Styles */
        .giving-history-header {
            background: #007bff;
            color: white;
            padding: 15px 20px;
            font-size: 16px;
            font-weight: 600;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .giving-metrics {
            display: flex;
            padding: 20px;
            gap: 20px;
            border-bottom: 1px solid #eee;
        }

        .metric-card {
            flex: 1;
            text-align: center;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 4px;
            background: white;
        }

        .metric-icon {
            font-size: 24px;
            color: #007bff;
            margin-bottom: 8px;
        }

        .metric-value {
            font-size: 24px;
            font-weight: bold;
            color: #333;
            margin-bottom: 5px;
        }

        .metric-label {
            font-size: 11px;
            color: #666;
            margin-bottom: 3px;
        }

        .metric-sublabel {
            font-size: 10px;
            color: #999;
        }

        .giving-details {
            padding: 20px;
        }

        .giving-section {
            margin-bottom: 20px;
        }

        .giving-section h4 {
            font-size: 14px;
            color: #333;
            margin-bottom: 10px;
            font-weight: 600;
        }

        .recent-gifts {
            font-size: 12px;
        }

        .gift-item {
            padding: 8px 0;
            border-bottom: 1px solid #f0f0f0;
            display: flex;
            justify-content: space-between;
        }

        .gift-item:last-child {
            border-bottom: none;
        }

        /* Footer Styles */
        .bottom-toolbar {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            background: white;
            border-top: 1px solid #ddd;
            padding: 10px 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            z-index: 1000;
        }

        .toolbar-left, .toolbar-right {
            display: flex;
            gap: 8px;
        }

        .btn {
            padding: 6px 12px;
            border-radius: 3px;
            border: 1px solid;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 5px;
            font-size: 12px;
            cursor: pointer;
            background: none;
        }

        .btn-primary {
            background-color: #007bff;
            border-color: #007bff;
            color: white;
        }

        .btn-success {
            background-color: #28a745;
            border-color: #28a745;
            color: white;
        }

        .btn-outline {
            background-color: transparent;
            border-color: #007bff;
            color: #007bff;
        }

        .btn:hover {
            opacity: 0.9;
        }
    </style>
</head>
<body>
    <div class="main-container">
        <!-- Left Sidebar - Profile Card -->
        <div class="profile-sidebar">
            <div class="profile-card">
                <div class="profile-photo">
                    JB
                </div>
                <div class="profile-name">Mr. Joseph M. Banks, Sr.</div>
                <div class="profile-id">ID #********</div>
            </div>

            <div class="profile-details">
                <div class="detail-group">
                    <div class="detail-item">
                        <i class="fas fa-map-marker-alt"></i>
                        <div>
                            <div>1209 E Bethany Home Rd</div>
                            <div>Phoenix, AZ</div>
                            <div>85014 - 2623</div>
                        </div>
                    </div>
                </div>

                <div class="detail-group">
                    <div class="detail-item">
                        <i class="fas fa-phone"></i>
                        <span>📞 ************</span>
                    </div>
                    <div class="detail-item">
                        <i class="fas fa-fax"></i>
                        <span>📠 ************</span>
                    </div>
                    <div class="detail-item">
                        <i class="fas fa-mobile"></i>
                        <span>📱 ************</span>
                    </div>
                </div>

                <div class="detail-group">
                    <div class="social-links">
                        <a href="#"><i class="fab fa-facebook"></i></a>
                        <a href="#"><i class="fab fa-twitter"></i></a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Main Content Area -->
        <div class="main-content">
            <!-- Tab Navigation -->
            <div class="tab-navigation">
                <a href="#" class="tab-item">Timeline</a>
                <a href="#" class="tab-item active">Individual</a>
                <a href="#" class="tab-item">Giving History</a>
                <a href="#" class="tab-item">Codes</a>
                <a href="#" class="tab-item">Moves Management</a>
            </div>

            <!-- Tab Content -->
            <div class="tab-content">
                <!-- Individual Tab Content -->
                <div class="individual-content">
                    <!-- Top Section with Individual Details -->
                    <div style="padding: 20px; border-bottom: 1px solid #eee;">
                        <div style="display: flex; gap: 40px;">
                            <div>
                                <h4 style="margin: 0 0 10px 0; font-size: 14px; color: #333;">Occupation</h4>
                                <p style="margin: 0; font-size: 12px;">CEO</p>
                                <p style="margin: 0; font-size: 12px;">Emeritus</p>
                                <p style="margin: 0; font-size: 12px;">Joseph Banks</p>
                            </div>
                            <div>
                                <h4 style="margin: 0 0 10px 0; font-size: 14px; color: #333;">Current Date</h4>
                                <p style="margin: 0; font-size: 12px; font-weight: bold;">$14,698</p>
                                <p style="margin: 0; font-size: 12px; color: #666;">01/23/2024</p>
                            </div>
                            <div>
                                <h4 style="margin: 0 0 10px 0; font-size: 14px; color: #333;">Most Recent</h4>
                                <p style="margin: 0; font-size: 12px; font-weight: bold;">$500</p>
                                <p style="margin: 0; font-size: 12px; color: #666;">01/23/2024</p>
                            </div>
                            <div>
                                <h4 style="margin: 0 0 10px 0; font-size: 14px; color: #333;">Flags</h4>
                                <div style="display: flex; flex-wrap: wrap; gap: 4px;">
                                    <span style="background: #dc3545; color: white; padding: 2px 6px; border-radius: 3px; font-size: 10px;">BIG DONOR</span>
                                    <span style="background: #ffc107; color: black; padding: 2px 6px; border-radius: 3px; font-size: 10px;">LAPSED</span>
                                    <span style="background: #17a2b8; color: white; padding: 2px 6px; border-radius: 3px; font-size: 10px;">MAJOR GIFT</span>
                                    <span style="background: #28a745; color: white; padding: 2px 6px; border-radius: 3px; font-size: 10px;">MONTHLY DONOR</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Bottom Section with Tasks, Notes, Contacts, Actions -->
                    <div style="display: flex; height: 400px;">
                        <!-- Tasks -->
                        <div style="flex: 1; border-right: 1px solid #eee; padding: 15px;">
                            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px;">
                                <h4 style="margin: 0; font-size: 14px; color: #333;">Tasks</h4>
                                <button style="background: #007bff; color: white; border: none; border-radius: 3px; padding: 4px 8px; font-size: 12px;">+</button>
                            </div>
                            <div style="text-align: center; color: #666; font-size: 24px; margin-top: 50px;">
                                <div style="font-size: 48px; font-weight: bold;">25</div>
                                <div style="font-size: 12px;">Outstanding</div>
                            </div>
                        </div>

                        <!-- Notes -->
                        <div style="flex: 1; border-right: 1px solid #eee; padding: 15px;">
                            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px;">
                                <h4 style="margin: 0; font-size: 14px; color: #333;">Notes</h4>
                                <button style="background: #007bff; color: white; border: none; border-radius: 3px; padding: 4px 8px; font-size: 12px;">+</button>
                            </div>
                            <div style="font-size: 12px;">
                                <div style="margin-bottom: 10px;">
                                    <div style="color: #007bff;">● Feb 19, 2025</div>
                                    <div>test 2025</div>
                                </div>
                                <div style="margin-bottom: 10px;">
                                    <div style="color: #007bff;">● Nov 14, 2022</div>
                                    <div>Send Thank you Letter</div>
                                </div>
                                <div style="margin-bottom: 10px;">
                                    <div style="color: #007bff;">● Nov 14, 2022</div>
                                    <div>Schedule meeting</div>
                                </div>
                            </div>
                        </div>

                        <!-- Contacts -->
                        <div style="flex: 1; border-right: 1px solid #eee; padding: 15px;">
                            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px;">
                                <h4 style="margin: 0; font-size: 14px; color: #333;">Contacts</h4>
                                <button style="background: #007bff; color: white; border: none; border-radius: 3px; padding: 4px 8px; font-size: 12px;">+</button>
                            </div>
                            <div style="text-align: center; color: #666; font-size: 24px; margin-top: 30px;">
                                <div style="font-size: 48px; font-weight: bold;">46</div>
                                <div style="font-size: 12px;">Contacts</div>
                                <div style="font-size: 12px; margin-top: 20px;">
                                    <div>Smith</div>
                                    <div>1600 Pennsylvania Ave NW</div>
                                    <div>Washington, DC 20500</div>
                                    <div>7017653132</div>
                                    <div><EMAIL></div>
                                </div>
                            </div>
                        </div>

                        <!-- Actions -->
                        <div style="flex: 1; padding: 15px;">
                            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px;">
                                <h4 style="margin: 0; font-size: 14px; color: #333;">Actions</h4>
                                <button style="background: #007bff; color: white; border: none; border-radius: 3px; padding: 4px 8px; font-size: 12px;">+</button>
                            </div>
                            <div style="font-size: 12px;">
                                <div style="margin-bottom: 10px;">
                                    <div style="color: #007bff;">● May 04, 2023</div>
                                    <div>Volunteer - Booth</div>
                                </div>
                                <div style="margin-bottom: 10px;">
                                    <div style="color: #007bff;">● Mar 30, 2023</div>
                                    <div>Volunteer - Booth</div>
                                </div>
                                <div style="margin-bottom: 10px;">
                                    <div style="color: #007bff;">● Mar 14, 2023</div>
                                    <div>Volunteer - Booth</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bottom Toolbar -->
    <div class="bottom-toolbar">
        <div class="toolbar-left">
            <button class="btn btn-outline">
                <i class="fas fa-chart-bar"></i>
            </button>
            <button class="btn btn-primary">
                <i class="fas fa-plus"></i>
            </button>
            <button class="btn btn-primary">
                <i class="fas fa-download"></i>
            </button>
        </div>
        <div class="toolbar-right">
            <button class="btn btn-primary">
                <i class="fas fa-edit"></i>
                Edit Full Record
            </button>
            <button class="btn btn-success">
                <i class="fas fa-copy"></i>
                Copy Record
            </button>
            <button class="btn btn-primary">
                <i class="fas fa-search"></i>
                Look up
            </button>
            <button class="btn btn-primary">
                <i class="fas fa-print"></i>
                Print Call Sheets
            </button>
        </div>
    </div>

    <!-- Hidden Giving History Tab Content (for reference) -->
    <div id="giving-history-content" style="display: none;">
        <div class="giving-history-header">
            <span>Giving History</span>
            <div>
                <button class="btn btn-success" style="margin-right: 10px;">Add Gift</button>
                <button class="btn btn-primary">All</button>
                <button class="btn btn-primary">Statement</button>
                <button class="btn btn-primary">Mail History</button>
            </div>
        </div>

        <div class="giving-metrics">
            <div class="metric-card">
                <div class="metric-icon">📊</div>
                <div class="metric-value">$994,948.01</div>
                <div class="metric-label">All-Time Total</div>
                <div class="metric-sublabel">1,639 gifts</div>
            </div>
            <div class="metric-card">
                <div class="metric-icon">💳</div>
                <div class="metric-value">$5,030</div>
                <div class="metric-label">Soft Credits</div>
                <div class="metric-sublabel">2 gifts</div>
            </div>
            <div class="metric-card">
                <div class="metric-icon">💰</div>
                <div class="metric-value">$0</div>
                <div class="metric-label">Pledges</div>
                <div class="metric-sublabel">YTD</div>
            </div>
            <div class="metric-card">
                <div class="metric-icon">🔄</div>
                <div class="metric-value">0 cycles</div>
                <div class="metric-label">Consecutive Gifts</div>
                <div class="metric-sublabel">Since 2008 Cycle</div>
            </div>
        </div>

        <div class="giving-details">
            <div class="giving-section">
                <h4>Recent Gifts</h4>
                <div class="recent-gifts">
                    <div class="gift-item">
                        <span>MID 461646 • #1255 • 1/23/23 • $500 • CH</span>
                    </div>
                    <div class="gift-item">
                        <span>PAC • Housefile • MISC1 • N/A</span>
                    </div>
                    <div class="gift-item">
                        <span>MID 538178 • #1004 • 7/14/20 • $1,000 • CH</span>
                    </div>
                    <div class="gift-item">
                        <span>G2020 • Housefile • 0001 • N/A</span>
                    </div>
                    <div class="gift-item">
                        <span>MID 538000 • #1240 • 5/26/20 • $2,479 • WR</span>
                    </div>
                    <div class="gift-item">
                        <span>P2020 • Event1 • C5 • N/A</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Tab functionality
        document.addEventListener('DOMContentLoaded', function() {
            const tabItems = document.querySelectorAll('.tab-item');
            const individualContent = document.querySelector('.individual-content');
            const givingHistoryContent = document.getElementById('giving-history-content');

            tabItems.forEach(tab => {
                tab.addEventListener('click', function(e) {
                    e.preventDefault();

                    // Remove active class from all tabs
                    tabItems.forEach(t => t.classList.remove('active'));

                    // Add active class to clicked tab
                    this.classList.add('active');

                    // Show/hide content based on tab
                    if (this.textContent.trim() === 'Giving History') {
                        individualContent.style.display = 'none';
                        givingHistoryContent.style.display = 'block';
                        givingHistoryContent.style.height = 'calc(100vh - 120px)';
                        givingHistoryContent.style.overflow = 'auto';
                    } else {
                        individualContent.style.display = 'block';
                        givingHistoryContent.style.display = 'none';
                    }
                });
            });
        });
    </script>
</body>
</html>
