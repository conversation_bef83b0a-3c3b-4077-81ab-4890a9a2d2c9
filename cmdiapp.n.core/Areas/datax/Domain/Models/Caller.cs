﻿using cmdiapp.n.core.Areas.datax.Domain.Data;
using cmdiapp.n.core.Domain.ViewModels;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.IO;
using System.Linq;
using System.Runtime.Serialization;
using System.Web;

namespace cmdiapp.n.core.Areas.datax.Domain.Models
{
    /// <summary>
    /// Class that represents a data integrator, e.g., Anedot.
    /// </summary>
    [Table("Caller")]
    public class Caller : _entityBase_datax, iItemType
    {
        /// <summary>
        /// The primary key.
        /// </summary>
        [Column, Key]
        public int Id { get; set; }

        /// <summary>
        /// The name of the caller.
        /// </summary>
        [Column, MaxLength(100)]
        public string name { get; set; }

        [Column, MaxLength(100)]
        public string callerKey { get; set; }

        [<PERSON>umn, <PERSON>Length(100)]
        public string callerKeyName { get; set; }

        [<PERSON>umn, MaxLength(1)]
        public string appKeyLoc { get; set; }

        [Column, MaxLength(100)]
        public string appKeyPath { get; set; }

        [Column, MaxLength(1)]
        public string callerMsgTypeLoc { get; set; }

        [Column, MaxLength(100)]
        public string callerMsgTypePath { get; set; }

        [Column, MaxLength(1)]
        public string callerMsgTransIdLoc { get; set; }

        [Column, MaxLength(100)]
        public string callerMsgTransIdPath { get; set; }

        [Column]
        public bool active { get; set; }

        [Column]
        public DateTime? createdAtUtc { get; set; }

        /// <summary>
        /// JSON string of configuration for displaying caller.
        /// </summary>
        public string displayConfig { get; set; }        
    }
}