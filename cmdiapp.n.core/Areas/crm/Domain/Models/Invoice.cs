﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Data;
using System.Linq;
using System.Web.Mvc;
using System.Data.Linq;
using System.ComponentModel.DataAnnotations.Schema;

using cmdiapp.n.core.Domain.ViewModels;
using cmdiapp.n.core.Areas.crm.Domain.Data;

namespace cmdiapp.n.core.Areas.crm.Domain.Models
{
    public class invoice_statement : iItemType
    {
        [Column]
        [Key]
        public int INVOICEID { get; set; }

        [Column]
        public string ROOT { get; set; }

        [Column]
        public string VENDOR { get; set; }

        [Column]
        public Nullable<DateTime> INVDTE { get; set; }

        [Column]
        public Nullable<DateTime> DUEDTE { get; set; }

        [Column]
        public Nullable<decimal> AMT { get; set; }

        [Column]
        public string STATUS { get; set; }

        [Column]
        public string ADJTYPE { get; set; }

        [Column]
        public Nullable<DateTime> ADJDTE { get; set; }

        [Column]
        public string FUNDCODE { get; set; }

        [Column]
        public Boolean ISMEMO { get; set; }

        [Column]
        public string DESCRIP { get; set; }

        [Column]
        public string FECTYPE { get; set; }

        [Column]
        public string ELECTYR { get; set; }

        [Column]
        public string FECDESCRIP { get; set; }
    }

    [NotMapped]
    public class invoice_statement_ext : invoice_statement
    {
        public int count_ { get; set; }

        public Int64 rowNo { get; set; }
    }

    [Table("INVOICE")]
    public class INVOICE : _entityBase_crm, iItemType
    {
        [Column]
        [Key]
        public int INVOICEID { get; set; }

        [Column]
        public int ENTITYID { get; set; }

        [Column]
        public Int16 INVSTATUSID { get; set; }

        [Column]
        public Int16 FUNDID { get; set; }

        [Column]
        public Nullable<Int16> CENTERID { get; set; }

        [Column]
        public string INVNO { get; set; }

        [Column]
        public Nullable<DateTime> INVDTE { get; set; }

        [Column]
        public Nullable<DateTime> DUEDTE { get; set; }
        
        [Column]
        public Nullable<decimal> AMT { get; set; }

        [Column]
        public string DESCRIP { get; set; }

        [Column]
        public string FECDESCRIP { get; set; }

        [Column]
        public Nullable<Int16> FECTXNTYPEID { get; set; }

        [Column]
        public Nullable<Int16> TXNCODEID { get; set; }

        [Column]
        public Nullable<Int16> TXNCATID { get; set; }

        [Column]
        public Nullable<Int16> ELECTCDID { get; set; }

        [Column]
        public string ELECTYR { get; set; }

        [Column]
        public string ELECTOTH { get; set; }

        [Column]
        public bool ISMEMO { get; set; }

        [Column]
        public string MEMOTXT { get; set; }

        [Column]
        public Nullable<int> LINKINVOICEID { get; set; }

        [Column]
        public short? UID { get; set; }

        [Column("_updating_uid")]
        public short? updating_uid { get; set; }

        [Column]
        public Nullable<DateTime> UPDATEDON { get; set; }

        [Column]
        public Nullable<decimal> LINKSUM { get; set; }

        [Column]
        public Nullable<int> ORIGINVOICEID { get; set; }

        [Column]
        public Nullable<Int16> ADJTYPEID { get; set; }

        [Column]
        public Nullable<DateTime> ADJDTE { get; set; }

        [Column]
        public Nullable<byte> COUNTER { get; set; }

        [Column]
        public Nullable<decimal> cBALANCE { get; set; }

        [Column]
        public string CHKNO { get; set; }

        [Column]
        public Nullable<DateTime> DISSEMDTE { get; set; }

        [Column]
        public string SOP { get; set; }

        [Column]
        public string SOFECCANID { get; set; }

        [Column]
        public string SOCANPFX { get; set; }

        [Column]
        public string SOCANFN { get; set; }

        [Column]
        public string SOCANMN { get; set; }

        [Column]
        public string SOCANLN { get; set; }

        [Column]
        public string SOCANSFX { get; set; }

        [Column]
        public string SOCANOFFICE { get; set; }

        [Column]
        public string SOCANSTATE { get; set; }

        [Column]
        public string SOCANDIST { get; set; }

        [Column]
        public string SIGNPFX { get; set; }

        [Column]
        public string SIGNFN { get; set; }

        [Column]
        public string SIGNMN { get; set; }

        [Column]
        public string SIGNLN { get; set; }

        [Column]
        public string SIGNSFX { get; set; }

        [Column]
        public Nullable<DateTime> SIGNDTE { get; set; }

    }

    [Table("lkINVSTATUS")]
    public class lkINVSTATUS : _entityBase_crm, iItemType
    {
        [Column]
        [Key]
        public Int16 INVSTATUSID { get; set; }

        [Column]
        public string CODE { get; set; }

        [Column]
        public string DESCRIP { get; set; }
    }

    [Table("jtINVACCT")]
    public class jtINVACCT : _entityBase_crm, iItemType
    {
        [Column]
        [Key]
        public int INVACCTID { get; set; }

        [Column]
        public int INVOICEID { get; set; }

        [Column]
        public Nullable<Int16> CHARTACCTID { get; set; }

        [Column]
        public Nullable<decimal> AMT { get; set; }
    }

    public class v_invoice_AcctChart
    {
        [Column]
        [Key]
        public int INVACCTID { get; set; }

        [Column]
        public int? INVOICEID { get; set; }

        [Column]
        public short CHARTACCTID { get; set; }

        [Column]
        public string DESCRIP { get; set; }

        [Column]
        public Nullable<decimal> AMT { get; set; }
    }

    public class v_ultimate_vendor_invoice 
    {
        [Column]
        [Key]
        public int INVOICEID { get; set; }

        [Column]
        public string NAME { get; set; }

        [Column]
        public Nullable<DateTime> INVDTE { get; set; }

        [Column]
        public Nullable<decimal> AMT { get; set; }

        [Column]
        public int LINKINVOICEID { get; set; }
    }

    [NotMapped]
    public class v_invoices_ext : InvoiceR
    {
        public int count_ { get; set; }

        public Int64 rowNo { get; set; }
    }

    public class InvoiceR : _entityBase_crm, iItemType
    {
        [Column]
        [Key]
        public int INVOICEID { get; set; }

        [Column]
        public int VENDORID { get; set; }

        [Column]
        public string VENDORTYPE { get; set; }

        [Column]
        public string ORGNAME { get; set; }

        [Column]
        public string PREFIX { get; set; }

        [Column]
        public string FNAME { get; set; }

        [Column]
        public string MNAME { get; set; }

        [Column]
        public string LNAME { get; set; }

        [Column]
        public string SUFFIX { get; set; }

        [Column]
        public string STREET { get; set; }

        [Column]
        public string ADDR1 { get; set; }

        [Column]
        public string CITY { get; set; }

        [Column]
        public string STATE { get; set; }

        [Column]
        public string ZIP { get; set; }

        [Column]
        public DateTime? INVDTE { get; set; }

        [Column]
        public decimal? AMT { get; set; }

        [Column]
        public string FUNDCODE { get; set; }

        [Column]
        public string TRANSTYPE { get; set; }

        [Column]
        public string DESCRIPTION { get; set; }

        [Column]
        public string BANKACCT { get; set; }

        [Column]
        public bool MEMO { get; set; }

        [Column]
        public string MEMOTXT { get; set; }

        [Column]
        public string FEC_DESCRIPTION { get; set; }
    }

    public class v_invoice_payment
    {
        [Column]
        [Key]
        public int TXNID { get; set; }

        [Column]
        public Nullable<DateTime> TXNDTE { get; set; }

        [Column]
        public string CHKNO { get; set; }

        [Column]
        public Nullable<decimal> AMT { get; set; }
    }

    [Table("v_entity_invoice")]
    public class v_entity_invoice : _entityBase_crm, iItemType
    {
        [Column]
        [Key]
        public int INVOICEID { get; set; }

        [Column]
        public int ENTITYID { get; set; }

        [Column]
        public string CODE { get; set; }

        [Column]
        public string VENDOR { get; set; }

        [Column]
        public string FUNDCODE { get; set; }

        [Column]
        public string INVNO { get; set; }

        [Column]
        public Nullable<DateTime> INVDTE { get; set; }

        [Column]
        public Nullable<DateTime> DUEDTE { get; set; }

        [Column]
        public Nullable<decimal> AMT { get; set; }

        [Column]
        public string COMMENT { get; set; }

        [Column]
        public string STATUS { get; set; }

        [Column]
        public Nullable<decimal> cBALANCE { get; set; }

        [NotMapped]
        public bool SELECTED { get; set; }

        [NotMapped]
        public decimal PAYAMT { get; set; }
    }

    public class v_entity_invoice_ext : v_entity_invoice
    {
        public int count_ { get; set; }

        public Int64 rowNo { get; set; }
    }

    [Table("INVOICEPAY")]
    public class INVOICEPAY : _entityBase_crm, iItemType
    {
        [Key]
        [Column]
        public int PAYID { get; set; }

        [Column]
        public Nullable<Int16> UID { get; set; }

        [Column]
        public Nullable<int> STARTCHKNO { get; set; }

        [Column]
        public DateTime CHKDATE { get; set; }

        [Column]
        public Nullable<decimal> TOTAMT { get; set; }

        [Column]
        public Nullable<int> TOTCHK { get; set; }

        [Column]
        public Nullable<DateTime> CREATEDON { get; set; }

        [Column]
        public Nullable<DateTime> PRINTEDON { get; set; }
    }

    [Table("INVOICESEL")]
    public class INVOICESEL : _entityBase_crm, iItemType
    {
        [Key]
        [Column]
        public int SELID { get; set; }

        [Column]
        public int PAYID { get; set; }

        [Column]
        public Nullable<int> CHKID { get; set; }

        [Column]
        public int INVOICEID { get; set; }

        [Column]
        public Nullable<decimal> AMT { get; set; }
    }

    [Table("INVOICECHK")]
    public class INVOICECHK : _entityBase_crm, iItemType
    {
        [Key]
        [Column]
        public int CHKID { get; set; }

        [Column]
        public int PAYID { get; set; }

        [Column]
        public Nullable<int> ENTITYID { get; set; }

        [Column]
        public Nullable<int> TXNID { get; set; }

        [Column]
        public string PAYTO { get; set; }

        [Column]
        public Nullable<int> CHKNO { get; set; }

        [Column]
        public Nullable<decimal> AMT { get; set; }

        [Column]
        public string MEMO { get; set; }

        [Column]
        public Nullable<DateTime> PRINTEDON { get; set; }
    }

    public class InvoiceQR_ext1 : InvoiceQR
    {
        public int count_ { get; set; }

        public Int64 rowNo { get; set; }
    }

    public class InvoiceQR : _entityBase_crm, iItemType
    {

        public int INVOICEID { get; set; }
        public string INVNO { get; set; }
        public int VENDORID { get; set; }
        public string VENDORTYPE { get; set; }
        public string ORGNAME { get; set; }
        public string PREFIX { get; set; }
        public string FNAME { get; set; }
        public string MNAME { get; set; }
        public string LNAME { get; set; }
        public string SUFFIX { get; set; }
        public string STREET { get; set; }
        public string ADDR1 { get; set; }
        public string CITY { get; set; }
        public string STATE { get; set; }
        public string ZIP { get; set; }
        public DateTime? INVDTE { get; set; }
        public decimal? AMT { get; set; }
        public string FUNDCODE { get; set; }
        public string STATUS { get; set; }
        public string COMMENT { get; set; }
        public DateTime? FIRST_INVDTE { get; set; }
        public DateTime? RECENT_INVDTE { get; set; }
        public int CTDNO_INV { get; set; }
        public decimal? CTDAMT_INV { get; set; }
        public int YTDNO_INV { get; set; }
        public decimal? YTDAMT_INV { get; set; }
        public decimal? CUMAMT_INV { get; set; }
        public int CHARTACCTID { get; set; }
        public string MEMOTXT { get; set; }
        public string FECDESCRIP { get; set; }
        public bool ISMEMO { get; set; }       
        public Nullable<Int16> FECTXNTYPEID { get; set; }
        public string LINE { get; set; }
        public string ACCTCODE { get; set; }
        public string CODE { get; set; }        
    }

    public class LinkInvViewModel
    {
        public int INVOICEID { get; set; }
        public int LINKINVOICEID { get; set; }
    }

    [Table("INVOICEDOC")]
    public class INVOICEDOC : _entityBase_crm, iItemType
    {
        [Column]
        public int INVOICEDOCID { get; set; }

        [Column]
        public int? INVOICEID { get; set; }

        [Column]
        public string FILENAME { get; set; }

        [Column]
        public byte[] CONTENT { get; set; }

        [Column("_updating_uid")]
        public short? updating_uid { get; set; }
    }

    public class invLookUpData : iItemType
    {
        public List<dmFUND> FUNDCODE;
        public List<dmCENTER> BANKACCCODE;
        public List<lkFECTxnType> LINENO;
        public List<lkChartAcct> CHARTACCT;
        public List<lkExpMonyType> PAYMENTTYPE;
        public List<lkElectCD> ELECTIONCD;
        public List<lkTxnCode> TRANSCODE;
        public List<lkFECDesc> FECDESCRIPTION;
        public List<lkINVSTATUS> INVSTATUS;
        public List<Vendor> VENDORS;
        public List<lkTXNADJTYPE> ADJTYPES;
        public List<canoffice> CANOFFICE;
    }

    [NotMapped]
    public class Inv2 : INVOICE
    {
        public List<v_invoice_AcctChart> chartaccount { get; set; }
        public invUVdata ultimatevendor { get; set; }
        public List<v_invoice_payment> invoicepayment { get; set; }
    }

    public class invUVdata : iItemType
    {
        public List<v_ultimate_vendor_invoice> UVDATA;
        public Decimal? LINKSUM;
        public Decimal? AMT;
    }

    [Table("jtTXNINV")]
    public class jtTXNINV : _entityBase_crm, iItemType
    {
        [Column]
        public int jtTXNINVID { get; set; }

        [Column]
        public int TXNID { get; set; }

        [Column]
        public int  INVOICEID { get; set; }
    }

}