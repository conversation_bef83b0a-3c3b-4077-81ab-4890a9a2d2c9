﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Threading;
using System.Threading.Tasks;
using System.Web;
using System.Web.Http;

namespace cmdiapp.n.core.Areas.query.Domain.Models
{
    public class FileDownloadResult : IHttpActionResult
    {
        private readonly string FilePath;
        private readonly string ContentType;
        private readonly string ClientFileName;

        public FileDownloadResult(string filePath, string clientFileName, string contentType = null)
        {
            FilePath = filePath;
            ContentType = contentType;
            ClientFileName = clientFileName;
        }

        public Task<HttpResponseMessage> ExecuteAsync(CancellationToken cancellationToken)
        {
            return Task.Run<HttpResponseMessage>(() =>
            {
                if (!File.Exists(FilePath))
                {
                    return new HttpResponseMessage(HttpStatusCode.NotFound);
                }
                // open file stream and memory stream and write file stream to memory
                FileStream fileStream = File.OpenRead(FilePath);
                MemoryStream memStream = new MemoryStream();
                fileStream.CopyTo(memStream);
                // set response content as byte array
                ByteArrayContent byteArrayContent = new ByteArrayContent(memStream.ToArray());
                HttpResponseMessage response = new HttpResponseMessage(HttpStatusCode.OK);
                response.Content = byteArrayContent;
                // get file mime type
                string contentType = ContentType ?? MimeMapping.GetMimeMapping(Path.GetExtension(FilePath));
                response.Content.Headers.ContentType = new MediaTypeHeaderValue(contentType);
                response.Content.Headers.ContentLength = byteArrayContent.Headers.ContentLength;
                response.Content.Headers.ContentDisposition =
                    new ContentDispositionHeaderValue("attachment") { FileName = ClientFileName };
                // dispose of streams
                fileStream.Dispose();
                memStream.Dispose();
                return response;
            }, cancellationToken);
        }
    }
}