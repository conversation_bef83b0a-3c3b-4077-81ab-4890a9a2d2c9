﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Net;
using System.Web.Http;
using System.Web.Script.Serialization;
using System.Data;
using System.Data.Common;
using System.Net.Http;
using System.Net.Http.Headers;
using System.IO;
using System.Web.Caching;

using Ninject;
using Ninject.Web.Mvc;

using cmdiapp.n.core.Domain.ViewModels;
using cmdiapp.n.core.Core.App_Start;
using cmdiapp.n.core.Core;
using cmdiapp.n.core.Domain.Data;
using cmdiapp.n.core.Domain.Models;

using cmdiapp.n.core.Areas.crm.Core;
using cmdiapp.n.core.Areas.crm.Domain.Models;
using cmdiapp.n.core.Areas.crm.Domain.ViewModels;
using cmdiapp.n.core.Areas.crm.Domain.Data;
using cmdiapp.n.core.Areas.crm.Domain.Repositories;
using cmdiapp.n.core.Areas.crm.Domain.Services;
using cmdiapp.n.core.Areas.dataService.Domain.Data;
using cmdiapp.n.core.Areas.dataService.Domain.Models;
using cmdiapp.n.core.Areas.dataService.Domain.Services;
using cmdiapp.n.core.Library;
using cmdiapp.n.core.Presentation.Controllers;
using cmdiapp.n.core.Areas.crm.Presentation.Controllers.api;
using AutoMapper;
using DocumentFormat.OpenXml.Drawing.Charts;

namespace cmdiapp.n.core.Areas.crm.Presentation.Controllers.api
{   

    [Authorize]
    public class ComplianceController : ApiController
    {
        #region [ Declaration ]
        
        private I_entity_crm _entity_crm;
        private I_entity _entity;
        private IpeopleService _peopleService;

        #endregion

        public ComplianceController()
        {
            _entity_crm = I_entityManager_crm.getEntity();
            _entity = I_entityManager.getEntity();
            _peopleService = NinjectMVC.kernel.Get<IpeopleService>();
            
        }

        public class DupeProcessBank
        {
            public string[] selectedMERGEPIDs { get; set; }

            public string[] selectedNOTDUPEPIDs { get; set; }
        }
    

        public class redesignation : iItemType
        {
            public int MID { get; set; }
            public int PID { get; set; }
            public string NAME { get; set; }
            public DateTime? BATCHDTE { get; set; }
            public string FUNDCODE { get; set; }
            public decimal? AMT { get; set; }
            public decimal? PAGGREGATE { get; set; }
            public decimal? GAGGREGATE { get; set; }

            public class ADJUSTTYPE
            {
                public string ADJTYPE { get; set; }
                public string DESCRIP { get; set; }
            }

            public DateTime? ADJDTE { get; set; }
            public decimal? ADJAMT { get; set; }

            public class ADJUSTFUNDID
            {
                public Int16? FUNDID { get; set; }
                public string FUNDCODE { get; set; }
            }

            //public string ADJFUNDID { get; set; }
            public decimal? EXCESS_AMT { get; set; }

            public bool? APPROVED { get; set; }
        }

        public class redesignation_ext : redesignation
        {
            public int count_ { get; set; }

            public Int64 rowNo { get; set; }
        }

        [HttpGet, Route("crm/api/GetRedesignationList/Get")]
        [apiAuthorize(AccessElement = "/crm/Compliance/Tasks", AccessLevel = "v")]
        public genericResponse GetRedesignation(int? page, int? pageSize)
        {
            int _pageSize = (pageSize == null || pageSize.Value == 0 ? 10 : pageSize.Value);
            int _pageNo = (page == null || page.Value == 0 ? 1 : page.Value);

            string sql = @"select M.MID, M.PID , 
                            dbo.oFULLNAME(0, P.PREFIX,P.FNAME, P.MNAME, P.LNAME, P.SUFFIX) AS NAME, 
                            M.BATCHDTE,
                            F.FUNDCODE, 
                            M.AMT,
                            CAST((SELECT 0 + SF.CTDAMT FROM SUMMARYF SF INNER JOIN dmFUND F ON F.FUNDID = SF.FUNDID WHERE F.fundType = ''P'' AND F.ACTIVE = 1 AND SF.PID = P.PID FOR XML PATH('''')) as money) [PAGGREGATE],
                            CAST((SELECT 0 + SF.CTDAMT FROM SUMMARYF SF INNER JOIN dmFUND F ON F.FUNDID = SF.FUNDID WHERE F.fundType = ''G'' AND F.ACTIVE = 1 AND SF.PID = P.PID FOR XML PATH('''')) as money) [GAGGREGATE],
                            CASE 
                            WHEN F.fundType = ''P'' THEN 
                            CAST((SELECT 0 + SF.CTDAMT FROM SUMMARYF SF INNER JOIN dmFUND F ON F.FUNDID = SF.FUNDID WHERE F.fundType = ''P'' AND F.ACTIVE = 1 AND SF.PID = P.PID FOR XML PATH('''')) as money) - 
                            ISNULL((CASE
		                             WHEN T.PEOTYPE = ''I'' THEN F.LIMIT_I 
		                             WHEN T.PEOTYPE = ''R'' THEN F.LIMIT_R 
		                             WHEN T.PEOTYPE = ''P'' THEN F.LIMIT_P 
		                             WHEN T.PEOTYPE = ''O'' THEN F.LIMIT_O 
		                             WHEN T.PEOTYPE = ''S'' THEN F.LIMIT_S 
		                             WHEN T.PEOTYPE = ''N'' THEN F.LIMIT_N 
		                             WHEN T.PEOTYPE = ''C'' THEN F.LIMIT_C 
                                    ELSE 0 END),0)
                            WHEN F.fundType = ''G'' THEN 
                            CAST((SELECT 0 + SF.CTDAMT FROM SUMMARYF SF INNER JOIN dmFUND F ON F.FUNDID = SF.FUNDID WHERE F.fundType = ''G'' AND F.ACTIVE = 1 AND SF.PID = P.PID FOR XML PATH('''')) as money) - 
                            ISNULL((CASE
		                             WHEN T.PEOTYPE = ''I'' THEN F.LIMIT_I 
		                             WHEN T.PEOTYPE = ''R'' THEN F.LIMIT_R 
		                             WHEN T.PEOTYPE = ''P'' THEN F.LIMIT_P 
		                             WHEN T.PEOTYPE = ''O'' THEN F.LIMIT_O 
		                             WHEN T.PEOTYPE = ''S'' THEN F.LIMIT_S 
		                             WHEN T.PEOTYPE = ''N'' THEN F.LIMIT_N 
		                             WHEN T.PEOTYPE = ''C'' THEN F.LIMIT_C 
                                    ELSE 0 END),0)
                            ELSE 0 END as EXCESS_AMT,
                            NULL AS ADJUSTTYPE, NULL AS ADJDTE,  NULL AS ADJAMT, NULL AS ADJUSTFUNDID, CONVERT(bit, 0) AS APPROVED
                            from MONY M 
                            INNER JOIN (PEOPLE P JOIN LKPEOTYPE T ON p.PEOTYPEID = T.PEOTYPEID) ON P.PID = M.PID 
                            LEFT JOIN dmFUND F ON F.FUNDID = M.FUNDID
                            LEFT JOIN lkADJTYPE A ON M.ADJTYPEID = A.ADJTYPEID
                            WHERE M.EXCEPID = (select TOP 1 EXCEPID from lkEXCEP where EXCEP = ''G1'') AND F.active = 1";

            string orderBy = "PID DESC, BATCHDTE DESC";
            List<redesignation_ext> _list = new List<redesignation_ext>();

            var q = _entity_crm.getContext().Database.SqlQuery<redesignation_ext>(String.Format("EXEC dbo.get_pagedDataSet '{0}', '{1}', {2}, {3}", sql, orderBy, _pageSize, _pageNo));
            _list = q.ToList();

            Mapper.CreateMap<redesignation_ext, redesignation>();
            IEnumerable<iItemType> results = _list.Select(a => Mapper.Map<redesignation_ext, redesignation>(a)).ToList();

            genericResponse _response;

            if (results.Count() > 0)
            {
                _response = new genericResponse() { success = true, __count = _list.FirstOrDefault().count_, results = results.ToList() };
            }
            else
            {
                _response = new genericResponse() { success = true, __count = 0 };
            }

            return _response;
        }

        [HttpGet, Route("crm/api/compliance/CodedOverLimitDonorsG1")]
        [apiAuthorize(AccessElement = "/crm/Compliance/Tasks", AccessLevel = "v")]
        public genericResponse GetRedesignationCount()
        {
            string sql = @"select COUNT(M.MID) 
                            from MONY M 
                            INNER JOIN (PEOPLE P JOIN LKPEOTYPE T ON p.PEOTYPEID = T.PEOTYPEID) ON P.PID = M.PID 
                            LEFT JOIN dmFUND F ON F.FUNDID = M.FUNDID
                            LEFT JOIN lkADJTYPE A ON M.ADJTYPEID = A.ADJTYPEID
                            WHERE M.EXCEPID = (select TOP 1 EXCEPID from lkEXCEP where EXCEP = '{0}') AND F.active = 1";

            int redesig = _entity_crm.getContext().Database.SqlQuery<int>(string.Format(sql, "G1")).First();

            genericResponse _response;

            try
            {
                _response = new genericResponse() { success = true, __count = redesig };
            }
            catch
            {
                _response = new genericResponse() { success = false, __count = 0 };
            }

            return _response;
        }

        
        [HttpGet, Route("crm/api/compliance/GetDupePairsInfo")]
        [apiAuthorize(AccessElement = "cmdiapp.dms.merge", AccessLevel = "e")]
        public genericResponse GetDupePairsInfo(string matchType = "All", int isIndividual = 1, bool Donors = true)
        {
            #region [[ Old code ]]
            //string sql = @"SELECT *, DENSE_RANK() OVER(ORDER BY KEYLINEID DESC)  AS 'KEYLINERANK' FROM [dbo].[z_dedupe_dupe] WHERE KEYLINEID IN (SELECT DISTINCT TOP 100 KEYLINEID FROM [dbo].[z_dedupe_dupe] WHERE ISNULL([KEEP],0) = 0 and ISNULL([MERGE],0) = 0 ORDER BY KEYLINEID ASC) AND ISNULL([KEEP],0) = 0 and ISNULL([MERGE],0) = 0 ORDER BY KEYLINEID ASC";
            //string sql = @"SELECT *, DENSE_RANK() OVER(ORDER BY KEYLINEID DESC)  AS 'KEYLINERANK' FROM [dbo].[z_dedupe_dupe] Z WHERE KEYLINEID IN (SELECT DISTINCT TOP 20 KEYLINEID FROM [dbo].[z_dedupe_dupe] WHERE ISNULL([KEEP],0) = 0 and ISNULL([MERGE],0) = 0 AND TOTALCOUNT > 1 ORDER BY KEYLINEID ASC) AND ISNULL([KEEP],0) = 0 and ISNULL([MERGE],0) = 0 AND NOT EXISTS(SELECT 1 FROM ssMRG_NOTADUPE S WHERE S.PID1 = Z.PID OR S.PID2 = Z.PID) ORDER BY KEYLINEID, MATCHED ASC";

            //IF WE NEED TO DISPLAY PROCESSED RECORDS...
            //string sql = @"SELECT *, DENSE_RANK() OVER(ORDER BY KEYLINEID DESC)  AS 'KEYLINERANK' FROM [dbo].[z_dedupe_dupe] Z WHERE KEYLINEID IN (SELECT DISTINCT TOP 20 KEYLINEID FROM [dbo].[z_dedupe_dupe] AND TOTALCOUNT > 1 ORDER BY KEYLINEID ASC) ORDER BY KEYLINEID, MATCHED ASC";
            //string sql = @"IF OBJECT_ID('TEMPDB..#TEMPTBL') IS NOT NULL BEGIN DROP TABLE #TEMPTBL END SELECT DISTINCT top 20 keylineid , CTDAMT INTO #TEMPTBL FROM [z_dedupe_dupe] WHERE ISNULL([KEEP],0) = 0 and ISNULL([MERGE],0) = 0 AND TOTALCOUNT > 1 ORDER BY CTDAMT DESC SELECT *, DENSE_RANK() OVER(ORDER BY KEYLINEID DESC) AS 'KEYLINERANK' FROM [dbo].[z_dedupe_dupe] Z WHERE KEYLINEID IN (SELECT DISTINCT KEYLINEID FROM  #TEMPTBL) AND ISNULL([KEEP],0) = 0  AND ISNULL([MERGE],0) = 0  AND NOT EXISTS(SELECT 1 FROM ssMRG_NOTADUPE S WHERE S.PID1 = Z.PID OR S.PID2 = Z.PID) ORDER BY KEYLINEID, MATCHED ASC";
            #endregion

            string sql = string.Format(@"EXEC [dbo].[z_dedupe_pull] 20, '{0}',{1},{2}", matchType, isIndividual, Donors ? 1 : 0);

            List<v_DupeR_ext1> _list = new List<v_DupeR_ext1>();

            var q = _entity_crm.getContext().Database.SqlQuery<v_DupeR_ext1>(sql);
            _list = q.ToList();

            Mapper.CreateMap<v_DupeR_ext1, v_DupeR_ext1>();
            IEnumerable<iItemType> results = _list.Select(a => Mapper.Map<v_DupeR_ext1, v_DupeR_ext1>(a)).ToList();

            genericResponse _response;

            if (results.Count() > 0)
            {
                _response = new genericResponse() { success = true, __count = _list.FirstOrDefault().count_, results = results.ToList() };
            }
            else
            {
                _response = new genericResponse() { success = true, __count = 0 };
            }

            return _response;

        }

        [HttpGet, Route("crm/api/compliance/GetDupeFilters")]
        [apiAuthorize(AccessElement = "cmdiapp.dms.merge", AccessLevel = "e")]
        public List<string> GetDupeFilters()
        {
            string sql = @"SELECT DISTINCT MATCHTYPE FROM z_dedupe_dupe";

            List<string> _list = new List<string>();

            var q = _entity_crm.getContext().Database.SqlQuery<string>(sql);
            _list = q.ToList();
            
            return _list;

        }

        #region [[  NO need to double check after user's decision to merge or keep  ]]
        //public string GetSQLToVerify(string matchType, int selFirstPID, int selSecondPID)
        //{
        //    string cSql = "";

        //    if (matchType == "ADDRESS")
        //    {
        //        cSql = string.Format("SELECT COUNT(*) OCCURS FROM PEOPLE P OUTER APPLY (SELECT TOP 1 * FROM PKEY J WHERE J.PID = P.PID) DK WHERE P.ACTIVE = 1 AND (P.PID = {0} OR P.PID = {1}) AND DK.KEYTADDR <> '' AND P.LNAME  <> '' AND P.FNAME  <> '' AND NOT EXISTS(SELECT 1 FROM ssMRG_NOTADUPE S WHERE (S.PID1 = {0} AND S.PID2 = {1})  OR (S.PID1 = {1} AND S.PID2 = {0})) GROUP BY LEFT(DK.KEYTADDR,15) + CAST(LEFT(REPLACE(ISNULL(P.LNAME,''),' ',''),5) AS CHAR(5)) + CAST(LEFT(REPLACE(ISNULL(P.FNAME,''),' ',''),3) AS CHAR(3)) HAVING COUNT(*) > 1", selFirstPID, selSecondPID);
        //    }
        //    else if (matchType == "EMPLOYER")
        //    {
        //        cSql = string.Format("SELECT COUNT(*) OCCURS FROM PEOPLE P WHERE P.ACTIVE = 1 AND P.EMPLOYER  <> '' AND P.LNAME  <> '' AND P.FNAME  <> '' AND (P.PID = {0} OR P.PID = {1}) AND P.EMPLOYER NOT IN ('','N/A','RETIRED','SELF','SELF-EMPLOYED','NONE','HOMEMAKER','NA','SELF EMPLOYED','UNEMPLOYED','-','.') AND NOT EXISTS(SELECT 1 FROM ssMRG_NOTADUPE S WHERE (S.PID1 = {0} AND S.PID2 = {1})  OR (S.PID1 = {1} AND S.PID2 = {0})) GROUP BY LEFT(LTRIM(RTRIM(ISNULL(P.EMPLOYER,''))),20) + CAST(LEFT(REPLACE(ISNULL(P.LNAME,''),' ',''),5) AS CHAR(5)) + CAST(LEFT(REPLACE(ISNULL(P.FNAME,''),' ',''),3) AS CHAR(3)) HAVING COUNT(*) > 1", selFirstPID, selSecondPID);
        //    }
        //    else if (matchType == "HOMEPHONE")
        //    {
        //        cSql = string.Format("SELECT COUNT(*) OCCURS FROM PEOPLE P JOIN PHONE PH ON P.PID = PH.PID AND PH.PRIME = 1 AND PH.PHNTYPEID = 1 WHERE P.ACTIVE = 1 AND LEN(PH.PHNNO) > 6 AND (P.PID = {0} OR P.PID = {1}) AND NOT EXISTS(SELECT 1 FROM ssMRG_NOTADUPE S WHERE (S.PID1 = {0} AND S.PID2 = {1})  OR (S.PID1 = {1} AND S.PID2 = {0})) GROUP BY PH.PHNNO + CAST(LEFT(REPLACE(ISNULL(P.LNAME,''),' ',''),5) AS CHAR(5)) + CAST(LEFT(REPLACE(ISNULL(P.FNAME,''),' ',''),3) AS CHAR(3)) HAVING COUNT(*) > 1", selFirstPID, selSecondPID);
        //    }
        //    else if (matchType == "EMAIL")
        //    {
        //        cSql = string.Format("SELECT COUNT(*) OCCURS FROM PEOPLE P JOIN PHONE PH ON P.PID = PH.PID AND PH.PRIME = 1 AND PH.PHNTYPEID = 4 WHERE P.ACTIVE = 1 AND CHARINDEX('@',PH.PHNNO) > 0  AND (P.PID = {0} OR P.PID = {1}) AND NOT EXISTS(SELECT 1 FROM ssMRG_NOTADUPE S WHERE (S.PID1 = {0} AND S.PID2 = {1})  OR (S.PID1 = {1} AND S.PID2 = {0})) GROUP BY PH.PHNNO + CAST(LEFT(REPLACE(ISNULL(P.LNAME,''),' ',''),5) AS CHAR(5)) + CAST(LEFT(REPLACE(ISNULL(P.FNAME,''),' ',''),3) AS CHAR(3)) HAVING COUNT(*) > 1", selFirstPID, selSecondPID);
        //    }
        //    else if (matchType == "SPOUSE")
        //    {
        //        cSql = string.Format("SELECT COUNT(*) OCCURS FROM PEOPLE P WHERE P.ACTIVE = 1 AND P.SPOUSENAME  <> '' AND P.LNAME  <> '' AND (P.PID = {0} OR P.PID = {1}) AND NOT EXISTS(SELECT 1 FROM ssMRG_NOTADUPE S WHERE (S.PID1 = {0} AND S.PID2 = {1})  OR (S.PID1 = {1} AND S.PID2 = {0})) GROUP BY ISNULL(P.SPOUSENAME,'') + CAST(LEFT(REPLACE(ISNULL(P.LNAME,''),' ',''),5) AS CHAR(5)) + CAST(LEFT(REPLACE(ISNULL(P.FNAME,''),' ',''),3) AS CHAR(3)) HAVING COUNT(*) > 1", selFirstPID, selSecondPID);

        //    }
        //    else if (matchType == "CELLPHONE")
        //    {
        //        cSql = string.Format("SELECT COUNT(*) OCCURS FROM PEOPLE P JOIN PHONE PH ON P.PID = PH.PID AND PH.PRIME = 1 AND PH.PHNTYPEID = 3 WHERE P.ACTIVE = 1 AND LEN(PH.PHNNO) > 6 AND (P.PID = {0} OR P.PID = {1}) AND NOT EXISTS(SELECT 1 FROM ssMRG_NOTADUPE S WHERE (S.PID1 = {0} AND S.PID2 = {1})  OR (S.PID1 = {1} AND S.PID2 = {0})) GROUP BY PH.PHNNO + CAST(LEFT(REPLACE(ISNULL(P.LNAME,''),' ',''),5) AS CHAR(5)) + CAST(LEFT(REPLACE(ISNULL(P.FNAME,''),' ',''),3) AS CHAR(3)) HAVING COUNT(*) > 1", selFirstPID, selSecondPID);
        //    }
        //    //go back
        //    return cSql;
        //}
        #endregion

        public bool checkKeylineId(string matchType, int selFirstPID, int selSecondPID, int keylineForThisSet)
        {
            try
            {                
                string sql = string.Format("select * from [dbo].[z_dedupe_dupe] where (PID ={0} or PID={1}) and MATCHTYPE='{2}' AND KEYLINEID={3}", selFirstPID, selSecondPID, matchType, keylineForThisSet);                                
                List<DupeR> dupe = _entity_crm.getContext().Database.SqlQuery<DupeR>(sql).ToList();
                if (dupe.Count() == 2 && dupe[0].KEYLINEID == dupe[1].KEYLINEID)
                {
                    return true;
                }
                return false;
            }
            catch (Exception ex)
            {
                return false;
            }
        }


        [HttpPost, Route("crm/api/compliance/ProcessDupeInfo")]
        [apiAuthorize(AccessElement = "cmdiapp.dms.merge", AccessLevel = "e")]
        public genericResponse ProcessDupeInfo(DupeProcessBank dupeBank)
        {
            genericResponse _response = null;
            string processIssues = "Could not process :";
            bool OverallSuccess = true;
            int recCount = 0;
            
            try
            {

                #region [[ For Non Dupe ]]
                //Let us process the Non Dupes first if selected by the user...
                if (dupeBank.selectedNOTDUPEPIDs != null && dupeBank.selectedNOTDUPEPIDs.Count() > 0)
                {
                    for (int i = 0; i <= dupeBank.selectedNOTDUPEPIDs.Count() - 1; i++)
                    {

                        try
                        {
                            #region [[ get second pair PID ]]
                            string[] temp = dupeBank.selectedNOTDUPEPIDs[i].ToString().Split('+');
                            int selFirstPID = Convert.ToInt32(temp[0]);
                            int keyLineId= Convert.ToInt32(temp[1]);
                            //Let us find second pair for this selection.
                            string sql = string.Format(@"SELECT * FROM [dbo].[z_dedupe_dupe] 
                            WHERE MATCHED in (1,2) AND PID != {0}
                            AND KEYLINEID = {1} ", selFirstPID, keyLineId);
                            var q = _entity_crm.getContext().Database.SqlQuery<v_DupeR_ext1>(sql);
                            int totalRecords = q.ToList().FirstOrDefault().TOTALCOUNT;
                            int selSecondPID = q.ToList().FirstOrDefault().PID;
                            int keylineForThisSet = q.ToList().FirstOrDefault().KEYLINEID;
                            string matchType = q.ToList().FirstOrDefault().MATCHTYPE;
                            #endregion

                            #region [[ make sure pair exists ]]
                            //Now make sure Both Pair exists and have same KeyLineID....
                            bool checkKeylineID = checkKeylineId(matchType, selFirstPID, selSecondPID, keylineForThisSet);
                            
                            #endregion

                            #region [[ step after verification ]]
                            if (checkKeylineID)
                            {
                                #region [[ Mark them as Non dupe ]]
                                int _dummy = _entity_crm.getContext().Database.ExecuteSqlCommand("EXEC dbo.record_notAdupe {0}, {1}, {2}", selFirstPID, selSecondPID, session.userSession.UserName + "(Crimson)");
                                #endregion

                                #region [[ Mark in the temp table so we do not pull them again ]]
                                List<stru_keyValue> result = null;
                                //Now deal with the Report Defination...
                                var usql = string.Format("Update [dbo].[z_dedupe_dupe] SET KEEP = 1,[MATCHED] = NULL WHERE PID = {0}", selFirstPID);
                                usql +=" "+ string.Format(" Update [dbo].[z_dedupe_dupe] SET [KEEP] = 1 WHERE PID IN ({0})", selSecondPID);
                                if (totalRecords > 2)
                                {
                                    //Additional Processing to change the Matched Sequence.....we have have more than 2 records in a set...first two are done so we need
                                    //to change the matched sequence so that we can continue the process......
                                    int desiredTC = totalRecords - 1;

                                    usql += " " + string.Format(" Update [dbo].[z_dedupe_dupe] SET [MATCHED] = 1 WHERE PID IN ({0})", selSecondPID);
                                    usql += " " + string.Format(" Update [dbo].[z_dedupe_dupe] SET [TOTALCOUNT] = " + desiredTC + " WHERE KEYLINEID = {0} ", keylineForThisSet);
                                    //Now change the Matched sequence for remaining records...
                                    usql += " " + string.Format(" Update [dbo].[z_dedupe_dupe] SET [MATCHED] = [MATCHED] - 1 WHERE KEYLINEID = {0} AND PID NOT IN ({1},{2}) ", keylineForThisSet, selFirstPID, selSecondPID);

                                }
                                usql += " select 'success' as 'key'";
                                //Execute the SQL Now....
                                var uq = _entity_crm.getContext().Database.SqlQuery<stru_keyValue>(usql);
                                //result..
                                result = uq.ToList();
                                #endregion

                                recCount = recCount + 1;

                            }
                            else
                            {
                                //we will continue to the next selection in the array...
                                processIssues = processIssues + " " + dupeBank.selectedNOTDUPEPIDs[i].ToString();
                                OverallSuccess = false;
                            }
                            #endregion
                                                        
                        }
                        catch(Exception ex)
                        {
                            //we will continue to the next selection in the array...
                            processIssues = processIssues + " " + dupeBank.selectedNOTDUPEPIDs[i].ToString();
                            OverallSuccess = false;
                        }
                        
                    }
                }
                #endregion

                #region [[ For Merge-Keep ]]
                //Let us process the Non Dupes first if selected by the user...
                if (dupeBank.selectedMERGEPIDs != null && dupeBank.selectedMERGEPIDs.Count() > 0)
                {
                    for (int i = 0; i <= dupeBank.selectedMERGEPIDs.Count() - 1; i++)
                    {

                        try
                        {
                            #region [[ get second pair PID ]]
                            //int selKeepPID = Convert.ToInt32(dupeBank.selectedMERGEPIDs[i].ToString());
                            string[] temp2 = dupeBank.selectedMERGEPIDs[i].ToString().Split('+');
                            int selKeepPID = Convert.ToInt32(temp2[0]);
                            int keyLineId2 = Convert.ToInt32(temp2[1]);
                            //Let us find second pair for this selection.
                            string sql = string.Format(@"SELECT * FROM [dbo].[z_dedupe_dupe] 
WHERE MATCHED in (1,2) AND  PID != {0} AND KEYLINEID ={1} ", selKeepPID,keyLineId2);
                            v_DupeR_ext1 rec = new v_DupeR_ext1();
                            var q = _entity_crm.getContext().Database.SqlQuery<v_DupeR_ext1>(sql);
                            int selMergePID = q.ToList().FirstOrDefault().PID;
                            //Let us find out the Matched Seq for this record
                            //int matchSeqForMergePID = q.ToList().FirstOrDefault().MATCHED;
                            int totalRecords = q.ToList().FirstOrDefault().TOTALCOUNT;
                            int keylineForThisSet = q.ToList().FirstOrDefault().KEYLINEID;
                            string matchType = q.ToList().FirstOrDefault().MATCHTYPE;
                            #endregion

                            #region [[ make sure pair exists and still dupes ]]
                            //Now make sure Both Pair exists and have same KeyLineID....
                            bool checkKeylineID = checkKeylineId(matchType, selKeepPID, selMergePID, keylineForThisSet);
                            
                            #endregion

                            #region [[ After Verification ]]
                            if (checkKeylineID)
                            {
                                string _sql = "declare @YES bit, @MSG varchar(100) " +
                                          " exec iMRG_b " + selKeepPID.ToString() + "," + selMergePID.ToString() + "," + crmSession.UID() + ",@YES output ,@MSG output " +
                                          " select @YES as 'YES',@MSG as 'MSG'";
                                SP_response __Yes_MSG = _peopleService.execute_SP(_sql);

                                #region [[ Mark in the temp table so we do not pull them again ]]
                                List<stru_keyValue> result = null;
                                //Now deal with the Report Defination...
                                var usql = string.Format("Update [dbo].[z_dedupe_dupe] SET [MERGE] = 1, [MATCHED] = NULL WHERE PID IN ({0})", selMergePID);
                                if (totalRecords == 2)
                                {
                                    usql += " " + string.Format(" Update [dbo].[z_dedupe_dupe] SET [KEEP] = 1 WHERE PID IN ({0})", selKeepPID); 
                                }
                                else
                                {
                                    //Additional Processing to change the Matched Sequence.....we have have more than 2 records in a set...first two are done so we need
                                    //to change the matched sequence so that we can continue the process......
                                    int desiredTC = totalRecords - 1;

                                    usql += " " + string.Format(" Update [dbo].[z_dedupe_dupe] SET [MATCHED] = 1 WHERE PID IN ({0})", selKeepPID);
                                    usql += " " + string.Format(" Update [dbo].[z_dedupe_dupe] SET [TOTALCOUNT] = " + desiredTC + " WHERE KEYLINEID = {0} ", keylineForThisSet);
                                    //Now change the Matched sequence for remaining records...
                                    usql += " " + string.Format(" Update [dbo].[z_dedupe_dupe] SET [MATCHED] = [MATCHED] - 1 WHERE KEYLINEID = {0} AND PID NOT IN ({1},{2}) ", keylineForThisSet, selMergePID, selKeepPID); 

                                }
                                usql += " select 'success' as 'key'";
                                //Execute the SQL Now....
                                var uq = _entity_crm.getContext().Database.SqlQuery<stru_keyValue>(usql);
                                //result..
                                result = uq.ToList();
                                #endregion

                                recCount = recCount + 1;

                            }
                            else
                            {
                                //we will continue to the next selection in the array...
                                processIssues = processIssues + " " + dupeBank.selectedNOTDUPEPIDs[i].ToString();
                                OverallSuccess = false;
                            }
                            #endregion
                        }
                        catch(Exception ex)
                        {
                            //we will continue to the next selection in the array...
                            processIssues = processIssues + " " + dupeBank.selectedMERGEPIDs[i].ToString();
                            OverallSuccess = false;
                        }

                    }

                }
                #endregion

                if (OverallSuccess)
                {
                    //we are good...all selected pairs processed
                    _response = new genericResponse() { success = true };
                }
                else
                {
                    //we are good...but with few exception...
                    _response = new genericResponse() { success = false, message = processIssues, __count = recCount };
                }
                
            }
            catch(Exception ex)
            {
                _response = new genericResponse() { success = false, message = "Problem processing Dupes records. Please contact customer support.", __count = 0  };
            }

            return _response;

        }

        
        //[HttpPost, Route("crm/api/Mony/RedesigAdjust")]
        //public genericResponse RedsignationAdjust(MONYADJUST monyadjust)
        //{
        //    genericResponse response;
        //    genericResponse responseAdjust = new genericResponse();

        //    try
        //    {
        //        //Insert - Lydia Method
        //        responseAdjust = Adjust(monyadjust.MID, monyadjust);

        //        if (responseAdjust.success == true)
        //        {
        //            //get G3 exception id.
        //            short? excepId = (Int16?)_entity_crm.getContext().Database.SqlQuery<Int16>(string.Format("select TOP 1 EXCEPID from lkEXCEP where EXCEP = '{0}'", "G3")).Single();

        //            //update original mony with G3 exception id.
        //            MONY origRecord = _entity_crm.Single<MONY>(a => a.MID == monyadjust.MID);
        //            if (origRecord != null)
        //            {
        //                origRecord.EXCEPID = excepId;
        //                origRecord.EXCEPDTE = DateTime.Now;
        //                _entity_crm.Update(origRecord);
        //                _entity_crm.CommitChanges();
        //            }

        //            //update new adjusted record with G3 exception id.
        //            MONY adjustRecord = _entity_crm.Single<MONY>(a => a.MID == responseAdjust.UniqueId);
        //            if (adjustRecord != null)
        //            {
        //                adjustRecord.EXCEPID = excepId;
        //                adjustRecord.EXCEPDTE = DateTime.Now;
        //                _entity_crm.Update(adjustRecord);
        //                _entity_crm.CommitChanges();
        //            }
        //        }

        //        response = new genericResponse() { success = true, __count = 1 };
        //        return response;
        //    }
        //    catch
        //    {
        //        response = new genericResponse() { success = false, __count = 0 };
        //        return response;
        //    }
        //}
                

        [HttpPost, Route("crm/api/Mony/RedesigAdjustList")]
        [apiAuthorize(AccessElement = "/crm/Compliance/Tasks", AccessLevel = "e")]
        public genericResponse RedsignationAdjustList(IList<MonyController.MONYADJUST> monyadjustList)
        {
            genericResponse response;
            genericResponse responseAdjust = new genericResponse();
            MonyController monyCtrl = new MonyController();

            try
            {
                if (monyadjustList.Count > 0)
                {
                    foreach (MonyController.MONYADJUST monyadjust in monyadjustList)
                    {
                        //Insert - Adjust Method
                        responseAdjust = monyCtrl.Adjust(monyadjust.MID, monyadjust);

                        if (responseAdjust.success == true)
                        {
                            //get G3 exception id.
                            short? excepId = (Int16?)_entity_crm.getContext().Database.SqlQuery<Int16>(string.Format("select TOP 1 EXCEPID from lkEXCEP where EXCEP = '{0}'", "G3")).Single();

                            //update original mony with G3 exception id.
                            MONY origRecord = _entity_crm.Single<MONY>(a => a.MID == monyadjust.MID);
                            if (origRecord != null)
                            {
                                origRecord.EXCEPID = excepId;
                                origRecord.EXCEPDTE = DateTime.Now;
                                _entity_crm.Update(origRecord);
                                _entity_crm.CommitChanges();
                            }

                            //get Adjusted MID
                            List<int> adjustedMids = _entity_crm.getContext().Database.SqlQuery<int>(string.Format("select MID from MONY where ORIGMID = {0}", monyadjust.MID)).ToList();

                            //update new adjusted records with G3 exception id.
                            foreach (int adjMid in adjustedMids)
                            {
                                MONY adjustRecord = _entity_crm.Single<MONY>(a => a.MID == adjMid);
                                if (adjustRecord != null)
                                {
                                    adjustRecord.EXCEPID = excepId;
                                    adjustRecord.EXCEPDTE = DateTime.Now;
                                    _entity_crm.Update(adjustRecord);
                                    _entity_crm.CommitChanges();
                                }
                            }
                            
                        }
                    }
                }

                response = new genericResponse() { success = true, __count = monyadjustList.Count };
                return response;
            }
            catch
            {
                response = new genericResponse() { success = false, __count = 0 };
                return response;
            }      
        }


        [HttpGet, Route("crm/api/compliance/complianceListDefById/{id}")]
        [apiAuthorize(AccessElement = @"Compliance\Compliance", AccessLevel = "v")]
        public listWithExportTemplateDef complianceListDefById(string id)
        {
            return getComplianceListDef(id);
        }

        private listWithExportTemplateDef getComplianceListDef(string id)
        {
            listWithExportTemplateDef def = new listWithExportTemplateDef();
            switch (id)
            {
                case "PossibleDuplicatesIndividuals":
                    def = new listWithExportTemplateDef(
                id,
                "Individual Duplicate",
                "crm/api/dashboard/compliance/list/" + id,
                "crm/api/Dashboard/ExportDuplicateIndividual",
                new List<actionBtn>()
                {
                    new actionBtn("", "fa fa-pencil data-table-btn edit-btn", "gotoMerge", "*", "View/Edit", true)
                },
                new List<fieldFormat>()
                {
                    new fieldFormat("int1","Id",null,"gotoPeopleTemplate"),
                    new fieldFormat("prefix","Prefix"),
                    new fieldFormat("fname","First Name"),
                    new fieldFormat("mname","Middle Name"),
                    new fieldFormat("lname","Last Name"),
                    new fieldFormat("int2","Matching Id",null,"gotoPeopleTemplate"),
                    new fieldFormat("str1","Field")
                }
                );
                    break;

                case "PossibleDuplicatesNon-Individuals":
                    def = new listWithExportTemplateDef(
                id,
                "Non-Individual Duplicate",
                "crm/api/dashboard/compliance/list/" + id,
                "crm/api/Dashboard/ExportDuplicateNonIndividual",
                new List<actionBtn>()
                {
                    new actionBtn("", "fa fa-pencil data-table-btn edit-btn", "gotoMerge", "*", "View/Edit", true)
                },
                new List<fieldFormat>()
                {
                    new fieldFormat("int1","Id",null,"gotoPeopleTemplate"),
                    new fieldFormat("prefix","Prefix"),
                    new fieldFormat("fname","First Name"),
                    new fieldFormat("mname","Middle Name"),
                    new fieldFormat("lname","Last Name"),
                    new fieldFormat("int2","Matching Id",null,"gotoPeopleTemplate"),
                    new fieldFormat("str1","Field")
                }
                );
                    break;

                case "IrregularNames":
                    def = new listWithExportTemplateDef(
                id,
                "Irregular Name",
                "crm/api/dashboard/compliance/list/" + id,
                "crm/api/Dashboard/ExportIrregularName",
                new List<actionBtn>(),
                new List<fieldFormat>()
                {
                    new fieldFormat("id","Id",null,"gotoPeopleTemplate"),
                    new fieldFormat("prefix","Prefix"),
                    new fieldFormat("fname","First Name"),
                    new fieldFormat("mname","Middle Name"),
                    new fieldFormat("lname","Last Name"),
                    new fieldFormat("str2","Reason"),
                    new fieldFormat("str1","Field"),
                    new fieldFormat("date1","MRC Date","date"),
                    new fieldFormat("money1","CTD Total","money"),
                    new fieldFormat("money2","YTD Total","money")
                }
                );
                    break;
                case "ShortNames":
                    def = new listWithExportTemplateDef(
                id,
                "Short Name",
                "crm/api/dashboard/compliance/list/" + id,
                "crm/api/Dashboard/ExportShortName",
                new List<actionBtn>(),
                new List<fieldFormat>()
                {
                    new fieldFormat("id","Id",null,"gotoPeopleTemplate"),
                    new fieldFormat("prefix","Prefix"),
                    new fieldFormat("fname","First Name"),
                    new fieldFormat("mname","Middle Name"),
                    new fieldFormat("lname","Last Name"),
                    new fieldFormat("date1","MRC Date","date"),
                    new fieldFormat("money1","CTD Total","money"),
                    new fieldFormat("money2","YTD Total","money")
                }
                );
                    break;

                case "PACswoFECID":
                    def = new listWithExportTemplateDef(
                id,
                "PAC's Without FEC ID",
                "crm/api/dashboard/compliance/list/" + id,
                "crm/api/Dashboard/ExportPACWithoutFEC",
                new List<actionBtn>(),
                new List<fieldFormat>()
                {
                    new fieldFormat("id","Id",null,"gotoPeopleTemplate"),
                    new fieldFormat("name","Name"),
                    new fieldFormat("street","Street"),
                    new fieldFormat("city","City"),
                    new fieldFormat("state","State"),
                    new fieldFormat("date1","Gift Date","date"),
                    new fieldFormat("money1","CTD","money"),
                    new fieldFormat("money2","YTD","money")
                }
                );
                    break;

                case "PartnershipsLLCswoAttributions":
                    def = new listWithExportTemplateDef(
                id,
                "Partnership w/o partnership attributions",
                "crm/api/dashboard/compliance/list/" + id,
                "crm/api/Dashboard/ExportPartnershipAttr",
                new List<actionBtn>(),
                new List<fieldFormat>()
                {
                    new fieldFormat("id","Id",null,"gotoPeopleTemplate"),
                    new fieldFormat("name","Name"),
                    new fieldFormat("street","Street"),
                    new fieldFormat("city","City"),
                    new fieldFormat("state","State"),
                    new fieldFormat("zip","Zip Code"),
                    new fieldFormat("date1","Gift Date","date"),
                    new fieldFormat("money1","CTD","money"),
                    new fieldFormat("money2","YTD","money")
                }
                );
                    break;
                case "UncodedOverLimitDonors":
                    def = new listWithExportTemplateDef(
                        id,
                        "Uncoded Overlimit Donors",
                        "crm/api/dashboard/compliance/list/" + id,
                        "crm/api/Dashboard/ExportOverlimitDonors",
                        new List<actionBtn>(),
                        new List<fieldFormat>()
                            {
                                new fieldFormat("int1","Id",null,"gotoPeopleTemplate"),
                                new fieldFormat("name","Name"),
                                new fieldFormat("str1","Fund Code"),
                                new fieldFormat("money1","Amount", "money"),
                                new fieldFormat("date1","MRC Date", "date")
                            }
                        );
                    break;
                case "UnbalancedEarmarks":
                    def = new listWithExportTemplateDef(
                        id,
                        "Unbalanced Earmarks",
                        "crm/api/dashboard/compliance/UnbalancedEarmarksList",
                        "crm/api/Dashboard/ExportUnbalancedEarmarks",
                        new List<actionBtn>(),
                        new List<fieldFormat>()
                            {
                                new fieldFormat("MID", "MID", null, "gotoMoneyTemplate"),
                                new fieldFormat("PID","PID",null,"gotoPeopleTemplate"),
                                new fieldFormat("FNAME","First"),
                                new fieldFormat("LNAME","Last"),
                                new fieldFormat("AMT","Amount", "money"),
                                new fieldFormat("ATTRIBUTED", "Attributed", "money"),
                                new fieldFormat("ATTRIBUTEDCOUNT", "# Attributed", "number"),
                                new fieldFormat("DIFFERENCE", "Difference", "money")
                            }
                        );
                    break;
                case "UnbalancedWinRedEarmarks":
                    def = new listWithExportTemplateDef(
                        id,
                        "Unbalanced WinRed Earmarks",
                        "crm/api/dashboard/compliance/UnbalancedWinRedEarmarksList",
                        "crm/api/Dashboard/ExportUnbalancedWinRedEarmarks",
                        new List<actionBtn>(),
                        new List<fieldFormat>()
                            {
                                new fieldFormat("MID", "MID", null, "gotoMoneyTemplate"),
                                new fieldFormat("PID","PID",null,"gotoPeopleTemplate"),
                                new fieldFormat("FNAME","First"),
                                new fieldFormat("LNAME","Last"),
                                new fieldFormat("AMT","Amount", "money"),
                                new fieldFormat("ATTRIBUTED", "Attributed", "money"),
                                new fieldFormat("ATTRIBUTEDCOUNT", "# Attributed", "number"),
                                new fieldFormat("DIFFERENCE", "Difference", "money")
                            }
                        );
                    break;
                case "ComplianceBadAddresses":
                    def = new listWithExportTemplateDef(
                        id,
                        "Compliance Bad Addresses",
                        "crm/api/dashboard/compliance/ComplianceBadAddressesList",
                        "crm/api/Dashboard/ExportComplianceBadAddresses",
                        new List<actionBtn>(),
                        new List<fieldFormat>()
                            {
                                new fieldFormat("PID","PID",null,"gotoPeopleTemplate"),
                                new fieldFormat("FNAME","First"),
                                new fieldFormat("LNAME","Last"),
                                new fieldFormat("STREET","Street"),
                                new fieldFormat("CITY", "City"),
                                new fieldFormat("STATE", "State"),
                                new fieldFormat("ZIP","Zip"),
                                new fieldFormat("CTDAMT", "CTD Total", "money"),
                                new fieldFormat("YTDAMT", "YTD Total", "money")
                            }
                        );
                    break;
                /*
                case "ExpiringCodedOverlimits":
                    def = new listWithExportTemplateDef(
                        id,
                        "Expiring Coded Overlimit Donors",
                        "crm/api/dashboard/compliance/ExpiringCodedOverlimitsList",
                        "crm/api/Dashboard/ExportExpiringCodedOverlimits",
                        new List<actionBtn>(),
                        new List<fieldFormat>()
                            {
                                new fieldFormat("MID", "MID", null, "gotoMoneyTemplate"),
                                new fieldFormat("PID","PID",null,"gotoPeopleTemplate"),
                                new fieldFormat("FNAME","First"),
                                new fieldFormat("LNAME","Last"),
                                new fieldFormat("AMT","Amount", "money"),
                                new fieldFormat("BATCHDTE", "Batch Date", "date"),
                                new fieldFormat("EXCEP", "Exception"),
                                new fieldFormat("POTREFUNDDATE", "Pot. Refund Date", "date"),
                                new fieldFormat("FUNDCODE", "Fund"),
                                new fieldFormat("LIMIT", "Limit", "money"),
                                new fieldFormat("TOTAL", "Total", "money"),
                            }
                        );
                    break;
                */
                case "ExpiringCodedOverlimits":
                    def = new listWithExportTemplateDef(
                        id,
                        "Expiring Coded Overlimit Donors",
                        "crm/api/dashboard/compliance/list/" + id,
                        "crm/api/Dashboard/ExpiringCodedOverlimits",
                        new List<actionBtn>(),
                        new List<fieldFormat>()
                            {
                                new fieldFormat("id", "MID", null, "gotoMoneyTemplate"),
                                new fieldFormat("int1","PID",null,"gotoPeopleTemplate"),
                                new fieldFormat("fname","First"),
                                new fieldFormat("lname","Last"),
                                new fieldFormat("money1","Amount", "money"),
                                new fieldFormat("date1", "Batch Date", "date"),
                                new fieldFormat("str1", "Exception"),
                                new fieldFormat("date2", "Pot. Refund Date", "date"),
                                new fieldFormat("str2", "Fund"),
                                new fieldFormat("int2", "Limit", "money"),
                                new fieldFormat("money2", "Total", "money"),
                            }
                        );
                    break;
            }

            return def;
        }

        [HttpGet, Route("crm/api/compliance/ComplianceAdjustmentSummary/{adjustment}")]
        [apiAuthorize(AccessElement = @"Compliance\Compliance", AccessLevel = "v")]
        public listWithExportTemplateDef complianceAdjustmentSummary(string adjustment)
        {
            listWithExportTemplateDef def = new listWithExportTemplateDef();
            def = new listWithExportTemplateDef(
                    "complianceAdjustmentSummary",
                    adjustment + " Adjustments (This QTR)",
                    "crm/api/dashboard/compliance/AdjustmentSummary/" + adjustment,
                    "crm/api/dashboard/ExportComplianceAdjustmentSummary",
                    new List<actionBtn>(),
                    new List<fieldFormat>()
                    {
                                new fieldFormat("MID","MID", null, "gotoMoneyTemplate"),
                                new fieldFormat("PID","PID",null,"gotoPeopleTemplate"),
                                new fieldFormat("FNAME","First Name"),
                                new fieldFormat("LNAME","Last Name"),
                                new fieldFormat("BATCHDTE","Batch Date", "date"),
                                new fieldFormat("BATCHNO","Batch No"),
                                new fieldFormat("ADJDTE","Adjustment Date", "date"),
                                new fieldFormat("ADJUSTMENT", "Adjustment"),
                                new fieldFormat("ADJAMT","Adjustment Amt", "money")
                    },
                    adjustment
                );
            return def;
        }

        #region [[[ emp/occup append ]]]
        [HttpGet, Route("crm/api/compliance/getEmpOccup")]
        public List<z_compl_EmpOccu> getEmpOccup(int topN)
        {
            List<z_compl_EmpOccu> result = new List<z_compl_EmpOccu>();
            if (!session.userSession.is_sysAdmin)
            {
                return result;
            }
            #region sql
            string sql = @"
SELECT TOP {0}
	A.PID, A.[Name], P.[Employer], P.[Occupation],

	--- Probably, no need to display (Begin) --------------
	'' AS [SourceDb],  
	0 AS [SourceDbPID],
	'' AS [SourceMRC],
	--- Probably, no need to display (End) --------------

	A.[sName],
	A.[sEmployer],
	A.[sOccupation],
	A.[CreatedAt],
    (SELECT COUNT(*) FROM z_compl_EmpOccu WHERE ISNULL(handle,0) = 0) AS totalCount
FROM [z_compl_EmpOccu] A
	INNER JOIN PEOPLE P ON A.PID=P.PID
WHERE 
	--||| Found a match from the batch process
	ISNULL(handle,0) = 0

	--||| Still no or invalid Emp/Occup.
	AND (
		( ISNULL(P.EMPLOYER,'')='' AND ISNULL(P.OCCUPATION,'')='' )
		OR ( ISNULL(P.EMPLOYER,'')='' AND P.OCCUPATION NOT IN ('Retired','Student','Homemaker','Entrepreneur') )
		OR ( ISNULL(P.OCCUPATION,'')='' AND P.EMPLOYER NOT IN ('Retired','Student','Homemaker','Entrepreneur') )
	)	";
            #endregion
            result = _entity_crm.getContext().Database.SqlQuery<z_compl_EmpOccu>(string.Format(sql, topN)).ToList();
            return result;
        }
        [HttpPost, Route("crm/api/compliance/EmpOccup/update")]
        public genericResponse updateEmpOccup(List<int> pids)
        {
            genericResponse response = new genericResponse();
            if (!session.userSession.is_sysAdmin || pids.Count == 0)
            {
                response.success = false;
                response.message = "Sorry, there is a problem processing.";
                return response;
            }
            #region sql
            string pids_to_update = string.Join(",", pids);
            string sql = @"
            BEGIN TRY
                BEGIN TRANSACTION;

                -- First update
                UPDATE P 
                SET 
                    P.EMPLOYER = Z.sEmployer,
                    P.OCCUPATION = Z.sOccupation,
                    P._updating_uid = {0}
                FROM PEOPLE P 
                INNER JOIN [z_compl_EmpOccu] Z ON P.PID = Z.PID
                WHERE P.PID IN ({1});

                -- Second update
                UPDATE Z 
                SET 
                    Z.handle = 3,
                    Z.handledAt = GETDATE()
                FROM [z_compl_EmpOccu] Z 
                WHERE Z.PID IN ({1});

                COMMIT;

                SELECT 1 AS success;
            END TRY
            BEGIN CATCH
                ROLLBACK;

                -- Return error information
                SELECT 
                    --ERROR_NUMBER() AS ErrorNumber,
                    --ERROR_MESSAGE() AS ErrorMessage,
                    0 AS success;
            END CATCH;
";
            #endregion
            int res = _entity_crm.getContext().Database.SqlQuery<int>(string.Format(sql, session.userSession.UserId_i, pids_to_update)).FirstOrDefault();
            if (res == 0) {
                response.success = false;
                response.message = "There is a problem processing, please try again later.";
            }
            else
            {
                response.success = true;
                response.message = "The selected records are updated successfully.";
            }
            return response;
        }
        [HttpPost, Route("crm/api/compliance/EmpOccup/ignore")]
        public genericResponse ignoreEmpOccup(List<int> pids)
        {
            genericResponse response = new genericResponse();
            if (!session.userSession.is_sysAdmin || pids.Count == 0)
            {
                response.success = false;
                response.message = "Sorry, there is a problem processing.";
                return response;
            }
            #region sql
            string pids_to_update = string.Join(",", pids);
            string sql = @"
            UPDATE Z 
            SET 
                Z.handle = 9,
                Z.handledAt = GETDATE()
            FROM [z_compl_EmpOccu] Z 
            WHERE Z.PID IN ({0});

            SELECT 1 AS success;
";
            #endregion
            int rowsAffected = _entity_crm.getContext().Database.ExecuteSqlCommand(string.Format(sql, pids_to_update));

            response.success = rowsAffected > 0;
            response.message = response.success
                ? "The selected records are updated successfully."
                : "There is a problem processing, please try again later.";
            
            return response;
        }
        #endregion
    }
}
