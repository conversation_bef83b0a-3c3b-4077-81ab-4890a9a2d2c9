﻿using System.Web.Http;

using Ninject;

using cmdiapp.n.core.Areas.legislator.Domain.Services;
using cmdiapp.n.core.Areas.legislator.Domain.Models;
using cmdiapp.n.core.Core.App_Start;
using cmdiapp.n.core.Areas.crm.Domain.Models;
using cmdiapp.n.core.Domain.ViewModels;
using System.Collections.Generic;
using cmdiapp.n.core.Areas.crm.ViewModels;

namespace cmdiapp.n.core.Areas.legislator.Presentation.api
{
    [RoutePrefix("legislator/api/Legislator")]
    public class LegislatorController : ApiController
    {
        private ILegislatorService _service;
        private IContactService _contactService;

        public LegislatorController()
        {
            _service = NinjectMVC.kernel.Get<ILegislatorService>();
            _contactService = NinjectMVC.kernel.Get<IContactService>();
        }

        [HttpGet, Route("{id}")]
        [Authorize]
        public Legislator Get(string id)
        {
            return _service.GetLegislator(id);
        }

        [HttpGet, Route("{id}/SocialMedia")]
        [Authorize]
        public LegislatorSocialMediaAccount GetSocialMedia(string id)
        {
            return _service.GetSocialMedia(id);
        }

        [HttpGet, Route("{id}/CommitteeMemberships")]
        [Authorize]
        public CommitteeMembership[] GetCommitteMemberships(string id)
        {
            return _service.GetCommitteeMemberships(id);
        }

        [HttpGet, Route("{id}/Photo")]
        [Authorize]
        public LegislatorPhoto GetPhoto(string id)
        {
            return _service.GetPhoto(id);
        }

        [HttpGet, Route("{id}/CrmLegislatorInfo")]
        [Authorize]
        public LegislatorInfo GetCrmLegislatorInfo(string id)
        {
            return _service.GetCrmLegislatorInfo(id);
        }

        [HttpPost, Route("SaveCrmLegislatorInfo")]
        [Authorize]
        public genericResponse SaveCrmLegislatorInfo(LegislatorInfo info)
        {
            if (!_service.MemberTrackingIsEnabled())
            {
                return new genericResponse
                {
                    success = false,
                    message = "Member Tracking is not enabled for this project."
                };
            }
            return _service.SaveCrmLegislatorInfo(info);
        }

        [HttpGet, Route("{id}/ContributionTotals")]
        [Authorize]
        public List<LegislatorDirectContributionTotal> GetLegislatorDirectContributionTotal(string id)
        {
            return _service.GetLegislatorDirectContributionTotals(id);
        }

        [HttpGet, Route("TxnDetails/{entityId}/{electionCodeId}/{electionYear}")]
        [Authorize]
        public List<Txn> GetTxnDetails(int entityId, int electionCodeId, string electionYear)
        {
            return _service.GetTxnDetails(entityId, electionCodeId, electionYear);
        }

        [HttpGet, Route("{id}/PersonalContributionTotals")]
        [Authorize]
        public List<LegislatorContributionTotal> GetLegislatorPersonalContributionTotals(string id)
        {
            return _service.GetLegislatorPersonalContributionTotals(id);
        }

        [HttpGet, Route("PersonalContribs/{entityId}/{year}")]
        [Authorize]
        public List<PersonalContribLegislatorDisplay> GetPersonalContribs(int entityId, int year)
        {
            return _service.GetPersonalContribs(entityId, year);
        }

        [HttpGet, Route("{id}/Contacts")]
        [Authorize]
        public List<Domain.Models.Contact> GetContacts(string id, bool includeDeleted = false)
        {
            return _contactService.GetByLegislator(id, includeDeleted);
        }

        [HttpGet, Route("WithContactChanges/{searchText}")]
        public List<LegislatorAutocompleteDisplay> GetLegislatorsWithContactChanges(string searchText, int take = 10)
        {
            return _service.GetLegislatorsWithContactChanges(searchText, take);
        }
    }
}