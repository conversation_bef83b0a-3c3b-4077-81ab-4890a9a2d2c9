﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Data;
using System.Linq;
using System.Web.Mvc;
using System.Data.Linq;
using System.ComponentModel.DataAnnotations.Schema;
using cmdiapp.n.core.Areas.crm.Domain.Data;

using cmdiapp.n.core.Domain.ViewModels;

namespace cmdiapp.n.core.Areas.crm.Domain.Models
{   
    [Table("x_ms_org")]
    public class x_ms_org : _entityBase_crm, iItemType
    {
        [Column]
        [Key]
        public int Id { get; set; }

        [Column]
        public string name { get; set; }
        [Column]
        public string addr1 { get; set; }
        [Column]
        public string addr2 { get; set; }
        [Column]
        public string city { get; set; }
        [Column]
        public string state { get; set; }
        [Column]
        public string zip { get; set; }

        [Column]
        public string headerLogoUrl { get; set; }

        [Column]
        public string footer { get; set; }

    }
    public class x_ms_org_ext 
    {
        public int Id { get; set; }
        public string name { get; set; }
        public string addr1 { get; set; }
        public string addr2 { get; set; }
        public string city { get; set; }
        public string state { get; set; }
        public string zip { get; set; }
        public string headerLogoUrl { get; set; }
        public string footer { get; set; }
        public int count_ { get; set; }
        public Int64 rowNo { get; set; }
    }


    [Table("x_ms_template")]
    public class x_ms_template : _entityBase_crm, iItemType
    {
        [Column]
        [Key]
        public int Id { get; set; }

        [Column]
        public string name { get; set; }

        [Column]
        public string body { get; set; }

        [Column]
        public DateTime? createdAt { get; set; }

        [Column]
        public DateTime? updatedAt { get; set; }

    }
    public class x_ms_template_ext
    {
        public int Id { get; set; }
        public string name { get; set; }
        public string body { get; set; }
        public DateTime? createdAt { get; set; }
        public DateTime? updatedAt { get; set; }
        public int count_ { get; set; }
        public Int64 rowNo { get; set; }
    }
    [Table("x_ms_job")]
    public class x_ms_job : _entityBase_crm, iItemType
    {
        [Column]
        [Key]
        public int Id { get; set; }

        [Column]
        public string name { get; set; }

        [Column]
        public string username { get; set; }

        [Column]
        public DateTime? sendDate { get; set; }

        [Column]
        public DateTime? createdAt { get; set; }
        [Column]
        public DateTime? pickedupAt { get; set; }
        [Column]
        public DateTime? completedAt { get; set; }
        [Column]
        public string sql { get; set; }
        [Column]
        public int orgId { get; set; }
        [Column]
        public int tempId { get; set; }
        [Column]
        public int use_type { get; set; }
        [Column]
        public int mail_type { get; set; }
        [Column]
        public bool double_sided { get; set; }
        [Column]
        public bool return_envelope { get; set; }
        [Column]
        public bool color { get; set; }

    }
    
    public class queueLetterInfo
    {
        public string name { get; set; }
        public DateTime sendDate { get; set; }
        public int orgId { get; set; }
        public int tempId { get; set; }
        public string sqlKey { get; set; }
        public int use_type { get; set; }
        public int mail_type { get; set; }
        public bool double_sided { get; set; }
        public bool return_envelope { get; set; }
        public bool color { get; set; }
    }
}