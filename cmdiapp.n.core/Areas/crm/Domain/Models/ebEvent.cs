﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Data;
using System.Linq;
using System.Web.Mvc;
using System.Data.Linq;
using System.ComponentModel.DataAnnotations.Schema;

using cmdiapp.n.core.Domain.ViewModels;
using cmdiapp.n.core.Areas.crm.Domain.Data;

namespace cmdiapp.n.core.Areas.crm.Domain.Models
{
    [Table("x_eb_event")]
    public class ebEvent : _entityBase_crm
    {
        [Key]
        [Column]
        public Int64 id { get; set; }

        [Column]
        public string title { get; set; }
        public string description { get; set; }
        public string url { get; set; }

        public DateTime? start_date { get; set; }
        public DateTime? end_date { get; set; }   

        public string timezone { get; set; }
        public string status { get; set; }

        public string venue_name { get; set; }
        public string venue_address { get; set; }
        public string venue_address_2 { get; set; }
        public string venue_city { get; set; }
        public string venue_region { get; set; }
        public string venue_postal_code { get; set; }
        public string venue_country { get; set; }
        public string venue_country_code { get; set; }
        public decimal? venue_latitude { get; set; }
        public decimal? venue_longitude { get; set; }

        public int quantity_sold { get; set; }

        public DateTime? created { get; set; }   
        public DateTime? modified { get; set; }

        public int? SPCEVNTID { get; set; }

    }

    [Table("queue_eventAttendee")]
    public class queue_eventAttendee : _entityBase_crm
    {
        [Key]
        [Column]
        public int id { get; set; }

        [Column]
        public int SPCEVNTID { get; set; }

        // Need to add complete fields when needed
    }
}
