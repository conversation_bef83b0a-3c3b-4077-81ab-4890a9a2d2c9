﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Data.Linq;
using cmdiapp.n.core.Areas.crm.Domain.Models;

namespace cmdiapp.n.core.Areas.crm.Domain.Repositories
{
    internal class programRepository : _baseRepository<PROGRAM>, IprogramRepository
    {
        public programRepository(I__DBFactory databaseFactory)
            : base(databaseFactory)
        {

        }
    }

    internal class SubprogramRepository : _baseRepository<dtPROG>, ISubprogramRepository
    {
        public SubprogramRepository(I__DBFactory databaseFactory) : base(databaseFactory)
        {

        }
    }

    
}
