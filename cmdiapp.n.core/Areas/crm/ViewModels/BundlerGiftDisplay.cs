﻿using cmdiapp.n.core.Domain.ViewModels;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace cmdiapp.n.core.Areas.crm.ViewModels
{
    public class BundlerGiftDisplay : iItemType
    {
        // abbreviated class for displaying and exporting gifts
        // from Gift tab in bundler details page

        public int? TRACKNO { get; set; }
        public int MID { get; set; }
        public int PID { get; set; }
        public string FNAME { get; set; }
        public string LNAME { get; set; }
        public string BATCHNO { get; set; }
        public DateTime? BATCHDTE { get; set; }
        public decimal? AMT { get; set; }
        public string FUNDCODE { get; set; }
        public string FUNDDESC { get; set; }
        public string SRCECODE { get; set; }
        public string SRCEDESC { get; set; }
        public string PKGECODE { get; set; }
        public string PKGEDESC { get; set; }
        public string PAYMETHOD { get; set; }
    }
}