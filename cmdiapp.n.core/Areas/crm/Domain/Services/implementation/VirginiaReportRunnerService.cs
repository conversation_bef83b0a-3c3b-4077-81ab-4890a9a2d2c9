﻿using cmdiapp.n.core._Domain.ViewModels;
using cmdiapp.n.core.Areas.crm.Domain.Data;
using cmdiapp.n.core.Areas.crm.Domain.Models;
using cmdiapp.n.core.Library;
using System;
using System.Data.Common;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace cmdiapp.n.core.Areas.crm.Domain.Services.implementation
{
    public class VirginiaReportRunnerService : IVirginiaReportRunnerService
    {
        private readonly I_entity_crm _entity;
        private readonly IErrorLogger _errorLogger;

        public VirginiaReportRunnerService(I_entity_crm entity, IErrorLogger errorLogger)
        {
            _entity = entity;
            _errorLogger = errorLogger;
        }

        public async Task<GenericSuccessResponse<VirginiaReportOutput>> RunReportAsync(
            VirginiaReportParameters parameters,
            CancellationToken cancellationToken = default)
        {
            try
            {
                var validationResult = ValidateParameters(parameters);
                if (!(validationResult?.IsValid ?? false))
                {
                    return new GenericSuccessResponse<VirginiaReportOutput>
                    {
                        Success = false,
                        Message = validationResult?.ErrorMessage ?? "Input parameters are invalid."
                    };
                }

                using (var connection = _entity.GetConnection())
                using (var command = GetCommand(connection, parameters))
                {
                    await connection.OpenAsync(cancellationToken);
                    string xmlString = (string)await command.ExecuteScalarAsync(cancellationToken);
                    connection.Close();
                    return new GenericSuccessResponse<VirginiaReportOutput>
                    {
                        Success = true,
                        Results = new VirginiaReportOutput
                        {
                            XmlString = xmlString
                        }
                    };
                }
                
            }
            catch (Exception ex)
            {
                _errorLogger.Log($"Encountered when running Virginia report: {ex.GetType().Name}: {ex.Message}");
                return new GenericSuccessResponse<VirginiaReportOutput>
                {
                    Success = false,
                    Message = "An error occurred when running report."
#if DEBUG
                    ,MessageKey = $"{ex.GetType().Name}: {ex.Message}"
#endif
                };
            }
        }
        public string ValidateReportParameters(VirginiaReportParameters parameters)
        {
            var res = ValidateParameters(parameters);
            if (res.IsValid) return "";
            else return res.ErrorMessage;
        }
        private ParameterValidationResult ValidateParameters(VirginiaReportParameters parameters)
        {
            if (parameters == null)
            {
                return new ParameterValidationResult
                {
                    IsValid = false,
                    ErrorMessage = "No parameters were provided."
                };
            }

            if (!(parameters.FundIds?.Any() ?? false))
            {
                return new ParameterValidationResult
                {
                    IsValid = false,
                    ErrorMessage = "Must have at least one Fund Id."
                };
            }

            if (parameters.ReportEnd < parameters.ReportStart)
            {
                return new ParameterValidationResult
                {
                    IsValid = false,
                    ErrorMessage = $"Report End ({parameters.ReportEnd}) cannot be earlier " +
                        $"than Report Start ({parameters.ReportStart})."
                };
            }

            return new ParameterValidationResult
            {
                IsValid = true
            };
        }

        private DbCommand GetCommand(DbConnection connection, VirginiaReportParameters parameters)
        {
            var command = connection.CreateCommand();
            command.CommandType = System.Data.CommandType.StoredProcedure;            
            command.CommandText = $"x_va_generateVirginiaReport_v2";

            AddParameter(command, "@fundIds", string.Join(",", parameters.FundIds));
            AddParameter(command, "@filerId", parameters.FilerId);
            AddParameter(command, "@reportStart", parameters.ReportStart);
            AddParameter(command, "@reportEnd", parameters.ReportEnd);
            AddParameter(command, "@filingDate", parameters.FilingDate);
            AddParameter(command, "@reportTypeId", parameters.ReportTypeId);
            AddParameter(command, "@isAmendment", parameters.IsAmendment);
            AddParameter(command, "@amendmentNumber", parameters.AmendmentNumber);
            AddParameter(command, "@saveThisReport", parameters.SaveThisReport);

            return command;
        }

        private void AddParameter(DbCommand command, string name, object value)
        {
            var parameter = command.CreateParameter();
            parameter.ParameterName = name;
            parameter.Value = value;
            command.Parameters.Add(parameter);
        }

        private class ParameterValidationResult
        {
            public bool IsValid { get; set; }
            public string ErrorMessage { get; set; }
        }
    }
}