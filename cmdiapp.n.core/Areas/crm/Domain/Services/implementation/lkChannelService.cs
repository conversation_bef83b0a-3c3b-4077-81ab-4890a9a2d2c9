﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

using Ninject;
using Ninject.Web.Mvc;

using cmdiapp.n.core.Core.App_Start;
using cmdiapp.n.core.Areas.crm.Domain.Models;
using cmdiapp.n.core.Areas.crm.Domain.ViewModels;
using cmdiapp.n.core.Areas.crm.Domain.Repositories;


namespace cmdiapp.n.core.Areas.crm.Domain.Services
{
    public class lkChannelService : IlkChannelService
    {
        private I___unitOfWork _unitOfWork;
        private I__DBFactory _dbFactory;
        private IlkChannelRepository _lkChannelRepository;

        public lkChannelService(I___unitOfWork unitOfWork, I__DBFactory dbFactory, IlkChannelRepository lkChannelRepository)
        {
            _unitOfWork = unitOfWork;
            _dbFactory = dbFactory;
            _lkChannelRepository = lkChannelRepository;
        }

        //Read all Channels
        public List<lkChannel> get_all_channels()
        {
            var _allRecords = _lkChannelRepository.All();
            //return data
            return _allRecords.OrderBy(p => p.channelId).ToList();
        }

        public List<lkChannel_ext> get_all_channel_exts(string where)
        {
            string sql = (!string.IsNullOrEmpty(where) ? string.Format("SELECT * FROM lkChannel WHERE {0}", where) : "SELECT * FROM lkChannel");

            var q = _dbFactory.getContext().Database.SqlQuery<lkChannel_ext>(sql);

            return q.ToList();
        }
        public bool check_unqiueness_channel(lkChannel _channel)
        {
            // take care of apostrophe
            string v = _channel.channel.Replace("'", "''");
            string sql = "";

            if (_channel.channelId > 0)   // Existing Record
                sql = String.Format("SELECT Count(*) as recordCount FROM lkChannel WHERE channel = '{0}' AND channelId <> {1}", v, _channel.channelId);
            else
                sql = String.Format("SELECT Count(*) as recordCount FROM lkChannel WHERE channel = '{0}'", v);

            int isZero = _dbFactory.getContext().Database.SqlQuery<int>(sql).FirstOrDefault();
            return (isZero == 0) ? true : false;
        }
        public bool check_unqiueness_descrip(lkChannel _channel)
        {
            // take care of apostrophe
            string v = _channel.descrip.Replace("'", "''");
            string sql = "";

            if (_channel.channelId > 0)   // Existing Record
                sql = String.Format("SELECT Count(*) as recordCount FROM lkChannel WHERE descrip = '{0}' AND channelId <> {1}", v, _channel.channelId);
            else
                sql = String.Format("SELECT Count(*) as recordCount FROM lkChannel WHERE descrip = '{0}'", v);

            int isZero = _dbFactory.getContext().Database.SqlQuery<int>(sql).FirstOrDefault();
            return (isZero == 0) ? true : false;
        }
        public void Update(lkChannel _lkChannel)
        {
            _lkChannelRepository.Update(_lkChannel);
            _unitOfWork.commit();
        }
        public void Add(lkChannel _lkChannel)
        {
            _lkChannelRepository.Add(_lkChannel);
            _unitOfWork.commit();
        }
        public void Delete(lkChannel _lkChannel)
        {
            _lkChannelRepository.Delete(_lkChannel);
            _unitOfWork.commit();
        }
        public lkChannel get_channel(int channelId)
        {
            string sql = string.Format("SELECT * FROM lkChannel WHERE channelId={0}", channelId);
            var q = _dbFactory.getContext().Database.SqlQuery<lkChannel>(sql);
            return q.FirstOrDefault();
        }
        public bool check_record_linked(int channelId)
        {
            string sql = string.Format("SELECT Count(*) FROM MONY WHERE channelId={0}", channelId);
            var q = _dbFactory.getContext().Database.SqlQuery<int>(sql).FirstOrDefault();
            return q > 0;
        }
        
    }

    public class lkTargetChannelService : IlkTargetChannelService
    {
        private I___unitOfWork _unitOfWork;
        private I__DBFactory _dbFactory;
        private IlkTargetChannelRepository _lkTargetChannelRepository;

        public lkTargetChannelService(I___unitOfWork unitOfWork, I__DBFactory dbFactory, IlkTargetChannelRepository lkTargetChannelRepository)
        {
            _unitOfWork = unitOfWork;
            _dbFactory = dbFactory;
            _lkTargetChannelRepository = lkTargetChannelRepository;
        }

        //Read all Target Channels
        public List<lkTargetChannel> get_all_targetchannels()
        {
            var _allRecords = _lkTargetChannelRepository.All();
            //return data
            return _allRecords.OrderBy(p => p.channelId).ToList();
        }

    }

}
