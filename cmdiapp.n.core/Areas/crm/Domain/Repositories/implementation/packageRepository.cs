﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Data.Linq;
using cmdiapp.n.core.Areas.crm.Domain.Models;

namespace cmdiapp.n.core.Areas.crm.Domain.Repositories
{
    internal class packageRepository : _baseRepository<PACKAGE>, IpackageRepository
    {
        public packageRepository(I__DBFactory databaseFactory) : base(databaseFactory)
        {

        }
    }

    internal class initiativeTypeRepository : _baseRepository<lkInitiativeType>, IinitiativeTypeRepository
    {
        public initiativeTypeRepository(I__DBFactory databaseFactory) : base(databaseFactory)
        {

        }
    }
        
}