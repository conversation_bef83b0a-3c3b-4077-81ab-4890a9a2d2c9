﻿using cmdiapp.n.core.Areas.query.Domain.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.Serialization;
using System.Web;

namespace cmdiapp.n.core.Areas.datax.Domain.Models.ViewModels
{
    /// <summary>
    /// Class for POSTing required data for creating a Cremir email list
    /// </summary>
    [DataContract]
    public class CreateCremirEmailListInfo
    {
        /// <summary>
        /// Gets or sets the email list to create
        /// </summary>
        [DataMember(Name = "emailList")]
        public CremirEmailList EmailList { get; set; }
        /// <summary>
        /// Gets or sets the query instance that generates the SQL
        /// for selecting records of the list
        /// </summary>
        [DataMember(Name = "queryInstance")]
        public QueryInstanceInfo QueryInstance { get; set; }

    }
}