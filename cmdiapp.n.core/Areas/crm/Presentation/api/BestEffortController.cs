﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Net;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Web.Http;
using System.Web.Script.Serialization;
using System.Drawing;
using System.IO;
using System.Runtime.Serialization.Json;

using Ninject;
using Ninject.Web.Mvc;

using AutoMapper;

using cmdiapp.n.core.Core;
using cmdiapp.n.core.Core.App_Start;
using cmdiapp.n.core.Library;
using cmdiapp.n.core.Domain.ViewModels;
using cmdiapp.n.core.Presentation.Controllers;
using cmdiapp.n.core.Areas.crm.Core;
using cmdiapp.n.core.Areas.crm.Domain.Models;
using cmdiapp.n.core.Areas.crm.Domain.Data;
using cmdiapp.n.core.Areas.crm.Domain.Services;
using cmdiapp.n.core.Areas.query.Domain.Models;
using cmdiapp.n.core.Areas.query;
using cmdiapp.n.core.Areas.dataService.Domain.Data;
using cmdiapp.n.core.Areas.dataService.Domain.Models;
using cmdiapp.n.core.Areas.dataService.Domain.Services;

namespace cmdiapp.n.core.Areas.crm.Presentation.Controllers.api
{
    ///  (controller) Best Effort

    [Authorize]
    [apiAuthorize(AccessElement = "cmdiapp.dms.BestEfforts", AccessLevel = "v")]
    public class BestEffortController : ApiController
    {
        #region [[ Declaration ]]
        private I_entity_crm _entity_crm;
        private IdataService _dataService;

        // used by new Crimson
        public class BestEffortParm {
            public QueryInstanceInfo qinstance { get; set; } 
            public bool markRec { get; set; }
            public bool createNote { get; set; }
            public string contactNote { get; set; }
            public string contactDate { get; set; }
        }
        #endregion

        #region [[ (constructor) BestEffortController ]]
        public BestEffortController()
        {
            _entity_crm = I_entityManager_crm.getEntity();
            _dataService = I_entityManager_ds.getService();
        }
        #endregion


        #region [[ sub routines for Search & Export ]]

        #region [ (private-List<BestEffort_ext1>) Quick Text Search ]
        /*
        private List<BestEffort_ext1> quickTextSearch(string searchText, int pageSize, int pageNo, string sortOptions, string qDefName = "crm_bestEffort")
        {

            string _where = "";

            #region [[ Search by ID if numeric ]]
            int id;
            if (int.TryParse(searchText, out id))
            {
                _where = string.Format("M.PID = {0} OR M.MID = {0}", id);
            }
            #endregion

            #region [[ Otherwise, Search by String ]]
            else if (!string.IsNullOrEmpty(searchText))
            {

                #region [ Split Search Text > string1, string2 & string3 and create "Where" statement ]
                char[] delimiterChars = { ' ', ',', '.', ';', ':', '|', '\t' };

                string[] words = searchText.Split(delimiterChars);

                if (words.Count() > 0)
                {
                    string string1 = words[0];
                    string string2 = (words.Count() > 1 ? words[1] : "");
                    string string3 = (words.Count() > 2 ? words[2] : "");

                    string _FNAME_where = "";

                    #region [ Where Clause ]

                    _FNAME_where = q_library.get_normFNAMEwhere("FNAME", string1, _dataService).Replace("'", "'");

                    switch (words.Count())
                    {
                        case 1: // Gift ID or First or Last Name
                            _where = string.Format(_FNAME_where + " OR LNAME LIKE '{0}%'", string1);
                            break;

                        case 2: // First and Last Name  OR First OR Last OR Gift ID
                            _where = string.Format("(" + _FNAME_where + " AND LNAME LIKE '{0}%') OR FNAME LIKE '{1}%' OR LNAME LIKE '{1}%'", string2, searchText);
                            break;

                        case 3: // First, Middle and Last Name  OR Last OR Gift ID
                            _where = string.Format("(" + _FNAME_where + " AND MNAME LIKE '{0}%' AND LNAME LIKE '{1}%') OR FNAME LIKE '{2}%' OR LNAME LIKE '{2}%'", string2, string3, searchText);
                            break;
                        default:
                            break;
                    }
                    #endregion

                }
                else
                    return new List<BestEffort_ext1>();
                #endregion

            }
            #endregion

            // No search text is given, return an empty dataset
            else
                return new List<BestEffort_ext1>();

            #region [[ Run a Query ]]
            if (string.IsNullOrEmpty(sortOptions)) sortOptions = "LNAME, FNAME";

            // SQL Definition
            q_def_S _def = q_library.get_q_def_fr_cache(qDefName);

            // Compose SQL
            string sql = string.Format(@"
                            SELECT DISTINCT 
                            {0}
                            FROM {1}
                            WHERE {2} AND {3}
                        ", _def.sq_fieldsV, _def.sq_from, _where, sql_exclude);

            sql = sql.Trim().Replace("'", "''");

            var q = _entity_crm.getContext().Database.SqlQuery<BestEffort_ext1>(String.Format("EXEC dbo.get_pagedDataSet '{0}', '{1}', {2}, {3}", sql, sortOptions, pageSize, pageNo));

            List<BestEffort_ext1> result = q.ToList();

            cl_query.create_a_sqlLog(
                        sql.Trim(),
                        "crm/api/BestEffort/Search(Adv)",
                        util.serialize_toXmlString(_where),
                        crmSession.UID().Value,
                        (result != null && result.Count() > 0 ? result.FirstOrDefault().count_ : 0),
                        "Best Effort - View Columns",
                        "",
                        _entity_crm);

            return result;
            #endregion
        }
        */
        #endregion

        #region [ (private) Advanced Search - SQL Filtering ]

        #region [ func.string.get_sqlFIELDs ] Enable optional OUTPUT fields if used.
        private string get_sqlFIELDs(string p_sql_where, string p_default_fields)
        {
            return p_default_fields;
        }
        #endregion

        #endregion

        #region [ (private-List<BestEffort_ext1>) Advanced Search ]
        private List<BestEffort_ext1> sqlSearch(List<searchItem> filters, int pageSize, int pageNo, string sortOptions, string qDefName = "crm_bestEffort")
        {
            if (string.IsNullOrEmpty(sortOptions)) sortOptions = "LNAME, FNAME";

            // SQL Definition
            q_def_S _def = q_library.get_q_def_fr_cache(qDefName);

            // Compose SQL
            string sql = q_library.getSQL(_def, filters, session.currentDomain_project._connectionString(), null, get_sqlFIELDs, true);

            string _fundcode = sql.Substring(sql.IndexOf("F2.FUNDCODE"), sql.Length - sql.IndexOf("F2.FUNDCODE"));

            sql = sql.Substring(0, sql.IndexOf("F2.FUNDCODE")) + String.Format("S.CTDAMT > 200 AND dbo.oValidOCCEMPv2(P.OCCUPATION, P.EMPLOYER) = 0", _fundcode);

            sql = sql.Replace("[[FUNDCODES]]", _fundcode.Replace("F2.FUNDCODE", "F.FUNDCODE"));

            sql = sql.Replace("[[FUNDCODES2]]", _fundcode);

            sql = sql.Replace("[[FUNDCODES3]]", _fundcode.Replace("F2.FUNDCODE", "F3.FUNDCODE"));

            sql = sql.Replace("[[FUNDCODES4]]", _fundcode.Replace("F2.FUNDCODE", "F4.FUNDCODE"));

            string sqlParam = sql.Trim().Replace("'", "''");
            System.Diagnostics.Stopwatch stopWatch = new System.Diagnostics.Stopwatch();
            stopWatch.Start();
            var q = _entity_crm.getContext().Database.SqlQuery<BestEffort_ext1>(String.Format("EXEC dbo.get_pagedDataSet '{0}', '{1}', {2}, {3}", sqlParam, sortOptions, pageSize, pageNo));

            List<BestEffort_ext1> result = q.ToList();
            stopWatch.Stop();

            cl_query.create_a_sqlLog(
                        sql.Trim(),
                        "crm/api/BestEffort/Search(Adv)",
                        util.serialize_toXmlString(filters),
                        crmSession.UID().Value,
                        (result != null && result.Count() > 0 ? result.FirstOrDefault().count_ : 0),
                        "Best Effort Search - View Columns",
                        "",
                        _entity_crm,
             Convert.ToSingle(stopWatch.ElapsedMilliseconds / 1000.0));

            return result;
        }
        #endregion

        #region [ (private-System.Data.DataTable) Advanced Search - Export ]

        //private System.Data.DataTable sqlSearch_forExport__dataset(List<searchItem> filters)
        private System.Data.DataSet sqlSearch_forExport__dataset(List<searchItem> filters)
        {

            // SQL Definition
            q_def_S _def = q_library.get_q_def_fr_cache("crm_bestEffort");

            // Compose SQL
            string sql = q_library.getSQL(_def, filters, session.currentDomain_project._connectionString(), null, get_sqlFIELDs, false);

            string _fundcode = sql.Substring(sql.IndexOf("F2.FUNDCODE"), sql.Length - sql.IndexOf("F2.FUNDCODE"));

            sql = sql.Substring(0, sql.IndexOf("F2.FUNDCODE")) + String.Format("S.CTDAMT > 200 AND dbo.oValidOCCEMPv2(P.OCCUPATION, P.EMPLOYER) = 0", _fundcode);

            sql = sql.Replace("[[FUNDCODES]]", _fundcode.Replace("F2.FUNDCODE", "F.FUNDCODE"));

            sql = sql.Replace("[[FUNDCODES2]]", _fundcode);

            sql = sql.Replace("[[FUNDCODES3]]", _fundcode.Replace("F2.FUNDCODE", "F3.FUNDCODE"));

            sql = sql.Replace("[[FUNDCODES4]]", _fundcode.Replace("F2.FUNDCODE", "F4.FUNDCODE"));

            //sql = sql.Replace("SELECT DISTINCT", "SELECT DISTINCT TOP 100000"); // ############ LIMIT TO 100K FOR NOW)

            System.Diagnostics.Stopwatch stopWatch = new System.Diagnostics.Stopwatch();
            stopWatch.Start();
            System.Data.DataSet _dataSet = q_library.get_dataset_w_sql__single(session.currentDomain_project._connectionString(), sql, "result", 0, 0);
            stopWatch.Stop();

            cl_query.create_a_sqlLog(
            sql.Trim(),
            "crm/api/BestEffort/Export",
            util.serialize_toXmlString(filters),
            crmSession.UID().Value,
            (_dataSet != null && _dataSet.Tables.Count > 0 && _dataSet.Tables[0].Rows.Count > 0 ? _dataSet.Tables[0].Rows.Count : 0),
            "Best Effort Search - Export Columns",
            "HTTP",     // Saved Path: HTTP Flush 
            _entity_crm,
             Convert.ToSingle(stopWatch.ElapsedMilliseconds / 1000.0));

            //return _dataSet.Tables[0];
            return _dataSet;
        }

        /*
        private List<BestEffort_ext1> sqlSearch_forExport__list(List<searchItem> filters)
        {
            // SQL Definition
            q_def_S _def = q_library.get_q_def_fr_cache("crm_bestEffort");

            // Compose SQL
            string sql = q_library.getSQL(_def, filters, session.currentDomain_project._connectionString(), null, get_sqlFIELDs, false);

            // Exclude records already in BESTEFRT table
            sql += "AND " + sql_exclude;

            //sql = sql.Replace("SELECT DISTINCT", "SELECT DISTINCT TOP 100000"); // ############ LIMIT TO 100K FOR NOW)

            var q = _entity_crm.getContext().Database.SqlQuery<BestEffort_ext1>(String.Format("{0}", sql));
            return q.ToList();
        }
        */
        #endregion


        #endregion

        #region [[ Mark Records ]]
        private int sqlSearch_markRec(List<searchItem> filters)
        {
            // SQL Definition
            q_def_S _def = q_library.get_q_def_fr_cache("crm_bestEffort");

            // Compose SQL
            string sql = q_library.getSQL(_def, filters, session.currentDomain_project._connectionString(), null, get_sqlFIELDs, false);

            string _fundcode = sql.Substring(sql.IndexOf("F2.FUNDCODE"), sql.Length - sql.IndexOf("F2.FUNDCODE"));

            sql = sql.Substring(0, sql.IndexOf("F2.FUNDCODE")) + String.Format("S.CTDAMT > 200 AND dbo.oValidOCCEMPv2(P.OCCUPATION, P.EMPLOYER) = 0", _fundcode);

            sql = sql.Replace("[[FUNDCODES]]", _fundcode.Replace("F2.FUNDCODE", "F.FUNDCODE"));

            sql = sql.Replace("[[FUNDCODES2]]", _fundcode);

            sql = sql.Replace("[[FUNDCODES3]]", _fundcode.Replace("F2.FUNDCODE", "F3.FUNDCODE"));

            sql = sql.Replace("[[FUNDCODES4]]", _fundcode.Replace("F2.FUNDCODE", "F4.FUNDCODE"));

            //sql = sql.Replace("SELECT DISTINCT", "SELECT DISTINCT TOP 100000"); // ############ LIMIT TO 100K FOR NOW)

            string mark_query = String.Format(@" 
                                        INSERT INTO BESTEFRT (PID, MID, REQDTE)
                                        SELECT ib.PID, ib.MID, CONVERT(VARCHAR(10),GETDATE(),101)
                                        FROM
                                            ( {0} ) 
                                            ia, MONY ib, dmFUND ic 
                                        WHERE ia.PID = ib.PID AND ib.COUNTER = 1 AND
                                        ib.FUNDID = ic.FUNDID AND {1} AND
                                        ib.MID NOT IN ( SELECT MID FROM BESTEFRT) 
                                        SELECT @@rowcount as ROWSCT 
                                      ", sql, _fundcode.Replace("F2.FUNDCODE","ic.FUNDCODE"));

            int count = _entity_crm.getContext().Database.SqlQuery<int>(String.Format("{0}", mark_query)).FirstOrDefault();
            return count; 
        }
        #endregion

        #region [[ Create Note ]]
        private int sqlSearch_createNote(List<searchItem> filters, string contactNote, string contactDate)
        {
            // SQL Definition
            q_def_S _def = q_library.get_q_def_fr_cache("crm_bestEffort");

            // Compose SQL
            string sql = q_library.getSQL(_def, filters, session.currentDomain_project._connectionString(), null, get_sqlFIELDs, false);

            string _fundcode = sql.Substring(sql.IndexOf("F2.FUNDCODE"), sql.Length - sql.IndexOf("F2.FUNDCODE"));

            sql = sql.Substring(0, sql.IndexOf("F2.FUNDCODE")) + String.Format("S.CTDAMT > 200 AND dbo.oValidOCCEMPv2(P.OCCUPATION, P.EMPLOYER) = 0", _fundcode);

            sql = sql.Replace("[[FUNDCODES]]", _fundcode.Replace("F2.FUNDCODE", "F.FUNDCODE"));

            sql = sql.Replace("[[FUNDCODES2]]", _fundcode);

            sql = sql.Replace("[[FUNDCODES3]]", _fundcode.Replace("F2.FUNDCODE", "F3.FUNDCODE"));

            sql = sql.Replace("[[FUNDCODES4]]", _fundcode.Replace("F2.FUNDCODE", "F4.FUNDCODE"));

            //sql = sql.Replace("SELECT DISTINCT", "SELECT DISTINCT TOP 100000"); // ############ LIMIT TO 100K FOR NOW)

            string insert_cmgmt_query =
            @" INSERT INTO ACTHIST (PID, HISTTYPEID, HISTDATE,SUBJECT, NOTE, UID) ";

            string subject = "Best Efforts";

            insert_cmgmt_query = string.Format(@" {0} 
            SELECT a.PID,1,'{1}', '{2}' ,'{3}' ,{4}
            FROM ( {5} ) a ",
                insert_cmgmt_query,                              //{0}
                contactDate,                                     //{1}
                subject,                                         //{2}
                contactNote,                                     //{3}
                crmSession.UID().Value,                          //{4}
                sql);                                            //{5} 

            insert_cmgmt_query += "; SELECT @@rowcount as ROWSCT";
            int count = _entity_crm.getContext().Database.SqlQuery<int>(String.Format("{0}", insert_cmgmt_query)).FirstOrDefault();
            return count;
        }
        #endregion

        /*
        #region [[ (genericResponse) Search[GET] - crm/api/BestEffort/Search ]]
        [HttpGet, Route("crm/api/BestEffort/Search")]
        [apiAuthorize(AccessElement = "cmdiapp.dms.BestEfforts", AccessLevel = "v")]
        public genericResponse Search(string searchText, int? page, int? pageSize, string qDefName = "crm_bestEffort")
        {
            #region [ Retrieve Input ]
            string _searchText = Library.util.kill_sqlBlacklistWord((searchText ?? "").Trim().Replace("'", "''''"));

            if (string.IsNullOrEmpty(_searchText))
                return new genericResponse() { __count = 0, success = true };

            int _pageSize = (pageSize == null || pageSize.Value == 0 ? 10 : pageSize.Value);
            int _pageNo = (page == null || page.Value == 0 ? 1 : page.Value);

            string sortField = HttpContext.Current.Request.QueryString["sort[0][field]"];
            string sortDir = HttpContext.Current.Request.QueryString["sort[0][dir]"];
            string _sortOptions = "";
            if (!string.IsNullOrEmpty(sortField))
                _sortOptions = sortField;
            if (!string.IsNullOrEmpty(sortField) && !string.IsNullOrEmpty(sortDir))
                _sortOptions = _sortOptions + " " + sortDir;
            #endregion

            #region [ Quick Text Search ]
            if (!string.IsNullOrEmpty(_searchText))
            {
                List<BestEffort_ext1> _list = quickTextSearch(_searchText, _pageSize, _pageNo, _sortOptions, qDefName);
                if (_list != null && _list.Count() > 0)
                {
                    Mapper.CreateMap<BestEffort_ext1, BestEffort>();
                    IEnumerable<iItemType> results = _list.Select(a => Mapper.Map<BestEffort_ext1, BestEffort>(a)).ToList();
                    return new genericResponse() { success = true, __count = _list.FirstOrDefault().count_, results = results.ToList() };
                }
            }
            #endregion

            return new genericResponse() { success = true, __count = 0 };
        }
        #endregion
        */

        #region [[ (genericResponse) Search[POST] - crm/api/BestEffort/Search ]]

        [HttpPost, Route("crm/api/BestEffort/Search")]
        [apiAuthorize(AccessElement = "cmdiapp.dms.BestEfforts", AccessLevel = "v")]
        public genericResponse Search(searchParam _param, string qDefName = "crm_bestEffort")
        {
            #region [ Retrieve Input ]
            string _searchText = _param.searchText_;

            if (string.IsNullOrEmpty(_searchText)
                && (_param.searchData == null || _param.searchData.Count() == 0 || string.IsNullOrEmpty(_param.searchData[0].valuef)))
                return new genericResponse() { __count = 0, success = true };

            int _pageSize = _param.pageSize_;
            int _pageNo = _param.page_;
            string _sortOptions = _param.sortOption1_;
            #endregion

            #region [ HTTPGET - Retrieve "Sort" options ]
            //string sortField = HttpContext.Current.Request.QueryString["sort[0][field]"];
            //string sortDir = HttpContext.Current.Request.QueryString["sort[0][dir]"];
            #endregion

            List<BestEffort_ext1> _list;

            /*
            #region [ 1. Quick Text Search ]

            if (!string.IsNullOrEmpty(_searchText))
                _list = quickTextSearch(_searchText, _pageSize, _pageNo, _sortOptions);
            #endregion

            #region [ 2. Search Data ]
            else
                _list = sqlSearch(_param.searchData, _pageSize, _pageNo, _sortOptions, qDefName);
            #endregion
            */
            _list = sqlSearch(_param.searchData, _pageSize, _pageNo, _sortOptions, qDefName);

            if (_list != null && _list.Count() > 0)
            {
                Mapper.CreateMap<BestEffort_ext1, BestEffort>();
                IEnumerable<iItemType> results = _list.Select(a => Mapper.Map<BestEffort_ext1, BestEffort>(a)).ToList();
                return new genericResponse() { success = true, __count = _list.FirstOrDefault().count_, results = results.ToList() };
            }

            return new genericResponse() { success = true, __count = 0 };
        }
        #endregion

        #region [[ Export ]]
        [HttpPost, Route("crm/api/BestEffort/ExportKey")]
        [apiAuthorize(AccessElement = "cmdiapp.dms.BestEfforts", AccessLevel = "e")]
        public genericResponse ExportKey(searchParam param)
        {
            string key_ = System.Guid.NewGuid().ToString();
            HttpRuntime.Cache.Insert(key_, param, null, DateTime.UtcNow.Add(new TimeSpan(1, 0, 1)), System.Web.Caching.Cache.NoSlidingExpiration);
            return new genericResponse { message = key_ };
        }

        [HttpGet, Route("crm/api/BestEffort/Export")]
        [apiAuthorize(AccessElement = "cmdiapp.dms.BestEfforts", AccessLevel = "e")]
        //public bool Export(string key)
        public genericResponse Export(string key, string markRec, string createNote, string contactNote, string contactDate)
        {
            key = key.Replace("\"", "").Trim();

            searchParam _param = (searchParam)HttpRuntime.Cache[key];

            //if (_param == null)
            //    return false;

            //System.Data.DataTable _dt = sqlSearch_forExport__dataset(_param.searchData);
            //if (_dt != null)
            //{
            //    CreateExcelFile.CreateExcelDocument(_dt.Copy(), "Crimson - Best Effort", HttpContext.Current.Response);
            //}

            //return true;

            System.Data.DataSet _ds = sqlSearch_forExport__dataset(_param.searchData);

            if (_ds != null)
            {
                // Mark records after query
                int count = 0;
                if (createNote == "Y")
                {
                    count = sqlSearch_createNote(_param.searchData, contactNote, contactDate);
                }
                if (markRec == "Y")
                {
                    count = sqlSearch_markRec(_param.searchData);
                }
                string key_ = System.Guid.NewGuid().ToString();
                HttpRuntime.Cache.Insert(key_, _ds, null, DateTime.UtcNow.Add(new TimeSpan(1, 0, 1)), System.Web.Caching.Cache.NoSlidingExpiration);
                return new genericResponse { message = key_, __count = count };
            }
            else
            {
                return new genericResponse { message = null, __count = 0 }; 
            }          
        }

        [HttpPost, Route("crm/api/BestEffort/Export2")]
        [apiAuthorize(AccessElement = "cmdiapp.dms.BestEfforts", AccessLevel = "e")]
        public genericResponse Export2(BestEffortParm parm)
        {
            QueryRuntime qr = new QueryRuntime(parm.qinstance);
            string sql = qr.sql();
            int count = 0;
            string sql_mark = sql.Replace("SELECT DISTINCT", "SELECT [M].MID,");

            if (parm.createNote)
            {
                string insert_cmgmt_query = @" INSERT INTO ACTHIST (PID, HISTTYPEID, HISTDATE, SUBJECT, NOTE, UID) ";

                string subject = "Best Efforts";

                insert_cmgmt_query = string.Format(@" {0} 
                        SELECT a.PID, 1, CONVERT(VARCHAR(10), '{1}', 101), '{2}' ,'{3}' ,{4}
                        FROM ( {5} ) a ",
                                insert_cmgmt_query,                              
                                parm.contactDate,                                     
                                subject,                                         
                                parm.contactNote,                                     
                                crmSession.UID().Value,
                                sql_mark);                                             

                insert_cmgmt_query += "; SELECT @@rowcount as ROWSCT";
                _entity_crm.getContext().Database.SqlQuery<int>(String.Format("{0}", insert_cmgmt_query)).FirstOrDefault();
            }

            if (parm.markRec)
            {
                string mark_query = String.Format(@"INSERT INTO BESTEFRT (PID, MID, REQDTE) 
                                        SELECT r.PID, r.MID, CONVERT(VARCHAR(10),GETDATE(),101)
                                        FROM ( {0} ) r 
                                        WHERE r.MID NOT IN (SELECT MID FROM BESTEFRT) 
                                        SELECT @@rowcount as ROWSCT 
                                      ", sql_mark);

                count = _entity_crm.getContext().Database.SqlQuery<int>(String.Format("{0}", mark_query)).FirstOrDefault();
            }

            return new genericResponse { success = true,  message = "Best Effort records are marked successfully", __count = count };
        }
        #endregion

        #region [[ mail Merge ]]
        [HttpPost, Route("crm/api/BestEffort/MailMergeKey")]
        public string MailMergeKey(searchParam _param)
        {
            System.Data.DataSet _ds = sqlSearch_forExport__dataset(_param.searchData);

            if (_ds != null)
            {
                string key_ = System.Guid.NewGuid().ToString();
                HttpRuntime.Cache.Insert(key_, _ds, null, DateTime.UtcNow.Add(new TimeSpan(1, 0, 1)), System.Web.Caching.Cache.NoSlidingExpiration);
                return key_;
            }
            else
            {
                return null;
            }
        }
        #endregion
    }
}
