﻿using System.ComponentModel.DataAnnotations.Schema;

using cmdiapp.n.core.Areas.crm.Domain.Data;
using cmdiapp.n.core.Domain.ViewModels;
using System.ComponentModel.DataAnnotations;

namespace cmdiapp.n.core.Areas.crm.Domain.Models
{
    [Table("LEGISLATORINFO")]
    public class LegislatorInfo : _entityBase_crm, iItemType
    {
        [Column, Key]
        public int LEGISLATORINFOID { get; set; }

        [Column, MaxLength(25)]
        public string LEGISLATORID { get; set; }

        [Column, MaxLength]
        public string BIO { get; set; }
    }
}