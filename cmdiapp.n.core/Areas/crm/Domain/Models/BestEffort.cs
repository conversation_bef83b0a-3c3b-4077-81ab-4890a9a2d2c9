﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Data;
using System.Linq;
using System.Web.Mvc;
using System.Data.Linq;
using System.ComponentModel.DataAnnotations.Schema;

using cmdiapp.n.core.Domain.ViewModels;
using cmdiapp.n.core.Areas.crm.Domain.Data;

namespace cmdiapp.n.core.Areas.crm.Domain.Models
{
    public class BestEffort : _entityBase_crm, iItemType
    {
        public int PID { get; set; }

        public string PEOTYPE { get; set; }

        public string PREFIX { get; set; }

        public string FNAME { get; set; }

        public string MNAME { get; set; }

        public string LNAME { get; set; }

        public string SUFFIX { get; set; }

        public string NAME { get; set; }

        public string SALUTATION { get; set; }

        public string EMPLOYER { get; set; }

        public string OCCUPATION { get; set; }

        public string MAILNAME { get; set; }

        public string MAILSALUTATION { get; set; }

        public bool PRIMEMAIL { get; set; }

        public string STREET { get; set; }

        public string ADDR1 { get; set; }

        public string ADDR2 { get; set; }

        public string CITY { get; set; }

        public string STATE { get; set; }

        public string ZIP { get; set; }

        public string PLUS4 { get; set; }

        public string ZIP_PLUS4 { get; set; }

        public string HMPHN { get; set; }

        public string BSPHN { get; set; }

        public string FAXPHN { get; set; }

        public string EMAIL { get; set; }

        public decimal CTDAMT { get; set; }

        public decimal LGIFT { get; set; }

        public DateTime LGIFTDTE { get; set; }

        /*
        public Decimal? HPC { get; set; }

        public Decimal? YTDGIFT { get; set; }

        public DateTime? LASTGIFTDATE { get; set; }

        public Decimal? LASTGIFT { get; set; }

        public string LGIFTSRCE { get; set; }

        public string LGIFTPKGE { get; set; }

        public string LGIFTPROG { get; set; }
        */
        
    }

    public class BestEffort_ext1 : BestEffort
    {
        public int count_ { get; set; }

        public Int64 rowNo { get; set; }
    }
     
}
