﻿using cmdiapp.n.core.Core.App_Start;
using Ninject;
using System;
using System.Collections.Generic;
using System.Data.Entity;
using System.Linq;
using System.Linq.Expressions;

namespace cmdiapp.n.core.Areas.datax.Domain.Data
{
    public interface I_entity_datax
    {
        DbContext getContext();

        IQueryable<TSource> All<TSource>() where TSource : _entityBase_datax, new();

        void Add<TSource>(IEnumerable<TSource> items) where TSource : _entityBase_datax, new();
        void Add<TSource>(TSource item) where TSource : _entityBase_datax, new();

        void CommitChanges();

        void Delete<TSource>(Expression<Func<TSource, bool>> expression) where TSource : _entityBase_datax, new();
        void Delete<TSource>(TSource item) where TSource : _entityBase_datax, new();

        void Dispose();

        TSource Single<TSource>(Expression<Func<TSource, bool>> expression) where TSource : _entityBase_datax, new();

        void Update<TSource>(TSource item) where TSource : _entityBase_datax, new();

        void Refresh<TSource>(TSource item) where TSource : _entityBase_datax, new();
    }
    public static class I_entity_dataxManager
    {
        public static I_entity_datax getEntity()
        {
            return NinjectMVC.kernel.Get<I_entity_datax>();
        }

        public static I_entity_datax getEntity(string connectionString)
        {
            return NinjectMVC.kernel.Get<I_entity_datax>(new Ninject.Parameters.ConstructorArgument("connectionString", connectionString, true));
        }

    }
}
