﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Data;
using System.Linq;
using System.Web.Mvc;
using System.Data.Linq;
using System.ComponentModel.DataAnnotations.Schema;
using cmdiapp.n.core.Areas.crm.Domain.Data;

using cmdiapp.n.core.Domain.ViewModels;

namespace cmdiapp.n.core.Areas.crm.Domain.Models
{   
    [Table("Attribute")]
    public class Attribute_ : _entityBase_crm, iItemType
    {
        [Column]
        [Key]
        public int id { get; set; }

        [Column]
        public int categoryId { get; set; }
        [ForeignKey("categoryId")]
        public virtual AttributeCategory Category { get; set; }
        [Column]
        public string name { get; set; }

        [Column]
        public int priority { get; set; }

        [DatabaseGenerated(DatabaseGeneratedOption.Computed)]
        public string fullNameC { get; set; }

        [Column]
        public string note { get; set; }

        [Column]
        public string customizedColumns { get; set; }

        [Column]
        public string fromOtherDataInfo { get; set; }

        [Column]
        public DateTime? createdAtUtc { get; set; }

        [Column]
        public DateTime? updatedAtUtc { get; set; }

    }

    [Table("AttributeCategory")]
    public class AttributeCategory : _entityBase_crm, iItemType
    {
        [Column]
        [Key]
        public int id { get; set; }

        [Column]
        public string name { get; set; }

        [Column]
        public int priority { get; set; }

        [Column]
        public string labeling { get; set; }

        [DatabaseGenerated(DatabaseGeneratedOption.Computed)]
        public string labelingC { get; set; }

        [Column]
        public string note { get; set; }
        
        [Column]
        public DateTime? createdAtUtc { get; set; }

        [Column]
        public DateTime? updatedAtUtc { get; set; }

    }
       
    [Table("AttributePeople")]
    public class AttributePeople : _entityBase_crm, iItemType
    {
        [Column]
        [Key]
        public int id { get; set; }

        [Column]
        public int PID { get; set; }

        [Column]
        public int attributeId { get; set; }
        [ForeignKey("attributeId")]
        public virtual Attribute_ Attribute { get; set; }

        [NotMapped]
        public string AttributeName
        {
            get
            {
                return Attribute != null && Attribute.name != null ? Attribute.name : null;
            }
        }
        [NotMapped]
        public string CategoryName
        {
            get
            {
                return Attribute != null && Attribute.Category != null && Attribute.Category.name != null ? Attribute.Category.name : null;
            }
        }

        [Column]
        public DateTime startDate { get; set; }

        [Column]
        public DateTime? endDate { get; set; }

        [Column]
        public string customizedData { get; set; }

        [Column]
        public bool? active { get; set; }

        [DatabaseGenerated(DatabaseGeneratedOption.Computed)]
        public int? startYearC { get; set; }

        [DatabaseGenerated(DatabaseGeneratedOption.Computed)]
        public int? endYearC { get; set; }

        [DatabaseGenerated(DatabaseGeneratedOption.Computed)]
        public string displayValueC { get; set; }

        [Column]
        public DateTime? createdAtUtc { get; set; }

        [Column]
        public DateTime? updatedAtUtc { get; set; }

    }

    public class AttributePeople_export : _entityBase_crm, iItemType
    {
        public int id { get; set; }
        public int PID { get; set; }
        public int attributeId { get; set; }
        public string AttributeName { get; set; }
        public string CategoryName { get; set; }
        public DateTime startDate { get; set; }
        public DateTime? endDate { get; set; }
        public string customizedData { get; set; }
        public bool? active { get; set; }
        public int? startYearC { get; set; }
        public int? endYearC { get; set; }
        public string displayValueC { get; set; }
        public DateTime? createdAtUtc { get; set; }
        public DateTime? updatedAtUtc { get; set; }
    }

    public class Attribute_ext : _entityBase_crm, iItemType
    {
        public int id { get; set; }
        public int categoryId { get; set; }
        public string name { get; set; }
        public int priority { get; set; }
        public string fullNameC { get; set; }
        public string note { get; set; }
        public string customizedColumns { get; set; }
        public string fromOtherDataInfo { get; set; }
        public DateTime? createdAtUtc { get; set; }

        public int count_ { get; set; }
        public Int64 rowNo { get; set; }
    }
    public class AttributeView : _entityBase_crm, iItemType
    {
        public int id { get; set; }
        public int categoryId { get; set; }
        public string name { get; set; }
        public int priority { get; set; }
        public string fullNameC { get; set; }
        public string note { get; set; }
        public string customizedColumns { get; set; }
        public string fromOtherDataInfo { get; set; }
        public DateTime? createdAtUtc { get; set; }
        public DateTime? updatedAtUtc { get; set; }

    }
    public class AttributeCategory_ext : _entityBase_crm, iItemType
    {
        public int id { get; set; }
        public string name { get; set; }
        public int priority { get; set; }
        public string labeling { get; set; }
        public string labelingC { get; set; }
        public string note { get; set; }
        public DateTime? createdAtUtc { get; set; }
        public DateTime? updatedAtUtc { get; set; }
        public int count_ { get; set; }
        public Int64 rowNo { get; set; }

    }
    public class AttributeCategoryView : _entityBase_crm, iItemType
    {
        public int id { get; set; }
        public string name { get; set; }
        public int priority { get; set; }
        public string labeling { get; set; }
        public string labelingC { get; set; }
        public string note { get; set; }
        public DateTime? createdAtUtc { get; set; }
        public DateTime? updatedAtUtc { get; set; }

    }
    public class labelingList
    {
        public labelingList(string l,string c)
        {
            labeling = l;
            labelingC = c;
        }
        public string labeling { get; set; }
        public string labelingC { get; set; }
    }
}