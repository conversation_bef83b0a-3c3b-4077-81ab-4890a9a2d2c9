﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Data.Linq;
using System.Web;
using System.Data.Entity;
using cmdiapp.n.core.Areas.crm.Domain.Models;

namespace cmdiapp.n.core.Areas.dataService.Domain.Data
{
    public class _dsDBContext : DbContext
    {
        public _dsDBContext(string _appDBContext_connString_itself_or_name)
            : base(_appDBContext_connString_itself_or_name)
        {
            Database.SetInitializer<_dsDBContext>(new DBInitializer());
        }

        protected override void OnModelCreating(DbModelBuilder modelBuilder)
        {
            modelBuilder.Conventions.Remove<System.Data.Entity.ModelConfiguration.Conventions.PluralizingTableNameConvention>();
        }
    }

    public class DBInitializer : IDatabaseInitializer<_dsDBContext>
    {
        public void InitializeDatabase(_dsDBContext context)
        {
            if (!context.Database.Exists())
            {
                throw new NotSupportedException("The database does not exist.");
            }
        }
    }

}