﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;

namespace cmdiapp.n.core.Areas.datax.Domain.Services
{
    /// <summary>
    /// Contains utility methods for sending
    /// requests to Cremir
    /// </summary>
    public interface ICremirUtilService
    {
        /// <summary>
        /// Gets the base URL that locates Cremir
        /// </summary>
        string BaseUrl { get; }
        /// <summary>
        /// Adds required authorization header to http request
        /// </summary>
        /// <param name="request">the http request to add authorization to</param>
        void AddAuthToRequest(HttpRequestMessage request);        
    }
}
