﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Net;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Web.Http;
using System.Web.Script.Serialization;
using System.Drawing;
using System.IO;
using System.Data.SqlClient;
using System.Collections.ObjectModel;
using System.Xml.Linq;

using Ninject;
using Ninject.Web.Mvc;

using AutoMapper;
using cmdiapp.n.core.Core;
using cmdiapp.n.core.Core.App_Start;
using cmdiapp.n.core.Domain.ViewModels;
using cmdiapp.n.core.Areas.crm.Core;
using cmdiapp.n.core.Areas.crm.Domain.Models;
using cmdiapp.n.core.Areas.crm.Domain.ViewModels;
using cmdiapp.n.core.Areas.crm.Domain.Data;
using cmdiapp.n.core.Areas.crm.Domain.Repositories;
using cmdiapp.n.core.Areas.crm.Domain.Services;
using cmdiapp.n.core.Areas.dataService.Domain.Data;
using cmdiapp.n.core.Areas.dataService.Domain.Models;
using cmdiapp.n.core.Areas.dataService.Domain.Services;
using cmdiapp.n.core.Presentation.Controllers;
using cmdiapp.n.core.Areas.query.Domain.Models;
using cmdiapp.n.core.Domain.Models;
using AutoMapper;
using System.Data;
using cmdiapp.n.core.Library;

namespace cmdiapp.n.core.Areas.crm.Presentation.Controllers.api
{
    [Authorize]
    public class AccountCodeSettingsController : ApiController
    {
        #region [[ Declaration ]]
        private I_entity_crm _entity_crm;
        
        private readonly IReportDataService _reportService;
        private userSession _userSession;
        
        #endregion
        
        #region [[ (constructor) SettingsController ]]
        public AccountCodeSettingsController()
        {
            _entity_crm = I_entityManager_crm.getEntity();
            _reportService = NinjectMVC.kernel.Get<IReportDataService>();
            _userSession = session.userSession;
        }
        #endregion

        #region[MONEY->SETTINGS->ACCOUNTCODE by Tanvir]
        [HttpPost, Route("crm/api/AccountCodeSettings/ExportAccountCodeKey")]
        [apiAuthorize(AccessElement = "cmdiapp.dms.dmcenter", AccessLevel = "v")]
        public string ExportAccountCodeKey(string searchText)
        {
            if (string.IsNullOrEmpty(searchText)) searchText = "";

            searchParam param = new searchParam();
            param.searchText = searchText;

            string key_ = System.Guid.NewGuid().ToString();
            HttpRuntime.Cache.Insert(key_, param, null, DateTime.UtcNow.Add(new TimeSpan(1, 0, 1)), System.Web.Caching.Cache.NoSlidingExpiration);

            return key_;

        }

        [HttpGet, Route("crm/api/AccountCodeSettings/ExportAccountCode")]
        [apiAuthorize(AccessElement = "cmdiapp.dms.dmcenter", AccessLevel = "v")]
        public string ExportAccountCode(string key)
        {
            string sToday = String.Format("{0:yyyyMMMdd__hh_mm_ss tt}", DateTime.Now);
            string moduleName = "Account Code";
            string _sql_exportAccountCode = @"select 
                                        CENTERCODE as [Code],
                                        DESCRIP as [Description], 
                                        case
	                                        when BEGINBAL is null Then ''
	                                        else BEGINBAL
	                                        End As [Begin Bal],
                                        case 
	                                        when BANKACCT is null Then ''
	                                        when RTrim(LTrim(BANKACCT)) ='' then ''
	                                        else RTrim(LTrim(BANKACCT))
	                                        End AS [Bank Acct],


                                        case 
	                                        When	convert(date, BEGINBALDTE, 101) = convert(date, '1/1/1900', 101) Then '' 
	                                        when BEGINBALDTE is null Then ''
	                                        Else	CONVERT(VARCHAR(10),BEGINBALDTE,101) 
	                                        End AS [Begin Bal Date],

                                        case DEFFLAG 
	                                         when 1  Then 'Yes'
	                                         else 'No'
	                                         End As [Default],
                                        case ACTIVE 
	                                         when 1  Then 'Yes'
	                                         else 'No'
	                                         End As [Active]
                                        from dmCENTER";

            searchParam _param = (searchParam)HttpRuntime.Cache[key];
            //string sToday = String.Format("{0:yyyyMMMdd__hh_mm_ss tt}", DateTime.Now);
            //string moduleName = "AccountCodes";
            string fileName = moduleName + "_" + System.Guid.NewGuid() + "_" + _userSession.UserName + "_" + sToday;

            DataSet ds = null;

            if (string.IsNullOrEmpty(_param.searchText))
            {
                ds = _reportService.get_dataset_w_sql__single(_sql_exportAccountCode, "AccountCodes");
            }
            else
            {
                _param.searchText = Library.util.kill_sqlBlacklistWord(_param.searchText);

                string _where = "";

                if (!string.IsNullOrEmpty(_param.searchText))
                {
                    #region [ Split Search Text > string1, string2 & string3 and create "Where" statement ]
                    char[] delimiterChars = { ' ', ',', '.', ';', ':', '|', '\t' };

                    string[] words = _param.searchText.Split(delimiterChars);

                    if (words.Count() > 0)
                    {
                        _where = string.Format("CENTERCODE LIKE '%{0}%' OR DESCRIP LIKE '%{0}%'", words[0]);
                        for (int i = 1; i < words.Length; i++)
                        {
                            _where = _where + string.Format("CENTERCODE LIKE '%{0}%' OR DESCRIP LIKE '%{0}%'", words[i]);
                        }
                    }
                    #endregion
                }
                //We need SQL with Where condition
                string sql = _sql_exportAccountCode + " Where {0}";
                //Compose the SQL
                sql = String.Format(sql, _where);

                ds = _reportService.get_dataset_w_sql__single(sql, "AccountCodes");

            }

            if (ds != null)
            {
                string key_ = System.Guid.NewGuid().ToString();
                HttpRuntime.Cache.Insert(key_, ds, null, DateTime.UtcNow.Add(new TimeSpan(1, 0, 1)), System.Web.Caching.Cache.NoSlidingExpiration);
                //return the Key
                return key_;
            }
            else
            {
                return null;
            }
        }

        //Note: Get Account Code List
        [HttpGet, Route("crm/api/AccountCodeSettings/GetAccountCodes")]
        [apiAuthorize(AccessElement = @"cmdiapp.dms.dmcenter", AccessLevel = "v")]
        public genericResponse GetAccountCodes(string searchText)
        {
            string _sql =
                        @"SELECT 
                        [dmC].[CENTERID] AS [CENTERID], 
                        [dmC].[CENTERCODE] AS [CENTERCODE], 
                        [dmC].[DESCRIP] AS [DESCRIP], 
                        [dmC].[UPDATEDON] AS [UPDATEDON], 
                        [dmC].[CCPROFILE] AS [CCPROFILE], 
                        [dmC].[BANKACCT] AS [BANKACCT], 
                        [dmC].[BEGINBAL] AS [BEGINBAL], 
                        [dmC].[BEGINBALDTE] AS [BEGINBALDTE], 
                        [dmC].[DEFFLAG] AS [DEFFLAG], 
                        [dmC].[ACTIVE] AS [ACTIVE], 
                        [dmC].[SYSTEM] AS [SYSTEM]
                        FROM [dbo].[dmCENTER] AS [dmC]";

            //order by
            string _sortOption = "[dmC].[CENTERCODE] ASC";

            //where
            string _where = "";
            if (!string.IsNullOrEmpty(searchText))
            {
                searchText = Library.util.kill_sqlBlacklistWord(searchText);


                if (!string.IsNullOrEmpty(searchText))
                {
                    #region [ Split Search Text > string1, string2 & string3 and create "Where" statement ]
                    char[] delimiterChars = { ' ', ',', '.', ';', ':', '|', '\t' };

                    string[] words = searchText.Split(delimiterChars);

                    if (words.Count() > 0)
                    {
                        _where = string.Format("CENTERCODE LIKE '%{0}%' OR DESCRIP LIKE '%{0}%'", words[0]);
                        for (int i = 1; i < words.Length; i++)
                        {
                            _where = _where + string.Format("CENTERCODE LIKE '%{0}%' OR DESCRIP LIKE '%{0}%'", words[i]);
                        }
                    }
                    #endregion
                }
                _sql = _sql + " where " + _where;
            }
            _sql = _sql + " order by " + _sortOption;


            try
            {
                List<dmCENTER> _list = _entity_crm.getContext().Database.SqlQuery<dmCENTER>(_sql).ToList();

                return new genericResponse() { success = true, __count = _list.Count, results = _list.ToList<iItemType>() };
            }
            catch(Exception ex)
            {
                return new genericResponse() { success = false, __count = 0 };
            }
        }

        [HttpGet, Route("crm/api/AccountCodeSettings/getAccountCode/{_CENTERID}")]
        [apiAuthorize(AccessElement = @"cmdiapp.dms.dmcenter", AccessLevel = "v")]
        public dmCENTER getAccountCode(Int16 _CENTERID)
        {
            if (_CENTERID < 0)//meaning new exception code
                return new dmCENTER
                {
                    CENTERID = -1
                };
            //meaning edit
            return _entity_crm.All<dmCENTER>().Where(dmC => dmC.CENTERID == _CENTERID).FirstOrDefault();
        }

        //Note: Save and Edit AccountCode
        [HttpPost, Route("crm/api/AccountCodeSettings/saveAccountCode")]
        [apiAuthorize(AccessElement = @"cmdiapp.dms.dmcenter", AccessLevel = "a")]
        public genericResponse saveAccountCode(dmCENTER2 dmC)
        {
            dmCENTER _record = null;
            //check uniqueness for code
            _record = _entity_crm.Single<dmCENTER>(a => a.CENTERCODE.Trim().ToLower() == dmC.CENTERCODE.Trim().ToLower());

            if (_record != null && _record.CENTERID != dmC.CENTERID)
                return new genericResponse() { success = false, message = "Code: " + dmC.CENTERCODE.Trim() + " already exists. Please enter unique Code and try again." };


            //check if already exists or new record
            _record = _entity_crm.Single<dmCENTER>(a => a.CENTERID == dmC.CENTERID);
            try
            {
                // existing record
                if (_record != null && _record.CENTERID >= 0)
                {
                    _record.CENTERCODE = dmC.CENTERCODE;
                    _record.DESCRIP = dmC.DESCRIP;
                    _record.UPDATEDON = System.DateTime.Now;
                    _record.CCPROFILE = dmC.CCPROFILE;
                    _record.BANKACCT = dmC.BANKACCT;
                    _record.BEGINBAL = dmC.BEGINBAL;
                    _record.BEGINBALDTE = dmC.BEGINBALDTE;
                    _record.DEFFLAG = dmC.DEFFLAG == true ? (Byte)1 : (Byte)0;
                    _record.ACTIVE = dmC.ACTIVE;
                    _record.SYSTEM = dmC.SYSTEM == true ? (Byte)1 : (Byte)0;

                    _entity_crm.Update(_record);
                }
                else// new record
                {
                    _record = new dmCENTER();

                    //_record.CENTERID	= dmC.CENTERID
                    _record.CENTERCODE = dmC.CENTERCODE;
                    _record.DESCRIP = dmC.DESCRIP;
                    _record.UPDATEDON = System.DateTime.Now;
                    _record.CCPROFILE = dmC.CCPROFILE;
                    _record.BANKACCT = dmC.BANKACCT;
                    _record.BEGINBAL = dmC.BEGINBAL;
                    _record.BEGINBALDTE = dmC.BEGINBALDTE;
                    _record.DEFFLAG = dmC.DEFFLAG == true ? (Byte)1 : (Byte)0;
                    _record.ACTIVE = dmC.ACTIVE;
                    _record.SYSTEM = dmC.SYSTEM == true ? (Byte)1 : (Byte)0;
                    _entity_crm.Add(_record);
                }

                _entity_crm.CommitChanges();
                return new genericResponse() { success = true, message = "Code was saved successfully saved" };
            }
            catch (Exception ex)
            {
                return new genericResponse() { success = false, message = "Code was NOT saved. Try again later" };
            }

        }

        //Note: Delete Account Code
        [HttpPost, Route("crm/api/AccountCodeSettings/DeleteAccountCode/{_CENTERID}")]
        [apiAuthorize(AccessElement = @"cmdiapp.dms.dmcenter", AccessLevel = "d")]
        public genericResponse DeleteAccountCode(int _CENTERID)
        {
            try
            {
                //check if already in use in TXN table
                string InUSEquery = @"declare @_centerID int={0};
                select
                (select count(*) from dtBATCH where CENTERID =@_centerID)+
                (select count(*) from MONY where CENTERID =@_centerID)+
                (select count(*) from TXN where CENTERID =@_centerID)";

                string isInUse = String.Format(InUSEquery, _CENTERID);
                int count = _entity_crm.getContext().Database.SqlQuery<int>(isInUse).FirstOrDefault();
                if (count > 0)
                {
                    genericResponse _respnse = new genericResponse() { success = false, message = "Code can not be deleted. It is being referenced." };
                    return _respnse;
                }
                //if not in use, delete it
                _entity_crm.Delete<dmCENTER>(a => a.CENTERID == _CENTERID);
                _entity_crm.CommitChanges();

                genericResponse _response = new genericResponse() { success = true, message = "Code deleted successfully." };
                return _response;
            }
            catch
            {
                genericResponse _response = new genericResponse() { success = false, message = "Error occurred while deleting code. Please try again later." };
                return _response;
            }
        }
        #endregion
    }
}
