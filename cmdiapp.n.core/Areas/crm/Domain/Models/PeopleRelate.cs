﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Data;
using System.Linq;
using System.Web.Mvc;
using System.Data.Linq;
using System.ComponentModel.DataAnnotations.Schema;

using cmdiapp.n.core.Domain.ViewModels;
using cmdiapp.n.core.Areas.crm.Domain.Data;
using System.Xml;
using System.Xml.Linq;

namespace cmdiapp.n.core.Areas.crm.Domain.Models
{
    //public class PeopleRelateInfo
    //{
    //    public int JTRELATEID { get; set; }

    //    public int ParentPID { get; set; }

    //    public int PID { get; set; }

    //    public int RELATETO { get; set; }

    //    public string NAME { get; set; }
        
    //    public string COMPANY { get; set; }

    //    public string RNAME { get; set; }

    //    public string RCOMPANY { get; set; }

    //    public Int16 RELATETYPEID { get; set; }

    //    public string DESCRIP { get; set; }

    //    public string RECIPROCAL { get; set; }

    //    public string COMMENT { get; set; }
        
    //    public DateTime? UPDATEDON { get; set; }

    //    public DateTime? RELATEDTE { get; set; }

    //    public byte[] R1_PICTURE { get; set; }

    //    public byte[] R2_PICTURE { get; set; }

    //    public Int16? R1_PEOTYPEID { get; set; }

    //    public Int16? R2_PEOTYPEID { get; set; }

    //    public int hasChildren { get; set; }

    //}

    public class PeopleRelateInfo
    {
        public int PID { get; set; }

        public int RELATETO { get; set; }

        public string NAME { get; set; }

        public int hasChildren { get; set; }

        public int JTRELATEID { get; set; }

        public int NEXTSTEPID { get; set; }

        public string PIDJTRELATEID
        {
            get
            {
                return string.Format("{0}|{1}|{2}", PID, JTRELATEID, RELATETO);
            }
        }

    }

    public class PeopleRelateDetailInfo
    {
        public string COMMENT { get; set; }

        public string RELATEDTE { get; set; }

        public int JTRELATEID { get; set; }
    }

    [Table("lkRELATETYPE")]
    public class lkRELATETYPE : _entityBase_crm, iItemType
    {
        [Column]
        [Key]
        public Int16 RELATETYPEID { get; set; }

        [Column]
        public string RELATETYPE { get; set; }

        [Column]
        public string DESCRIP { get; set; }

        [Column]
        public string RECIPROCAL { get; set; }
        
        [Column]
        public Byte DEFFLAG { get; set; }

        [Column]
        public Byte SYSTEM { get; set; }
    }

}