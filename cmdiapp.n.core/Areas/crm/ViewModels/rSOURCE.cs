using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace cmdiapp.n.core.Domain.ViewModels
{
    public class rSOURCE : iItemType
    {
        public int SRCEID { get; set; }
        public int PKGEID { get; set; }
        //public virtual PACKAGE PACKAGE { get; set; }

        public string PK<PERSON>CODE { get; set; }
        public string PKGEDESC { get; set; }
        public string SRCECODE { get; set; }
        public string SRCEDESC { get; set; }
        public string EVNTCODE { get; set; }
        public string LISTNO { get; set; }
        public DateTime? MAILDTE { get; set; }
        public DateTime? FIRSTCAGE { get; set; }
        public DateTime? LASTCAGE { get; set; }
        public Decimal? COSTPROD1 { get; set; }
        public Decimal? COSTPROD2 { get; set; }
        public Decimal? COSTPROD3 { get; set; }
        public Decimal? COSTPOSTG { get; set; }
        public Decimal? COSTRESP1 { get; set; }
        public Decimal? COSTRESP2 { get; set; }
        public Decimal? COSTRESP3 { get; set; }
        public int? sQTYMAIL { get; set; }
        public int? sMONY { get; set; }
        public int? sPEOPLE { get; set; }
        public Decimal? sGROSS { get; set; }
        public Decimal? sNET { get; set; }
        public Decimal? sCOST { get; set; }
        public Decimal? sGROSSPM { get; set; }
        public Decimal? sNETPM { get; set; }
        public Decimal? sCOSTPM { get; set; }
        public Decimal? sGROSSRSP { get; set; }
        public Decimal? sNETRSP { get; set; }
        public Decimal? sCOSTRSP { get; set; }
        public Double? sRSPPCT { get; set; }
        public String COMMENT { get; set; }
        public DateTime? UPDATEDON { get; set; }
        public DateTime? LASTACTI { get; set; }
        public int? SACTIVITY { get; set; }

    }
}

