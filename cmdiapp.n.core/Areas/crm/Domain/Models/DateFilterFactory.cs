﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Web;

using cmdiapp.n.core.Areas.query.Domain.Models;
using cmdiapp.n.core.Domain.ViewModels;

namespace cmdiapp.n.core.Areas.crm.Domain.Models
{
    public class DateFilterFactory<T> where T : iItemType
    {
        public string Code { get; set; }
        public string ColumnName { get; set; }
        public string SQL { get; set; }
        public Expression<Func<T, bool>> Predicate { get; set; }

        public DateFilterFactory(string code, string columnName, bool doubleQuote=false, string propName=null)
        {
            Code = code.ToUpper();
            ColumnName = columnName;
            // "item" in "item => ..."
            ParameterExpression item = Expression.Parameter(typeof(T), "item");
            // property of item, i.e., item.prop
            MemberExpression prop = Expression.Property(item, propName ?? columnName);
            switch (Code)
            {
                case "W":
                    DateTime firstDay = Library.util.getFirstDayOfWeek(System.DateTime.Today);
                    DateTime lastDay = Library.util.getLastDayOfWeek(System.DateTime.Today).Date.AddSeconds(86399);
                    SQL = String.Format("{0} BETWEEN '{1}' AND '{2}'", 
                        ColumnName, 
                        String.Format("{0:MM/dd/yyyy HH:mm:ss}", firstDay), 
                        String.Format("{0:MM/dd/yyyy HH:mm:ss}", lastDay));                    
                    Predicate = GetBetweenDatesLambda(firstDay, lastDay, item, prop);
                    break;
                case "M":
                    DateTime mFirstDay = Library.util.getFirstDayOfMonth(System.DateTime.Today);
                    DateTime mLastDay = Library.util.getLastDayOfMonth(System.DateTime.Today).Date.AddSeconds(86399);
                    SQL = String.Format("{0} BETWEEN '{1}' AND '{2}'", 
                        ColumnName, 
                        String.Format("{0:MM/dd/yyyy HH:mm:ss}", mFirstDay), 
                        String.Format("{0:MM/dd/yyyy HH:mm:ss}", mLastDay));
                    Predicate = GetBetweenDatesLambda(mFirstDay, mLastDay, item, prop);
                    break;
                case "Q":
                    DateTime qFirstDay = Library.util.getFirstDayOfQuarter(System.DateTime.Today);
                    DateTime qLastDay = Library.util.getLastDayOfQuarter(System.DateTime.Today).Date.AddSeconds(86399);
                    SQL = String.Format("{0} BETWEEN '{1}' AND '{2}'", 
                        ColumnName, 
                        String.Format("{0:MM/dd/yyyy HH:mm:ss}", qFirstDay), 
                        String.Format("{0:MM/dd/yyyy HH:mm:ss}", qLastDay));
                    Predicate = GetBetweenDatesLambda(qFirstDay, qLastDay, item, prop);
                    break;
                case "Y":
                    DateTime yFirstDay = Library.util.getFirstDayOfYear(System.DateTime.Today);
                    DateTime yLastDay = Library.util.getLastDayOfYear(System.DateTime.Today).Date.AddSeconds(86399);
                    SQL = String.Format("{0} BETWEEN '{1}' AND '{2}'", 
                        ColumnName, 
                        String.Format("{0:MM/dd/yyyy HH:mm:ss}", yFirstDay), 
                        String.Format("{0:MM/dd/yyyy HH:mm:ss}", yLastDay));
                    Predicate = GetBetweenDatesLambda(yFirstDay, yLastDay, item, prop);
                    break;
                case "LW":
                    DateTime lwFirstDay = Library.util.getFirstDayOfWeek(System.DateTime.Today.AddDays(-7));
                    DateTime lwLastDay = Library.util.getLastDayOfWeek(System.DateTime.Today.AddDays(-7)).Date.AddSeconds(86399);
                    SQL = String.Format("{0} BETWEEN '{1}' AND '{2}'", 
                        ColumnName, 
                        String.Format("{0:MM/dd/yyyy HH:mm:ss}", lwFirstDay), 
                        String.Format("{0:MM/dd/yyyy HH:mm:ss}", lwLastDay));
                    Predicate = GetBetweenDatesLambda(lwFirstDay, lwLastDay, item, prop);
                    break;
                case "LM":
                    DateTime lmFirstDay = Library.util.getFirstDayOfMonth(System.DateTime.Today.AddMonths(-1));
                    DateTime lmLastDay = Library.util.getLastDayOfMonth(System.DateTime.Today.AddMonths(-1)).Date.AddSeconds(86399);
                    SQL = String.Format("{0} BETWEEN '{1}' AND '{2}'", 
                        ColumnName, 
                        String.Format("{0:MM/dd/yyyy HH:mm:ss}", lmFirstDay), 
                        String.Format("{0:MM/dd/yyyy HH:mm:ss}", lmLastDay));
                    Predicate = GetBetweenDatesLambda(lmFirstDay, lmLastDay, item, prop);
                    break;
                case "LQ":
                    DateTime lqFirstDay = Library.util.getFirstDayOfQuarter(System.DateTime.Today.AddMonths(-3));
                    DateTime lqLastDay = Library.util.getLastDayOfQuarter(System.DateTime.Today.AddMonths(-3)).Date.AddSeconds(86399);
                    SQL = String.Format("{0} BETWEEN '{1}' AND '{2}'", 
                        ColumnName, 
                        String.Format("{0:MM/dd/yyyy HH:mm:ss}", lqFirstDay), 
                        String.Format("{0:MM/dd/yyyy HH:mm:ss}", lqLastDay));
                    Predicate = GetBetweenDatesLambda(lqFirstDay, lqLastDay, item, prop);
                    break;
                case "LY":
                    DateTime lyFirstDay = Library.util.getFirstDayOfYear(System.DateTime.Today.AddYears(-1));
                    DateTime lyLastDay = Library.util.getLastDayOfYear(System.DateTime.Today.AddYears(-1)).Date.AddSeconds(86399);
                    SQL = String.Format("{0} BETWEEN '{1}' AND '{2}'", 
                        ColumnName, 
                        String.Format("{0:MM/dd/yyyy HH:mm:ss}", lyFirstDay),
                        String.Format("{0:MM/dd/yyyy HH:mm:ss}", lyLastDay));
                    Predicate = GetBetweenDatesLambda(lyFirstDay, lyLastDay, item, prop);
                    break;
                case "2Y":
                    DateTime twoyFirstDay = Library.util.getFirstDayOfYear(System.DateTime.Today.AddYears(-2));
                    DateTime twoyLastDay = Library.util.getLastDayOfYear(System.DateTime.Today.AddYears(-2)).Date.AddSeconds(86399);
                    SQL = String.Format("{0} BETWEEN '{1}' AND '{2}'",
                        ColumnName,
                        String.Format("{0:MM/dd/yyyy HH:mm:ss}", twoyFirstDay),
                        String.Format("{0:MM/dd/yyyy HH:mm:ss}", twoyLastDay));
                    Predicate = GetBetweenDatesLambda(twoyFirstDay, twoyLastDay, item, prop);
                    break;
                default:
                    SQL = "1 = 1";
                    Predicate = Expression.Lambda<Func<T, bool>>(Expression.Constant(true), item);
                    break;
            }
            if (doubleQuote)
            {
                SQL = SQL.Replace("'", "''");
            }
        }

        private Expression<Func<T, bool>> GetBetweenDatesLambda(
            DateTime firstDay, DateTime lastDay, ParameterExpression itemExpression, MemberExpression propExpression)
        {
            Expression lower;
            Expression upper;
            Expression condition;
            try
            {
                lower = Expression.Constant(firstDay);
                upper = Expression.Constant(lastDay);
                condition = Expression.And(Expression.GreaterThanOrEqual(propExpression, lower), 
                    Expression.LessThan(propExpression, upper));

            }
            catch (Exception ex)
            {
                if (ex.Message.Contains("System.Nullable"))
                {
                    // property is Nullable<DateTime> so need to convert constants
                    try
                    {
                        lower = Expression.Convert(Expression.Constant(firstDay), typeof(DateTime?));
                        upper = Expression.Convert(Expression.Constant(lastDay), typeof(DateTime?));
                        condition = Expression.And(Expression.GreaterThanOrEqual(propExpression, lower),
                            Expression.LessThan(propExpression, upper));

                    }
                    catch (Exception)
                    {
                        // unknown error so just return true
                        condition = Expression.Constant(true);
                    }
                } else
                {
                    // unknown error so just return true
                    condition = Expression.Constant(true);
                }
            }
            return Expression.Lambda<Func<T, bool>>(condition, itemExpression);
        }

        public static DataTableFilter GetDataTableFilter(string label, string paramName)
        {
            return new DataTableFilter
            {
                label = label,
                paramName = paramName,
                options = new DataTableFilterOption[9]
                {
                    new DataTableFilterOption
                    {
                        label = "This Week",
                        value = "W"
                    },
                    new DataTableFilterOption
                    {
                        label = "This Month",
                        value = "M"
                    },
                    new DataTableFilterOption
                    {
                        label = "This Quarter",
                        value = "Q"
                    },
                    new DataTableFilterOption
                    {
                        label = "This Year",
                        value = "Y"
                    },
                    new DataTableFilterOption
                    {
                        label = "Last Week",
                        value = "LW"
                    },
                    new DataTableFilterOption
                    {
                        label = "Last Month",
                        value = "LM"
                    },
                    new DataTableFilterOption
                    {
                        label = "Last Quarter",
                        value = "LQ"
                    },
                    new DataTableFilterOption
                    {
                        label = "Last Year",
                        value = "LY"
                    },
                    new DataTableFilterOption
                    {
                        label = "Two Years Ago",
                        value = "2Y"
                    }
                }
            };
        }
    }
}