using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Data;
using System.Linq;
using System.Web.Mvc;
using System.Data.Linq;
using System.ComponentModel.DataAnnotations.Schema;

using cmdiapp.n.core.Areas.crm.Domain.Data;
using cmdiapp.n.core.Domain.ViewModels;

namespace cmdiapp.n.core.Areas.crm.Domain.Models
{
    [Table("ssSQLLOG")]
    public class ssSQLLOG : _entityBase_crm, iItemType
    {

        [Column]
        [Key]
        public int SQLLOGID { get; set; }

        [Column]
        public Nullable<int> UID { get; set; }

        [Column]
        public string calledFROM { get; set; }

        [Column]
        public Nullable<System.DateTime> RUNTIME { get; set; }

        [Column]
        public string SQL { get; set; }

        [Column]
        public Nullable<int> noRECS { get; set; }

        [Column]
        public Nullable<bool> SAVED { get; set; }

        [Column]
        public string SAVEDPATH { get; set; }

        [Column]
        public string outputFIELDS { get; set; }

        [Column]
        public string filtering { get; set; }

        [Column]
        public Single? duration { get; set; }
    }
}
