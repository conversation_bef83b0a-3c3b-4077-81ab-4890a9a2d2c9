﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Data;
using System.Linq;
using System.Web.Mvc;
using System.Data.Linq;
using System.ComponentModel.DataAnnotations.Schema;
using cmdiapp.n.core.Areas.crm.Domain.Data;

namespace cmdiapp.n.core.Areas.crm.Domain.Models
{
    public class dataActivity
    {
        public string Type { get; set; }

        public int EntityId { get; set; }
        public string Action { get; set; }
        public DateTime? theDateTime { get; set; }
        public string theDateTimes { get; set; }
    }
}