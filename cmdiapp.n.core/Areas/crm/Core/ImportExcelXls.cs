﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Data;

using DocumentFormat.OpenXml.Packaging;
using System.IO;
using OfficeOpenXml;

namespace cmdiapp.n.core.Areas.crm.Core
{
    public class ImportExcelXls
    {
        private string FileName { get; set; }
        private bool hasHeaders { get; set; }
        private bool date1904 { get; set; }
        private string logPath = @"D:\cmdi.http\_root\tmp\Logs\excel_parser_log.txt";
        //private string logPath = @"C:\Logs\excel_parser_log.txt";
        public DataSet result { get; set; }

        public ImportExcelXls(string FileName, bool hasHeaders, bool returnall, bool open = true, int sheetIndex = 0)
        {
            this.FileName = FileName;
            this.hasHeaders = hasHeaders;
            if (open == true)
            {
                    //For correcting Excel for Mac date issues
                    //check if workbook uses 1/1/1904 instead of 1/1/1900 as base date
                    //correction occurs below in OpenExcel()
                    using (SpreadsheetDocument ssd = SpreadsheetDocument.Open(FileName, false))
                    {
                        WorkbookPart wb = ssd.WorkbookPart;
                        //Date1904 is OpenXml.BooleanValue not bool type--is null if not specified
                        if (wb.Workbook.WorkbookProperties.Date1904 != null)
                        {
                            this.date1904 = wb.Workbook.WorkbookProperties.Date1904;
                        }
                        else
                        {
                            this.date1904 = false;
                        }

                    }
               
                this.result = OpenExcel(returnall, sheetIndex);
            }
        }

        private DataTable BuildHeadersFromFirstRowThenRemoveFirstRow(DataTable dt)
        {
            DataRow firstRow = dt.Rows[0];

            for (int i = 0; i < dt.Columns.Count; i++)
            {
                //Check if data is present.
                if (!(firstRow[i] == null || firstRow[i].ToString().Trim().Equals("")))
                {
                    dt.Columns[i].ColumnName = firstRow[i].ToString().Trim();
                }
            }

            dt.Rows.RemoveAt(0);

            return dt;
        }

        private void DuplicateColumnNameCheck(DataTable dt)
        {
            List<string> columnNames = new List<string>();
            DataRow firstRow = dt.Rows[0];

            for (int i = 0; i < dt.Columns.Count; i++)
            {
                //Check if data is present.
                if (!(firstRow[i] == null || firstRow[i].ToString().Trim().Equals("")))
                {
                    //Check if the column name exists.
                    if (columnNames.Contains(firstRow[i].ToString().Trim()))
                    {
                        throw new DuplicateNameException(string.Format("Duplicate column header ({0}) found in the file, remove duplicate column(s) ", firstRow[i].ToString().Trim()), null);
                    }
                    else
                    {
                        columnNames.Add(firstRow[i].ToString().Trim());
                    }
                }
            }

        }

        public IList<SelectListItemImp> GetWorkSheets()
        {
            var workSheets = new List<SelectListItemImp>();
            bool enableLogging = File.Exists(logPath);

            void Log(string message)
            {
                if (enableLogging)
                {
                    File.AppendAllText(logPath, $"[{DateTime.Now}] {message}\n");
                }
            }

            Log($"Start GetWorkSheets: {FileName}");
            try
            {
                if (!File.Exists(FileName))
                {
                    throw new FileNotFoundException("Excel file not found.", FileName);
                }

                using (var package = new ExcelPackage(new FileInfo(FileName)))
                {
                    Log("Opened Excel file with OfficeOpenXml\n");

                    workSheets.Add(new SelectListItemImp { Text = "", Value = "-1" });

                    int i = 1;
                    foreach (var worksheet in package.Workbook.Worksheets)
                    {
                        workSheets.Add(new SelectListItemImp
                        {
                            Value = i.ToString(),
                            Text = worksheet.Name
                        });
                        i++;
                    }
                }
                Log("Completed GetWorkSheets");
            }
            catch (Exception ex)
            {
                Log($"ERROR in GetWorkSheets: {ex}");
                throw new ApplicationException("Error reading Excel sheets. See logs.", ex);
            }

            return workSheets;
        }

        // <<<code for oledb and odbc connections to get worksheets>>>

        //public IList<SelectListItemImp> GetWorkSheets()
        //{
        //    var workSheets = new List<SelectListItemImp>();
        //    string HDR = hasHeaders ? "Yes" : "No";
        //    //string strConn = $"Provider=Microsoft.ACE.OLEDB.16.0;Data Source={FileName};Extended Properties=\"Excel 12.0 Xml;HDR={HDR};MaxScanRows=15;IMEX=1;READONLY=TRUE;\"";
        //    string strConn = "Driver={Microsoft Excel Driver (*.xls, *.xlsx, *.xlsm, *.xlsb)};" +
        //             "Dbq=" + FileName + ";";
        //    string logPath = @"D:\cmdi.http\_root\tmp\Logs\excel_parser_log.txt";
        //    //string logPath = @"C:\Logs\excel_parser_log.txt";
        //    File.AppendAllText(logPath, $"[{DateTime.Now}] Start GetWorkSheets: {FileName}\n");

        //    try
        //    {
        //        using (var conn = new OdbcConnection(strConn))
        //        {
        //            File.AppendAllText(logPath, $"[{DateTime.Now}] OdbcConnection before open\n");
        //            conn.Open();
        //            File.AppendAllText(logPath, $"[{DateTime.Now}] OdbcConnection open\n");
        //            using (DataTable schemaTable = conn.GetSchema("Tables"))
        //            {
        //                workSheets.Add(new SelectListItemImp { Text = "", Value = "-1" });

        //                int i = 0;
        //                foreach (DataRow schemaRow in schemaTable.Rows)
        //                {
        //                    string sheetName = schemaRow["TABLE_NAME"].ToString();

        //                    workSheets.Add(new SelectListItemImp
        //                    {
        //                        Value = i.ToString(),
        //                        Text = sheetName
        //                    });
        //                    i++;
        //                }
        //            }
        //        }

        //        File.AppendAllText(logPath, $"[{DateTime.Now}] Completed GetWorkSheets\n");
        //    }
        //    catch (Exception ex)
        //    {
        //        string error = $"[{DateTime.Now}] ERROR in GetWorkSheets: {ex}\n";
        //        File.AppendAllText(logPath, error);
        //        throw new ApplicationException("Error reading Excel sheets. See logs.", ex);
        //    }
        //    finally
        //    {
        //        // Optional: Encourage COM/ODBC cleanup
        //        GC.Collect();
        //        GC.WaitForPendingFinalizers();
        //        File.AppendAllText(logPath, $"[{DateTime.Now}] finally block executed.\n");
        //    }
        //    return workSheets;
        //}

        //public IList<SelectListItemImp> GetWorkSheets()
        //{
        //    List<SelectListItemImp> workSheets = new List<SelectListItemImp>();
        //    string HDR = hasHeaders ? "Yes" : "No";
        //    string strConn = "Provider=Microsoft.ACE.OLEDB.16.0;Data Source=" +
        //            @FileName + ";Extended Properties=\"Excel 12.0 Xml;HDR=" +
        //            HDR + ";MaxScanRows=15;IMEX=1;READONLY=TRUE;\"";
        //    using (OleDbConnection conn = new OleDbConnection(strConn))
        //    {
        //        conn.Open();
        //        DataTable schemaTable = conn.GetOleDbSchemaTable(
        //            OleDbSchemaGuid.Tables, new object[] { null, null, null, "TABLE" });
        //        workSheets.Add(new SelectListItemImp { Text = "", Value = "-1" });
        //        int i = 0;
        //        foreach (DataRow schemaRow in schemaTable.Rows)
        //        {
        //            SelectListItemImp item = new SelectListItemImp();
        //            item.Value = i.ToString();
        //            item.Text = schemaRow["TABLE_NAME"].ToString();
        //            workSheets.Add(item);
        //            i += 1;
        //        }
        //    }
        //    return workSheets;
        //}


        private DataTable MacDateCorrection(DataTable dt)
        {
            //changes 1904 based dates to 1900 based
            //see https://support.microsoft.com/en-us/help/214330/differences-between-the-1900-and-the-1904-date-system-in-excel

            for (int i=0; i < dt.Columns.Count; i++)
            {
                //check for date in first cell of each column or if column name contains "date"
                DateTime x;
                if(DateTime.TryParse(dt.Rows[1][i].ToString(), out x) || dt.Columns[i].ColumnName.ToLower().Contains("date"))
                {
                    foreach(DataRow row in dt.Rows)
                    {
                        DateTime date;
                        if(DateTime.TryParse(row[i].ToString(), out date))
                        {
                            //1462 = 4 years + 2 days (for 2 leap years)
                            DateTime newDate = date.AddDays(1462);
                            row[i] = newDate.ToString();
                        }
                        
                    }
                }
            }

            return dt;
        }

        private DataSet OpenExcel(bool returnall, int sheetIndex)
        {
            DataSet output = new DataSet();

            if (!File.Exists(FileName))
                throw new FileNotFoundException("Excel file not found.", FileName);

            try
            {
                using (var package = new ExcelPackage(new FileInfo(FileName)))
                {
                    var worksheets = package.Workbook.Worksheets;

                    if (sheetIndex <= 0 || sheetIndex > worksheets.Count)
                        throw new ArgumentOutOfRangeException(nameof(sheetIndex), "Invalid sheet index.");

                    var worksheet = worksheets[sheetIndex];
                    var sheetName = worksheet.Name;

                    DataTable outputTable = new DataTable(sheetName);
                    output.Tables.Add(outputTable);

                    int startRow = worksheet.Dimension.Start.Row;
                    int endRow = worksheet.Dimension.End.Row;
                    int startCol = worksheet.Dimension.Start.Column;
                    int endCol = worksheet.Dimension.End.Column;

                    int rowLimit = returnall ? endRow : Math.Min(startRow + 4, endRow); // read top 5 rows if not returnall

                    // Read headers from first row (since HDR = "No")
                    for (int col = startCol; col <= endCol; col++)
                    {
                        outputTable.Columns.Add($"Column{col}");
                    }

                    for (int row = startRow; row <= rowLimit; row++)
                    {
                        var dataRow = outputTable.NewRow();
                        for (int col = startCol; col <= endCol; col++)
                        {
                            dataRow[col - startCol] = worksheet.Cells[row, col].Text;
                        }
                        outputTable.Rows.Add(dataRow);
                    }

                    DuplicateColumnNameCheck(outputTable);
                    outputTable = BuildHeadersFromFirstRowThenRemoveFirstRow(outputTable);

                    if (date1904)
                    {
                        MacDateCorrection(outputTable);
                    }
                }
            }
            catch (Exception ex)
            {
                if (File.Exists(logPath))
                {
                    File.AppendAllText(logPath, $"[{DateTime.Now}] Error: {ex.Message}\n");
                }
                throw new ApplicationException($"{ex.Message}.", ex);
            }

            return output;
        }
        // oledb version
        //private DataSet OpenExcel2(bool returnall, int sheetIndex)
        //{

        //    //string HDR = hasHeaders ? "Yes" : "No";
        //    string HDR = "No";
        //    string strConn = "Provider=Microsoft.ACE.OLEDB.16.0;Data Source=" +
        //            FileName + ";Extended Properties=\"Excel 12.0;HDR=" +
        //            HDR + ";MaxScanRows=0;IMEX=1;\"";

        //    //string strConn = "Provider=Microsoft.ACE.OLEDB.16.0;Data Source=" +
        //    //        FileName + ";Extended Properties=\"Excel 12.0;HDR=" +
        //    //        HDR + ";MaxScanRows=15;IMEX=1;\"";

        //    //This  is change
        //    DataSet output = new DataSet();
        //    try
        //    {
        //        using (OleDbConnection conn = new OleDbConnection(strConn))
        //        {
        //            conn.Open();

        //            DataTable schemaTable = conn.GetOleDbSchemaTable(
        //                OleDbSchemaGuid.Tables, new object[] { null, null, null, "TABLE" });
        //            DataRow schemaRow = schemaTable.Rows[sheetIndex]; // only 1st sheet
        //            string sheet = schemaRow["TABLE_NAME"].ToString();

        //            //We need $ sign after the sheet if sheet does not have the name given by the user 
        //            //otherwise we will not see any data for certain Excel files
                    
        //            OleDbCommand cmd;
        //            if (!returnall)
        //            {
        //                //cmd = sheet.Contains("$") ? new OleDbCommand("SELECT TOP 5 * FROM [" + sheet + "]", conn) :
        //                //                                     new OleDbCommand("SELECT TOP 5 * FROM [" + sheet + "$]", conn);

        //                cmd = sheet.Contains("$") ? new OleDbCommand("SELECT TOP 5 * FROM [" + sheet + "]", conn) :
        //                                                     new OleDbCommand("SELECT TOP 5 * FROM [" + sheet + "$]", conn);

        //                //cmd = sheet.Contains("$") ? new OleDbCommand("SELECT TOP 5 * FROM [_2014_Maness_GE_Contributions_for_report$ ]", conn) :
        //                //                                    new OleDbCommand("SELECT TOP 5 * FROM [_2014_Maness_GE_Contributions_for_report$]", conn);
        //            }
        //            else
        //            {
        //                cmd = sheet.Contains("$") ? new OleDbCommand("SELECT * FROM [" + sheet + "]", conn) :
        //                                                     new OleDbCommand("SELECT * FROM [" + sheet + "$]", conn);
        //            }

        //            DataTable outputTable = new DataTable(sheet);
        //            output.Tables.Add(outputTable);
        //            using (OleDbDataAdapter adapter = new OleDbDataAdapter(cmd))
        //            {
        //                adapter.Fill(outputTable);
        //                // process table
        //            }

        //            DuplicateColumnNameCheck(outputTable);

        //            //We need this as now First row which hosts the Header will be created : Header NO option does not create any Header..
        //            outputTable = BuildHeadersFromFirstRowThenRemoveFirstRow(outputTable);

        //            //correct dates if file has 1904 based date (e.g., in Excel for Mac)
        //            if (date1904)
        //            {
        //                MacDateCorrection(outputTable);
        //            }
        //        }
        //    }
        //    catch (Exception ex)
        //    {
        //        if (File.Exists(logPath))
        //        {
        //            File.AppendAllText(logPath, $"[{DateTime.Now}] {ex.Message}\n");
        //        }
        //        throw new ApplicationException("Excel parsing failed. See logs.", ex);
        //    }
        //    return output;
        //}      

    }
}