﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Data;
using System.Linq;
using System.Web.Mvc;
using System.Data.Linq;
using System.ComponentModel.DataAnnotations.Schema;

using cmdiapp.n.core.Domain.ViewModels;
using cmdiapp.n.core.Areas.crm.Domain.Data;

namespace cmdiapp.n.core.Areas.crm.Domain.Models
{
    [Table("JOB")]
    public class JOB : _entityBase_crm, iItemType
    {
        [Key]
        public int JOBID { get; set; }

        public Int16? JOBTYPEID { get; set; }

        public Int16? UID { get; set; }

        public string SCRIPT { get; set; }

        public Int16? JOBSTATID { get; set; }

        public DateTime? QUEUEDON { get; set; }

        public DateTime? DONEON { get; set; }

        public string NOTIFYUSERID { get; set; }

        public DateTime? NOTIFIEDON { get; set; }

        public Byte QUEUED { get; set; }

        public string DESCRIP { get; set; }

        public DateTime? STARTON { get; set; }
    }

    [Table("jtJOB")]
    public class jtJOB : _entityBase_crm, iItemType
    {
        [Key]
        public int jtJOBID { get; set; }
        public int JOBID { get; set; }
        public int PID { get; set; }
    }
}
