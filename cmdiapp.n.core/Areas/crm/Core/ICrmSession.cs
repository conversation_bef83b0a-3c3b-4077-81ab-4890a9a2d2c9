﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace cmdiapp.n.core.Areas.crm.Core
{
    /// <summary>
    /// Wrapper interface for <see cref="crmSession"/>
    /// to allow dependency injection, so its consumers
    /// can be tested
    /// </summary>
    public interface ICrmSession
    {
        short? GetCurrentUid();

        string GetConfigValue(string configKey);

        // TODO: Add other methods in crmSession class
    }
}
