{
    "id": "fundraisingHome",
    "displayName": "Fundraising",
    "layoutCssClass": "fundraising-layout",
    "subtitle": "<lastUpdate>",
    "reCalculationUrl": "/crm/api/dashboard/fundraising/recalc",
    "dataSource": {
        "parameters": [
            {
                "name": "period",
                "value": "<period>"
            },
            {
                "name": "fund",
                "value": "<fund>"
            },
            {
                "name": "program",
                "value": "<program>"
            }
        ]
    },
    "panels": [
        {
            "id": "fundraisingNav",
            "component": "dashboardNav",
            "dataSource": {
                "parameters": [
                    {
                        "name": "dashboardId",
                        "value": "fundraisingHome"
                    }
                ],
                "serverSource": {
                    "location": "[dbo].[dsb_dashboardNav]"
                }
            }
        },
        {
            "id": "fundraisingEditableContainer",
            "component": "editableContainer",
            "data": true,
            "layoutCssClass": "fundraising-layout",
            "panels": [
                <<access item="OverallFundraisingTotals">
        {
            "id": "totalByFund",
            "component": "advancedPieChart",
            "dataSource": {
                "parameters": [
                    {
                        "name": "period",
                        "value": "<period>"
                    },
                    {
                        "name": "fund",
                        "value": "<fund>"
                    }
                ],
                "serverSource": {
                    "location": "[dbo].[dsb_fundraisingTotalByFund]"
                }
            }
        },
        <</access>>
        {
            "id": "totalByProgram",
            "component": "horizontalBarChart",
            "dataSource": {
                "parameters": [
                    {
                        "name": "period",
                        "value": "<period>"
                    },
                    {
                        "name": "fund",
                        "value": "<fund>"
                    },
                    {
                        "name": "program",
                        "value": "<program>"
                    }
                ],
                "serverSource": {
                    "location": "[dbo].[dsb_fundraisingTotalByProgram]"
                }
            }
        },
        {
            "id": "fundraisingNavInside",
            "component": "dashboardButtons",
            "dataSource": {
                "parameters": [
                    {
                        "name": "dashboardId",
                        "value": "fundraising-inside"
                    }
                ],
                "serverSource": {
                    "location": "[dbo].[dsb_dashboardNav]"
                }
            }
        },
        {
            "id": "dailyGivingTrend",
            "component": "fundraisingDailyGiving",
            "dataSource": {
                "parameters": [
                    {
                        "name": "fund",
                        "value": "<fund>"
                    },
                    {
                        "name": "program",
                        "value": "<program>"
                    }
                ],
                "serverSource": {
                    "location": "[dbo].[dsb_fundraisingDailyNumbers]"
                }
            }
        },
        {
            "id": "totals",
            "component": "iconNumber",
            "dataSource": {
                "parameters": [
                    {
                        "name": "period",
                        "value": "All"
                    },
                    {
                        "name": "fund",
                        "value": "All"
                    },
                    {
                        "name": "program",
                        "value": "All"
                    },
                    {
                        "name": "from",
                        "value": "fundraisingHome"
                    }
                ],
                "serverSource": {
                    "location": "[dbo].[dsb_fundraisingTotal]"
                }
            }
        },
        {
            "id": "yearlyDonorRetention",
            "component": "normalizedHorizontalBarChart",
            "dataSource": {
                "parameters": [],
                "serverSource": {
                    "location": "[dbo].[dsb_fundraisingYearlyDonorRetention]"
                }
            }
        },
        {
            "id": "dropdownsFlexGroup",
            "component": "flexGroup",
            "layoutCssClass": "fundraising-bnty-sliders",
            "data": {
                "styles": {
                    "flexDirection": "row",
                    "flexWrap": "wrap"
                }
            },
            "panels": [
                {
                    "id": "lowestGiftAmount",
                    "component": "rangeSlider",
                    "data": {
                        "parameterValue": 0,
                        "parameterName": "lowestGiftAmount",
                        "label": "Lowest Gift Amount",
                        "step": 100,
                        "min": 0,
                        "max": 5000,
                        "format": "currency",
                        "targetPanelIds": [
                            "lybntDropdown",
                            "sybntDropdown"
                        ]
                    }
                },
                {
                    "id": "lybntFlexGroup",
                    "component": "flexGroup",
                    "panels": [
                        {
                            "id": "thisYear",
                            "component": "rangeSlider",
                            "data": {
                                "parameterValue": "<currentFiscalYear>",
                                "parameterName": "thisYear",
                                "label": "This Year",
                                "step": 1,
                                "min": 2000,
                                "max": "<currentFiscalYear>",
                                "format": "string",
                                "targetPanelIds": [
                                    "lybntDropdown"
                                ]
                            }
                        },
                        {
                            "id": "lybntDropdown",
                            "component": "dropdown",
                            "panels": [
                                {
                                    "id": "lybntDropdownCard",
                                    "component": "DummyComponent",
                                    "dataSource": {
                                        "parameters": [
                                            {
                                                "name": "lowestGiftAmount",
                                                "value": 0
                                            },
                                            {
                                                "name": "thisYear",
                                                "value": "<currentFiscalYear>"
                                            },
                                            {
                                                "name": "onlyIncludeLastYear",
                                                "value": true
                                            },
                                            {
                                                "name": "startingYear",
                                                "value": 0
                                            }
                                        ],
                                        "serverSource": {
                                            "location": "[dbo].[dsb_fundraisingButNotThisYear]"
                                        }
                                    }
                                }
                            ]
                        }
                    ]
                },
                {
                    "id": "sybntFlexGroup",
                    "component": "flexGroup",
                    "panels": [
                        {
                            "id": "startingYear",
                            "component": "rangeSlider",
                            "data": {
                                "parameterValue": 2000,
                                "parameterName": "startingYear",
                                "label": "Starting year",
                                "step": 1,
                                "min": 2000,
                                "max": "<currentFiscalYear>",
                                "format": "string",
                                "targetPanelIds": [
                                    "sybntDropdown"
                                ]
                            }
                        },
                        {
                            "id": "sybntDropdown",
                            "component": "dropdown",
                            "panels": [
                                {
                                    "id": "sybntDropdownCard",
                                    "component": "DummyComponent",
                                    "dataSource": {
                                        "parameters": [
                                            {
                                                "name": "lowestGiftAmount",
                                                "value": 0
                                            },
                                            {
                                                "name": "thisYear",
                                                "value": "<currentFiscalYear>"
                                            },
                                            {
                                                "name": "onlyIncludeLastYear",
                                                "value": false
                                            },
                                            {
                                                "name": "startingYear",
                                                "value": 2000
                                            }
                                        ],
                                        "serverSource": {
                                            "location": "[dbo].[dsb_fundraisingButNotThisYear]"
                                        }
                                    }
                                }
                            ]
                        }
                    ]
                }
            ]
        },

        {
            "id": "top10PeopleSources",
            "component": "topN",
            "dataSource": {
                "parameters": [
                    {
                        "name": "dataPoint",
                        "value": "donation-top10source-ctd"
                    },
                    {
                        "name": "TopN",
                        "value": 5
                    },
                    {
                        "name": "backGroundColor",
                        "value": "white"
                    }
                ],
                "serverSource": {
                    "location": "[dbo].[dsb_fundraisingCtdTop10]"
                }
            }
        },
        
        {
            "id": "top10CountiesCtd",
            "component": "topN",
            "dataSource": {
                "parameters": [
                    {
                        "name": "dataPoint",
                        "value": "donation-top10county-ctd"
                    },
                    {
                        "name": "TopN",
                        "value": 5
                    },
                    {
                        "name": "backGroundColor",
                        "value": "white"
                    }
                ],
                "serverSource": {
                    "location": "[dbo].[dsb_fundraisingCtdTop10]"
                }
            }
        },
        {
            "id": "top10CitiesCtd",
            "component": "topN",
            "dataSource": {
                "parameters": [
                    {
                        "name": "dataPoint",
                        "value": "donation-top10city-ctd"
                    },
                    {
                        "name": "TopN",
                        "value": 5
                    },
                    {
                        "name": "backGroundColor",
                        "value": "white"
                    }
                ],
                "serverSource": {
                    "location": "[dbo].[dsb_fundraisingCtdTop10]"
                }
            }
        },
        {
            "id": "top10ZipCtd",
            "component": "topN",
            "dataSource": {
                "parameters": [
                    {
                        "name": "dataPoint",
                        "value": "donation-top10zip-ctd"
                    },
                    {
                        "name": "TopN",
                        "value": 5
                    },
                    {
                        "name": "backGroundColor",
                        "value": "white"
                    }
                ],
                "serverSource": {
                    "location": "[dbo].[dsb_fundraisingCtdTop10]"
                }
            }
        },
        {
            "id": "top10StateCtd",
            "component": "topN",
            "dataSource": {
                "parameters": [
                    {
                        "name": "dataPoint",
                        "value": "donation-top10state-ctd"
                    },
                    {
                        "name": "TopN",
                        "value": 5
                    },
                    {
                        "name": "backGroundColor",
                        "value": "white"
                    }
                ],
                "serverSource": {
                    "location": "[dbo].[dsb_fundraisingCtdTop10]"
                }
            }
        },
        {
            "id": "top10CDCtd",
            "component": "topN",
            "dataSource": {
                "parameters": [
                    {
                        "name": "dataPoint",
                        "value": "donation-top10CD-ctd"
                    },
                    {
                        "name": "TopN",
                        "value": 5
                    },
                    {
                        "name": "backGroundColor",
                        "value": "white"
                    }
                ],
                "serverSource": {
                    "location": "[dbo].[dsb_fundraisingCtdTop10]"
                }
            }
        },
        <<access item="incomeReports">
        {
            "id": "incomeReports",
            "component": "topN",
            "dataSource": {
                "parameters": [
                    {
                        "name": "sectionScopeId",
                        "value": 52
                    },
                    {
                        "name": "reportSection",
                        "value": "IncomeReports"
                    },
                    {
                        "name": "number",
                        "value": 5
                    }
                ],
                "serverSource": {
                    "type": "CSharp",
                    "location": "cmdiapp.n.core.Areas.dashboard.Domain.Models.dataFromGateway.getSectionReports"
                }
            }
        },
        </access>>

        {
            "id": "myMoves",
            "component": "topNDetailed",
            "customization": { "isHidden": true },
            "dataSource": {
                "parameters": [
                    {
                        "name": "uid",
                        "value": "<uid>"
                    }
                ],
                "serverSource": {
                    "location": "[dbo].[dsb_myMovesDetailed]"
                }
            }
        },
                {
                    "id": "myTasks",
                    "component": "topNDetailed",
                    "customization": { "isHidden": true },
                    "dataSource": {
                        "parameters": [
                            {
                                "name": "uid",
                                "value": "<uid>"
                            }
                        ],
                        "serverSource": {
                            "location": "[dbo].[dsb_myTasksDetailed]"
                        }
                    }
                },    
                <<access item="myReports">
                {
                    "id": "myReports",
                    "component": "topN",
                    "customization": { "isHidden": true },
                    "dataSource": {
                        "parameters": [
                            {
                                "name": "number",
                                "value": 5
                            }
                        ],
                        "serverSource": {
                            "type": "CSharp",
                            "location": "cmdiapp.n.core.Areas.dashboard.Domain.Models.dataFromGateway.myReports"
                        }
                    }
                },
                </access>>
                {
                    "id": "mySearches",
                    "component": "topN",
                    "customization": { "isHidden": true },
                    "dataSource": {
                        "parameters": [
                            {
                                "name": "number",
                                "value": 5
                            }
                        ],
                        "serverSource": {
                            "type": "CSharp",
                            "location": "cmdiapp.n.core.Areas.dashboard.Domain.Models.dataFromGateway.mySearches"
                        }
                    }
                },   
                <<access item="allReports">
                {
                    "id": "allReports",
                    "component": "topN",
                    "customization": { "isHidden": true },
                    "dataSource": {
                        "parameters": [
                            {
                                "name": "number",
                                "value": 5
                            }
                        ],
                        "serverSource": {
                            "type": "CSharp",
                            "location": "cmdiapp.n.core.Areas.dashboard.Domain.Models.dataFromGateway.allReports"
                        }
                    }
                },
                </access>>

                {
                    "id": "eventTotalRaisedGroup",
                    "component": "flexGroup",
                    "customization": { "isHidden": true },
                    "layoutCssClass": "event-card-group-layout",
                    "data": {
                        "styles": {
                            "flexDirection": "row",
                            "flexWrap": "wrap"
                        }
                    },
                    "panels": [
                        {
                            "id": "eventTotalRaised",
                            "component": "iconNumber",
                            "dataSource": {
                                "serverSource": {
                                    "location": "[dbo].[dsb_eventTotalRaised]"
                                }
                            }
                        }
                    ]
                }
            ]
        }

        
    ]
}
