﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using cmdiapp.n.core.Areas.datax.Domain.Data;
using cmdiapp.n.core.Areas.datax.Domain.Models;
using cmdiapp.n.core.Areas.datax.Domain.Models.ViewModels;
using cmdiapp.n.core.Core;

namespace cmdiapp.n.core.Areas.datax.Domain.Services.implementation
{
    /// <summary>
    /// An implementation of <see cref="ICallerService"/>
    /// </summary>
    public class CallerService : ICallerService
    {
        private I_entity_datax _entity;

        public CallerService(I_entity_datax entity)
        {
            _entity = entity;
        }

        public List<CallerDisplay> GetCallers()
        {
            bool sysAdmin = session.userSession.is_sysAdmin;
            return _entity.All<Caller>()
                .Where(c => (sysAdmin || c.active)
                    && !string.IsNullOrEmpty(c.displayConfig))
                .Select(c => new CallerDisplay()
                {
                    Id = c.Id,
                    IsActive = c.active,
                    ConfigJson = string.IsNullOrEmpty(c.displayConfig) ? "" : c.displayConfig
                }).ToList();
        }

        public CallerDisplay GetCallerAnedot()
        {
            return _entity.All<Caller>()
                .Where(c => c.active
                    && (c.callerKeyName == "anedot")
                    && !string.IsNullOrEmpty(c.displayConfig))
                .Select(c => new CallerDisplay()
                {
                    Id = c.Id,
                    ConfigJson = string.IsNullOrEmpty(c.displayConfig) ? "" : c.displayConfig
                }).FirstOrDefault();
        }
    }
}