﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Data;
using System.Linq;
using System.Web.Mvc;
using System.Data.Linq;
using System.ComponentModel.DataAnnotations.Schema;

using cmdiapp.n.core.Domain.ViewModels;
using cmdiapp.n.core.Areas.crm.Domain.Data;

namespace cmdiapp.n.core.Areas.crm.Domain.Models
{
    [Table("pmCLUB")]
    public class pmCLUB : _entityBase_crm
    {
        [Column]
        [Key]
        public int CLUBID { get; set; }

        [Column]
        [Required(ErrorMessage = "The Club Code is required.")]
        [StringLength(30, ErrorMessage = "Club Code cannot be longer than 30 characters.")]
        public string CLUBCODE { get; set; }

    }

    [Table("jtCLUB")]
    public class jtCLUB : _entityBase_crm, iItemType
    {
        [Column]
        [Key]
        public int JTCLUBID { get; set; }

        [Column]
        public int PID { get; set; }

        [Column]
        public int CLUBID { get; set; }

        [Column]
        public string SOLICITOR { get; set; }

        [Column]
        public string CANDIDATE { get; set; }

        [Column]
        public Nullable<DateTime> RNEWDTE { get; set; }

        [Column]
        public Nullable<Int16> cSTATUS { get; set; }

        [Column]
        public Byte PRIME { get; set; }

        [Column]
        public Byte CLOSED { get; set; }

        [Column]
        public bool OVERRIDE { get; set; }

        [Column("_updating_uid")]
        public Nullable<short> updating_uid { get; set; }
    }

    public class lkCLUBSTATR : _entityBase_crm
    {
        [Column]
        [Key]
        public Int16 CLUBSTATID { get; set; }

        [Column]
        public int CLUBID { get; set; }

        [Column]
        public string DESCRIP { get; set; }

    }

    [Table("lkCLUBSTAT")] 
    public class lkCLUBSTAT
    {
        [Column]
        [Key]
        public Int16 CLUBSTATID { get; set; }

        [Column]
        public int CLUBID { get; set; }

        [ForeignKey("CLUBID")]
        public virtual pmCLUB pmCLUB { get; set; }

        [Column]
        [Required(ErrorMessage = "The Club Status is required.")]
        [StringLength(50, ErrorMessage = "Club Status cannot be longer than 50 characters.")]
        public string DESCRIP { get; set; }

    }
    
    public class pmCLUB_ext
    {
        public int CLUBID { get; set; }

        public string CLUBCODE { get; set; }

        public int count_ { get; set; }

        public Int64 rowNo { get; set; }

    }

    public class lkCLUBSTAT_ext
    {
        public Int16 CLUBSTATID { get; set; }

        public int CLUBID { get; set; }

        public string CLUBCODE { get; set; }

        public string DESCRIP { get; set; }
                
        public int count_ { get; set; }

        public Int64 rowNo { get; set; }

    }

}