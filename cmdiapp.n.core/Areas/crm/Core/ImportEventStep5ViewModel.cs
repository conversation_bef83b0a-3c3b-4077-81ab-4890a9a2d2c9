﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Data;
using System.Web.Mvc;
using cmdiapp.n.core.Domain.Models;
using cmdiapp.n.core.Areas.crm.Domain.Models;

namespace cmdiapp.n.core.Areas.crm.Core
{
    public class ImportEventStep5ViewModel
    {
        public int? RECNO { get; set; }
        public string STATUS { get; set; }
        public int? ID { get; set; }
        public string RECTYPE { get; set; }
        public string PEOTYPE { get; set; }
        public string PREFIX { get; set; }
        public string FNAME { get; set; }
        public string MNAME { get; set; }
        public string LNAME { get; set; }
        public string SUFFIX { get; set; }
        public string EMPLOYER { get; set; }
        public string OCCUPATION { get; set; }
        //public string TITLE { get; set; }
        public string ADDRTYPE { get; set; }
        public string STREET { get; set; }
        public string ADDR1 { get; set; }
        public string ADDR2 { get; set; }
        public string CITY { get; set; }
        public string STATE { get; set; }
        public string ZIP { get; set; }
        public string PLUS4 { get; set; }
        //public string BSTREET { get; set; }
        //public string BADDR1 { get; set; }
        //public string BADDR2 { get; set; }
        //public string BCITY { get; set; }
        //public string BSTATE { get; set; }
        //public string BZIP { get; set; }
        //public string BPLUS4 { get; set; }
        //public string HMPHONE { get; set; }
        //public string BSPHONE { get; set; }
        public string FAX { get; set; }
        public string CELLPHONE { get; set; }
        public string EMAIL { get; set; }
        //public string SecondaryEMAIL { get; set; }
        //public string URL { get; set; }
        public string SPOUSENAME { get; set; }
        //public string PRIMEMAIL { get; set; }
        //public string INFSALUT { get; set; }
        //public string SALUTATION { get; set; }
        //public string MAILSALUTATION { get; set; }
        //public string MAILNAME { get; set; }
        //public string ASSISTANT { get; set; }
        //public string ASSTBSPHONE { get; set; }
        //public string ASSTEMAIL { get; set; }
        //public DateTime? DOB { get; set; }
        //public string CPREFIX { get; set; }
        //public string CFNAME { get; set; }
        //public string CMNAME { get; set; }
        //public string CLNAME { get; set; }
        //public string CSUFFIX { get; set; }
        //public string CTITLE { get; set; }
        //public string cSTREET { get; set; }
        //public string cADDR1 { get; set; }
        //public string cADDR2 { get; set; }
        //public string cCITY { get; set; }
        //public string cSTATE { get; set; }
        //public string cZIP { get; set; }
        //public string cPLUS4 { get; set; }
        //public string cEMAIL { get; set; }
        //public string cHMPHONE { get; set; }
        //public string cBSPHONE { get; set; }
        //public string cFAX { get; set; }
        //public string cCELLPHONE { get; set; }
        //public string cINFSALUT { get; set; }
        public string EVNTCODE { get; set; }
        public string EVNTSTATUS { get; set; }
        public string INVITEETYPE { get; set; }
        public DateTime? ATTENDDTE { get; set; }
        public string TABLENO { get; set; }
        public string ANSWER01 { get; set; }
        public string ANSWER02 { get; set; }
        public string ANSWER03 { get; set; }
        public string ANSWER04 { get; set; }
        public string ANSWER05 { get; set; }
        public string ANSWER06 { get; set; }
        public string ANSWER07 { get; set; }
        public string ANSWER08 { get; set; }
        public string ANSWER09 { get; set; }
        public string ANSWER10 { get; set; }
        public string ANSWER11 { get; set; }
        public string ANSWER12 { get; set; }
        public string ANSWER13 { get; set; }
        public string ANSWER14 { get; set; }
        public string ANSWER15 { get; set; }
        public string ANSWER16 { get; set; }
        public string ANSWER17 { get; set; }
        public string ANSWER18 { get; set; }
        public string ANSWER19 { get; set; }
        public string ANSWER20 { get; set; }
        public string ANSWER21 { get; set; }
        public string ANSWER22 { get; set; }
        public string ANSWER23 { get; set; }
        public string ANSWER24 { get; set; }
        public string ANSWER25 { get; set; }
        //public string FLAG1 { get; set; }
        //public string FLAG2 { get; set; }
        //public string FLAG3 { get; set; }
        //public string FLAG4 { get; set; }
        //public string FLAG5 { get; set; }
        //public string FLAG6 { get; set; }
        //public string FLAG7 { get; set; }
        //public string FLAG8 { get; set; }
        //public string FLAG9 { get; set; }
        //public string FLAG10 { get; set; }
        //public string KEYWORD1 { get; set; }
        //public string KEYWORD2 { get; set; }
        //public string KEYWORD3 { get; set; }
        //public string KEYWORD4 { get; set; }
        //public string KEYWORD5 { get; set; }
        //public string KEYWORD6 { get; set; }
        //public string KEYWORD7 { get; set; }
        //public string KEYWORD8 { get; set; }
        //public string KEYWORD9 { get; set; }
        //public string KEYWORD10 { get; set; }
    }

}
   