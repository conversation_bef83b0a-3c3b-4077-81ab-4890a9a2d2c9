﻿using cmdiapp.n.core.Areas.query.Domain.Models;
using System;

namespace cmdiapp.n.core.Areas.crm.Domain.Models
{
    public class PeopleLGiftDateSpecificationFilter : IExternalApiSpecificationFilter
    {
        private const string LGiftDateFilterId = "pspgs_ldte";
        private const int DateOnAfterIndex = 2;
        private const int DateOnBeforeIndex = 3;
        private const int DateBetweenIndex = 4;

        public DateTime? StartDate { get; }
        public DateTime? EndDate { get; }

        public PeopleLGiftDateSpecificationFilter(DateTime? startDate, DateTime? endDate)
        {
            StartDate = startDate;
            EndDate = endDate;
        }

        public QueryFilterInstance ConvertToQueryInstanceFilter()
        {
            var filter = new QueryFilterInstance { key = LGiftDateFilterId };
            if (StartDate == null && EndDate == null)
            {
                filter.operatorIdx = DateOnAfterIndex;
                // Choose a date that will not filter out any records.
                filter.values = new string[1] { FormatDate(new DateTime(1800, 1, 1)) };
                return filter;
            }

            if (EndDate == null)
            {
                filter.operatorIdx = DateOnAfterIndex;
                filter.values = new string[1] { FormatDate(StartDate.Value) };
                return filter;
            }

            if (StartDate == null)
            {
                filter.operatorIdx = DateOnBeforeIndex;
                filter.values = new string[1] { FormatDate(EndDate.Value) };
                return filter;
            }

            filter.operatorIdx = DateBetweenIndex;
            filter.values = new string[2] { FormatDate(StartDate.Value), FormatDate(EndDate.Value) };
            return filter;
        }

        private string FormatDate(DateTime date)
        {
            return ExternalApiSpecificationUtil.FormatDate(date);
        }
    }
}