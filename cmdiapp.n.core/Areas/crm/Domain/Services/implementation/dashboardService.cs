﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

using Ninject;
using Ninject.Web.Mvc;

using cmdiapp.n.core.Core.App_Start;
using cmdiapp.n.core.Domain.ViewModels;
using cmdiapp.n.core.Areas.crm.Domain.Models;
using cmdiapp.n.core.Areas.crm.Domain.ViewModels;
using cmdiapp.n.core.Areas.crm.Domain.Repositories;

namespace cmdiapp.n.core.Areas.crm.Domain.Services
{
    public class dashboardService : IdashboardService
    {
        private I___unitOfWork _unitOfWork;
        private I__DBFactory _dbFactory;

        public dashboardService(I___unitOfWork unitOfWork, I__DBFactory dbFactory)
        {
            _unitOfWork = unitOfWork;
            _dbFactory = dbFactory;
        }

        public ytdTotal get_fundraising_ytdTotal()
        {
            var q = _dbFactory.getContext().Database.SqlQuery<ytdTotal>(String.Format("SELECT * FROM dbo.fn_z_rs_ytd_progress_latest()"));

            return q.FirstOrDefault();
        }

        public List<ytdByProgram> get_fundraising_ytdByProgram()
        {
            var q = _dbFactory.getContext().Database.SqlQuery<ytdByProgram>(String.Format("SELECT * FROM dbo.fn_z_rs_ytd_progress_ByProgram()"));

            return q.ToList();
        }

        public peopleCount get_peopleCount()
        {
            // NOTE.  This needs to be re-written
            string sql = @"
                    SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED

                    SELECT
                        GETDATE() AS RUNTIME,
	                    (SELECT TOP 1 [count] FROM dbd_numbers WHERE dataPoint='People' AND period='*All' AND label='*All' ORDER BY [timestamp] DESC) AS [no_allPeople],
	                    (SELECT TOP 1 [count] FROM dbd_numbers WHERE dataPoint='Donors' AND period='*All' ORDER BY [timestamp] DESC) AS [no_donors],
	                    (SELECT TOP 1 [count] FROM dbd_numbers WHERE dataPoint='Donors' AND period='CTD' ORDER BY [timestamp] DESC) AS [no_currenCycleDonors],
	                    (SELECT TOP 1 [count] FROM dbd_numbers WHERE dataPoint='Donors' AND period='YTD' ORDER BY [timestamp] DESC) AS [no_ytdDonors]
            ";

            var q = _dbFactory.getContext().Database.SqlQuery<peopleCount>(sql);
            return q.FirstOrDefault();
        }

        #region [[ List<dbd_number>.get_BreakdownByProgram ]]
        public List<dbd_number> get_BreakdownByProgram()
        {
            string sql = string.Format("SELECT * FROM dbo.fn_dbd_numbers_donations_byProgram('','')");

            var q = _dbFactory.getContext().Database.SqlQuery<dbd_number>(sql);
            return q.ToList();
        }
        #endregion

        #region [[ List<dbd_number>.get_BreakdownByMonyType ]]
        public List<dbd_number> get_BreakdownByMonyType()
        {
            string sql = string.Format("SELECT * FROM dbo.fn_dbd_numbers_donations_byMonyType('','')");

            var q = _dbFactory.getContext().Database.SqlQuery<dbd_number>(sql);
            return q.ToList();
        }
        #endregion

        #region [[ List<dbd_number>.get_BreakdownByFund ]]
        public List<dbd_number> get_BreakdownByFund()
        {
            string sql = string.Format("SELECT * FROM dbo.fn_dbd_numbers_donations_byFund('')");

            var q = _dbFactory.getContext().Database.SqlQuery<dbd_number>(sql);
            return q.ToList();
        }
        #endregion

        #region [[ List<dbd_number>.get_BreakdownByMonyType ]] Retrieve all daily totals for all 'Period' options.
        public List<dbd_number> get_dailyTotals()
        {
            string sql = "SELECT * FROM dbo.fn_dbd_numbers_donations_byProgram__byDate___allCompiled()";

            string sql_all = string.Format(sql, dbdPeriod.ALL);

            var q = _dbFactory.getContext().Database.SqlQuery<dbd_number>(sql);
            return q.ToList();
        }
        #endregion

        #region [[ List<dbd_number>.get_all_dbd_numbers ]]
        public List<dbd_number> get_all_dbd_numbers()
        {
            string sql = string.Format("SELECT * FROM dbo.dbd_numbers");

            var q = _dbFactory.getContext().Database.SqlQuery<dbd_number>(sql);
            return q.ToList();
        }
        #endregion

        #region [[ Compliance ]]

        public List<compliancebesteffortdata> get_bestEffort(string fundid, string fromdate, string todate)
        {
            string sql = "";
            if (fundid.Equals("") || fromdate.Equals("") || todate.Equals(""))
            {
                sql = "SELECT P.PID, CASE WHEN P.PEOTYPEID=1 THEN 'I' ELSE '-' END AS INDI, " +
                        "dbo.oVALIDOCCEMP(OCCUPATION,EMPLOYER) AS hasVOE " +
                        "INTO #T " +
                        "FROM PEOPLE p, MONY m, dmFUND f, ssSYSTEM s " +
                        "WHERE p.PID=m.PID AND m.COUNTER=1 AND m.FUNDID=f.FUNDID AND f.CYCLE=s.CURRCYCLE " +
                        "GROUP BY P.PID, CASE WHEN P.PEOTYPEID=1 THEN 'I' ELSE '-' END, " +
                        "dbo.oVALIDOCCEMP(OCCUPATION,EMPLOYER) " +
                        "HAVING SUM(AMT) > 200";
            }
            else
            {
                //fund - 27
                if (fundid.Split(',').Length == 1)
                {
                    sql = "SELECT P.PID, CASE WHEN P.PEOTYPEID=1 THEN 'I' ELSE '-' END AS INDI, " +
                        "dbo.oVALIDOCCEMP(OCCUPATION,EMPLOYER) AS hasVOE " +
                        "INTO #T " +
                        "FROM PEOPLE p, MONY m, dmFUND f, ssSYSTEM s " +
                        "WHERE p.PID=m.PID AND m.COUNTER=1 AND m.FUNDID=f.FUNDID AND f.CYCLE=s.CURRCYCLE AND " +
                        "EXISTS(SELECT MID FROM MONY WHERE PID = p.PID AND COUNTER = 1 AND FUNDID = " + fundid + " AND " +
                        "m.BATCHDTE BETWEEN '" + fromdate + "' AND '" + todate + "') " +
                        "GROUP BY P.PID, CASE WHEN P.PEOTYPEID=1 THEN 'I' ELSE '-' END, " +
                        "dbo.oVALIDOCCEMP(OCCUPATION,EMPLOYER) " +
                        "HAVING SUM(AMT) > 200";
                }
                else
                {
                    //fundid - 27,77 - ('27','77')
                    fundid = "('" + fundid.Replace(",", "','") + "')";

                    sql = "SELECT P.PID, CASE WHEN P.PEOTYPEID=1 THEN 'I' ELSE '-' END AS INDI, " +
                        "dbo.oVALIDOCCEMP(OCCUPATION,EMPLOYER) AS hasVOE " +
                        "INTO #T " +
                        "FROM PEOPLE p, MONY m, dmFUND f, ssSYSTEM s " +
                        "WHERE p.PID=m.PID AND m.COUNTER=1 AND m.FUNDID=f.FUNDID AND f.CYCLE=s.CURRCYCLE AND " +
                        "EXISTS(SELECT MID FROM MONY WHERE PID = p.PID AND COUNTER = 1 AND FUNDID in " + fundid + " AND " +
                        "m.BATCHDTE BETWEEN '" + fromdate + "' AND '" + todate + "') " +
                        "GROUP BY P.PID, CASE WHEN P.PEOTYPEID=1 THEN 'I' ELSE '-' END, " +
                        "dbo.oVALIDOCCEMP(OCCUPATION,EMPLOYER) " +
                        "HAVING SUM(AMT) > 200";
                }



            }

            sql += "\r\n SELECT 'Valid' as type, (SELECT COUNT(PID) FROM #T WHERE INDI='I' AND hasVOE=1) as cnt " +
                        "UNION " +
                        "SELECT 'Invalid' as type, (SELECT COUNT(PID) FROM #T WHERE INDI='I' AND hasVOE=0) as cnt " +
                        "UNION " +
                        "SELECT 'Non-Individual' as type, (SELECT COUNT(PID) FROM #T WHERE INDI<>'I') as cnt";
            sql += "\r\n DROP TABLE #T";

            var q = _dbFactory.getContext().Database.SqlQuery<compliancebesteffortdata>(sql);

            return q.ToList();
        }

        public List<complianceoverlimitdata> get_overLimit(string alldata)
        {
            string sql = "";

            if (alldata.Equals("Y"))
            {
                sql = @"
                            --===== PREP ENVIRONMENT
                            SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED
                            IF OBJECT_ID('TEMPDB..#FUND411') IS NOT NULL BEGIN DROP TABLE #FUND411 END
                            IF OBJECT_ID('TEMPDB..#OUTPUT') IS NOT NULL BEGIN DROP TABLE #OUTPUT END

                            --===== BUILD FUND TABLE
                            SELECT FUNDID, FUNDCODE, CYCLE, LIMIT_I, LIMIT_R, LIMIT_P, LIMIT_O, LIMIT_S, LIMIT_N, LIMIT_C, CYCLESTART
                            INTO #FUND411
                            FROM dmFUND D
                            WHERE ACTIVE = 1

                            CREATE INDEX IDX_FUNDID_FUNDID ON #FUND411 (FUNDID)

                            --===== BUILD OUTPUT TABLE
                            IF OBJECT_ID('TEMPDB..#OUTPUT') IS NOT NULL BEGIN DROP TABLE #OUTPUT END
                            IF OBJECT_ID('TEMPDB..#CODEDOUTPUT') IS NOT NULL BEGIN DROP TABLE #CODEDOUTPUT END

                            --Over Limit Donor
                            SELECT
                            IDENTITY(INT,1,1) LINE_NO,
                            P.PID,dbo.oFullName(0,'',P.FNAME,P.MNAME,P.LNAME,P.SUFFIX) NAME,F.FUNDCODE,F.FUNDID,
                            CASE WHEN F.CYCLE = '99' THEN SF.YTDAMT ELSE SF.CTDAMT END as CUMTOT
                            INTO #OUTPUT
                            FROM SUMMARYF SF
                            INNER JOIN #FUND411 F ON SF.FUNDID = F.FUNDID
                            INNER JOIN PEOPLE P ON SF.PID = P.PID
                            JOIN LKPEOTYPE T ON P.PEOTYPEID = T.PEOTYPEID
                            LEFT OUTER JOIN ADDRESS A ON P.PID = A.PID AND A.PRIME = 1
                            WHERE CASE WHEN F.CYCLE = '99' THEN SF.YTDAMT ELSE SF.CTDAMT END > (
                            SELECT TOP 1 CASE
                            WHEN T.PEOTYPE = 'I' THEN F.LIMIT_I
                            WHEN T.PEOTYPE = 'R' THEN F.LIMIT_R
                            WHEN T.PEOTYPE = 'P' THEN F.LIMIT_P
                            WHEN T.PEOTYPE = 'O' THEN F.LIMIT_O
                            WHEN T.PEOTYPE = 'S' THEN F.LIMIT_S
                            WHEN T.PEOTYPE = 'N' THEN F.LIMIT_N
                            WHEN T.PEOTYPE = 'C' THEN F.LIMIT_C
                            END
                            FROM #FUND411 F2 WHERE F.FUNDID = F2.FUNDID)
                            ORDER BY T.DESCRIP, P.PID, F.FUNDCODE

                            --Link with FUNDID & PID Get Coded Donor
                            SELECT DISTINCT X.PID, X.FUNDID
                            INTO #CODEDOUTPUT
                            FROM #OUTPUT X
                            INNER JOIN (MONY M INNER JOIN lkEXCEP E ON E.EXCEPID = M.EXCEPID)
                            ON X.PID = M.PID AND X.FUNDID = M.FUNDID AND M.COUNTER = 1 
                            WHERE E.EXCEP IN ('A1','B1','G1','K1','R1','U1','W1','X1','Y1','A2','B2','G2','K2','R2','W2','X2','Y2')

                            --Delete Coded Donor from #output
                            DELETE #OUTPUT
                            FROM #OUTPUT O INNER JOIN #CODEDOUTPUT U ON O.PID = U.PID AND O.FUNDID = U.FUNDID


                            --===== DISPLAY OUTPUT
                            SELECT X.PID, X.NAME, X.FUNDCODE, X.CUMTOT FROM #OUTPUT X 
                            ORDER BY X.LINE_NO

                            DROP TABLE #FUND411
                            DROP TABLE #OUTPUT
                            DROP TABLE #CODEDOUTPUT";
            }
            else
            {
                sql = @"
                            --===== PREP ENVIRONMENT
                            SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED
                            IF OBJECT_ID('TEMPDB..#FUND411') IS NOT NULL BEGIN DROP TABLE #FUND411 END
                            IF OBJECT_ID('TEMPDB..#OUTPUT') IS NOT NULL BEGIN DROP TABLE #OUTPUT END

                            --===== BUILD FUND TABLE
                            SELECT FUNDID, FUNDCODE, CYCLE, LIMIT_I, LIMIT_R, LIMIT_P, LIMIT_O, LIMIT_S, LIMIT_N, LIMIT_C, CYCLESTART
                            INTO #FUND411
                            FROM dmFUND D
                            WHERE ACTIVE = 1

                            CREATE INDEX IDX_FUNDID_FUNDID ON #FUND411 (FUNDID)

                            --===== BUILD OUTPUT TABLE
                            IF OBJECT_ID('TEMPDB..#OUTPUT') IS NOT NULL BEGIN DROP TABLE #OUTPUT END
                            IF OBJECT_ID('TEMPDB..#CODEDOUTPUT') IS NOT NULL BEGIN DROP TABLE #CODEDOUTPUT END

                            --Over Limit Donor
                            SELECT 
                            IDENTITY(INT,1,1) LINE_NO,
                            P.PID,dbo.oFullName(0,'',P.FNAME,P.MNAME,P.LNAME,P.SUFFIX) NAME,F.FUNDCODE,F.FUNDID,
                            CASE WHEN F.CYCLE = '99' THEN SF.YTDAMT ELSE SF.CTDAMT END as CUMTOT
                            INTO #OUTPUT
                            FROM SUMMARYF SF
                            INNER JOIN #FUND411 F ON SF.FUNDID = F.FUNDID
                            INNER JOIN PEOPLE P ON SF.PID = P.PID
                            JOIN LKPEOTYPE T ON P.PEOTYPEID = T.PEOTYPEID
                            LEFT OUTER JOIN ADDRESS A ON P.PID = A.PID AND A.PRIME = 1
                            WHERE CASE WHEN F.CYCLE = '99' THEN SF.YTDAMT ELSE SF.CTDAMT END > (
                            SELECT TOP 1 CASE
                            WHEN T.PEOTYPE = 'I' THEN F.LIMIT_I
                            WHEN T.PEOTYPE = 'R' THEN F.LIMIT_R
                            WHEN T.PEOTYPE = 'P' THEN F.LIMIT_P
                            WHEN T.PEOTYPE = 'O' THEN F.LIMIT_O
                            WHEN T.PEOTYPE = 'S' THEN F.LIMIT_S
                            WHEN T.PEOTYPE = 'N' THEN F.LIMIT_N
                            WHEN T.PEOTYPE = 'C' THEN F.LIMIT_C
                            END
                            FROM #FUND411 F2 WHERE F.FUNDID = F2.FUNDID)
                            ORDER BY T.DESCRIP, P.PID, F.FUNDCODE

                            --Link with FUNDID & PID Get Coded Donor
                            SELECT DISTINCT X.PID, X.FUNDID
                            INTO #CODEDOUTPUT
                            FROM #OUTPUT X
                            INNER JOIN (MONY M INNER JOIN lkEXCEP E ON E.EXCEPID = M.EXCEPID)
                            ON X.PID = M.PID AND X.FUNDID = M.FUNDID AND M.COUNTER = 1 
                            WHERE E.EXCEP IN ('A1','B1','G1','K1','R1','U1','W1','X1','Y1','A2','B2','G2','K2','R2','W2','X2','Y2')

                            --Delete Coded Donor from #output
                            DELETE #OUTPUT
                            FROM #OUTPUT O INNER JOIN #CODEDOUTPUT U ON O.PID = U.PID AND O.FUNDID = U.FUNDID


                            --===== DISPLAY OUTPUT
                            SELECT TOP 10 X.PID, X.NAME, X.FUNDCODE, X.CUMTOT FROM #OUTPUT X 
                            ORDER BY X.LINE_NO

                            DROP TABLE #FUND411
                            DROP TABLE #OUTPUT
                            DROP TABLE #CODEDOUTPUT";
            }

            var q = _dbFactory.getContext().Database.SqlQuery<complianceoverlimitdata>(sql);

            return q.ToList();
        }

        public int get_pacswithnofecidcount()
        {
            string sql = @" SELECT COUNT(P.PID) as TOTALCOUNT
                                    FROM PEOPLE P
        	                            INNER JOIN lkPEOTYPE T ON P.PEOTYPEID = T.PEOTYPEID
        	                            LEFT OUTER JOIN ADDRESS A ON P.PID = A.PID AND A.PRIME = 1  	
        	                            INNER JOIN SUMMARYD SD ON P.PID = SD.PID  
        	                            LEFT OUTER JOIN CONTACT C ON P.PID = C.PID AND C.PRIME=1
                                    WHERE T.PEOTYPE IN ('P','0') AND ISNULL(P.FECCMTEID,'') = ''
                                    ";
            var q = _dbFactory.getContext().Database.SqlQuery<int>(sql);

            return q.First();
        }

//        public List<pacswithnofecid> get_pacswithnofecid()
//        {
//            string sql = @" SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED 
//        
//                                    SELECT 
//        	                        P.PID,
//                                    ISNULL(dbo.oFULLNAME(0,P.PREFIX,P.FNAME,P.MNAME,P.LNAME,P.SUFFIX),'') NAME,	
//                                    ISNULL(A.STREET,'') AS STREET, 
//                                    ISNULL(A.CITY,'') AS CITY,
//                                    ISNULL(A.STATE,'') AS [STATE],
//                                    ISNULL(dbo.oDATEPART(SD.LGIFTDTE),'') AS RECENTGIFTDATE,
//                                    CONVERT(NUMERIC(12,2),ISNULL(SD.CTDAMT,0)) AS CTDAMT,
//                                    CONVERT(NUMERIC(12,2),ISNULL(SD.YTDAMT,0)) AS YTDAMT
//                                    FROM PEOPLE P
//        	                            INNER JOIN lkPEOTYPE T ON P.PEOTYPEID = T.PEOTYPEID
//        	                            LEFT OUTER JOIN ADDRESS A ON P.PID = A.PID AND A.PRIME = 1  	
//        	                            INNER JOIN SUMMARYD SD ON P.PID = SD.PID  
//        	                            LEFT OUTER JOIN CONTACT C ON P.PID = C.PID AND C.PRIME=1
//                                    WHERE T.PEOTYPE IN ('P','0') AND ISNULL(P.FECCMTEID,'') = ''
//                                   ";
//            var q = _dbFactory.getContext().Database.SqlQuery<pacswithnofecid>(sql);

//            return q.ToList();
//        }

        public int get_irregularnamecount()
        {
            string sql = @" SELECT COUNT(P.PID) AS TOTALCOUNT
                                    FROM PEOPLE P
        	                            LEFT OUTER JOIN ADDRESS A ON P.PID = A.PID AND A.PRIME = 1 
                                    WHERE 
                                    (
                                    (PREFIX LIKE '%  %' OR PREFIX LIKE '%&%' OR PREFIX LIKE '% and %' OR CHARINDEX(PREFIX,',') NOT IN (0) OR CHARINDEX(PREFIX,';') NOT IN (0) OR CHARINDEX(PREFIX,'/') NOT IN (0) OR CHARINDEX(PREFIX,'\') NOT IN (0) OR ISNUMERIC(SUBSTRING(PREFIX,1,1)) = 1 AND LEFT(PREFIX,1) NOT LIKE '[0-9]')
                                    OR
                                    (FNAME LIKE '%&%'  OR FNAME LIKE '%  %' OR FNAME LIKE '% and %' OR CHARINDEX(FNAME,'.') NOT IN (0,2) OR CHARINDEX(FNAME,',') NOT IN (0) OR CHARINDEX(FNAME,';') NOT IN (0) OR CHARINDEX(FNAME,'/') NOT IN (0) OR CHARINDEX(FNAME,'\') NOT IN (0) OR ISNUMERIC(SUBSTRING(FNAME,1,1)) = 1 )
                                    OR
                                    (MNAME LIKE '%  %' OR MNAME LIKE '%&%'  OR MNAME LIKE '% and %' OR CHARINDEX(MNAME,'.') NOT IN (0,2) OR CHARINDEX(MNAME,',') NOT IN (0) OR CHARINDEX(MNAME,';') NOT IN (0) OR CHARINDEX(MNAME,'/') NOT IN (0) OR CHARINDEX(MNAME,'\') NOT IN (0) OR ISNUMERIC(SUBSTRING(MNAME,1,1)) = 1 )
                                    OR
                                    (LNAME LIKE '%  %' OR CHARINDEX(LNAME,'.') NOT IN (0) OR CHARINDEX(LNAME,',') NOT IN (0) OR CHARINDEX(LNAME,';') NOT IN (0) OR CHARINDEX(LNAME,'/') NOT IN (0) OR CHARINDEX(LNAME,'\') NOT IN (0) OR ISNUMERIC(SUBSTRING(LNAME,1,1)) = 1 )
                                    )
                                  ";
            var q = _dbFactory.getContext().Database.SqlQuery<int>(sql);

            return q.First();
        }

//        public List<irregularname> get_irregularname()
//        {
//            string sql = @" SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED
//        
//                                    SELECT 
//                                    CASE 
//        	                            WHEN PREFIX LIKE '%  %' THEN 'PREFIX'
//        	                            WHEN PREFIX LIKE '%&%' THEN 'PREFIX'
//        	                            WHEN PREFIX LIKE '% and %' THEN 'PREFIX'
//        	                            WHEN CHARINDEX(PREFIX,',') NOT IN (0) THEN 'PREFIX'
//        	                            WHEN CHARINDEX(PREFIX,';') NOT IN (0) THEN 'PREFIX'
//        	                            WHEN CHARINDEX(PREFIX,'/') NOT IN (0) THEN 'PREFIX'
//        	                            WHEN CHARINDEX(PREFIX,'\') NOT IN (0) THEN 'PREFIX'
//        	                            WHEN ISNUMERIC(SUBSTRING(PREFIX,1,1)) = 1 THEN 'PREFIX'
//        	                            WHEN FNAME LIKE '%&%' THEN 'FNAME'
//        	                            WHEN FNAME LIKE '%  %' THEN 'FNAME'
//        	                            WHEN FNAME LIKE '% and %' THEN 'FNAME'
//        	                            WHEN CHARINDEX(FNAME,'.') NOT IN (0,2) THEN 'FNAME'
//        	                            WHEN CHARINDEX(FNAME,',') NOT IN (0) THEN 'FNAME'
//        	                            WHEN CHARINDEX(FNAME,';') NOT IN (0) THEN 'FNAME'
//        	                            WHEN CHARINDEX(FNAME,'/') NOT IN (0) THEN 'FNAME'
//        	                            WHEN CHARINDEX(FNAME,'\') NOT IN (0) THEN 'FNAME'
//        	                            WHEN ISNUMERIC(SUBSTRING(FNAME,1,1)) = 1 THEN 'FNAME'
//        	                            WHEN MNAME LIKE '%  %' THEN 'MNAME'
//        	                            WHEN MNAME LIKE '%&%'  THEN 'MNAME'
//        	                            WHEN MNAME LIKE '% and %'  THEN 'MNAME'
//        	                            WHEN CHARINDEX(MNAME,'.') NOT IN (0,2) THEN 'MNAME'
//        	                            WHEN CHARINDEX(MNAME,',') NOT IN (0) THEN 'MNAME'
//        	                            WHEN CHARINDEX(MNAME,';') NOT IN (0) THEN 'MNAME'
//        	                            WHEN CHARINDEX(MNAME,'/') NOT IN (0) THEN 'MNAME'
//        	                            WHEN CHARINDEX(MNAME,'\') NOT IN (0) THEN 'MNAME'
//        	                            WHEN ISNUMERIC(SUBSTRING(MNAME,1,1)) = 1 THEN 'MNAME'
//        	                            WHEN LNAME LIKE '%  %' THEN 'LNAME'
//        	                            WHEN CHARINDEX(LNAME,'.') NOT IN (0) THEN 'LNAME'
//        	                            WHEN CHARINDEX(LNAME,',') NOT IN (0) THEN 'LNAME'
//        	                            WHEN CHARINDEX(LNAME,';') NOT IN (0) THEN 'LNAME'
//        	                            WHEN CHARINDEX(LNAME,'/') NOT IN (0) THEN 'LNAME'
//        	                            WHEN CHARINDEX(LNAME,'\') NOT IN (0) THEN 'LNAME'
//        	                            WHEN ISNUMERIC(SUBSTRING(LNAME,1,1)) = 1 THEN 'LNAME'
//                                    END [BAD_FIELD] ,
//                                    CASE
//        	                            WHEN PREFIX LIKE '%  %' THEN '[  ] Exists'
//        	                            WHEN PREFIX LIKE '%&%' THEN '[&] Exists'
//        	                            WHEN PREFIX LIKE '% and %' THEN '[and] Exists'
//        	                            WHEN CHARINDEX(PREFIX,',') NOT IN (0) THEN '[,] Exists'
//        	                            WHEN CHARINDEX(PREFIX,';') NOT IN (0) THEN '[;] Exists'
//        	                            WHEN CHARINDEX(PREFIX,'/') NOT IN (0) THEN '[/] Exists'
//        	                            WHEN CHARINDEX(PREFIX,'\') NOT IN (0) THEN '[\] Exists'
//        	                            WHEN ISNUMERIC(SUBSTRING(PREFIX,1,1)) = 1 THEN 'First Byte Numeric'
//        	                            WHEN FNAME LIKE '%&%' THEN '[&] Exists'
//        	                            WHEN FNAME LIKE '%  %' THEN '[  ] Exists'
//        	                            WHEN FNAME LIKE '% and %' THEN '[and] Exists'
//        	                            WHEN CHARINDEX(FNAME,'.') NOT IN (0,2) THEN '[.] Not 2nd Position'
//        	                            WHEN CHARINDEX(FNAME,',') NOT IN (0) THEN '[,] Exists'
//        	                            WHEN CHARINDEX(FNAME,';') NOT IN (0) THEN '[;] Exists'
//        	                            WHEN CHARINDEX(FNAME,'/') NOT IN (0) THEN '[/] Exists'
//        	                            WHEN CHARINDEX(FNAME,'\') NOT IN (0) THEN '[\] Exists'
//        	                            WHEN ISNUMERIC(SUBSTRING(FNAME,1,1)) = 1 THEN 'First Byte Numeric'
//        	                            WHEN MNAME LIKE '%  %' THEN '[  ] Exists'
//        	                            WHEN MNAME LIKE '%&%'  THEN '[&] Exists'
//        	                            WHEN MNAME LIKE '% and %'  THEN '[and] Exists'
//        	                            WHEN CHARINDEX(MNAME,'.') NOT IN (0,2) THEN '[.] Not 2nd Position'
//        	                            WHEN CHARINDEX(MNAME,',') NOT IN (0) THEN '[,] Exists'
//        	                            WHEN CHARINDEX(MNAME,';') NOT IN (0) THEN '[;] Exists'
//        	                            WHEN CHARINDEX(MNAME,'/') NOT IN (0) THEN '[/] Exists'
//        	                            WHEN CHARINDEX(MNAME,'\') NOT IN (0) THEN '[\] Exists'
//        	                            WHEN ISNUMERIC(SUBSTRING(MNAME,1,1)) = 1 THEN 'First Byte Numeric'
//        	                            WHEN LNAME LIKE '%  %' THEN '[  ] Exists'
//        	                            WHEN CHARINDEX(LNAME,'.') NOT IN (0) THEN '[.] Exists'
//        	                            WHEN CHARINDEX(LNAME,',') NOT IN (0) THEN '[,] Exists'
//        	                            WHEN CHARINDEX(LNAME,';') NOT IN (0) THEN '[;] Exists'
//        	                            WHEN CHARINDEX(LNAME,'/') NOT IN (0) THEN '[/] Exists'
//        	                            WHEN CHARINDEX(LNAME,'\') NOT IN (0) THEN '[\] Exists'
//        	                            WHEN ISNUMERIC(SUBSTRING(LNAME,1,1)) = 1 THEN 'First Byte Numeric'
//                                    END [REASON],
//                                    P.PID,
//                                    ISNULL(P.PREFIX,'') PREFIX,
//                                    ISNULL(P.FNAME,'') AS FNAME, 
//                                    ISNULL(P.MNAME,'') MNAME,
//                                    ISNULL(P.LNAME,'') LNAME 
//                                    FROM PEOPLE P
//        	                            LEFT OUTER JOIN ADDRESS A ON P.PID = A.PID AND A.PRIME = 1 
//                                    WHERE 
//                                    (
//                                    (PREFIX LIKE '%  %' OR PREFIX LIKE '%&%' OR PREFIX LIKE '% and %' OR CHARINDEX(PREFIX,',') NOT IN (0) OR CHARINDEX(PREFIX,';') NOT IN (0) OR CHARINDEX(PREFIX,'/') NOT IN (0) OR CHARINDEX(PREFIX,'\') NOT IN (0) OR ISNUMERIC(SUBSTRING(PREFIX,1,1)) = 1 AND LEFT(PREFIX,1) NOT LIKE '[0-9]')
//                                    OR
//                                    (FNAME LIKE '%&%'  OR FNAME LIKE '%  %' OR FNAME LIKE '% and %' OR CHARINDEX(FNAME,'.') NOT IN (0,2) OR CHARINDEX(FNAME,',') NOT IN (0) OR CHARINDEX(FNAME,';') NOT IN (0) OR CHARINDEX(FNAME,'/') NOT IN (0) OR CHARINDEX(FNAME,'\') NOT IN (0) OR ISNUMERIC(SUBSTRING(FNAME,1,1)) = 1 )
//                                    OR
//                                    (MNAME LIKE '%  %' OR MNAME LIKE '%&%'  OR MNAME LIKE '% and %' OR CHARINDEX(MNAME,'.') NOT IN (0,2) OR CHARINDEX(MNAME,',') NOT IN (0) OR CHARINDEX(MNAME,';') NOT IN (0) OR CHARINDEX(MNAME,'/') NOT IN (0) OR CHARINDEX(MNAME,'\') NOT IN (0) OR ISNUMERIC(SUBSTRING(MNAME,1,1)) = 1 )
//                                    OR
//                                    (LNAME LIKE '%  %' OR CHARINDEX(LNAME,'.') NOT IN (0) OR CHARINDEX(LNAME,',') NOT IN (0) OR CHARINDEX(LNAME,';') NOT IN (0) OR CHARINDEX(LNAME,'/') NOT IN (0) OR CHARINDEX(LNAME,'\') NOT IN (0) OR ISNUMERIC(SUBSTRING(LNAME,1,1)) = 1 )
//                                    )
//                                    ORDER BY P.PID
//                                   ";
//            var q = _dbFactory.getContext().Database.SqlQuery<irregularname>(sql);

//            return q.ToList();
//        }

//        public List<llcnoattribution> get_partnershipwithoutattribution()
//        {
//            string sql = @" SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED
//        
//                                    SELECT DISTINCT 
//                                    P.PID,
//                                    ISNULL(dbo.oFULLNAME(0,P.PREFIX,P.FNAME,P.MNAME,P.LNAME,P.SUFFIX),'') NAME,   
//                                    ISNULL(A.STREET,'') AS STREET, 
//                                    ISNULL(A.ADDR1,'') AS ADDR1,
//                                    ISNULL(A.ADDR2,'') AS ADDR2,
//                                    ISNULL(A.CITY,'') AS CITY,
//                                    ISNULL(A.STATE,'') AS [STATE],
//                                    ISNULL(A.ZIP,'') AS ZIP,
//                                    ISNULL(P.FECCMTEID,'') AS FECID,
//                                    ISNULL((SELECT TOP 1 PH.PHNNO FROM PHONE PH WHERE PH.PID = P.PID AND PH.PRIME = 1 AND PH.PHNTYPEID = 2),'') BSPHN,
//                                    ISNULL((SELECT TOP 1 PH.PHNNO FROM PHONE PH WHERE PH.PID = P.PID AND PH.PRIME = 1 AND PH.PHNTYPEID = 4),'') EMAIL,
//                                    ISNULL(dbo.oDATEPART(SD.LGIFTDTE),'') AS RECENTGIFTDATE,
//                                    CONVERT(NUMERIC(12,2),ISNULL(SD.CTDAMT,0)) AS CTDAMT,
//                                    CONVERT(NUMERIC(12,2),ISNULL(SD.YTDAMT,0)) AS YTDAMT,
//                                    ISNULL(C.FNAME,'') AS CON_FNAME,
//                                    ISNULL(C.LNAME,'') AS CON_LNAME,
//                                    ISNULL(C.EMAIL,'') AS CON_EMAIL
//                                    FROM PEOPLE P
//                                          INNER JOIN lkPEOTYPE T ON P.PEOTYPEID = T.PEOTYPEID
//                                          LEFT OUTER JOIN ADDRESS A ON P.PID = A.PID AND A.PRIME = 1        
//                                          INNER JOIN SUMMARYD SD ON P.PID = SD.PID  
//                                          LEFT OUTER JOIN CONTACT C ON P.PID = C.PID AND C.PRIME=1
//                                          INNER JOIN
//                                                (SELECT m.PID, m.MID, COUNT(m2.MID) as CT 
//                                                      FROM MONY m 
//                                                            INNER JOIN PEOPLE p 
//                                                                  INNER JOIN lkPEOTYPE t ON p.PEOTYPEID = t.PEOTYPEID AND t.PEOTYPE = 'R'
//                                                            ON m.PID = p.PID AND m.COUNTER = 1 AND m.SOFTMONEY = 0
//                                                            LEFT JOIN MONY m2 
//                                                                  INNER JOIN lkADJTYPE j ON m2.ADJTYPEID = j.ADJTYPEID AND j.ADJTYPE = 'PT'
//                                                            ON m.MID = m2.ORIGMID 
//                                                      GROUP BY m.PID, m.MID HAVING COUNT(m2.MID) = 0) R 
//                                                ON P.PID = R.PID 
//                                    ORDER BY NAME
//                                   ";
//            var q = _dbFactory.getContext().Database.SqlQuery<llcnoattribution>(sql);

//            return q.ToList();
//        }

        public int get_partnershipwithoutattributioncount()
        {
            string sql = @" SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED

                            SELECT DISTINCT 
                            P.PID,
                            ISNULL(dbo.oFULLNAME(0,P.PREFIX,P.FNAME,P.MNAME,P.LNAME,P.SUFFIX),'') NAME,   
                            ISNULL(A.STREET,'') AS STREET, 
                            ISNULL(A.ADDR1,'') AS ADDR1,
                            ISNULL(A.ADDR2,'') AS ADDR2,
                            ISNULL(A.CITY,'') AS CITY,
                            ISNULL(A.STATE,'') AS [STATE],
                            ISNULL(A.ZIP,'') AS ZIP,
                            ISNULL(P.FECCMTEID,'') AS FECID,
                            ISNULL((SELECT TOP 1 PH.PHNNO FROM PHONE PH WHERE PH.PID = P.PID AND PH.PRIME = 1 AND PH.PHNTYPEID = 2),'') BSPHN,
                            ISNULL((SELECT TOP 1 PH.PHNNO FROM PHONE PH WHERE PH.PID = P.PID AND PH.PRIME = 1 AND PH.PHNTYPEID = 4),'') EMAIL,
                            ISNULL(dbo.oDATEPART(SD.LGIFTDTE),'') AS RECENTGIFTDATE,
                            CONVERT(NUMERIC(12,2),ISNULL(SD.CTDAMT,0)) AS CTDAMT,
                            CONVERT(NUMERIC(12,2),ISNULL(SD.YTDAMT,0)) AS YTDAMT,
                            ISNULL(C.FNAME,'') AS CON_FNAME,
                            ISNULL(C.LNAME,'') AS CON_LNAME,
                            ISNULL(C.EMAIL,'') AS CON_EMAIL
                            FROM PEOPLE P
                                  INNER JOIN lkPEOTYPE T ON P.PEOTYPEID = T.PEOTYPEID
                                  LEFT OUTER JOIN ADDRESS A ON P.PID = A.PID AND A.PRIME = 1        
                                  INNER JOIN SUMMARYD SD ON P.PID = SD.PID  
                                  LEFT OUTER JOIN CONTACT C ON P.PID = C.PID AND C.PRIME=1
                                  INNER JOIN
                                        (SELECT m.PID, m.MID, COUNT(m2.MID) as CT 
                                              FROM MONY m 
                                                    INNER JOIN PEOPLE p 
                                                          INNER JOIN lkPEOTYPE t ON p.PEOTYPEID = t.PEOTYPEID AND t.PEOTYPE = 'R'
                                                    ON m.PID = p.PID AND m.COUNTER = 1 AND m.SOFTMONEY = 0
                                                    LEFT JOIN MONY m2 
                                                          INNER JOIN lkADJTYPE j ON m2.ADJTYPEID = j.ADJTYPEID AND j.ADJTYPE = 'PT'
                                                    ON m.MID = m2.ORIGMID 
                                              GROUP BY m.PID, m.MID HAVING COUNT(m2.MID) = 0) R 
                                        ON P.PID = R.PID 
                            ORDER BY NAME
                           ";
            var q = _dbFactory.getContext().Database.SqlQuery<llcnoattribution>(sql);

            return q.Count();
        }

        public int get_duplicateindividualcount()
        {
            #region [[ Full Query]]

            string _sql = @" SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED
                            IF OBJECT_ID('TEMPDB..#ALL') IS NOT NULL BEGIN DROP TABLE #ALL END
                            IF OBJECT_ID('TEMPDB..#ALL2') IS NOT NULL BEGIN DROP TABLE #ALL2 END
                            IF OBJECT_ID('TEMPDB..#ALL3') IS NOT NULL BEGIN DROP TABLE #ALL3 END
                            IF OBJECT_ID('TEMPDB..#ADDR') IS NOT NULL BEGIN DROP TABLE #ADDR END
                            IF OBJECT_ID('TEMPDB..#EMPL') IS NOT NULL BEGIN DROP TABLE #EMPL END
                            IF OBJECT_ID('TEMPDB..#PHON') IS NOT NULL BEGIN DROP TABLE #PHON END
                            IF OBJECT_ID('TEMPDB..#EMAI') IS NOT NULL BEGIN DROP TABLE #EMAI END
                            IF OBJECT_ID('TEMPDB..#PEOPLE') IS NOT NULL BEGIN DROP TABLE #PEOPLE END
                            IF OBJECT_ID('TEMPDB..#NAMEDUP') IS NOT NULL BEGIN DROP TABLE #NAMEDUP END
                            IF OBJECT_ID('TEMPDB..#OUTPUT') IS NOT NULL BEGIN DROP TABLE #OUTPUT END
                            --===== SUSPECT DUPLICATE 'NAME' FIRST
                            SELECT
                            CAST(LEFT(REPLACE(ISNULL(LNAME,''),' ',''),5) AS CHAR(5)) AS LNAMEK, 
                            CAST(LEFT(REPLACE(ISNULL(FNAME,''),' ',''),3) AS CHAR(3)) AS FNAMEK
                            INTO	#NAMEDUP
                            FROM 	PEOPLE
                            WHERE	LNAME<>'' AND PEOTYPEID IN (SELECT TOP 1 PEOTYPEID FROM lkPEOTYPE WHERE PEOTYPE = 'I')
                            GROUP BY
	                            CAST(LEFT(REPLACE(ISNULL(LNAME,''),' ',''),5) AS CHAR(5)),
	                            CAST(LEFT(REPLACE(ISNULL(FNAME,''),' ',''),3) AS CHAR(3))
                            HAVING COUNT(*)>1
                            CREATE INDEX IX_TTTNAMEDUP ON #NAMEDUP (LNAMEK, FNAMEK)

                            --===== CREATE PEOPLE INSTANCE OF 
                            SELECT
                            P.PID, A.LNAMEK, A.FNAMEK, CAST(LEFT(P.MNAME,1) AS CHAR(1)) AS MNAMEK,
                            dbo.oKEY_PREFIX(P.PREFIX) AS PREFIXK, CAST(LEFT(P.SUFFIX,3) AS CHAR(3)) AS SUFFIXK,
                            LEFT(LTRIM(RTRIM(P.EMPLOYER)),20) AS EMPLOYERK
                            INTO 	#PEOPLE
                            FROM 	#NAMEDUP A,
	                            PEOPLE P
                            WHERE	A.LNAMEK=LEFT(REPLACE(ISNULL(P.LNAME,''),' ',''),5) AND A.FNAMEK=LEFT(REPLACE(ISNULL(P.FNAME,''),' ',''),3)

                            CREATE CLUSTERED INDEX IX_TTTPEOPLE_NAME ON #PEOPLE (LNAMEK,FNAMEK,MNAMEK,PREFIXK,SUFFIXK)
                            CREATE INDEX IX_TTTPEOPLE_FNAMEK ON #PEOPLE (FNAMEK)
                            CREATE INDEX IX_TTTPEOPLE_M ON #PEOPLE (MNAMEK)
                            CREATE INDEX IX_TTTPEOPLE_P ON #PEOPLE (PREFIXK)
                            CREATE INDEX IX_TTTPEOPLE_S ON #PEOPLE (SUFFIXK)
                            CREATE INDEX IX_TTTPEOPLE_PID ON #PEOPLE (PID)

                            --===== ADDRESS DUP
                            SELECT DISTINCT
                            '1. ADDRESS' AS MATCHTYPE,
                            DP.PID AS PID1, 
                            MP.PID AS PID2
                            INTO	#ADDR
                            FROM 	#PEOPLE DP, PKEY DK,
	                            #PEOPLE MP, PKEY MK
                            WHERE	DP.PID<MP.PID AND
	                            DP.PID=DK.PID AND MP.PID=MK.PID AND
	                            DK.KEYTADDR = MK.KEYTADDR 
	                            AND	DP.LNAMEK=MP.LNAMEK AND DP.FNAMEK=MP.FNAMEK
                            CREATE INDEX IX_TTTADDR_PID1 ON #ADDR (PID1, PID2)
                            CREATE INDEX IX_TTTADDR_PID2 ON #ADDR (PID2, PID1)

                            --===== EMPLOYER DUP
                            SELECT  
                            '2. EMPLOYER' AS MATCHTYPE,
                            DP.PID AS PID1,
                            MP.PID AS PID2
                            INTO	#EMPL
                            FROM 	#PEOPLE DP,
	                            #PEOPLE MP
                            WHERE	DP.PID<MP.PID AND
	                            DP.LNAMEK=MP.LNAMEK AND DP.FNAMEK=MP.FNAMEK AND
	                            ISNULL(DP.EMPLOYERK,'') NOT IN ('','N/A','RETIRED','SELF','SELF-EMPLOYED','NONE','HOMEMAKER','NA','SELF EMPLOYED','UNEMPLOYED') AND DP.EMPLOYERK=MP.EMPLOYERK AND 
	                            ( ISNULL(DP.MNAMEK,'')='' OR ISNULL(MP.MNAMEK,'')='' OR DP.MNAMEK=MP.MNAMEK ) AND
	                            ( (ISNULL(DP.PREFIXK,'')='' AND ISNULL(MP.PREFIXK,'')<>'') OR 
	                              (ISNULL(MP.PREFIXK,'')='' AND ISNULL(DP.PREFIXK,'')<>'') OR 
	                              DP.PREFIXK=MP.PREFIXK ) AND
	                            ( ISNULL(DP.SUFFIXK,'')='' OR ISNULL(MP.SUFFIXK,'')='' OR DP.SUFFIXK=MP.SUFFIXK ) AND
	                            (SELECT COUNT(*) FROM #ADDR WHERE (PID1=DP.PID AND PID2=MP.PID) OR (PID1=MP.PID AND PID2=DP.PID)) = 0

                            CREATE INDEX IX_TTTEMPL_PID1 ON #EMPL (PID1, PID2)
                            CREATE INDEX IX_TTTEMPL_PID2 ON #EMPL (PID2, PID1)

                            --===== PHONE DUP
                            SELECT  
                            '3. H-PHONE' AS MATCHTYPE,
                            DP.PID AS PID1, 
                            MP.PID AS PID2 
                            INTO	#PHON
                            FROM 	#PEOPLE DP, PHONE DH,
	                            #PEOPLE MP, PHONE MH
                            WHERE	DP.PID<MP.PID AND
	                            DP.PID=DH.PID AND DH.PHNTYPEID=1 AND DH.PRIME=1 AND
	                            MP.PID=MH.PID AND MH.PHNTYPEID=1 AND MH.PRIME=1 AND
	                            DP.LNAMEK=MP.LNAMEK AND DP.FNAMEK=MP.FNAMEK AND 
	                            LEN(DH.PHNNO)>6 AND DH.PHNNO=MH.PHNNO AND
	                            ( ISNULL(DP.MNAMEK,'')='' OR ISNULL(MP.MNAMEK,'')='' OR DP.MNAMEK=MP.MNAMEK ) AND
	                            ( (ISNULL(DP.PREFIXK,'')='' AND ISNULL(MP.PREFIXK,'')<>'') OR 
	                              (ISNULL(MP.PREFIXK,'')='' AND ISNULL(DP.PREFIXK,'')<>'') OR 
	                              DP.PREFIXK=MP.PREFIXK ) AND
	                            ( ISNULL(DP.SUFFIXK,'')='' OR ISNULL(MP.SUFFIXK,'')='' OR DP.SUFFIXK=MP.SUFFIXK )

                            DELETE #PHON
                            FROM #PHON A
                            WHERE	(SELECT COUNT(*) FROM #ADDR WHERE (PID1=A.PID1 AND PID2=A.PID2) OR (PID1=A.PID2 AND PID2=A.PID1)) > 0 OR
	                            (SELECT COUNT(*) FROM #EMPL WHERE (PID1=A.PID1 AND PID2=A.PID2) OR (PID1=A.PID2 AND PID2=A.PID1)) > 0

                            CREATE INDEX IX_TTTPHON_PID1 ON #PHON (PID1, PID2)
                            CREATE INDEX IX_TTTPHON_PID2 ON #PHON (PID2, PID1)

                            --===== EMAIL DUP
                            SELECT  
                            '4. eMAIL' AS MATCHTYPE,
                            DP.PID AS PID1, 
                            MP.PID AS PID2 
                            INTO	#EMAI
                            FROM 	#PEOPLE DP, PHONE DH,
	                            #PEOPLE MP, PHONE MH
                            WHERE	DP.PID<MP.PID AND
	                            DP.PID=DH.PID AND DH.PHNTYPEID=4 AND DH.PRIME=1 AND
	                            MP.PID=MH.PID AND MH.PHNTYPEID=4 AND MH.PRIME=1 AND
	                            DP.LNAMEK=MP.LNAMEK AND DP.FNAMEK=MP.FNAMEK AND 
	                            LEN(DH.PHNNO)>6 AND DH.PHNNO=MH.PHNNO AND
	                            ( ISNULL(DP.MNAMEK,'')='' OR ISNULL(MP.MNAMEK,'')='' OR DP.MNAMEK=MP.MNAMEK ) AND
	                            ( (ISNULL(DP.PREFIXK,'')='' AND ISNULL(MP.PREFIXK,'')<>'') OR 
	                              (ISNULL(MP.PREFIXK,'')='' AND ISNULL(DP.PREFIXK,'')<>'') OR 
	                              DP.PREFIXK=MP.PREFIXK ) AND
	                            ( ISNULL(DP.SUFFIXK,'')='' OR ISNULL(MP.SUFFIXK,'')='' OR DP.SUFFIXK=MP.SUFFIXK )

                            DELETE #EMAI
                            FROM #EMAI A
                            WHERE	(SELECT COUNT(*) FROM #ADDR WHERE (PID1=A.PID1 AND PID2=A.PID2) OR (PID1=A.PID2 AND PID2=A.PID1)) > 0 OR
	                            (SELECT COUNT(*) FROM #EMPL WHERE (PID1=A.PID1 AND PID2=A.PID2) OR (PID1=A.PID2 AND PID2=A.PID1)) > 0 OR
	                            (SELECT COUNT(*) FROM #PHON WHERE (PID1=A.PID1 AND PID2=A.PID2) OR (PID1=A.PID2 AND PID2=A.PID1)) > 0 

                            SELECT * INTO #ALL FROM #ADDR
                            UNION SELECT * FROM #EMPL
                            UNION SELECT * FROM #PHON
                            UNION SELECT * FROM #EMAI

                            SELECT A.*,S1.CTDAMT AS CTDAMT1, S2.CTDAMT AS CTDAMT2 INTO #ALL2 FROM #ALL A left outer join SUMMARYD S1 on A.PID1 = S1.PID left outer join SUMMARYD S2 on A.PID2 = S2.PID

                            SELECT MATCHTYPE, CASE WHEN CTDAMT2>CTDAMT1 THEN PID2 ELSE PID1 END AS PID1, CASE WHEN CTDAMT2>CTDAMT1 THEN PID1 ELSE PID2 END AS PID2 INTO #ALL3 FROM #ALL2
                            CREATE INDEX IX_TTTALL3_PID1 ON #ALL3 (PID1)
                            CREATE INDEX IX_TTTALL3_PID2 ON #ALL3 (PID2)

                            --===== DUMP INTO FINAL OUTPUT TABLE
                            SELECT A.MATCHTYPE, 
                            DP.PID AS PID1, DP.PREFIX AS PREFIX1, DP.FNAME AS FNAME1, DP.MNAME AS MNAME1, DP.LNAME AS LNAME1, DP.SUFFIX AS SUFFIX1, 
                            DP.EMPLOYER AS EMPLOYER1, DP.OCCUPATION AS OCCUPATION1, DP.SPOUSENAME AS SPOUSE1, DA.STREET AS STREET1, DA.ADDR1 AS ADDR1, DA.CITY AS CITY1, DA.STATE STATE1, DA.ZIP ZIP1, 
                            DH.PHNNO AS HOMEPHONE1, DE.PHNNO AS EMAIL1, DP.TITLE [TITLE1], DS.CTDAMT AS CTDAMT1, DS.LGIFTDTE AS MRCDATE1,

                            MP.PID AS PID2, MP.PREFIX AS PREFIX2, MP.FNAME AS FNAME2, MP.MNAME AS MNAME2, MP.LNAME AS LNAME2, MP.SUFFIX AS SUFFIX2,
                            MP.EMPLOYER AS EMPLOYER2, MP.OCCUPATION AS OCCUPATION2, MP.SPOUSENAME AS SPOUSE2, MA.STREET AS STREET2, MA.ADDR1 AS ADDR2, MA.CITY AS CITY2, MA.STATE AS STATE2, MA.ZIP AS ZIP2, 
                            MH.PHNNO AS HOMEPHONE2, ME.PHNNO AS EMAIL2, MP.TITLE [TITLE2], MS.CTDAMT AS CTDAMT2, MS.LGIFTDTE AS MRCDATE2
                            INTO #OUTPUT
                            FROM 	#ALL3 A left outer join ADDRESS DA on A.PID1=DA.PID AND DA.PRIME=1
				                            left outer join PHONE DH on A.PID1=DH.PID AND DH.PRIME=1 AND DH.PHNTYPEID=1
				                            left outer join PHONE DE on A.PID1=DE.PID AND DE.PRIME=1 AND DE.PHNTYPEID=4
				                            left outer join ADDRESS MA on A.PID2=MA.PID AND MA.PRIME=1
				                            left outer join PHONE MH on A.PID2=MH.PID AND MH.PRIME=1 AND MH.PHNTYPEID=1
				                            left outer join PHONE ME on A.PID2=ME.PID AND ME.PRIME=1 AND ME.PHNTYPEID=4,
	                            PEOPLE DP, SUMMARYD DS,	PEOPLE MP, SUMMARYD MS
                            WHERE	A.PID1=DP.PID AND A.PID1=DS.PID AND
		                            A.PID2=MP.PID AND A.PID2=MS.PID
                            ORDER BY MATCHTYPE, PID1

                            --===== CLEAN UP OUTPUT
                            UPDATE T SET
	                            T.[PREFIX1] = ISNULL(T.[PREFIX1],''),
	                            T.[FNAME1] = ISNULL(T.[FNAME1],''),
	                            T.[MNAME1] = ISNULL(T.[MNAME1],''),
	                            T.[LNAME1] = ISNULL(T.[LNAME1],''),
	                            T.[SUFFIX1] = ISNULL(T.[SUFFIX1],''),
	                            T.[EMPLOYER1] = ISNULL(T.[EMPLOYER1],''),
	                            T.[OCCUPATION1] = ISNULL(T.[OCCUPATION1],''),
	                            T.[SPOUSE1] = ISNULL(T.[SPOUSE1],''),
	                            T.[STREET1] = ISNULL(T.[STREET1],''),
	                            T.[ADDR1] = ISNULL(T.[ADDR1],''),
	                            T.[CITY1] = ISNULL(T.[CITY1],''),
	                            T.[STATE1] = ISNULL(T.[STATE1],''),
	                            T.[ZIP1] = ISNULL(T.[ZIP1],''), 
	                            T.[HOMEPHONE1] = ISNULL(T.[HOMEPHONE1],''),
	                            T.[EMAIL1] = ISNULL(T.[EMAIL1],''),
	                            T.[TITLE1] = ISNULL(T.[TITLE1],''),
	                            T.[CTDAMT1] = ISNULL(T.[CTDAMT1],0),
	                            T.[PREFIX2] = ISNULL(T.[PREFIX2],''),
	                            T.[FNAME2] = ISNULL(T.[FNAME2],''),
	                            T.[MNAME2] = ISNULL(T.[MNAME2],''),
	                            T.[LNAME2] = ISNULL(T.[LNAME2],''),
	                            T.[SUFFIX2] = ISNULL(T.[SUFFIX2],''),
	                            T.[EMPLOYER2] = ISNULL(T.[EMPLOYER2],''),
	                            T.[OCCUPATION2] = ISNULL(T.[OCCUPATION2],''),
	                            T.[SPOUSE2] = ISNULL(T.[SPOUSE2],''),
	                            T.[STREET2] = ISNULL(T.[STREET2],''),
	                            T.[ADDR2] = ISNULL(T.[ADDR2],''),
	                            T.[CITY2] = ISNULL(T.[CITY2],''),
	                            T.[STATE2] = ISNULL(T.[STATE2],''),
	                            T.[ZIP2] = ISNULL(T.[ZIP2],''), 
	                            T.[HOMEPHONE2] = ISNULL(T.[HOMEPHONE2],''),
	                            T.[EMAIL2] = ISNULL(T.[EMAIL2],''),
	                            T.[TITLE2] = ISNULL(T.[TITLE2],''),
	                            T.[CTDAMT2] = ISNULL(T.[CTDAMT2],0)
                            FROM #OUTPUT T

                            --===== DISPLAY RESULTS
                            SELECT * FROM #OUTPUT ORDER BY MATCHTYPE, PID1
                                      ";

            #endregion

            string sql = @"SELECT COUNT(ZDupID) AS IND_COUNT FROM Z_DUP_DT WHERE ZDupID = (SELECT MAX(ZDupID) FROM Z_DUP WHERE [TYPE] = 'IND')";

            string countsql = @"SELECT COUNT(ZDupID) AS DATA_COUNT FROM Z_DUP_DT";
            var qcount = _dbFactory.getContext().Database.SqlQuery<int>(countsql).First();

            //if count is zero, assume timer is not present.
            if (qcount == 0)
            {
                var q1 = _dbFactory.getContext().Database.SqlQuery<duplicateindividual>(_sql);
                return q1.ToList().Count();
            }
            else
            {
                var q2 = _dbFactory.getContext().Database.SqlQuery<int>(sql);
                return q2.First<int>();
            }


        }

        public List<duplicateindividual> get_duplicateindividual()
        {

            #region [[ Full Query]]
            string _sql = @" SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED
                            IF OBJECT_ID('TEMPDB..#ALL') IS NOT NULL BEGIN DROP TABLE #ALL END
                            IF OBJECT_ID('TEMPDB..#ALL2') IS NOT NULL BEGIN DROP TABLE #ALL2 END
                            IF OBJECT_ID('TEMPDB..#ALL3') IS NOT NULL BEGIN DROP TABLE #ALL3 END
                            IF OBJECT_ID('TEMPDB..#ADDR') IS NOT NULL BEGIN DROP TABLE #ADDR END
                            IF OBJECT_ID('TEMPDB..#EMPL') IS NOT NULL BEGIN DROP TABLE #EMPL END
                            IF OBJECT_ID('TEMPDB..#PHON') IS NOT NULL BEGIN DROP TABLE #PHON END
                            IF OBJECT_ID('TEMPDB..#EMAI') IS NOT NULL BEGIN DROP TABLE #EMAI END
                            IF OBJECT_ID('TEMPDB..#PEOPLE') IS NOT NULL BEGIN DROP TABLE #PEOPLE END
                            IF OBJECT_ID('TEMPDB..#NAMEDUP') IS NOT NULL BEGIN DROP TABLE #NAMEDUP END
                            IF OBJECT_ID('TEMPDB..#OUTPUT') IS NOT NULL BEGIN DROP TABLE #OUTPUT END
                            --===== SUSPECT DUPLICATE 'NAME' FIRST
                            SELECT
                            CAST(LEFT(REPLACE(ISNULL(LNAME,''),' ',''),5) AS CHAR(5)) AS LNAMEK, 
                            CAST(LEFT(REPLACE(ISNULL(FNAME,''),' ',''),3) AS CHAR(3)) AS FNAMEK
                            INTO	#NAMEDUP
                            FROM 	PEOPLE
                            WHERE	LNAME<>'' AND PEOTYPEID IN (SELECT TOP 1 PEOTYPEID FROM lkPEOTYPE WHERE PEOTYPE = 'I')
                            GROUP BY
	                            CAST(LEFT(REPLACE(ISNULL(LNAME,''),' ',''),5) AS CHAR(5)),
	                            CAST(LEFT(REPLACE(ISNULL(FNAME,''),' ',''),3) AS CHAR(3))
                            HAVING COUNT(*)>1
                            CREATE INDEX IX_TTTNAMEDUP ON #NAMEDUP (LNAMEK, FNAMEK)

                            --===== CREATE PEOPLE INSTANCE OF 
                            SELECT
                            P.PID, A.LNAMEK, A.FNAMEK, CAST(LEFT(P.MNAME,1) AS CHAR(1)) AS MNAMEK,
                            dbo.oKEY_PREFIX(P.PREFIX) AS PREFIXK, CAST(LEFT(P.SUFFIX,3) AS CHAR(3)) AS SUFFIXK,
                            LEFT(LTRIM(RTRIM(P.EMPLOYER)),20) AS EMPLOYERK
                            INTO 	#PEOPLE
                            FROM 	#NAMEDUP A,
	                            PEOPLE P
                            WHERE	A.LNAMEK=LEFT(REPLACE(ISNULL(P.LNAME,''),' ',''),5) AND A.FNAMEK=LEFT(REPLACE(ISNULL(P.FNAME,''),' ',''),3)

                            CREATE CLUSTERED INDEX IX_TTTPEOPLE_NAME ON #PEOPLE (LNAMEK,FNAMEK,MNAMEK,PREFIXK,SUFFIXK)
                            CREATE INDEX IX_TTTPEOPLE_FNAMEK ON #PEOPLE (FNAMEK)
                            CREATE INDEX IX_TTTPEOPLE_M ON #PEOPLE (MNAMEK)
                            CREATE INDEX IX_TTTPEOPLE_P ON #PEOPLE (PREFIXK)
                            CREATE INDEX IX_TTTPEOPLE_S ON #PEOPLE (SUFFIXK)
                            CREATE INDEX IX_TTTPEOPLE_PID ON #PEOPLE (PID)

                            --===== ADDRESS DUP
                            SELECT DISTINCT
                            '1. ADDRESS' AS MATCHTYPE,
                            DP.PID AS PID1, 
                            MP.PID AS PID2
                            INTO	#ADDR
                            FROM 	#PEOPLE DP, PKEY DK,
	                            #PEOPLE MP, PKEY MK
                            WHERE	DP.PID<MP.PID AND
	                            DP.PID=DK.PID AND MP.PID=MK.PID AND
	                            DK.KEYTADDR = MK.KEYTADDR 
	                            AND	DP.LNAMEK=MP.LNAMEK AND DP.FNAMEK=MP.FNAMEK
                            CREATE INDEX IX_TTTADDR_PID1 ON #ADDR (PID1, PID2)
                            CREATE INDEX IX_TTTADDR_PID2 ON #ADDR (PID2, PID1)

                            --===== EMPLOYER DUP
                            SELECT  
                            '2. EMPLOYER' AS MATCHTYPE,
                            DP.PID AS PID1,
                            MP.PID AS PID2
                            INTO	#EMPL
                            FROM 	#PEOPLE DP,
	                            #PEOPLE MP
                            WHERE	DP.PID<MP.PID AND
	                            DP.LNAMEK=MP.LNAMEK AND DP.FNAMEK=MP.FNAMEK AND
	                            ISNULL(DP.EMPLOYERK,'') NOT IN ('','N/A','RETIRED','SELF','SELF-EMPLOYED','NONE','HOMEMAKER','NA','SELF EMPLOYED','UNEMPLOYED') AND DP.EMPLOYERK=MP.EMPLOYERK AND 
	                            ( ISNULL(DP.MNAMEK,'')='' OR ISNULL(MP.MNAMEK,'')='' OR DP.MNAMEK=MP.MNAMEK ) AND
	                            ( (ISNULL(DP.PREFIXK,'')='' AND ISNULL(MP.PREFIXK,'')<>'') OR 
	                              (ISNULL(MP.PREFIXK,'')='' AND ISNULL(DP.PREFIXK,'')<>'') OR 
	                              DP.PREFIXK=MP.PREFIXK ) AND
	                            ( ISNULL(DP.SUFFIXK,'')='' OR ISNULL(MP.SUFFIXK,'')='' OR DP.SUFFIXK=MP.SUFFIXK ) AND
	                            (SELECT COUNT(*) FROM #ADDR WHERE (PID1=DP.PID AND PID2=MP.PID) OR (PID1=MP.PID AND PID2=DP.PID)) = 0

                            CREATE INDEX IX_TTTEMPL_PID1 ON #EMPL (PID1, PID2)
                            CREATE INDEX IX_TTTEMPL_PID2 ON #EMPL (PID2, PID1)

                            --===== PHONE DUP
                            SELECT  
                            '3. H-PHONE' AS MATCHTYPE,
                            DP.PID AS PID1, 
                            MP.PID AS PID2 
                            INTO	#PHON
                            FROM 	#PEOPLE DP, PHONE DH,
	                            #PEOPLE MP, PHONE MH
                            WHERE	DP.PID<MP.PID AND
	                            DP.PID=DH.PID AND DH.PHNTYPEID=1 AND DH.PRIME=1 AND
	                            MP.PID=MH.PID AND MH.PHNTYPEID=1 AND MH.PRIME=1 AND
	                            DP.LNAMEK=MP.LNAMEK AND DP.FNAMEK=MP.FNAMEK AND 
	                            LEN(DH.PHNNO)>6 AND DH.PHNNO=MH.PHNNO AND
	                            ( ISNULL(DP.MNAMEK,'')='' OR ISNULL(MP.MNAMEK,'')='' OR DP.MNAMEK=MP.MNAMEK ) AND
	                            ( (ISNULL(DP.PREFIXK,'')='' AND ISNULL(MP.PREFIXK,'')<>'') OR 
	                              (ISNULL(MP.PREFIXK,'')='' AND ISNULL(DP.PREFIXK,'')<>'') OR 
	                              DP.PREFIXK=MP.PREFIXK ) AND
	                            ( ISNULL(DP.SUFFIXK,'')='' OR ISNULL(MP.SUFFIXK,'')='' OR DP.SUFFIXK=MP.SUFFIXK )

                            DELETE #PHON
                            FROM #PHON A
                            WHERE	(SELECT COUNT(*) FROM #ADDR WHERE (PID1=A.PID1 AND PID2=A.PID2) OR (PID1=A.PID2 AND PID2=A.PID1)) > 0 OR
	                            (SELECT COUNT(*) FROM #EMPL WHERE (PID1=A.PID1 AND PID2=A.PID2) OR (PID1=A.PID2 AND PID2=A.PID1)) > 0

                            CREATE INDEX IX_TTTPHON_PID1 ON #PHON (PID1, PID2)
                            CREATE INDEX IX_TTTPHON_PID2 ON #PHON (PID2, PID1)

                            --===== EMAIL DUP
                            SELECT  
                            '4. eMAIL' AS MATCHTYPE,
                            DP.PID AS PID1, 
                            MP.PID AS PID2 
                            INTO	#EMAI
                            FROM 	#PEOPLE DP, PHONE DH,
	                            #PEOPLE MP, PHONE MH
                            WHERE	DP.PID<MP.PID AND
	                            DP.PID=DH.PID AND DH.PHNTYPEID=4 AND DH.PRIME=1 AND
	                            MP.PID=MH.PID AND MH.PHNTYPEID=4 AND MH.PRIME=1 AND
	                            DP.LNAMEK=MP.LNAMEK AND DP.FNAMEK=MP.FNAMEK AND 
	                            LEN(DH.PHNNO)>6 AND DH.PHNNO=MH.PHNNO AND
	                            ( ISNULL(DP.MNAMEK,'')='' OR ISNULL(MP.MNAMEK,'')='' OR DP.MNAMEK=MP.MNAMEK ) AND
	                            ( (ISNULL(DP.PREFIXK,'')='' AND ISNULL(MP.PREFIXK,'')<>'') OR 
	                              (ISNULL(MP.PREFIXK,'')='' AND ISNULL(DP.PREFIXK,'')<>'') OR 
	                              DP.PREFIXK=MP.PREFIXK ) AND
	                            ( ISNULL(DP.SUFFIXK,'')='' OR ISNULL(MP.SUFFIXK,'')='' OR DP.SUFFIXK=MP.SUFFIXK )

                            DELETE #EMAI
                            FROM #EMAI A
                            WHERE	(SELECT COUNT(*) FROM #ADDR WHERE (PID1=A.PID1 AND PID2=A.PID2) OR (PID1=A.PID2 AND PID2=A.PID1)) > 0 OR
	                            (SELECT COUNT(*) FROM #EMPL WHERE (PID1=A.PID1 AND PID2=A.PID2) OR (PID1=A.PID2 AND PID2=A.PID1)) > 0 OR
	                            (SELECT COUNT(*) FROM #PHON WHERE (PID1=A.PID1 AND PID2=A.PID2) OR (PID1=A.PID2 AND PID2=A.PID1)) > 0 

                            SELECT * INTO #ALL FROM #ADDR
                            UNION SELECT * FROM #EMPL
                            UNION SELECT * FROM #PHON
                            UNION SELECT * FROM #EMAI

                            SELECT A.*,S1.CTDAMT AS CTDAMT1, S2.CTDAMT AS CTDAMT2 INTO #ALL2 FROM #ALL A left outer join SUMMARYD S1 on A.PID1 = S1.PID left outer join SUMMARYD S2 on A.PID2 = S2.PID

                            SELECT MATCHTYPE, CASE WHEN CTDAMT2>CTDAMT1 THEN PID2 ELSE PID1 END AS PID1, CASE WHEN CTDAMT2>CTDAMT1 THEN PID1 ELSE PID2 END AS PID2 INTO #ALL3 FROM #ALL2
                            CREATE INDEX IX_TTTALL3_PID1 ON #ALL3 (PID1)
                            CREATE INDEX IX_TTTALL3_PID2 ON #ALL3 (PID2)

                            --===== DUMP INTO FINAL OUTPUT TABLE
                            SELECT A.MATCHTYPE, 
                            DP.PID AS PID1, DP.PREFIX AS PREFIX1, DP.FNAME AS FNAME1, DP.MNAME AS MNAME1, DP.LNAME AS LNAME1, DP.SUFFIX AS SUFFIX1, 
                            DP.EMPLOYER AS EMPLOYER1, DP.OCCUPATION AS OCCUPATION1, DP.SPOUSENAME AS SPOUSE1, DA.STREET AS STREET1, DA.ADDR1 AS ADDR1, DA.CITY AS CITY1, DA.STATE STATE1, DA.ZIP ZIP1, 
                            DH.PHNNO AS HOMEPHONE1, DE.PHNNO AS EMAIL1, DP.TITLE [TITLE1], DS.CTDAMT AS CTDAMT1, DS.LGIFTDTE AS MRCDATE1,

                            MP.PID AS PID2, MP.PREFIX AS PREFIX2, MP.FNAME AS FNAME2, MP.MNAME AS MNAME2, MP.LNAME AS LNAME2, MP.SUFFIX AS SUFFIX2,
                            MP.EMPLOYER AS EMPLOYER2, MP.OCCUPATION AS OCCUPATION2, MP.SPOUSENAME AS SPOUSE2, MA.STREET AS STREET2, MA.ADDR1 AS ADDR2, MA.CITY AS CITY2, MA.STATE AS STATE2, MA.ZIP AS ZIP2, 
                            MH.PHNNO AS HOMEPHONE2, ME.PHNNO AS EMAIL2, MP.TITLE [TITLE2], MS.CTDAMT AS CTDAMT2, MS.LGIFTDTE AS MRCDATE2
                            INTO #OUTPUT
                            FROM 	#ALL3 A left outer join ADDRESS DA on A.PID1=DA.PID AND DA.PRIME=1
				                            left outer join PHONE DH on A.PID1=DH.PID AND DH.PRIME=1 AND DH.PHNTYPEID=1
				                            left outer join PHONE DE on A.PID1=DE.PID AND DE.PRIME=1 AND DE.PHNTYPEID=4
				                            left outer join ADDRESS MA on A.PID2=MA.PID AND MA.PRIME=1
				                            left outer join PHONE MH on A.PID2=MH.PID AND MH.PRIME=1 AND MH.PHNTYPEID=1
				                            left outer join PHONE ME on A.PID2=ME.PID AND ME.PRIME=1 AND ME.PHNTYPEID=4,
	                            PEOPLE DP, SUMMARYD DS,	PEOPLE MP, SUMMARYD MS
                            WHERE	A.PID1=DP.PID AND A.PID1=DS.PID AND
		                            A.PID2=MP.PID AND A.PID2=MS.PID
                            ORDER BY MATCHTYPE, PID1

                            --===== CLEAN UP OUTPUT
                            UPDATE T SET
	                            T.[PREFIX1] = ISNULL(T.[PREFIX1],''),
	                            T.[FNAME1] = ISNULL(T.[FNAME1],''),
	                            T.[MNAME1] = ISNULL(T.[MNAME1],''),
	                            T.[LNAME1] = ISNULL(T.[LNAME1],''),
	                            T.[SUFFIX1] = ISNULL(T.[SUFFIX1],''),
	                            T.[EMPLOYER1] = ISNULL(T.[EMPLOYER1],''),
	                            T.[OCCUPATION1] = ISNULL(T.[OCCUPATION1],''),
	                            T.[SPOUSE1] = ISNULL(T.[SPOUSE1],''),
	                            T.[STREET1] = ISNULL(T.[STREET1],''),
	                            T.[ADDR1] = ISNULL(T.[ADDR1],''),
	                            T.[CITY1] = ISNULL(T.[CITY1],''),
	                            T.[STATE1] = ISNULL(T.[STATE1],''),
	                            T.[ZIP1] = ISNULL(T.[ZIP1],''), 
	                            T.[HOMEPHONE1] = ISNULL(T.[HOMEPHONE1],''),
	                            T.[EMAIL1] = ISNULL(T.[EMAIL1],''),
	                            T.[TITLE1] = ISNULL(T.[TITLE1],''),
	                            T.[CTDAMT1] = ISNULL(T.[CTDAMT1],0),
	                            T.[PREFIX2] = ISNULL(T.[PREFIX2],''),
	                            T.[FNAME2] = ISNULL(T.[FNAME2],''),
	                            T.[MNAME2] = ISNULL(T.[MNAME2],''),
	                            T.[LNAME2] = ISNULL(T.[LNAME2],''),
	                            T.[SUFFIX2] = ISNULL(T.[SUFFIX2],''),
	                            T.[EMPLOYER2] = ISNULL(T.[EMPLOYER2],''),
	                            T.[OCCUPATION2] = ISNULL(T.[OCCUPATION2],''),
	                            T.[SPOUSE2] = ISNULL(T.[SPOUSE2],''),
	                            T.[STREET2] = ISNULL(T.[STREET2],''),
	                            T.[ADDR2] = ISNULL(T.[ADDR2],''),
	                            T.[CITY2] = ISNULL(T.[CITY2],''),
	                            T.[STATE2] = ISNULL(T.[STATE2],''),
	                            T.[ZIP2] = ISNULL(T.[ZIP2],''), 
	                            T.[HOMEPHONE2] = ISNULL(T.[HOMEPHONE2],''),
	                            T.[EMAIL2] = ISNULL(T.[EMAIL2],''),
	                            T.[TITLE2] = ISNULL(T.[TITLE2],''),
	                            T.[CTDAMT2] = ISNULL(T.[CTDAMT2],0)
                            FROM #OUTPUT T

                            --===== DISPLAY RESULTS
                            SELECT * FROM #OUTPUT ORDER BY MATCHTYPE, PID1
                          ";
            #endregion

            string sql = @"SELECT MATCHTYPE,PID1,PREFIX1,FNAME1,MNAME1,LNAME1,SUFFIX1,EMPLOYER1,OCCUPATION1,SPOUSE1,STREET1,ADDR1,CITY1,STATE1,ZIP1,HOMEPHONE1,EMAIL1,TITLE1,CTDAMT1,MRCDATE1,PID2,PREFIX2,FNAME2,MNAME2,LNAME2,SUFFIX2,EMPLOYER2,OCCUPATION2,SPOUSE2,STREET2,ADDR2,CITY2,STATE2,ZIP2,HOMEPHONE2,EMAIL2,TITLE2,CTDAMT2,MRCDATE2 FROM Z_DUP_DT WHERE zDupID = (SELECT MAX(ZDupID) FROM Z_DUP WHERE [TYPE] = 'IND') ORDER BY MATCHTYPE, PID1";

            string countsql = @"SELECT COUNT(ZDupID) AS DATA_COUNT FROM Z_DUP_DT";
            var qcount = _dbFactory.getContext().Database.SqlQuery<int>(countsql).First();

            //if count is zero, assume timer is not present.
            if (qcount == 0)
            {
                var q1 = _dbFactory.getContext().Database.SqlQuery<duplicateindividual>(_sql);
                return q1.ToList();
            }
            else
            {
                var q2 = _dbFactory.getContext().Database.SqlQuery<duplicateindividual>(sql);
                return q2.ToList();
            }


        }

        public int get_duplicatenonindividualcount()
        {

            #region [[Full Query]]
            string _sql = @" --=======================================================================================
                            --===== NON-INDIVIDUAL DUPE CHECK REPORT
                            --===== LOGIC BORROWED FROM DEXTER REPORT #cq.donors# 108. Dupe Checker [Non-Individuals]
                            --=======================================================================================

                            --===== PREP ENVIRONMENT
                            SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED
                            IF OBJECT_ID('TEMPDB..#KEYLINEDUPES') IS NOT NULL BEGIN DROP TABLE #KEYLINEDUPES END
                            IF OBJECT_ID('TEMPDB..#KEYLINECANDIDATES') IS NOT NULL BEGIN DROP TABLE #KEYLINECANDIDATES END
                            IF OBJECT_ID('TEMPDB..#FECIDDUPES') IS NOT NULL BEGIN DROP TABLE #FECIDDUPES END
                            IF OBJECT_ID('TEMPDB..#FECIDDUPECANDIDATES') IS NOT NULL BEGIN DROP TABLE #FECIDDUPECANDIDATES END
                            IF OBJECT_ID('TEMPDB..#KEYLINESUMMARY') IS NOT NULL BEGIN DROP TABLE #KEYLINESUMMARY END
                            IF OBJECT_ID('TEMPDB..#FECIDSUMMARY') IS NOT NULL BEGIN DROP TABLE #FECIDSUMMARY END
                            IF OBJECT_ID('TEMPDB..#ROLLUP') IS NOT NULL BEGIN DROP TABLE #ROLLUP END

                            --===== GET DUPES ON SPECIAL KEYLINE INTO A TEMP TABLE
                            SELECT dbo.ozExtractFixLen(dbo.ozFmtAlphaNumeric(A.ZIP),5)+dbo.ozExtractFixLen(dbo.ozFmtAlphaNumeric(A.STREET),10) [KEYLINE],
                            COUNT(DISTINCT P.PID) [DUPES]
                            INTO #KEYLINEDUPES
                            FROM	ADDRESS A 
	                            JOIN PEOPLE P ON A.PID = P.PID AND A.PRIME = 1
	                            JOIN LKPEOTYPE L ON P.PEOTYPEID = L.PEOTYPEID
                            WHERE L.PEOTYPE <> 'I'
                            GROUP BY dbo.ozExtractFixLen(dbo.ozFmtAlphaNumeric(A.ZIP),5)+dbo.ozExtractFixLen(dbo.ozFmtAlphaNumeric(A.STREET),10) 
                            HAVING COUNT(DISTINCT P.PID) > 1
                            CREATE INDEX XXXX_KEYLINEDUPES ON #KEYLINEDUPES (KEYLINE)

                            --===== GET PIDs FOR DUPES ON KEYLINE
                            SELECT P.PID, P.FECCMTEID, T.KEYLINE, P.PREFIX, P.FNAME, P.MNAME, P.LNAME, (SELECT TOP 1 C.PREFIX FROM CONTACT C WHERE C.PRIME = 1 AND C.PID = P.PID) cPREFIX, (SELECT TOP 1 C.FNAME FROM CONTACT C WHERE C.PRIME = 1 AND C.PID = P.PID) cFNAME,
                            (SELECT TOP 1 C.MNAME FROM CONTACT C WHERE C.PRIME = 1 AND C.PID = P.PID) cMNAME, (SELECT TOP 1 C.LNAME FROM CONTACT C WHERE C.PRIME = 1 AND C.PID = P.PID) cLNAME, (SELECT TOP 1 C.TITLE FROM CONTACT C WHERE C.PRIME = 1 AND C.PID = P.PID) cTITLE,
                            A.STREET, A.ADDR1, A.ADDR2, A.CITY, A.STATE, A.ZIP, A.PLUS4
                            INTO #KEYLINECANDIDATES
                            FROM	PEOPLE P
	                            JOIN ADDRESS A ON P.PID = A.PID AND A.PRIME = 1
	                            JOIN #KEYLINEDUPES T ON (dbo.ozExtractFixLen(dbo.ozFmtAlphaNumeric(A.ZIP),5)+dbo.ozExtractFixLen(dbo.ozFmtAlphaNumeric(A.STREET),10)) = T.KEYLINE
	                            JOIN LKPEOTYPE L ON P.PEOTYPEID = L.PEOTYPEID
                            WHERE L.PEOTYPE <> 'I'
                            CREATE INDEX XXXX_KEYLINECANDIDATES ON #KEYLINECANDIDATES (KEYLINE)
                            CREATE INDEX XXXX_KEYLINECANDIDATESPID ON #KEYLINECANDIDATES (PID)

                            --===== GET DUPES ON FECID
                            SELECT P.FECCMTEID, COUNT(DISTINCT P.PID) [DUPES]
                            INTO #FECIDDUPES
                            FROM	PEOPLE P
	                            JOIN LKPEOTYPE L ON P.PEOTYPEID = L.PEOTYPEID
                            WHERE L.PEOTYPE <> 'I' AND ISNULL(P.FECCMTEID,'') <> ''
                            GROUP BY P.FECCMTEID 
                            HAVING COUNT(DISTINCT P.PID) > 1
                            CREATE INDEX XXXX_FECIDDUPES ON #FECIDDUPES (FECCMTEID)

                            --===== GET PIDs FOR DUPES ON FECCMTEID
                            SELECT P.PID, T.FECCMTEID,
                            P.PREFIX, P.FNAME, P.MNAME, P.LNAME, 
                            (SELECT TOP 1 C.PREFIX FROM CONTACT C WHERE C.PRIME = 1 AND C.PID = P.PID) cPREFIX,
                            (SELECT TOP 1 C.FNAME FROM CONTACT C WHERE C.PRIME = 1 AND C.PID = P.PID) cFNAME,
                            (SELECT TOP 1 C.MNAME FROM CONTACT C WHERE C.PRIME = 1 AND C.PID = P.PID) cMNAME,
                            (SELECT TOP 1 C.LNAME FROM CONTACT C WHERE C.PRIME = 1 AND C.PID = P.PID) cLNAME,
                            (SELECT TOP 1 C.TITLE FROM CONTACT C WHERE C.PRIME = 1 AND C.PID = P.PID) cTITLE,
                            A.STREET, A.ADDR1, A.ADDR2, A.CITY, A.STATE, A.ZIP, A.PLUS4
                            INTO	#FECIDDUPECANDIDATES
                            FROM	PEOPLE P
	                            JOIN LKPEOTYPE L ON P.PEOTYPEID = L.PEOTYPEID
	                            JOIN ADDRESS A ON P.PID = A.PID AND A.PRIME = 1
	                            JOIN #FECIDDUPES T ON T.FECCMTEID = P.FECCMTEID
                            WHERE L.PEOTYPE <> 'I'
                            AND P.PEOTYPEID NOT IN (SELECT TOP 1 PEOTYPEID FROM lkPEOTYPE WHERE PEOTYPE = 'I')
                            CREATE INDEX XXXX_FECIDDUPECANDIDATES ON #FECIDDUPECANDIDATES (FECCMTEID)
                            CREATE INDEX XXXX_FECIDDUPECANDIDATESPID ON #FECIDDUPECANDIDATES (PID)

                            --===== SUMMARIZE RESULTS
                            SELECT IDENTITY(int, 1,1) [ROWNUMBER], '1 - KEYLINE MATCH' [METHOD], 
                            KC1.KEYLINE [SEARCHTEXT1] , KC1.PID [PID1] , KC1.FECCMTEID [FECCMTEID1],
                            KC1.PREFIX [PREFIX1] , KC1.FNAME [FNAME1] , KC1.MNAME [MNAME1] , KC1.LNAME [LNAME1] , KC1.cPREFIX [cPREFIX1] , KC1.cFNAME [cFNAME1] , KC1.cMNAME [cMNAME1] , KC1.cLNAME [cLNAME1] , KC1.cTITLE [cTITLE1] , 
                            KC1.STREET [STREET1], KC1.ADDR1 [ADDR1] , KC1.CITY [CITY1] , KC1.STATE [STATE1] , KC1.ZIP [ZIP1] , KC1.PLUS4 [PLUS41] ,
                            KC2.KEYLINE [SEARCHTEXT2] , KC2.PID [PID2] , KC1.FECCMTEID [FECCMTEID2],
                            KC2.PREFIX [PREFIX2] , KC2.FNAME [FNAME2] , KC2.MNAME [MNAME2] , KC2.LNAME [LNAME2] , KC2.cPREFIX [cPREFIX2] , KC2.cFNAME [cFNAME2] , KC2.cMNAME [cMNAME2] , KC2.cLNAME [cLNAME2] , KC2.cTITLE [cTITLE2] , 
                            KC2.STREET [STREET2], KC2.ADDR2 [ADDR2] , KC2.CITY [CITY2] , KC2.STATE [STATE2] , KC2.ZIP [ZIP2] , KC2.PLUS4 [PLUS42] 
                            INTO	#KEYLINESUMMARY
                            FROM 	#KEYLINECANDIDATES KC1
	                            JOIN #KEYLINECANDIDATES KC2 ON KC1.PID < KC2.PID AND KC1.KEYLINE = KC2.KEYLINE
	                            JOIN #KEYLINEDUPES KLD1 ON KC1.KEYLINE = KLD1.KEYLINE 
	                            JOIN #KEYLINEDUPES KLD2 ON KC2.KEYLINE = KLD2.KEYLINE 
                            ORDER BY KC1.KEYLINE

                            SELECT IDENTITY(int, 1,1) [ROWNUMBER], '2 - FECCMTEID MATCH' [METHOD] , 
                            KC1.FECCMTEID [SEARCHTEXT1] , KC1.PID [PID1] , KC1.FECCMTEID [FECCMTEID1],
                            KC1.PREFIX [PREFIX1] , KC1.FNAME [FNAME1] , KC1.MNAME [MNAME1] , KC1.LNAME [LNAME1] , KC1.cPREFIX [cPREFIX1] , KC1.cFNAME [cFNAME1] , KC1.cMNAME [cMNAME1] , KC1.cLNAME [cLNAME1] , KC1.cTITLE [cTITLE1] , 
                            KC1.STREET [STREET1], KC1.ADDR1 [ADDR1] , KC1.CITY [CITY1] , KC1.STATE [STATE1] , KC1.ZIP [ZIP1] , KC1.PLUS4 [PLUS41] ,
                            KC2.FECCMTEID [SEARCHTEXT2] , KC2.PID [PID2] , KC2.FECCMTEID [FECCMTEID2],
                            KC2.PREFIX [PREFIX2] , KC2.FNAME [FNAME2] , KC2.MNAME [MNAME2] , KC2.LNAME [LNAME2] , KC2.cPREFIX [cPREFIX2] , KC2.cFNAME [cFNAME2] , KC2.cMNAME [cMNAME2] , KC2.cLNAME [cLNAME2] , KC2.cTITLE [cTITLE2] , 
                            KC2.STREET [STREET2], KC2.ADDR2 [ADDR2] , KC2.CITY [CITY2] , KC2.STATE [STATE2] , KC2.ZIP [ZIP2] , KC2.PLUS4 [PLUS42] 
                            INTO	#FECIDSUMMARY
                            FROM	#FECIDDUPECANDIDATES KC1
	                            JOIN #FECIDDUPECANDIDATES KC2 ON KC1.PID < KC2.PID AND KC1.FECCMTEID = KC2.FECCMTEID
	                            JOIN #FECIDDUPES KLD1 ON KC1.FECCMTEID = KLD1.FECCMTEID 
	                            JOIN #FECIDDUPES KLD2 ON KC2.FECCMTEID = KLD2.FECCMTEID
                            ORDER BY KC1.FECCMTEID

                            --===== DUMP RESULTS INTO A FINAL TABLE
                            SELECT * INTO #ROLLUP FROM #KEYLINESUMMARY
                            UNION ALL 
                            SELECT * FROM #FECIDSUMMARY 

                            --===== CLEANUP
                            UPDATE T SET 
	                            T.FECCMTEID1 = ISNULL(T.FECCMTEID1,''),
	                            T.[SEARCHTEXT1] = ISNULL(T.[SEARCHTEXT1],''), 
	                            T.[PREFIX1] = ISNULL(T.[PREFIX1],''), 
	                            T.[FNAME1] = ISNULL(T.[FNAME1],''), 
	                            T.[MNAME1] = ISNULL(T.[MNAME1],''), 
	                            T.[LNAME1] = ISNULL(T.[LNAME1],''), 
	                            T.[cPREFIX1] = ISNULL(T.[cPREFIX1],''), 
	                            T.[cFNAME1] = ISNULL(T.[cFNAME1],''), 
	                            T.[cMNAME1] = ISNULL(T.[cMNAME1],''), 
	                            T.[cLNAME1] = ISNULL(T.[cLNAME1],''), 
	                            T.[cTITLE1] = ISNULL(T.[cTITLE1],''), 
	                            T.[STREET1] = ISNULL(T.[STREET1],''), 
	                            T.[ADDR1] = ISNULL(T.[ADDR1],''), 
	                            T.[CITY1] = ISNULL(T.[CITY1],''), 
	                            T.[STATE1] = ISNULL(T.[STATE1],''), 
	                            T.[ZIP1] = ISNULL(T.[ZIP1],''), 
	                            T.[PLUS41] = ISNULL(T.[PLUS41],''), 
	                            T.FECCMTEID2 = ISNULL(T.FECCMTEID2,''),
	                            T.[SEARCHTEXT2] = ISNULL(T.[SEARCHTEXT2],''), 
	                            T.[PREFIX2] = ISNULL(T.[PREFIX2],''), 
	                            T.[FNAME2] = ISNULL(T.[FNAME2],''), 
	                            T.[MNAME2] = ISNULL(T.[MNAME2],''), 
	                            T.[LNAME2] = ISNULL(T.[LNAME2],''), 
	                            T.[cPREFIX2] = ISNULL(T.[cPREFIX2],''), 
	                            T.[cFNAME2] = ISNULL(T.[cFNAME2],''), 
	                            T.[cMNAME2] = ISNULL(T.[cMNAME2],''), 
	                            T.[cLNAME2] = ISNULL(T.[cLNAME2],''), 
	                            T.[cTITLE2] = ISNULL(T.[cTITLE2],''), 
	                            T.[STREET2] = ISNULL(T.[STREET2],''), 
	                            T.[ADDR2] = ISNULL(T.[ADDR2],''), 
	                            T.[CITY2] = ISNULL(T.[CITY2],''), 
	                            T.[STATE2] = ISNULL(T.[STATE2],''), 
	                            T.[ZIP2] = ISNULL(T.[ZIP2],''), 
	                            T.[PLUS42] = ISNULL(T.[PLUS42],'') 
                            FROM	#ROLLUP T

                            --===== QUERY THE FINAL TABLE
                            SELECT * FROM #ROLLUP T ORDER BY CAST(LEFT(T.METHOD,1) AS INTEGER), T.ROWNUMBER
                                      ";
            #endregion

            string sql = @"SELECT COUNT(ZDupID) AS NON_IND_COUNT FROM Z_DUP_DT WHERE ZDupID = (SELECT MAX(ZDupID) FROM Z_DUP WHERE [TYPE] = 'NON-IND')";

            string countsql = @"SELECT COUNT(ZDupID) AS DATA_COUNT FROM Z_DUP_DT";
            var qcount = _dbFactory.getContext().Database.SqlQuery<int>(countsql).First();

            //if count is zero, assume timer is not present.
            if (qcount == 0)
            {
                var q1 = _dbFactory.getContext().Database.SqlQuery<duplicatenonindividual>(_sql);
                return q1.ToList().Count();
            }
            else
            {
                var q2 = _dbFactory.getContext().Database.SqlQuery<int>(sql);
                return q2.First<int>();
            }

        }

        public List<duplicatenonindividual> get_duplicatenonindividual()
        {
            #region[[Full Query]]
            string _sql = @" --=======================================================================================
                            --===== NON-INDIVIDUAL DUPE CHECK REPORT
                            --===== LOGIC BORROWED FROM DEXTER REPORT #cq.donors# 108. Dupe Checker [Non-Individuals]
                            --=======================================================================================

                            --===== PREP ENVIRONMENT
                            SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED
                            IF OBJECT_ID('TEMPDB..#KEYLINEDUPES') IS NOT NULL BEGIN DROP TABLE #KEYLINEDUPES END
                            IF OBJECT_ID('TEMPDB..#KEYLINECANDIDATES') IS NOT NULL BEGIN DROP TABLE #KEYLINECANDIDATES END
                            IF OBJECT_ID('TEMPDB..#FECIDDUPES') IS NOT NULL BEGIN DROP TABLE #FECIDDUPES END
                            IF OBJECT_ID('TEMPDB..#FECIDDUPECANDIDATES') IS NOT NULL BEGIN DROP TABLE #FECIDDUPECANDIDATES END
                            IF OBJECT_ID('TEMPDB..#KEYLINESUMMARY') IS NOT NULL BEGIN DROP TABLE #KEYLINESUMMARY END
                            IF OBJECT_ID('TEMPDB..#FECIDSUMMARY') IS NOT NULL BEGIN DROP TABLE #FECIDSUMMARY END
                            IF OBJECT_ID('TEMPDB..#ROLLUP') IS NOT NULL BEGIN DROP TABLE #ROLLUP END

                            --===== GET DUPES ON SPECIAL KEYLINE INTO A TEMP TABLE
                            SELECT dbo.ozExtractFixLen(dbo.ozFmtAlphaNumeric(A.ZIP),5)+dbo.ozExtractFixLen(dbo.ozFmtAlphaNumeric(A.STREET),10) [KEYLINE],
                            COUNT(DISTINCT P.PID) [DUPES]
                            INTO #KEYLINEDUPES
                            FROM	ADDRESS A 
	                            JOIN PEOPLE P ON A.PID = P.PID AND A.PRIME = 1
	                            JOIN LKPEOTYPE L ON P.PEOTYPEID = L.PEOTYPEID
                            WHERE L.PEOTYPE <> 'I'
                            GROUP BY dbo.ozExtractFixLen(dbo.ozFmtAlphaNumeric(A.ZIP),5)+dbo.ozExtractFixLen(dbo.ozFmtAlphaNumeric(A.STREET),10) 
                            HAVING COUNT(DISTINCT P.PID) > 1
                            CREATE INDEX XXXX_KEYLINEDUPES ON #KEYLINEDUPES (KEYLINE)

                            --===== GET PIDs FOR DUPES ON KEYLINE
                            SELECT P.PID, P.FECCMTEID, T.KEYLINE, P.PREFIX, P.FNAME, P.MNAME, P.LNAME, (SELECT TOP 1 C.PREFIX FROM CONTACT C WHERE C.PRIME = 1 AND C.PID = P.PID) cPREFIX, (SELECT TOP 1 C.FNAME FROM CONTACT C WHERE C.PRIME = 1 AND C.PID = P.PID) cFNAME,
                            (SELECT TOP 1 C.MNAME FROM CONTACT C WHERE C.PRIME = 1 AND C.PID = P.PID) cMNAME, (SELECT TOP 1 C.LNAME FROM CONTACT C WHERE C.PRIME = 1 AND C.PID = P.PID) cLNAME, (SELECT TOP 1 C.TITLE FROM CONTACT C WHERE C.PRIME = 1 AND C.PID = P.PID) cTITLE,
                            A.STREET, A.ADDR1, A.ADDR2, A.CITY, A.STATE, A.ZIP, A.PLUS4
                            INTO #KEYLINECANDIDATES
                            FROM	PEOPLE P
	                            JOIN ADDRESS A ON P.PID = A.PID AND A.PRIME = 1
	                            JOIN #KEYLINEDUPES T ON (dbo.ozExtractFixLen(dbo.ozFmtAlphaNumeric(A.ZIP),5)+dbo.ozExtractFixLen(dbo.ozFmtAlphaNumeric(A.STREET),10)) = T.KEYLINE
	                            JOIN LKPEOTYPE L ON P.PEOTYPEID = L.PEOTYPEID
                            WHERE L.PEOTYPE <> 'I'
                            CREATE INDEX XXXX_KEYLINECANDIDATES ON #KEYLINECANDIDATES (KEYLINE)
                            CREATE INDEX XXXX_KEYLINECANDIDATESPID ON #KEYLINECANDIDATES (PID)

                            --===== GET DUPES ON FECID
                            SELECT P.FECCMTEID, COUNT(DISTINCT P.PID) [DUPES]
                            INTO #FECIDDUPES
                            FROM	PEOPLE P
	                            JOIN LKPEOTYPE L ON P.PEOTYPEID = L.PEOTYPEID
                            WHERE L.PEOTYPE <> 'I' AND ISNULL(P.FECCMTEID,'') <> ''
                            GROUP BY P.FECCMTEID 
                            HAVING COUNT(DISTINCT P.PID) > 1
                            CREATE INDEX XXXX_FECIDDUPES ON #FECIDDUPES (FECCMTEID)

                            --===== GET PIDs FOR DUPES ON FECCMTEID
                            SELECT P.PID, T.FECCMTEID,
                            P.PREFIX, P.FNAME, P.MNAME, P.LNAME, 
                            (SELECT TOP 1 C.PREFIX FROM CONTACT C WHERE C.PRIME = 1 AND C.PID = P.PID) cPREFIX,
                            (SELECT TOP 1 C.FNAME FROM CONTACT C WHERE C.PRIME = 1 AND C.PID = P.PID) cFNAME,
                            (SELECT TOP 1 C.MNAME FROM CONTACT C WHERE C.PRIME = 1 AND C.PID = P.PID) cMNAME,
                            (SELECT TOP 1 C.LNAME FROM CONTACT C WHERE C.PRIME = 1 AND C.PID = P.PID) cLNAME,
                            (SELECT TOP 1 C.TITLE FROM CONTACT C WHERE C.PRIME = 1 AND C.PID = P.PID) cTITLE,
                            A.STREET, A.ADDR1, A.ADDR2, A.CITY, A.STATE, A.ZIP, A.PLUS4
                            INTO	#FECIDDUPECANDIDATES
                            FROM	PEOPLE P
	                            JOIN LKPEOTYPE L ON P.PEOTYPEID = L.PEOTYPEID
	                            JOIN ADDRESS A ON P.PID = A.PID AND A.PRIME = 1
	                            JOIN #FECIDDUPES T ON T.FECCMTEID = P.FECCMTEID
                            WHERE L.PEOTYPE <> 'I'
                            AND P.PEOTYPEID NOT IN (SELECT TOP 1 PEOTYPEID FROM lkPEOTYPE WHERE PEOTYPE = 'I')
                            CREATE INDEX XXXX_FECIDDUPECANDIDATES ON #FECIDDUPECANDIDATES (FECCMTEID)
                            CREATE INDEX XXXX_FECIDDUPECANDIDATESPID ON #FECIDDUPECANDIDATES (PID)

                            --===== SUMMARIZE RESULTS
                            SELECT IDENTITY(int, 1,1) [ROWNUMBER], '1 - KEYLINE MATCH' [METHOD], 
                            KC1.KEYLINE [SEARCHTEXT1] , KC1.PID [PID1] , KC1.FECCMTEID [FECCMTEID1],
                            KC1.PREFIX [PREFIX1] , KC1.FNAME [FNAME1] , KC1.MNAME [MNAME1] , KC1.LNAME [LNAME1] , KC1.cPREFIX [cPREFIX1] , KC1.cFNAME [cFNAME1] , KC1.cMNAME [cMNAME1] , KC1.cLNAME [cLNAME1] , KC1.cTITLE [cTITLE1] , 
                            KC1.STREET [STREET1], KC1.ADDR1 [ADDR1] , KC1.CITY [CITY1] , KC1.STATE [STATE1] , KC1.ZIP [ZIP1] , KC1.PLUS4 [PLUS41] ,
                            KC2.KEYLINE [SEARCHTEXT2] , KC2.PID [PID2] , KC1.FECCMTEID [FECCMTEID2],
                            KC2.PREFIX [PREFIX2] , KC2.FNAME [FNAME2] , KC2.MNAME [MNAME2] , KC2.LNAME [LNAME2] , KC2.cPREFIX [cPREFIX2] , KC2.cFNAME [cFNAME2] , KC2.cMNAME [cMNAME2] , KC2.cLNAME [cLNAME2] , KC2.cTITLE [cTITLE2] , 
                            KC2.STREET [STREET2], KC2.ADDR2 [ADDR2] , KC2.CITY [CITY2] , KC2.STATE [STATE2] , KC2.ZIP [ZIP2] , KC2.PLUS4 [PLUS42] 
                            INTO	#KEYLINESUMMARY
                            FROM 	#KEYLINECANDIDATES KC1
	                            JOIN #KEYLINECANDIDATES KC2 ON KC1.PID < KC2.PID AND KC1.KEYLINE = KC2.KEYLINE
	                            JOIN #KEYLINEDUPES KLD1 ON KC1.KEYLINE = KLD1.KEYLINE 
	                            JOIN #KEYLINEDUPES KLD2 ON KC2.KEYLINE = KLD2.KEYLINE 
                            ORDER BY KC1.KEYLINE

                            SELECT IDENTITY(int, 1,1) [ROWNUMBER], '2 - FECCMTEID MATCH' [METHOD] , 
                            KC1.FECCMTEID [SEARCHTEXT1] , KC1.PID [PID1] , KC1.FECCMTEID [FECCMTEID1],
                            KC1.PREFIX [PREFIX1] , KC1.FNAME [FNAME1] , KC1.MNAME [MNAME1] , KC1.LNAME [LNAME1] , KC1.cPREFIX [cPREFIX1] , KC1.cFNAME [cFNAME1] , KC1.cMNAME [cMNAME1] , KC1.cLNAME [cLNAME1] , KC1.cTITLE [cTITLE1] , 
                            KC1.STREET [STREET1], KC1.ADDR1 [ADDR1] , KC1.CITY [CITY1] , KC1.STATE [STATE1] , KC1.ZIP [ZIP1] , KC1.PLUS4 [PLUS41] ,
                            KC2.FECCMTEID [SEARCHTEXT2] , KC2.PID [PID2] , KC2.FECCMTEID [FECCMTEID2],
                            KC2.PREFIX [PREFIX2] , KC2.FNAME [FNAME2] , KC2.MNAME [MNAME2] , KC2.LNAME [LNAME2] , KC2.cPREFIX [cPREFIX2] , KC2.cFNAME [cFNAME2] , KC2.cMNAME [cMNAME2] , KC2.cLNAME [cLNAME2] , KC2.cTITLE [cTITLE2] , 
                            KC2.STREET [STREET2], KC2.ADDR2 [ADDR2] , KC2.CITY [CITY2] , KC2.STATE [STATE2] , KC2.ZIP [ZIP2] , KC2.PLUS4 [PLUS42] 
                            INTO	#FECIDSUMMARY
                            FROM	#FECIDDUPECANDIDATES KC1
	                            JOIN #FECIDDUPECANDIDATES KC2 ON KC1.PID < KC2.PID AND KC1.FECCMTEID = KC2.FECCMTEID
	                            JOIN #FECIDDUPES KLD1 ON KC1.FECCMTEID = KLD1.FECCMTEID 
	                            JOIN #FECIDDUPES KLD2 ON KC2.FECCMTEID = KLD2.FECCMTEID
                            ORDER BY KC1.FECCMTEID

                            --===== DUMP RESULTS INTO A FINAL TABLE
                            SELECT * INTO #ROLLUP FROM #KEYLINESUMMARY
                            UNION ALL 
                            SELECT * FROM #FECIDSUMMARY 

                            --===== CLEANUP
                            UPDATE T SET 
	                            T.FECCMTEID1 = ISNULL(T.FECCMTEID1,''),
	                            T.[SEARCHTEXT1] = ISNULL(T.[SEARCHTEXT1],''), 
	                            T.[PREFIX1] = ISNULL(T.[PREFIX1],''), 
	                            T.[FNAME1] = ISNULL(T.[FNAME1],''), 
	                            T.[MNAME1] = ISNULL(T.[MNAME1],''), 
	                            T.[LNAME1] = ISNULL(T.[LNAME1],''), 
	                            T.[cPREFIX1] = ISNULL(T.[cPREFIX1],''), 
	                            T.[cFNAME1] = ISNULL(T.[cFNAME1],''), 
	                            T.[cMNAME1] = ISNULL(T.[cMNAME1],''), 
	                            T.[cLNAME1] = ISNULL(T.[cLNAME1],''), 
	                            T.[cTITLE1] = ISNULL(T.[cTITLE1],''), 
	                            T.[STREET1] = ISNULL(T.[STREET1],''), 
	                            T.[ADDR1] = ISNULL(T.[ADDR1],''), 
	                            T.[CITY1] = ISNULL(T.[CITY1],''), 
	                            T.[STATE1] = ISNULL(T.[STATE1],''), 
	                            T.[ZIP1] = ISNULL(T.[ZIP1],''), 
	                            T.[PLUS41] = ISNULL(T.[PLUS41],''), 
	                            T.FECCMTEID2 = ISNULL(T.FECCMTEID2,''),
	                            T.[SEARCHTEXT2] = ISNULL(T.[SEARCHTEXT2],''), 
	                            T.[PREFIX2] = ISNULL(T.[PREFIX2],''), 
	                            T.[FNAME2] = ISNULL(T.[FNAME2],''), 
	                            T.[MNAME2] = ISNULL(T.[MNAME2],''), 
	                            T.[LNAME2] = ISNULL(T.[LNAME2],''), 
	                            T.[cPREFIX2] = ISNULL(T.[cPREFIX2],''), 
	                            T.[cFNAME2] = ISNULL(T.[cFNAME2],''), 
	                            T.[cMNAME2] = ISNULL(T.[cMNAME2],''), 
	                            T.[cLNAME2] = ISNULL(T.[cLNAME2],''), 
	                            T.[cTITLE2] = ISNULL(T.[cTITLE2],''), 
	                            T.[STREET2] = ISNULL(T.[STREET2],''), 
	                            T.[ADDR2] = ISNULL(T.[ADDR2],''), 
	                            T.[CITY2] = ISNULL(T.[CITY2],''), 
	                            T.[STATE2] = ISNULL(T.[STATE2],''), 
	                            T.[ZIP2] = ISNULL(T.[ZIP2],''), 
	                            T.[PLUS42] = ISNULL(T.[PLUS42],'') 
                            FROM	#ROLLUP T

                            --===== QUERY THE FINAL TABLE
                            SELECT * FROM #ROLLUP T ORDER BY CAST(LEFT(T.METHOD,1) AS INTEGER), T.ROWNUMBER
             ";
            #endregion

            string sql = @"SELECT ROWNUMBER,METHOD,SEARCHTEXT1,PID1,FECCMTEID1,PREFIX1,FNAME1,MNAME1,LNAME1,cPREFIX1,cFNAME1,cMNAME1,cLNAME1,cTITLE1,STREET1,ADDR1,CITY1,STATE1,ZIP1,PLUS41,SEARCHTEXT2,PID2,FECCMTEID2,PREFIX2,FNAME2,MNAME2,LNAME2,cPREFIX2,cFNAME2,cMNAME2,cLNAME2,cTITLE2,STREET2,ADDR2,CITY2,STATE2,ZIP2,PLUS42 FROM Z_DUP_DT WHERE ZDupID = (SELECT MAX(ZDupID) FROM Z_DUP WHERE [TYPE] = 'NON-IND') ORDER BY CAST(LEFT(METHOD,1) AS INTEGER), ROWNUMBER";

            string countsql = @"SELECT COUNT(ZDupID) AS DATA_COUNT FROM Z_DUP_DT";
            var qcount = _dbFactory.getContext().Database.SqlQuery<int>(countsql).First();

            //if count is zero, assume timer is not present.
            if (qcount == 0)
            {
                var q1 = _dbFactory.getContext().Database.SqlQuery<duplicatenonindividual>(_sql);
                return q1.ToList();
            }
            else
            {
                var q2 = _dbFactory.getContext().Database.SqlQuery<duplicatenonindividual>(sql);
                return q2.ToList();
            }


        }

        #endregion

        #region [[ Treasury ]]

        public List<AccountRegister> get_accountregister(AccountRegisterSearch filter)
        {
            //var sql = "EXEC dbo.iTreasury_CashBal 1, 3, '1/1/2010', '2/5/2014', null";
            var sql = "EXEC dbo.iTreasury_CashBal ";

            sql = sql + (filter.CENTERID == null ? "null" : filter.CENTERID.ToString());
            sql = sql + ", ";

            sql = sql + (filter.TXNTYPEID == null ? "null" : filter.TXNTYPEID.ToString());
            sql = sql + ", ";

            sql = sql + (filter.TXNDTEFROM == null ? "null" : "'" + filter.TXNDTEFROM.ToString() + "'");
            sql = sql + ", ";

            sql = sql + (filter.TXNDTETO == null ? "null" : "'" + filter.TXNDTETO.ToString() + "'");
            sql = sql + ", ";

            sql = sql + (filter.VENDOR == null ? "null" : "'" + filter.VENDOR.ToString() + "'");

            var q = _dbFactory.getContext().Database.SqlQuery<AccountRegister>(sql);

            return q.ToList();
        }

        public List<AccountCashSummary> get_accountcashsummary()
        {
            var sql = "EXEC dbo.iTreasury_CashBalSummary ";

            var q = _dbFactory.getContext().Database.SqlQuery<AccountCashSummary>(sql);

            return q.ToList();
        }

        #endregion

        #region [[ Invoice ]]

        public List<InvoiceSummary> get_invoicesummary()
        {
            var sql = @"SELECT FUNDCODE, FUNDCODE + ' Open Total' AS DESCRIP, SUM(CBALANCE) AS FUNDBALANCE FROM V_ENTITY_INVOICE WHERE [STATUS] = 'OPEN' GROUP BY FUNDCODE";

            var q = _dbFactory.getContext().Database.SqlQuery<InvoiceSummary>(sql);

            return q.ToList();
        }

        public List<InvoiceTotals> get_invoicetotals(string fundcode)
        {
            var sql = "EXEC dbo.iInvoice_Balance '" + fundcode + "'";

            var q = _dbFactory.getContext().Database.SqlQuery<InvoiceTotals>(sql);

            return q.ToList();
        }

        #endregion

        #region [[ Base service functions to pull counts and list from (dbd) tables) (2015-07-13 by Junho) ]]
        
        public List<dbd_number> dbd_numbers_fromListData(string dataPoint)
        {
            string sql = string.Format("SELECT * FROM dbo.fn_dbd_numbers_fromListData('{0}')", dataPoint);
            var q = _dbFactory.getContext().Database.SqlQuery<dbd_number>(sql);
            return q.ToList();
        }

        public List<dbd_listData> dbd_listData(string dataPoint, string sortOptions, int pageSize=10, int pageNo=1)
        {
            string sql = String.Format("EXEC dbo.z_dbd_paged_listData '{0}','{1}', {2}, {3}", dataPoint, sortOptions, pageSize, pageNo);
            var q = _dbFactory.getContext().Database.SqlQuery<dbd_listData>(sql);
            return q.ToList();
        }

        #endregion

        #region [[ Compliance - Custom Datasets ]]
        
        #region [ Itemized Best Efforts ]
        // List of Fund Codes that have related data in the report
        public List<string> get_itemizedBestEfforts_fundCodes()
        {
            string sql = "SELECT DISTINCT str2 AS FUNDCODE FROM dbd_listData WHERE dataPoint='Itemized Best Efforts'";
            var q = _dbFactory.getContext().Database.SqlQuery<string>(sql);
            return q.ToList();
        }

        // Data for Pie Chart
        public List<dbd_number> get_itemizedBestEfforts(string fundCodes="", string dateStart="", string dateEnd="")
        {

            //set dates in mm/dd/yyyy format
            DateTime fromdt;
            DateTime todt;
            if (!String.IsNullOrEmpty(dateStart))
            {
                fromdt = DateTime.Parse(dateStart);
                dateStart = fromdt.ToShortDateString();
            }

            if (!String.IsNullOrEmpty(dateEnd))
            {
                todt = DateTime.Parse(dateEnd);
                dateEnd = todt.ToShortDateString();
            }

            string sql = "";
            if (!String.IsNullOrEmpty(fundCodes) && fundCodes.StartsWith("·") && fundCodes.EndsWith("·") 
                && !String.IsNullOrEmpty(dateStart) && !String.IsNullOrEmpty(dateEnd))
                sql = String.Format("SELECT * FROM dbo.fn_dbd_compliance_bestEfforts('{0}','{1}','{2}')", fundCodes, dateStart, dateEnd);
            else if (!String.IsNullOrEmpty(fundCodes) && fundCodes.StartsWith("·") && fundCodes.EndsWith("·") 
                     && (String.IsNullOrEmpty(dateStart) || String.IsNullOrEmpty(dateEnd)))
                sql = String.Format("SELECT * FROM dbo.fn_dbd_compliance_bestEfforts('{0}',default,default)", fundCodes);
            else if ((String.IsNullOrEmpty(fundCodes) || !fundCodes.StartsWith("·") || !fundCodes.EndsWith("·"))
                     && (!String.IsNullOrEmpty(dateStart) && !String.IsNullOrEmpty(dateEnd)))
                sql = String.Format("SELECT * FROM dbo.fn_dbd_compliance_bestEfforts(default,'{0}','{1}')", dateStart, dateEnd);
            else
                sql = "SELECT * FROM dbo.fn_dbd_compliance_bestEfforts(default,default,default)";

            var q = _dbFactory.getContext().Database.SqlQuery<dbd_number>(sql);
            return q.ToList();
        }
        #endregion

        #endregion

        public int deleteRecord(string _sql)
        {
            var q = _dbFactory.getContext().Database.SqlQuery<Int32>(_sql);
            return q.FirstOrDefault();
        }

        public int GetUnbalancedEarmarksCount()
        {
            string sql = "SELECT COUNT(*) FROM dbo.fn_UnbalancedEarmarks()";
            int count = _dbFactory.getContext().Database.SqlQuery<int>(sql).FirstOrDefault();
            return count;
        }

        public List<UnbalancedEarmark> GetUnbalancedEarmarks(int pageSize, int page, string sortColumn = "", bool sortDesc = true)
        {
            string sql = "SELECT * FROM dbo.fn_UnbalancedEarmarks()";
            var q = _dbFactory.getContext().Database.SqlQuery<UnbalancedEarmark>(sql).Where(x => true);

            if (!string.IsNullOrEmpty(sortColumn))
            {
                var propertyInfo = typeof(UnbalancedEarmark).GetProperty(sortColumn);
                q = sortDesc ?
                    q.OrderByDescending(ue => propertyInfo.GetValue(ue)) :
                    q.OrderBy(ue => propertyInfo.GetValue(ue));
            }

            if (pageSize > 0 && page > 0)
            {
                q = q.Skip(pageSize * (page - 1)).Take(pageSize);
            }

            return q.ToList();
        }

        public int GetUnbalancedWinRedEarmarksCount()
        {
            string sql = "SELECT COUNT(*) FROM dbo.fn_UnbalancedWinRedEarmarks()";
            int count = _dbFactory.getContext().Database.SqlQuery<int>(sql).FirstOrDefault();
            return count;
        }

        public List<UnbalancedEarmark> GetUnbalancedWinRedEarmarks(int pageSize, int page, string sortColumn = "", bool sortDesc = true)
        {
            string sql = "SELECT * FROM dbo.fn_UnbalancedWinRedEarmarks()";
            var q = _dbFactory.getContext().Database.SqlQuery<UnbalancedEarmark>(sql).Where(x => true);

            if (!string.IsNullOrEmpty(sortColumn))
            {
                var propertyInfo = typeof(UnbalancedEarmark).GetProperty(sortColumn);
                q = sortDesc ?
                    q.OrderByDescending(ue => propertyInfo.GetValue(ue)) :
                    q.OrderBy(ue => propertyInfo.GetValue(ue));
            }

            if (pageSize > 0 && page > 0)
            {
                q = q.Skip(pageSize * (page - 1)).Take(pageSize);
            }

            return q.ToList();
        }

        public int GetComplianceBadAddressesCount()
        {
            string sql = "SELECT COUNT(*) FROM dbo.fn_complianceBadAddresses()";
            int count = _dbFactory.getContext().Database.SqlQuery<int>(sql).FirstOrDefault();
            return count;
        }

        public List<ComplianceBadAddress> GetComplianceBadAddresses(int pageSize, int page, string sortColumn = "", bool sortDesc = true)
        {
            string sql = "SELECT * FROM dbo.fn_complianceBadAddresses()";
            var q = _dbFactory.getContext().Database.SqlQuery<ComplianceBadAddress>(sql).Where(x => true);

            if (!string.IsNullOrEmpty(sortColumn))
            {
                var propertyInfo = typeof(ComplianceBadAddress).GetProperty(sortColumn);
                q = sortDesc ?
                    q.OrderByDescending(ue => propertyInfo.GetValue(ue)) :
                    q.OrderBy(ue => propertyInfo.GetValue(ue));
            }

            if (pageSize > 0 && page > 0)
            {
                q = q.Skip(pageSize * (page - 1)).Take(pageSize);
            }

            return q.ToList();
        }

        public int GetComplianceAdjustmentSummaryCount(string adjustment)
        {
            string sql = "SELECT COUNT(*) FROM [dbo].[fn_AdjustmentSummaryDetail]() where ADJDTE between DATEADD(qq, DATEDIFF(qq, 0, GETDATE()), 0) and DATEADD(dd, -1, DATEADD(qq, DATEDIFF(qq, 0, GETDATE()) + 1, 0))";
            if (adjustment != "All") {
                sql += " and adjustment = '" + adjustment + "'";
            }
            int count = _dbFactory.getContext().Database.SqlQuery<int>(sql).FirstOrDefault();
            return count;
        }

        public List<ComplianceAdjustmentSummary> GetComplianceAdjustmentSummary(string adjustment, int pageSize, int page, string sortColumn = "", bool sortDesc = true)
        {
            string sql = "SELECT * FROM [dbo].[fn_AdjustmentSummaryDetail]() where ADJDTE between DATEADD(qq, DATEDIFF(qq, 0, GETDATE()), 0) and DATEADD(dd, -1, DATEADD(qq, DATEDIFF(qq, 0, GETDATE()) + 1, 0))";
            if (adjustment != "All")
            {
                sql += " and adjustment = '" + adjustment + "'";
            }
            var q = _dbFactory.getContext().Database.SqlQuery<ComplianceAdjustmentSummary>(sql).Where(x => true);

            if (!string.IsNullOrEmpty(sortColumn))
            {
                var propertyInfo = typeof(ComplianceAdjustmentSummary).GetProperty(sortColumn);
                q = sortDesc ?
                    q.OrderByDescending(ue => propertyInfo.GetValue(ue)) :
                    q.OrderBy(ue => propertyInfo.GetValue(ue));
            }

            if (pageSize > 0 && page > 0)
            {
                q = q.Skip(pageSize * (page - 1)).Take(pageSize);
            }

            return q.ToList();
        }

        /*
        public int GetExpiringCodedOverlimitsCount()
        {
            string sql = "SELECT * FROM dbo.fn_ExpiringCodedOverlimits()";
            return _dbFactory.getContext().Database.SqlQuery<ExpiringOverlimitGift>(sql).Count();
        }

        public List<ExpiringOverlimitGift> GetExpiringCodedOverlimits(int pageSize, int page, string sortColumn = "", bool sortDesc = true)
        {
            string sql = "SELECT * FROM dbo.fn_ExpiringCodedOverlimits()";
            var q = _dbFactory.getContext().Database.SqlQuery<ExpiringOverlimitGift>(sql).Where(x => true);

            if (!string.IsNullOrEmpty(sortColumn))
            {
                var propertyInfo = typeof(ExpiringOverlimitGift).GetProperty(sortColumn);
                q = sortDesc ?
                    q.OrderByDescending(ue => propertyInfo.GetValue(ue)) :
                    q.OrderBy(ue => propertyInfo.GetValue(ue));
            }

            if (pageSize > 0 && page > 0)
            {
                q = q.Skip(pageSize * (page - 1)).Take(pageSize);
            }

            return q.ToList();
        }
        */
        public List<zLoadLog_ext> get_all_zLoadLog_ext(string where, string orderBy, int pageSize, int pageNo)
        {
            string sql = (!string.IsNullOrEmpty(where) ? string.Format("SELECT * FROM zLoadLog WHERE {0}", where) : "SELECT * FROM zLoadLog");

            var q = _dbFactory.getContext().Database.SqlQuery<zLoadLog_ext>(String.Format("EXEC dbo.get_pagedDataSet '{0}', '{1}', {2}, {3}", sql, orderBy, pageSize, pageNo));

            return q.ToList();
        }
    }
}