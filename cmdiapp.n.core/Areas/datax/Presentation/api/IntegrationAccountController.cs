﻿using cmdiapp.n.core.Areas.datax.Domain.Models;
using cmdiapp.n.core.Areas.datax.Domain.Models.ViewModels;
using cmdiapp.n.core.Areas.datax.Domain.Services;
using cmdiapp.n.core.Core;
using cmdiapp.n.core.Core.App_Start;
using cmdiapp.n.core.Domain.ViewModels;
using cmdiapp.n.core.Presentation.Controllers;
using Ninject;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Web.Http;

namespace cmdiapp.n.core.Areas.datax.Presentation.api
{
    [RoutePrefix("datax/api/IntegrationAccount")]
    public class IntegrationAccountController : ApiController
    {
        private IAccountService _service;

        public IntegrationAccountController()
        {
            _service = NinjectMVC.kernel.Get<IAccountService>();
        }

        [HttpGet, Route("{id}")]
        [apiAuthorize]
        public Account GetAccount(int id)
        {
            return _service.Get(id);
        }

        [HttpGet, Route("Project")]
        [apiAuthorize]
        public List<Account> GetAccountsForCurrentProject()
        {
            return _service.GetAccountsForCurrentProject();
        }

        [HttpGet, Route("Project/{callerId}")]
        [apiAuthorize]
        public List<Account> GetAccountsForCurrentProject(int callerId)
        {
            //For wealth Engine
            if (callerId == 9)
            {
                var config = session.currentDomain_projectConfigs.Where(c => c.Key == "Wealth Engine - User name and password").FirstOrDefault();
                if (config == null)
                    return new List<Account>();
                else
                {
                    string val = config.Val;
                    string uid = val.Split('&')[0].Replace("uid=","");
                    return new List<Account>() { new Account() { clientCallerAccountId = uid, client = uid } };
                }
            }
            else
                return _service.GetAccountsForCurrentProject(callerId);
        }

        [HttpGet, Route("ProjectCount/{callerId}")]
        public int GetCountofAccountsForCurrentProject(int callerId)
        {
            //for Wealth Engine
            if (callerId == 9)
            {
                return session.currentDomain_projectConfigs.Where(c => c.Key == "Wealth Engine - User name and password").Count();
            }
            else
                return _service.GetCountofAccountsForCurrentProject(callerId);
        }

        [HttpPost, Route("Delete/{id}")]
        [apiAuthorize]
        public genericResponse Delete(int id)
        {
            return _service.Delete(id);
        }

        [HttpPost, Route("Save")]
        [apiAuthorize]
        public genericResponse Save(Account account)
        {
            return _service.Save(account);
        }

        [HttpPost, Route("New")]
        [apiAuthorize]
        public Account New(NewAccountInfo info)
        {
            return _service.GetNewAccountModel(info);
        }
    }
}