﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Data.Linq;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Xml.Linq;

using cmdiapp.n.core.Domain.ViewModels;
using cmdiapp.n.core.Areas.crm.Domain.Data;

namespace cmdiapp.n.core.Areas.crm.Domain.Models
{
    // (view)v_x_vm_voter  -  Used for a single PEOPLE Add & Update ]]

    [Table("v_x_vm_voter")]
    public class Voter : _entityBase_crm, iItemType
    {

        [Key]
        [Column]
        public int jt_people_voter_Id { get; set; }       

        [Column]
        public int PID { get; set; }

        [Column]
        public Int64 voterSeq { get; set; }

        [Column]
        public string Gender { get; set; }
        public string Gender_ { get { return (Gender == "M" ? "Male" : (Gender == "F" ? "Female" : "")); } }

        [Column]
        public DateTime? DOB { get; set; }
        public string DOB_ { get { return string.Format("(born {0:MM/dd/yyyy})", DOB); } }

        [Column]
        public int? Age { get; set; }
        public string Age_ { get { return (Age!=null && Age.Value>0 ? "Age "+Age.Value.ToString() : "" ); } }

        [Column]
        public string MaritalStatus { get; set; }

        [Column]
        public string Ethnic_Description { get; set; }

        [Column]
        public string Religion { get; set; }

        [Column]
        public string DwellingType { get; set; }

        [Column]
        public string EstimatedIncome { get; set; }

        [Column]
        public string Education { get; set; }

        [Column]
        public string OccupationGroup { get; set; }

        [Column]
        public string Occupation { get; set; }

        [Column]
        public int HHCount { get; set; }

        [Column]
        public string HHGender { get; set; }

        [Column]
        public string Party { get; set; }

        [Column]
        public string HHParties { get; set; }

        [Column]
        public string PresenceOfChildren { get; set; }
        
        [Column]
        public DateTime? RegistrationDate { get; set; }

        [Column]
        public int? VoterSince { get; set; }

        [Column]
        public int? YearsSinceRegistered { get; set; }

        [Column]
        public string County { get; set; }

        [Column]
        public string Precinct { get; set; }

        [Column]
        public int? VoteFrequency { get; set; }

        [Column]
        public string VoteHistories { get; set; }
        public List<VoteHistory> VoteHistories_
        {
            get
            {
                string xml_ = @"<r00t>" + VoteHistories + @"</r00t>";
                XDocument _vHistories = XDocument.Parse(xml_);

                var q = from a in _vHistories.Descendants("vote")
                        select new VoteHistory()
                        {
                            ElectionType = (a.Element("ElectionType") != null ? Convert.ToString(a.Element("ElectionType").Value) : ""),
                            ElectionDate = (a.Element("ElectionDate") != null ? Convert.ToDateTime(a.Element("ElectionDate").Value) : DateTime.MinValue),
                            Voting = (a.Element("Voting") != null ? Convert.ToString(a.Element("Voting").Value) : "")
                        };

                return q.OrderByDescending(a=> a.ElectionDate).ToList();
            }
        }
    }

    [NotMapped]
    public class VoteHistory
    {
        public string ElectionType { get; set; }
        public DateTime? ElectionDate { get; set; }
        public string Voting { get; set; }

        public string Election
        {
            get
            {
                return (!string.IsNullOrEmpty(ElectionType) && ElectionDate.HasValue ? string.Format("{0} ({1:MM/dd/yyyy})", ElectionType, ElectionDate) : ""); 
            }
        }
    }
}