﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Text.RegularExpressions;

namespace cmdiapp.n.core.Areas.query.Domain.Models
{
    #region [[[ class.q_sortOptionItem_def ]]] Retreive/objectize a "q_sortOption" definition.
    /*
            string q_sortOption_def_xml = @"
                <qsoi>
                  <i>pspp_fname</i>     -> Id
                  <c>First Name</c>     -> Caption
                  <n>P.FNAME</n>        -> Field Name
                  <o>ASC</o>            -> Order Type (ASCending | DESCending)
                </qsoi>
            ";
     */
    public class q_sortOption
    {
        public string id { get; set; }                 
        public string caption { get; set; }        
        public string order { get; set; }
        public int idx { get; set; }
    }

    public class q_sortOption_S : q_sortOption
    {
        public string name { get; set; }
    }

    #endregion


}