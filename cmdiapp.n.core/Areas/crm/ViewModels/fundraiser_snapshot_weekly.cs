﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Data;
using System.Linq;
using System.Web.Mvc;
using System.Data.Linq;

namespace cmdiapp.n.core.Areas.crm.Domain.ViewModels
{
    // SQL Function (db.fundraiser_snapshot_weekly)
    public class fundraiser_snapshot_weekly
    {
        public int trackno { get; set; }
        public string FNAME { get; set; }
        public string LNAME { get; set; }
        public string FULLNAME { get; set; }

        public decimal? cRaised { get; set; }
        public decimal? cRollup { get; set; }
        public decimal? commitment { get; set; }
        public int? no_donors { get; set; }
        public int? no_fundraisers { get; set; }
        public decimal? qtr_total { get; set; }
        public int? qtr_no_donations { get; set; }
        public decimal? week_total { get; set; }
        public int? week_no_donations { get; set; }
        public decimal? prevWeek_total { get; set; }
        public int? prevWeek_no_donations { get; set; }

    }
}