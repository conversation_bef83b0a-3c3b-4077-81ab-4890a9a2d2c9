﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.Mvc;

using System.Data;
using AutoMapper;

using cmdiapp.n.core.Core;
using cmdiapp.n.core.Core.App_Start;
using cmdiapp.n.core.Domain.Models;
using cmdiapp.n.core.Domain.ViewModels;
using cmdiapp.n.core.Areas.crm.Core;
using cmdiapp.n.core.Areas.crm.Domain.Data;
using cmdiapp.n.core.Areas.crm.Domain.Models;
using cmdiapp.n.core.Areas.crm.Domain.ViewModels;
using cmdiapp.n.core.Areas.crm.Domain.Repositories;
using cmdiapp.n.core.Areas.crm.Domain.Services;
using cmdiapp.n.core.Library;
using cmdiapp.n.core.Presentation.Controllers;
using cmdiapp.n.core.Areas.dataService.Domain.Data;
using cmdiapp.n.core.Areas.dataService.Domain.Services;
using cmdiapp.n.core.Domain.Repositories;
using cmdiapp.n.core.Domain.Services;
using cmdiapp.n.core.Areas.query;

using Ninject;
using Ninject.Web.Mvc;
using System.Collections.ObjectModel;
using System.Xml.Linq;
using System.IO;

namespace cmdiapp.n.core.Areas.crm.Presentation.Controllers
{
    [Authorize]
    public class PayRequestController : Controller
    {
        #region [[ Declaration ]]
        private I_entity_crm _entity_crm;
        userSession _userSession = session.userSession;
        #endregion

        public PayRequestController()
        {
            _entity_crm = I_entityManager_crm.getEntity();
        }

        #region [[ My Payment Requests Search ]]
        [HttpGet, Route("crm/PayRequest/MyPaymentRequests")]
        [aElementAuthorize(AccessElement = "/crm/PayRequest/MyPaymentRequests", AccessLevel = "v")]
        public ActionResult MyPaymentRequests(string udlo)
        {
            return Redirect("/query/search?qdefname=crm_mypayrequestSearch&jsName=mypayrequestSearch" + (!String.IsNullOrEmpty(udlo) && udlo.ToLower() == "y" ? "&udlo=y" : ""));
        }
        #endregion

        #region [[ Payment Requests Search ]]
        [HttpGet, Route("crm/PayRequest/PaymentRequests")]
        [aElementAuthorize(AccessElement = "/crm/PayRequest/PaymentRequests", AccessLevel = "v")]
        public ActionResult PaymentRequests(string udlo)
        {
            return Redirect("/query/search?qdefname=crm_payrequestSearch&jsName=payrequestSearch" + (!String.IsNullOrEmpty(udlo) && udlo.ToLower() == "y" ? "&udlo=y" : ""));
        }
        #endregion

        #region [[ Add Payment Request ]]
        [aElementAuthorize(AccessElement = "/crm/PayRequest/MyPaymentRequests", AccessLevel = "a")]
        public ActionResult Add(string udlo, int entityid, bool? myPayReq)
        {
            pageInfo _pageInfo = new pageInfo();
            if (udlo == "y")
                _pageInfo.LayoutPage = crmConstants.DEFAULT_LAYOUT_BLANK;
            else
                _pageInfo.LayoutPage = "";

            _pageInfo.title = "Payment Request";
            _pageInfo.title_short = "Add";
            _pageInfo.pageEntityz_uniqueId = "0";
            if (entityid.Equals(0))
            {
                _pageInfo.pageData1 = "0";
            }
            else
            {
                _pageInfo.pageData1 = entityid.ToString();
            }
            _pageInfo.pageData2 = myPayReq.ToString();
            _pageInfo.pageData3 = "0";
            _pageInfo.pageData4 = crmSession.UID().ToString();
            _pageInfo.pageData5 = "0";
            _pageInfo.pageData6 = "0";
            return View(@"../Expenditure/PayRequest/PayRequestEdit", _pageInfo);
        }
        #endregion

        #region [[ Edit Payment Request ]]
        [aElementAuthorize(AccessElement = "/crm/PayRequest/MyPaymentRequests", AccessLevel = "e")]
        public ActionResult Edit(string udlo, int id, int entityid, bool? myPayReq, bool? autoEdit)
        {
            pageInfo _pageInfo = new pageInfo();
            if (udlo == "y")
                _pageInfo.LayoutPage = crmConstants.DEFAULT_LAYOUT_BLANK;
            else
                _pageInfo.LayoutPage = "";

            _pageInfo.title = "Payment Request";
            _pageInfo.title_short = "Edit";
            _pageInfo.pageEntityz_uniqueId = id.ToString();
            if (entityid.Equals(0))
            {
                _pageInfo.pageData1 = "0";
            }
            else
            {
                _pageInfo.pageData1 = entityid.ToString();
            }
            _pageInfo.pageData2 = myPayReq.ToString();
            _pageInfo.pageData3 = "0";
            _pageInfo.pageData4 = crmSession.UID().ToString();
            _pageInfo.pageData5 = "0";
            _pageInfo.pageData6 = "0";
            _pageInfo.pageData7 = autoEdit.ToString();
            return View(@"../Expenditure/PayRequest/PayRequestEdit", _pageInfo);
        }
        #endregion

        #region [[ Approve Payment Request ]]
        [aElementAuthorize(AccessElement = "/crm/PayRequest/PaymentRequests", AccessLevel = "e")]
        public ActionResult Approve(string udlo, int id, int entityid, bool? myPayReq)
        {
            pageInfo _pageInfo = new pageInfo();
            if (udlo == "y")
                _pageInfo.LayoutPage = crmConstants.DEFAULT_LAYOUT_BLANK;
            else
                _pageInfo.LayoutPage = "";

            _pageInfo.title = "Payment Request";
            _pageInfo.title_short = "Approve";
            _pageInfo.pageEntityz_uniqueId = id.ToString();
            if (entityid.Equals(0))
            {
                _pageInfo.pageData1 = "0";
            }
            else
            {
                _pageInfo.pageData1 = entityid.ToString();
            }
            _pageInfo.pageData2 = myPayReq.ToString();
            _pageInfo.pageData3 = "0";
            _pageInfo.pageData4 = crmSession.UID().ToString();
            _pageInfo.pageData5 = "0";
            _pageInfo.pageData6 = "0";
            // change status to under review if submitted
            PAYREQ _record = _entity_crm.Single<PAYREQ>(a => a.PAYREQID == id);
            if (_record.STATUSID == 2)
            {
                _record.STATUSID = 3;
                _entity_crm.Update(_record);
                _entity_crm.CommitChanges();
            }

            return View(@"../Expenditure/PayRequest/PayRequestEdit", _pageInfo);
        }
        #endregion

        #region [[ Add New Vendor ]]
        [aElementAuthorize(AccessElement = "/crm/PayRequest/PaymentRequests", AccessLevel = "a")]
        public ActionResult AddVendor(string udlo, string txntype)
        {
            pageInfo _pageInfo = new pageInfo();
            if (udlo == "y")
                _pageInfo.LayoutPage = crmConstants.DEFAULT_LAYOUT_BLANK;
            else
                _pageInfo.LayoutPage = "";

            _pageInfo.title = "Add a New Vendor";
            _pageInfo.title_short = "Add";
            _pageInfo.pageEntityz_uniqueId = "0";
            _pageInfo.pageData1 = txntype;
            _pageInfo.pageData2 = "0";
            return View(@"../Expenditure/VendorEdit", _pageInfo);
        }
        #endregion

        #region [[ Add Ultimate Vendor record ]]
        [aElementAuthorize(AccessElement = "/crm/PayRequest/MyPaymentRequests", AccessLevel = "a")]
        public ActionResult AddUV(string udlo, int entityid, int linkpayreqid, bool? myPayReq, int fundid, int typeid, DateTime reqdte)
        {
            pageInfo _pageInfo = new pageInfo();
            if (udlo == "y")
                _pageInfo.LayoutPage = crmConstants.DEFAULT_LAYOUT_BLANK;
            else
                _pageInfo.LayoutPage = "";

            _pageInfo.title = "Payment Request Ultimate Vendor Record";
            _pageInfo.title_short = "Add";
            _pageInfo.pageEntityz_uniqueId = "0";
            if (entityid.Equals(0))
            {
                _pageInfo.pageData1 = "0";
            }
            else
            {
                _pageInfo.pageData1 = entityid.ToString();
            }
            _pageInfo.pageData2 = myPayReq.ToString();
            _pageInfo.pageData3 = linkpayreqid.ToString();
            _pageInfo.pageData4 = crmSession.UID().ToString();
            _pageInfo.pageData5 = fundid.ToString();
            _pageInfo.pageData6 = typeid.ToString();
            _pageInfo.pageData7 = reqdte.ToString();
            return View(@"../Expenditure/PayRequest/PayRequestEdit", _pageInfo);
        }

        #endregion

        #region [[ Upload and download attachement ]]
        [HttpPost]
        public JsonResult UploadPayReqDoc(HttpPostedFileBase file, int payreqid)
        {
            genericResponse _response;
            try
            {
                //Let us check for the file size first
                if (file.ContentLength <= (4 * 1024 * 1024))
                {
                    byte[] ByteArray = new byte[file.ContentLength];
                    file.InputStream.Read(ByteArray, 0, file.ContentLength);

                    PAYREQDOC _record;

                    _record = _entity_crm.Single<PAYREQDOC>(a => a.PAYREQID == payreqid);

                    if (_record == null)
                    {
                        //new record
                        _record = new PAYREQDOC();
                        _record.PAYREQID = payreqid;
                        _record.CONTENT = ByteArray;
                        _record.FILENAME = file.FileName;
                        _record.updating_uid = (short)_userSession.UserId_i;
                        _entity_crm.Add(_record);
                    }
                    else
                    {
                        //existing record
                        _record.CONTENT = ByteArray;
                        _record.FILENAME = file.FileName;
                        _record.updating_uid = (short)_userSession.UserId_i;
                        _entity_crm.Update(_record);
                    }
                    _entity_crm.CommitChanges();

                    //file name
                    PAYREQDOC fileDetails = new PAYREQDOC();
                    fileDetails.FILENAME = file.FileName;
                    List<iItemType> _dataset = new List<iItemType>();
                    _dataset.Add(fileDetails);

                    //Response Back
                    _response = new genericResponse() { success = true, results = _dataset.ToList() };

                }
                else
                {
                    _response = new genericResponse() { success = false, message = "Attachment exceeded the maximum size of 4 MB.", };
                }

                return Json(_response, JsonRequestBehavior.AllowGet);
            }
            catch
            {
                _response = new genericResponse() { success = false, message = "There is a problem processing attachment." };
                return Json(_response, JsonRequestBehavior.AllowGet);
            }
        }

        [aElementAuthorize(AccessElement = "/crm/PayRequest/MyPaymentRequests", AccessLevel = "a")]
        public ActionResult DownloadPayReqDoc(int payreqid)
        {
            try
            {
                PAYREQDOC _record;
                _record = _entity_crm.Single<PAYREQDOC>(a => a.PAYREQID == payreqid);

                //Grab the Data
                byte[] fileData = _record.CONTENT;
                //Add header and show the file as Inline
                Response.AppendHeader("Content-Disposition", "inline; filename=" + _record.FILENAME);
                return File(fileData, util.get_contentType(_record.FILENAME));
            }
            catch (FileNotFoundException)
            {
                throw new HttpException(404, string.Format("The file for Transaction {0} can not be processed at this time.", payreqid));
            }
            catch (Exception ex)
            {
                throw new HttpException();
            }

        }
        #endregion

        #region [[ Show Linked Payment Request ]]
        [aElementAuthorize(AccessElement = "/crm/PayRequest/MyPaymentRequests", AccessLevel = "v")]
        public ActionResult PayReqLinkedDetails(string udlo, int? id, bool? myPayReq)
        {
            pageInfo _pageInfo = new pageInfo();
            if (udlo == "y")
                _pageInfo.LayoutPage = crmConstants.DEFAULT_LAYOUT_BLANK;
            else
                _pageInfo.LayoutPage = "";

            _pageInfo.title = "Ultimate Vendor Details";
            _pageInfo.title_short = "Ultimate Vendor";
            _pageInfo.pageEntityz_uniqueId = id.ToString();
            _pageInfo.pageData2 = myPayReq.ToString();
            return View(@"../Expenditure/PayRequest/PayReqLinkedDetails", _pageInfo);
        }
        #endregion
    }
}