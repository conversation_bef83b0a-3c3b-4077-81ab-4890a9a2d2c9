﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace cmdiapp.n.core.Areas.crm.Domain.Models
{
    public class expChannel
    {
        public int channelId { get; set; }
        public string name { get; set; }
        public int order { get; set; }
        public bool show { get; set; }
        public int noOfSuppFlags { get; set; }
    }

    public class expChannelCount
    {
        public int index { get; set; }
        public int? count { get; set; }

        public int cacheId { get; set; }  // zCache.cacheId of cached Sql and Count-ready data
    }
}