﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Data.Linq;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

using cmdiapp.n.core.Areas.crm.Domain.Data;

namespace cmdiapp.n.core.Areas.crm.Domain.Models
{
    [Table("lkADDRTYPE")]
    public class AddressType : _entityBase_crm
    {
        [Column]
        [Key]
        public short ADDRTYPEID { get; set; }
        [Column]
        public string ADDRTYPE { get; set; }
        [Column]
        public string DESCRIP { get; set; }
        [Column]
        public byte DEFFLAG { get; set; }
        [Column]
        public byte SYSTEM { get; set; }
    }    
}