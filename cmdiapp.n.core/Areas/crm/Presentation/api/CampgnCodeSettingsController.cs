﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Net;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Web.Http;
using System.Web.Script.Serialization;
using System.Drawing;
using System.IO;
using System.Data.SqlClient;
using System.Collections.ObjectModel;
using System.Xml.Linq;

using Ninject;
using Ninject.Web.Mvc;

using AutoMapper;
using cmdiapp.n.core.Core;
using cmdiapp.n.core.Core.App_Start;
using cmdiapp.n.core.Domain.ViewModels;
using cmdiapp.n.core.Areas.crm.Core;
using cmdiapp.n.core.Areas.crm.Domain.Models;
using cmdiapp.n.core.Areas.crm.Domain.ViewModels;
using cmdiapp.n.core.Areas.crm.Domain.Data;
using cmdiapp.n.core.Areas.crm.Domain.Repositories;
using cmdiapp.n.core.Areas.crm.Domain.Services;
using cmdiapp.n.core.Areas.dataService.Domain.Data;
using cmdiapp.n.core.Areas.dataService.Domain.Models;
using cmdiapp.n.core.Areas.dataService.Domain.Services;
using cmdiapp.n.core.Presentation.Controllers;
using cmdiapp.n.core.Areas.query.Domain.Models;
using cmdiapp.n.core.Domain.Models;
using AutoMapper;
using System.Data;
using cmdiapp.n.core.Library;

namespace cmdiapp.n.core.Areas.crm.Presentation.Controllers.api
{
    [Authorize]
    public class CampgnCodeSettingsController : ApiController
    {
        #region [[ Declaration ]]
        private I_entity_crm _entity_crm;
        
        private readonly IReportDataService _reportService;
        private userSession _userSession;
        
        #endregion
        
        #region [[ (constructor) SettingsController ]]
        public CampgnCodeSettingsController()
        {
            _entity_crm = I_entityManager_crm.getEntity();
            _reportService = NinjectMVC.kernel.Get<IReportDataService>();
            _userSession = session.userSession;
        }
        #endregion

        #region[MONEY->SETTINGS->CAMPGNCODE]
        [HttpPost, Route("crm/api/CampgnCodeSettings/ExportCampgnCodeKey")]
        [apiAuthorize(AccessElement = "cmdiapp.dms.dmcampgn", AccessLevel = "v")]
        public string ExportCampgnCodeKey(string searchText)
        {
            if (string.IsNullOrEmpty(searchText)) searchText = "";

            searchParam param = new searchParam();
            param.searchText = searchText;

            string key_ = System.Guid.NewGuid().ToString();
            HttpRuntime.Cache.Insert(key_, param, null, DateTime.UtcNow.Add(new TimeSpan(1, 0, 1)), System.Web.Caching.Cache.NoSlidingExpiration);

            return key_;

        }

        [HttpGet, Route("crm/api/CampgnCodeSettings/ExportCampgnCode")]
        [apiAuthorize(AccessElement = "cmdiapp.dms.dmcampgn", AccessLevel = "v")]
        public string ExportCampgnCode(string key)
        {
            string sToday = String.Format("{0:yyyyMMMdd__hh_mm_ss tt}", DateTime.Now);
            string moduleName = "Campaign Code";
            string _sql_exportCampagnCode = @"select 
                                        CAMPGNCODE as [Code],
                                        DESCRIP as [Description]  
                                        from dmCAMPGN";

            searchParam _param = (searchParam)HttpRuntime.Cache[key];
            string fileName = moduleName + "_" + System.Guid.NewGuid() + "_" + _userSession.UserName + "_" + sToday;

            DataSet ds = null;

            if (string.IsNullOrEmpty(_param.searchText))
            {
                ds = _reportService.get_dataset_w_sql__single(_sql_exportCampagnCode, "CampgnCodes");
            }
            else
            {
                _param.searchText = Library.util.kill_sqlBlacklistWord(_param.searchText);

                string _where = "";

                if (!string.IsNullOrEmpty(_param.searchText))
                {
                    #region [ Split Search Text > string1, string2 & string3 and create "Where" statement ]
                    char[] delimiterChars = { ' ', ',', '.', ';', ':', '|', '\t' };

                    string[] words = _param.searchText.Split(delimiterChars);

                    if (words.Count() > 0)
                    {
                        _where = string.Format("CAMPGNCODE LIKE '%{0}%' OR DESCRIP LIKE '%{0}%'", words[0]);
                        for (int i = 1; i < words.Length; i++)
                        {
                            _where = _where + string.Format("CAMPGNCODE LIKE '%{0}%' OR DESCRIP LIKE '%{0}%'", words[i]);
                        }
                    }
                    #endregion
                }
                //We need SQL with Where condition
                string sql = _sql_exportCampagnCode + " Where {0}";
                //Compose the SQL
                sql = String.Format(sql, _where);

                ds = _reportService.get_dataset_w_sql__single(sql, "CampgnCodes");

            }

            if (ds != null)
            {
                string key_ = System.Guid.NewGuid().ToString();
                HttpRuntime.Cache.Insert(key_, ds, null, DateTime.UtcNow.Add(new TimeSpan(1, 0, 1)), System.Web.Caching.Cache.NoSlidingExpiration);
                //return the Key
                return key_;
            }
            else
            {
                return null;
            }
        }

        //Note: Get Campgn Code List
        [HttpGet, Route("crm/api/CampgnCodeSettings/GetCampgnCodes")]
        [apiAuthorize(AccessElement = @"cmdiapp.dms.dmcampgn", AccessLevel = "v")]
        public genericResponse GetCampgnCodes(string searchText)
        {
            string _sql =
                        @"SELECT 
                        [dmC].[CAMPGNID] AS [CAMPGNID], 
                        [dmC].[CAMPGNCODE] AS [CAMPGNCODE], 
                        [dmC].[DESCRIP] AS [DESCRIP], 
                        [dmC].[UPDATEDON] AS [UPDATEDON]
                        FROM [dbo].[dmCAMPGN] AS [dmC]";

            //order by
            string _sortOption = "[dmC].[CAMPGNCODE] ASC";

            //where
            string _where = "";
            if (!string.IsNullOrEmpty(searchText))
            {
                searchText = Library.util.kill_sqlBlacklistWord(searchText);


                if (!string.IsNullOrEmpty(searchText))
                {
                    #region [ Split Search Text > string1, string2 & string3 and create "Where" statement ]
                    char[] delimiterChars = { ' ', ',', '.', ';', ':', '|', '\t' };

                    string[] words = searchText.Split(delimiterChars);

                    if (words.Count() > 0)
                    {
                        _where = string.Format("CAMPGNCODE LIKE '%{0}%' OR DESCRIP LIKE '%{0}%'", words[0]);
                        for (int i = 1; i < words.Length; i++)
                        {
                            _where = _where + string.Format("CAMPGNCODE LIKE '%{0}%' OR DESCRIP LIKE '%{0}%'", words[i]);
                        }
                    }
                    #endregion
                }
                _sql = _sql + " where " + _where;
            }
            _sql = _sql + " order by " + _sortOption;


            try
            {
                List<dmCAMPGN> _list = _entity_crm.getContext().Database.SqlQuery<dmCAMPGN>(_sql).ToList();

                return new genericResponse() { success = true, __count = _list.Count, results = _list.ToList<iItemType>() };
            }
            catch(Exception ex)
            {
                return new genericResponse() { success = false, __count = 0 };
            }
        }

        [HttpGet, Route("crm/api/CampgnCodeSettings/getCampgnCode/{_CAMPGNID}")]
        [apiAuthorize(AccessElement = @"cmdiapp.dms.dmcampgn", AccessLevel = "v")]
        public dmCAMPGN getCampgnCode(Int16 _CAMPGNID)
        {
            if (_CAMPGNID < 0)
                return new dmCAMPGN
                {
                    CAMPGNID = -1
                };
            //meaning edit
            return _entity_crm.All<dmCAMPGN>().Where(dmC => dmC.CAMPGNID == _CAMPGNID).FirstOrDefault();
        }

        //Note: Save and Edit CampgnCode
        [HttpPost, Route("crm/api/CampgnCodeSettings/saveCampgnCode")]
        [apiAuthorize(AccessElement = @"cmdiapp.dms.dmcampgn", AccessLevel = "a")]
        public genericResponse saveCampgnCode(dmCAMPGN dmC)
        {
            dmCAMPGN _record = null;
            //check uniqueness for code
            _record = _entity_crm.Single<dmCAMPGN>(a => a.CAMPGNCODE.Trim().ToLower() == dmC.CAMPGNCODE.Trim().ToLower());

            if (_record != null && _record.CAMPGNID != dmC.CAMPGNID)
                return new genericResponse() { success = false, message = "Code: " + dmC.CAMPGNCODE.Trim() + " already exists. Please enter unique Code and try again." };


            //check if already exists or new record
            _record = _entity_crm.Single<dmCAMPGN>(a => a.CAMPGNID == dmC.CAMPGNID);
            try
            {
                // existing record
                if (_record != null && _record.CAMPGNID >= 0)
                {
                    _record.CAMPGNCODE = dmC.CAMPGNCODE;
                    _record.DESCRIP = dmC.DESCRIP;
                    _record.UPDATEDON  = System.DateTime.Now;

                    _entity_crm.Update(_record);
                }
                else// new record
                {
                    _record = new dmCAMPGN();
                    _record.CAMPGNCODE = dmC.CAMPGNCODE;
                    _record.DESCRIP = dmC.DESCRIP;
                    _record.UPDATEDON = System.DateTime.Now;
                    _entity_crm.Add(_record);
                }

                _entity_crm.CommitChanges();
                return new genericResponse() { success = true, message = "Code was saved successfully saved" };
            }
            catch (Exception ex)
            {
                return new genericResponse() { success = false, message = "Code was NOT saved. Try again later" };
            }

        }

        //Note: Delete Campgn Code
        [HttpPost, Route("crm/api/CampgnCodeSettings/DeleteCampgnCode/{_CAMPGNID}")]
        [apiAuthorize(AccessElement = @"cmdiapp.dms.dmcampgn", AccessLevel = "d")]
        public genericResponse DeleteCampgnCode(int _CAMPGNID)
        {
            try
            {
                //check if already in use in MONY table
                string InUSEquery = @"declare @_campgnID int={0};
                select
                (select count(*) from dtBATCH where CAMPGNID =@_campgnID)+
                (select count(*) from MONY where CAMPGNID =@_campgnID)";

                string isInUse = String.Format(InUSEquery, _CAMPGNID);
                int count = _entity_crm.getContext().Database.SqlQuery<int>(isInUse).FirstOrDefault();
                if (count > 0)
                {
                    genericResponse _respnse = new genericResponse() { success = false, message = "Code can not be deleted. It is being referenced." };
                    return _respnse;
                }
                //if not in use, delete it
                _entity_crm.Delete<dmCAMPGN>(a => a.CAMPGNID == _CAMPGNID);
                _entity_crm.CommitChanges();

                genericResponse _response = new genericResponse() { success = true, message = "Code deleted successfully." };
                return _response;
            }
            catch
            {
                genericResponse _response = new genericResponse() { success = false, message = "Error occurred while deleting code. Please try again later." };
                return _response;
            }
        }
        #endregion
    }
}
