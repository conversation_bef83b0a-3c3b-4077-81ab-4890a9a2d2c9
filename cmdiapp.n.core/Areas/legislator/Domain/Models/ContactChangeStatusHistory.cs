﻿using cmdiapp.n.core.Areas.legislator.Domain.Data;
using cmdiapp.n.core.Domain.ViewModels;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Web;

namespace cmdiapp.n.core.Areas.legislator.Domain.Models
{
    [Table("ContactChangeStatusHistory")]
    public class ContactChangeStatusHistory : _entityBase_legislator, iItemType
    {
        [Key, Column]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        public int Id { get; set; }

        [Column]
        public int contactChangeId { get; set; }

        [Column]
        public short userId_i { get; set; }

        [Column]
        public DateTime createdOn { get; set; }

        [Column]
        public int oldStatusId { get; set; }

        [Column]
        public int newStatusId { get; set; }
    }
}