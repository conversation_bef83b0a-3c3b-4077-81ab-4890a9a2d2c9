﻿using cmdiapp.n.core.Areas.quickdash.Domain.Services;
using cmdiapp.n.core.Core;
using cmdiapp.n.core.Domain.Models;
using Newtonsoft.Json;
using System;
using System.IO;
using System.Linq;
using System.Runtime.Serialization;

namespace cmdiapp.n.core.Areas.dashboard.Domain.Services.implementation
{
    /// <summary>
    /// Implementation of <see cref="IDashboardEnvironmentService"/>
    /// </summary>
    public class DashboardEnvironmentService : IDashboardEnvironmentService
    {
        private readonly userSession _userSession;
        private readonly IQuickDashDefinitionService _definitionService;
        private readonly int _userGroupId;
        private readonly ConfigurableDashboard[] _dashboards;
        // version-based definition customizations will be based on consistent naming convention
        // that this Func will represent
        // (e.g., nonprofit versions add "NP" to the end of the default definition)
        // will be null if no customization for present version
        private readonly Func<string, string> _versionIdTransform;

        public DashboardEnvironmentService(IQuickDashDefinitionService definitionService)
        {
            _definitionService = definitionService;
            _userSession = session.userSession;
            _userGroupId = session.currentDomainAuthorizedProject_userGroupId;
            _dashboards = new ConfigurableDashboard[3]
            {
                new ConfigurableDashboard("home", configKeyBase: "home-dashboard-id"),
                new ConfigurableDashboard("peopleProfile", isVersionOnly: true),
                new ConfigurableDashboard("fundraisingHome", isVersionOnly: true)
            };
            string version = session.currentDomain_project.projectType.appVersion?.ToLower().Trim() ?? "";
            _versionIdTransform = GetVersionTransform(version);
        }

        private Func<string, string> GetVersionTransform(string version)
        {
            if (version == "nonprofit")
            {
                return (string id) => id + "NP";
            }
            return null;
        }

        public string GetDashboardIdForEnvironment(string defaultId)
        {
            // check if this dashboard definition is configurable
            ConfigurableDashboard dash = _dashboards.SingleOrDefault(ad => ad.DefaultDashboardId == defaultId);
            if (dash == null)
            {
                return defaultId;
            }

            if (dash.IsVersionOnly)
            {
                if (_versionIdTransform == null)
                {
                    return defaultId;
                }
                string transformedId = _versionIdTransform(defaultId);
                return DefinitionFileExists(transformedId) ? transformedId : defaultId;
            }

            // check for configKeys with the appropriate configKeyBase (in ConfigurableDashboard class)
            configItem[] configItems = _userSession.configItems
                .Where(ci => ci.Key?.StartsWith(dash.ConfigKeyBase) ?? false)
                .ToArray();

            // user configuration takes priority
            configItem userItem = configItems.FirstOrDefault(ci => ci.Key.EndsWith("user"));
            if (userItem != null && DefinitionFileExists(userItem?.Val))
            {
                return userItem.Val;
            }

            configItem userGroupItem = configItems.FirstOrDefault(ci => ci.Key.EndsWith("user-group"));
            if (userGroupItem != null)
            {
                UserGroupConfig userGroupConfig = GetUserGroupConfigs(userGroupItem.Val)
                    .SingleOrDefault(config => config.UserGroupId == _userGroupId);
                if (userGroupConfig != null && DefinitionFileExists(userGroupConfig?.DashboardId))
                {
                    return userGroupConfig.DashboardId;
                }
            }

            configItem projectItem = configItems.FirstOrDefault(ci => ci.Key.EndsWith("project"));
            if (projectItem != null && DefinitionFileExists(projectItem?.Val))
            {
                return projectItem.Val;
            }

            // if no usable configVals, check if version has customized definition
            if (_versionIdTransform != null)
            {
                string transformedId = _versionIdTransform(defaultId);
                return DefinitionFileExists(transformedId) ? transformedId : defaultId;
            }

            return defaultId;
        }

        private bool DefinitionFileExists(string dashboardId)
        {
            return !string.IsNullOrEmpty(dashboardId)
                && _definitionService.HeaderFileExists(dashboardId)
                && _definitionService.DefinitionFileExists(dashboardId);
        }        

        /// <summary>
        /// deserializes JSON user group config val
        /// </summary>
        /// <param name="configVal"></param>
        /// <returns></returns>
        private UserGroupConfig[] GetUserGroupConfigs(string configVal)
        {
            if (string.IsNullOrEmpty(configVal))
            {
                return new UserGroupConfig[0];
            }
            JsonSerializer serializer = new JsonSerializer();
            JsonReader reader = new JsonTextReader(new StringReader(configVal));
            return serializer.Deserialize<UserGroupConfig[]>(reader);
        }

        private class ConfigurableDashboard
        {
            public string DefaultDashboardId { get; set; }
            public string ConfigKeyBase { get; set; }
            public bool IsVersionOnly { get; set; }

            public ConfigurableDashboard(string dashboardId, string configKeyBase = "", bool isVersionOnly = false)
            {
                DefaultDashboardId = dashboardId;
                ConfigKeyBase = configKeyBase;
                IsVersionOnly = isVersionOnly || string.IsNullOrEmpty(ConfigKeyBase);
            }
        }

        [DataContract]
        private class UserGroupConfig
        {
            [DataMember(Name = "userGroupId")]
            public int UserGroupId { get; set; }

            [DataMember(Name = "dashboardId")]
            public string DashboardId { get; set; }
        }
    }
}