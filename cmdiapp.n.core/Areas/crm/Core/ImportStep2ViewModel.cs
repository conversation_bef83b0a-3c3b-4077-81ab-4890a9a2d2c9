﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Data;
using System.Web.Mvc;
using cmdiapp.n.core.Domain.Models;
using cmdiapp.n.core.Areas.crm.Domain.Models;

namespace cmdiapp.n.core.Areas.crm.Core
{
    public class ImportStep2ViewModel
    {
        public IList<SelectListItemImpStep2> ImpTypefields { get; set; }
        public IList<SelectListItemImpStep2> dmFundfields { get; set; }
        public IList<SelectListItemImpStep2> lkchannels { get; set; }
        public IList<SelectListItemImpStep2> lkeventcode { get; set; }        

        #region [[ Additional Fields for Data Retrieval ]]
        public string defaultBatch { get; set; }
        public DateTime defaultBatchDate { get; set; }
        public int  noOfGifts { get; set; }
        public decimal totalAmount { get; set; }
        public DateTime attenddate { get; set; }

        public DateTime interactionDate { get; set; }
        public string source { get; set; }
        public bool IsOverwriteOk { get; set; }

        public int impTypeFieldSel { get; set; }
        public Int16 dmFundFieldSel { get; set; }
        public Int16 lkPeopleCodeFieldSel { get; set; }
        public Int16 lkChannelFieldSel { get; set; }
        public Int16 intfundTypeFieldSel { get; set; }
        public string lkEventCodeFieldSel { get; set; }
        
        public string impTypeFieldSelText { get; set; }
        public string dmFundFieldSelText { get; set; }
        public string lkChannelFieldSelText { get; set; }
        public string intfundTypeFieldSelText { get; set; }
        public string lkEventCodeFieldSelText { get; set; }
        public string lkPeopleCodeFieldSelText { get; set; } 
        
        public string mappingData { get; set; }
        public string uploadedFileName { get; set; }
        #endregion

        public ImportStep2ViewModel()
        {
        }
       
        public ImportStep2ViewModel(IQueryable<lkIMPTYPE> ImpTypes, IQueryable<dmFUND> dmFunds, IQueryable<lkChannel> channels, IQueryable<pmSPCEVNT> eventcode)
        {
            //Fields based on Import Type
            ImpTypefields = new List<SelectListItemImpStep2>();             
            foreach (lkIMPTYPE f in ImpTypes)
            {
                SelectListItemImpStep2 s = new SelectListItemImpStep2();                
                s.ValueInt = f.IMPTYPEID;
                s.Text = f.IMPTYPE;
                ImpTypefields.Add(s);
                
            }
            //For dmFund
            dmFundfields = new List<SelectListItemImpStep2>();
            dmFundfields.Add(new SelectListItemImpStep2() { ValueInt = 0, Text = " " });
            foreach (dmFUND f in dmFunds)
            {
                SelectListItemImpStep2 s = new SelectListItemImpStep2();
                s.ValueInt = f.FUNDID;
                s.Text = f.FUNDCODE + " - " + f.FUNDDESC;
                dmFundfields.Add(s);
            }
            //For Channels
            lkchannels = new List<SelectListItemImpStep2>();
            foreach (lkChannel f in channels)
            {
                SelectListItemImpStep2 s = new SelectListItemImpStep2();
                s.ValueInt = f.channelId;
                s.Text = f.descrip;
                lkchannels.Add(s);
            }
           
            //For Event code
            lkeventcode = new List<SelectListItemImpStep2>();
            lkeventcode.Add(new SelectListItemImpStep2() { ValueInt = 0, Text = " " });            
            foreach (pmSPCEVNT f in eventcode)
            {
                SelectListItemImpStep2 s = new SelectListItemImpStep2();
                s.ValueInt = f.SPCEVNTID;
                s.Value = f.EVNTCODE;
                s.Text = f.EVNTCODE + " - " + f.EVNTDESC;                
                lkeventcode.Add(s);
            }      
        }
    }

    public class SelectListItemImpStep2
    {
        public string Value { get; set; }
        public string Text { get; set; }
        public int ValueInt { get; set; }
    }
}