﻿using cmdiapp.n.core.Areas.datax.Domain.Models;
using cmdiapp.n.core.Areas.datax.Domain.Services;
using cmdiapp.n.core.Core.App_Start;
using cmdiapp.n.core.Presentation.Controllers;
using Ninject;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Web.Http;

namespace cmdiapp.n.core.Areas.datax.Presentation.api
{
    [RoutePrefix("datax/api/Lookup")]
    public class DataxLookupController : ApiController
    {
        private readonly ILookupService _service;

        public DataxLookupController()
        {
            _service = NinjectMVC.kernel.Get<ILookupService>();
        }

        [HttpGet, Route("TimeZones")]
        [apiAuthorize]
        public List<SqlTimeZone> GetTimeZones()
        {
            return _service.GetTimeZones();
        }
    }
}
