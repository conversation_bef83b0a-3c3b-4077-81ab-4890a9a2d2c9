﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Data;
using System.Linq;
using System.Web.Mvc;
using System.Data.Linq;
using System.ComponentModel.DataAnnotations.Schema;

using cmdiapp.n.core.Domain.ViewModels;
using cmdiapp.n.core.Areas.crm.Domain.Data;

namespace cmdiapp.n.core.Areas.crm.Domain.Models
{
    public class fec_form_type
    {
        public string formcode { get; set; }

        public string formdesc { get; set; }
    }

    public class fec_report_code
    {
        public string rptcode { get; set; }

        public string rptdesc { get; set; }
    }

    public class fec_export_type
    {
        public string expcode { get; set; }

        public string expdesc { get; set; }
    }

    public class fec_export_data
    {
        public string EXPORTTYPE { get; set; }

        public string REPORTCODE { get; set; }

        public string FORMTYPE { get; set; }

        public string FUNDCODE1 { get; set; }

        public string FUNDCODE2 { get; set; }

        public string FUNDCODE3 { get; set; }

        public DateTime BEGINDTEc { get; set; }

        public DateTime BEGINDTE { get; set; }

        public DateTime ENDDTEc { get; set; }

        public DateTime ENDDTE { get; set; }

        public decimal CUMTOT { get; set; }

        public bool LOBBYIST { get; set; }

        public bool ROLLUP { get; set; }

        public bool REPORTED { get; set; }

        public bool canexport { get; set; }

        public decimal FEDPCT { get; set; }

        public int IEDATE { get; set; }

        public bool INCRPT24 { get; set; }

        public string TOTALGRP1 { get; set;  }

        public string TOTALGRP2 { get; set; }

        public string TOTALGRP3 { get; set; }

        public string FUNDCODE4 { get; set; }

        public string TOTALGRP4 { get; set; }
    }
}
