﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Data;
using System.Linq;
using System.Web.Mvc;
using System.Data.Linq;
using System.ComponentModel.DataAnnotations.Schema;

using cmdiapp.n.core.Areas.crm.Domain.Data;
using cmdiapp.n.core.Domain.ViewModels;

namespace cmdiapp.n.core.Areas.crm.Domain.Models
{
    [Table("dmFUND")]
    public class dmFUND : _entityBase_crm,iItemType
    {
        [Column]
        [Key]
        /*
         By request, FUNDID should be editable on Add.
         [Key] attribute tells EntityFramework to assume
         that this column value is being automatically
         generated by DB (e.g., by IDENTITY(1,1)) and so
         it is not included in EF generated INSERT query.
         DatabaseGenerated attribute below tells EF to change this behavior.
             */
        [DatabaseGenerated(DatabaseGeneratedOption.None)]
        public Int16 FUNDID { get; set; }

        [Column]
        public string FUNDCODE { get; set; }

        [Column]
        public string FUNDDESC { get; set; }

        public string CODEDESCRIP
        {
            get
            {
                return string.Format("{0} - {1}", FUNDCODE, FUNDDESC);
            }
        }

        // Added by Lydia 2013-10-25
        [Column]
        public string fundType { get; set; }

        [Column]
        public Nullable<bool> ACTIVE { get; set; }

        //// Added by Tanvir 2014-02-26
        [Column]
        public string FORM3 { get; set; }

        [Column]
        public string COMMENT { get; set; }

        [Column]
        public DateTime? LASTUSED { get; set; }

        [Column]
        public DateTime? UPDATEDON { get; set; }

        [Column]
        public Int16?    cycle { get; set; }

        [Column]
        public Decimal? limit_I { get; set; }

        [Column]
        public Decimal? limit_R { get; set; }
        
        [Column]
        public Decimal? limit_P { get; set; }

        [Column]
        public Decimal? limit_O { get; set; }

        [Column]
        public Decimal? limit_S { get; set; }

        [Column]
        public Decimal? limit_N { get; set; }

        [Column]
        public Decimal? limit_C { get; set; }

        [Column]
        public string CCPROFILE { get; set; }

        [Column]
        public Nullable<bool> include_in_report { get; set; }

        [Column]
        public Decimal?goal { get; set; }

        [Column]
        public int? goal_donor { get; set; }

        [Column]
        public string dc_project_name { get; set; }

        [Column]
        public bool INCLUDE_IN_SUMCALC { get; set; }

        [Column]
        public DateTime? CYCLESTART { get; set; }

        [Column]
        public DateTime? CYCLEEND { get; set; }

        [Column]
        public bool EXCLUDE_FROM_ROLLUP { get; set; }

        [Column]
        public Decimal? limit_M { get; set; }
       
        [NotMapped]
        public int fundID_original { get; set; }

        [Column]
        public bool DEBTRETIRED { get; set; }

        [Column]
        public Nullable<DateTime> DEBTRETIREDON { get; set; }

        [Column]
        public string FUNDORGNAME { get; set; }
        
        [Column]
        public string FUNDORGADDR { get; set; }

        [Column]
        public string clientCode { get; set; }

    }


    public class dmFUNDForDataEntry
    {
        public Int16 FUNDID { get; set; }
                
        public string FUNDCODE { get; set; }
                
        public string FUNDDESC { get; set; }
                
    }

    [Table("lkMONYTYPE")]
    public class lkMONYTYPE : _entityBase_crm, iItemType
    {
        [Column]
        [Key]
        public Int16 MONYTYPEID { get; set; }

        [Column]
        public string MONYTYPE { get; set; }

        [Column]
        public string DESCRIP { get; set; }

        public string CODEDESCRIP
        {
            get
            {
                return string.Format("{0} - {1}", MONYTYPE, DESCRIP);
            }
        }

        public Nullable<byte> DEFFLAG { get; set; }

        public Nullable<byte> SYSTEM { get; set; }

        [Column]
        public string appVersion { get; set; }

    }


    public class lkMONYTYPEForDataEntry
    {
        public Int16 MONYTYPEID { get; set; }

        public string MONYTYPE { get; set; }
                
        public string DESCRIP { get; set; }

    }

    [Table("lkFUNDFORMTYPE")]
    public class lkFUNDFORMTYPE : _entityBase_crm, iItemType
    {
        [Column]
        [Key]
        public short FUNDFORMTYPEID { get; set; }
        [Column]
        public string FUNDFORMTYPE { get; set; }
    }
}