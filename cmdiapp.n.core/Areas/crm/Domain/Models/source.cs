﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Data;
using System.Linq;
using System.Web.Mvc;
using System.Data.Linq;
using System.ComponentModel.DataAnnotations.Schema;

using cmdiapp.n.core.Areas.crm.Domain.Data;
using cmdiapp.n.core.Domain.ViewModels;

namespace cmdiapp.n.core.Areas.crm.Domain.Models
{
    [Table("SOURCE")]
    public class SOURCE : _entityBase_crm, iItemType
    {
        [Column]
        [Key]
        public int SRCEID { get; set; }

        [Column]
        public int PKGEID { get; set; }

        [ForeignKey("PKGEID")]
        public virtual PACKAGE PACKAGE { get; set; }

        [Column]
        [Required(ErrorMessage = "The Source Code is required.")]
        public string SRCECODE { get; set; }

        [Column]
        [Required(ErrorMessage = "The Source Description is required.")]
        public string SRCEDESC { get; set; }

        [Column]
        public string LISTNOG { get; set; }

        [Column]
        public string LISTNO { get; set; }

        [Column]
        public DateTime? MAILDTE { get; set; }

        [Column]
        public DateTime? FIRSTCAGE { get; set; }

        [Column]
        public DateTime? LASTCAGE { get; set; }

        [Column(TypeName = "Money")]
        public Decimal? COSTPROD1 { get; set; }

        [Column(TypeName = "Money")]
        public Decimal? COSTPROD2 { get; set; }

        [Column(TypeName = "Money")]
        public Decimal? COSTPROD3 { get; set; }

        [Column(TypeName = "Money")]
        public Decimal? COSTPOSTG { get; set; }

        [Column(TypeName = "Money")]
        public Decimal? COSTRESP1 { get; set; }

        [Column(TypeName = "Money")]
        public Decimal? COSTRESP2 { get; set; }

        [Column(TypeName = "Money")]
        public Decimal? COSTRESP3 { get; set; }

        [Column]
        public int? sQTYMAIL { get; set; }

        [Column]
        public int? sMONY { get; set; }

        [Column]
        public int? sPEOPLE { get; set; }

        [Column(TypeName = "Money")]
        public Decimal? sGROSS { get; set; }

        [Column(TypeName = "Money")]
        public Decimal? sNET { get; set; }

        [Column(TypeName = "Money")]
        public Decimal? sCOST { get; set; }

        [Column(TypeName = "Money")]
        public Decimal? sGROSSPM { get; set; }

        [Column(TypeName = "Money")]
        public Decimal? sNETPM { get; set; }

        [Column(TypeName = "Money")]
        public Decimal? sCOSTPM { get; set; }

        [Column(TypeName = "Money")]
        public Decimal? sGROSSRSP { get; set; }

        [Column(TypeName = "Money")]
        public Decimal? sNETRSP { get; set; }

        [Column(TypeName = "Money")]
        public Decimal? sCOSTRSP { get; set; }

        [Column(TypeName = "Money")]
        public Decimal? COSTACTI { get; set; }

        [Column(TypeName = "Money")]
        public Decimal? COSTFLAT { get; set; }

        [Column]
        public Double? COSTPERC { get; set; }

        [Column]
        public Double? sRSPPCT { get; set; }

        [Column]
        public string COMMENT { get; set; }

        [Column]
        public DateTime? UPDATEDON { get; set; }

        [Column]
        public DateTime? LASTACTI { get; set; }

        [Column]
        public int? SACTIVITY { get; set; }

        [Column]
        public Int32? SPCEVNTID { get; set; }

        [Column]
        public DateTime? FIRSTACTI { get; set; }

        [Column]
        public int? sPEOPLEi { get; set; }

        [Column]
        public Double? sRSPPCTi { get; set; }

        [Column]
        public int? teleM_completedCalls { get; set; }

        [Column]
        public int? teleM_noPledges { get; set; }

        [Column]
        public Decimal? teleM_totalPledges { get; set; }

        [Column]
        public Boolean? teleM { get; set; }
        [Column]
        public int? NonDonorCount { get; set; }

        [NotMapped]
        //[Required(ErrorMessage = "The Inititative is required.")]
        public string inititative { get; set; }

        [NotMapped]
        public string EVNTCODE { get; set; }

        [NotMapped]
        public string EVNTDESC { get; set; }

        [NotMapped]
        public Double? Percent_sRSPPCT { get; set; }

        /*
         ############# NEED TO COMPLETE
         */

    }

    public class SOURCE_ext : sourceR
    {
        public int count_ { get; set; }
        public Int64 rowNo { get; set; }
    }

    public class ExcelInputData
    {
        public string ColumnData { get; set; }
    }

    public class sourceR : _entityBase_crm, iItemType
    {
        public int SRCEID { get; set; }
        public int PKGEID { get; set; }       
        public string PKGECODE { get; set; }
        public string PKGEDESC { get; set; }
        public string SRCECODE { get; set; }
        public string SRCEDESC { get; set; }
        public string EVNTCODE { get; set; }
        public string LISTNO { get; set; }
        public DateTime? MAILDTE { get; set; }
        public DateTime? FIRSTCAGE { get; set; }
        public DateTime? LASTCAGE { get; set; }
        public Decimal? COSTPROD1 { get; set; }
        public Decimal? COSTPROD2 { get; set; }
        public Decimal? COSTPROD3 { get; set; }
        public Decimal? COSTPOSTG { get; set; }
        public Decimal? COSTRESP1 { get; set; }
        public Decimal? COSTRESP2 { get; set; }
        public Decimal? COSTRESP3 { get; set; }
        public int? sQTYMAIL { get; set; }
        public int? sMONY { get; set; }
        public int? sPEOPLE { get; set; }
        public Decimal? sGROSS { get; set; }
        public Decimal? sNET { get; set; }
        public Decimal? sCOST { get; set; }
        public Decimal? sGROSSPM { get; set; }
        public Decimal? sNETPM { get; set; }
        public Decimal? sCOSTPM { get; set; }
        public Decimal? sGROSSRSP { get; set; }
        public Decimal? sNETRSP { get; set; }
        public Decimal? sCOSTRSP { get; set; }
        public Double? sRSPPCT { get; set; }
        public string COMMENT { get; set; }
        public DateTime? UPDATEDON { get; set; }
        public DateTime? LASTACTI { get; set; }
        public int? SACTIVITY { get; set; }
        public int teleM_completedCalls { get; set; }
        public int teleM_noPledges { get; set; }
        public Decimal? teleM_totalPledges { get; set; }
        public Boolean? teleM { get; set; }
    }

    public class ActivityTotalsFinal
    {
        public int SRCEID { get; set; }

        public int sACTIVITYsum { get; set; }

        public int sPEOPLEisum { get; set; }
    }

    public class DonationTotalsFinal
    {
        public DateTime Firstcage { get; set; }
        public DateTime Lastcage { get; set; }
        public int? TotalDonationCount { get; set; }
        public decimal? TotalDonationDollar { get; set; }
        public decimal? TotalDonationCost { get; set; }
        public decimal? TotalDonationNet { get; set; }
    }

    public class pmSPCEVNT
    {
        public int SPCEVNTID { get; set; }
        public string EVNTCODE { get; set; }
        public string EVNTDESC { get; set; }
    }

    public class Initiative
    {
        public int PKGEID { get; set; }
        public string PKGECODE { get; set; }
        public string PKGEDESC { get; set; }
    }

    [Table("SRCEBUDGET")]
    public class SRCEBUDGET : _entityBase_crm, iItemType
    {
        [Column]
        [Key]
        public int SRCEBUDGETID { get; set; }

        [Column]
        public int SRCEID { get; set; }

        [Column(TypeName = "Money")]
        public Decimal? BUDGETM1 { get; set; }

        [Column(TypeName = "Money")]
        public Decimal? BUDGETM2 { get; set; }

        [Column(TypeName = "Money")]
        public Decimal? BUDGETM3 { get; set; }

        [Column(TypeName = "Money")]
        public Decimal? BUDGETM4 { get; set; }

        [Column(TypeName = "Money")]
        public Decimal? BUDGETM5 { get; set; }

        [Column(TypeName = "Money")]
        public Decimal? BUDGETM6 { get; set; }

        [Column(TypeName = "Money")]
        public Decimal? BUDGETM7 { get; set; }

        [Column(TypeName = "Money")]
        public Decimal? BUDGETM8 { get; set; }

        [Column(TypeName = "Money")]
        public Decimal? BUDGETM9 { get; set; }

        [Column(TypeName = "Money")]
        public Decimal? BUDGETM10 { get; set; }

        [Column(TypeName = "Money")]
        public Decimal? BUDGETM11 { get; set; }

        [Column(TypeName = "Money")]
        public Decimal? BUDGETM12 { get; set; }

        [Column(TypeName = "Money")]
        public Decimal? BUDGETYR { get; set; }

        // 1 = Monthly, 2 = Yearly
        [Column]
        public Byte TYPE { get; set; }

        [Column(TypeName = "Money")]
        public Decimal? EXPENSEM1 { get; set; }

        [Column(TypeName = "Money")]
        public Decimal? EXPENSEM2 { get; set; }

        [Column(TypeName = "Money")]
        public Decimal? EXPENSEM3 { get; set; }

        [Column(TypeName = "Money")]
        public Decimal? EXPENSEM4 { get; set; }

        [Column(TypeName = "Money")]
        public Decimal? EXPENSEM5 { get; set; }

        [Column(TypeName = "Money")]
        public Decimal? EXPENSEM6 { get; set; }

        [Column(TypeName = "Money")]
        public Decimal? EXPENSEM7 { get; set; }

        [Column(TypeName = "Money")]
        public Decimal? EXPENSEM8 { get; set; }

        [Column(TypeName = "Money")]
        public Decimal? EXPENSEM9 { get; set; }

        [Column(TypeName = "Money")]
        public Decimal? EXPENSEM10 { get; set; }

        [Column(TypeName = "Money")]
        public Decimal? EXPENSEM11 { get; set; }

        [Column(TypeName = "Money")]
        public Decimal? EXPENSEM12 { get; set; }

        [Column(TypeName = "Money")]
        public Decimal? EXPENSEYR { get; set; }

        [Column]
        public Int16? SRCEYEAR { get; set; }

    }

    [Table("v_eventcode")]
    public class v_eventcode : _entityBase_crm
    {
        [Column]
        [Key]
        public int SPCEVNTID { get; set; }

        [Column]
        public string CODEDESC { get; set; }
    }

    public class TrxListForSrcCode : _entityBase_crm, iItemType
    {
        public int MID { get; set; }
        public int PID { get; set; }
        public int SRCEID { get; set; }
        public string PREFIX { get; set; }
        public string FNAME { get; set; }
        public string MNAME { get; set; }
        public string LNAME { get; set; }
        public string SUFFIX { get; set; }
        public string NAME { get; set; }
        public DateTime? ENTRYDTE { get; set; }
        public string ENTRYDTEs
        {
            get
            {
                return string.Format("{0:MM/dd/yyyy}", ENTRYDTE);
            }
        }
        public DateTime? BATCHDTE { get; set; }
        public string BATCHNO { get; set; }
        public decimal AMT { get; set; }
        public string FUNDCODE { get; set; }
        public string SRCECODE { get; set; }
        public short PROGID { get; set; }
        public string PROGRAM { get; set; }
        public string PKGECODE { get; set; }
        public string PKGEDESC { get; set; }
        public string CONTTYPE { get; set; }
        public string SRCEDESC { get; set; }
        public string ACCTCODE { get; set; }
        public short FUNDID { get; set; }
        public string FUNDDESC { get; set; }
        public short CAMPGNID { get; set; }
        public string CAMPGNCODE { get; set; }
        public string CAMPGN { get; set; }
        public short CENTERID { get; set; }
        public string CENTERCODE { get; set; }
        public string CENTER { get; set; }
        public string MONYTYPE { get; set; }
    }

    public class TrxListForSrcCode_ext1 : TrxListForSrcCode
    {
        public int count_ { get; set; }

        public Int64 rowNo { get; set; }
    }

    public class ApplyToOthers
    {
        public int packageId { get; set; }
        public bool applyAll { get; set; }
        public decimal? costpostg { get; set; }
        public decimal? costprod1 { get; set; }
        public decimal? costprod2 { get; set; }
        //public DateTime? maildate { get; set; }
    }

    public class ApplyMailDateToOthers
    {
        public int packageId { get; set; }
        public DateTime? mailDate { get; set; }
    }
    public class srceImportErrorData
    {
        public List<SOURCE> srcList { get; set; }
        public List<SOURCE> srcInOtherIList { get; set; }
        public string fileName { get; set; }
        public int WORKSHEET { get; set; }
        public int rowindex { get; set; }
    }
}