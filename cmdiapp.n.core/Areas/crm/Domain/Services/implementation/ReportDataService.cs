﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

using System.Data;
using System.Data.SqlClient;

using Ninject;
using Ninject.Web.Mvc;

using cmdiapp.n.core.Core.App_Start;
using cmdiapp.n.core.Areas.crm.Core;
using cmdiapp.n.core.Areas.crm.Domain.Models;
using cmdiapp.n.core.Areas.crm.Domain.ViewModels;
using cmdiapp.n.core.Areas.crm.Domain.Repositories;

namespace cmdiapp.n.core.Areas.crm.Domain.Services
{
    public class ReportDataService : IReportDataService
    {
        private I___unitOfWork _unitOfWork;
        private I__DBFactory _dbFactory;

        public ReportDataService(I___unitOfWork unitOfWork, I__DBFactory dbFactory)
        {
            _unitOfWork = unitOfWork;
            _dbFactory = dbFactory;
        }

        public DataSet get_dataset_w_sql__multi(List<string> p_sql, List<string> p_datasetName, List<int> p_startIndex, List<int> p_maxRow)
        {
            try
            {
                // Get the DB connection
                string lc_dbConnStr = _dbFactory.getContext().Database.Connection.ConnectionString;
                if (lc_dbConnStr == "")
                    return null;

                using (var _dbConn = new SqlConnection(lc_dbConnStr))
                {
                    string _sql;
                    string _datasetName;
                    int _startIndex;
                    int _maxRow;
                    System.Data.DataSet _dataset = new System.Data.DataSet();
                    SqlDataAdapter _adapter;

                    for (int i = 0; i < p_sql.Count(); i++)
                    {
                        _sql = p_sql[i];

                        _datasetName = p_datasetName[i];
                        _startIndex = p_startIndex[i];
                        _maxRow = p_maxRow[i];

                        _adapter = new SqlDataAdapter(_sql, _dbConn);
                        _adapter.SelectCommand.CommandTimeout = 1800000;  // 30 min.

                        if (_startIndex == 0 && _maxRow == 0)
                            _adapter.Fill(_dataset, _datasetName);
                        else
                            _adapter.Fill(_dataset, _startIndex, _maxRow, _datasetName);

                        _adapter.Dispose();
                    }

                    return _dataset;
                }
            }
            catch (System.Exception e)
            {
                // This will need to be rewritten to return more detail message
                return null;
            }
        }

        public DataSet get_dataset_w_sql__single(string p_sql, string p_datasetName)
        {
            try
            {
                // Get the DB connection
                string lc_dbConnStr = _dbFactory.getContext().Database.Connection.ConnectionString;
                if (lc_dbConnStr == "")
                    return null;

                using (var _dbConn = new SqlConnection(lc_dbConnStr))
                {
                    string _sql;
                    string _datasetName;
                    int _startIndex;
                    int _maxRow;
                    System.Data.DataSet _dataset = new System.Data.DataSet();
                    SqlDataAdapter _adapter;

                    
                    _sql = p_sql;

                    _datasetName = p_datasetName;
                    _startIndex = 0;
                    _maxRow = 0;

                    _adapter = new SqlDataAdapter(_sql, _dbConn);
                    _adapter.SelectCommand.CommandTimeout = 1800000;  // 30 min.

                    if (_startIndex == 0 && _maxRow == 0)
                      _adapter.Fill(_dataset, _datasetName);
                    else
                      _adapter.Fill(_dataset, _startIndex, _maxRow, _datasetName);

                    _adapter.Dispose();
                    
                    return _dataset;
                }
            }
            catch (System.Exception e)
            {
                // This will need to be rewritten to return more detail message
                return null;
            }
        }

        public int get_SelectedPackageId(string pkgCode, string pkgDescription, int progId)
        {
            //For Package Id
            try
            {
                //string sql = string.Format("SELECT * FROM PACKAGE  WHERE Upper(PKGEDESC) ='{0}' AND Upper(PKGECODE) = '{1}'", pkgDescription.Trim().ToUpper(), pkgCode.Trim().ToUpper());
                string sql = string.Format("SELECT * FROM PACKAGE  WHERE RTRIM(LTRIM(Upper(PKGECODE))) = '{0}' AND PROGID = {1}", pkgCode.Trim().ToUpper(), progId);
                var q = _dbFactory.getContext().Database.SqlQuery<PACKAGE>(sql);
                return q.FirstOrDefault().PKGEID;
            }
            catch
            {
                return -1;
            }
            
        }

        public int get_SelectedPackageIdForSub(string pkgCode, string pkgDescription, int dtprogId)
        {
            try
            {
                
                string sql = string.Format("SELECT * FROM PACKAGE  WHERE RTRIM(LTRIM(Upper(PKGECODE))) = '{0}' AND dtPROGID = {1}", pkgCode.Trim().ToUpper(), dtprogId);
                var q = _dbFactory.getContext().Database.SqlQuery<PACKAGE>(sql);
                return q.FirstOrDefault().PKGEID;
            }
            catch(Exception ex)
            {
                return 0;
            }

        }

    }
}