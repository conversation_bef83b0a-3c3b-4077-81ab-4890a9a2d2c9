﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Data;
using System.Linq;
using System.Web.Mvc;
using System.Data.Linq;

namespace cmdiapp.n.core.Areas.crm.Domain.ViewModels
{
    public class donorForm
    {
        [Display(Name = "ID")]
        public string Id { get; set; }

        [Display(Name = "Prefix")]
        public string PREFIX { get; set; }

        [Display(Name = "First Name")]
        public string FNAME { get; set; }

        [Display(Name = "Middle Name")]
        public string MNAME { get; set; }

        [Display(Name = "Last Name")]
        public string LNAME { get; set; }

        [Display(Name = "Suffix")]
        public string SUFFIX { get; set; }

        [Display(Name = "Street")]
        public string STREET { get; set; }

        [Display(Name = "Additional Address Line")]
        public string ADDR1 { get; set; }

        [Display(Name = "City")]
        public string CITY { get; set; }

        [Display(Name = "State")]
        public string STATE { get; set; }

        [Display(Name = "Zip")]
        public string ZIP { get; set; }

        [Display(Name = "Phone#")]
        public string PHONE { get; set; }

        [Display(Name = "Phone Type")]
        public string PHONETYPE { get; set; }

        [Display(Name = "Email")]
        public string EMAIL { get; set; }
    }
}