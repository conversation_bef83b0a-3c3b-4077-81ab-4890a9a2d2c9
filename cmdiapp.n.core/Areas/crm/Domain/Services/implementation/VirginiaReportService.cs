﻿using cmdiapp.n.core.Areas.crm.Domain.Data;
using cmdiapp.n.core.Areas.crm.Domain.Models;
using cmdiapp.n.core.Areas.crm.ViewModels;
using cmdiapp.n.core.Domain.ViewModels;
using cmdiapp.n.core.Library;
using System;
using System.Collections.Generic;
using System.Data.Entity;
using System.Linq;
using System.Threading.Tasks;

namespace cmdiapp.n.core.Areas.crm.Domain.Services.implementation
{
    public class VirginiaReportService : IVirginiaReportService
    {
        private readonly I_entity_crm _entity;
        private readonly IErrorLogger _errorLogger;

        public VirginiaReportService(
            I_entity_crm entity,
            IErrorLogger errorLogger)
        {
            _entity = entity;
            _errorLogger = errorLogger;
        }

        public genericResponse Delete(int id)
        {
            try
            {
                var existing = _entity.Single<VirginiaReport>(report => report.Id == id);
                if (existing != null)
                {
                    _entity.Delete(existing);
                    _entity.CommitChanges();
                }
                return new genericResponse() { success = true };
            }
            catch (Exception ex)
            {
                _errorLogger.Log(
                    $"Failed to get VirginiaReport (id: {id}) due to {ex.GetType().Name}: {ex.Message}");
                return new genericResponse()
                {
                    success = false,
                    message = "An error occurred. Unable to delete report."
#if DEBUG
                    ,messageKey = $"{ex.GetType().Name}: ${ex.Message}"
#endif
                };
            }
        }

        public Task<List<VirginiaReportDisplay>> GetAllAsync(int filerId)
        {
            try
            {
                return (from report in _entity.All<VirginiaReport>()
                        join reportType in _entity.All<VirginiaReportType>() on report.ReportTypeId equals reportType.Id
                        where report.FilerId == filerId
                        orderby report.FilingDate descending, report.AmendmentNumber descending
                        select new VirginiaReportDisplay
                        {
                            Id = report.Id,
                            FilerId = report.FilerId,
                            ElectionCycle = report.ElectionCycle,
                            FilingDate = report.FilingDate,
                            ReportPeriodStart = report.ReportPeriodStart,
                            ReportPeriodEnd = report.ReportPeriodEnd,
                            ReceiptsThisPeriod = report.ReceiptsThisPeriod,
                            DisbursementsThisPeriod = report.DisbursementsThisPeriod,
                            TotalReceiptsThisElectionCycle = report.TotalReceiptsThisElectionCycle,
                            TotalDisbursementsThisElectionCycle = report.TotalDisbursementsThisElectionCycle,
                            LoanBalance = report.LoanBalance,
                            EndingBalance = report.EndingBalance,
                            IsAmendment = report.IsAmendment,
                            AmendmentNumber = report.AmendmentNumber,
                            CreatedOn = report.CreatedOn,
                            ReportType = reportType.Name,
                            ReportTypeCode = reportType.Code
                        }).ToListAsync();
            }
            catch (Exception ex)
            {
                _errorLogger.Log($"Failed to get VirginiaReports due to {ex.GetType().Name}: {ex.Message}");
                return Task.FromResult(new List<VirginiaReportDisplay>());
            }
        }

        public Task<List<VirginiaReportType>> GetReportTypesAsync()
        {
            return _entity.All<VirginiaReportType>().ToListAsync();
        }
    }
}