﻿using cmdiapp.n.core.Areas.crm.Domain.Data;
using System;
using System.ComponentModel.DataAnnotations.Schema;
using System.Runtime.Serialization;

namespace cmdiapp.n.core.Areas.crm.Domain.Models
{
    [Table("x_il_reports")]
    [DataContract]
    public class IllinoisReport : _entityBase_crm
    {
        [Column("id")]
        [DataMember(Name = "id")]
        public int Id { get; set; }

        [Column("filerInfoId")]
        [DataMember(Name = "filerInfoId")]
        public int FilerInfoId { get; set; }

        [Column("filingDate")]
        [DataMember(Name = "filingDate")]
        public DateTime FilingDate { get; set; }

        [Column("reportPeriodStart")]
        [DataMember(Name = "reportPeriodStart")]
        public DateTime ReportPeriodStart { get; set; }

        [Column("reportPeriodEnd")]
        [DataMember(Name = "reportPeriodEnd")]
        public DateTime ReportPeriodEnd { get; set; }

        [Column("TotalReceipts")]
        [DataMember(Name = "TotalReceipts")]
        public decimal? TotalReceipts { get; set; }

        [Column("TotalInkind")]
        [DataMember(Name = "TotalInkind")]
        public decimal? TotalInkind { get; set; }

        [Column("TotalExpenditures")]
        [DataMember(Name = "TotalExpenditures")]
        public decimal TotalExpenditures { get; set; }

        [Column("TotalDebts")]
        [DataMember(Name = "TotalDebts")]
        public decimal TotalDebts { get; set; }

        [Column("Invest")]
        [DataMember(Name = "Invest")]
        public decimal Invest { get; set; }

        [Column("EndFundsAvail")]
        [DataMember(Name = "EndFundsAvail")]
        public decimal EndFundsAvail { get; set; }

        [Column("isAmendment")]
        [DataMember(Name = "isAmendment")]
        public bool IsAmendment { get; set; }

        [Column("reasonForAmendment")]
        [DataMember(Name = "reasonForAmendment")]
        public string reasonForAmendment { get; set; }

        [Column("createdOn")]
        [DataMember(Name = "createdOn")]
        public DateTime CreatedOn { get; set; }

        [Column("reportTypeId")]
        [DataMember(Name = "reportTypeId")]
        public int ReportTypeId { get; set; }
    }
}