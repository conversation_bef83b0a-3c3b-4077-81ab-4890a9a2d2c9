﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

using Ninject;
using Ninject.Web.Mvc;

using cmdiapp.n.core.Core.App_Start;
using cmdiapp.n.core.Areas.crm.Domain.Models;
using cmdiapp.n.core.Areas.crm.Domain.ViewModels;
using cmdiapp.n.core.Areas.crm.Domain.Repositories;

namespace cmdiapp.n.core.Areas.crm.Domain.Services
{
    public class lkIMPFLDService : IlkIMPFLDService
    {
        private I___unitOfWork _unitOfWork;
        private I__DBFactory _dbFactory;
        private IlkIMPFLDRepository _lkIMPFLDRepository;

        public lkIMPFLDService(I___unitOfWork unitOfWork, I__DBFactory dbFactory, IlkIMPFLDRepository lkIMPFLDRepository)
        {
            _unitOfWork = unitOfWork;
            _dbFactory = dbFactory;
            _lkIMPFLDRepository = lkIMPFLDRepository;
        }

        //Read all Imp Fields - include money fields and exclude interaction fields
        public List<lkImpField> get_all_impfields(string impType)
        {
            var _allRecords = _lkIMPFLDRepository.All();

            return _allRecords.Where(x => x.IMPTYPE == impType && x.IMPLISTID != 3).OrderBy(p => p.SEQ).ToList();
        }

        //CONTACT IMPORT TYPE - exclude money fields and interaction fields
        public List<lkImpField> get_contact_impfields(string impType)
        {
            var _allRecords = _lkIMPFLDRepository.All();

            return _allRecords.Where(x => x.IMPTYPE == impType && x.IMPLISTID != 2 && x.IMPLISTID != 3).OrderBy(p => p.SEQ).ToList();
        }

        //INTERACTION IMPORT TYPE
        public List<lkImpField> get_interaction_impfields(string impType)
        {
            var _allRecords = _lkIMPFLDRepository.All();

            return _allRecords.Where(x => x.IMPTYPE == impType && x.IMPLISTID != 2).OrderBy(p => p.SEQ).ToList();
        }

        //Read all Event  Fields
        public List<lkImpField> get_allevent_impfields(string impType)
        {
            var _allRecords = _lkIMPFLDRepository.All();

            return _allRecords.Where(x => x.IMPTYPE == impType).OrderBy(p => p.SEQ).ToList();
        }
        
    }
}