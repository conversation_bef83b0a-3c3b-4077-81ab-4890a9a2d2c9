# 🚀 Crimson CRM People Profile - Development Setup

## 📋 Overview

This guide helps you work with the actual Crimson CRM people profile components on macOS, even though the full .NET Framework application requires Windows.

## 🎯 Current Status

✅ **Dependencies Installed**: npm packages are ready  
✅ **Preview Available**: Development preview showing current structure  
✅ **Components Located**: Real Angular components identified  

## 🏗️ Architecture Understanding

### Dashboard System
- **Configuration**: `Areas/dashboard/resource/quickdash/peopleProfile.definition.json`
- **Main Component**: `_src/ts/crm/dashboard/dashboard.component.ts`
- **Service**: `_src/ts/crm/dashboard/dashboard-definition.service.ts`

### Key People Profile Components
```
📂 _src/ts/crm/dashboard/people_profile/
├── profile-overview.component.{html,css,ts}     # Left sidebar profile card
├── profile-timeline.component.{html,css,ts}    # Timeline with activities  
├── profile-main.component.{html,css,ts}        # Main content area
├── giving-history-details/                     # Giving history tab
├── people-profile-footer.component.{html,css,ts} # Bottom toolbar
└── [other specialized components]
```

## 🛠️ Development Workflow Options

### Option 1: Direct Component Editing (Recommended)
Work directly with the Angular components and preview changes:

1. **Edit Components**: Modify `.html`, `.css`, and `.ts` files directly
2. **Preview Changes**: Use the development preview to see styling changes
3. **Test in Development Environment**: Deploy to your dev server to test functionality

### Option 2: Angular Development Server
Set up Angular development server for component testing:

```bash
cd cmdiapp.n.web
npm run build-crm-dev    # Build the CRM module
npm run watch            # Watch for changes
```

### Option 3: Component Isolation
Create isolated component previews for specific improvements:

1. Extract individual components
2. Create standalone previews with mock data
3. Test styling and layout changes
4. Apply changes back to main components

## 📁 Key Files to Modify

### Profile Overview (Left Sidebar)
- **Template**: `_src/ts/crm/dashboard/people_profile/profile-overview.component.html`
- **Styles**: `_src/ts/crm/dashboard/people_profile/profile-overview.component.css`
- **Logic**: `_src/ts/crm/dashboard/people_profile/profile-overview.component.ts`

### Timeline
- **Template**: `_src/ts/crm/dashboard/people_profile/profile-timeline.component.html`
- **Styles**: `_src/ts/crm/dashboard/people_profile/profile-timeline.component.css`
- **Logic**: `_src/ts/crm/dashboard/people_profile/profile-timeline.component.ts`

### Giving History
- **Template**: `_src/ts/crm/dashboard/people_profile/giving-history-details/giving-history-details.component.html`
- **Styles**: `_src/ts/crm/dashboard/people_profile/giving-history-details/giving-history-details.component.css`
- **Logic**: `_src/ts/crm/dashboard/people_profile/giving-history-details/giving-history-details.component.ts`

## 🎨 Current Styling System

### CSS Classes Used
- `.box-typical` - Standard container
- `.bordercolor-darkgray` - Border styling
- `.backgroundcolor-faintgray` - Background colors
- `.color-primary` - Primary color scheme
- `.overview-container` - Profile card container
- `.widget` - Timeline and other widgets

### Color Scheme
- **Primary**: `#007bff` (Blue)
- **Dark Gray**: `#6c7a86`
- **Faint Gray**: `#f8f9fa`
- **Border**: `#e1e5eb`

## 🔧 Making Improvements

### 1. Visual Design Updates
- Modify CSS files for modern styling
- Update color schemes and typography
- Improve spacing and layout

### 2. Template Enhancements
- Update HTML templates for better structure
- Add new UI elements or sections
- Improve responsive design

### 3. Functionality Improvements
- Enhance TypeScript components
- Add new features or interactions
- Improve data handling

## 📝 Testing Changes

### Local Testing
1. **Preview**: Use `people-profile-dev-preview.html` for visual changes
2. **Build**: Run `npm run build-crm-dev` to compile changes
3. **Deploy**: Push to development environment for full testing

### Production Deployment
1. **Branch**: Work in `sofia-people-profile-preview` branch
2. **Test**: Verify all changes in development environment
3. **Review**: Get approval before merging to main branch
4. **Deploy**: Follow standard deployment process

## 🚨 Important Notes

- **Database**: Full functionality requires SQL Server connection
- **Authentication**: Requires proper user session and permissions
- **Backend**: .NET Framework MVC handles data and routing
- **Safety**: Always work in development branch and test thoroughly

## 🎯 Next Steps

1. **Choose Focus Area**: Profile card, timeline, giving history, or overall layout
2. **Make Incremental Changes**: Start with small styling improvements
3. **Test Regularly**: Preview changes and test in development environment
4. **Document Changes**: Keep track of modifications for review

## 📞 Support

- Work in the `sofia-people-profile-preview` branch
- Test changes in development environment before production
- Document all modifications for team review
