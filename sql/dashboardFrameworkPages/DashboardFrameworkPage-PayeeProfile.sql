
/****** Object:  UserDefinedFunction [dbo].[dsb_payeeProfileCandidateInfo]    Script Date: 06/05/2018 10:05:07 ******/
IF  EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[dsb_payeeProfileCandidateInfo]') AND type in (N'FN', N'IF', N'TF', N'FS', N'FT'))
DROP FUNCTION [dbo].[dsb_payeeProfileCandidateInfo]
GO
/****** Object:  UserDefinedFunction [dbo].[dsb_payeeProfileDefaults]    Script Date: 06/05/2018 10:05:07 ******/
IF  EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[dsb_payeeProfileDefaults]') AND type in (N'FN', N'IF', N'TF', N'FS', N'FT'))
DROP FUNCTION [dbo].[dsb_payeeProfileDefaults]
GO
/****** Object:  UserDefinedFunction [dbo].[dsb_payeeProfileInfoPerRecType]    Script Date: 06/05/2018 10:05:07 ******/
IF  EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[dsb_payeeProfileInfoPerRecType]') AND type in (N'FN', N'IF', N'TF', N'FS', N'FT'))
DROP FUNCTION [dbo].[dsb_payeeProfileInfoPerRecType]
GO
/****** Object:  UserDefinedFunction [dbo].[dsb_payeeProfileOverview]    Script Date: 06/05/2018 10:05:07 ******/
IF  EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[dsb_payeeProfileOverview]') AND type in (N'FN', N'IF', N'TF', N'FS', N'FT'))
DROP FUNCTION [dbo].[dsb_payeeProfileOverview]
GO
/****** Object:  UserDefinedFunction [dbo].[payeeProfile_histories]    Script Date: 06/05/2018 10:05:07 ******/
IF  EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[payeeProfile_histories]') AND type in (N'FN', N'IF', N'TF', N'FS', N'FT'))
DROP FUNCTION [dbo].[payeeProfile_histories]
GO
/****** Object:  UserDefinedFunction [dbo].[dsb_payeeProfileHistory]    Script Date: 06/05/2018 10:05:07 ******/
IF  EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[dsb_payeeProfileHistory]') AND type in (N'FN', N'IF', N'TF', N'FS', N'FT'))
DROP FUNCTION [dbo].[dsb_payeeProfileHistory]
GO
/****** Object:  View [dbo].[v_entity_transactionR]    Script Date: 06/05/2018 10:05:06 ******/
IF  EXISTS (SELECT * FROM sys.views WHERE object_id = OBJECT_ID(N'[dbo].[v_entity_transactionR]'))
DROP VIEW [dbo].[v_entity_transactionR]
GO
/****** Object:  UserDefinedFunction [dbo].[fn_GenInvStatement]    Script Date: 06/05/2018 10:05:07 ******/
IF  EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[fn_GenInvStatement]') AND type in (N'FN', N'IF', N'TF', N'FS', N'FT'))
DROP FUNCTION [dbo].[fn_GenInvStatement]
GO
/****** Object:  UserDefinedFunction [dbo].[fn_GenTxnStatement]    Script Date: 06/05/2018 10:05:07 ******/
IF  EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[fn_GenTxnStatement]') AND type in (N'FN', N'IF', N'TF', N'FS', N'FT'))
DROP FUNCTION [dbo].[fn_GenTxnStatement]
GO
/****** Object:  UserDefinedFunction [dbo].[dsb_payeeProfileDisbursement]    Script Date: 06/05/2018 10:05:07 ******/
IF  EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[dsb_payeeProfileDisbursement]') AND type in (N'FN', N'IF', N'TF', N'FS', N'FT'))
DROP FUNCTION [dbo].[dsb_payeeProfileDisbursement]
GO
/****** Object:  UserDefinedFunction [dbo].[dsb_payeeProfileReceipt]    Script Date: 06/05/2018 10:05:07 ******/
IF  EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[dsb_payeeProfileReceipt]') AND type in (N'FN', N'IF', N'TF', N'FS', N'FT'))
DROP FUNCTION [dbo].[dsb_payeeProfileReceipt]
GO
/****** Object:  View [dbo].[v_entity_disbrsummaryR]    Script Date: 06/05/2018 10:05:06 ******/
IF  EXISTS (SELECT * FROM sys.views WHERE object_id = OBJECT_ID(N'[dbo].[v_entity_disbrsummaryR]'))
DROP VIEW [dbo].[v_entity_disbrsummaryR]
GO
/****** Object:  View [dbo].[v_entity_invsummaryR]    Script Date: 06/05/2018 10:05:06 ******/
IF  EXISTS (SELECT * FROM sys.views WHERE object_id = OBJECT_ID(N'[dbo].[v_entity_invsummaryR]'))
DROP VIEW [dbo].[v_entity_invsummaryR]
GO
/****** Object:  View [dbo].[v_entity_rcptsummaryR]    Script Date: 06/05/2018 10:05:06 ******/
IF  EXISTS (SELECT * FROM sys.views WHERE object_id = OBJECT_ID(N'[dbo].[v_entity_rcptsummaryR]'))
DROP VIEW [dbo].[v_entity_rcptsummaryR]
GO
/****** Object:  UserDefinedFunction [dbo].[dsb_payeeProfileInvoices]    Script Date: 06/05/2018 10:05:07 ******/
IF  EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[dsb_payeeProfileInvoices]') AND type in (N'FN', N'IF', N'TF', N'FS', N'FT'))
DROP FUNCTION [dbo].[dsb_payeeProfileInvoices]
GO

/****** Object:  UserDefinedFunction [dbo].[dsb_payeeProfileInvoices]    Script Date: 05/14/2018 12:34:01 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
/*  //////////////////////////////////////////////////////////////////
	  [05/01/2018 by Lydia]

	  (func)dsb_payeeProfileInvoices
			: Given a ENTITYID (in string), returns Open Invoices count and most recent 3 invoices

	\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\ */
CREATE FUNCTION [dbo].[dsb_payeeProfileInvoices](
	@entityid VARCHAR(10),
	@header VARCHAR(10)		-- true | false
)
RETURNS XML
BEGIN

	--||||| Compile an XML in Dashboard-LabelValue Format |||||-----
	DECLARE @data XML

	--||||| Header (Parent) data ||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||
	IF @header = 'true'
	BEGIN
		SET @data = (
				SELECT * 
				FROM (
					SELECT	TOP 1
							'Invoices' AS title,						
							-- selectAction
							'main' AS [selectAction/actionType],
							-- args
							'invoiceDetails' AS [selectAction/args/component],
							-- data
							@entityid AS [selectAction/args/data/entityid],
							'fa-plus' as iconClass,
							'Add Invoice' as iconTitle,
							'modal' as [iconSelectAction/actionType],
							--access 
							'a>InvoiceModule>a' AS [iconSelectAction/access],
								--args
								'editInvoice' as [iconSelectAction/args/component],
								@entityid as [iconSelectAction/args/data/entityid],
								0 as [iconSelectAction/args/data/id] 
					FROM (SELECT 1 AS T) A
				) data
				FOR XML PATH('data'), ELEMENTS
				)
	END

	--||||| Detail (Child) data |||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||
	ELSE BEGIN

		SET @data = (
				SELECT 

					(SELECT COUNT(*) FROM INVOICE v INNER JOIN lkINVSTATUS s ON v.INVSTATUSID = s.INVSTATUSID  
						WHERE s.CODE = 'Open' AND ENTITYID = CAST(@entityid AS INT)) AS [value],
					'Open Invoices' AS [label],
					
					(SELECT TOP 3
						CONVERT(VARCHAR, v.INVDTE, 1) + ' ' + LEFT(ISNULL(v.DESCRIP,''),15) + CASE WHEN LEN(v.DESCRIP) > 15 THEN '...' ELSE '' END as [title],
						'$' + dbo.oPutComma(v.AMT) AS [subtitle],
						'gray' AS [dotClass]
					 FROM INVOICE v
						INNER JOIN lkINVSTATUS s ON v.INVSTATUSID=s.INVSTATUSID
					 WHERE s.CODE in ('Open','Partial') 
							AND v.ENTITYID = CAST(@entityid AS INT)
					 ORDER BY v.INVDTE 
					 FOR XML PATH('items'), TYPE)
				 			
				FOR XML PATH(''), ROOT('data'), ELEMENTS
		)

		-- (Json.net/JsonConvert.SerializeXmlNode) Work-around to make a specific "Single Node" as an Array Node, and make its content as its first element
		SET @data = dbo.[dsb_util_JsonNetHandle_makeSingleNodeIntoArray](@data)
	END

	RETURN @data
END
GO
/****** Object:  View [dbo].[v_entity_rcptsummaryR]    Script Date: 05/14/2018 12:34:00 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER OFF
GO
CREATE VIEW [dbo].[v_entity_rcptsummaryR]
AS
SELECT e.ENTITYID,
ISNULL(e.CTDNO_REC,0) as CTDNO,
ISNULL(e.CTDAMT_REC,0) as CTDAMT,
ISNULL(e.YTDNO_REC,0) as YTDNO,
ISNULL(e.YTDAMT_REC,0) as YTDAMT,
(SELECT COUNT(*) FROM TXN WHERE ENTITYID = e.ENTITYID AND TXNTYPEID = 1) as CUMNO,
(SELECT ISNULL(SUM(AMT),0) FROM TXN WHERE ENTITYID = e.ENTITYID AND TXNTYPEID = 1) as CUMTOT,
(SELECT TOP 1 TXNDTE FROM TXN WHERE ENTITYID = e.ENTITYID AND TXNTYPEID = 1 ORDER BY TXNDTE) as FIRSTDTE,
(SELECT TOP 1 ISNULL(AMT,0) FROM TXN WHERE ENTITYID = e.ENTITYID AND TXNTYPEID = 1 ORDER BY TXNDTE) as FIRSTAMT,
(SELECT TOP 1 TXNDTE FROM TXN WHERE ENTITYID = e.ENTITYID AND TXNTYPEID = 1 ORDER BY TXNDTE DESC) as LASTDTE,
(SELECT TOP 1 ISNULL(AMT,0) FROM TXN WHERE ENTITYID = e.ENTITYID AND TXNTYPEID = 1 ORDER BY TXNDTE DESC) as LASTAMT,
(SELECT TOP 1 TXNDTE FROM TXN WHERE ENTITYID = e.ENTITYID AND TXNTYPEID = 1 ORDER BY AMT DESC) as HIGHDTE,
(SELECT TOP 1 ISNULL(AMT,0) FROM TXN WHERE ENTITYID = e.ENTITYID AND TXNTYPEID = 1 ORDER BY AMT DESC) as HIGHAMT,
(SELECT CASE WHEN COUNT(*) = 0 THEN 0 ELSE ROUND(ISNULL(SUM(AMT),0) / COUNT(*),2) END FROM TXN WHERE ENTITYID = e.ENTITYID AND TXNTYPEID = 1) as AVERAGE,
(SELECT COUNT(*) FROM TXN WHERE ENTITYID = e.ENTITYID AND TXNTYPEID = 1 AND YEAR(TXNDTE) = YEAR(GETDATE()) - 1) as LYRNO,
(SELECT ROUND(ISNULL(SUM(AMT),0),2) FROM TXN WHERE ENTITYID = e.ENTITYID AND TXNTYPEID = 1 AND YEAR(TXNDTE) = YEAR(GETDATE()) - 1) as LYRAMT,
(SELECT COUNT(*) FROM TXN WHERE ENTITYID = e.ENTITYID AND TXNTYPEID = 1 AND YEAR(TXNDTE) = YEAR(GETDATE()) - 2) as L2YRNO,
(SELECT ROUND(ISNULL(SUM(AMT),0),2) FROM TXN WHERE ENTITYID = e.ENTITYID AND TXNTYPEID = 1 AND YEAR(TXNDTE) = YEAR(GETDATE()) - 2) as L2YRAMT,
(SELECT COUNT(*) FROM TXN WHERE ENTITYID = e.ENTITYID AND TXNTYPEID = 1 AND YEAR(TXNDTE) = YEAR(GETDATE()) - 3) as L3YRNO,
(SELECT ROUND(ISNULL(SUM(AMT),0),2) FROM TXN WHERE ENTITYID = e.ENTITYID AND TXNTYPEID = 1 AND YEAR(TXNDTE) = YEAR(GETDATE()) - 3) as L3YRAMT
FROM ENTITY e
GO
/****** Object:  View [dbo].[v_entity_invsummaryR]    Script Date: 05/14/2018 12:34:00 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER OFF
GO
CREATE VIEW [dbo].[v_entity_invsummaryR]
AS
SELECT e.ENTITYID,
ISNULL(e.CTDNO_INV,0) as CTDNO,
ISNULL(e.CTDAMT_INV,0) as CTDAMT,
ISNULL(e.YTDNO_INV,0) as YTDNO,
ISNULL(e.YTDAMT_INV,0) as YTDAMT,
(SELECT COUNT(*) FROM INVOICE WHERE ENTITYID = e.ENTITYID) as CUMNO,
(SELECT ISNULL(SUM(AMT),0) FROM INVOICE WHERE ENTITYID = e.ENTITYID) as CUMTOT,
(SELECT TOP 1 INVDTE FROM INVOICE WHERE ENTITYID = e.ENTITYID ORDER BY INVDTE) as FIRSTDTE,
(SELECT TOP 1 ISNULL(AMT,0) FROM INVOICE WHERE ENTITYID = e.ENTITYID ORDER BY INVDTE) as FIRSTAMT,
(SELECT TOP 1 INVDTE FROM INVOICE WHERE ENTITYID = e.ENTITYID ORDER BY INVDTE DESC) as LASTDTE,
(SELECT TOP 1 ISNULL(AMT,0) FROM INVOICE WHERE ENTITYID = e.ENTITYID ORDER BY INVDTE DESC) as LASTAMT,
(SELECT TOP 1 INVDTE FROM INVOICE WHERE ENTITYID = e.ENTITYID ORDER BY AMT DESC) as HIGHDTE,
(SELECT TOP 1 ISNULL(AMT,0) FROM INVOICE WHERE ENTITYID = e.ENTITYID ORDER BY AMT DESC) as HIGHAMT,
(SELECT CASE WHEN COUNT(*) = 0 THEN 0 ELSE ROUND(ISNULL(SUM(AMT),0) / COUNT(*),2) END FROM INVOICE WHERE ENTITYID = e.ENTITYID) as AVERAGE,
(SELECT COUNT(*) FROM INVOICE WHERE ENTITYID = e.ENTITYID AND YEAR(INVDTE) = YEAR(GETDATE()) - 1) as LYRNO,
(SELECT ROUND(ISNULL(SUM(AMT),0),2) FROM INVOICE WHERE ENTITYID = e.ENTITYID AND YEAR(INVDTE) = YEAR(GETDATE()) - 1) as LYRAMT,
(SELECT COUNT(*) FROM INVOICE WHERE ENTITYID = e.ENTITYID AND YEAR(INVDTE) = YEAR(GETDATE()) - 2) as L2YRNO,
(SELECT ROUND(ISNULL(SUM(AMT),0),2) FROM INVOICE WHERE ENTITYID = e.ENTITYID AND YEAR(INVDTE) = YEAR(GETDATE()) - 2) as L2YRAMT,
(SELECT COUNT(*) FROM INVOICE WHERE ENTITYID = e.ENTITYID AND YEAR(INVDTE) = YEAR(GETDATE()) - 3) as L3YRNO,
(SELECT ROUND(ISNULL(SUM(AMT),0),2) FROM INVOICE WHERE ENTITYID = e.ENTITYID AND YEAR(INVDTE) = YEAR(GETDATE()) - 3) as L3YRAMT
FROM ENTITY e
GO
/****** Object:  View [dbo].[v_entity_disbrsummaryR]    Script Date: 05/14/2018 12:34:00 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER OFF
GO
CREATE VIEW [dbo].[v_entity_disbrsummaryR]
AS
SELECT e.ENTITYID,
ISNULL(e.CTDNO_EXP,0) as CTDNO,
ISNULL(e.CTDAMT_EXP,0) as CTDAMT,
ISNULL(e.YTDNO_EXP,0) as YTDNO,
ISNULL(e.YTDAMT_EXP,0) as YTDAMT,
(SELECT COUNT(*) FROM TXN WHERE ENTITYID = e.ENTITYID AND TXNTYPEID = 2) as CUMNO,
(SELECT ISNULL(SUM(AMT),0) FROM TXN WHERE ENTITYID = e.ENTITYID AND TXNTYPEID = 2) as CUMTOT,
(SELECT TOP 1 TXNDTE FROM TXN WHERE ENTITYID = e.ENTITYID AND TXNTYPEID = 2 ORDER BY TXNDTE) as FIRSTDTE,
(SELECT TOP 1 ISNULL(AMT,0) FROM TXN WHERE ENTITYID = e.ENTITYID AND TXNTYPEID = 2 ORDER BY TXNDTE) as FIRSTAMT,
(SELECT TOP 1 TXNDTE FROM TXN WHERE ENTITYID = e.ENTITYID AND TXNTYPEID = 2 ORDER BY TXNDTE DESC) as LASTDTE,
(SELECT TOP 1 ISNULL(AMT,0) FROM TXN WHERE ENTITYID = e.ENTITYID AND TXNTYPEID = 2 ORDER BY TXNDTE DESC) as LASTAMT,
(SELECT TOP 1 TXNDTE FROM TXN WHERE ENTITYID = e.ENTITYID AND TXNTYPEID = 2 ORDER BY AMT DESC) as HIGHDTE,
(SELECT TOP 1 ISNULL(AMT,0) FROM TXN WHERE ENTITYID = e.ENTITYID AND TXNTYPEID = 2 ORDER BY AMT DESC) as HIGHAMT,
(SELECT CASE WHEN COUNT(*) = 0 THEN 0 ELSE ROUND(ISNULL(SUM(AMT),0) / COUNT(*),2) END FROM TXN WHERE ENTITYID = e.ENTITYID AND TXNTYPEID = 2) as AVERAGE,
(SELECT COUNT(*) FROM TXN WHERE ENTITYID = e.ENTITYID AND TXNTYPEID = 2 AND YEAR(TXNDTE) = YEAR(GETDATE()) - 1) as LYRNO,
(SELECT ROUND(ISNULL(SUM(AMT),0),2) FROM TXN WHERE ENTITYID = e.ENTITYID AND TXNTYPEID = 2 AND YEAR(TXNDTE) = YEAR(GETDATE()) - 1) as LYRAMT,
(SELECT COUNT(*) FROM TXN WHERE ENTITYID = e.ENTITYID AND TXNTYPEID = 2 AND YEAR(TXNDTE) = YEAR(GETDATE()) - 2) as L2YRNO,
(SELECT ROUND(ISNULL(SUM(AMT),0),2) FROM TXN WHERE ENTITYID = e.ENTITYID AND TXNTYPEID = 2 AND YEAR(TXNDTE) = YEAR(GETDATE()) - 2) as L2YRAMT,
(SELECT COUNT(*) FROM TXN WHERE ENTITYID = e.ENTITYID AND TXNTYPEID = 2 AND YEAR(TXNDTE) = YEAR(GETDATE()) - 3) as L3YRNO,
(SELECT ROUND(ISNULL(SUM(AMT),0),2) FROM TXN WHERE ENTITYID = e.ENTITYID AND TXNTYPEID = 2 AND YEAR(TXNDTE) = YEAR(GETDATE()) - 3) as L3YRAMT
FROM ENTITY e
GO
/****** Object:  UserDefinedFunction [dbo].[dsb_payeeProfileReceipt]    Script Date: 05/14/2018 12:34:01 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
/*  //////////////////////////////////////////////////////////////////
	  [05/02/2018 by Lydia]

	  (func)dsb_payeeProfileReceipt
			: Given a ENTITYID (in string), returns Receipt Profile

	\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\ */
CREATE FUNCTION [dbo].[dsb_payeeProfileReceipt](
	@entityid VARCHAR(10),
	@header VARCHAR(10)		-- true | false
)
RETURNS XML
BEGIN

	--||||| Compile an XML in Dashboard-LabelValue Format |||||-----
	DECLARE @data XML


	--||||| Header (Parent) data ||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||
	IF @header = 'true'
	BEGIN
		SET @data = (
				SELECT * 
				FROM (
					SELECT	TOP 1
							'Receipts' AS title,						
							-- selectAction
							'main' AS [selectAction/actionType],
							-- args
							'transactionDetails' AS [selectAction/args/component],
							-- data
							@entityid AS [selectAction/args/data/entityid],
							'R' AS [selectAction/args/data/txntype], 
							'fa-plus' as iconClass,
							'Add Receipt' as iconTitle,
							'modal' as [iconSelectAction/actionType],
							--access 
							'a>cmdiapp.dms.entity_qq(Receipts)>a' AS [iconSelectAction/access],
								--args
								'transaction' as [iconSelectAction/args/component],
								@entityid as [iconSelectAction/args/data/entityid],
								0 as [iconSelectAction/args/data/id],
								0 as [iconSelectAction/args/data/linkTxnId],
								'R' as [iconSelectAction/args/data/txnType]
					FROM (SELECT 1 AS T) A
				) data
				FOR XML PATH('data'), ELEMENTS
				)
	END

	--||||| Detail (Child) data |||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||
	ELSE BEGIN

			SET @data = (
					SELECT * 
					FROM (
						SELECT value, label1, label2, url
						FROM ENTITY
						CROSS APPLY (
							VALUES
							-- Number (Total $)
							('$'+dbo.oPutComma(CAST(ROUND(ISNULL(CUMAMT_REC,0),0) AS INT)), 
								'Total', 'Receipts',NULL),
							-- Number (Most Recent)
							('$'+ CAST((SELECT TOP 1 dbo.oPutComma(CAST(ROUND(ISNULL(AMT,0),0) AS INT)) FROM TXN WHERE TXNTYPEID = 1 AND ENTITYID = @entityid ORDER BY TXNDTE DESC) AS VARCHAR), 
							'Most Recent', (SELECT TOP 1 dbo.oDATEPART(TXNDTE) FROM TXN WHERE TXNTYPEID = 1 AND ENTITYID = @entityid ORDER BY TXNDTE DESC) ,NULL)
						) C (value, label1, label2, url)
						WHERE ENTITYID = CAST(@entityid AS INT)
					) dsdN
					FOR XML AUTO, ELEMENTS, ROOT('data')
				 )
	END

	RETURN @data
END
GO
/****** Object:  UserDefinedFunction [dbo].[dsb_payeeProfileDisbursement]    Script Date: 05/14/2018 12:34:01 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
/*  //////////////////////////////////////////////////////////////////
	  [05/02/2018 by Lydia]

	  (func)dsb_payeeProfileDisbursement
			: Given a ENTITYID (in string), returns Disbursement Profile

	\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\ */
CREATE FUNCTION [dbo].[dsb_payeeProfileDisbursement](
	@entityid VARCHAR(10),
	@header VARCHAR(10)		-- true | false
)
RETURNS XML
BEGIN

	--||||| Compile an XML in Dashboard-LabelValue Format |||||-----
	DECLARE @data XML


	--||||| Header (Parent) data ||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||
	IF @header = 'true'
	BEGIN
		SET @data = (
				SELECT * 
				FROM (
					SELECT	TOP 1
							'Expenditures' AS title,						
							-- selectAction
							'main' AS [selectAction/actionType],
							-- args
							'transactionDetails' AS [selectAction/args/component],
							-- data
							@entityid AS [selectAction/args/data/entityid],
							'D' AS [selectAction/args/data/txntype], 
							'fa-plus' as iconClass,
							'Add Expenditure' as iconTitle,
							'modal' as [iconSelectAction/actionType],
							--access 
							'a>cmdiapp.dms.entity_qq(Disbursements)>a' AS [iconSelectAction/access],
								--args
								'transaction' as [iconSelectAction/args/component],
								@entityid as [iconSelectAction/args/data/entityid],
								0 as [iconSelectAction/args/data/id],
								0 as [iconSelectAction/args/data/linkTxnId],
								'D' as [iconSelectAction/args/data/txnType]
					FROM (SELECT 1 AS T) A
				) data
				FOR XML PATH('data'), ELEMENTS
				)
	END

	--||||| Detail (Child) data |||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||
	ELSE BEGIN

			SET @data = (
					SELECT * 
					FROM (
						SELECT value, label1, label2, url
						FROM ENTITY
						CROSS APPLY (
							VALUES
							-- Number (Total $)
							('$'+dbo.oPutComma(CAST(ROUND(ISNULL(CUMAMT_EXP,0),0) AS INT)), 
								'Total', 'Expenditures',NULL),
							-- Number (Most Recent)
							('$'+ CAST((SELECT TOP 1 dbo.oPutComma(CAST(ROUND(ISNULL(AMT,0),0) AS INT)) FROM TXN WHERE TXNTYPEID = 2 AND ENTITYID = @entityid ORDER BY TXNDTE DESC) AS VARCHAR), 
							'Most Recent', (SELECT TOP 1 dbo.oDATEPART(TXNDTE) FROM TXN WHERE TXNTYPEID = 2 AND ENTITYID = @entityid ORDER BY TXNDTE DESC) ,NULL)
						) C (value, label1, label2, url)
						WHERE ENTITYID = CAST(@entityid AS INT)
					) dsdN
					FOR XML AUTO, ELEMENTS, ROOT('data')
				 )
	END

	RETURN @data
END
GO
/****** Object:  UserDefinedFunction [dbo].[fn_GenTxnStatement]    Script Date: 05/14/2018 12:34:02 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER OFF
GO
CREATE FUNCTION [dbo].[fn_GenTxnStatement](@ENTITYID INT, @TXNTYPE VARCHAR(1))
RETURNS @OUTPUT TABLE (
	ID INT,
	TXNID INT,
	ROOT CHAR(1),
	ENTITYID INT,
	VENDOR VARCHAR(100),
	CODE VARCHAR(3),
	TXNDTE DATETIME,
	AMT MONEY,
	ADJTYPE VARCHAR(50),
	ADJDTE DATETIME,
	FUNDCODE VARCHAR(15),
	ISMEMO BIT,
	DESCRIP VARCHAR(100),
	FECTYPE VARCHAR(60),
	CHKNO VARCHAR(50),
	ELECTYR VARCHAR(4),
	SORTSEQ VARCHAR(25),
	SORTDTE DATETIME,
	FECDESCRIP VARCHAR(100)		
)
AS
BEGIN

DECLARE @ORIGTXNID INT, @TXNTYPEID SMALLINT

SET @TXNTYPEID = (SELECT TXNTYPEID FROM lkTXNTYPE WHERE CODE = @TXNTYPE)

DECLARE @TXN TABLE (
	ID INT NOT NULL IDENTITY(1,1),
	TXNID INT NULL,
	ROOT CHAR(1) NULL,
	ENTITYID INT NULL,
	VENDOR VARCHAR(100) NULL,
	CODE VARCHAR(3) NULL,
	TXNDTE DATETIME NULL,
	AMT MONEY NULL,
	ADJTYPE VARCHAR(50) NULL,
	ADJDTE DATETIME NULL,
	FUNDCODE VARCHAR(15) NULL,
	ISMEMO BIT NULL,
	DESCRIP VARCHAR(100) NULL,
	FECTYPE VARCHAR(60) NULL,
	CHKNO VARCHAR(50) NULL,
	ELECTYR VARCHAR(4) NULL,
	SORTSEQ VARCHAR(25) NULL,
	SORTDTE DATETIME NULL,
	FECDESCRIP VARCHAR(100) NULL
	)

-- All txns for specified vendor
INSERT @TXN (TXNID, ROOT, ENTITYID, CODE, VENDOR, TXNDTE, AMT, ADJTYPE, ADJDTE, FUNDCODE, ISMEMO, DESCRIP, FECTYPE, CHKNO, ELECTYR, SORTSEQ, SORTDTE, FECDESCRIP)	
select t.TXNID, '»', t.ENTITYID, p.CODE, CASE WHEN p.CODE in ('IND','CAN') THEN dbo.oFULLNAME(0, e.PREFIX, e.FNAME, e.MNAME, e.LNAME, e.SUFFIX) ELSE e.ORGNAME END as VENDOR,
	TXNDTE, t.AMT, ISNULL(a.DESCRIP,'') as ADJTYPE, t.ADJDTE, f.FUNDCODE, t.ISMEMO, t.DESCRIP, 
	l.LINE + ' - ' + l.DESCRIP as FECTYPE, t.CHKNO, t.ELECTYR, CASE WHEN ISNULL(t.ORIGTXNID,0) = 0 THEN t.TXNID ELSE t.ORIGTXNID END as SORTSEQ, t.TXNDTE,
	t.FECDESCRIP
from TXN t LEFT JOIN dmFUND f ON t.FUNDID = f.FUNDID 
	LEFT JOIN lkFECTXNTYPE l ON t.FECTXNTYPEID = l.FECTXNTYPEID
	INNER JOIN lkTXNTYPE p ON t.TXNTYPEID = p.TXNTYPEID
	LEFT JOIN lkTXNADJTYPE a ON t.ADJTYPEID = a.ADJTYPEID
	INNER JOIN ENTITY e ON t.ENTITYID = e.ENTITYID
where t.TXNTYPEID = @TXNTYPEID AND t.ENTITYID = @ENTITYID AND ISNULL(t.ADJTYPEID,0) = 0 AND ISNULL(t.LINKTXNID,0) = 0
union
select t.TXNID, '»', t.ENTITYID, p.CODE, CASE WHEN p.CODE in ('IND','CAN') THEN dbo.oFULLNAME(0, e.PREFIX, e.FNAME, e.MNAME, e.LNAME, e.SUFFIX) ELSE e.ORGNAME END as VENDOR,
	t.TXNDTE, t.AMT, ISNULL(a.DESCRIP,'') as ADJTYPE, t.ADJDTE, f.FUNDCODE, t.ISMEMO, t.DESCRIP, 
	l.LINE + ' - ' + l.DESCRIP as FECTYPE, t.CHKNO, t.ELECTYR, CASE WHEN ISNULL(t.ORIGTXNID,0) = 0 THEN t.TXNID ELSE t.ORIGTXNID END as SORTSEQ, t.TXNDTE,
	t.FECDESCRIP
from TXN t LEFT JOIN dmFUND f ON t.FUNDID = f.FUNDID 
	LEFT JOIN lkFECTXNTYPE l ON t.FECTXNTYPEID = l.FECTXNTYPEID
	INNER JOIN lkTXNTYPE p ON t.TXNTYPEID = p.TXNTYPEID
	LEFT JOIN lkTXNADJTYPE a ON t.ADJTYPEID = a.ADJTYPEID
	INNER JOIN ENTITY e ON t.ENTITYID = e.ENTITYID
	INNER JOIN TXN t2 ON t.ORIGTXNID = t2.TXNID 
WHERE t.TXNTYPEID = @TXNTYPEID AND t2.ENTITYID <> t.ENTITYID AND t.ENTITYID = @ENTITYID
union
select t.TXNID, '»', t.ENTITYID, p.CODE, CASE WHEN p.CODE in ('IND','CAN') THEN dbo.oFULLNAME(0, e.PREFIX, e.FNAME, e.MNAME, e.LNAME, e.SUFFIX) ELSE e.ORGNAME END as VENDOR,
	t.TXNDTE, t.AMT, 'Ultimate Vendor' as ADJTYPE, t.ADJDTE, f.FUNDCODE, t.ISMEMO, t.DESCRIP, 
	l.LINE + ' - ' + l.DESCRIP as FECTYPE, t.CHKNO, t.ELECTYR, CASE WHEN ISNULL(t.ORIGTXNID,0) = 0 THEN t.TXNID ELSE t.ORIGTXNID END as SORTSEQ, t.TXNDTE,
	t.FECDESCRIP
from TXN t LEFT JOIN dmFUND f ON t.FUNDID = f.FUNDID 
	LEFT JOIN lkFECTXNTYPE l ON t.FECTXNTYPEID = l.FECTXNTYPEID
	INNER JOIN lkTXNTYPE p ON t.TXNTYPEID = p.TXNTYPEID
	INNER JOIN ENTITY e ON t.ENTITYID = e.ENTITYID
	INNER JOIN TXN t2 ON t.LINKTXNID = t2.TXNID 
WHERE t.TXNTYPEID = @TXNTYPEID AND t2.ENTITYID <> t.ENTITYID AND t.ENTITYID = @ENTITYID 


INSERT @TXN (TXNID, ROOT, ENTITYID, CODE, VENDOR, TXNDTE, AMT, ADJTYPE, ADJDTE, FUNDCODE, ISMEMO, DESCRIP, FECTYPE, CHKNO, ELECTYR, SORTSEQ, SORTDTE,FECDESCRIP)	 
select t.TXNID, ' ', t.ENTITYID, p.CODE, CASE WHEN p.CODE in ('IND','CAN') THEN dbo.oFULLNAME(0, e.PREFIX, e.FNAME, e.MNAME, e.LNAME, e.SUFFIX) ELSE e.ORGNAME END as VENDOR,
	t.TXNDTE, t.AMT, 'Ultimate Vendor' as ADJTYPE, t.TXNDTE, f.FUNDCODE, t.ISMEMO, t.DESCRIP, 
	l.LINE + ' - ' + l.DESCRIP as FECTYPE, t.CHKNO, t.ELECTYR, CASE WHEN ISNULL(t.LINKTXNID,0) = 0 THEN t.TXNID ELSE t.LINKTXNID END as SORTSEQ, t2.TXNDTE,
	t.FECDESCRIP
from TXN t LEFT JOIN dmFUND f ON t.FUNDID = f.FUNDID 
	LEFT JOIN lkFECTXNTYPE l ON t.FECTXNTYPEID = l.FECTXNTYPEID
	INNER JOIN lkTXNTYPE p ON t.TXNTYPEID = p.TXNTYPEID
	INNER JOIN ENTITY e ON t.ENTITYID = e.ENTITYID
	INNER JOIN @TXN t2 ON t.LINKTXNID = t2.TXNID 
WHERE t.TXNTYPEID = @TXNTYPEID 

DECLARE cALL CURSOR READ_ONLY FAST_FORWARD FOR 
	SELECT TXNID FROM @TXN ORDER BY ID
OPEN cALL
FETCH NEXT FROM cALL INTO @ORIGTXNID

WHILE @@FETCH_STATUS = 0
BEGIN
	INSERT @TXN (TXNID, ROOT, ENTITYID, CODE, VENDOR, TXNDTE, AMT, ADJTYPE, ADJDTE, FUNDCODE, ISMEMO, DESCRIP, FECTYPE, CHKNO, ELECTYR, SORTSEQ, SORTDTE,FECDESCRIP)	
	select t.TXNID, ' ', t.ENTITYID, p.CODE, CASE WHEN p.CODE in ('IND','CAN') THEN dbo.oFULLNAME(0, e.PREFIX, e.FNAME, e.MNAME, e.LNAME, e.SUFFIX) ELSE e.ORGNAME END as VENDOR,
		TXNDTE, t.AMT, ISNULL(a.DESCRIP,'') as ADJTYPE, t.ADJDTE, f.FUNDCODE, t.ISMEMO, t.DESCRIP, 
		l.LINE + ' - ' + l.DESCRIP as FECTYPE, t.CHKNO, t.ELECTYR, CASE WHEN ISNULL(t.ORIGTXNID,0) = 0 THEN t.TXNID ELSE t.ORIGTXNID END as SORTSEQ,
		(SELECT TXNDTE FROM TXN WHERE TXNID = @ORIGTXNID) as SORTDTE ,t.FECDESCRIP
	FROM dbo.GetAllTxnId(@ORIGTXNID) o 
		INNER JOIN TXN t 
			LEFT JOIN dmFUND f ON t.FUNDID = f.FUNDID 
			LEFT JOIN lkFECTXNTYPE l ON t.FECTXNTYPEID = l.FECTXNTYPEID
			INNER JOIN lkTXNTYPE p ON t.TXNTYPEID = p.TXNTYPEID
			LEFT JOIN lkTXNADJTYPE a ON t.ADJTYPEID = a.ADJTYPEID
			INNER JOIN ENTITY e ON t.ENTITYID = e.ENTITYID
		ON o.TXNID = t.TXNID AND o.LEVEL > 0

	FETCH NEXT FROM cALL INTO @ORIGTXNID
END
CLOSE cALL
DEALLOCATE cALL

INSERT @OUTPUT 
SELECT * 
FROM @TXN ORDER BY SORTDTE DESC, SORTSEQ, ROOT DESC, TXNID 

RETURN
END
GO
/****** Object:  UserDefinedFunction [dbo].[fn_GenInvStatement]    Script Date: 05/14/2018 12:34:01 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER OFF
GO
CREATE FUNCTION [dbo].[fn_GenInvStatement](@ENTITYID INT)
RETURNS @OUTPUT TABLE (
	ID INT,
	INVOICEID INT,
	ROOT CHAR(1),
	ENTITYID INT,
	VENDOR VARCHAR(100),
	CODE VARCHAR(3),
	INVDTE DATETIME,
	DUEDTE DATETIME,
	AMT MONEY,
	STATUS VARCHAR(20),
	ADJTYPE VARCHAR(50),
	ADJDTE DATETIME,
	FUNDCODE VARCHAR(15),
	ISMEMO BIT,
	DESCRIP VARCHAR(100),
	FECTYPE VARCHAR(60),
	ELECTYR VARCHAR(4),
	SORTSEQ VARCHAR(25),
	SORTDTE DATETIME,
	FECDESCRIP VARCHAR(100)	
)
AS
BEGIN

DECLARE @ORIGINVOICEID INT

DECLARE @INVOICE TABLE (
	ID INT NOT NULL IDENTITY(1,1),
	INVOICEID INT NULL,
	ROOT CHAR(1) NULL,
	ENTITYID INT NULL,
	VENDOR VARCHAR(100) NULL,
	CODE VARCHAR(3) NULL,
	INVDTE DATETIME NULL,
	DUEDTE DATETIME NULL,
	AMT MONEY NULL,
	STATUS VARCHAR(20) NULL,
	ADJTYPE VARCHAR(50) NULL,
	ADJDTE DATETIME NULL,
	FUNDCODE VARCHAR(15) NULL,
	ISMEMO BIT NULL,
	DESCRIP VARCHAR(100) NULL,
	FECTYPE VARCHAR(60) NULL,
	ELECTYR VARCHAR(4) NULL,
	SORTSEQ VARCHAR(25) NULL,
	SORTDTE DATETIME NULL,
	FECDESCRIP VARCHAR(100) NULL
	)

-- All invoices for specified vendor
INSERT @INVOICE (INVOICEID, ROOT, ENTITYID, CODE, VENDOR, INVDTE, DUEDTE, AMT, STATUS, ADJTYPE, ADJDTE, FUNDCODE, ISMEMO, DESCRIP, FECTYPE, ELECTYR, SORTSEQ, SORTDTE,FECDESCRIP)	
select t.INVOICEID, '»', t.ENTITYID, p.CODE, CASE WHEN p.CODE in ('IND','CAN') THEN dbo.oFULLNAME(0, e.PREFIX, e.FNAME, e.MNAME, e.LNAME, e.SUFFIX) ELSE e.ORGNAME END as VENDOR,
	t.INVDTE, t.DUEDTE, t.AMT, s.CODE, ISNULL(a.DESCRIP,'') as ADJTYPE, t.ADJDTE, f.FUNDCODE, t.ISMEMO, t.DESCRIP, 
	l.LINE + ' - ' + l.DESCRIP as FECTYPE, t.ELECTYR,  CASE WHEN ISNULL(t.ORIGINVOICEID,0) = 0 THEN t.INVOICEID ELSE t.ORIGINVOICEID END as SORTSEQ, t.INVDTE,
	t.FECDESCRIP
from INVOICE t LEFT JOIN dmFUND f ON t.FUNDID = f.FUNDID 
	LEFT JOIN lkFECTXNTYPE l ON t.FECTXNTYPEID = l.FECTXNTYPEID
	LEFT JOIN lkTXNADJTYPE a ON t.ADJTYPEID = a.ADJTYPEID
	INNER JOIN ENTITY e 
		INNER JOIN lkENTITYTYPE p ON e.ENTITYTYPEID = p.ENTITYTYPEID
	ON t.ENTITYID = e.ENTITYID
	LEFT JOIN lkINVSTATUS s ON t.INVSTATUSID = s.INVSTATUSID
where t.ENTITYID = @ENTITYID AND ISNULL(t.ADJTYPEID,0) = 0 AND ISNULL(t.LINKINVOICEID,0) = 0
union
select t.INVOICEID, '»', t.ENTITYID, p.CODE, CASE WHEN p.CODE in ('IND','CAN') THEN dbo.oFULLNAME(0, e.PREFIX, e.FNAME, e.MNAME, e.LNAME, e.SUFFIX) ELSE e.ORGNAME END as VENDOR,
	t.INVDTE, t.DUEDTE, t.AMT, s.CODE, ISNULL(a.DESCRIP,'') as ADJTYPE, t.ADJDTE, f.FUNDCODE, t.ISMEMO, t.DESCRIP, 
	l.LINE + ' - ' + l.DESCRIP as FECTYPE, t.ELECTYR, CASE WHEN ISNULL(t.ORIGINVOICEID,0) = 0 THEN t.INVOICEID ELSE t.ORIGINVOICEID END as SORTSEQ, t.INVDTE,
	t.FECDESCRIP
from INVOICE t LEFT JOIN dmFUND f ON t.FUNDID = f.FUNDID 
	LEFT JOIN lkFECTXNTYPE l ON t.FECTXNTYPEID = l.FECTXNTYPEID
	LEFT JOIN lkTXNADJTYPE a ON t.ADJTYPEID = a.ADJTYPEID
	INNER JOIN ENTITY e 
		INNER JOIN lkENTITYTYPE p ON e.ENTITYTYPEID = p.ENTITYTYPEID
	ON t.ENTITYID = e.ENTITYID
	LEFT JOIN lkINVSTATUS s ON t.INVSTATUSID = s.INVSTATUSID
	INNER JOIN INVOICE t2 ON t.ORIGINVOICEID = t2.INVOICEID 
WHERE t2.ENTITYID <> t.ENTITYID AND t.ENTITYID = @ENTITYID 
union
select t.INVOICEID, '»', t.ENTITYID, p.CODE, CASE WHEN p.CODE in ('IND','CAN') THEN dbo.oFULLNAME(0, e.PREFIX, e.FNAME, e.MNAME, e.LNAME, e.SUFFIX) ELSE e.ORGNAME END as VENDOR,
	t.INVDTE, t.DUEDTE, t.AMT, s.CODE, 'Ultimate Vendor' as ADJTYPE, t.ADJDTE, f.FUNDCODE, t.ISMEMO, t.DESCRIP, 
	l.LINE + ' - ' + l.DESCRIP as FECTYPE, t.ELECTYR, CASE WHEN ISNULL(t.ORIGINVOICEID,0) = 0 THEN t.INVOICEID ELSE t.ORIGINVOICEID END as SORTSEQ, t.INVDTE,
	t.FECDESCRIP
from INVOICE t LEFT JOIN dmFUND f ON t.FUNDID = f.FUNDID 
	LEFT JOIN lkFECTXNTYPE l ON t.FECTXNTYPEID = l.FECTXNTYPEID
	INNER JOIN ENTITY e 
			INNER JOIN lkENTITYTYPE p ON e.ENTITYTYPEID = p.ENTITYTYPEID
	ON t.ENTITYID = e.ENTITYID
	LEFT JOIN lkINVSTATUS s ON t.INVSTATUSID = s.INVSTATUSID	
	INNER JOIN INVOICE t2 ON t.LINKINVOICEID = t2.INVOICEID 
WHERE t2.ENTITYID <> t.ENTITYID AND t.ENTITYID = @ENTITYID 

INSERT @INVOICE (INVOICEID, ROOT, ENTITYID, CODE, VENDOR, INVDTE, DUEDTE, AMT, STATUS, ADJTYPE, ADJDTE, FUNDCODE, ISMEMO, DESCRIP, FECTYPE, ELECTYR, SORTSEQ, SORTDTE,FECDESCRIP)	
select t.INVOICEID, ' ', t.ENTITYID, p.CODE, CASE WHEN p.CODE in ('IND','CAN') THEN dbo.oFULLNAME(0, e.PREFIX, e.FNAME, e.MNAME, e.LNAME, e.SUFFIX) ELSE e.ORGNAME END as VENDOR,
	t.INVDTE, t.DUEDTE, t.AMT, s.CODE, 'Ultimate Vendor' as ADJTYPE, t.ADJDTE, f.FUNDCODE, t.ISMEMO, t.DESCRIP, 
	l.LINE + ' - ' + l.DESCRIP as FECTYPE, t.ELECTYR, CASE WHEN ISNULL(t.LINKINVOICEID,0) = 0 THEN t.INVOICEID ELSE t.LINKINVOICEID END as SORTSEQ, t2.INVDTE,
	t.FECDESCRIP
from INVOICE t LEFT JOIN dmFUND f ON t.FUNDID = f.FUNDID 
	LEFT JOIN lkFECTXNTYPE l ON t.FECTXNTYPEID = l.FECTXNTYPEID
	INNER JOIN ENTITY e 
		INNER JOIN lkENTITYTYPE p ON e.ENTITYTYPEID = p.ENTITYTYPEID
	ON t.ENTITYID = e.ENTITYID
	LEFT JOIN lkINVSTATUS s ON t.INVSTATUSID = s.INVSTATUSID
	INNER JOIN @INVOICE t2 ON t.LINKINVOICEID = t2.INVOICEID 

DECLARE cALL CURSOR READ_ONLY FAST_FORWARD FOR 
	SELECT INVOICEID FROM @INVOICE ORDER BY ID
OPEN cALL
FETCH NEXT FROM cALL INTO @ORIGINVOICEID

WHILE @@FETCH_STATUS = 0
BEGIN
	INSERT @INVOICE (INVOICEID, ROOT, ENTITYID, CODE, VENDOR, INVDTE, DUEDTE, AMT, STATUS, ADJTYPE, ADJDTE, FUNDCODE, ISMEMO, DESCRIP, FECTYPE, ELECTYR, SORTSEQ, SORTDTE,FECDESCRIP)	
	select t.INVOICEID, ' ', t.ENTITYID, p.CODE, CASE WHEN p.CODE in ('IND','CAN') THEN dbo.oFULLNAME(0, e.PREFIX, e.FNAME, e.MNAME, e.LNAME, e.SUFFIX) ELSE e.ORGNAME END as VENDOR,
		t.INVDTE, t.DUEDTE, t.AMT, s.CODE, ISNULL(a.DESCRIP,'') as ADJTYPE, t.ADJDTE, f.FUNDCODE, t.ISMEMO, t.DESCRIP, 
		l.LINE + ' - ' + l.DESCRIP as FECTYPE, t.ELECTYR, CASE WHEN ISNULL(t.ORIGINVOICEID,0) = 0 THEN t.INVOICEID ELSE t.ORIGINVOICEID END as SORTSEQ,
		(SELECT INVDTE FROM INVOICE WHERE INVOICEID = @ORIGINVOICEID) as SORTDTE,t.FECDESCRIP
	FROM dbo.GetAllInvId(@ORIGINVOICEID) o 
		INNER JOIN INVOICE t 
			LEFT JOIN dmFUND f ON t.FUNDID = f.FUNDID 
			LEFT JOIN lkFECTXNTYPE l ON t.FECTXNTYPEID = l.FECTXNTYPEID
			LEFT JOIN lkTXNADJTYPE a ON t.ADJTYPEID = a.ADJTYPEID
			INNER JOIN ENTITY e 
				INNER JOIN lkENTITYTYPE p ON e.ENTITYTYPEID = p.ENTITYTYPEID
			ON t.ENTITYID = e.ENTITYID
			LEFT JOIN lkINVSTATUS s ON t.INVSTATUSID = s.INVSTATUSID
		ON o.INVOICEID = t.INVOICEID AND o.LEVEL > 0

	FETCH NEXT FROM cALL INTO @ORIGINVOICEID
END
CLOSE cALL
DEALLOCATE cALL

INSERT @OUTPUT 
SELECT * 
FROM @INVOICE ORDER BY SORTDTE DESC, SORTSEQ, ROOT DESC, INVOICEID 

RETURN
END
GO
/****** Object:  View [dbo].[v_entity_transactionR]    Script Date: 05/14/2018 12:34:00 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER OFF
GO
CREATE VIEW [dbo].[v_entity_transactionR]
AS
SELECT DISTINCT T.TXNID ,
	LX.CODE,   
	E.ENTITYID AS ENTITYID, 
    ISNULL(ET.CODE,'') AS VENDORTYPE,
    ISNULL(E.ORGNAME,'') ORGNAME,
    ISNULL(E.PREFIX,'') PREFIX,
    ISNULL(E.FNAME,'') FNAME,
    ISNULL(E.MNAME,'') MNAME,
    ISNULL(E.LNAME,'') LNAME,
    ISNULL(E.SUFFIX,'') SUFFIX,
    ISNULL(E.STREET1,'') STREET,
    ISNULL(E.STREET2,'') ADDR1,
    ISNULL(E.CITY,'') CITY,
    ISNULL(E.STATE,'') [STATE],
    ISNULL(E.ZIP,'') ZIP,	
    T.TXNDTE,
    ISNULL(T.AMT,0) AMT,
    ISNULL(F.FUNDCODE,'') FUNDCODE,
	ISNULL(LF.LINE,'') + ' - ' + ISNULL(LF.DESCRIP,'') AS LINE,
    ISNULL(T.DESCRIP,'') AS [DESCRIPTION],
    ISNULL(C.DESCRIP,'') AS BANKACCT,
    T.ISMEMO AS MEMO,
    ISNULL(T.MEMOTXT,'') MEMOTXT,
    ISNULL(MT.MONYTYPE,'') PAYTYPE, 
    ISNULL(T.CHKNO,'') CHECKNO,
    ISNULL(T.FECDESCRIP,'') FEC_DESCRIPTION,
    T.COUNTER 
FROM TXN T
    INNER JOIN ENTITY E ON T.ENTITYID = E.ENTITYID
    LEFT OUTER JOIN lkENTITYTYPE ET ON E.ENTITYTYPEID = ET.ENTITYTYPEID
    INNER JOIN dmFUND F ON T.FUNDID = F.FUNDID
    LEFT OUTER JOIN lkTXNCAT LT ON T.TXNCATID = LT.TXNCATID
    LEFT OUTER JOIN lkELECTCD LE ON T.ELECTCDID = LE.ELECTCDID
    LEFT OUTER JOIN lkFECTXNTYPE LF ON T.FECTXNTYPEID = LF.FECTXNTYPEID
    LEFT OUTER JOIN dmCENTER C ON T.CENTERID = C.CENTERID
    LEFT OUTER JOIN JTTXNACCT JC ON T.TXNID = JC.TXNID
    LEFT OUTER JOIN LKCHARTACCT LC ON JC.CHARTACCTID = LC.CHARTACCTID
    LEFT OUTER JOIN LKTXNTYPE LX ON T.TXNTYPEID = LX.TXNTYPEID
    LEFT OUTER JOIN lkEXPMONYTYPE MT ON T.MONYTYPEID = MT.MONYTYPEID
GO
/****** Object:  UserDefinedFunction [dbo].[dsb_payeeProfileHistory]    Script Date: 05/14/2018 12:34:01 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
/*  //////////////////////////////////////////////////////////////////
	  [04/04/2018 by Renjie]

	  (func>Xml)[dsb_payeeProfileHistory]
		{ data: chains }

	\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\ */
CREATE FUNCTION [dbo].[dsb_payeeProfileHistory](
	@entityid VARCHAR(10),
	@header VARCHAR(10)		-- true | false
)
RETURNS XML
BEGIN

	--||||| Compile an XML in Dashboard-LabelValue Format |||||-----
	DECLARE @data XML

	--||||| Header (Parent) data ||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||
	IF @header = 'true'
	BEGIN
		SET @data = (
				SELECT * 
				FROM (
					SELECT	TOP 1
							'History' AS title,						
							-- selectAction
								'main' AS [selectAction/actionType],
								-- args
									'payeehistory' AS [selectAction/args/component],
									-- data
									@entityid AS [selectAction/args/data/entityid]
					FROM (SELECT 1 AS T) A
				) data
				FOR XML PATH('data'), ELEMENTS
				)
	END

	--||||| Detail (Child) data |||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||
	ELSE BEGIN

		SET @data = (
				SELECT 
					(
					 SELECT top 3 CONVERT(VARCHAR, A.CREATEDON, 107) AS [title], [ACTION] AS [subtitle]
					 FROM VENDHIST A 
					 WHERE A.ENTITYID = (CAST(@entityid AS INT))
					 ORDER BY A.CREATEDON DESC 
					 FOR XML PATH('items'), TYPE)				 			
				FOR XML PATH(''), ROOT('data'), ELEMENTS
		)

		-- (Json.net/JsonConvert.SerializeXmlNode) Work-around to make a specific "Single Node" as an Array Node, and make its content as its first element
		SET @data = dbo.[dsb_util_JsonNetHandle_makeSingleNodeIntoArray](@data)
	END

	RETURN @data
END
GO
/****** Object:  UserDefinedFunction [dbo].[payeeProfile_histories]    Script Date: 05/14/2018 12:34:02 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
/*	///////////////////////////////////////////////////////////////////////////////////////////

		[2018-05-10 by Lydia]
			
		(func) [dbo].[payeeProfile_histories]

			eg.
				SELECT [dbo].[payeeProfile_histories]('6')

		Given ENTITYID 
			return aggregated history records

			eg. SELECT * FROM [dbo].[payeeProfile_histories] (6) ORDER BY HSTDATE DESC

	\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\ */
CREATE FUNCTION [dbo].[payeeProfile_histories](
	@ENTITYID INT=0
	)
RETURNS @result TABLE 
(
	[AUDITID] INT,
	[UPDDESC] VARCHAR(50),
	[TABLENAME] VARCHAR(50),
	[HSTDATE] [DateTime],
	[HSTDATEs] VARCHAR(100),
	[USERNAME] VARCHAR(100),
	[ACTION] VARCHAR(50),
	[CHANGEDESC] VARCHAR(max)
)
AS
BEGIN

DECLARE @HOLDTHIS TABLE(
	[HISTORYID] [int] PRIMARY KEY NONCLUSTERED,

	[AUDITID] [int],
	[ACTION] VARCHAR(50) NULL,
	[USERNAME] VARCHAR(100) NULL,
	[UPDDESC] VARCHAR(100) NULL,
	[TABLENAME] [varchar](20) NULL,
	[FIELDNAME] VARCHAR(50) NULL,
	HSTDATE DATETIME NULL,

	OLDVALUE VARCHAR(250) NULL,
	NEWVALUE VARCHAR(250) NULL

	UNIQUE CLUSTERED (AUDITID, HISTORYID)
)

INSERT INTO @HOLDTHIS (HISTORYID,AUDITID,[ACTION],USERNAME,UPDDESC,TABLENAME,FIELDNAME,HSTDATE,OLDVALUE,NEWVALUE)
SELECT  
	H.VENDHISTID,
	H.VENDHISTID, H.[ACTION], 
	LEFT(REPLACE(ISNULL(U.FNAME,'')+' '+ISNULL(U.MNAME,'')+' '+ISNULL(U.LNAME,''),'  ',' '),50) AS USERNAME,
	K.DESCRIP AS UPDDESC,
	H.TABLENAME, H.FIELDNAME,H.CREATEDON,
	H.OLDVALUE, H.NEWVALUE
FROM	VENDHIST H WITH(NOLOCK, INDEX(IX_VENDHIST_ENTITYID))
	LEFT OUTER JOIN ssUSER U ON H.UID=U.UID
	LEFT OUTER JOIN lkUPDTYPE K ON H.UPDTYPEID=K.UPDTYPEID
WHERE H.ENTITYID = @ENTITYID 
	
INSERT INTO @result (AUDITID,UPDDESC,TABLENAME,USERNAME,[ACTION],HSTDATE,HSTDATEs,[CHANGEDESC])
SELECT 	
	AUDITID, UPDDESC, TABLENAME, USERNAME, [ACTION], MAX(HSTDATE), MAX(CONVERT(VARCHAR, HSTDATE, 100)),
 	(
		Select 
			ISNULL(FIELDNAME,'')+' ('+
			CASE WHEN H2.OLDVALUE IS NULL OR H2.OLDVALUE='' THEN 'blank' ELSE H2.OLDVALUE END +'>'+
			CASE WHEN H2.NEWVALUE IS NULL OR H2.NEWVALUE='' THEN 'blank' ELSE H2.NEWVALUE END +') | '
		From @HOLDTHIS H2
		Where H2.AUDITID = A.AUDITID
		For XML PATH ('')
				) AS [CHANGEDESC]
FROM @HOLDTHIS A
GROUP BY
	AUDITID, UPDDESC, TABLENAME, USERNAME, [ACTION]						

UPDATE @result SET CHANGEDESC=REPLACE(CHANGEDESC,'&gt;','>')
UPDATE @result SET CHANGEDESC=SUBSTRING(CHANGEDESC,0,LEN(CHANGEDESC)-1)

	RETURN
END
GO
/****** Object:  UserDefinedFunction [dbo].[dsb_payeeProfileOverview]    Script Date: 05/14/2018 12:34:01 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
/*  //////////////////////////////////////////////////////////////////
	  [05/01/2018 by Lydia]

	  (func>Xml)dsb_payeeProfileOverview
			: Given a ENTITYID (in string), returns a Payee Profile Overview in XML

		eg.
			SELECT dbo.dsb_peopleProfileOverview('74178535')
	\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\ */
CREATE FUNCTION [dbo].[dsb_payeeProfileOverview](
	@entityid VARCHAR(10)
)
RETURNS XML
BEGIN
	DECLARE @data XML
	DECLARE @code VARCHAR(3)
	SET @code = (SELECT TOP 1 t.CODE FROM ENTITY e INNER JOIN lkENTITYTYPE t ON e.ENTITYTYPEID = t.ENTITYTYPEID WHERE e.ENTITYID = CAST(@entityid AS INT))

	IF @code in ('IND','CAN')
	BEGIN
		SET @data = (
				SELECT * 
				FROM (
					SELECT	TOP 1 
							CAST(ENTITYID AS VARCHAR(10)) AS id,						
							dbo.oFULLNAME(0,'',FNAME,MNAME,LNAME,SUFFIX) AS [name],
							-- Address
								STREET1 AS [address/street1],
								STREET2 AS [address/street2],
								CITY AS [address/city],
								[STATE] AS [address/state],
								ZIP AS [address/zip],
								PLUS4 AS [address/plus4],
								dbo.oFULLADDR(STREET1,STREET2,CITY,[STATE],ZIP,PLUS4) AS [address/full],
							-- Contact Numbers
								EMAIL AS [contact/email],
								PHONE AS [contact/phone],
								CELL AS [contact/mobile],
								FAX AS [contact/fax],
							-- Picture
								PICTURE AS photo
					FROM	ENTITY 
					WHERE	ENTITYID = CAST(@entityid AS INT)		
				) data
				FOR XML PATH('data'), ELEMENTS, BINARY BASE64
		 )
	END
	ELSE 
	BEGIN
			SET @data = (
				SELECT * 
				FROM (
					SELECT	TOP 1 
							CAST(ENTITYID AS VARCHAR(10)) AS id,						
							ORGNAME AS [name],
							-- Address
							STREET1 AS [address/street1],
							STREET2 AS [address/street2],
							CITY AS [address/city],
							[STATE] AS [address/state],
							ZIP AS [address/zip],
							PLUS4 AS [address/plus4],
							dbo.oFULLADDR(STREET1,STREET2,CITY,[STATE],ZIP,PLUS4) AS [address/full],
							-- Contact Numbers
							EMAIL AS [contact/email],
							PHONE AS [contact/phone],
							CELL AS [contact/mobile],
							FAX AS [contact/fax],
							-- Picture
							PICTURE AS photo
					FROM	ENTITY 
					WHERE	ENTITYID = CAST(@entityid AS INT)		
				) data
				FOR XML PATH('data'), ELEMENTS, BINARY BASE64
		 )
	END
	RETURN @data
END
GO
/****** Object:  UserDefinedFunction [dbo].[dsb_payeeProfileInfoPerRecType]    Script Date: 05/14/2018 12:34:01 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
/*  //////////////////////////////////////////////////////////////////
	  [05/01/2018 by Lydia]

	  (func)dsb_payeeProfileInfoPerRecType
			: Given a ENTITYID (in string), returns Payee Profile Info per Type
	\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\ */
CREATE FUNCTION [dbo].[dsb_payeeProfileInfoPerRecType](
	@entityid VARCHAR(10),
	@header VARCHAR(10)		-- true | false
)
RETURNS XML
BEGIN

	--||||| Compile an XML in Dashboard-LabelValue Format |||||-----
	DECLARE @data XML

	--||||| Header (Parent) data ||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||
	IF @header = 'true'
	BEGIN
		SET @data = (
				SELECT * 
				FROM (
					SELECT	TOP 1
							T.DESCRIP AS title,						
							-- selectAction
								'modal' AS [selectAction/actionType],
								-- args
									'vendoredit' AS [selectAction/args/component],
									-- data
									@entityid AS [selectAction/args/data/id]
					FROM ENTITY E LEFT OUTER JOIN lkENTITYTYPE T ON E.ENTITYTYPEID = T.ENTITYTYPEID
					WHERE E.ENTITYID = CAST(@entityid AS INT)
				) data
				FOR XML PATH('data'), ELEMENTS, BINARY BASE64
				)
		IF (SELECT COUNT(*) FROM ENTITY WHERE ENTITYID = @entityid AND IS1099 = 1) > 0
		BEGIN
         SET @data.modify('         
              insert <accent>1099</accent> as first
              into (/data)[1]') ; 
        END
	END

	--||||| Detail (Child) data |||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||
	ELSE BEGIN
		DECLARE @CODE VARCHAR(3)
		SET @CODE = (SELECT TOP 1 t.CODE FROM ENTITY e INNER JOIN lkENTITYTYPE t ON e.ENTITYTYPEID = t.ENTITYTYPEID WHERE e.ENTITYID = CAST(@entityid AS INT))
		--||||| Individual Record |||||---------------------------------
		IF @CODE = 'IND'
		BEGIN
			SET @data = (
					SELECT * 
					FROM (
						SELECT label, value, url
						FROM ENTITY e
						CROSS APPLY (
							VALUES
							('Employer', EMPLOYER, NULL),
							('Occupation', OCCUPATION, NULL),
							('Acct ID', VENDACCTID, NULL)
						) C (label, value, url)
						WHERE ENTITYID = CAST(@entityid AS INT)
					) dsdLV
					FOR XML AUTO, ELEMENTS, ROOT('data')
				 ) 
		END
		--||||| Candidate Record |||||---------------------------------
		ELSE IF @CODE = 'CAN'
		BEGIN
			SET @data = (
					SELECT * 
					FROM (
						SELECT label, value, url
						FROM ENTITY e
						CROSS APPLY (
							VALUES
							('Employer', EMPLOYER, NULL),
							('Occupation', OCCUPATION, NULL),
							('Acct ID', VENDACCTID, NULL),
							('Contact', CONTACT, NULL)
						) C (label, value, url)
						WHERE ENTITYID = CAST(@entityid AS INT)
					) dsdLV
					FOR XML AUTO, ELEMENTS, ROOT('data')
				 ) 
		END
		--||||| Candidate Committee Record |||||---------------------------------
		ELSE IF @CODE = 'CCM'
		BEGIN
			SET @data = (
					SELECT * 
					FROM (
						SELECT label, value, url
						FROM ENTITY e
						CROSS APPLY (
							VALUES
							('FEC ID', FECCMTEID, NULL),
							('Tax ID', ISNULL(TAXID,''), NULL),
							('Acct ID', VENDACCTID, NULL),
							('Contact', CONTACT, NULL)		
						) C (label, value, url)
						WHERE ENTITYID = CAST(@entityid AS INT)
					) dsdLV
					FOR XML AUTO, ELEMENTS, ROOT('data')
				 ) 
		END
		--||||| Political Committee Record |||||---------------------------------
		ELSE IF @CODE in ('PAC','COM','PTY','NPO','ANP')
		BEGIN
			SET @data = (
					SELECT * 
					FROM (
						SELECT label, value, url
						FROM ENTITY e
						CROSS APPLY (
							VALUES
							('FEC ID', FECCMTEID, NULL),
							('Tax ID', ISNULL(TAXID,''), NULL),
							('Acct ID', VENDACCTID, NULL),
							('Contact', CONTACT, NULL)
						) C (label, value, url)
						WHERE ENTITYID = CAST(@entityid AS INT)
					) dsdLV
					FOR XML AUTO, ELEMENTS, ROOT('data')
				 ) 
		END
		ELSE
		--||||| Organization Record |||||---------------------------------		
		BEGIN
			SET @data = (
					SELECT * 
					FROM (
						SELECT label, value, url
						FROM ENTITY e
						CROSS APPLY (
							VALUES
							('Tax ID', ISNULL(TAXID,''), NULL),
							('Acct ID', VENDACCTID, NULL),
							('Contact', CONTACT, NULL)
						) C (label, value, url)
						WHERE ENTITYID = CAST(@entityid AS INT)
					) dsdLV
					FOR XML AUTO, ELEMENTS, ROOT('data')
				 ) 
		END
	END
	RETURN @data
END
GO
/****** Object:  UserDefinedFunction [dbo].[dsb_payeeProfileDefaults]    Script Date: 05/14/2018 12:34:01 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
/*  //////////////////////////////////////////////////////////////////
	  [05/01/2018 by Lydia]

	  (func)dsb_payeeProfileDefaults
			: Given a ENTITYID (in string), returns Candidate Info only for Candidate type
	\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\ */
CREATE FUNCTION [dbo].[dsb_payeeProfileDefaults](
	@entityid VARCHAR(10),
	@header VARCHAR(10)		-- true | false
)
RETURNS XML
BEGIN

	--||||| Compile an XML in Dashboard-LabelValue Format |||||-----
	DECLARE @data XML

	--||||| Header (Parent) data ||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||
	IF @header = 'true'
	BEGIN
		SET @data = (
				SELECT * 
				FROM (
					SELECT	TOP 1
							'Setting Defaults' AS title,						
							-- selectAction
								'modal' AS [selectAction/actionType],
								-- args
									'vendoredit' AS [selectAction/args/component],
									-- data
									@entityid AS [selectAction/args/data/id]
					FROM ENTITY E  
					WHERE E.ENTITYID = CAST(@entityid AS INT) 
				) data
				FOR XML PATH('data'), ELEMENTS, BINARY BASE64
				)
	END

	--||||| Detail (Child) data |||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||
	ELSE 
	BEGIN
		IF (SELECT COUNT(*) FROM ENTITY WHERE ENTITYID = CAST(@entityid AS INT) AND (FECTXNTYPEID3X_REC IS NOT NULL OR FECTXNTYPEID3X_EXP IS NOT NULL)) = 1
			BEGIN
			SET @data = (
					SELECT * 
					FROM (
						SELECT label, value, url
						FROM ENTITY E 
						CROSS APPLY (
							VALUES
							('FEC Description', VENDFECDESCRIP, NULL),
							('Form 3X Receipt Line Number', (SELECT t.LINE + ' - ' + t.DESCRIP FROM lkFECTXNTYPE t WHERE t.FECTXNTYPEID = E.FECTXNTYPEID3X_REC), NULL),
							('Form 3X Disbursement Line Number', (SELECT t.LINE + ' - ' + t.DESCRIP FROM lkFECTXNTYPE t WHERE t.FECTXNTYPEID = E.FECTXNTYPEID3X_EXP), NULL) 									
						) C (label, value, url)
						WHERE E.ENTITYID = CAST(@entityid AS INT) 
					) dsdLV
					FOR XML AUTO, ELEMENTS, ROOT('data')
				 ) 
			END
		ELSE IF (SELECT COUNT(*) FROM ENTITY WHERE ENTITYID = CAST(@entityid AS INT) AND (FECTXNTYPEID3P_REC IS NOT NULL OR FECTXNTYPEID3P_EXP IS NOT NULL)) = 1
			BEGIN
			SET @data = (
					SELECT * 
					FROM (
						SELECT label, value, url
						FROM ENTITY E 
						CROSS APPLY (
							VALUES
							('FEC Description', VENDFECDESCRIP, NULL),
							('Form 3P Receipt Line Number', (SELECT t.LINE + ' - ' + t.DESCRIP FROM lkFECTXNTYPE t WHERE t.FECTXNTYPEID = E.FECTXNTYPEID3P_REC), NULL),
							('Form 3P Disbursement Line Number', (SELECT t.LINE + ' - ' + t.DESCRIP FROM lkFECTXNTYPE t WHERE t.FECTXNTYPEID = E.FECTXNTYPEID3P_EXP), NULL) 									
						) C (label, value, url)
						WHERE E.ENTITYID = CAST(@entityid AS INT) 
					) dsdLV
					FOR XML AUTO, ELEMENTS, ROOT('data')
				 ) 
			END
		ELSE
			BEGIN
			SET @data = (
					SELECT * 
					FROM (
						SELECT label, value, url
						FROM ENTITY E 
						CROSS APPLY (
							VALUES
							('FEC Description', VENDFECDESCRIP, NULL),
							('Form 3 Receipt Line Number', (SELECT t.LINE + ' - ' + t.DESCRIP FROM lkFECTXNTYPE t WHERE t.FECTXNTYPEID = E.FECTXNTYPEID_REC), NULL),
							('Form 3 Disbursement Line Number', (SELECT t.LINE + ' - ' + t.DESCRIP FROM lkFECTXNTYPE t WHERE t.FECTXNTYPEID = E.FECTXNTYPEID_EXP), NULL) 
						) C (label, value, url)
						WHERE E.ENTITYID = CAST(@entityid AS INT) 
					) dsdLV
					FOR XML AUTO, ELEMENTS, ROOT('data')
				 ) 
			END
	END
	RETURN @data
END
GO


IF  EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[dsb_payeeProfileCandidateInfoWithMemberTracking]') AND type in (N'FN', N'IF', N'TF', N'FS', N'FT'))
DROP FUNCTION [dbo].[dsb_payeeProfileCandidateInfoWithMemberTracking]
GO
/*
	[09/27/2018] By Ryan
	used when configVal for 'cong-memb-track' = 'Y'
*/
CREATE FUNCTION [dbo].[dsb_payeeProfileCandidateInfoWithMemberTracking](
	@entityid VARCHAR(10),
	@header VARCHAR(10)		-- true | false
)
RETURNS XML
BEGIN

	--||||| Compile an XML in Dashboard-LabelValue Format |||||-----
	DECLARE @data XML

	--||||| Header (Parent) data ||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||
	IF @header = 'true'
	BEGIN
		SET @data = (
				SELECT * 
				FROM (
					SELECT	TOP 1
							'Candidate Info' AS title,						
							-- selectAction
								'modal' AS [selectAction/actionType],
								-- args
									(CASE WHEN ISNULL(E.LEGISLATORID, '') <> '' THEN 'legislator' ELSE 'vendoredit' END) AS [selectAction/args/component],
									-- data
									(CASE WHEN ISNULL(E.LEGISLATORID, '') <> '' THEN  E.LEGISLATORID ELSE @entityid END) AS [selectAction/args/data/id]
					FROM ENTITY E INNER JOIN lkENTITYTYPE T ON E.ENTITYTYPEID = T.ENTITYTYPEID 
					WHERE E.ENTITYID = CAST(@entityid AS INT) AND (T.CODE = 'CCM' OR (T.CODE IN ('CAN', 'PAC') AND ISNULL(E.LEGISLATORID,'') <> ''))
				) data
				FOR XML PATH('data'), ELEMENTS, BINARY BASE64
				)
		IF(@data IS NULL)
		BEGIN
		SET @data =(
				SELECT * 
				FROM (
					SELECT	
							'Candidate Info' AS title
				) data
				FOR XML PATH('data'), ELEMENTS, BINARY BASE64
				)
		END
	END

	--||||| Detail (Child) data |||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||
	ELSE 
	BEGIN
			SET @data = (
					SELECT * 
					FROM (
						SELECT label, value, url
						FROM ENTITY E INNER JOIN lkENTITYTYPE T ON E.ENTITYTYPEID = T.ENTITYTYPEID 
						CROSS APPLY (
							VALUES
							('Candidate Name', dbo.oFULLNAME(0, CANDPREFIX, CANDFNAME, CANDMNAME, CANDLNAME, CANDSUFFIX), NULL),
							('Office', 
								CASE CANDOFFICE 
									WHEN 'S' THEN 'Senate (' + CANDSTATE + ')' 
									WHEN 'H' THEN 'Congress (' + CANDSTATE + ' - ' + CANDDIST + ')' 
									ELSE NULl END, NULL),
							('Candidate FEC ID', CANDFECID, NULL)		
						) C (label, value, url)
						WHERE E.ENTITYID = CAST(@entityid AS INT) AND (T.CODE = 'CCM' OR (T.CODE IN ('CAN', 'PAC') AND ISNULL(E.LEGISLATORID,'') <> ''))
					) dsdLV
					FOR XML AUTO, ELEMENTS, ROOT('data')
				 ) 
			IF(@data IS NULL)
		BEGIN
		SET @data =(
				SELECT * 
				FROM (
					SELECT	
							'N/A' AS label
				) data
				FOR XML PATH('data'), ELEMENTS, BINARY BASE64
				)
		END
	END
	RETURN @data
END
GO
GRANT EXECUTE ON [dbo].[dsb_payeeProfileCandidateInfoWithMemberTracking] TO [Regular Users] AS [dbo]
GO
/****** Object:  UserDefinedFunction [dbo].[dsb_payeeProfileCandidateInfo]    Script Date: 05/14/2018 12:34:01 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
/*  //////////////////////////////////////////////////////////////////
	  [05/01/2018 by Lydia]

	  (func)dsb_payeeProfileCandidateInfo
			: Given a ENTITYID (in string), returns Candidate Info only for Candidate type
	\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\ */
CREATE FUNCTION [dbo].[dsb_payeeProfileCandidateInfo](
	@entityid VARCHAR(10),
	@header VARCHAR(10)		-- true | false
)
RETURNS XML
BEGIN

	--||||| Compile an XML in Dashboard-LabelValue Format |||||-----
	DECLARE @data XML

	--||||| Header (Parent) data ||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||
	IF @header = 'true'
	BEGIN
		SET @data = (
				SELECT * 
				FROM (
					SELECT	TOP 1
							'Candidate Info' AS title,						
							-- selectAction
								'modal' AS [selectAction/actionType],
								-- args
									'vendoredit' AS [selectAction/args/component],
									-- data
									@entityid AS [selectAction/args/data/id]
					FROM ENTITY E INNER JOIN lkENTITYTYPE T ON E.ENTITYTYPEID = T.ENTITYTYPEID 
					WHERE E.ENTITYID = CAST(@entityid AS INT) AND T.CODE = 'CCM'
				) data
				FOR XML PATH('data'), ELEMENTS, BINARY BASE64
				)
		IF(@data IS NULL)
		BEGIN
		SET @data =(
				SELECT * 
				FROM (
					SELECT	
							'Candidate Info' AS title
				) data
				FOR XML PATH('data'), ELEMENTS, BINARY BASE64
				)
		END
	END

	--||||| Detail (Child) data |||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||
	ELSE 
	BEGIN
			SET @data = (
					SELECT * 
					FROM (
						SELECT label, value, url
						FROM ENTITY E INNER JOIN lkENTITYTYPE T ON E.ENTITYTYPEID = T.ENTITYTYPEID 
						CROSS APPLY (
							VALUES
							('Candidate Name', dbo.oFULLNAME(0, CANDPREFIX, CANDFNAME, CANDMNAME, CANDLNAME, CANDSUFFIX), NULL),
							('Office', 
								CASE CANDOFFICE 
									WHEN 'S' THEN 'Senate (' + CANDSTATE + ')' 
									WHEN 'H' THEN 'Congress (' + CANDSTATE + ' - ' + CANDDIST + ')' 
									ELSE NULl END, NULL),
							('Candidate FEC ID', CANDFECID, NULL)		
						) C (label, value, url)
						WHERE E.ENTITYID = CAST(@entityid AS INT) AND T.CODE = 'CCM'
					) dsdLV
					FOR XML AUTO, ELEMENTS, ROOT('data')
				 ) 
			IF(@data IS NULL)
		BEGIN
		SET @data =(
				SELECT * 
				FROM (
					SELECT	
							'N/A' AS label
				) data
				FOR XML AUTO, ELEMENTS, ROOT('data')
				)
		END
	END
	RETURN @data
END
GO
GRANT SELECT ON [dbo].[v_entity_disbrsummaryR] TO [Regular Users] AS [dbo]
GO
GRANT SELECT ON [dbo].[v_entity_invsummaryR] TO [Regular Users] AS [dbo]
GO
GRANT SELECT ON [dbo].[v_entity_rcptsummaryR] TO [Regular Users] AS [dbo]
GO
GRANT SELECT ON [dbo].[v_entity_transactionR] TO [Regular Users] AS [dbo]
GO
GRANT EXECUTE ON [dbo].[dsb_payeeProfileCandidateInfo] TO [Regular Users] AS [dbo]
GO
GRANT EXECUTE ON [dbo].[dsb_payeeProfileDefaults] TO [Regular Users] AS [dbo]
GO
GRANT EXECUTE ON [dbo].[dsb_payeeProfileDisbursement] TO [Regular Users] AS [dbo]
GO
GRANT EXECUTE ON [dbo].[dsb_payeeProfileHistory] TO [Regular Users] AS [dbo]
GO
GRANT EXECUTE ON [dbo].[dsb_payeeProfileInfoPerRecType] TO [Regular Users] AS [dbo]
GO
GRANT EXECUTE ON [dbo].[dsb_payeeProfileInvoices] TO [Regular Users] AS [dbo]
GO
GRANT EXECUTE ON [dbo].[dsb_payeeProfileOverview] TO [Regular Users] AS [dbo]
GO
GRANT EXECUTE ON [dbo].[dsb_payeeProfileReceipt] TO [Regular Users] AS [dbo]
GO
GRANT SELECT ON [dbo].[fn_GenInvStatement] TO [Regular Users] AS [dbo]
GO
GRANT SELECT ON [dbo].[fn_GenTxnStatement] TO [Regular Users] AS [dbo]
GO
GRANT SELECT ON [dbo].[payeeProfile_histories] TO [Regular Users] AS [dbo]
GO



IF OBJECT_ID('dsb_payeeBudgetAllocate') IS NOT NULL
	DROP FUNCTION dsb_payeeBudgetAllocate
GO

/*  //////////////////////////////////////////////////////////////////
	  [06/19/2018 by Renjie]

	  (func)[dsb_payeeBudgetAllocate]
			: Given a entityid (in string), returns a budget allocate info

		eg.
			SELECT dbo.[dsb_payeeBudgetAllocate]('994', 'true')
			SELECT dbo.[dsb_payeeBudgetAllocate]('994', 'false')
	\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\ */
CREATE FUNCTION [dbo].[dsb_payeeBudgetAllocate](
	@entityid VARCHAR(10),
	@header VARCHAR(10)		-- true | false
)
RETURNS XML
BEGIN

	--||||| Compile an XML in Dashboard-LabelValue Format |||||-----
	DECLARE @data XML


	--||||| Header (Parent) data ||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||
	IF @header = 'true'
	BEGIN
		SET @data = (
				SELECT * 
				FROM (
					SELECT	TOP 1
							'Budget' AS title,						
							-- selectAction
								'main' AS [selectAction/actionType],

								-- args
									'budgetTxn' AS [selectAction/args/component],
									-- data
									@entityid AS [selectAction/args/data/id],
							'fa-cog' as iconClass,
							'Budget Setting' as iconTitle,
							'modal' as [iconSelectAction/actionType],
							--access 
							'a>cmdiapp.dms.entity_qq(Vendors)>a' AS [iconSelectAction/access],
								--args
								'openBudgetSetting' as [iconSelectAction/args/component],
								@entityid as [iconSelectAction/args/data/id]
					FROM (SELECT 1 AS T) A
				) data
				FOR XML PATH('data'), ELEMENTS
				)
	END

	--||||| Detail (Child) data |||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||
	ELSE BEGIN
	DECLARE @budget INT
	DECLARE @totalContribution INT
	SET @budget = ISNULL((SELECT SUM(amount) FROM BudgetAlloc WHERE ENTITYID=@entityid AND YEAR(expectedDate)=YEAR(GETDATE())),0)
	SET @totalContribution = ISNULL((SELECT SUM(T.AMT) FROM TXN T INNER JOIN lkFECTXNTYPE LK ON T.FECTXNTYPEID=LK.FECTXNTYPEID
WHERE LK.LINE='SB23' AND T.TXNTYPEID=2 AND T.ENTITYID=@entityid AND YEAR(T.TXNDTE)=YEAR(getdate())),0)
			SET @data = (
					SELECT * 
					FROM (
						SELECT value, label1, label2, url
						FROM  (
							VALUES
							-- Number (Total $)
							('$'+dbo.oPutComma(@budget), 
								'Total Budget', NULL,NULL),
							-- Number (Most outstanding)
							('$'+dbo.oPutComma(@totalContribution), 
								'Total Contribution', NULL,NULL)
						) C (value, label1, label2, url)
					) dsdN
					FOR XML AUTO, ELEMENTS, ROOT('data')
				 )
	END

	RETURN @data
END

GO
GRANT EXECUTE ON [dbo].[dsb_payeeBudgetAllocate] TO [Regular Users] AS [dbo]
GO


IF OBJECT_ID('dsb_payeeProfileContacts') IS NOT NULL
	DROP FUNCTION dsb_payeeProfileContacts
GO
/*  //////////////////////////////////////////////////////////////////
	  [01/15/2019 by Ryan]

	  (func>Xml)dsb_payeeProfileContacts
		eg.
			SELECT dbo.dsb_payeeProfileContacts('6','true')
			SELECT dbo.dsb_payeeProfileContacts('6','false')

		{ data: profilePanel }
		eg.

	\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\ */
CREATE FUNCTION [dbo].[dsb_payeeProfileContacts](
	@entityid VARCHAR(10),
	@header VARCHAR(10)		-- true | false
)
RETURNS XML
BEGIN

	--||||| Compile an XML in Dashboard-LabelValue Format |||||-----
	DECLARE @data XML

	--||||| Header (Parent) data ||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||
	IF @header = 'true'
	BEGIN
		SET @data = (
				SELECT * 
				FROM (
					SELECT	TOP 1
							'Contacts' AS title,						
							-- selectAction
								'main' AS [selectAction/actionType],
								-- args
									'payeeProfileContacts' AS [selectAction/args/component],
									-- data
									@entityid AS [selectAction/args/data/entityId],
									(select LEGISLATORID from ENTITY where ENTITYID = CAST(@entityid as int)) as [selectAction/args/data/legislatorId],
							'fa-plus' as iconClass,
							'Add Contact' as iconTitle,
							'modal' as [iconSelectAction/actionType],
							--access 
							'a>cmdiapp.dms.entity_qq(Vendors)>e' AS [iconSelectAction/access],
								--args
								'editPayeeContact' as [iconSelectAction/args/component],
								@entityid as [iconSelectAction/args/data/entityId]
					FROM (SELECT 1 AS T) A
				) data
				FOR XML PATH('data'), ELEMENTS
				)
	END

	--||||| Detail (Child) data |||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||
	ELSE BEGIN

		SET @data = (
				SELECT 

					(SELECT COUNT(*) FROM ENTITYCONTACT WHERE ENTITYID = CAST(@entityid AS INT)) AS [value],
					'Contacts' AS [label],
					(SELECT TOP 1
						ISNULL(FNAME,'')+' '+ISNULL(LNAME,'') AS [title],'hidden' AS [dotClass]
					 FROM ENTITYCONTACT 
					 WHERE ISNULL(INACTIVE,0)=0 
							AND ENTITYID = CAST(@entityid AS INT)
							
					 ORDER BY PRIME DESC, UPDATEDON DESC
					 FOR XML PATH('items'), TYPE),
					 (SELECT TOP 1
						ISNULL(STREET,'')+' '+ISNULL(ADDR1,'') AS [title],'hidden' AS [dotClass]
					 FROM ENTITYCONTACT 
					 WHERE ISNULL(INACTIVE,0)=0 
							AND ENTITYID = CAST(@entityid AS INT)
							
					 ORDER BY PRIME DESC, UPDATEDON DESC
					 FOR XML PATH('items'), TYPE),
					 (SELECT TOP 1
						CASE WHEN 
						ISNULL(CITY,'')='' THEN ISNULL(STATE,'')+' '+ISNULL(ZIP,'')
						ELSE ISNULL(CITY,'')+', '+ISNULL(STATE,'')+' '+ISNULL(ZIP,'')
						END AS [title],'hidden' AS [dotClass]
					 FROM ENTITYCONTACT 
					 WHERE ISNULL(INACTIVE,0)=0 
							AND ENTITYID = CAST(@entityid AS INT)
							
					 ORDER BY PRIME DESC, UPDATEDON DESC
					 FOR XML PATH('items'), TYPE),
					 (SELECT TOP 1
						CASE WHEN 
						ISNULL(HMPHN,'')='' THEN ISNULL(BSPHN,'')
						ELSE ISNULL(HMPHN,'')
						END AS [title],'hidden' AS [dotClass]
					 FROM ENTITYCONTACT 
					 WHERE ISNULL(INACTIVE,0)=0 
							AND ENTITYID = CAST(@entityid AS INT)
							
					 ORDER BY PRIME DESC, UPDATEDON DESC
					 FOR XML PATH('items'), TYPE),
					 (SELECT TOP 1
						ISNULL(EMAIL,'') AS [title],'hidden' AS [dotClass]
					 FROM ENTITYCONTACT 
					 WHERE ISNULL(INACTIVE,0)=0 
							AND ENTITYID = CAST(@entityid AS INT)
							
					 ORDER BY PRIME DESC, UPDATEDON DESC
					 FOR XML PATH('items'), TYPE)
					
				 			
				FOR XML PATH(''), ROOT('data'), ELEMENTS
		)

		-- (Json.net/JsonConvert.SerializeXmlNode) Work-around to make a specific "Single Node" as an Array Node, and make its content as its first element
		SET @data = dbo.[dsb_util_JsonNetHandle_makeSingleNodeIntoArray](@data)
	END

	RETURN @data
END
GO
GRANT EXECUTE ON [dbo].[dsb_payeeProfileContacts] TO [Regular Users] AS [dbo]
GO

