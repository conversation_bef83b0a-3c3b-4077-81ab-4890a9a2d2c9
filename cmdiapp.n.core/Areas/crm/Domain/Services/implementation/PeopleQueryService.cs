﻿using cmdiapp.n.core.Areas.crm.Domain.Models;
using cmdiapp.n.core.Areas.crm.ViewModels;
using cmdiapp.n.core.Areas.query.Domain.Models;
using cmdiapp.n.core.Library;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace cmdiapp.n.core.Areas.crm.Domain.Services.implementation
{
    public class PeopleQueryService : IPeopleQueryService
    {
        private readonly IQueryService _queryService;
        private readonly IErrorLogger _errorLogger;

        public PeopleQueryService(IQueryService queryService, IErrorLogger errorLogger)
        {
            _queryService = queryService;
            _errorLogger = errorLogger;
        }

        public Task<PagedExternalApiResult<List<object>>> GetPeopleRecordsAsync(
            ExternalApiSpecification spec,
            CancellationToken cancellationToken)
        {
            if (spec == null)
            {
                throw new ArgumentNullException(nameof(spec));
            }

            cancellationToken.ThrowIfCancellationRequested();

            try
            {
                if (!spec.Filters.Any())
                {
                    spec.Filters.Add(new PeopleNoFilterSpecificationFilter());
                }

                var queryInstance = ConvertSpecToQueryInstance(spec);
                var result = _queryService.ToList(
                    queryInstance,
                    new QueryRuntimeParams
                    {
                        resultType = QueryResultType.pagedExport,
                        page = spec.PageNumber,
                        pageSize = spec.PageSize
                    });
                return Task.FromResult(new PagedExternalApiResult<List<object>>
                {
                    IsSuccessful = true,
                    Result = result,
                    PageNumber = spec.PageNumber,
                    PageSize = spec.PageSize
                });
            }
            catch (Exception ex)
            {
                _errorLogger.Log(
                    $"Encountered exception when attempting to run PeopleQuery {ex.GetType().Name}: {ex.Message}");
                return Task.FromResult(new PagedExternalApiResult<List<object>>
                {
                    IsSuccessful = false,
                    Message = "An error occurred."
                });
            }
        }

        private QueryInstanceInfo ConvertSpecToQueryInstance(ExternalApiSpecification spec)
        {
            var instance = new QueryInstanceInfo()
            {
                defKey = "peopleSearch",
                filters = spec.Filters.Select(f => f.ConvertToQueryInstanceFilter()).ToList(),
                exportOptions = GetExportOptions(spec.ExportGroupsAndIds)
            };

            return instance;
        }

        private List<QueryExportOptionInstance> GetDefaultExportOptions()
        {
            return new List<QueryExportOptionInstance>
            {
                new QueryExportOptionInstance 
                {
                    key = "pseg_idRecordType",
                    groupKey = "pseg_idRecordTypeGroup"
                },
                new QueryExportOptionInstance 
                {
                    key = "pseg_name",
                    groupKey = "pseg_nameGroup"
                },
                new QueryExportOptionInstance 
                {
                    key = "pseg_empl",
                    groupKey = "pseg_emplGroup"
                },
                new QueryExportOptionInstance 
                {
                    key = "pseg_addressPrimary",
                    groupKey = "pseg_addressGroup"
                },
                new QueryExportOptionInstance 
                {
                    key = "pseg_phones",
                    groupKey = "pseg_phonesGroup"
                },
                new QueryExportOptionInstance 
                {
                    key = "pseg_giving",
                    groupKey = "pseg_givingGroup"
                }
            };
        }

        private List<QueryExportOptionInstance> GetExportOptions(string[] exportOptions)
        {
            if (exportOptions == null || exportOptions.Count() == 0)
                return GetDefaultExportOptions();
            else
            {
                List<QueryExportOptionInstance> listOfExportOptions = new List<QueryExportOptionInstance>();
                for (int i = 0; i < exportOptions.Count(); i++)
                {
                    string[] option = exportOptions[i].Split(',');
                    listOfExportOptions.Add(
                        new QueryExportOptionInstance
                        {
                            key =  option[1],
                            groupKey = option[0]
                        }
                    );
                }
                return listOfExportOptions;
            }
        }

    }
}