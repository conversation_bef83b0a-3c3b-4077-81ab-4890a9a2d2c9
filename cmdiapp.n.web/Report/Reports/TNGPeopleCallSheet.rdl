<?xml version="1.0" encoding="utf-8"?>
<Report xmlns="http://schemas.microsoft.com/sqlserver/reporting/2008/01/reportdefinition" xmlns:rd="http://schemas.microsoft.com/SQLServer/reporting/reportdesigner">
  <Body>
    <ReportItems>
      <Tablix Name="List1">
        <TablixBody>
          <TablixColumns>
            <TablixColumn>
              <Width>7.63889in</Width>
            </TablixColumn>
          </TablixColumns>
          <TablixRows>
            <TablixRow>
              <Height>6.125in</Height>
              <TablixCells>
                <TablixCell>
                  <CellContents>
                    <Rectangle Name="List1_Contents">
                      <ReportItems>
                        <Image Name="image1">
                          <Source>Database</Source>
                          <Value>=Fields!PICTURE.Value</Value>
                          <MIMEType>image/bmp</MIMEType>
                          <Sizing>Fit</Sizing>
                          <Top>0.375in</Top>
                          <Left>0.01389in</Left>
                          <Height>1.25in</Height>
                          <Width>1.125in</Width>
                          <Style />
                        </Image>
                        <Line Name="line2">
                          <Top>3.16667in</Top>
                          <Left>0.01389in</Left>
                          <Height>0in</Height>
                          <Width>7.375in</Width>
                          <ZIndex>1</ZIndex>
                          <Style>
                            <Border>
                              <Style>Solid</Style>
                            </Border>
                          </Style>
                        </Line>
                        <Textbox Name="tbBIO">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=Fields!BIO.Value</Value>
                                  <Style />
                                </TextRun>
                              </TextRuns>
                              <Style />
                            </Paragraph>
                          </Paragraphs>
                          <Top>3.25in</Top>
                          <Left>0.01389in</Left>
                          <Height>0.5in</Height>
                          <Width>7.375in</Width>
                          <ZIndex>2</ZIndex>
                          <Style>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                        <Textbox Name="tbBackgroundLabel">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>Background</Value>
                                  <Style>
                                    <FontSize>12pt</FontSize>
                                    <FontWeight>Bold</FontWeight>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style />
                            </Paragraph>
                          </Paragraphs>
                          <Top>2.875in</Top>
                          <Left>0.01389in</Left>
                          <Height>0.25in</Height>
                          <Width>1.375in</Width>
                          <ZIndex>3</ZIndex>
                          <Style>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                        <Textbox Name="tbName">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=Fields!NAME.Value</Value>
                                  <Style>
                                    <FontSize>12pt</FontSize>
                                    <FontWeight>Bold</FontWeight>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style />
                            </Paragraph>
                          </Paragraphs>
                          <Left>0.13889in</Left>
                          <Height>0.25in</Height>
                          <Width>2.14584in</Width>
                          <ZIndex>4</ZIndex>
                          <Style>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                        <Textbox Name="tbPID">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>="ID: " + CStr(Fields!PID.Value) + IIf(Fields!TrackNo.Value&gt;0, CHR(10) + "Tracking#: " + CStr(Fields!TrackNo.Value), "")</Value>
                                  <Style>
                                    <FontSize>9pt</FontSize>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Left</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <Left>2.35417in</Left>
                          <Height>0.25in</Height>
                          <Width>1.5625in</Width>
                          <ZIndex>5</ZIndex>
                          <Style>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                        <Textbox Name="tbOccupationAndEmployer">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=IIf(IsNothing(Fields!OCCUPATION.Value),"",Fields!OCCUPATION.Value)</Value>
                                  <Style>
                                    <FontSize>9pt</FontSize>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Right</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <Left>3.98612in</Left>
                          <Height>0.25in</Height>
                          <Width>1.47743in</Width>
                          <ZIndex>6</ZIndex>
                          <Style>
                            <Border>
                              <Style>None</Style>
                            </Border>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                            <Direction>RTL</Direction>
                          </Style>
                        </Textbox>
                        <Textbox Name="tbHomePhoneLabel">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>Home:</Value>
                                  <Style />
                                </TextRun>
                              </TextRuns>
                              <Style />
                            </Paragraph>
                          </Paragraphs>
                          <Top>0.5in</Top>
                          <Left>1.26389in</Left>
                          <Height>0.25in</Height>
                          <Width>0.625in</Width>
                          <ZIndex>7</ZIndex>
                          <Style>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                        <Textbox Name="tbHomePhone">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=Fields!HMPH.Value</Value>
                                  <Style />
                                </TextRun>
                              </TextRuns>
                              <Style />
                            </Paragraph>
                          </Paragraphs>
                          <Top>0.5in</Top>
                          <Left>2.01389in</Left>
                          <Height>0.25in</Height>
                          <Width>2.375in</Width>
                          <ZIndex>8</ZIndex>
                          <Style>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                        <Textbox Name="tbCellPhoneLabel">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>Cell:</Value>
                                  <Style />
                                </TextRun>
                              </TextRuns>
                              <Style />
                            </Paragraph>
                          </Paragraphs>
                          <Top>0.75in</Top>
                          <Left>1.26389in</Left>
                          <Height>0.25in</Height>
                          <Width>0.625in</Width>
                          <ZIndex>9</ZIndex>
                          <Style>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                        <Textbox Name="tbCellPhone">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=Fields!CELLPH.Value</Value>
                                  <Style />
                                </TextRun>
                              </TextRuns>
                              <Style />
                            </Paragraph>
                          </Paragraphs>
                          <Top>0.75in</Top>
                          <Left>2.01389in</Left>
                          <Height>0.25in</Height>
                          <Width>2.375in</Width>
                          <ZIndex>10</ZIndex>
                          <Style>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                        <Textbox Name="tbEmailLabel">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>Email:</Value>
                                  <Style />
                                </TextRun>
                              </TextRuns>
                              <Style />
                            </Paragraph>
                          </Paragraphs>
                          <Top>1in</Top>
                          <Left>1.26389in</Left>
                          <Height>0.25in</Height>
                          <Width>0.625in</Width>
                          <ZIndex>11</ZIndex>
                          <Style>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                        <Textbox Name="tbEmail">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=Fields!EMAIL.Value</Value>
                                  <Style />
                                </TextRun>
                              </TextRuns>
                              <Style />
                            </Paragraph>
                          </Paragraphs>
                          <Top>1in</Top>
                          <Left>2.01389in</Left>
                          <Height>0.25in</Height>
                          <Width>2.375in</Width>
                          <ZIndex>12</ZIndex>
                          <Style>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                        <Textbox Name="tbWorkPhone">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>="Work: " &amp; Fields!BUSPH.Value</Value>
                                  <Style />
                                </TextRun>
                              </TextRuns>
                              <Style />
                            </Paragraph>
                          </Paragraphs>
                          <Top>0.5in</Top>
                          <Left>4.63889in</Left>
                          <Height>0.25in</Height>
                          <Width>2.75in</Width>
                          <ZIndex>13</ZIndex>
                          <Style>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                        <Textbox Name="tbAssistant">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>="Assistant:  " + Fields!ASSISTANT.Value</Value>
                                  <Style />
                                </TextRun>
                              </TextRuns>
                              <Style />
                            </Paragraph>
                          </Paragraphs>
                          <Top>0.75in</Top>
                          <Left>4.63889in</Left>
                          <Height>0.25in</Height>
                          <Width>2.75in</Width>
                          <ZIndex>14</ZIndex>
                          <Style>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                        <Textbox Name="tbInformalSalutation">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>="Informal Salutation:  " + Fields!INFSALUT.Value</Value>
                                  <Style />
                                </TextRun>
                              </TextRuns>
                              <Style />
                            </Paragraph>
                          </Paragraphs>
                          <Top>1in</Top>
                          <Left>4.63889in</Left>
                          <Height>0.25in</Height>
                          <Width>2.75in</Width>
                          <ZIndex>15</ZIndex>
                          <Style>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                        <Textbox Name="tbSpouseName">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>="Spouse:  " + Fields!SPOUSENAME.Value</Value>
                                  <Style />
                                </TextRun>
                              </TextRuns>
                              <Style />
                            </Paragraph>
                          </Paragraphs>
                          <Top>1.25in</Top>
                          <Left>4.63889in</Left>
                          <Height>0.25in</Height>
                          <Width>2.75in</Width>
                          <ZIndex>16</ZIndex>
                          <Style>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                        <Line Name="line4">
                          <Top>0.375in</Top>
                          <Left>1.26389in</Left>
                          <Height>0in</Height>
                          <Width>6.125in</Width>
                          <ZIndex>17</ZIndex>
                          <Style>
                            <Border>
                              <Style>Solid</Style>
                            </Border>
                          </Style>
                        </Line>
                        <Textbox Name="tbPIDandPEOPLEtypeStoreVar">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>="ID# - " &amp; ( Fields!PID.Value ) &amp; "    Type - " &amp; Fields!PEOTYPE.Value</Value>
                                  <Style>
                                    <FontSize>8pt</FontSize>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style />
                            </Paragraph>
                          </Paragraphs>
                          <Top>5.875in</Top>
                          <Left>2in</Left>
                          <Height>0.25in</Height>
                          <Width>5.375in</Width>
                          <ZIndex>18</ZIndex>
                          <Visibility>
                            <Hidden>true</Hidden>
                          </Visibility>
                          <Style>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                        <Textbox Name="ADDR1_ADDR2">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=Fields!ADDR1.Value +"  "+ Fields!ADDR2.Value</Value>
                                  <Style />
                                </TextRun>
                              </TextRuns>
                              <Style />
                            </Paragraph>
                          </Paragraphs>
                          <Top>1.375in</Top>
                          <Left>1.26389in</Left>
                          <Height>0.25in</Height>
                          <Width>3.125in</Width>
                          <ZIndex>19</ZIndex>
                          <Style>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                        <Textbox Name="STREET">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=Fields!STREET.Value</Value>
                                  <Style />
                                </TextRun>
                              </TextRuns>
                              <Style />
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>STREET</rd:DefaultName>
                          <Top>1.625in</Top>
                          <Left>1.26389in</Left>
                          <Height>0.25in</Height>
                          <Width>3.125in</Width>
                          <ZIndex>20</ZIndex>
                          <Style>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                        <Textbox Name="tbCityAndStateAndZip">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=Fields!CITY.Value+"  "+Fields!STATE.Value+"  "+Fields!ZIP.Value</Value>
                                  <Style />
                                </TextRun>
                              </TextRuns>
                              <Style />
                            </Paragraph>
                          </Paragraphs>
                          <Top>1.875in</Top>
                          <Left>1.26389in</Left>
                          <Height>0.25in</Height>
                          <Width>3.125in</Width>
                          <ZIndex>21</ZIndex>
                          <Style>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                        <Textbox Name="STATE_CONGRESSIONAL_DISTRICT">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>="District: " + Fields!STATE.Value + " " + Fields!CDCODE.Value</Value>
                                  <Style />
                                </TextRun>
                              </TextRuns>
                              <Style />
                            </Paragraph>
                          </Paragraphs>
                          <Top>2.25in</Top>
                          <Left>1.26389in</Left>
                          <Height>0.25in</Height>
                          <Width>2.75in</Width>
                          <ZIndex>22</ZIndex>
                          <Visibility>
                            <Hidden>=IIF(Fields!appVersion.Value="NonProfit",True,False)</Hidden>
                          </Visibility>
                          <Style>
                            <Border>
                              <Style>None</Style>
                            </Border>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                        <Textbox Name="COUNTY">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>="County: " + Fields!COUNTY.Value</Value>
                                  <Style />
                                </TextRun>
                              </TextRuns>
                              <Style />
                            </Paragraph>
                          </Paragraphs>
                          <Top>2.5in</Top>
                          <Left>1.26389in</Left>
                          <Height>0.25in</Height>
                          <Width>2.75in</Width>
                          <ZIndex>23</ZIndex>
                          <Style>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                        <Textbox Name="tbOccupationAndEmployer2">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=IIf(IsNothing(Fields!EMPLOYER.Value),"",Fields!EMPLOYER.Value + " - ")</Value>
                                  <Style>
                                    <FontSize>9pt</FontSize>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Left</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <Left>5.50521in</Left>
                          <Height>0.25in</Height>
                          <Width>1.88368in</Width>
                          <ZIndex>24</ZIndex>
                          <Style>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                            <Direction>RTL</Direction>
                          </Style>
                        </Textbox>
                        <Textbox Name="tbAssistant2">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>="Assistant Phone #:  " + Fields!ASTPHN.Value</Value>
                                  <Style />
                                </TextRun>
                              </TextRuns>
                              <Style />
                            </Paragraph>
                          </Paragraphs>
                          <Top>1.55729in</Top>
                          <Left>4.63889in</Left>
                          <Height>0.25in</Height>
                          <Width>2.75in</Width>
                          <ZIndex>25</ZIndex>
                          <Style>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                        <Textbox Name="tbAssistant3">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>="Assistant Email:  " + Fields!ASTEMAIL.Value</Value>
                                  <Style />
                                </TextRun>
                              </TextRuns>
                              <Style />
                            </Paragraph>
                          </Paragraphs>
                          <Top>1.84896in</Top>
                          <Left>4.63889in</Left>
                          <Height>0.25in</Height>
                          <Width>2.75in</Width>
                          <ZIndex>26</ZIndex>
                          <Style>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                        <Textbox Name="tbAssistant4">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>="Direct Phone #:  " + Fields!DIRPHN.Value</Value>
                                  <Style />
                                </TextRun>
                              </TextRuns>
                              <Style />
                            </Paragraph>
                          </Paragraphs>
                          <Top>2.14062in</Top>
                          <Left>4.63889in</Left>
                          <Height>0.25in</Height>
                          <Width>2.75in</Width>
                          <ZIndex>27</ZIndex>
                          <Style>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                        <Subreport Name="Profile_MoneyList">
                          <ReportName>Profile_MoneyList</ReportName>
                          <Parameters>
                            <Parameter Name="PID">
                              <Value>=Fields!PID.Value</Value>
                            </Parameter>
                          </Parameters>
                          <KeepTogether>true</KeepTogether>
                          <Top>3.86632in</Top>
                          <Left>0.01389in</Left>
                          <Height>0.25in</Height>
                          <Width>7.1875in</Width>
                          <ZIndex>28</ZIndex>
                          <Style>
                            <Border>
                              <Style>None</Style>
                            </Border>
                          </Style>
                        </Subreport>
                      </ReportItems>
                      <KeepTogether>true</KeepTogether>
                      <Style />
                    </Rectangle>
                  </CellContents>
                </TablixCell>
              </TablixCells>
            </TablixRow>
          </TablixRows>
        </TablixBody>
        <TablixColumnHierarchy>
          <TablixMembers>
            <TablixMember />
          </TablixMembers>
        </TablixColumnHierarchy>
        <TablixRowHierarchy>
          <TablixMembers>
            <TablixMember>
              <Group Name="list1_PID">
                <GroupExpressions>
                  <GroupExpression>=Fields!PID.Value</GroupExpression>
                </GroupExpressions>
                <PageBreak>
                  <BreakLocation>Between</BreakLocation>
                </PageBreak>
              </Group>
              <SortExpressions>
                <SortExpression>
                  <Value>=Fields!LNAME.Value</Value>
                </SortExpression>
                <SortExpression>
                  <Value>=Fields!FNAME.Value</Value>
                </SortExpression>
              </SortExpressions>
              <DataElementOutput>Output</DataElementOutput>
              <KeepTogether>true</KeepTogether>
            </TablixMember>
          </TablixMembers>
        </TablixRowHierarchy>
        <DataSetName>iGetProfile</DataSetName>
        <PageBreak>
          <BreakLocation>End</BreakLocation>
        </PageBreak>
        <Height>6.125in</Height>
        <Width>7.63889in</Width>
        <Style>
          <FontFamily>Tahoma</FontFamily>
          <FontSize>14pt</FontSize>
          <Color>SlateGray</Color>
        </Style>
      </Tablix>
    </ReportItems>
    <Height>6.125in</Height>
    <Style />
  </Body>
  <Width>7.63889in</Width>
  <Page>
    <PageFooter>
      <Height>0.28125in</Height>
      <PrintOnFirstPage>true</PrintOnFirstPage>
      <PrintOnLastPage>true</PrintOnLastPage>
      <ReportItems>
        <Textbox Name="tbPIDandPEOPLETYPEfooter">
          <CanGrow>true</CanGrow>
          <KeepTogether>true</KeepTogether>
          <Paragraphs>
            <Paragraph>
              <TextRuns>
                <TextRun>
                  <Value>=ReportItems!tbPIDandPEOPLEtypeStoreVar.Value &amp;"  "&amp; DateString</Value>
                  <Style />
                </TextRun>
              </TextRuns>
              <Style />
            </Paragraph>
          </Paragraphs>
          <Height>0.25in</Height>
          <Width>3.625in</Width>
          <Style>
            <PaddingLeft>2pt</PaddingLeft>
            <PaddingRight>2pt</PaddingRight>
            <PaddingTop>2pt</PaddingTop>
            <PaddingBottom>2pt</PaddingBottom>
          </Style>
        </Textbox>
        <Textbox Name="tbCurrentPageOfTotalPages">
          <CanGrow>true</CanGrow>
          <KeepTogether>true</KeepTogether>
          <Paragraphs>
            <Paragraph>
              <TextRuns>
                <TextRun>
                  <Value>="Page " &amp; Globals!PageNumber &amp; " of " &amp; Globals!TotalPages</Value>
                  <Style>
                    <FontSize>8pt</FontSize>
                  </Style>
                </TextRun>
              </TextRuns>
              <Style />
            </Paragraph>
          </Paragraphs>
          <Left>4.5in</Left>
          <Height>0.25in</Height>
          <Width>2.875in</Width>
          <ZIndex>1</ZIndex>
          <Style>
            <PaddingLeft>2pt</PaddingLeft>
            <PaddingRight>2pt</PaddingRight>
            <PaddingTop>2pt</PaddingTop>
            <PaddingBottom>2pt</PaddingBottom>
            <Direction>RTL</Direction>
          </Style>
        </Textbox>
      </ReportItems>
      <Style />
    </PageFooter>
    <TopMargin>0.5in</TopMargin>
    <BottomMargin>0.5in</BottomMargin>
    <Style />
  </Page>
  <AutoRefresh>0</AutoRefresh>
  <DataSources>
    <DataSource Name="DataSource">
      <DataSourceReference>DataSource</DataSourceReference>
      <rd:SecurityType>None</rd:SecurityType>
      <rd:DataSourceID>7f10d137-98e8-429e-a2c6-5b9871cd1bb9</rd:DataSourceID>
    </DataSource>
  </DataSources>
  <DataSets>
    <DataSet Name="iGetProfile">
      <Query>
        <DataSourceName>DataSource</DataSourceName>
        <CommandText>SELECT TOP 20 ACTIVITY.SUBJECT, ACTIVITY.NOTE,P.PID, P.CHKDGT, K.DESCRIP AS PEOTYPE, C.DESCRIP AS PEOCODE, ISNULL(P.PREFIX,'') as PREFIX, ISNULL(P.FNAME,'') as FNAME, ISNULL(P.MNAME,'') as MNAME, ISNULL(P.LNAME,'') as LNAME, ISNULL(P.SUFFIX,'') as SUFFIX, 
 dbo.oFULLNAME(0, P.PREFIX, P.FNAME, P.MNAME, P.LNAME, P.SUFFIX) as NAME, P.OCCUPATION, P.EMPLOYER,
 P.SALUTATION, P.INFSALUT, P.SPOUSENAME, 
dbo.oPHONEwMASK2(H.PHNNO) AS HMPH, dbo.oPHONEwMASK2(B.PHNNO) AS BUSPH, dbo.oPHONEwMASK2(F.PHNNO) AS FAXPH, E.PHNNO AS EMAIL, 
dbo.oPHONEwMASK2(M.PHNNO) AS CELLPH, A.ADDR1, A.ADDR2, A.STREET, ISNULL(A.CITY,'') as CITY, ISNULL(A.STATE,'') as STATE, ISNULL(A.ZIP,'') as ZIP, ISNULL(A.PLUS4,'') as PLUS4,
 dbo.oGetFLAGDESCSTR(P.PID) AS FLAGLIST,dbo.oGetKWRDSTR(P.PID) AS KWRDLIST,P.BIO, P.PICTURE, P.TITLE, P.ASSISTANT, P.TrackNo, A.CDCODE, lkCOUNTY.COUNTY, ssSystem.ClientName
,ISNULL(S.LGIFT,0) as LGIFT,S.LGIFTDTE,ISNULL(S.NOGIFTS,0) AS NOGIFTS,ISNULL(S.CTDAMT,0.00) as CTDAMT
,ISNULL(S.CUMTOT,0.00) AS CUMTOT,ISNULL(S.YTDAMT,0.00) AS YTDAMT
 FROM PEOPLE P with (nolock) left outer join ADDRESS A with (nolock) on P.PID = A.PID AND A.PRIME=1
 left outer join lkCOUNTY with (nolock) on A.COUNTY = lkCOUNTY.COUNTYID
 left outer join lkPEOTYPE K with (nolock) on P.PEOTYPEID = K.PEOTYPEID
 left outer join lkPEOCODE C with (nolock) on P.PEOCODEID = C.PEOCODEID
 left outer join PHONE H with (nolock) on P.PID = H.PID AND H.PHNTYPEID=1 AND H.PRIME=1
 left outer join PHONE B with (nolock) on P.PID = B.PID AND B.PHNTYPEID=2 AND B.PRIME=1
 left outer join PHONE F with (nolock) on P.PID = F.PID AND F.PHNTYPEID=5 AND F.PRIME=1
 left outer join PHONE E with (nolock) on P.PID = E.PID AND E.PHNTYPEID=4 AND E.PRIME=1
 left outer join PHONE M with (nolock) on P.PID = M.PID AND M.PHNTYPEID=3 AND M.PRIME=1
 left outer join SUMMARYD S with (nolock) on P.PID = S.PID
 left outer join (select top 1 ACTIVITY.PID, ACTIVITY.SUBJECT, ACTIVITY.NOTE from ACTIVITY with (nolock) inner join lkacttype with (nolock) on ACTIVITY.ACTTYPEID = lkacttype.ACTTYPEID where ACTIVITY.DONE = 0 and lkacttype.ACTTYPE = 'CL' order by ACTDATE desc) ACTIVITY on P.PID = ACTIVITY.PID
 left outer join (select top 1 ClientName from ssSystem order by DateImplem) ssSystem on 1=1
where P.PICTURE is not null and P.PID=74000006</CommandText>
        <rd:UseGenericDesigner>true</rd:UseGenericDesigner>
      </Query>
      <Fields>
        <Field Name="SUBJECT">
          <DataField>SUBJECT</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="NOTE">
          <DataField>NOTE</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="PID">
          <DataField>PID</DataField>
          <rd:TypeName>System.Int32</rd:TypeName>
        </Field>
        <Field Name="CHKDGT">
          <DataField>CHKDGT</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="PEOTYPE">
          <DataField>PEOTYPE</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="PEOCODE">
          <DataField>PEOCODE</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="PREFIX">
          <DataField>PREFIX</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="FNAME">
          <DataField>FNAME</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="MNAME">
          <DataField>MNAME</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="LNAME">
          <DataField>LNAME</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="SUFFIX">
          <DataField>SUFFIX</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="NAME">
          <DataField>NAME</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="OCCUPATION">
          <DataField>OCCUPATION</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="EMPLOYER">
          <DataField>EMPLOYER</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="SALUTATION">
          <DataField>SALUTATION</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="INFSALUT">
          <DataField>INFSALUT</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="SPOUSENAME">
          <DataField>SPOUSENAME</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="HMPH">
          <DataField>HMPH</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="BUSPH">
          <DataField>BUSPH</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="FAXPH">
          <DataField>FAXPH</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="EMAIL">
          <DataField>EMAIL</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="CELLPH">
          <DataField>CELLPH</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="ADDR1">
          <DataField>ADDR1</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="ADDR2">
          <DataField>ADDR2</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="STREET">
          <DataField>STREET</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="CITY">
          <DataField>CITY</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="STATE">
          <DataField>STATE</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="ZIP">
          <DataField>ZIP</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="PLUS4">
          <DataField>PLUS4</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="FLAGLIST">
          <DataField>FLAGLIST</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="KWRDLIST">
          <DataField>KWRDLIST</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="BIO">
          <DataField>BIO</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="PICTURE">
          <DataField>PICTURE</DataField>
          <rd:TypeName>System.Byte[]</rd:TypeName>
        </Field>
        <Field Name="TITLE">
          <DataField>TITLE</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="ASSISTANT">
          <DataField>ASSISTANT</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="TrackNo">
          <DataField>TrackNo</DataField>
          <rd:TypeName>System.Int32</rd:TypeName>
        </Field>
        <Field Name="CDCODE">
          <DataField>CDCODE</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="COUNTY">
          <DataField>COUNTY</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="ClientName">
          <DataField>ClientName</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="LGIFT">
          <DataField>LGIFT</DataField>
          <rd:TypeName>System.Decimal</rd:TypeName>
        </Field>
        <Field Name="LGIFTDTE">
          <DataField>LGIFTDTE</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="NOGIFTS">
          <DataField>NOGIFTS</DataField>
          <rd:TypeName>System.Int32</rd:TypeName>
        </Field>
        <Field Name="CTDAMT">
          <DataField>CTDAMT</DataField>
          <rd:TypeName>System.Decimal</rd:TypeName>
        </Field>
        <Field Name="CUMTOT">
          <DataField>CUMTOT</DataField>
          <rd:TypeName>System.Decimal</rd:TypeName>
        </Field>
        <Field Name="YTDAMT">
          <DataField>YTDAMT</DataField>
          <rd:TypeName>System.Decimal</rd:TypeName>
        </Field>
        <Field Name="ASTPHN">
          <DataField>ASTPHN</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="ASTEMAIL">
          <DataField>ASTEMAIL</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="DIRPHN">
          <DataField>DIRPHN</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="appVersion">
          <DataField>appVersion</DataField>
          <rd:UserDefined>true</rd:UserDefined>
        </Field>
      </Fields>
    </DataSet>
  </DataSets>
  <Language>en-US</Language>
  <ConsumeContainerWhitespace>true</ConsumeContainerWhitespace>
  <rd:ReportUnitType>Inch</rd:ReportUnitType>
  <rd:ReportID>b273b6bf-bd8b-481f-9f49-ef6634214383</rd:ReportID>
</Report>