﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Linq.Expressions;
using System.IO;
using System.Data.Entity;

using Ninject;
using Ninject.Web.Mvc;

using cmdiapp.n.core.Core;
using cmdiapp.n.core.Core.App_Start;
using System.Data.Common;

namespace cmdiapp.n.core.Areas.crm.Domain.Data
{
    public interface I_entity_crm
    {
        DbContext getContext();

        DbConnection GetConnection();

        IQueryable<TSource> All<TSource>() where TSource : _entityBase_crm, new();

        void Add<TSource>(IEnumerable<TSource> items) where TSource : _entityBase_crm, new();
        void Add<TSource>(TSource item) where TSource : _entityBase_crm, new();

        void CommitChanges();

        void Delete<TSource>(Expression<Func<TSource, bool>> expression) where TSource : _entityBase_crm, new();
        void Delete<TSource>(TSource item) where TSource : _entityBase_crm, new();

        void Dispose();

        TSource Single<TSource>(Expression<Func<TSource, bool>> expression) where TSource : _entityBase_crm, new();
        //void LoadWith<TSource>(Expression<Func<TSource, object>> fieldToLoadWith) where TSource : _entityBase_crm, new();

        void Update<TSource>(TSource item) where TSource : _entityBase_crm, new();

        void Refresh<TSource>(TSource item) where TSource : _entityBase_crm, new();
    }

    public static class I_entityManager_crm
    {
        public static I_entity_crm getEntity()
        {
            return NinjectMVC.kernel.Get<I_entity_crm>();
        }

        public static I_entity_crm getEntity(string connectionString)
        {
            return NinjectMVC.kernel.Get<I_entity_crm>(new Ninject.Parameters.ConstructorArgument("connectionString", connectionString, true));
        }

    }
}