﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.Mvc;
using System.Runtime.Serialization.Json;
using System.Web.Script.Serialization;
using System.IO;

using System.Data;
using System.Dynamic;
using System.Web.Helpers;
using System.Xml;
using System.Xml.Linq;

using AutoMapper;

using cmdiapp.n.core.Core;
using cmdiapp.n.core.Core.App_Start;
using cmdiapp.n.core.Domain.Models;
using cmdiapp.n.core.Domain.ViewModels;
using cmdiapp.n.core.Areas.crm.Core;
using cmdiapp.n.core.Areas.crm.Domain.Data;
using cmdiapp.n.core.Areas.crm.Domain.Models;
using cmdiapp.n.core.Areas.crm.Domain.ViewModels;
using cmdiapp.n.core.Areas.crm.Domain.Repositories;
using cmdiapp.n.core.Areas.crm.Domain.Services;
using cmdiapp.n.core.Library;
using cmdiapp.n.core.Presentation.Controllers;
using cmdiapp.n.core._Presentation.Attributes;

namespace cmdiapp.n.core.Areas.crm.Presentation.Controllers
{
    [Authorize]
    [aElementAuthorize(AccessElement = "cmdiapp.dms.tasks", AccessLevel = "v")]
    public class TaskController : Controller
    {
        private readonly ItaskService _taskService;
        private readonly IflagService _flagService;
        private readonly IkeywordService _keywordService;
        private readonly I_entity_crm _entity;
        private readonly IReportDataService _reportDataService;
        private System.Xml.XmlNode _node;
        System.Collections.Generic.List<string> ids = new System.Collections.Generic.List<string>();
        System.Collections.Generic.List<string> sqls = new System.Collections.Generic.List<string>();
        System.Collections.Generic.List<int> zeros = new System.Collections.Generic.List<int>();
        System.Xml.XmlNodeList xlist;
        System.Data.DataSet dSet;
        //Get User Session Info
        userSession _userSession = session.userSession;
        private I___unitOfWork _unitOfWork;
        private I__DBFactory _dbFactory;
        supportMethods _supportMethods = new supportMethods();

        public TaskController(ItaskService taskService, I_entity_crm entity, IReportDataService ReportDataService,
            IflagService FlagService,
            IkeywordService KeywordService,
            I___unitOfWork unitOfWork, I__DBFactory dbFactory)
        {
            _taskService = taskService;
            _entity = entity;
            _reportDataService = ReportDataService;

            _flagService = FlagService;
            _keywordService = KeywordService;

            //For DB Connection Info
            _unitOfWork = unitOfWork;
            _dbFactory = dbFactory;
        }
                
        public ActionResult MyTasks(string udlo)
        {
            pageInfo _pageInfo = new pageInfo();
            if (udlo == "y")
                _pageInfo.LayoutPage = crmConstants.DEFAULT_LAYOUT_BLANK;
            else
                _pageInfo.LayoutPage = "";

            return View(@"_MyTask", _pageInfo);
        }

        public ActionResult ViewTask(string udlo, int Id)
        {
            pageInfo _pageInfo = new pageInfo();
            if (udlo == "y")
                _pageInfo.LayoutPage = crmConstants.DEFAULT_LAYOUT_BLANK;
            else
                _pageInfo.LayoutPage = "";

            _pageInfo.pageEntityz_uniqueId = Id.ToString();
            return View(@"_MyTaskN", _pageInfo);
        }
        
        public ActionResult TaskC()
        {
            return View(@"_MyTaskC");
        }

        public ActionResult TaskL(string udlo)
        {
            return View(@"_MyTaskL");
        }

        public ActionResult Task(string udlo,int? Id)
        {
            pageInfo _pageInfo = new pageInfo();
            if (udlo == "y")
                _pageInfo.LayoutPage = crmConstants.DEFAULT_LAYOUT_BLANK;
            else
                _pageInfo.LayoutPage = "";

            _pageInfo.pageData1 = "0"; //This is groupType - will be used when we pull ScheduleFor list
            _pageInfo.pageData2 = Guid.NewGuid().ToString();

            if(Id != null)
                _pageInfo.pageData3 = "";//New Window Option
            else
                _pageInfo.pageData3 = "ng_taskSelectCtrl";//Content page option
            //go back now
            return View(@"_Task", _pageInfo);
        }
        
        //This method is used to display My tasks in the List view
//        [HttpGet]
//        public JsonResult GetMyTasks(int? page, int? pageSize)
//        {
//            #region [ Retrieve "Sort" options ]
//            string sortField = HttpContext.Request.QueryString["sort[0][field]"];
//            string sortDir = HttpContext.Request.QueryString["sort[0][dir]"];

//            int _pageSize = (pageSize == null ? 10 : pageSize.Value);
//            int _pageNo = (page == null ? 1 : page.Value);

//            string _sortOptions = "";
//            if (!string.IsNullOrEmpty(sortField))
//                _sortOptions = sortField;
//            if (!string.IsNullOrEmpty(sortField) && !string.IsNullOrEmpty(sortDir))
//                _sortOptions = _sortOptions + " " + sortDir;
//            #endregion

//            List<UserTasks_ext1> _list = new List<UserTasks_ext1>();
//            genericResponse _response;

//            #region [[ Prepare for SQL ]]
//            //Now Prepare for the SQL statement
//            string _sql = String.Format(@"SELECT  ISNULL(A.ACTID,0) ACTID,
//                                     A.ACTDATE AS DUEBY,  
//                                     ISNULL(LEFT(dbo.oFullName(0,'''',U.FNAME,U.MNAME,U.LNAME,''''),60),'''')  AS SCHEDFOR,  
//                                     ISNULL(T.DESCRIP,'''') AS TASKTYPE, 
//                                     ISNULL(A.SUBJECT,'''') [SUBJECT],
//                                     ISNULL(LEFT(CAST(A.NOTE as VARCHAR(4000)),150),'''') as NOTE,  
//                                     ISNULL(P.PID,0) PID,
//                                     ISNULL(LEFT(dbo.oFullName(1,'''',P.FNAME,P.MNAME,P.LNAME,''''),60),'''') AS NAME, 
//                                     ISNULL(AD.CITY,'''') CITY, ISNULL(AD.STATE,'''') [STATE],
//                                     dbo.oGetPhone(P.PID,''HM'') HMPHN,
//                                     dbo.oGetPhone(P.PID,''BS'') BSPHN,
//                                     dbo.oGetPhone(P.PID,''CE'') AS CELL,  
//                                     A.SCHEDON,DATEADD(day,1,A.SCHEDON) NEXTDAY,
//                                     ISNULL(LEFT(dbo.oFullName(0,'''',V.FNAME,V.MNAME,V.LNAME,''''),60),'''')  AS SCHEDBY  
//                             FROM PEOPLE P 
//	                            inner join ACTIVITY A on P.PID = A.PID  
//		                            left outer join lkACTTYPE T on A.ACTTYPEID = T.ACTTYPEID  
//		                            left outer join lkPURPOSE R on A.PURPOSEID = R.PURPOSEID   
//		                            left outer join lkPRIORITY O on A.PRIORITYID = O.PRIORITYID   
//		                            inner join ssUSER U on A.SCHEDFOR = U.UID   
//		                            inner join ssUSER V on A.SCHEDBY = V.UID   
//		                            left outer join ADDRESS AD on P.PID = AD.PID AND AD.PRIME = 1   		
//                            WHERE A.DONE = 0  AND U.USERID = ''{0}''", _userSession.UserName);

//            #endregion

//            //Coming here when SearchText is null
//            if (string.IsNullOrEmpty(_sortOptions)) _sortOptions = "DUEBY";
//            _list = _taskService.get_MyTasks(_sql, _sortOptions, _pageSize, _pageNo);

//            Mapper.CreateMap<UserTasks_ext1, rUserTasks>();
//            IEnumerable<iItemType> results = _list.Select(a => Mapper.Map<UserTasks_ext1, rUserTasks>(a)).ToList();
//            if (results.Count() > 0)
//            {
//                _response = new genericResponse() { success = true, __count = _list.FirstOrDefault().count_, results = results.ToList() };
//            }
//            else
//            {
//                _response = new genericResponse() { success = true, __count = 0 };
//            }

//            return Json(_response, JsonRequestBehavior.AllowGet);

//        }
        
        //This method is used for My Tasks Calendar View
        
//        [HttpGet]
//        public JsonResult GetAllMyTasks()
//        {
//            genericResponse _response;
//            List<UserTasks> _list = new List<UserTasks>();

//            #region [[ Prepare for SQL ]]
//            //Now Prepare for the SQL statement
//            string _sql = String.Format(@"SELECT  ISNULL(A.ACTID,0) ACTID,
//                                     A.ACTDATE AS DUEBY,  
//                                     ISNULL(LEFT(dbo.oFullName(0,'''',U.FNAME,U.MNAME,U.LNAME,''''),60),'''')  AS SCHEDFOR,  
//                                     ISNULL(T.DESCRIP,'''') AS TASKTYPE, 
//                                     ISNULL(A.SUBJECT,'''') [SUBJECT],
//                                     ISNULL(LEFT(CAST(A.NOTE as VARCHAR(4000)),150),'''') as NOTE,  
//                                     ISNULL(P.PID,0) PID,
//                                     ISNULL(LEFT(dbo.oFullName(1,'''',P.FNAME,P.MNAME,P.LNAME,''''),60),'''') AS NAME, 
//                                     ISNULL(AD.CITY,'''') CITY, ISNULL(AD.STATE,'''') [STATE],
//                                     dbo.oGetPhone(P.PID,''HM'') HMPHN,
//                                     dbo.oGetPhone(P.PID,''BS'') BSPHN,
//                                     dbo.oGetPhone(P.PID,''CE'') AS CELL,  
//                                     A.SCHEDON, DATEADD(day,1,A.ACTDATE) NEXTDAY,
//                                     ISNULL(LEFT(dbo.oFullName(0,'''',V.FNAME,V.MNAME,V.LNAME,''''),60),'''')  AS SCHEDBY  
//                             FROM PEOPLE P 
//	                            inner join ACTIVITY A on P.PID = A.PID  
//		                            left outer join lkACTTYPE T on A.ACTTYPEID = T.ACTTYPEID  
//		                            left outer join lkPURPOSE R on A.PURPOSEID = R.PURPOSEID   
//		                            left outer join lkPRIORITY O on A.PRIORITYID = O.PRIORITYID   
//		                            inner join ssUSER U on A.SCHEDFOR = U.UID   
//		                            inner join ssUSER V on A.SCHEDBY = V.UID   
//		                            left outer join ADDRESS AD on P.PID = AD.PID AND AD.PRIME = 1   		
//                            WHERE A.DONE = 0  AND U.USERID = ''{0}'' ORDER BY DUEBY", _userSession.UserName);

//            #endregion

//            _list = _taskService.get_allMyTasks(_sql);
            
//            Mapper.CreateMap<UserTasks, rUserTasks>();
//            IEnumerable<iItemType> results = _list.Select(a => Mapper.Map<UserTasks, rUserTasks>(a)).ToList();
//            if (results.Count() > 0)
//            {
//                _response = new genericResponse() { success = true, __count = results.Count(), results = results.ToList() };
//            }
//            else
//            {
//                _response = new genericResponse() { success = true, __count = 0, results = null };
//            }

//            return Json(_response, JsonRequestBehavior.AllowGet);

//        }
             
        //This method is used to Export My Tasks
        
//        public ActionResult ExportMyTasks()
//        {
//            string sToday = String.Format("{0:yyyyMMMdd__hh_mm_ss tt}", DateTime.Now);
//            string moduleName = "MyTasks";

//            #region [[ Prepare SQL Statement ]]
//            string _sql = String.Format(@"SELECT  ISNULL(A.ACTID,0) ACTID,
//                                     A.ACTDATE AS DUEBY,  
//                                     ISNULL(LEFT(dbo.oFullName(0,'',U.FNAME,U.MNAME,U.LNAME,''),60),'')  AS SCHEDFOR,  
//                                     ISNULL(T.DESCRIP,'') AS TASKTYPE, 
//                                     ISNULL(A.SUBJECT,'') [SUBJECT],
//                                     ISNULL(LEFT(CAST(A.NOTE as VARCHAR(4000)),150),'') as NOTE,  
//                                     ISNULL(P.PID,0) PID,
//                                     ISNULL(LEFT(dbo.oFullName(1,'',P.FNAME,P.MNAME,P.LNAME,''),60),'') AS NAME, 
//                                     ISNULL(AD.CITY,'') CITY, ISNULL(AD.STATE,'') [STATE],
//                                     dbo.oGetPhone(P.PID,'HM') HMPHN,
//                                     dbo.oGetPhone(P.PID,'BS') BSPHN,
//                                     dbo.oGetPhone(P.PID,'CE') AS CELL,  
//                                     A.SCHEDON, DATEADD(day,1,A.ACTDATE) NEXTDAY,
//                                     ISNULL(LEFT(dbo.oFullName(0,'',V.FNAME,V.MNAME,V.LNAME,''),60),'')  AS SCHEDBY  
//                             FROM PEOPLE P 
//	                            inner join ACTIVITY A on P.PID = A.PID  
//		                            left outer join lkACTTYPE T on A.ACTTYPEID = T.ACTTYPEID  
//		                            left outer join lkPURPOSE R on A.PURPOSEID = R.PURPOSEID   
//		                            left outer join lkPRIORITY O on A.PRIORITYID = O.PRIORITYID   
//		                            inner join ssUSER U on A.SCHEDFOR = U.UID   
//		                            inner join ssUSER V on A.SCHEDBY = V.UID   
//		                            left outer join ADDRESS AD on P.PID = AD.PID AND AD.PRIME = 1   		
//                            WHERE A.DONE = 0  AND U.USERID = '{0}' ORDER BY DUEBY", _userSession.UserName);
//            #endregion
            
//            return new ExcelResult
//            {
//                fileName = moduleName + "_" + System.Guid.NewGuid() + "_" + _userSession.UserName + "_" + sToday + ".xlsx",
//                filePath = "~/Downloads/",
//                sqlStatement = _sql,
//                sheetName = moduleName,
//                connectionSring = _dbFactory.getContext().Database.Connection.ConnectionString,
//                clientsidefileName = moduleName + "_" + _userSession.UserName + "_" + sToday + ".xlsx"
//            };

//        }

        //This method is used to Export Tasks
        //public ActionResult ExportTasks()
        //{
        //    string sToday = String.Format("{0:yyyyMMMdd__hh_mm_ss tt}", DateTime.Now);
        //    string moduleName = "Tasks";
        //    string _sql = "";

        //    if (Session["TaskSearchQuery_" + _userSession.UserId] != null)
        //    {
        //        _sql = Session["TaskSearchQuery_" + _userSession.UserId].ToString();
        //    }

        //    return new ExcelResult
        //    {
        //        fileName = moduleName + "_" + System.Guid.NewGuid() + "_" + _userSession.UserName + "_" + sToday + ".xlsx",
        //        filePath = "~/Downloads/",
        //        sqlStatement = _sql,
        //        sheetName = moduleName,
        //        connectionSring = _dbFactory.getContext().Database.Connection.ConnectionString,
        //        clientsidefileName = moduleName + "_" + _userSession.UserName + "_" + sToday + ".xlsx"
        //    };
        //}

        //Used for Reassign Task
        
        //[HttpPost]
        //public JsonResult ReassignTask(int actId, int uid)
        //{
        //    try
        //    {
        //        string _sql  = "UPDATE ACTIVITY SET SCHEDFOR = " + uid.ToString() + " WHERE ACTID = " + actId.ToString();
                
        //        _sql += " select 1 as '1'";

        //        int msg = _taskService.reassign_TaskItems(_sql);

        //        if (msg == 1)
        //        {
        //            //Sucess
        //            return Json(new { success = true });
        //        }
        //        else
        //        {
        //            //Can not assign 
        //            return Json(new { success = false, message = "The assign task operation is not successful. Please try again later." });
        //        }
        //    }
        //    catch
        //    {
        //        //Can not assign 
        //        return Json(new { success = false, message = "The assign task operation is not successful. Please try again later." });
        //    }
        //}

        #region [[ Print Summary : Task and MyTask Module : By Bhavesh ]]

        #region [[ Task Module ]]
//        [HttpPost]
//        public ActionResult PrintSummary(TaskSearch _taskSearch)
//        {
//            try
//            {
//                if (_taskSearch != null)
//                {
//                    //Create Unique GUID Here
//                    string p_key = System.Guid.NewGuid().ToString();

//                    //let us check for the Inputs from the user - First for Schedule By Users
//                    #region [[ Get UidBy and UidFor from the User Input ]]
//                    var uidBy = "";
//                    foreach (ScheduleBy cb in _taskSearch.scheduleByUsers)
//                        if (cb.IsSelected == true)
//                            uidBy += (uidBy == "" ? cb.uid.ToString() : "," + cb.uid.ToString());

//                    //Now for Schedule For Users
//                    var uidFor = "";
//                    foreach (ScheduleFor cb in _taskSearch.scheduleForUsers)
//                        if (cb.IsSelected == true)
//                            uidFor += (uidFor == "" ? cb.uid.ToString() : "," + cb.uid.ToString());

//                    #endregion

//                    #region [[ Create SQL Statement ]]
//                    //For Type of the Task
//                    string doneType = "";
//                    if (_taskSearch.IsIncludeAllOpenTasks)
//                        doneType = "0";
//                    if (_taskSearch.IsIncludeAllCompTasks)
//                        doneType += (doneType == "" ? "1" : ",1");

//                    string dteField = "ACTDATE";
//                    if (!_taskSearch.IsDueDate)
//                        dteField = "DONEON";

//                    string _sql = @"SELECT A.ACTID, A.ACTDATE AS DUEBY, 
//                            (SELECT LEFT(dbo.oFullName(0,'''',S.FNAME,S.MNAME,S.LNAME,''''),60) FROM ssUSER S WHERE UID = A.SCHEDFOR) AS SCHEDFOR, 
//                            T.DESCRIP AS TASKTYPE, A.SUBJECT, CAST(A.NOTE AS VARCHAR(MAX)) AS NOTE, P.PID, LEFT(dbo.oFullName(0,P.PREFIX,P.FNAME,P.MNAME,P.LNAME,P.SUFFIX),60) AS NAME, 
//                            ISNULL(P.PREFIX,'''')PREFIX,ISNULL(P.FNAME,'''')FNAME,ISNULL(P.MNAME,'''') MNAME,ISNULL(P.LNAME,'''')LNAME,ISNULL(P.SUFFIX,'''') SUFFIX,
//                            ISNULL(AD.STREET,'''') STREET,ISNULL(AD.ADDR1,'''') ADDR1,ISNULL(AD.ADDR2,'''') ADDR2,
//                            ISNULL(AD.CITY,'''') CITY, ISNULL(AD.STATE,'''') [STATE],ISNULL(AD.ZIP,'''') ZIP,
//                            HP.PHNNO AS HMPHN, BP.PHNNO AS BSPHN, CP.PHNNO AS CELL, 
//                            A.SCHEDON, (SELECT LEFT(dbo.oFullName(0,'''',S.FNAME,S.MNAME,S.LNAME,''''),60) FROM ssUSER S WHERE UID = A.SCHEDBY) AS SCHEDBY, 
//                            CAST(a.DONE as BIT) as DONE, 
//                            -- CONVERT(VARCHAR(10), A.DONEON, 101) as DONEON 
//                            A.DONEON FROM ACTIVITY A INNER JOIN (PEOPLE P 
//                                            LEFT OUTER JOIN ADDRESS AD ON P.PID = AD.PID AND AD.PRIME = 1 
//                                            LEFT OUTER JOIN PHONE HP ON P.PID = HP.PID AND HP.PHNTYPEID = 1 AND HP.PRIME=1
//                                            LEFT OUTER JOIN PHONE BP ON P.PID = BP.PID AND BP.PHNTYPEID = 2 AND BP.PRIME=1
//                                            LEFT OUTER JOIN PHONE CP ON P.PID = CP.PID AND CP.PHNTYPEID = 3 AND CP.PRIME=1) 
//                                        ON A.PID = P.PID 
//                                            LEFT OUTER JOIN lkACTTYPE T ON A.ACTTYPEID = T.ACTTYPEID 
//                                            LEFT OUTER JOIN lkPURPOSE R ON A.PURPOSEID = R.PURPOSEID 
//                                            LEFT OUTER JOIN lkPRIORITY O ON A.PRIORITYID = O.PRIORITYID 
//                                            INNER JOIN ssUSER U ON A.SCHEDFOR = U.UID 
//                                            INNER JOIN ssUSER V ON A.SCHEDBY = V.UID " +
//                                    "WHERE U.UID IN (" + uidFor.ToString() + ") AND V.UID IN (" + uidBy.ToString() + ") AND " +
//                                "A.DONE IN (" + doneType.ToString() + ")  " +
//                                ((_taskSearch.groupType == "1") ? " AND T.ACTTYPE LIKE 'SC' " : " ");

//                    if (_taskSearch.DUEDTEFR != null && _taskSearch.DUEDTETO != null && _taskSearch.DUEDTEFR > Convert.ToDateTime("12/31/1970") && _taskSearch.DUEDTETO > Convert.ToDateTime("12/31/1970"))
//                        _sql += " AND A." + dteField.ToString() + " BETWEEN ''" + String.Format("{0:MM/dd/yyyy HH:mm:ss}", _taskSearch.DUEDTEFR) + "'' AND ''" +
//                                    String.Format("{0:MM/dd/yyyy HH:mm:ss}", _taskSearch.DUEDTETO) + "'' ";

//                    if (_taskSearch.COMPDTEFR != null && _taskSearch.COMPDTETO != null && _taskSearch.COMPDTEFR > Convert.ToDateTime("12/31/1970") && _taskSearch.COMPDTETO > Convert.ToDateTime("12/31/1970"))
//                        _sql += " AND A." + dteField.ToString() + " BETWEEN ''" + String.Format("{0:MM/dd/yyyy HH:mm:ss}", _taskSearch.COMPDTEFR) + "'' AND ''" +
//                                    String.Format("{0:MM/dd/yyyy HH:mm:ss}", _taskSearch.COMPDTETO) + "'' ";

//                    string _tmp_srchSQL = _sql.Replace("HP.PHNNO", "dbo.oPHONEwMASK(HP.PHNNO)").Replace("BP.PHNNO", "dbo.oPHONEwMASK(BP.PHNNO)").Replace("CP.PHNNO", "dbo.oPHONEwMASK(CP.PHNNO)");
//                    #endregion

//                    #region [[ For XML + Collection ]]
//                    string _xml = _supportMethods.get_report_data_sourceDef_in_xml_summary(_tmp_srchSQL);
//                    //Create collection
//                    System.Xml.XmlDocument doc = new System.Xml.XmlDocument();
//                    doc.LoadXml(_xml);
//                    _node = doc.DocumentElement.SelectSingleNode("/ReportParameters");

//                    System.Collections.Specialized.NameValueCollection _collection = new System.Collections.Specialized.NameValueCollection();
//                    _collection.Add("cc", _node.Attributes["clientcode"].Value);
//                    _collection.Add("rn", _node.Attributes["reportcode"].Value);
//                    _collection.Add("XML", _node.OuterXml);
//                    //Save the Report Parameter collection in the Session
//                    System.Web.HttpContext.Current.Session.Add(p_key + "_c", _collection);
//                    #endregion

//                    #region [[ For DataSet ]]
//                    DataSet _psDataset = _reportDataService.get_dataset_w_sql__single(_tmp_srchSQL, "iGetTasks");
//                    //First Data Set 
//                    System.Web.HttpContext.Current.Session.Add(p_key + "_fd", _psDataset);
//                    //For Report Name
//                    System.Web.HttpContext.Current.Session.Add(p_key + "_rName", "PrintTaskSummary");
//                    #endregion

//                    if (_psDataset.Tables[0].Rows.Count > 0)
//                    {
//                        return Json(new { success = true, key = p_key });
//                    }
//                    else
//                    {
//                        return Json(new { success = false, message = "There are no data to create Print Summary Report. Please change your selection." });
//                    }

//                }
//                else
//                {
//                    return Json(new { success = false, message = "There is some issue in processing Print Summary Report request. Please try again later." });
//                }
//            }
//            catch (Exception ex)
//            {
//                return Json(new { success = false, message = "There is some issue in processing Print Summary Report request. Please try again later." });
//            }

//        }

        public ActionResult ShowPrintSummary(string p_key)
        {
            try
            {

                if (HttpRuntime.Cache[p_key + "_c"] != null && HttpRuntime.Cache[p_key + "_fd"] != null)
                //if (Session[p_key + "_c"] != null && Session[p_key + "_fd"] != null)
                {

                    Session[p_key + "_c"] = HttpRuntime.Cache[p_key + "_c"];
                    Session[p_key + "_fd"] = HttpRuntime.Cache[p_key + "_fd"];
                    //Let us go to the Response Analysis Report Page - Note only one .. is required
                    return Redirect("../Report/ReportPage.aspx?p_key=" + p_key);
                }
                else
                {
                    return Json(new { success = false, JsonRequestBehavior.AllowGet });
                }
            }
            catch
            {
                return Json(new { success = false, JsonRequestBehavior.AllowGet });
            }

        }
        #endregion

            #region [[ For MyTask Module ]]
        [NotLaunchpadAuthorize]
        public ActionResult ShowMyTaskPrintSummary()
        {
            try
            {
                //Create Unique GUID Here
                string p_key = System.Guid.NewGuid().ToString();

                #region [[ Prepare for SQL ]]
                //Now Prepare for the SQL statement
                string _sql = @"SELECT A.ACTID, A.ACTDATE AS DUEBY, 
                            (SELECT LEFT(dbo.oFullName(0,'',S.FNAME,S.MNAME,S.LNAME,''),60) FROM ssUSER S WHERE UID = A.SCHEDFOR) AS SCHEDFOR, 
                            T.DESCRIP AS TASKTYPE, A.SUBJECT, CAST(A.NOTE AS VARCHAR(MAX)) AS NOTE, P.PID, LEFT(dbo.oFullName(0,P.PREFIX,P.FNAME,P.MNAME,P.LNAME,P.SUFFIX),60) AS NAME, 
                            ISNULL(P.PREFIX,'')PREFIX,ISNULL(P.FNAME,'')FNAME,ISNULL(P.MNAME,'') MNAME,ISNULL(P.LNAME,'')LNAME,ISNULL(P.SUFFIX,'') SUFFIX,
                            ISNULL(AD.STREET,'') STREET,ISNULL(AD.ADDR1,'') ADDR1,ISNULL(AD.ADDR2,'') ADDR2,
                            ISNULL(AD.CITY,'') CITY, ISNULL(AD.STATE,'') [STATE],ISNULL(AD.ZIP,'') ZIP,
                            HP.PHNNO AS HMPHN, BP.PHNNO AS BSPHN, CP.PHNNO AS CELL, 
                            A.SCHEDON, (SELECT LEFT(dbo.oFullName(0,'',S.FNAME,S.MNAME,S.LNAME,''),60) FROM ssUSER S WHERE UID = A.SCHEDBY) AS SCHEDBY, 
                            CAST(a.DONE as BIT) as DONE, 
                            -- CONVERT(VARCHAR(10), A.DONEON, 101) as DONEON 
                            A.DONEON FROM ACTIVITY A INNER JOIN (PEOPLE P 
                                            LEFT OUTER JOIN ADDRESS AD ON P.PID = AD.PID AND AD.PRIME = 1 
                                            LEFT OUTER JOIN PHONE HP ON P.PID = HP.PID AND HP.PHNTYPEID = 1 AND HP.PRIME=1
                                            LEFT OUTER JOIN PHONE BP ON P.PID = BP.PID AND BP.PHNTYPEID = 2 AND BP.PRIME=1
                                            LEFT OUTER JOIN PHONE CP ON P.PID = CP.PID AND CP.PHNTYPEID = 3 AND CP.PRIME=1) 
                                        ON A.PID = P.PID 
                                            LEFT OUTER JOIN lkACTTYPE T ON A.ACTTYPEID = T.ACTTYPEID 
                                            LEFT OUTER JOIN lkPURPOSE R ON A.PURPOSEID = R.PURPOSEID 
                                            LEFT OUTER JOIN lkPRIORITY O ON A.PRIORITYID = O.PRIORITYID 
                                            INNER JOIN ssUSER U ON A.SCHEDFOR = U.UID 
                                            INNER JOIN ssUSER V ON A.SCHEDBY = V.UID " +
                                   "WHERE A.DONE = 0 AND U.USERID IN (" + "'" + _userSession.UserName.ToString() + "'" + ")";

                string _tmp_srchSQL = _sql.Replace("HP.PHNNO", "dbo.oPHONEwMASK(HP.PHNNO)").Replace("BP.PHNNO", "dbo.oPHONEwMASK(BP.PHNNO)").Replace("CP.PHNNO", "dbo.oPHONEwMASK(CP.PHNNO)");
                #endregion

                #region [[ For XML + Collection ]]
                string _xml = _supportMethods.get_report_data_sourceDef_in_xml_summary(_tmp_srchSQL);
                //Create collection
                System.Xml.XmlDocument doc = new System.Xml.XmlDocument();
                doc.LoadXml(_xml);
                _node = doc.DocumentElement.SelectSingleNode("/ReportParameters");

                System.Collections.Specialized.NameValueCollection _collection = new System.Collections.Specialized.NameValueCollection();
                _collection.Add("cc", _node.Attributes["clientcode"].Value);
                _collection.Add("rn", _node.Attributes["reportcode"].Value);
                _collection.Add("XML", _node.OuterXml);
                //Save the Report Parameter collection in the Session
                System.Web.HttpContext.Current.Session.Add(p_key + "_c", _collection);
                #endregion

                #region [[ For DataSet ]]
                DataSet _psDataset = _reportDataService.get_dataset_w_sql__single(_tmp_srchSQL, "iGetTasks");
                //First Data Set 
                System.Web.HttpContext.Current.Session.Add(p_key + "_fd", _psDataset);
                //For Report Name
                System.Web.HttpContext.Current.Session.Add(p_key + "_rName", "PrintTaskSummary");
                #endregion
                
                if (Session[p_key + "_c"] != null && Session[p_key + "_fd"] != null)
                {
                    //Let us go to the Print Summary Report
                    return Redirect("../Report/ReportPage.aspx?p_key=" + p_key);
                }
                else
                {
                    return Json(new { success = false, JsonRequestBehavior.AllowGet });
                }
            }
            catch
            {
                return Json(new { success = false, JsonRequestBehavior.AllowGet });
            }
        }
        #endregion

        #endregion


        public string GetListFromArray(string[] array, string whichEntity)
        {
            string list = "";
            if (array != null && array.Count() > 0)
            {
                for (int i = 0; i <= array.Count() - 1; i++)
                {
                    if (string.IsNullOrEmpty(list))
                    {
                        if (whichEntity == "flag")
                            list = _flagService.getFlagID(array[i].ToString()).ToString();
                        else if (whichEntity == "kwrd")
                            list = _keywordService.getKwrdID(array[i].ToString()).ToString();

                    }
                    else
                    {
                        if (whichEntity == "flag")
                            list = list + "," + _flagService.getFlagID(array[i].ToString()).ToString();
                        else if (whichEntity == "kwrd")
                            list = list + "," + _keywordService.getKwrdID(array[i].ToString()).ToString();

                    }
                }
            }

            if (!string.IsNullOrEmpty(list))
                list = "(" + list + ")";

            return list;
        }
        
        #region [[ Print Call Sheets : Task and MyTask Module : By Bhavesh ]]

        #region [[ Task Module ]]
        [HttpPost]        
        public ActionResult PrintCallSheets(TaskSearch _taskSearch)
        {
            try
            {
                if (_taskSearch != null)
                {
                    //Create Unique GUID Here
                    string p_key = System.Guid.NewGuid().ToString();

                    //let us check for the Inputs from the user - First for Schedule By Users
                    #region [[ Get UidBy and UidFor from the User Input ]]
                    var uidBy = "";
                    foreach (ScheduleBy cb in _taskSearch.scheduleByUsers)
                        if (cb.IsSelected == true)
                            uidBy += (uidBy == "" ? cb.uid.ToString() : "," + cb.uid.ToString());

                    //Now for Schedule For Users
                    var uidFor = "";
                    foreach (ScheduleFor cb in _taskSearch.scheduleForUsers)
                        if (cb.IsSelected == true)
                            uidFor += (uidFor == "" ? cb.uid.ToString() : "," + cb.uid.ToString());

                    #endregion

                    #region [[ Create SQL Statement ]]
                    //For Type of the Task
                    string doneType = "";
                    if (_taskSearch.IsIncludeAllOpenTasks)
                        doneType = "0";
                    if (_taskSearch.IsIncludeAllCompTasks)
                        doneType += (doneType == "" ? "1" : ",1");

                    string dteField = "ACTDATE";
                    if (!_taskSearch.IsDueDate)
                        dteField = "DONEON";

                    //let us check for additional filters...if user have selected them
                    string flaglist = GetListFromArray(_taskSearch.SELFLAGS, "flag");
                    string kwrdlist = GetListFromArray(_taskSearch.SELKWRDS, "kwrd");

                    string _sql = @"SELECT A.ACTID, A.ACTDATE AS DUEBY, 
                            (SELECT LEFT(dbo.oFullName(0,'',S.FNAME,S.MNAME,S.LNAME,''),60) FROM ssUSER S WHERE UID = A.SCHEDFOR) AS SCHEDFOR, 
                            T.DESCRIP AS TASKTYPE, A.SUBJECT, CAST(A.NOTE AS VARCHAR(MAX)) AS NOTE, P.PID, LEFT(dbo.oFullName(0,P.PREFIX,P.FNAME,P.MNAME,P.LNAME,P.SUFFIX),60) AS NAME, 
                            ISNULL(P.PREFIX,'')PREFIX,ISNULL(P.FNAME,'')FNAME,ISNULL(P.MNAME,'') MNAME,ISNULL(P.LNAME,'')LNAME,ISNULL(P.SUFFIX,'') SUFFIX,
                            ISNULL(AD.STREET,'') STREET,ISNULL(AD.ADDR1,'') ADDR1,ISNULL(AD.ADDR2,'') ADDR2,
                            ISNULL(AD.CITY,'') CITY, ISNULL(AD.STATE,'') [STATE],ISNULL(AD.ZIP,'') ZIP,
                            HP.PHNNO AS HMPHN, BP.PHNNO AS BSPHN, CP.PHNNO AS CELL, 
                            A.SCHEDON, (SELECT LEFT(dbo.oFullName(0,'',S.FNAME,S.MNAME,S.LNAME,''),60) FROM ssUSER S WHERE UID = A.SCHEDBY) AS SCHEDBY, 
                            CAST(a.DONE as BIT) as DONE, 
                            -- CONVERT(VARCHAR(10), A.DONEON, 101) as DONEON 
                            A.DONEON FROM ACTIVITY A INNER JOIN (PEOPLE P 
                                            LEFT OUTER JOIN ADDRESS AD ON P.PID = AD.PID AND AD.PRIME = 1 
                                            LEFT OUTER JOIN PHONE HP ON P.PID = HP.PID AND HP.PHNTYPEID = 1 AND HP.PRIME=1
                                            LEFT OUTER JOIN PHONE BP ON P.PID = BP.PID AND BP.PHNTYPEID = 2 AND BP.PRIME=1
                                            LEFT OUTER JOIN PHONE CP ON P.PID = CP.PID AND CP.PHNTYPEID = 3 AND CP.PRIME=1) 
                                        ON A.PID = P.PID 
                                            LEFT OUTER JOIN lkACTTYPE T ON A.ACTTYPEID = T.ACTTYPEID 
                                            LEFT OUTER JOIN lkPURPOSE R ON A.PURPOSEID = R.PURPOSEID 
                                            LEFT OUTER JOIN lkPRIORITY O ON A.PRIORITYID = O.PRIORITYID
                                            LEFT OUTER JOIN jtFLAG JF ON P.PID=JF.PID LEFT OUTER JOIN dmFLAG DF ON JF.FLAGID = DF.FLAGID
                                            LEFT OUTER JOIN jtKWRD JK ON P.PID=JK.PID LEFT OUTER JOIN dmKWRD DK ON JK.KWRDID = DK.KWRDID 
                                            INNER JOIN ssUSER U ON A.SCHEDFOR = U.UID 
                                            INNER JOIN ssUSER V ON A.SCHEDBY = V.UID " +
                                    "WHERE U.UID IN (" + uidFor.ToString() + ") AND V.UID IN (" + uidBy.ToString() + ") AND " +
                                "A.DONE IN (" + doneType.ToString() + ")  " +
                                ((_taskSearch.groupType == "1") ? " AND T.ACTTYPE LIKE 'SC' " : " ");

                    if (_taskSearch.DUEDTEFR != null && _taskSearch.DUEDTETO != null && _taskSearch.DUEDTEFR > Convert.ToDateTime("12/31/1970") && _taskSearch.DUEDTETO > Convert.ToDateTime("12/31/1970"))
                        _sql += " AND A." + dteField.ToString() + " BETWEEN '" + String.Format("{0:MM/dd/yyyy HH:mm:ss}", _taskSearch.DUEDTEFR) + "' AND '" +
                                    String.Format("{0:MM/dd/yyyy HH:mm:ss}", _taskSearch.DUEDTETO) + "' ";

                    if (_taskSearch.COMPDTEFR != null && _taskSearch.COMPDTETO != null && _taskSearch.COMPDTEFR > Convert.ToDateTime("12/31/1970") && _taskSearch.COMPDTETO > Convert.ToDateTime("12/31/1970"))
                        _sql += " AND A." + dteField.ToString() + " BETWEEN '" + String.Format("{0:MM/dd/yyyy HH:mm:ss}", _taskSearch.COMPDTEFR) + "' AND '" +
                                    String.Format("{0:MM/dd/yyyy HH:mm:ss}", _taskSearch.COMPDTETO) + "' ";

                    #region [[ Extra filters added in Feb 2015 ]]
                    //And Condition for FLAG selection....
                    if (!string.IsNullOrEmpty(flaglist))
                    {
                        _sql += " AND [DF].FLAGID in " + flaglist;
                    }
                    //For kwrd Selection...
                    if (!string.IsNullOrEmpty(kwrdlist))
                    {
                        _sql += " AND [DK].KWRDID in " + kwrdlist;
                    }
                    //For State Entry...
                    if (!string.IsNullOrEmpty(_taskSearch.STATENAME))
                    {
                        _sql += " AND [AD].STATE in ('" + _taskSearch.STATENAME + "')";
                    }
                    #endregion

                    // sql to pull PID's
                    _sql = "SELECT DISTINCT P.PID " + _sql.Substring(_sql.IndexOf("FROM ACTIVITY A INNER JOIN "));
                    #endregion

                    #region [[ Create XML Statement ]]
                    string _xml = "";
                    if (_taskSearch.pVersion == "Profile_full")
                        _xml = _supportMethods.get_report_data_sourceDef_in_xml_Full(_sql, "");
                    else if (_taskSearch.pVersion == "Profile_brief")
                        _xml = _supportMethods.get_report_data_sourceDef_in_xml_Brief(_sql,true);
                    else if (_taskSearch.pVersion == "Profile_pocket")
                        _xml = _supportMethods.get_report_data_sourceDef_in_xml_Pocket(_sql);
                    else if (_taskSearch.pVersion == "Profile_extended")
                        _xml = _supportMethods.get_report_data_sourceDef_in_xml_Extended(_sql);
                    else if (_taskSearch.pVersion == "Profile_PAC")
                        _xml = _supportMethods.get_report_data_sourceDef_in_xml_PAC(_sql);
                    else if (_taskSearch.pVersion == "Profile_PAC_ext")
                        _xml = _supportMethods.get_report_data_sourceDef_in_xml_PAC_ext(_sql);
                    #endregion

                    #region [[ For Collection ]]
                    //Create collection
                    System.Xml.XmlDocument doc = new System.Xml.XmlDocument();
                    doc.LoadXml(_xml);
                    _node = doc.DocumentElement.SelectSingleNode("/ReportParameters");

                    System.Collections.Specialized.NameValueCollection _collection = new System.Collections.Specialized.NameValueCollection();
                    _collection.Add("cc", _node.Attributes["clientcode"].Value);
                    _collection.Add("rn", _node.Attributes["reportcode"].Value);
                    _collection.Add("XML", _node.OuterXml);
                    //Save the Report Parameter collection in the Session
                    System.Web.HttpContext.Current.Session.Add(p_key + "_c", _collection);
                    #endregion

                    #region [[ For Queries DataSet - First Dataset ]]
                    //Get Queries Dataset
                    xlist = _node.SelectNodes("Queries/Query"); 

                    foreach (System.Xml.XmlNode node in xlist)
                    {
                        ids.Add(node.Attributes["id"].Value);
                        sqls.Add(node.InnerText);
                        zeros.Add(0);
                    }
                    dSet = _reportDataService.get_dataset_w_sql__multi(sqls, ids, zeros, zeros);
                    //For Resizing the Picture...
                    dSet = _supportMethods.ReSizePictureinDataSet(dSet);
                    //First Dataset
                    System.Web.HttpContext.Current.Session.Add(p_key + "_fd", dSet);
                    #endregion

                    #region [[ For SubQueries Dataset ]]
                    //First set all for null and then Get SubQueries DataSet
                    xlist = null;
                    dSet = null;
                    xlist = _node.SelectNodes("Queries/SubQuery");
                    ids.Clear();
                    sqls.Clear();
                    zeros.Clear();
                    foreach (System.Xml.XmlNode node in xlist)
                    {
                        ids.Add(node.Attributes["id"].Value);
                        sqls.Add(node.InnerText);
                        zeros.Add(0);
                    }
                    dSet = _reportDataService.get_dataset_w_sql__multi(sqls, ids, zeros, zeros);
                    //Second Dataset
                    System.Web.HttpContext.Current.Session.Add(p_key + "_sd", dSet);
                    #endregion
                    //return the Key
                    return Json(new { success = true, key = p_key });

                }
                else
                {
                    return Json(new { success = false, message = "There is some issue in processing Print Call Sheets Report request. Please try again later." });
                }
            }
            catch (Exception ex)
            {
                return Json(new { success = false, message = "There is some issue in processing Print Call Sheets Report request. Please try again later." });
            }

        }

        [NotLaunchpadAuthorize]
        public ActionResult ShowPrintCallSheets(string p_key)
        {
            try
            {
                if (Session[p_key + "_c"] != null && Session[p_key + "_fd"] != null && Session[p_key + "_sd"] != null)
                {
                    //Let us go to the Response Analysis Report Page - Note only one .. is required
                    return Redirect("../Report/ReportPage.aspx?p_key=" + p_key);
                }
                else
                {
                    return Json(new { success = false, JsonRequestBehavior.AllowGet });
                }
            }
            catch
            {
                return Json(new { success = false, JsonRequestBehavior.AllowGet });
            }

        }
        #endregion

        #region [[ My Task Module ]]
        [NotLaunchpadAuthorize]
        public ActionResult ShowMyTaskPrintCallSheets(string pVersion)
        {
            try
            {
                //Create Unique GUID Here
                string p_key = System.Guid.NewGuid().ToString();

                #region [[ Prepare for SQL ]]
                string _sql = @"SELECT A.ACTID, A.ACTDATE AS DUEBY, 
                            (SELECT LEFT(dbo.oFullName(0,'''',S.FNAME,S.MNAME,S.LNAME,''''),60) FROM ssUSER S WHERE UID = A.SCHEDFOR) AS SCHEDFOR, 
                            T.DESCRIP AS TASKTYPE, A.SUBJECT, CAST(A.NOTE AS VARCHAR(MAX)) AS NOTE, P.PID, LEFT(dbo.oFullName(0,P.PREFIX,P.FNAME,P.MNAME,P.LNAME,P.SUFFIX),60) AS NAME, 
                            ISNULL(P.PREFIX,'''')PREFIX,ISNULL(P.FNAME,'''')FNAME,ISNULL(P.MNAME,'''') MNAME,ISNULL(P.LNAME,'''')LNAME,ISNULL(P.SUFFIX,'''') SUFFIX,
                            ISNULL(AD.STREET,'''') STREET,ISNULL(AD.ADDR1,'''') ADDR1,ISNULL(AD.ADDR2,'''') ADDR2,
                            ISNULL(AD.CITY,'''') CITY, ISNULL(AD.STATE,'''') [STATE],ISNULL(AD.ZIP,'''') ZIP,
                            HP.PHNNO AS HMPHN, BP.PHNNO AS BSPHN, CP.PHNNO AS CELL, 
                            A.SCHEDON, (SELECT LEFT(dbo.oFullName(0,'''',S.FNAME,S.MNAME,S.LNAME,''''),60) FROM ssUSER S WHERE UID = A.SCHEDBY) AS SCHEDBY, 
                            CAST(a.DONE as BIT) as DONE, 
                            -- CONVERT(VARCHAR(10), A.DONEON, 101) as DONEON 
                            A.DONEON FROM ACTIVITY A INNER JOIN (PEOPLE P 
                                            LEFT OUTER JOIN ADDRESS AD ON P.PID = AD.PID AND AD.PRIME = 1 
                                            LEFT OUTER JOIN PHONE HP ON P.PID = HP.PID AND HP.PHNTYPEID = 1 AND HP.PRIME=1
                                            LEFT OUTER JOIN PHONE BP ON P.PID = BP.PID AND BP.PHNTYPEID = 2 AND BP.PRIME=1
                                            LEFT OUTER JOIN PHONE CP ON P.PID = CP.PID AND CP.PHNTYPEID = 3 AND CP.PRIME=1) 
                                        ON A.PID = P.PID 
                                            LEFT OUTER JOIN lkACTTYPE T ON A.ACTTYPEID = T.ACTTYPEID 
                                            LEFT OUTER JOIN lkPURPOSE R ON A.PURPOSEID = R.PURPOSEID 
                                            LEFT OUTER JOIN lkPRIORITY O ON A.PRIORITYID = O.PRIORITYID 
                                            INNER JOIN ssUSER U ON A.SCHEDFOR = U.UID 
                                            INNER JOIN ssUSER V ON A.SCHEDBY = V.UID " +
                                            "WHERE A.DONE=0 AND U.USERID IN (" + "'" + _userSession.UserName.ToString() + "'" + ")";
                   

                // sql to pull PID's
                _sql = "SELECT DISTINCT P.PID " + _sql.Substring(_sql.IndexOf("FROM ACTIVITY A INNER JOIN "));
                #endregion
                
                #region [[ Create XML Statement ]]
                string _xml = "";
                if (pVersion == "Profile_full")
                    _xml = _supportMethods.get_report_data_sourceDef_in_xml_Full(_sql, "");
                else if (pVersion == "Profile_brief")
                    _xml = _supportMethods.get_report_data_sourceDef_in_xml_Brief(_sql);
                else if (pVersion == "Profile_pocket")
                    _xml = _supportMethods.get_report_data_sourceDef_in_xml_Pocket(_sql);
                else if (pVersion == "Profile_PAC")
                    _xml = _supportMethods.get_report_data_sourceDef_in_xml_PAC(_sql);
                #endregion

                #region [[ For Collection ]]
                //Create collection
                System.Xml.XmlDocument doc = new System.Xml.XmlDocument();
                doc.LoadXml(_xml);
                _node = doc.DocumentElement.SelectSingleNode("/ReportParameters");

                System.Collections.Specialized.NameValueCollection _collection = new System.Collections.Specialized.NameValueCollection();
                _collection.Add("cc", _node.Attributes["clientcode"].Value);
                _collection.Add("rn", _node.Attributes["reportcode"].Value);
                _collection.Add("XML", _node.OuterXml);
                //Save the Report Parameter collection in the Session
                System.Web.HttpContext.Current.Session.Add(p_key + "_c", _collection);
                #endregion

                #region [[ For Queries DataSet - First Dataset ]]
                //Get Queries Dataset
                xlist = _node.SelectNodes("Queries/Query"); 

                foreach (System.Xml.XmlNode node in xlist)
                {
                    ids.Add(node.Attributes["id"].Value);
                    sqls.Add(node.InnerText);
                    zeros.Add(0);
                }
                dSet = _reportDataService.get_dataset_w_sql__multi(sqls, ids, zeros, zeros);
                //For Resizing the Picture...
                dSet = _supportMethods.ReSizePictureinDataSet(dSet);
                //First Dataset
                System.Web.HttpContext.Current.Session.Add(p_key + "_fd", dSet);
                #endregion

                #region [[ For SubQueries Dataset ]]
                //First set all for null and then Get SubQueries DataSet
                xlist = null;
                dSet = null;
                xlist = _node.SelectNodes("Queries/SubQuery");
                ids.Clear();
                sqls.Clear();
                zeros.Clear();
                foreach (System.Xml.XmlNode node in xlist)
                {
                    ids.Add(node.Attributes["id"].Value);
                    sqls.Add(node.InnerText);
                    zeros.Add(0);
                }
                dSet = _reportDataService.get_dataset_w_sql__multi(sqls, ids, zeros, zeros);
                //Second Dataset
                System.Web.HttpContext.Current.Session.Add(p_key + "_sd", dSet);
                #endregion

                if (Session[p_key + "_c"] != null && Session[p_key + "_fd"] != null && Session[p_key + "_sd"] != null)
                {
                    //Let us go to the Response Analysis Report Page - Note only one .. is required
                    return Redirect("../Report/ReportPage.aspx?p_key=" + p_key);
                }
                else
                {
                    return Json(new { success = false, JsonRequestBehavior.AllowGet });
                }
            }
            catch
            {
                return Json(new { success = false, JsonRequestBehavior.AllowGet });
            }

        }
        #endregion

        #endregion

        //Used to see if Tasks result can be obtained or not based on user selection
        //[HttpPost]
        //public JsonResult TaskCanBeSearched(TaskSearch _taskSearch)
        //{
        //    genericResponseTask _response;
        //    try
        //    {
        //        //let us check for the Inputs from the user - First for Schedule By Users
        //        #region [[ Check User Input ]]
        //        var uidBy = "";
        //        foreach (ScheduleBy cb in _taskSearch.scheduleByUsers)
        //            if (cb.IsSelected == true)
        //                uidBy += (uidBy == "" ? cb.uid.ToString() : "," + cb.uid.ToString());

        //        if (uidBy == "")
        //        {
        //            _response = new genericResponseTask() { success = false, message = "You must select at least one user scheduling the tasks!", returneddata = "step1" };
        //            return Json(_response, JsonRequestBehavior.AllowGet);
        //        }

        //        //Now for Schedule For Users
        //        var uidFor = "";
        //        foreach (ScheduleFor cb in _taskSearch.scheduleForUsers)
        //            if (cb.IsSelected == true)
        //                uidFor += (uidFor == "" ? cb.uid.ToString() : "," + cb.uid.ToString());

        //        if (uidFor == "")
        //        {
        //            _response = new genericResponseTask() { success = false, message = "You must select at least one user performing the tasks!", returneddata = "step2" };
        //            return Json(_response, JsonRequestBehavior.AllowGet);
        //        }

        //        //Check for rare case scenario
        //        if (_taskSearch.DUEDTEFR != null && _taskSearch.DUEDTETO != null && _taskSearch.COMPDTEFR != null && _taskSearch.COMPDTETO != null)
        //        {
        //            _response = new genericResponseTask() { success = false, message = "Beginning and Ending date selection exists for both Due and Completed date. Please select for one of them.!", returneddata = "step3" };
        //            return Json(_response, JsonRequestBehavior.AllowGet);
        //        }

        //        //Now check for Due Date/Completed Date Options
        //        if (_taskSearch.IsDueDate)
        //        {
        //            //User has slected all due dates Option - let us check if all due date or From/To range available
        //            if (!_taskSearch.IsIncludeAllDueDates)
        //            {
        //                if (_taskSearch.DUEDTEFR == null)
        //                {
        //                    _response = new genericResponseTask() { success = false, message = "You must select a beginning due date!", returneddata = "step3" };
        //                    return Json(_response, JsonRequestBehavior.AllowGet);
        //                }
        //                if (_taskSearch.DUEDTETO == null)
        //                {
        //                    _response = new genericResponseTask() { success = false, message = "You must select a ending due date!", returneddata = "step3" };
        //                    return Json(_response, JsonRequestBehavior.AllowGet);
        //                }
        //            }

        //        }
        //        else
        //        {
        //            //User has selected all Completed dates Option - let us check if all completed date or From/To range available
        //            if (!_taskSearch.IsIncludeAllCompDates)
        //            {
        //                if (_taskSearch.COMPDTEFR == null)
        //                {
        //                    _response = new genericResponseTask() { success = false, message = "You must select a beginning completed date!", returneddata = "step3" };
        //                    return Json(_response, JsonRequestBehavior.AllowGet);
        //                }
        //                if (_taskSearch.COMPDTETO == null)
        //                {
        //                    _response = new genericResponseTask() { success = false, message = "You must select a ending completed date!", returneddata = "step3" };
        //                    return Json(_response, JsonRequestBehavior.AllowGet);
        //                }
        //            }
        //        }
                               
        //        #endregion
        //        //return resonse with data
        //        _response = new genericResponseTask()
        //        {
        //            success = true,
        //            uidBy = uidBy,
        //            uidFor = uidFor,
        //            IsDueDate = _taskSearch.IsDueDate,
        //            COMPDTEFR = _taskSearch.COMPDTEFR,
        //            COMPDTETO = _taskSearch.COMPDTETO,
        //            DUEDTEFR = _taskSearch.DUEDTEFR,
        //            DUEDTETO = _taskSearch.DUEDTETO,
        //            IsIncludeAllCompTasks = _taskSearch.IsIncludeAllCompTasks,
        //            IsIncludeAllOpenTasks = _taskSearch.IsIncludeAllOpenTasks,
        //            groupType = _taskSearch.groupType
                                          
        //        };
        //        return Json(_response, JsonRequestBehavior.AllowGet);
        //    }
        //    catch (Exception ex)
        //    {
        //        _response = new genericResponseTask() { success = false, message = "There is some problem in checking user inputs for the tasks." };
        //        return Json(_response, JsonRequestBehavior.AllowGet);
        //    }

        //}
        
//        [HttpGet]
//        public JsonResult GetTaskSearchResults(string uidBy, string uidFor, bool IsDueDate,
//            DateTime? COMPDTEFR, DateTime? COMPDTETO, DateTime? DUEDTEFR, DateTime? DUEDTETO, bool IsIncludeAllOpenTasks, bool IsIncludeAllCompTasks, string groupType, int? page, int? pageSize)
//        {
//            #region [ Retrieve "Sort" options ]
//            string sortField = HttpContext.Request.QueryString["sort[0][field]"];
//            string sortDir = HttpContext.Request.QueryString["sort[0][dir]"];

//            int _pageSize = (pageSize == null ? 10 : pageSize.Value);
//            int _pageNo = (page == null ? 1 : page.Value);

//            string _sortOptions = "";
//            if (!string.IsNullOrEmpty(sortField))
//                _sortOptions = sortField;
//            if (!string.IsNullOrEmpty(sortField) && !string.IsNullOrEmpty(sortDir))
//                _sortOptions = _sortOptions + " " + sortDir;
//            #endregion

//            List<TaskSearchResult_ext1> _list = new List<TaskSearchResult_ext1>();
//            genericResponse _response;

//            #region [[ Check User Input and Create the SQL statement ]]
                        
//            //For Type of the Task
//            string doneType = "";
//            if (IsIncludeAllOpenTasks)
//                doneType = "0";
//            if (IsIncludeAllCompTasks)
//                doneType += (doneType == "" ? "1" : ",1");

//            string dteField = "ACTDATE";
//            if (!IsDueDate)
//                dteField = "DONEON";

//            string _sql = @"SELECT A.ACTID, A.ACTDATE AS DUEBY, 
//                            (SELECT LEFT(dbo.oFullName(0,'''',S.FNAME,S.MNAME,S.LNAME,''''),60) FROM ssUSER S WHERE UID = A.SCHEDFOR) AS SCHEDFOR, 
//                            T.DESCRIP AS TASKTYPE, A.SUBJECT, CAST(A.NOTE AS VARCHAR(MAX)) AS NOTE, P.PID, LEFT(dbo.oFullName(0,P.PREFIX,P.FNAME,P.MNAME,P.LNAME,P.SUFFIX),60) AS NAME, 
//                            ISNULL(P.PREFIX,'''')PREFIX,ISNULL(P.FNAME,'''')FNAME,ISNULL(P.MNAME,'''') MNAME,ISNULL(P.LNAME,'''')LNAME,ISNULL(P.SUFFIX,'''') SUFFIX,
//                            ISNULL(AD.STREET,'''') STREET,ISNULL(AD.ADDR1,'''') ADDR1,ISNULL(AD.ADDR2,'''') ADDR2,
//                            ISNULL(AD.CITY,'''') CITY, ISNULL(AD.STATE,'''') [STATE],ISNULL(AD.ZIP,'''') ZIP,
//                             HP.PHNNO AS HMPHN, BP.PHNNO AS BSPHN, CP.PHNNO AS CELL, 
//                            A.SCHEDON, (SELECT LEFT(dbo.oFullName(0,'''',S.FNAME,S.MNAME,S.LNAME,''''),60) FROM ssUSER S WHERE UID = A.SCHEDBY) AS SCHEDBY, 
//                            CAST(a.DONE as BIT) as DONE, 
//                            -- CONVERT(VARCHAR(10), A.DONEON, 101) as DONEON 
//                            A.DONEON FROM ACTIVITY A INNER JOIN (PEOPLE P 
//                                            LEFT OUTER JOIN ADDRESS AD ON P.PID = AD.PID AND AD.PRIME = 1 
//                                            LEFT OUTER JOIN PHONE HP ON P.PID = HP.PID AND HP.PHNTYPEID = 1 AND HP.PRIME=1
//                                            LEFT OUTER JOIN PHONE BP ON P.PID = BP.PID AND BP.PHNTYPEID = 2 AND BP.PRIME=1
//                                            LEFT OUTER JOIN PHONE CP ON P.PID = CP.PID AND CP.PHNTYPEID = 3 AND CP.PRIME=1) 
//                                        ON A.PID = P.PID 
//                                            LEFT OUTER JOIN lkACTTYPE T ON A.ACTTYPEID = T.ACTTYPEID 
//                                            LEFT OUTER JOIN lkPURPOSE R ON A.PURPOSEID = R.PURPOSEID 
//                                            LEFT OUTER JOIN lkPRIORITY O ON A.PRIORITYID = O.PRIORITYID 
//                                            INNER JOIN ssUSER U ON A.SCHEDFOR = U.UID 
//                                            INNER JOIN ssUSER V ON A.SCHEDBY = V.UID " +
//                            "WHERE U.UID IN (" + uidFor.ToString() + ") AND V.UID IN (" + uidBy.ToString() + ") AND " +
//                        "A.DONE IN (" + doneType.ToString() + ")  " +
//                        ((groupType == "1") ? " AND T.ACTTYPE LIKE 'SC' " : " ");

//            if (DUEDTEFR != null && DUEDTETO != null && DUEDTEFR > Convert.ToDateTime("12/31/1970") && DUEDTETO > Convert.ToDateTime("12/31/1970"))
//                _sql += " AND A." + dteField.ToString() + " BETWEEN ''" + String.Format("{0:MM/dd/yyyy HH:mm:ss}", DUEDTEFR) + "'' AND ''" +
//                            String.Format("{0:MM/dd/yyyy HH:mm:ss}", DUEDTETO) + "'' ";

//            if (COMPDTEFR != null && COMPDTETO != null && COMPDTEFR > Convert.ToDateTime("12/31/1970") && COMPDTETO > Convert.ToDateTime("12/31/1970"))
//                _sql += " AND A." + dteField.ToString() + " BETWEEN ''" + String.Format("{0:MM/dd/yyyy HH:mm:ss}", COMPDTEFR) + "'' AND ''" +
//                            String.Format("{0:MM/dd/yyyy HH:mm:ss}", COMPDTETO) + "'' ";

//            //_sql += "ORDER BY 9, 10, 2";
//            #endregion


//            #region [[ Save the Query in the Session for Export Use ]]
//            //Clear the session first
//            Session["TaskSearchQuery_" + _userSession.UserId] = null;
//            //Now add the Query
//            System.Web.HttpContext.Current.Session.Add("TaskSearchQuery_" + _userSession.UserId, _sql);
//            #endregion

//            //Coming here when SearchText is null
//            if (string.IsNullOrEmpty(_sortOptions)) _sortOptions = "PREFIX,FNAME,DUEBY";
//            _list = _taskService.get_allTasks(_sql, _sortOptions, _pageSize, _pageNo);


//            Mapper.CreateMap<TaskSearchResult_ext1, rTaskSearchResult>();
//            IEnumerable<iItemType> results = _list.Select(a => Mapper.Map<TaskSearchResult_ext1, rTaskSearchResult>(a)).ToList();
//            if (results.Count() > 0)
//            {
//                _response = new genericResponse() { success = true, __count = _list.FirstOrDefault().count_, results = results.ToList() };
//            }
//            else
//            {
//                _response = new genericResponse() { success = true, __count = 0 };
//            }

//            return Json(_response, JsonRequestBehavior.AllowGet);

//        }

    }
}
