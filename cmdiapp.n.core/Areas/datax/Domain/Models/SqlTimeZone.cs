﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace cmdiapp.n.core.Areas.datax.Domain.Models
{
    /// <summary>
    /// Class that represents a time zone with the value
    /// as a time zone code for SQL Server
    /// </summary>
    public class SqlTimeZone
    {
        public string Label { get; set; }
        public string Value { get; set; }
        /// <summary>
        /// Offset from GMT
        /// </summary>
        public int OffsetHours { get; set; }
        public int OffsetMinutes { get; set; }
    }
}