﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Data;
using System.Data.SqlClient;

using Ninject;
using Ninject.Web.Mvc;

using cmdiapp.n.core.Core;
using cmdiapp.n.core.Domain.Models;
using cmdiapp.n.core.Core.App_Start;
using cmdiapp.n.core.Areas.crm.Core;
using cmdiapp.n.core.Areas.crm.Domain.Models;
using cmdiapp.n.core.Areas.crm.Domain.ViewModels;
using cmdiapp.n.core.Areas.crm.Domain.Repositories;

using System.Reflection.Emit;
using System.Reflection;
using cmdiapp.n.core.Areas.crm.Domain.Data;
using cmdiapp.n.core.Library;
using System.Xml;
using Newtonsoft.Json;
using cmdiapp.n.core.Areas.crm.ViewModels;
using cmdiapp.n.core.Domain.ViewModels;
using cmdiapp.n.core.Areas.query.Domain.Models;
using System.Linq.Expressions;

namespace cmdiapp.n.core.Areas.crm.Domain.Services
{
    public class peopleService : IpeopleService
    {
        private I___unitOfWork _unitOfWork;
        private I__DBFactory _dbFactory;
        private I_entity_crm _entity_crm;
        supportMethods _supportMethods = new supportMethods();

        public peopleService(I___unitOfWork unitOfWork, I__DBFactory dbFactory, I_entity_crm entity_crm)
        {
            _unitOfWork = unitOfWork;
            _dbFactory = dbFactory;
            _entity_crm = entity_crm;

        }

        public MessageBoard process_sql(string p_sql, string p_datasetName, MessageBoard _msgBoard)
        {
            try
            {
                //get the data
                DataSet _data = _supportMethods.GetDataSet(_dbFactory.getContext().Database.Connection.ConnectionString, p_sql, p_datasetName);
                //cehck what we have
                if (_data != null)
                {
                    _msgBoard.status = true;
                    _msgBoard.xmlresponse = _data.GetXml();
                }
                else
                {
                    _msgBoard.status = false;
                    _msgBoard.xmlresponse = "<error msg=\"error occurred during data retrieval\"/>";
                    _msgBoard.message = "Error in DB Process: Saving DT Batch Data : Dataset Name  " + p_datasetName + " ";
                }

            }
            catch (System.Exception ex)
            {
                //we have some issue
                _msgBoard.status = false;
                _msgBoard.xmlresponse = "";
                _msgBoard.message = "Error in DB Process: Saving DT Batch Data : Dataset Name " + p_datasetName + " " + ex.InnerException.ToString();
            }
            //return response
            return _msgBoard;
        }
                
        public PeopleR get_v_people_1(int pid)
        {
            string sql = string.Format("SELECT * FROM v_people_1 WHERE PID={0}", pid);

            var q = _dbFactory.getContext().Database.SqlQuery<PeopleR>(sql);

            return q.FirstOrDefault();
        }

        public List<v_people_1_ext1> get_v_people_1(string where, string orderBy, int pageSize, int pageNo)
        {
            string sql = string.Format("SELECT * FROM v_people_1 WHERE {0}", where);
            //string sql = string.Format("SELECT P.PID, P.CHKDGT, P.PREFIX, P.FNAME, P.MNAME, P.LNAME, P.SUFFIX, CAST(dbo.oFULLNAME(0, P.PREFIX, P.FNAME, P.MNAME, P.LNAME, P.SUFFIX) AS VARCHAR(100)) AS FULLNAME_, P.SPOUSENAME, P.EMPLOYER, P.OCCUPATION, P.TITLE, P.ASSISTANT, P.TrackNo, dbo.oPHONEwMASK(H.PHNNO) AS HPHONE, P.STR1, P.STR2, P.NUM1, dbo.oPHONEwMASK(W.PHNNO) AS WPHONE, dbo.oPHONEwMASK(C.PHNNO) AS CPHONE, dbo.oPHONEwMASK(F.PHNNO) AS FAX, E.PHNNO AS EMAIL, FB.PHNNO AS FACEBOOKID, TW.PHNNO AS TWITTERID, LN.PHNNO AS LINKEDINID, PT.DESCRIP AS PEOTYPE, PC.DESCRIP AS PEOCODE, I.DESCRIP AS PEOCLASS, P.FECCMTEID, A.ADDR1, A.ADDR2, A.STREET, A.CITY, A.STATE, KR.REGION, A.ZIP, A.PLUS4, A.LATITUDE, A.LONGITUDE, dbo.oADDRESS(A.ADDR1, A.ADDR2, A.STREET, A.CITY, A.STATE, A.ZIP, A.PLUS4) AS Address_, S.FGIFT, S.FGIFTDTE, S.HPC, S.HPCDTE, S.LGIFT, S.LGIFTDTE, S.NOGIFTS, S.CUMTOT, P.BIO FROM dbo.PEOPLE AS P LEFT OUTER JOIN dbo.ADDRESS AS A ON P.PID = A.PID AND A.PRIME = 1 LEFT OUTER JOIN dbo.PHONE AS H ON P.PID = H.PID AND H.PRIME = 1 AND H.PHNTYPEID = 1 LEFT OUTER JOIN dbo.PHONE AS W ON P.PID = W.PID AND W.PRIME = 1 AND W.PHNTYPEID = 2 LEFT OUTER JOIN dbo.PHONE AS C ON P.PID = C.PID AND C.PRIME = 1 AND C.PHNTYPEID = 3 LEFT OUTER JOIN dbo.PHONE AS F ON P.PID = F.PID AND F.PRIME = 1 AND F.PHNTYPEID = 5 LEFT OUTER JOIN dbo.PHONE AS E ON P.PID = E.PID AND E.PRIME = 1 AND E.PHNTYPEID = 4 LEFT OUTER JOIN dbo.PHONE AS FB ON P.PID = FB.PID AND FB.PHNTYPEID = 21 AND FB.PRIME = 1 LEFT OUTER JOIN dbo.PHONE AS TW ON P.PID = TW.PID AND TW.PHNTYPEID = 22 AND TW.PRIME = 1 LEFT OUTER JOIN dbo.PHONE AS LN ON P.PID = LN.PID AND LN.PHNTYPEID = 23 AND LN.PRIME = 1 LEFT OUTER JOIN dbo.PHONE AS AP ON P.PID = AP.PID AND AP.PHNTYPEID = 11 AND AP.PRIME = 1 LEFT OUTER JOIN dbo.PHONE AS AE ON P.PID = AE.PID AND AE.PHNTYPEID = 12 AND AE.PRIME = 1 LEFT OUTER JOIN dbo.SUMMARYD AS S ON P.PID = S.PID LEFT OUTER JOIN dbo.lkPEOCLASS AS I ON P.PEOCLASSID = I.PEOCLASSID LEFT OUTER JOIN dbo.lkPEOTYPE AS PT ON P.PEOTYPEID = PT.PEOTYPEID LEFT OUTER JOIN dbo.lkPEOCODE AS PC ON P.PEOCODEID = PC.PEOCODEID LEFT OUTER JOIN dbo.lkREGION AS KR ON A.STATE = KR.STATE AND ISNULL(KR.ZIP, A.ZIP) = A.ZIP WHERE {0}", where);
            var q = _dbFactory.getContext().Database.SqlQuery<v_people_1_ext1>(String.Format("EXEC dbo.get_pagedDataSet '{0}', '{1}', {2}, {3}", sql, orderBy, pageSize, pageNo));

            return q.ToList();
        }
        
        public byte[] pictureOf(int pid)
        {
            var q = _dbFactory.getContext().Database.SqlQuery<peoplePicture>(String.Format("SELECT TOP 1 pid, picture FROM PEOPLE WHERE PID={0}", pid));
            peoplePicture _result = q.FirstOrDefault();
            if (_result != null)
                return q.FirstOrDefault().PICTURE;
            else
                return null;
        }

        public List<PeopleMatch> findMatch(int pid)
        {
            //var q = _dbFactory.getContext().Database.SqlQuery<PeopleMatch>(String.Format("SELECT * FROM dbo.[findMatch]({0},null,null,null,null,null,null,null,null,null)", pid));
            var q = _dbFactory.getContext().Database.SqlQuery<PeopleMatch>(String.Format("SELECT * FROM dbo.[findMatch_PID]({0})", pid));
            return q.ToList();
        }

        public List<PeopleMatch> findMatch(string fname, string mname, string lname, string suffix, string email, string hmph, string cellph, string street, string zip)
        {
            var q = _dbFactory.getContext().Database.SqlQuery<PeopleMatch>(String.Format("SELECT * FROM dbo.[findMatch](0,'{0}','{1}','{2}','{3}','{4}','{5}','{6}','{7}','{8}')", fname, mname, lname, suffix, email, hmph, cellph, street, zip));
            return q.ToList();
        }

        private class record_withPid
        {
            public int NEWPID { get; set; }
        }

        public int availableTrackNo()
        {
            #region [ Get a Range if specified ] 
            configItem rangeConfig = session.userSession.configItems.Where(a => a.Key == crmConstants.fundraise_Trackno_range).FirstOrDefault();
            int rangeStart=0, rangeEnd=0;
            if (rangeConfig != null && !string.IsNullOrEmpty(rangeConfig.Val))
            {
                string[] range = rangeConfig.Val.Split(',');
                rangeStart = int.Parse(range[0]);
                rangeEnd = int.Parse(range[1]);

                string sql = @" SELECT MIN(TRACKNO) + 1 AS AVAILABLE_TKNO 
                            FROM PEOPLE
                            WHERE TRACKNO>0 
                                AND TRACKNO + 1 NOT IN (SELECT TRACKNO From PEOPLE WHERE TRACKNO>0)
                            " + (rangeStart > 0 ? string.Format(" AND ISNULL(TRACKNO,0) BETWEEN {0} AND {1}", rangeStart, rangeEnd) : "");

                var q = _dbFactory.getContext().Database.SqlQuery<int>(sql);

                int availableOne = q.FirstOrDefault();
                if (availableOne > 0)
                    return availableOne;
                else
                    return 1;
            }
           else
                return 1;

            #endregion
        }

        public int addPeople(PeopleF _peopleF)
        {
            try
            {
                var sql = @"EXEC dbo.wp_AddPeople_4 {0},{1},{2},{3},{4},{5},{6},{7},{8},{9},{10},"
                        + "{11},{12},{13},{14},{15},{16},{17},{18},{19},{20},"
                        + "{21},{22},{23},{24},{25},{26},{27},{28},{29},{30},"
                        + "{31},{32},{33},{34},{35},{36},{37},{38},{39},{40},"
                        + "{41},{42},{43},{44},{45},{46},{47},{48}";

                var q = _dbFactory.getContext().Database.SqlQuery<record_withPid>(sql,
                    _peopleF.PID, _peopleF.PEOCODE, _peopleF.PEOTYPE, _peopleF.TRACKNO, _peopleF.TITLE,
                    _peopleF.PREFIX, _peopleF.FNAME, _peopleF.MNAME, _peopleF.LNAME, _peopleF.SUFFIX,
                    _peopleF.SALUTATION, _peopleF.INFSALUT, _peopleF.PRESSALUT, _peopleF.MAILNAME, _peopleF.SPOUSENAME,
                    _peopleF.EMPLOYER, _peopleF.OCCUPATION, _peopleF.FECCMTEID, _peopleF.INDUSTRY, _peopleF.ASSISTANT,
                    _peopleF.PRIMEMAIL, _peopleF.ADDRTYPE, _peopleF.STREET, _peopleF.ADDR1, _peopleF.ADDR2, _peopleF.CITY, _peopleF.STATE, _peopleF.ZIP, _peopleF.PLUS4,
                    _peopleF.HMPH, _peopleF.BUSPH, _peopleF.CELLPH, _peopleF.FAX, _peopleF.EMAIL, _peopleF.URL, _peopleF.FACEBOOKID, _peopleF.TWITTERID, _peopleF.BIO, _peopleF.updating_uid,
                    _peopleF.NUM1, _peopleF.STR1, _peopleF.STR2, _peopleF.ASSTPHONE, _peopleF.ASSTEMAIL, _peopleF.DOB, _peopleF.CHAPCODEID ,_peopleF.ACCTNO,_peopleF.DOD,_peopleF.COUNTRYID 
                    );

                record_withPid result = q.FirstOrDefault();
                if (result != null && result.NEWPID > 0)
                    return result.NEWPID;
                else
                    return -1;
            }
            catch
            {
                return -1;
            }
        }

        public int savePeople(PeopleF _peopleF)
        {
            try
            {
                var sql = @"EXEC dbo.wp_SavePeople_4 {0},{1},{2},{3},{4},{5},{6},{7},{8},{9},{10},"
                        + "{11},{12},{13},{14},{15},{16},{17},{18},{19},{20},"
                        + "{21},{22},{23},{24},{25},{26},{27},{28},{29},{30},"
                        + "{31},{32},{33},{34},{35},{36},{37},{38},{39},{40}," 
                        + "{41},{42},{43},{44},{45},{46},{47},{48}";

                int result = _dbFactory.getContext().Database.ExecuteSqlCommand(sql,
                    _peopleF.PID, _peopleF.PEOCODE, _peopleF.PEOTYPE, _peopleF.TRACKNO, _peopleF.TITLE,
                    _peopleF.PREFIX, _peopleF.FNAME, _peopleF.MNAME, _peopleF.LNAME, _peopleF.SUFFIX,
                    _peopleF.SALUTATION, _peopleF.INFSALUT, _peopleF.PRESSALUT, _peopleF.MAILNAME, _peopleF.SPOUSENAME,
                    _peopleF.EMPLOYER, _peopleF.OCCUPATION, _peopleF.FECCMTEID, _peopleF.INDUSTRY, _peopleF.ASSISTANT,
                    _peopleF.PRIMEMAIL, _peopleF.ADDRTYPE, _peopleF.STREET, _peopleF.ADDR1, _peopleF.ADDR2, _peopleF.CITY, _peopleF.STATE, _peopleF.ZIP, _peopleF.PLUS4,
                    _peopleF.HMPH, _peopleF.BUSPH, _peopleF.CELLPH, _peopleF.FAX, _peopleF.EMAIL, _peopleF.URL, _peopleF.FACEBOOKID, _peopleF.TWITTERID, _peopleF.BIO, _peopleF.updating_uid,
                    _peopleF.NUM1, _peopleF.STR1, _peopleF.STR2, _peopleF.ASSTPHONE, _peopleF.ASSTEMAIL, _peopleF.DOB, _peopleF.CHAPCODEID ,_peopleF.ACCTNO,_peopleF.DOD,_peopleF.COUNTRYID 
                    );


                return _peopleF.PID;
            }
            catch (Exception ex)
            {
                return -1;
            }
        }


        public int markDonorActive(int pid)
        {
            var sql = @"EXEC dbo.markDonorAsActive {0},{1}";

            int result = _dbFactory.getContext().Database.ExecuteSqlCommand(sql,pid,"Msg");

            return result;
            
        }


        public List<PeopleR_w_distance> nearbyRecords ( decimal ctr_latitude, decimal ctr_longitude, 
                                                        decimal rect_latitude1, decimal rect_latitude2, decimal rect_longitude1, decimal rect_longitude2,
                                                        int recLimit, int idExcl)
        {
            string _sql = String.Format("EXEC dbo.v_people_1__in_Rect {0},{1},{2},{3},{4},{5},{6},{7}",
                                                                                ctr_latitude, ctr_longitude, rect_latitude1, rect_latitude2, rect_longitude1, rect_longitude2,
                                                                                recLimit, idExcl);

            var q = _dbFactory.getContext().Database.SqlQuery<PeopleR_w_distance>("EXEC dbo.v_people_1__in_Rect {0},{1},{2},{3},{4},{5},{6},{7}",
                                                                                ctr_latitude, ctr_longitude, rect_latitude1, rect_latitude2, rect_longitude1, rect_longitude2,
                                                                                recLimit, idExcl);
            return q.ToList();
        }

        public List<PeopleR_w_distance> filterNearbyRecords(List<PeopleR_w_distance> nearbyRecords, List<NearbyFilter> filters)
        {
            if (filters == null)
            {
                return nearbyRecords;
            }

            return nearbyRecords.Where(r =>
                {
                    bool pass = true;
                    foreach (var filter in filters)
                    {
                        if (pass == false) { break; }
                        dynamic recordValue = r.GetType().GetProperty(filter.Column).GetValue(r, null);
                        if (filter.Type == "d")
                        {
                            filter.Value = Convert.ToDateTime(filter.Value);
                            recordValue = Convert.ToDateTime(recordValue);
                        }
                        else if (filter.Type != "s")
                        {
                            filter.Value = Convert.ToDecimal(filter.Value);
                            recordValue = Convert.ToDecimal(recordValue);
                        }
                        if (filter.Operator.Contains(">"))
                        {
                            pass = pass && recordValue >= filter.Value;
                        }
                        else if (filter.Operator == "==")
                        {
                            pass = pass && recordValue == filter.Value;
                        }
                        else
                        {
                            pass = pass && recordValue <= filter.Value;
                        }
                    }
                    return pass;
                }).ToList();
        }

        public List<ssMRGLIST_ext> GetMRGlistMenuByPIDs(int pid1, int pid2)
        {
            //showing limited data
            string SQL = String.Format(@"EXEC [dbo].[iGetMRGlistMenuByPIDs] {0},{1}",pid1,pid2);

            var listTableDesc = _dbFactory.getContext().Database.SqlQuery<ssMRGLIST_ext>(SQL);
            return listTableDesc.ToList<ssMRGLIST_ext>();
        }

        public List<ssMRGLIST> get_ssMRGLIST()
        {
            string where = " ";
            //for non profit
            if (session.currentDomain_project.projectType.appVersion.ToLower() == "nonprofit")
            {
                where = " and active =1 ";
            }
            //showing limited data
            string SQL = "select TABLENAME + convert(varchar,MRGLISTID) as ID,MRGLISTID,MRGTYPE,SHOWLIST,ALLWMOVE,ALLWDELETE,TABLEDESC from ssMRGLIST where SHOWLIST = 1 " + where + " order by LISTORDER";

            var listTableDesc = _dbFactory.getContext().Database.SqlQuery<ssMRGLIST>(SQL);
            return listTableDesc.ToList<ssMRGLIST>();
        }

        public ssMRGLIST get_ssMRGLIST(string id)
        {
            string sql = @"select TABLENAME + convert(varchar,MRGLISTID) as ID,
            MRGTYPE,
            SHOWLIST,
            ALLWMOVE,
            ALLWDELETE,
            TABLENAME,
            TABLEDESC,
            PIDFIELD,
            UNQIDFIELD,
            isnull(IDFIELD,'') as IDFIELD,
            isnull(DISPSQL,'') as DISPSQL from ssMRGLIST 
            where TABLENAME + convert(varchar,MRGLISTID) ='" + id + "'";

            var q = _dbFactory.getContext().Database.SqlQuery<ssMRGLIST>(sql);
            return q.FirstOrDefault();
        }

        private void CreateAutoImplementedProperty(
           TypeBuilder builder, string propertyName, Type propertyType)
        {
            const string PrivateFieldPrefix = "m_";
            const string GetterPrefix = "get_";
            const string SetterPrefix = "set_";

            // Generate the field.
            FieldBuilder fieldBuilder = builder.DefineField(
                string.Concat(PrivateFieldPrefix, propertyName), propertyType, FieldAttributes.Private);

            // Generate the property
            PropertyBuilder propertyBuilder = builder.DefineProperty(
                propertyName, System.Reflection.PropertyAttributes.HasDefault, propertyType, null);

            // Property getter and setter attributes.
            MethodAttributes propertyMethodAttributes =
                MethodAttributes.Public | MethodAttributes.SpecialName | MethodAttributes.HideBySig;

            // Define the getter method.
            MethodBuilder getterMethod = builder.DefineMethod(
                string.Concat(GetterPrefix, propertyName),
                propertyMethodAttributes, propertyType, Type.EmptyTypes);

            // Emit the IL code.
            // ldarg.0
            // ldfld,_field
            // ret
            ILGenerator getterILCode = getterMethod.GetILGenerator();
            getterILCode.Emit(OpCodes.Ldarg_0);
            getterILCode.Emit(OpCodes.Ldfld, fieldBuilder);
            getterILCode.Emit(OpCodes.Ret);

            // Define the setter method.
            MethodBuilder setterMethod = builder.DefineMethod(
                string.Concat(SetterPrefix, propertyName),
                propertyMethodAttributes, null, new Type[] { propertyType });

            // Emit the IL code.
            // ldarg.0
            // ldarg.1
            // stfld,_field
            // ret
            ILGenerator setterILCode = setterMethod.GetILGenerator();
            setterILCode.Emit(OpCodes.Ldarg_0);
            setterILCode.Emit(OpCodes.Ldarg_1);
            setterILCode.Emit(OpCodes.Stfld, fieldBuilder);
            setterILCode.Emit(OpCodes.Ret);

            propertyBuilder.SetGetMethod(getterMethod);
            propertyBuilder.SetSetMethod(setterMethod);
        }

        private TypeBuilder CreateTypeBuilder(
           string assemblyName, string moduleName, string typeName)
        {
            TypeBuilder typeBuilder = AppDomain
                .CurrentDomain
                .DefineDynamicAssembly(new AssemblyName(assemblyName), AssemblyBuilderAccess.Run)
                .DefineDynamicModule(moduleName)
                .DefineType(typeName, TypeAttributes.Public);
            typeBuilder.DefineDefaultConstructor(MethodAttributes.Public);
            return typeBuilder;
        }

        public List<object> get_GridDataFromSQL(string SQL, string PID, Dictionary<string, string> _dictionary)
        {
            SQL = SQL.Replace("<<ID>>", PID.ToString());
            string _sql_allstring = null;

            TypeBuilder builder = CreateTypeBuilder("MergeDynamicAssembly", "MergeModule", "MergeType");

            _sql_allstring = "select ";
            bool firstCol = true;
            foreach (KeyValuePair<string, string> pair in _dictionary)
            {
                if (!firstCol)
                    _sql_allstring += ", ";

                _sql_allstring += "RTrim(LTrim(ISNULL(CAST(" + pair.Value + " AS varchar(MAX)),''))) as [" + pair.Key + "]";
                firstCol = false;
            }
            _sql_allstring += " " + SQL.Substring(SQL.ToLower().LastIndexOf("from"));

            foreach (KeyValuePair<string, string> pair in _dictionary)
                CreateAutoImplementedProperty(builder, pair.Key, typeof(string));

            Type resultType = builder.CreateType();
            dynamic queryResult = _dbFactory.getContext().Database.SqlQuery(resultType, _sql_allstring);

            List<object> _obj = new List<object>();
            foreach (dynamic item in queryResult)
                _obj.Add(item);

            return _obj;
        }

        public Dictionary<string, string> getDictionary(string _sql)
        {
            _sql = _sql.ToLower();

            if (_sql == null || _sql.Trim() == String.Empty)
                return null;

            Dictionary<string, string> _dictionary = new Dictionary<string, string>();

            //discard everything except columns part
            int start = "Select".Length;
            int end = _sql.LastIndexOf("From ".ToLower());
            string cols = _sql.Substring(start, end - start).Trim();

            Stack<int> _colIndex = new Stack<int>();
            bool quote = false;
            Stack<char> _stack = new Stack<char>();
            _colIndex.Push(cols.Length);//grab the last column

            //scan from right to left
            for (int i = cols.Length - 1; i >= 0; i--)
            {
                if (cols[i] == '\'')
                    quote = !quote;
                if (!quote)//inside quote ignore everything
                {
                    if (cols[i] == ')')
                        _stack.Push(')');

                    if (cols[i] == '(')
                        _stack.Pop();//assuming no error in SQL

                    if (cols[i] == ',' && _stack.Count == 0)
                        _colIndex.Push(i);
                }
            }


            //show all tokens
            string colValue;
            string colName;
            int s = -1;
            //int I = 0;
            foreach (int i in _colIndex)
            {
                String col = cols.Substring(s + 1, i - s - 1).Trim();
                s = i;

                int index_AS = col.LastIndexOf("as ");
                int index_DOT = col.LastIndexOf(".");

                if (index_AS >= 0)
                {
                    colValue = col.Substring(0, index_AS).Trim();
                    colName = col.Substring(index_AS + 2).Trim();

                }
                else if (index_DOT >= 0)
                {
                    colValue = col;
                    colName = col.Substring(index_DOT + 1).Trim();

                }
                else
                {
                    colValue = col;
                    colName = col;
                }
                //remove [ or ] from Col Name
                if (colName[0] == '[')
                    colName = colName.Substring(1, colName.Length - 2);
                // remove dot [.] or space
                colName = colName.Replace(" ", "");
                colName = colName.Replace(".", "");

                _dictionary.Add(colName.ToUpper(), colValue);

            }
            return _dictionary;

        }

        //executes stored procedure for 'move'
        public SP_response execute_SP(string _sql)
        {
            var q = _dbFactory.getContext().Database.SqlQuery<SP_response>(_sql);

            return q.FirstOrDefault();
        }

        //executes stored procedure for 'delete'
        public int executeDelete(string _sql)
        {
            var q = _dbFactory.getContext().Database.SqlQuery<Int32>(_sql);
            return q.FirstOrDefault();
        }

        public PEOCODETYPEID getPEOcodetype(string PID)
        {
            string __sql = @"select PID,lkPEOCODE.PEOCODE ,lkPEOTYPE.PEOTYPE
            from PEOPLE inner join  lkPEOCODE on PEOPLE.PEOCODEID= lkPEOCODE.PEOCODEID
            inner join lkPEOTYPE on PEOPLE.PEOTYPEID=lkPEOTYPE.PEOTYPEID
            where PID =" + PID;

            var q = _dbFactory.getContext().Database.SqlQuery<PEOCODETYPEID>(__sql);
            return q.FirstOrDefault();
        }

        #region [[ For Relationship ]]

        public int Add(jtRELATE _jtRELATE)
        {
            _entity_crm.Add(_jtRELATE);
            _entity_crm.CommitChanges();
            return _jtRELATE.JTRELATEID;
        }

        public int Update(jtRELATE _jtRELATE)
        {
            _entity_crm.Update(_jtRELATE);
            _entity_crm.CommitChanges();
            return _jtRELATE.JTRELATEID;
        }

        public genericResponse DeleteRelationship(int jtRelateId)
        {
            jtRELATE relationship = _entity_crm.Single<jtRELATE>(r => r.JTRELATEID == jtRelateId);
            if (relationship == null)
            {
                return new genericResponse()
                {
                    success = false,
                    message = "There is no relationship record with that ID."
                };
            }
            try
            {
                _entity_crm.Delete(relationship);
                _entity_crm.CommitChanges();
                return new genericResponse()
                {
                    success = true
                };
            }
            catch (Exception)
            {
                return new genericResponse()
                {
                    success = false,
                    message = "An error occurred. Unable to delete relationship."
                };
            }
        }

        public List<lkRELATETYPE> GetRelateTypes()
        {
            return _entity_crm.All<lkRELATETYPE>().OrderBy(a => a.DESCRIP).ToList();
        }

        public List<RelationNode> GetRelationNodes(int parentPID, int parentRelateId = 0)
        {
            return (from relation in _entity_crm.All<jtRELATE>()
                    join type in _entity_crm.All<lkRELATETYPE>() on relation.RELATETYPEID equals type.RELATETYPEID
                    join peoPid in _entity_crm.All<PeopleR>() on relation.PID equals peoPid.PID
                    join peoRelateTo in _entity_crm.All<PeopleR>() on relation.RELATETO equals peoRelateTo.PID
                    where (relation.PID == parentPID || relation.RELATETO == parentPID)
                        && relation.JTRELATEID != parentRelateId
                    orderby parentPID == relation.PID ? peoRelateTo.LNAME : peoPid.LNAME,
                            parentPID == relation.PID ? peoRelateTo.FNAME : peoPid.FNAME,
                            parentPID == relation.PID ? peoRelateTo.MNAME : peoPid.MNAME
                    select new RelationNode
                    {
                        Relate = relation,
                        Name = parentPID == relation.PID ? peoRelateTo.FULLNAME_ : peoPid.FULLNAME_,
                        RelationDescrip = parentPID == relation.PID ? type.DESCRIP : type.RECIPROCAL,
                        ChildPID = parentPID == relation.PID ? relation.RELATETO : relation.PID
                    })
                    .ToList();
        }

        public bool HasRelationNodes(int parentPID, int parentRelateId = 0)
        {
            return _entity_crm.All<jtRELATE>()
                .Where(r => (r.PID == parentPID || r.RELATETO == parentPID) && r.JTRELATEID != parentRelateId)
                .Any();
        }
        
        #endregion


        #region [[ For Bundler ]]

        public List<BundlerR_ext1> get_Bundlers(string selFields, string sqlFrom, string where, string orderBy, int pageSize, int pageNo)
        {
            string baseSQL = "SELECT DISTINCT " + selFields.Replace("'","''") + " FROM " + sqlFrom;

            where = where.Replace("FNAME", "P.FNAME");
            where = where.Replace("LNAME", "P.LNAME");
            where = where.Replace("MNAME", "P.MNAME");


            string sql = baseSQL + string.Format(" WHERE {0}", where);

            var q = _dbFactory.getContext().Database.SqlQuery<BundlerR_ext1>(String.Format("EXEC dbo.get_pagedDataSet '{0}', '{1}', {2}, {3}", sql, orderBy, pageSize, pageNo));

            return q.ToList();
        }

        public List<BundlerListDisplay> GetBundlersList(string searchText, int page, int pageSize)
        {
            string[] searchWords = (searchText?.Trim()?.ToLower() ?? "")
                // splits on whitespace
                .Split((char[])null, StringSplitOptions.RemoveEmptyEntries);

            // evaluate query to list to get around OFFSET error
            // that occurs with EF paging in old SQL databases
            List<BundlerListDisplay> searchResult = _entity_crm.All<PeopleR>()
                .Join(_entity_crm.All<FUNDRAISE>(),
                    p => p.TRACKNO,
                    f => f.TRACKNO,
                    (p, f) => new BundlerListDisplay()
                    {
                        PID = p.PID,
                        Name = p.FULLNAME_,
                        TrackNo = f.TRACKNO
                    })
                .Select(x =>
                    new
                    {
                        record = x,
                        matchedWords = searchWords.Where(w =>
                                x.Name != null
                                && (x.Name.ToLower().Contains(w) || x.TrackNo.ToString().Contains(w)))
                            .Count()
                    })
                .Where(x => x.matchedWords > 0)
                .OrderByDescending(x => x.matchedWords)
                .ThenBy(x => x.record.TrackNo)
                .Select(x => x.record)
                .ToList();

            return searchResult
                .Skip((page - 1) * pageSize)
                .Take(pageSize)
                .ToList();
        }

        #endregion

        public object GetFundraiserDownlineByTrackNo(int trackNo)
        {
            // load the downline data as an xml document
            var xmlResult = new XmlDocument();
            string sql = String.Format("SELECT dbo.fundraiser_downline({0})", trackNo);
            var q = _dbFactory.getContext().Database.SqlQuery<string>(sql).FirstOrDefault();
            xmlResult.LoadXml(q);
            // convert to Json string
            string jsonText = JsonConvert.SerializeXmlNode(xmlResult).Replace("@", string.Empty);            
            return JsonConvert.DeserializeObject(jsonText);
        }

        public ContactConnection FetchContactConnectionsByTrackingNumber(int trackingNumber)
        {
            string sql = String.Format("EXEC dbo.dxt_summ_connection {0}", trackingNumber);

            var q = _dbFactory.getContext().Database.SqlQuery<ContactConnection>(sql);

            return q.Where(x => x.LEVEL == 0).FirstOrDefault();

        }

        public List<RelatePlusNames> GetRelationGraphLinks(int parentPID, int limit = 0)
        {
            List<jtRELATE> relations = _entity_crm.All<jtRELATE>()
                .Where(r => r.PID == parentPID || r.RELATETO == parentPID)
                .ToList();
            List<int> relatedIds = relations
                .Select(r => r.PID == parentPID ? r.RELATETO : r.PID)
                .ToList();
            List<int> parentsChecked = new List<int> { parentPID };

            if (limit != 1)
            {
                Tuple<List<int>, List<jtRELATE>> listTuple =
                    new Tuple<List<int>, List<jtRELATE>>(parentsChecked, relations);
                foreach (int id in relatedIds)
                {
                    GetLinks(id, listTuple, limit);
                }
            }
            relations.Sort((a, b) => parentsChecked.IndexOf(a.PID) - parentsChecked.IndexOf(b.PID));
            return relations.Select(r => new RelatePlusNames
            {
                jtRelate = r,
                SourceName = _entity_crm.Single<PeopleR>(p => p.PID == r.PID)?.FULLNAME_ ?? "",
                TargetName = _entity_crm.Single<PeopleR>(p => p.PID == r.RELATETO)?.FULLNAME_ ?? "",
            }).ToList();
        }

        private List<jtRELATE> GetLinks(int id, Tuple<List<int>, List<jtRELATE>> listTuple, int limit)
        {
            List<int> parentsChecked = listTuple.Item1;
            List<jtRELATE> relations = listTuple.Item2;
            List<jtRELATE> rel = _entity_crm.All<jtRELATE>()
                .Where(r => (r.PID == id || r.RELATETO == id)
                    && !(parentsChecked.Contains(r.PID) || parentsChecked.Contains(r.RELATETO)))
                .ToList();
            relations.AddRange(rel);
            parentsChecked.Add(id);
            foreach (int _id in rel
                .Select(r => r.PID == id ? r.RELATETO : r.PID)
                .ToList())
            {
                if (limit == 0 || parentsChecked.Count <= limit)
                {
                    GetLinks(_id, listTuple, limit);
                }
            }
            return relations;
        }

        public List<peopleTimeLine_ext> getProfileTimeLine(
            int pid, int page, int pageSize, string searchTxt, string type, string date)
        {
            // start query
            string sql = $"SELECT * FROM [fn_peopleProfileTimeline_getAll]({QzLib.sanitize(pid.ToString())})";
            sql += " Where 1=1 ";
            if (!string.IsNullOrEmpty(searchTxt))
            {
                sql += $" AND (detail like '%{searchTxt}%' OR code like '%{searchTxt}%')";
            }
            if (!string.IsNullOrEmpty(type))
            {
                sql += $" AND type = '{type}'";
            }
            if (!string.IsNullOrEmpty(date))
            {
                sql += " AND " + new DateFilterFactory<peopleTimeLine>(date, "date", false, "date").SQL;
            }

            var q = _dbFactory.getContext().Database.SqlQuery<peopleTimeLine_ext>(String.Format("EXEC dbo.get_pagedDataSet '{0}', '{1}', {2}, {3}", QzLib.sanitize(sql).Replace("'","''"), "date desc", pageSize, page));
            return q.ToList();
        }

    }
}