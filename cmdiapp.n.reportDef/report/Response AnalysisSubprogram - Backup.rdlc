﻿<?xml version="1.0" encoding="utf-8"?>
<Report xmlns="http://schemas.microsoft.com/sqlserver/reporting/2005/01/reportdefinition" xmlns:rd="http://schemas.microsoft.com/SQLServer/reporting/reportdesigner">
  <DataSources>
    <DataSource Name="Dexter">
      <rd:DataSourceID>892a8991-1132-4fc0-adb9-1a84b4266d6f</rd:DataSourceID>
      <DataSourceReference>Dexter</DataSourceReference>
    </DataSource>
  </DataSources>
  <InteractiveHeight>11in</InteractiveHeight>
  <ReportParameters>
    <ReportParameter Name="Program">
      <DataType>String</DataType>
      <Prompt>Sub-Program</Prompt>
      <ValidValues>
        <DataSetReference>
          <DataSetName>Program</DataSetName>
          <ValueField>PROGID</ValueField>
          <LabelField>DESCRIP</LabelField>
        </DataSetReference>
      </ValidValues>
    </ReportParameter>
    <ReportParameter Name="Package">
      <DataType>String</DataType>
      <DefaultValue>
        <Values>
          <Value>-1</Value>
        </Values>
      </DefaultValue>
      <Prompt>Package</Prompt>
      <ValidValues>
        <DataSetReference>
          <DataSetName>Package</DataSetName>
          <ValueField>PKGEID</ValueField>
          <LabelField>PKGEDESC</LabelField>
        </DataSetReference>
      </ValidValues>
      <MultiValue>true</MultiValue>
    </ReportParameter>
    <ReportParameter Name="Maildate_from">
      <DataType>DateTime</DataType>
      <DefaultValue>
        <Values>
          <Value>=DateAdd("m",-12,TODAY)</Value>
        </Values>
      </DefaultValue>
      <AllowBlank>true</AllowBlank>
      <Prompt>Maildate (From)</Prompt>
    </ReportParameter>
    <ReportParameter Name="Maildate_To">
      <DataType>DateTime</DataType>
      <DefaultValue>
        <Values>
          <Value>=TODAY()</Value>
        </Values>
      </DefaultValue>
      <AllowBlank>true</AllowBlank>
      <Prompt>Maildate (To)</Prompt>
    </ReportParameter>
    <ReportParameter Name="apply_maildate">
      <DataType>Boolean</DataType>
      <DefaultValue>
        <Values>
          <Value>=FALSE</Value>
        </Values>
      </DefaultValue>
      <AllowBlank>true</AllowBlank>
      <Prompt>Apply Maildate</Prompt>
    </ReportParameter>
    <ReportParameter Name="show_details">
      <DataType>Boolean</DataType>
      <DefaultValue>
        <Values>
          <Value>True</Value>
        </Values>
      </DefaultValue>
      <AllowBlank>true</AllowBlank>
      <Prompt>Show Details</Prompt>
    </ReportParameter>
    <ReportParameter Name="show_subtotal_LISTNOG">
      <DataType>Boolean</DataType>
      <DefaultValue>
        <Values>
          <Value>FALSE</Value>
        </Values>
      </DefaultValue>
      <AllowBlank>true</AllowBlank>
      <Prompt>Show Subtotal by List#(Group)</Prompt>
    </ReportParameter>
    <ReportParameter Name="show_subtotal_LISTNO">
      <DataType>Boolean</DataType>
      <DefaultValue>
        <Values>
          <Value>FALSE</Value>
        </Values>
      </DefaultValue>
      <AllowBlank>true</AllowBlank>
      <Prompt>Show Subtotal by List#</Prompt>
    </ReportParameter>
    <ReportParameter Name="show_subtotal_PKGE">
      <DataType>Boolean</DataType>
      <DefaultValue>
        <Values>
          <Value>True</Value>
        </Values>
      </DefaultValue>
      <AllowBlank>true</AllowBlank>
      <Prompt>Show Subtotal by Package</Prompt>
    </ReportParameter>
    <ReportParameter Name="client_name">
      <DataType>String</DataType>
      <DefaultValue>
        <DataSetReference>
          <DataSetName>SystemInfo</DataSetName>
          <ValueField>ClientName</ValueField>
        </DataSetReference>
      </DefaultValue>
      <AllowBlank>true</AllowBlank>
      <Hidden>true</Hidden>
    </ReportParameter>
  </ReportParameters>
  <rd:DrawGrid>true</rd:DrawGrid>
  <InteractiveWidth>8.5in</InteractiveWidth>
  <rd:GridSpacing>0.0625in</rd:GridSpacing>
  <rd:SnapToGrid>true</rd:SnapToGrid>
  <PageHeader>
    <PrintOnFirstPage>true</PrintOnFirstPage>
    <ReportItems>
      <Textbox Name="textbox30">
        <Width>3.0925in</Width>
        <Style>
          <Color>DimGray</Color>
          <FontFamily>Tahoma</FontFamily>
          <FontSize>7pt</FontSize>
          <FontWeight>700</FontWeight>
          <TextAlign>Right</TextAlign>
          <PaddingLeft>2pt</PaddingLeft>
          <PaddingRight>2pt</PaddingRight>
          <PaddingTop>2pt</PaddingTop>
          <PaddingBottom>2pt</PaddingBottom>
        </Style>
        <ZIndex>1</ZIndex>
        <CanGrow>true</CanGrow>
        <Left>7.36458in</Left>
        <Height>0.1875in</Height>
        <Value>=First("Program: " + ReportItems!tx_program.Value)</Value>
      </Textbox>
      <Textbox Name="textbox1">
        <rd:DefaultName>textbox1</rd:DefaultName>
        <Width>7.3125in</Width>
        <Style>
          <Color>SteelBlue</Color>
          <FontFamily>Tahoma</FontFamily>
          <FontWeight>700</FontWeight>
          <PaddingLeft>2pt</PaddingLeft>
          <PaddingRight>2pt</PaddingRight>
          <PaddingTop>2pt</PaddingTop>
          <PaddingBottom>2pt</PaddingBottom>
        </Style>
        <CanGrow>true</CanGrow>
        <Value>=("Response Analysis" + IIF(Parameters!show_details.Value=False," (Package-Level Summary)","") + " - " + Parameters!client_name.Value)</Value>
      </Textbox>
    </ReportItems>
    <Height>0.25in</Height>
    <PrintOnLastPage>true</PrintOnLastPage>
  </PageHeader>
  <BottomMargin>0.25in</BottomMargin>
  <rd:ReportID>ed2da4ae-3348-452d-8f0d-13e941b66aca</rd:ReportID>
  <PageWidth>11in</PageWidth>
  <DataSets>
    <DataSet Name="RespAnalysis">
      <Fields>
        <Field Name="PROGTYPE">
          <DataField>PROGTYPE</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="DESCRIP">
          <DataField>DESCRIP</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="PKGECODE">
          <DataField>PKGECODE</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="PKGEDESC">
          <DataField>PKGEDESC</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="K_MDTKEY">
          <DataField>K_MDTKEY</DataField>
          <rd:TypeName>System.Int64</rd:TypeName>
        </Field>
        <Field Name="PLastCage">
          <DataField>PLastCage</DataField>
          <rd:TypeName>System.DateTime</rd:TypeName>
        </Field>
        <Field Name="LISTNOG">
          <DataField>LISTNOG</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="LISTNO">
          <DataField>LISTNO</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="SRCECODE">
          <DataField>SRCECODE</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="SRCEDESC">
          <DataField>SRCEDESC</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="MAILDATE">
          <DataField>MAILDATE</DataField>
          <rd:TypeName>System.DateTime</rd:TypeName>
        </Field>
        <Field Name="MAILQTY">
          <DataField>MAILQTY</DataField>
          <rd:TypeName>System.Int32</rd:TypeName>
        </Field>
        <Field Name="FIRSTCAGE">
          <DataField>FIRSTCAGE</DataField>
          <rd:TypeName>System.DateTime</rd:TypeName>
        </Field>
        <Field Name="LASTCAGE">
          <DataField>LASTCAGE</DataField>
          <rd:TypeName>System.DateTime</rd:TypeName>
        </Field>
        <Field Name="DONORS">
          <DataField>DONORS</DataField>
          <rd:TypeName>System.Int32</rd:TypeName>
        </Field>
        <Field Name="DONATIONS">
          <DataField>DONATIONS</DataField>
          <rd:TypeName>System.Int32</rd:TypeName>
        </Field>
        <Field Name="RESPONSE">
          <DataField>RESPONSE</DataField>
          <rd:TypeName>System.Single</rd:TypeName>
        </Field>
        <Field Name="GROSS">
          <DataField>GROSS</DataField>
          <rd:TypeName>System.Decimal</rd:TypeName>
        </Field>
        <Field Name="GPMAIL">
          <DataField>GPMAIL</DataField>
          <rd:TypeName>System.Decimal</rd:TypeName>
        </Field>
        <Field Name="GPDONTN">
          <DataField>GPDONTN</DataField>
          <rd:TypeName>System.Decimal</rd:TypeName>
        </Field>
        <Field Name="COST">
          <DataField>COST</DataField>
          <rd:TypeName>System.Decimal</rd:TypeName>
        </Field>
        <Field Name="CPMAIL">
          <DataField>CPMAIL</DataField>
          <rd:TypeName>System.Decimal</rd:TypeName>
        </Field>
        <Field Name="CPDONTN">
          <DataField>CPDONTN</DataField>
          <rd:TypeName>System.Decimal</rd:TypeName>
        </Field>
        <Field Name="NET">
          <DataField>NET</DataField>
          <rd:TypeName>System.Decimal</rd:TypeName>
        </Field>
        <Field Name="NPMAIL">
          <DataField>NPMAIL</DataField>
          <rd:TypeName>System.Decimal</rd:TypeName>
        </Field>
        <Field Name="NPDONTN">
          <DataField>NPDONTN</DataField>
          <rd:TypeName>System.Decimal</rd:TypeName>
        </Field>
        <Field Name="ROI">
          <DataField>ROI</DataField>
          <rd:TypeName>System.Decimal</rd:TypeName>
        </Field>
      </Fields>
      <Query>
        <DataSourceName>Dexter</DataSourceName>
        <CommandText>SELECT 

--&lt;&lt;&lt;FIELDS&gt;&gt;&gt;,  
P.dtPROGCODE AS PROGTYPE, P.dtPROGDESC AS DESCRIP, 
K.PKGECODE, K.PKGEDESC,
CAST(K.MAILDTE AS BIGINT)*1000000 + K.PKGEID AS K_MDTKEY, 
K.LASTCAGE AS PLastCage,
LISTNOG, LISTNO,
S.SRCECODE, S.SRCEDESC,

--&lt;&lt;&lt;Calculated Fields&gt;&gt;&gt;
MIN(S.MAILDTE) AS MAILDATE, SUM(ISNULL(S.SQTYMAIL,0)) AS MAILQTY, 
MIN(S.FIRSTCAGE) AS FIRSTCAGE, MAX(S.LASTCAGE) AS LASTCAGE,
SUM(ISNULL(S.SPEOPLE,0)) AS DONORS, SUM(ISNULL(S.SMONY,0)) AS DONATIONS, 
'RESPONSE' = CASE WHEN SUM(S.SQTYMAIL)=0 THEN 0 ELSE (CAST(SUM(ISNULL(S.SMONY,0)) AS REAL)/SUM(S.SQTYMAIL))*100 END,
SUM(ISNULL(S.SGROSS,0)) AS GROSS, 
'GPMAIL'   = CASE WHEN SUM(ISNULL(S.SQTYMAIL,0))=0 THEN 0 ELSE SUM(ISNULL(S.SGROSS,0))/SUM(S.SQTYMAIL)END, 
'GPDONTN'  = CASE WHEN SUM(ISNULL(S.SMONY,0))=0 THEN 0 ELSE SUM(ISNULL(S.SGROSS,0))/SUM(S.SMONY) END, 
SUM(ISNULL((( ISNULL(S.COSTPROD1,0)+ISNULL(S.COSTPROD2,0)+ISNULL(S.COSTPROD3,0)+ISNULL(S.COSTPOSTG,0) )*ISNULL(S.SQTYMAIL,0))+( ISNULL(S.COSTRESP1,0)+ISNULL(S.COSTRESP2,0)+ISNULL(S.COSTRESP3,0) )*ISNULL(S.SMONY,0),0)) AS COST,
'CPMAIL'   = CASE WHEN SUM(S.SQTYMAIL)=0 THEN 0 ELSE SUM((( ISNULL(S.COSTPROD1,0)+ISNULL(S.COSTPROD2,0)+ISNULL(S.COSTPROD3,0)+ISNULL(S.COSTPOSTG,0) )*S.SQTYMAIL)+( ISNULL(S.COSTRESP1,0)+ISNULL(S.COSTRESP2,0)+ISNULL(S.COSTRESP3,0) )*ISNULL(S.SMONY,0))/SUM(S.SQTYMAIL) END,
'CPDONTN'  = CASE WHEN SUM(S.SMONY)=0 THEN 0 ELSE SUM((( ISNULL(S.COSTPROD1,0)+ISNULL(S.COSTPROD2,0)+ISNULL(S.COSTPROD3,0)+ISNULL(S.COSTPOSTG,0) )*ISNULL(S.SQTYMAIL,0))+( ISNULL(S.COSTRESP1,0)+ISNULL(S.COSTRESP2,0)+ISNULL(S.COSTRESP3,0) )*S.SMONY)/SUM(S.SMONY) END,
SUM(ISNULL(S.SGROSS,0))-SUM((( ISNULL(S.COSTPROD1,0)+ISNULL(S.COSTPROD2,0)+ISNULL(S.COSTPROD3,0)+ISNULL(S.COSTPOSTG,0) )*ISNULL(S.SQTYMAIL,0))+( ISNULL(S.COSTRESP1,0)+ISNULL(S.COSTRESP2,0)+ISNULL(S.COSTRESP3,0) )*S.SMONY) AS NET,
'NPMAIL'   = CASE WHEN SUM(S.SQTYMAIL)=0 THEN 0 ELSE (SUM(ISNULL(S.SGROSS,0))-SUM((( ISNULL(S.COSTPROD1,0)+ISNULL(S.COSTPROD2,0)+ISNULL(S.COSTPROD3,0)+ISNULL(S.COSTPOSTG,0) )*ISNULL(S.SQTYMAIL,0))+( ISNULL(S.COSTRESP1,0)+ISNULL(S.COSTRESP2,0)+ISNULL(S.COSTRESP3,0) )*ISNULL(S.SMONY,0)))/SUM(S.SQTYMAIL) END,
'NPDONTN'  = CASE WHEN SUM(ISNULL(S.SMONY,0))=0 THEN 0 ELSE (SUM(ISNULL(S.SGROSS,0))-SUM((( ISNULL(S.COSTPROD1,0)+ISNULL(S.COSTPROD2,0)+ISNULL(S.COSTPROD3,0)+ISNULL(S.COSTPOSTG,0) )*S.SQTYMAIL)+( ISNULL(S.COSTRESP1,0)+ISNULL(S.COSTRESP2,0)+ISNULL(S.COSTRESP3,0) )*ISNULL(S.SMONY,0)))/SUM(S.SMONY) END,

--ROI = Gross/Cost
ROI = CASE WHEN SUM(ISNULL((( ISNULL(S.COSTPROD1,0)+ISNULL(S.COSTPROD2,0)+ISNULL(S.COSTPROD3,0)+ISNULL(S.COSTPOSTG,0) )*ISNULL(S.SQTYMAIL,0))+( ISNULL(S.COSTRESP1,0)+ISNULL(S.COSTRESP2,0)+ISNULL(S.COSTRESP3,0) )*ISNULL(S.SMONY,0),0))=0
	THEN 0
	ELSE SUM(ISNULL(S.SGROSS,0))/SUM(ISNULL((( ISNULL(S.COSTPROD1,0)+ISNULL(S.COSTPROD2,0)+ISNULL(S.COSTPROD3,0)+ISNULL(S.COSTPOSTG,0) )*ISNULL(S.SQTYMAIL,0))+( ISNULL(S.COSTRESP1,0)+ISNULL(S.COSTRESP2,0)+ISNULL(S.COSTRESP3,0) )*ISNULL(S.SMONY,0),0)) END

FROM dtPROG P, PACKAGE K, SOURCE S

WHERE P.dtPROGID = K.dtPROGID AND K.PKGEID = S.PKGEID
	AND P.dtPROGID IN  (@Program)
	AND (-1 IN (@Package) OR K.PKGEID IN (@Package))
	AND (@apply_maildate=0 OR (K.MAILDTE BETWEEN @Maildate_From AND @Maildate_To))
	
GROUP BY  
	P.dtPROGCODE, P.dtPROGDESC, 
	K.PKGECODE, K.PKGEDESC,
	CAST(K.MAILDTE AS BIGINT)*1000000 + K.PKGEID, K.LASTCAGE, 
	LISTNOG, LISTNO, 	-- &lt;-- Swappable
	S.SRCECODE, S.SRCEDESC

ORDER BY
	DESCRIP,
	K.PKGECODE,
	--S.LISTNOG, S.LISTNO,
	S.SRCECODE</CommandText>
        <QueryParameters>
          <QueryParameter Name="@Program">
            <Value>=Parameters!Program.Value</Value>
          </QueryParameter>
          <QueryParameter Name="@Package">
            <Value>=Parameters!Package.Value</Value>
          </QueryParameter>
          <QueryParameter Name="@apply_maildate">
            <Value>=Parameters!apply_maildate.Value</Value>
          </QueryParameter>
          <QueryParameter Name="@Maildate_From">
            <Value>=Parameters!Maildate_from.Value</Value>
          </QueryParameter>
          <QueryParameter Name="@Maildate_To">
            <Value>=Parameters!Maildate_To.Value</Value>
          </QueryParameter>
        </QueryParameters>
        <rd:UseGenericDesigner>true</rd:UseGenericDesigner>
      </Query>
    </DataSet>
    <DataSet Name="SystemInfo">
      <Fields>
        <Field Name="ClientName">
          <DataField>ClientName</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="logo_http_path">
          <DataField>logo_http_path</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
      </Fields>
      <Query>
        <DataSourceName>Dexter</DataSourceName>
        <CommandText>SELECT ClientName, logo_http_path FROM ssSYSTEM</CommandText>
        <rd:UseGenericDesigner>true</rd:UseGenericDesigner>
      </Query>
    </DataSet>
    <DataSet Name="Program">
      <Fields>
        <Field Name="PROGID">
          <DataField>PROGID</DataField>
          <rd:TypeName>System.Int32</rd:TypeName>
        </Field>
        <Field Name="DESCRIP">
          <DataField>DESCRIP</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
      </Fields>
      <Query>
        <DataSourceName>Dexter</DataSourceName>
        <CommandText>SELECT dtPROGID AS PROGID, CAST(ISNULL(dtPROGCODE,'') + ' - ' +ISNULL(dtPROGDESC,'') AS VARCHAR(100)) AS DESCRIP FROM dtPROG WHERE dtPROGID&gt;0 ORDER BY DESCRIP</CommandText>
        <rd:UseGenericDesigner>true</rd:UseGenericDesigner>
      </Query>
    </DataSet>
    <DataSet Name="Package">
      <Fields>
        <Field Name="PROGID">
          <DataField>PROGID</DataField>
          <rd:TypeName>System.Int32</rd:TypeName>
        </Field>
        <Field Name="PKGEID">
          <DataField>PKGEID</DataField>
          <rd:TypeName>System.Int32</rd:TypeName>
        </Field>
        <Field Name="PKGEDESC">
          <DataField>PKGEDESC</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
      </Fields>
      <Query>
        <DataSourceName>Dexter</DataSourceName>
        <CommandText>SELECT PROGID, PKGEID, PKGECODE+' - '+ISNULL(PKGEDESC,'') AS PKGEDESC FROM PACKAGE WHERE dtPROGID=@Program 
UNION
SELECT -1, -1, '(Ignore)' AS PKGEDESC
ORDER BY PKGEDESC</CommandText>
        <QueryParameters>
          <QueryParameter Name="@Program">
            <Value>=Parameters!Program.Value</Value>
          </QueryParameter>
        </QueryParameters>
        <rd:UseGenericDesigner>true</rd:UseGenericDesigner>
      </Query>
    </DataSet>
  </DataSets>
  <Code />
  <Width>10.4675in</Width>
  <Body>
    <ReportItems>
      <List Name="list_RespAnalysis">
        <Sorting>
          <SortBy>
            <SortExpression>=Fields!PROGTYPE.Value</SortExpression>
            <Direction>Ascending</Direction>
          </SortBy>
        </Sorting>
        <DataSetName>RespAnalysis</DataSetName>
        <ReportItems>
          <Table Name="tb_RespAnalysis">
            <DataSetName>RespAnalysis</DataSetName>
            <TableGroups>
              <TableGroup>
                <Grouping Name="tb_RespAnalysis_group_Program">
                  <GroupExpressions>
                    <GroupExpression>=Fields!DESCRIP.Value</GroupExpression>
                  </GroupExpressions>
                  <PageBreakAtEnd>true</PageBreakAtEnd>
                </Grouping>
                <Sorting>
                  <SortBy>
                    <SortExpression>=Fields!DESCRIP.Value</SortExpression>
                    <Direction>Ascending</Direction>
                  </SortBy>
                </Sorting>
                <Footer>
                  <TableRows>
                    <TableRow>
                      <TableCells>
                        <TableCell>
                          <ReportItems>
                            <Textbox Name="textbox25">
                              <rd:DefaultName>textbox25</rd:DefaultName>
                              <Style>
                                <BorderColor>
                                  <Default>DarkGray</Default>
                                </BorderColor>
                                <FontFamily>Tahoma</FontFamily>
                                <FontSize>7pt</FontSize>
                                <FontWeight>700</FontWeight>
                                <VerticalAlign>Middle</VerticalAlign>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                              <ZIndex>114</ZIndex>
                              <CanGrow>true</CanGrow>
                              <Value />
                            </Textbox>
                          </ReportItems>
                        </TableCell>
                        <TableCell>
                          <ReportItems>
                            <Textbox Name="textbox26">
                              <rd:DefaultName>textbox26</rd:DefaultName>
                              <Style>
                                <BorderColor>
                                  <Default>DarkGray</Default>
                                </BorderColor>
                                <FontFamily>Tahoma</FontFamily>
                                <FontSize>7pt</FontSize>
                                <FontWeight>700</FontWeight>
                                <VerticalAlign>Middle</VerticalAlign>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                              <ZIndex>113</ZIndex>
                              <CanGrow>true</CanGrow>
                              <Value />
                            </Textbox>
                          </ReportItems>
                        </TableCell>
                        <TableCell>
                          <ReportItems>
                            <Textbox Name="textbox47">
                              <rd:DefaultName>textbox47</rd:DefaultName>
                              <Style>
                                <BorderColor>
                                  <Default>DarkGray</Default>
                                </BorderColor>
                                <FontFamily>Tahoma</FontFamily>
                                <FontSize>7pt</FontSize>
                                <FontWeight>700</FontWeight>
                                <VerticalAlign>Middle</VerticalAlign>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                              <ZIndex>112</ZIndex>
                              <CanGrow>true</CanGrow>
                              <Value />
                            </Textbox>
                          </ReportItems>
                        </TableCell>
                        <TableCell>
                          <ReportItems>
                            <Textbox Name="textbox48">
                              <rd:DefaultName>textbox48</rd:DefaultName>
                              <Style>
                                <BorderColor>
                                  <Default>DarkGray</Default>
                                </BorderColor>
                                <FontFamily>Tahoma</FontFamily>
                                <FontSize>7pt</FontSize>
                                <VerticalAlign>Middle</VerticalAlign>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                              <ZIndex>111</ZIndex>
                              <CanGrow>true</CanGrow>
                              <Value />
                            </Textbox>
                          </ReportItems>
                        </TableCell>
                        <TableCell>
                          <ReportItems>
                            <Textbox Name="textbox80">
                              <rd:DefaultName>textbox80</rd:DefaultName>
                              <Style>
                                <BorderColor>
                                  <Default>DarkGray</Default>
                                </BorderColor>
                                <FontFamily>Tahoma</FontFamily>
                                <FontSize>7pt</FontSize>
                                <Format>n0</Format>
                                <VerticalAlign>Middle</VerticalAlign>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                              <ZIndex>110</ZIndex>
                              <CanGrow>true</CanGrow>
                              <Value />
                            </Textbox>
                          </ReportItems>
                        </TableCell>
                        <TableCell>
                          <ReportItems>
                            <Textbox Name="textbox109">
                              <rd:DefaultName>textbox109</rd:DefaultName>
                              <Style>
                                <BorderColor>
                                  <Default>DarkGray</Default>
                                </BorderColor>
                                <FontFamily>Tahoma</FontFamily>
                                <FontSize>7pt</FontSize>
                                <Format>MM/dd/yy</Format>
                                <VerticalAlign>Middle</VerticalAlign>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                              <ZIndex>109</ZIndex>
                              <CanGrow>true</CanGrow>
                              <Value />
                            </Textbox>
                          </ReportItems>
                        </TableCell>
                        <TableCell>
                          <ReportItems>
                            <Textbox Name="textbox143">
                              <Style>
                                <BorderColor>
                                  <Default>DarkGray</Default>
                                </BorderColor>
                                <FontFamily>Tahoma</FontFamily>
                                <FontSize>7pt</FontSize>
                                <Format>MM/dd/yy</Format>
                                <VerticalAlign>Middle</VerticalAlign>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                              <ZIndex>108</ZIndex>
                              <CanGrow>true</CanGrow>
                              <Value />
                            </Textbox>
                          </ReportItems>
                        </TableCell>
                        <TableCell>
                          <ReportItems>
                            <Textbox Name="textbox113">
                              <rd:DefaultName>textbox113</rd:DefaultName>
                              <Style>
                                <BorderColor>
                                  <Default>DarkGray</Default>
                                </BorderColor>
                                <FontFamily>Tahoma</FontFamily>
                                <FontSize>7pt</FontSize>
                                <Format>n0</Format>
                                <VerticalAlign>Middle</VerticalAlign>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                              <ZIndex>107</ZIndex>
                              <CanGrow>true</CanGrow>
                              <Value />
                            </Textbox>
                          </ReportItems>
                        </TableCell>
                        <TableCell>
                          <ReportItems>
                            <Textbox Name="textbox114">
                              <rd:DefaultName>textbox114</rd:DefaultName>
                              <Style>
                                <BorderColor>
                                  <Default>DarkGray</Default>
                                </BorderColor>
                                <FontFamily>Tahoma</FontFamily>
                                <FontSize>7pt</FontSize>
                                <VerticalAlign>Middle</VerticalAlign>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                              <ZIndex>106</ZIndex>
                              <CanGrow>true</CanGrow>
                              <Value />
                            </Textbox>
                          </ReportItems>
                        </TableCell>
                        <TableCell>
                          <ReportItems>
                            <Textbox Name="textbox117">
                              <rd:DefaultName>textbox117</rd:DefaultName>
                              <Style>
                                <BorderColor>
                                  <Default>DarkGray</Default>
                                </BorderColor>
                                <FontFamily>Tahoma</FontFamily>
                                <FontSize>7pt</FontSize>
                                <VerticalAlign>Middle</VerticalAlign>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                              <ZIndex>105</ZIndex>
                              <CanGrow>true</CanGrow>
                              <Value />
                            </Textbox>
                          </ReportItems>
                        </TableCell>
                        <TableCell>
                          <ReportItems>
                            <Textbox Name="textbox118">
                              <rd:DefaultName>textbox118</rd:DefaultName>
                              <Style>
                                <BorderColor>
                                  <Default>DarkGray</Default>
                                </BorderColor>
                                <FontFamily>Tahoma</FontFamily>
                                <FontSize>7pt</FontSize>
                                <VerticalAlign>Middle</VerticalAlign>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                              <ZIndex>104</ZIndex>
                              <CanGrow>true</CanGrow>
                              <Value />
                            </Textbox>
                          </ReportItems>
                        </TableCell>
                        <TableCell>
                          <ReportItems>
                            <Textbox Name="textbox119">
                              <rd:DefaultName>textbox119</rd:DefaultName>
                              <Style>
                                <BorderColor>
                                  <Default>DarkGray</Default>
                                </BorderColor>
                                <FontFamily>Tahoma</FontFamily>
                                <FontSize>7pt</FontSize>
                                <VerticalAlign>Middle</VerticalAlign>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                              <ZIndex>103</ZIndex>
                              <CanGrow>true</CanGrow>
                              <Value />
                            </Textbox>
                          </ReportItems>
                        </TableCell>
                        <TableCell>
                          <ReportItems>
                            <Textbox Name="textbox120">
                              <rd:DefaultName>textbox120</rd:DefaultName>
                              <Style>
                                <BorderColor>
                                  <Default>DarkGray</Default>
                                </BorderColor>
                                <FontFamily>Tahoma</FontFamily>
                                <FontSize>7pt</FontSize>
                                <VerticalAlign>Middle</VerticalAlign>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                              <ZIndex>102</ZIndex>
                              <CanGrow>true</CanGrow>
                              <Value />
                            </Textbox>
                          </ReportItems>
                        </TableCell>
                        <TableCell>
                          <ReportItems>
                            <Textbox Name="textbox121">
                              <rd:DefaultName>textbox121</rd:DefaultName>
                              <Style>
                                <BorderColor>
                                  <Default>DarkGray</Default>
                                </BorderColor>
                                <FontFamily>Tahoma</FontFamily>
                                <FontSize>7pt</FontSize>
                                <VerticalAlign>Middle</VerticalAlign>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                              <ZIndex>101</ZIndex>
                              <CanGrow>true</CanGrow>
                              <Value />
                            </Textbox>
                          </ReportItems>
                        </TableCell>
                        <TableCell>
                          <ReportItems>
                            <Textbox Name="textbox122">
                              <rd:DefaultName>textbox122</rd:DefaultName>
                              <Style>
                                <BorderColor>
                                  <Default>DarkGray</Default>
                                </BorderColor>
                                <FontFamily>Tahoma</FontFamily>
                                <FontSize>7pt</FontSize>
                                <VerticalAlign>Middle</VerticalAlign>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                              <ZIndex>100</ZIndex>
                              <CanGrow>true</CanGrow>
                              <Value />
                            </Textbox>
                          </ReportItems>
                        </TableCell>
                        <TableCell>
                          <ReportItems>
                            <Textbox Name="textbox123">
                              <rd:DefaultName>textbox123</rd:DefaultName>
                              <Style>
                                <BorderColor>
                                  <Default>DarkGray</Default>
                                </BorderColor>
                                <FontFamily>Tahoma</FontFamily>
                                <FontSize>7pt</FontSize>
                                <VerticalAlign>Middle</VerticalAlign>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                              <ZIndex>99</ZIndex>
                              <CanGrow>true</CanGrow>
                              <Value />
                            </Textbox>
                          </ReportItems>
                        </TableCell>
                        <TableCell>
                          <ReportItems>
                            <Textbox Name="textbox124">
                              <rd:DefaultName>textbox124</rd:DefaultName>
                              <Style>
                                <BorderColor>
                                  <Default>DarkGray</Default>
                                </BorderColor>
                                <FontFamily>Tahoma</FontFamily>
                                <FontSize>7pt</FontSize>
                                <VerticalAlign>Middle</VerticalAlign>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                              <ZIndex>98</ZIndex>
                              <CanGrow>true</CanGrow>
                              <Value />
                            </Textbox>
                          </ReportItems>
                        </TableCell>
                      </TableCells>
                      <Height>0.125in</Height>
                    </TableRow>
                    <TableRow>
                      <TableCells>
                        <TableCell>
                          <ColSpan>3</ColSpan>
                          <ReportItems>
                            <Textbox Name="textbox27">
                              <rd:DefaultName>textbox27</rd:DefaultName>
                              <Style>
                                <BackgroundColor>Tan</BackgroundColor>
                                <BorderColor>
                                  <Default>DarkGray</Default>
                                </BorderColor>
                                <BorderStyle>
                                  <Right>None</Right>
                                  <Bottom>Solid</Bottom>
                                </BorderStyle>
                                <FontFamily>Tahoma</FontFamily>
                                <FontSize>7pt</FontSize>
                                <FontWeight>700</FontWeight>
                                <VerticalAlign>Middle</VerticalAlign>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                              <ZIndex>128</ZIndex>
                              <CanGrow>true</CanGrow>
                              <Value>=("PROGRAM TOTAL:  " + Trim(Fields!PROGTYPE.Value) + " - " + Fields!DESCRIP.Value)</Value>
                            </Textbox>
                          </ReportItems>
                        </TableCell>
                        <TableCell>
                          <ColSpan>2</ColSpan>
                          <ReportItems>
                            <Textbox Name="textbox29">
                              <rd:DefaultName>textbox29</rd:DefaultName>
                              <Style>
                                <BackgroundColor>Tan</BackgroundColor>
                                <BorderColor>
                                  <Default>DarkGray</Default>
                                </BorderColor>
                                <BorderStyle>
                                  <Right>None</Right>
                                  <Bottom>Solid</Bottom>
                                </BorderStyle>
                                <FontFamily>Tahoma</FontFamily>
                                <FontSize>7pt</FontSize>
                                <Format>n0</Format>
                                <TextAlign>Right</TextAlign>
                                <VerticalAlign>Middle</VerticalAlign>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                              <ZIndex>127</ZIndex>
                              <CanGrow>true</CanGrow>
                              <Value>=Sum(Fields!MAILQTY.Value, "tb_RespAnalysis_group_Program")</Value>
                            </Textbox>
                          </ReportItems>
                        </TableCell>
                        <TableCell>
                          <ReportItems>
                            <Textbox Name="textbox31">
                              <rd:DefaultName>textbox31</rd:DefaultName>
                              <Style>
                                <BackgroundColor>Tan</BackgroundColor>
                                <BorderColor>
                                  <Default>DarkGray</Default>
                                </BorderColor>
                                <BorderStyle>
                                  <Right>None</Right>
                                  <Bottom>Solid</Bottom>
                                </BorderStyle>
                                <FontFamily>Tahoma</FontFamily>
                                <FontSize>7pt</FontSize>
                                <Format>MM/dd/yy</Format>
                                <VerticalAlign>Middle</VerticalAlign>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                              <ZIndex>126</ZIndex>
                              <CanGrow>true</CanGrow>
                              <Value>=Min(Fields!FIRSTCAGE.Value, "tb_RespAnalysis_group_Program")</Value>
                            </Textbox>
                          </ReportItems>
                        </TableCell>
                        <TableCell>
                          <ReportItems>
                            <Textbox Name="textbox144">
                              <Style>
                                <BackgroundColor>Tan</BackgroundColor>
                                <BorderColor>
                                  <Default>DarkGray</Default>
                                </BorderColor>
                                <BorderStyle>
                                  <Right>None</Right>
                                  <Bottom>Solid</Bottom>
                                </BorderStyle>
                                <FontFamily>Tahoma</FontFamily>
                                <FontSize>7pt</FontSize>
                                <Format>MM/dd/yy</Format>
                                <VerticalAlign>Middle</VerticalAlign>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                              <ZIndex>125</ZIndex>
                              <CanGrow>true</CanGrow>
                              <Value>=Max(Fields!LASTCAGE.Value, "tb_RespAnalysis_group_Program")</Value>
                            </Textbox>
                          </ReportItems>
                        </TableCell>
                        <TableCell>
                          <ReportItems>
                            <Textbox Name="textbox33">
                              <rd:DefaultName>textbox33</rd:DefaultName>
                              <Style>
                                <BackgroundColor>Tan</BackgroundColor>
                                <BorderColor>
                                  <Default>DarkGray</Default>
                                </BorderColor>
                                <BorderStyle>
                                  <Right>None</Right>
                                  <Bottom>Solid</Bottom>
                                </BorderStyle>
                                <FontFamily>Tahoma</FontFamily>
                                <FontSize>7pt</FontSize>
                                <Format>n0</Format>
                                <VerticalAlign>Middle</VerticalAlign>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                              <ZIndex>124</ZIndex>
                              <CanGrow>true</CanGrow>
                              <Value>=Sum(Fields!DONATIONS.Value, "tb_RespAnalysis_group_Program")</Value>
                            </Textbox>
                          </ReportItems>
                        </TableCell>
                        <TableCell>
                          <ReportItems>
                            <Textbox Name="textbox34">
                              <rd:DefaultName>textbox34</rd:DefaultName>
                              <Style>
                                <BackgroundColor>Tan</BackgroundColor>
                                <BorderColor>
                                  <Default>DarkGray</Default>
                                </BorderColor>
                                <BorderStyle>
                                  <Right>None</Right>
                                  <Bottom>Solid</Bottom>
                                </BorderStyle>
                                <FontFamily>Tahoma</FontFamily>
                                <FontSize>7pt</FontSize>
                                <Format>N2</Format>
                                <VerticalAlign>Middle</VerticalAlign>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                              <ZIndex>123</ZIndex>
                              <CanGrow>true</CanGrow>
                              <Value>=IIF(Sum(Fields!MAILQTY.Value, "tb_RespAnalysis_group_Program") = 0, 
	0,
	Sum(Fields!DONATIONS.Value, "tb_RespAnalysis_group_Program")*100.0/Sum(Fields!MAILQTY.Value, "tb_RespAnalysis_group_Program"))</Value>
                            </Textbox>
                          </ReportItems>
                        </TableCell>
                        <TableCell>
                          <ReportItems>
                            <Textbox Name="textbox35">
                              <rd:DefaultName>textbox35</rd:DefaultName>
                              <ToggleImage>
                                <InitialState>true</InitialState>
                              </ToggleImage>
                              <Style>
                                <BackgroundColor>Tan</BackgroundColor>
                                <BorderColor>
                                  <Default>DarkGray</Default>
                                </BorderColor>
                                <BorderStyle>
                                  <Right>None</Right>
                                  <Bottom>Solid</Bottom>
                                </BorderStyle>
                                <FontFamily>Tahoma</FontFamily>
                                <FontSize>7pt</FontSize>
                                <Format>n2</Format>
                                <VerticalAlign>Middle</VerticalAlign>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                              <ZIndex>122</ZIndex>
                              <CanGrow>true</CanGrow>
                              <Value>=Sum(Fields!GROSS.Value, "tb_RespAnalysis_group_Program")</Value>
                            </Textbox>
                          </ReportItems>
                        </TableCell>
                        <TableCell>
                          <ReportItems>
                            <Textbox Name="textbox36">
                              <rd:DefaultName>textbox36</rd:DefaultName>
                              <Style>
                                <BackgroundColor>Tan</BackgroundColor>
                                <BorderColor>
                                  <Default>DarkGray</Default>
                                </BorderColor>
                                <BorderStyle>
                                  <Right>None</Right>
                                  <Bottom>Solid</Bottom>
                                </BorderStyle>
                                <FontFamily>Tahoma</FontFamily>
                                <FontSize>7pt</FontSize>
                                <Format>n2</Format>
                                <VerticalAlign>Middle</VerticalAlign>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                              <ZIndex>121</ZIndex>
                              <CanGrow>true</CanGrow>
                              <Value>=Sum(Fields!COST.Value, "tb_RespAnalysis_group_Program")</Value>
                            </Textbox>
                          </ReportItems>
                        </TableCell>
                        <TableCell>
                          <ReportItems>
                            <Textbox Name="textbox37">
                              <rd:DefaultName>textbox37</rd:DefaultName>
                              <Style>
                                <BackgroundColor>Tan</BackgroundColor>
                                <BorderColor>
                                  <Default>DarkGray</Default>
                                </BorderColor>
                                <BorderStyle>
                                  <Right>None</Right>
                                  <Bottom>Solid</Bottom>
                                </BorderStyle>
                                <FontFamily>Tahoma</FontFamily>
                                <FontSize>7pt</FontSize>
                                <Format>n2</Format>
                                <VerticalAlign>Middle</VerticalAlign>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                              <ZIndex>120</ZIndex>
                              <CanGrow>true</CanGrow>
                              <Value>=Sum(Fields!NET.Value, "tb_RespAnalysis_group_Program")</Value>
                            </Textbox>
                          </ReportItems>
                        </TableCell>
                        <TableCell>
                          <ReportItems>
                            <Textbox Name="textbox38">
                              <rd:DefaultName>textbox38</rd:DefaultName>
                              <Style>
                                <BackgroundColor>Tan</BackgroundColor>
                                <BorderColor>
                                  <Default>DarkGray</Default>
                                </BorderColor>
                                <BorderStyle>
                                  <Right>None</Right>
                                  <Bottom>Solid</Bottom>
                                </BorderStyle>
                                <FontFamily>Tahoma</FontFamily>
                                <FontSize>7pt</FontSize>
                                <Format>n2</Format>
                                <VerticalAlign>Middle</VerticalAlign>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                              <ZIndex>119</ZIndex>
                              <CanGrow>true</CanGrow>
                              <Value>=IIF(Sum(Fields!MAILQTY.Value, "tb_RespAnalysis_group_Program") = 0, 
	0,
	Sum(Fields!GROSS.Value, "tb_RespAnalysis_group_Program")/Sum(Fields!MAILQTY.Value, "tb_RespAnalysis_group_Program"))</Value>
                            </Textbox>
                          </ReportItems>
                        </TableCell>
                        <TableCell>
                          <ReportItems>
                            <Textbox Name="textbox39">
                              <rd:DefaultName>textbox39</rd:DefaultName>
                              <Style>
                                <BackgroundColor>Tan</BackgroundColor>
                                <BorderColor>
                                  <Default>DarkGray</Default>
                                </BorderColor>
                                <BorderStyle>
                                  <Right>None</Right>
                                  <Bottom>Solid</Bottom>
                                </BorderStyle>
                                <FontFamily>Tahoma</FontFamily>
                                <FontSize>7pt</FontSize>
                                <Format>n2</Format>
                                <VerticalAlign>Middle</VerticalAlign>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                              <ZIndex>118</ZIndex>
                              <CanGrow>true</CanGrow>
                              <Value>=IIF(Sum(Fields!MAILQTY.Value, "tb_RespAnalysis_group_Program") = 0, 
	0,
	Sum(Fields!COST.Value, "tb_RespAnalysis_group_Program")/Sum(Fields!MAILQTY.Value, "tb_RespAnalysis_group_Program"))</Value>
                            </Textbox>
                          </ReportItems>
                        </TableCell>
                        <TableCell>
                          <ReportItems>
                            <Textbox Name="textbox40">
                              <rd:DefaultName>textbox40</rd:DefaultName>
                              <Style>
                                <BackgroundColor>Tan</BackgroundColor>
                                <BorderColor>
                                  <Default>DarkGray</Default>
                                </BorderColor>
                                <BorderStyle>
                                  <Right>None</Right>
                                  <Bottom>Solid</Bottom>
                                </BorderStyle>
                                <FontFamily>Tahoma</FontFamily>
                                <FontSize>7pt</FontSize>
                                <Format>n2</Format>
                                <VerticalAlign>Middle</VerticalAlign>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                              <ZIndex>117</ZIndex>
                              <CanGrow>true</CanGrow>
                              <Value>=IIF(Sum(Fields!MAILQTY.Value, "tb_RespAnalysis_group_Program") = 0, 
	0,
	Sum(Fields!NET.Value, "tb_RespAnalysis_group_Program")/Sum(Fields!MAILQTY.Value, "tb_RespAnalysis_group_Program"))</Value>
                            </Textbox>
                          </ReportItems>
                        </TableCell>
                        <TableCell>
                          <ReportItems>
                            <Textbox Name="textbox41">
                              <rd:DefaultName>textbox41</rd:DefaultName>
                              <Style>
                                <BackgroundColor>Tan</BackgroundColor>
                                <BorderColor>
                                  <Default>DarkGray</Default>
                                </BorderColor>
                                <BorderStyle>
                                  <Right>None</Right>
                                  <Bottom>Solid</Bottom>
                                </BorderStyle>
                                <FontFamily>Tahoma</FontFamily>
                                <FontSize>7pt</FontSize>
                                <Format>n2</Format>
                                <VerticalAlign>Middle</VerticalAlign>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                              <ZIndex>116</ZIndex>
                              <CanGrow>true</CanGrow>
                              <Value>=IIF(Sum(Fields!DONATIONS.Value, "tb_RespAnalysis_group_Program") = 0, 
	0,
	Sum(Fields!GROSS.Value, "tb_RespAnalysis_group_Program")/Sum(Fields!DONATIONS.Value, "tb_RespAnalysis_group_Program"))</Value>
                            </Textbox>
                          </ReportItems>
                        </TableCell>
                        <TableCell>
                          <ReportItems>
                            <Textbox Name="textbox100">
                              <rd:DefaultName>textbox100</rd:DefaultName>
                              <Style>
                                <BackgroundColor>Tan</BackgroundColor>
                                <BorderColor>
                                  <Default>DarkGray</Default>
                                </BorderColor>
                                <BorderStyle>
                                  <Right>Solid</Right>
                                  <Bottom>Solid</Bottom>
                                </BorderStyle>
                                <FontFamily>Tahoma</FontFamily>
                                <FontSize>7pt</FontSize>
                                <Format>n2</Format>
                                <VerticalAlign>Middle</VerticalAlign>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                              <ZIndex>115</ZIndex>
                              <CanGrow>true</CanGrow>
                              <Value>=IIF(Sum(Fields!COST.Value, "tb_RespAnalysis_group_Program") = 0, 
	0,
	Sum(Fields!GROSS.Value, "tb_RespAnalysis_group_Program")*1.0/Sum(Fields!COST.Value, "tb_RespAnalysis_group_Program"))</Value>
                            </Textbox>
                          </ReportItems>
                        </TableCell>
                      </TableCells>
                      <Height>0.31509in</Height>
                    </TableRow>
                  </TableRows>
                </Footer>
              </TableGroup>
              <TableGroup>
                <Grouping Name="tb_RespAnalysis_Group_page_breaker">
                  <GroupExpressions>
                    <GroupExpression>=IIF(Parameters!show_details.Value=True AND Parameters!show_subtotal_PKGE.Value=True, Fields!PKGECODE.Value, "")</GroupExpression>
                  </GroupExpressions>
                  <PageBreakAtStart>true</PageBreakAtStart>
                </Grouping>
                <Footer>
                  <TableRows>
                    <TableRow>
                      <TableCells>
                        <TableCell>
                          <ReportItems>
                            <Textbox Name="textbox177">
                              <rd:DefaultName>textbox177</rd:DefaultName>
                              <Style>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                              <ZIndex>97</ZIndex>
                              <CanGrow>true</CanGrow>
                              <Value />
                            </Textbox>
                          </ReportItems>
                        </TableCell>
                        <TableCell>
                          <ReportItems>
                            <Textbox Name="textbox178">
                              <rd:DefaultName>textbox178</rd:DefaultName>
                              <Style>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                              <ZIndex>96</ZIndex>
                              <CanGrow>true</CanGrow>
                              <Value />
                            </Textbox>
                          </ReportItems>
                        </TableCell>
                        <TableCell>
                          <ReportItems>
                            <Textbox Name="textbox179">
                              <rd:DefaultName>textbox179</rd:DefaultName>
                              <Style>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                              <ZIndex>95</ZIndex>
                              <CanGrow>true</CanGrow>
                              <Value />
                            </Textbox>
                          </ReportItems>
                        </TableCell>
                        <TableCell>
                          <ReportItems>
                            <Textbox Name="textbox180">
                              <rd:DefaultName>textbox180</rd:DefaultName>
                              <Style>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                              <ZIndex>94</ZIndex>
                              <CanGrow>true</CanGrow>
                              <Value />
                            </Textbox>
                          </ReportItems>
                        </TableCell>
                        <TableCell>
                          <ReportItems>
                            <Textbox Name="textbox181">
                              <rd:DefaultName>textbox181</rd:DefaultName>
                              <Style>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                              <ZIndex>93</ZIndex>
                              <CanGrow>true</CanGrow>
                              <Value />
                            </Textbox>
                          </ReportItems>
                        </TableCell>
                        <TableCell>
                          <ReportItems>
                            <Textbox Name="textbox182">
                              <rd:DefaultName>textbox182</rd:DefaultName>
                              <Style>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                              <ZIndex>92</ZIndex>
                              <CanGrow>true</CanGrow>
                              <Value />
                            </Textbox>
                          </ReportItems>
                        </TableCell>
                        <TableCell>
                          <ReportItems>
                            <Textbox Name="textbox183">
                              <rd:DefaultName>textbox183</rd:DefaultName>
                              <Style>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                              <ZIndex>91</ZIndex>
                              <CanGrow>true</CanGrow>
                              <Value />
                            </Textbox>
                          </ReportItems>
                        </TableCell>
                        <TableCell>
                          <ReportItems>
                            <Textbox Name="textbox184">
                              <rd:DefaultName>textbox184</rd:DefaultName>
                              <Style>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                              <ZIndex>90</ZIndex>
                              <CanGrow>true</CanGrow>
                              <Value />
                            </Textbox>
                          </ReportItems>
                        </TableCell>
                        <TableCell>
                          <ReportItems>
                            <Textbox Name="textbox185">
                              <rd:DefaultName>textbox185</rd:DefaultName>
                              <Style>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                              <ZIndex>89</ZIndex>
                              <CanGrow>true</CanGrow>
                              <Value />
                            </Textbox>
                          </ReportItems>
                        </TableCell>
                        <TableCell>
                          <ReportItems>
                            <Textbox Name="textbox186">
                              <rd:DefaultName>textbox186</rd:DefaultName>
                              <Style>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                              <ZIndex>88</ZIndex>
                              <CanGrow>true</CanGrow>
                              <Value />
                            </Textbox>
                          </ReportItems>
                        </TableCell>
                        <TableCell>
                          <ReportItems>
                            <Textbox Name="textbox187">
                              <rd:DefaultName>textbox187</rd:DefaultName>
                              <Style>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                              <ZIndex>87</ZIndex>
                              <CanGrow>true</CanGrow>
                              <Value />
                            </Textbox>
                          </ReportItems>
                        </TableCell>
                        <TableCell>
                          <ReportItems>
                            <Textbox Name="textbox188">
                              <rd:DefaultName>textbox188</rd:DefaultName>
                              <Style>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                              <ZIndex>86</ZIndex>
                              <CanGrow>true</CanGrow>
                              <Value />
                            </Textbox>
                          </ReportItems>
                        </TableCell>
                        <TableCell>
                          <ReportItems>
                            <Textbox Name="textbox189">
                              <rd:DefaultName>textbox189</rd:DefaultName>
                              <Style>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                              <ZIndex>85</ZIndex>
                              <CanGrow>true</CanGrow>
                              <Value />
                            </Textbox>
                          </ReportItems>
                        </TableCell>
                        <TableCell>
                          <ReportItems>
                            <Textbox Name="textbox190">
                              <rd:DefaultName>textbox190</rd:DefaultName>
                              <Style>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                              <ZIndex>84</ZIndex>
                              <CanGrow>true</CanGrow>
                              <Value />
                            </Textbox>
                          </ReportItems>
                        </TableCell>
                        <TableCell>
                          <ReportItems>
                            <Textbox Name="textbox191">
                              <rd:DefaultName>textbox191</rd:DefaultName>
                              <Style>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                              <ZIndex>83</ZIndex>
                              <CanGrow>true</CanGrow>
                              <Value />
                            </Textbox>
                          </ReportItems>
                        </TableCell>
                        <TableCell>
                          <ReportItems>
                            <Textbox Name="textbox192">
                              <rd:DefaultName>textbox192</rd:DefaultName>
                              <Style>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                              <ZIndex>82</ZIndex>
                              <CanGrow>true</CanGrow>
                              <Value />
                            </Textbox>
                          </ReportItems>
                        </TableCell>
                        <TableCell>
                          <ReportItems>
                            <Textbox Name="textbox193">
                              <rd:DefaultName>textbox193</rd:DefaultName>
                              <Style>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                              <ZIndex>81</ZIndex>
                              <CanGrow>true</CanGrow>
                              <Value />
                            </Textbox>
                          </ReportItems>
                        </TableCell>
                      </TableCells>
                      <Height>0.0625in</Height>
                    </TableRow>
                  </TableRows>
                </Footer>
              </TableGroup>
              <TableGroup>
                <Grouping Name="tb_RespAnalysis_group_Package">
                  <GroupExpressions>
                    <GroupExpression>=IIF(Parameters!show_details.Value=False or Parameters!show_subtotal_PKGE.Value=True, Fields!PKGECODE.Value, "")</GroupExpression>
                  </GroupExpressions>
                </Grouping>
                <Sorting>
                  <SortBy>
                    <SortExpression>=Fields!PKGECODE.Value</SortExpression>
                    <Direction>Ascending</Direction>
                  </SortBy>
                </Sorting>
                <Header>
                  <TableRows>
                    <TableRow>
                      <Visibility>
                        <Hidden>=(Parameters!show_details.Value=False OR Parameters!show_subtotal_PKGE.Value=False)</Hidden>
                      </Visibility>
                      <TableCells>
                        <TableCell>
                          <ColSpan>5</ColSpan>
                          <ReportItems>
                            <Textbox Name="textbox5">
                              <rd:DefaultName>textbox5</rd:DefaultName>
                              <Style>
                                <FontFamily>Tahoma</FontFamily>
                                <FontSize>7pt</FontSize>
                                <VerticalAlign>Middle</VerticalAlign>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                              <ZIndex>141</ZIndex>
                              <CanGrow>true</CanGrow>
                              <Value>=("····· PACKAGE:  " + RTrim(Fields!PKGECODE.Value)+" - "+RTrim(Fields!PKGEDESC.Value) + " ·····")</Value>
                            </Textbox>
                          </ReportItems>
                        </TableCell>
                        <TableCell>
                          <ReportItems>
                            <Textbox Name="textbox194">
                              <rd:DefaultName>textbox194</rd:DefaultName>
                              <Style>
                                <FontFamily>Tahoma</FontFamily>
                                <FontSize>7pt</FontSize>
                                <VerticalAlign>Middle</VerticalAlign>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                              <ZIndex>140</ZIndex>
                              <CanGrow>true</CanGrow>
                              <Value />
                            </Textbox>
                          </ReportItems>
                        </TableCell>
                        <TableCell>
                          <ReportItems>
                            <Textbox Name="textbox195">
                              <rd:DefaultName>textbox195</rd:DefaultName>
                              <Style>
                                <FontFamily>Tahoma</FontFamily>
                                <FontSize>7pt</FontSize>
                                <VerticalAlign>Middle</VerticalAlign>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                              <ZIndex>139</ZIndex>
                              <CanGrow>true</CanGrow>
                              <Value />
                            </Textbox>
                          </ReportItems>
                        </TableCell>
                        <TableCell>
                          <ReportItems>
                            <Textbox Name="textbox196">
                              <rd:DefaultName>textbox196</rd:DefaultName>
                              <Style>
                                <FontFamily>Tahoma</FontFamily>
                                <FontSize>7pt</FontSize>
                                <VerticalAlign>Middle</VerticalAlign>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                              <ZIndex>138</ZIndex>
                              <CanGrow>true</CanGrow>
                              <Value />
                            </Textbox>
                          </ReportItems>
                        </TableCell>
                        <TableCell>
                          <ReportItems>
                            <Textbox Name="textbox197">
                              <rd:DefaultName>textbox197</rd:DefaultName>
                              <Style>
                                <FontFamily>Tahoma</FontFamily>
                                <FontSize>7pt</FontSize>
                                <VerticalAlign>Middle</VerticalAlign>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                              <ZIndex>137</ZIndex>
                              <CanGrow>true</CanGrow>
                              <Value />
                            </Textbox>
                          </ReportItems>
                        </TableCell>
                        <TableCell>
                          <ReportItems>
                            <Textbox Name="textbox198">
                              <rd:DefaultName>textbox198</rd:DefaultName>
                              <Style>
                                <FontFamily>Tahoma</FontFamily>
                                <FontSize>7pt</FontSize>
                                <VerticalAlign>Middle</VerticalAlign>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                              <ZIndex>136</ZIndex>
                              <CanGrow>true</CanGrow>
                              <Value />
                            </Textbox>
                          </ReportItems>
                        </TableCell>
                        <TableCell>
                          <ReportItems>
                            <Textbox Name="textbox199">
                              <rd:DefaultName>textbox199</rd:DefaultName>
                              <Style>
                                <FontFamily>Tahoma</FontFamily>
                                <FontSize>7pt</FontSize>
                                <VerticalAlign>Middle</VerticalAlign>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                              <ZIndex>135</ZIndex>
                              <CanGrow>true</CanGrow>
                              <Value />
                            </Textbox>
                          </ReportItems>
                        </TableCell>
                        <TableCell>
                          <ReportItems>
                            <Textbox Name="textbox200">
                              <rd:DefaultName>textbox200</rd:DefaultName>
                              <Style>
                                <FontFamily>Tahoma</FontFamily>
                                <FontSize>7pt</FontSize>
                                <VerticalAlign>Middle</VerticalAlign>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                              <ZIndex>134</ZIndex>
                              <CanGrow>true</CanGrow>
                              <Value />
                            </Textbox>
                          </ReportItems>
                        </TableCell>
                        <TableCell>
                          <ReportItems>
                            <Textbox Name="textbox201">
                              <rd:DefaultName>textbox201</rd:DefaultName>
                              <Style>
                                <FontFamily>Tahoma</FontFamily>
                                <FontSize>7pt</FontSize>
                                <VerticalAlign>Middle</VerticalAlign>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                              <ZIndex>133</ZIndex>
                              <CanGrow>true</CanGrow>
                              <Value />
                            </Textbox>
                          </ReportItems>
                        </TableCell>
                        <TableCell>
                          <ReportItems>
                            <Textbox Name="textbox202">
                              <rd:DefaultName>textbox202</rd:DefaultName>
                              <Style>
                                <FontFamily>Tahoma</FontFamily>
                                <FontSize>7pt</FontSize>
                                <VerticalAlign>Middle</VerticalAlign>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                              <ZIndex>132</ZIndex>
                              <CanGrow>true</CanGrow>
                              <Value />
                            </Textbox>
                          </ReportItems>
                        </TableCell>
                        <TableCell>
                          <ReportItems>
                            <Textbox Name="textbox203">
                              <rd:DefaultName>textbox203</rd:DefaultName>
                              <Style>
                                <FontFamily>Tahoma</FontFamily>
                                <FontSize>7pt</FontSize>
                                <VerticalAlign>Middle</VerticalAlign>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                              <ZIndex>131</ZIndex>
                              <CanGrow>true</CanGrow>
                              <Value />
                            </Textbox>
                          </ReportItems>
                        </TableCell>
                        <TableCell>
                          <ReportItems>
                            <Textbox Name="textbox204">
                              <rd:DefaultName>textbox204</rd:DefaultName>
                              <Style>
                                <FontFamily>Tahoma</FontFamily>
                                <FontSize>7pt</FontSize>
                                <VerticalAlign>Middle</VerticalAlign>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                              <ZIndex>130</ZIndex>
                              <CanGrow>true</CanGrow>
                              <Value />
                            </Textbox>
                          </ReportItems>
                        </TableCell>
                        <TableCell>
                          <ReportItems>
                            <Textbox Name="textbox205">
                              <rd:DefaultName>textbox205</rd:DefaultName>
                              <Style>
                                <FontFamily>Tahoma</FontFamily>
                                <FontSize>7pt</FontSize>
                                <VerticalAlign>Middle</VerticalAlign>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                              <ZIndex>129</ZIndex>
                              <CanGrow>true</CanGrow>
                              <Value />
                            </Textbox>
                          </ReportItems>
                        </TableCell>
                      </TableCells>
                      <Height>0.1625in</Height>
                    </TableRow>
                  </TableRows>
                  <RepeatOnNewPage>true</RepeatOnNewPage>
                </Header>
                <Footer>
                  <TableRows>
                    <TableRow>
                      <Visibility>
                        <Hidden>=(Parameters!show_details.Value=True AND Parameters!show_subtotal_PKGE.Value=False)</Hidden>
                      </Visibility>
                      <TableCells>
                        <TableCell>
                          <ReportItems>
                            <Textbox Name="tx_PKGECODE">
                              <Style>
                                <BackgroundColor>=IIF(Parameters!show_details.Value=True, "LightSalmon", "Transparent")</BackgroundColor>
                                <BorderColor>
                                  <Default>DimGray</Default>
                                </BorderColor>
                                <BorderStyle>
                                  <Bottom>=IIF(Parameters!show_details.Value=True,"Solid","None")</Bottom>
                                </BorderStyle>
                                <FontFamily>Tahoma</FontFamily>
                                <FontSize>7pt</FontSize>
                                <FontWeight>=IIF(Parameters!show_details.Value=TRUE, "Bold", "")</FontWeight>
                                <VerticalAlign>Middle</VerticalAlign>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                              <ZIndex>80</ZIndex>
                              <CanGrow>true</CanGrow>
                              <Value>=Fields!PKGECODE.Value</Value>
                            </Textbox>
                          </ReportItems>
                        </TableCell>
                        <TableCell>
                          <ReportItems>
                            <Textbox Name="textbox175">
                              <rd:DefaultName>textbox175</rd:DefaultName>
                              <Style>
                                <BackgroundColor>=IIF(Parameters!show_details.Value=True, "LightSalmon", "Transparent")</BackgroundColor>
                                <BorderColor>
                                  <Default>DimGray</Default>
                                </BorderColor>
                                <BorderStyle>
                                  <Bottom>=IIF(Parameters!show_details.Value=True,"Solid","None")</Bottom>
                                </BorderStyle>
                                <FontFamily>Tahoma</FontFamily>
                                <FontSize>7pt</FontSize>
                                <FontWeight>700</FontWeight>
                                <VerticalAlign>Middle</VerticalAlign>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                              <ZIndex>79</ZIndex>
                              <CanGrow>true</CanGrow>
                              <Value />
                            </Textbox>
                          </ReportItems>
                        </TableCell>
                        <TableCell>
                          <ReportItems>
                            <Textbox Name="tx_PKGEDESC">
                              <Style>
                                <BackgroundColor>=IIF(Parameters!show_details.Value=True, "LightSalmon", "Transparent")</BackgroundColor>
                                <BorderColor>
                                  <Default>DimGray</Default>
                                </BorderColor>
                                <BorderStyle>
                                  <Bottom>=IIF(Parameters!show_details.Value=True,"Solid","None")</Bottom>
                                </BorderStyle>
                                <FontFamily>Tahoma</FontFamily>
                                <FontSize>7pt</FontSize>
                                <FontWeight>=IIF(Parameters!show_details.Value=TRUE, "Bold", "")</FontWeight>
                                <VerticalAlign>Middle</VerticalAlign>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                              <ZIndex>78</ZIndex>
                              <CanGrow>true</CanGrow>
                              <Value>=Fields!PKGEDESC.Value</Value>
                            </Textbox>
                          </ReportItems>
                        </TableCell>
                        <TableCell>
                          <ReportItems>
                            <Textbox Name="textbox115">
                              <rd:DefaultName>textbox115</rd:DefaultName>
                              <Style>
                                <BackgroundColor>=IIF(Parameters!show_details.Value=True, "LightSalmon", "Transparent")</BackgroundColor>
                                <BorderColor>
                                  <Default>DimGray</Default>
                                </BorderColor>
                                <BorderStyle>
                                  <Bottom>=IIF(Parameters!show_details.Value=True,"Solid","None")</Bottom>
                                </BorderStyle>
                                <FontFamily>Tahoma</FontFamily>
                                <FontSize>7pt</FontSize>
                                <Format>MM/dd/yy</Format>
                                <VerticalAlign>Middle</VerticalAlign>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                              <ZIndex>77</ZIndex>
                              <CanGrow>true</CanGrow>
                              <Value>=Min(Fields!MAILDATE.Value, "tb_RespAnalysis_group_Package")</Value>
                            </Textbox>
                          </ReportItems>
                        </TableCell>
                        <TableCell>
                          <ReportItems>
                            <Textbox Name="textbox116">
                              <rd:DefaultName>textbox116</rd:DefaultName>
                              <Style>
                                <BackgroundColor>=IIF(Parameters!show_details.Value=True, "LightSalmon", "Transparent")</BackgroundColor>
                                <BorderColor>
                                  <Default>DimGray</Default>
                                </BorderColor>
                                <BorderStyle>
                                  <Bottom>=IIF(Parameters!show_details.Value=True,"Solid","None")</Bottom>
                                </BorderStyle>
                                <FontFamily>Tahoma</FontFamily>
                                <FontSize>7pt</FontSize>
                                <Format>n0</Format>
                                <VerticalAlign>Middle</VerticalAlign>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                              <ZIndex>76</ZIndex>
                              <CanGrow>true</CanGrow>
                              <Value>=Sum(Fields!MAILQTY.Value, "tb_RespAnalysis_group_Package")</Value>
                            </Textbox>
                          </ReportItems>
                        </TableCell>
                        <TableCell>
                          <ReportItems>
                            <Textbox Name="textbox88">
                              <rd:DefaultName>textbox88</rd:DefaultName>
                              <Style>
                                <BackgroundColor>=IIF(Parameters!show_details.Value=True, "LightSalmon", "Transparent")</BackgroundColor>
                                <BorderColor>
                                  <Default>DimGray</Default>
                                </BorderColor>
                                <BorderStyle>
                                  <Bottom>=IIF(Parameters!show_details.Value=True,"Solid","None")</Bottom>
                                </BorderStyle>
                                <FontFamily>Tahoma</FontFamily>
                                <FontSize>7pt</FontSize>
                                <Format>MM/dd/yy</Format>
                                <VerticalAlign>Middle</VerticalAlign>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                              <ZIndex>75</ZIndex>
                              <CanGrow>true</CanGrow>
                              <Value>=Min(Fields!FIRSTCAGE.Value, "tb_RespAnalysis_group_Package")</Value>
                            </Textbox>
                          </ReportItems>
                        </TableCell>
                        <TableCell>
                          <ReportItems>
                            <Textbox Name="textbox142">
                              <Style>
                                <BackgroundColor>=IIF(Parameters!show_details.Value=True, "LightSalmon", "Transparent")</BackgroundColor>
                                <BorderColor>
                                  <Default>DimGray</Default>
                                </BorderColor>
                                <BorderStyle>
                                  <Bottom>=IIF(Parameters!show_details.Value=True,"Solid","None")</Bottom>
                                </BorderStyle>
                                <FontFamily>Tahoma</FontFamily>
                                <FontSize>7pt</FontSize>
                                <Format>MM/dd/yy</Format>
                                <VerticalAlign>Middle</VerticalAlign>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                              <ZIndex>74</ZIndex>
                              <CanGrow>true</CanGrow>
                              <Value>=Max(Fields!LASTCAGE.Value, "tb_RespAnalysis_group_Package")</Value>
                            </Textbox>
                          </ReportItems>
                        </TableCell>
                        <TableCell>
                          <ReportItems>
                            <Textbox Name="textbox90">
                              <rd:DefaultName>textbox90</rd:DefaultName>
                              <Style>
                                <BackgroundColor>=IIF(Parameters!show_details.Value=True, "LightSalmon", "Transparent")</BackgroundColor>
                                <BorderColor>
                                  <Default>DimGray</Default>
                                </BorderColor>
                                <BorderStyle>
                                  <Bottom>=IIF(Parameters!show_details.Value=True,"Solid","None")</Bottom>
                                </BorderStyle>
                                <FontFamily>Tahoma</FontFamily>
                                <FontSize>7pt</FontSize>
                                <Format>n0</Format>
                                <VerticalAlign>Middle</VerticalAlign>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                              <ZIndex>73</ZIndex>
                              <CanGrow>true</CanGrow>
                              <Value>=Sum(Fields!DONATIONS.Value, "tb_RespAnalysis_group_Package")</Value>
                            </Textbox>
                          </ReportItems>
                        </TableCell>
                        <TableCell>
                          <ReportItems>
                            <Textbox Name="textbox91">
                              <rd:DefaultName>textbox91</rd:DefaultName>
                              <Style>
                                <BackgroundColor>=IIF(Parameters!show_details.Value=True, "LightSalmon", "Transparent")</BackgroundColor>
                                <BorderColor>
                                  <Default>DimGray</Default>
                                </BorderColor>
                                <BorderStyle>
                                  <Bottom>=IIF(Parameters!show_details.Value=True,"Solid","None")</Bottom>
                                </BorderStyle>
                                <FontFamily>Tahoma</FontFamily>
                                <FontSize>7pt</FontSize>
                                <Format>N2</Format>
                                <VerticalAlign>Middle</VerticalAlign>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                              <ZIndex>72</ZIndex>
                              <CanGrow>true</CanGrow>
                              <Value>=IIF(Sum(Fields!MAILQTY.Value, "tb_RespAnalysis_group_Package") = 0, 
	0,
	Sum(Fields!DONATIONS.Value, "tb_RespAnalysis_group_Package")*100.0/Sum(Fields!MAILQTY.Value, "tb_RespAnalysis_group_Package"))</Value>
                            </Textbox>
                          </ReportItems>
                        </TableCell>
                        <TableCell>
                          <ReportItems>
                            <Textbox Name="textbox92">
                              <rd:DefaultName>textbox92</rd:DefaultName>
                              <ToggleImage>
                                <InitialState>true</InitialState>
                              </ToggleImage>
                              <Style>
                                <BackgroundColor>=IIF(Parameters!show_details.Value=True, "LightSalmon", "Transparent")</BackgroundColor>
                                <BorderColor>
                                  <Default>DimGray</Default>
                                </BorderColor>
                                <BorderStyle>
                                  <Bottom>=IIF(Parameters!show_details.Value=True,"Solid","None")</Bottom>
                                </BorderStyle>
                                <FontFamily>Tahoma</FontFamily>
                                <FontSize>7pt</FontSize>
                                <Format>n2</Format>
                                <VerticalAlign>Middle</VerticalAlign>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                              <ZIndex>71</ZIndex>
                              <CanGrow>true</CanGrow>
                              <Value>=Sum(Fields!GROSS.Value, "tb_RespAnalysis_group_Package")</Value>
                            </Textbox>
                          </ReportItems>
                        </TableCell>
                        <TableCell>
                          <ReportItems>
                            <Textbox Name="textbox93">
                              <rd:DefaultName>textbox93</rd:DefaultName>
                              <Style>
                                <BackgroundColor>=IIF(Parameters!show_details.Value=True, "LightSalmon", "Transparent")</BackgroundColor>
                                <BorderColor>
                                  <Default>DimGray</Default>
                                </BorderColor>
                                <BorderStyle>
                                  <Bottom>=IIF(Parameters!show_details.Value=True,"Solid","None")</Bottom>
                                </BorderStyle>
                                <FontFamily>Tahoma</FontFamily>
                                <FontSize>7pt</FontSize>
                                <Format>n2</Format>
                                <VerticalAlign>Middle</VerticalAlign>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                              <ZIndex>70</ZIndex>
                              <CanGrow>true</CanGrow>
                              <Value>=Sum(Fields!COST.Value, "tb_RespAnalysis_group_Package")</Value>
                            </Textbox>
                          </ReportItems>
                        </TableCell>
                        <TableCell>
                          <ReportItems>
                            <Textbox Name="textbox94">
                              <rd:DefaultName>textbox94</rd:DefaultName>
                              <Style>
                                <BackgroundColor>=IIF(Parameters!show_details.Value=True, "LightSalmon", "Transparent")</BackgroundColor>
                                <BorderColor>
                                  <Default>DimGray</Default>
                                </BorderColor>
                                <BorderStyle>
                                  <Bottom>=IIF(Parameters!show_details.Value=True,"Solid","None")</Bottom>
                                </BorderStyle>
                                <FontFamily>Tahoma</FontFamily>
                                <FontSize>7pt</FontSize>
                                <Format>n2</Format>
                                <VerticalAlign>Middle</VerticalAlign>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                              <ZIndex>69</ZIndex>
                              <CanGrow>true</CanGrow>
                              <Value>=Sum(Fields!NET.Value, "tb_RespAnalysis_group_Package")</Value>
                            </Textbox>
                          </ReportItems>
                        </TableCell>
                        <TableCell>
                          <ReportItems>
                            <Textbox Name="textbox95">
                              <rd:DefaultName>textbox95</rd:DefaultName>
                              <Style>
                                <BackgroundColor>=IIF(Parameters!show_details.Value=True, "LightSalmon", "Transparent")</BackgroundColor>
                                <BorderColor>
                                  <Default>DimGray</Default>
                                </BorderColor>
                                <BorderStyle>
                                  <Bottom>=IIF(Parameters!show_details.Value=True,"Solid","None")</Bottom>
                                </BorderStyle>
                                <FontFamily>Tahoma</FontFamily>
                                <FontSize>7pt</FontSize>
                                <Format>n2</Format>
                                <VerticalAlign>Middle</VerticalAlign>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                              <ZIndex>68</ZIndex>
                              <CanGrow>true</CanGrow>
                              <Value>=IIF(Sum(Fields!MAILQTY.Value, "tb_RespAnalysis_group_Package") = 0, 
	0,
	Sum(Fields!GROSS.Value, "tb_RespAnalysis_group_Package")/Sum(Fields!MAILQTY.Value, "tb_RespAnalysis_group_Package"))</Value>
                            </Textbox>
                          </ReportItems>
                        </TableCell>
                        <TableCell>
                          <ReportItems>
                            <Textbox Name="textbox96">
                              <rd:DefaultName>textbox96</rd:DefaultName>
                              <Style>
                                <BackgroundColor>=IIF(Parameters!show_details.Value=True, "LightSalmon", "Transparent")</BackgroundColor>
                                <BorderColor>
                                  <Default>DimGray</Default>
                                </BorderColor>
                                <BorderStyle>
                                  <Bottom>=IIF(Parameters!show_details.Value=True,"Solid","None")</Bottom>
                                </BorderStyle>
                                <FontFamily>Tahoma</FontFamily>
                                <FontSize>7pt</FontSize>
                                <Format>n2</Format>
                                <VerticalAlign>Middle</VerticalAlign>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                              <ZIndex>67</ZIndex>
                              <CanGrow>true</CanGrow>
                              <Value>=IIF(Sum(Fields!MAILQTY.Value, "tb_RespAnalysis_group_Package") = 0, 
	0,
	Sum(Fields!COST.Value, "tb_RespAnalysis_group_Package")/Sum(Fields!MAILQTY.Value, "tb_RespAnalysis_group_Package"))</Value>
                            </Textbox>
                          </ReportItems>
                        </TableCell>
                        <TableCell>
                          <ReportItems>
                            <Textbox Name="textbox97">
                              <rd:DefaultName>textbox97</rd:DefaultName>
                              <Style>
                                <BackgroundColor>=IIF(Parameters!show_details.Value=True, "LightSalmon", "Transparent")</BackgroundColor>
                                <BorderColor>
                                  <Default>DimGray</Default>
                                </BorderColor>
                                <BorderStyle>
                                  <Bottom>=IIF(Parameters!show_details.Value=True,"Solid","None")</Bottom>
                                </BorderStyle>
                                <FontFamily>Tahoma</FontFamily>
                                <FontSize>7pt</FontSize>
                                <Format>n2</Format>
                                <VerticalAlign>Middle</VerticalAlign>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                              <ZIndex>66</ZIndex>
                              <CanGrow>true</CanGrow>
                              <Value>=IIF(Sum(Fields!MAILQTY.Value, "tb_RespAnalysis_group_Package") = 0, 
	0,
	Sum(Fields!NET.Value, "tb_RespAnalysis_group_Package")/Sum(Fields!MAILQTY.Value, "tb_RespAnalysis_group_Package"))</Value>
                            </Textbox>
                          </ReportItems>
                        </TableCell>
                        <TableCell>
                          <ReportItems>
                            <Textbox Name="textbox98">
                              <rd:DefaultName>textbox98</rd:DefaultName>
                              <Style>
                                <BackgroundColor>=IIF(Parameters!show_details.Value=True, "LightSalmon", "Transparent")</BackgroundColor>
                                <BorderColor>
                                  <Default>DimGray</Default>
                                </BorderColor>
                                <BorderStyle>
                                  <Bottom>=IIF(Parameters!show_details.Value=True,"Solid","None")</Bottom>
                                </BorderStyle>
                                <FontFamily>Tahoma</FontFamily>
                                <FontSize>7pt</FontSize>
                                <Format>n2</Format>
                                <VerticalAlign>Middle</VerticalAlign>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                              <ZIndex>65</ZIndex>
                              <CanGrow>true</CanGrow>
                              <Value>=IIF(Sum(Fields!DONATIONS.Value, "tb_RespAnalysis_group_Package") = 0, 
	0,
	Sum(Fields!GROSS.Value, "tb_RespAnalysis_group_Package")/Sum(Fields!DONATIONS.Value, "tb_RespAnalysis_group_Package"))</Value>
                            </Textbox>
                          </ReportItems>
                        </TableCell>
                        <TableCell>
                          <ReportItems>
                            <Textbox Name="textbox99">
                              <rd:DefaultName>textbox99</rd:DefaultName>
                              <Style>
                                <BackgroundColor>=IIF(Parameters!show_details.Value=True, "LightSalmon", "Transparent")</BackgroundColor>
                                <BorderColor>
                                  <Default>DimGray</Default>
                                </BorderColor>
                                <BorderStyle>
                                  <Right>=IIF(Parameters!show_details.Value=True,"Solid","None")</Right>
                                  <Bottom>=IIF(Parameters!show_details.Value=True,"Solid","None")</Bottom>
                                </BorderStyle>
                                <FontFamily>Tahoma</FontFamily>
                                <FontSize>7pt</FontSize>
                                <Format>n2</Format>
                                <VerticalAlign>Middle</VerticalAlign>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                              <ZIndex>64</ZIndex>
                              <CanGrow>true</CanGrow>
                              <Value>=IIF(Sum(Fields!COST.Value, "tb_RespAnalysis_group_Package") = 0, 
	0,
	Sum(Fields!GROSS.Value, "tb_RespAnalysis_group_Package")*1.0/Sum(Fields!COST.Value, "tb_RespAnalysis_group_Package"))</Value>
                            </Textbox>
                          </ReportItems>
                        </TableCell>
                      </TableCells>
                      <Height>0.26in</Height>
                    </TableRow>
                  </TableRows>
                </Footer>
              </TableGroup>
              <TableGroup>
                <Grouping Name="tb_RespAnalysis_group_ListNoG">
                  <GroupExpressions>
                    <GroupExpression>=IIF(Parameters!show_subtotal_LISTNOG.Value=True, Fields!LISTNOG.Value, "")</GroupExpression>
                  </GroupExpressions>
                </Grouping>
                <Footer>
                  <TableRows>
                    <TableRow>
                      <Visibility>
                        <Hidden>=(Not Parameters!show_subtotal_LISTNOG.Value  OR Not Parameters!show_details.Value)</Hidden>
                      </Visibility>
                      <TableCells>
                        <TableCell>
                          <ColSpan>3</ColSpan>
                          <ReportItems>
                            <Textbox Name="textbox6">
                              <rd:DefaultName>textbox6</rd:DefaultName>
                              <Style>
                                <BackgroundColor>OldLace</BackgroundColor>
                                <BorderColor>
                                  <Default>DarkGray</Default>
                                </BorderColor>
                                <FontFamily>Tahoma</FontFamily>
                                <FontSize>7pt</FontSize>
                                <FontWeight>700</FontWeight>
                                <VerticalAlign>Middle</VerticalAlign>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                              <ZIndex>63</ZIndex>
                              <CanGrow>true</CanGrow>
                              <Value>=("       LIST# GROUP:  "+Fields!LISTNOG.Value)</Value>
                            </Textbox>
                          </ReportItems>
                        </TableCell>
                        <TableCell>
                          <ReportItems>
                            <Textbox Name="textbox111">
                              <rd:DefaultName>textbox111</rd:DefaultName>
                              <Style>
                                <BackgroundColor>OldLace</BackgroundColor>
                                <BorderColor>
                                  <Default>DarkGray</Default>
                                </BorderColor>
                                <FontFamily>Tahoma</FontFamily>
                                <FontSize>7pt</FontSize>
                                <Format>MM/dd/yy</Format>
                                <TextAlign>Right</TextAlign>
                                <VerticalAlign>Middle</VerticalAlign>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                                <UnicodeBiDi>Embed</UnicodeBiDi>
                              </Style>
                              <ZIndex>62</ZIndex>
                              <CanGrow>true</CanGrow>
                              <Value>=Min(Fields!MAILDATE.Value, "tb_RespAnalysis_group_ListNoG")</Value>
                            </Textbox>
                          </ReportItems>
                        </TableCell>
                        <TableCell>
                          <ReportItems>
                            <Textbox Name="textbox112">
                              <rd:DefaultName>textbox112</rd:DefaultName>
                              <Style>
                                <BackgroundColor>OldLace</BackgroundColor>
                                <BorderColor>
                                  <Default>DarkGray</Default>
                                </BorderColor>
                                <FontFamily>Tahoma</FontFamily>
                                <FontSize>7pt</FontSize>
                                <Format>n0</Format>
                                <TextAlign>Right</TextAlign>
                                <VerticalAlign>Middle</VerticalAlign>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                              <ZIndex>61</ZIndex>
                              <CanGrow>true</CanGrow>
                              <Value>=Sum(Fields!MAILQTY.Value, "tb_RespAnalysis_group_ListNoG")</Value>
                            </Textbox>
                          </ReportItems>
                        </TableCell>
                        <TableCell>
                          <ReportItems>
                            <Textbox Name="textbox28">
                              <rd:DefaultName>textbox28</rd:DefaultName>
                              <Style>
                                <BackgroundColor>OldLace</BackgroundColor>
                                <BorderColor>
                                  <Default>DarkGray</Default>
                                </BorderColor>
                                <FontFamily>Tahoma</FontFamily>
                                <FontSize>7pt</FontSize>
                                <Format>MM/dd/yy</Format>
                                <TextAlign>Right</TextAlign>
                                <VerticalAlign>Middle</VerticalAlign>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                              <ZIndex>60</ZIndex>
                              <CanGrow>true</CanGrow>
                              <Value>=Min(Fields!FIRSTCAGE.Value, "tb_RespAnalysis_group_ListNoG")</Value>
                            </Textbox>
                          </ReportItems>
                        </TableCell>
                        <TableCell>
                          <ReportItems>
                            <Textbox Name="textbox127">
                              <Style>
                                <BackgroundColor>OldLace</BackgroundColor>
                                <BorderColor>
                                  <Default>DarkGray</Default>
                                </BorderColor>
                                <FontFamily>Tahoma</FontFamily>
                                <FontSize>7pt</FontSize>
                                <Format>MM/dd/yy</Format>
                                <TextAlign>Right</TextAlign>
                                <VerticalAlign>Middle</VerticalAlign>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                              <ZIndex>59</ZIndex>
                              <CanGrow>true</CanGrow>
                              <Value>=Max(Fields!LASTCAGE.Value, "tb_RespAnalysis_group_ListNoG")</Value>
                            </Textbox>
                          </ReportItems>
                        </TableCell>
                        <TableCell>
                          <ReportItems>
                            <Textbox Name="textbox43">
                              <rd:DefaultName>textbox43</rd:DefaultName>
                              <Style>
                                <BackgroundColor>OldLace</BackgroundColor>
                                <BorderColor>
                                  <Default>DarkGray</Default>
                                </BorderColor>
                                <FontFamily>Tahoma</FontFamily>
                                <FontSize>7pt</FontSize>
                                <Format>n0</Format>
                                <TextAlign>Right</TextAlign>
                                <VerticalAlign>Middle</VerticalAlign>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                              <ZIndex>58</ZIndex>
                              <CanGrow>true</CanGrow>
                              <Value>=Sum(Fields!DONATIONS.Value, "tb_RespAnalysis_group_ListNoG")</Value>
                            </Textbox>
                          </ReportItems>
                        </TableCell>
                        <TableCell>
                          <ReportItems>
                            <Textbox Name="textbox44">
                              <rd:DefaultName>textbox44</rd:DefaultName>
                              <Style>
                                <BackgroundColor>OldLace</BackgroundColor>
                                <BorderColor>
                                  <Default>DarkGray</Default>
                                </BorderColor>
                                <FontFamily>Tahoma</FontFamily>
                                <FontSize>7pt</FontSize>
                                <Format>N2</Format>
                                <TextAlign>Right</TextAlign>
                                <VerticalAlign>Middle</VerticalAlign>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                              <ZIndex>57</ZIndex>
                              <CanGrow>true</CanGrow>
                              <Value>=IIF(Sum(Fields!MAILQTY.Value, "tb_RespAnalysis_group_ListNoG") = 0, 
	0,
	Sum(Fields!DONATIONS.Value, "tb_RespAnalysis_group_ListNoG")*100.0/Sum(Fields!MAILQTY.Value, "tb_RespAnalysis_group_ListNoG"))</Value>
                            </Textbox>
                          </ReportItems>
                        </TableCell>
                        <TableCell>
                          <ReportItems>
                            <Textbox Name="textbox61">
                              <rd:DefaultName>textbox61</rd:DefaultName>
                              <ToggleImage>
                                <InitialState>true</InitialState>
                              </ToggleImage>
                              <Style>
                                <BackgroundColor>OldLace</BackgroundColor>
                                <BorderColor>
                                  <Default>DarkGray</Default>
                                </BorderColor>
                                <FontFamily>Tahoma</FontFamily>
                                <FontSize>7pt</FontSize>
                                <Format>n2</Format>
                                <TextAlign>Right</TextAlign>
                                <VerticalAlign>Middle</VerticalAlign>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                              <ZIndex>56</ZIndex>
                              <CanGrow>true</CanGrow>
                              <Value>=Sum(Fields!GROSS.Value, "tb_RespAnalysis_group_ListNoG")</Value>
                            </Textbox>
                          </ReportItems>
                        </TableCell>
                        <TableCell>
                          <ReportItems>
                            <Textbox Name="textbox62">
                              <rd:DefaultName>textbox62</rd:DefaultName>
                              <Style>
                                <BackgroundColor>OldLace</BackgroundColor>
                                <BorderColor>
                                  <Default>DarkGray</Default>
                                </BorderColor>
                                <FontFamily>Tahoma</FontFamily>
                                <FontSize>7pt</FontSize>
                                <Format>n2</Format>
                                <TextAlign>Right</TextAlign>
                                <VerticalAlign>Middle</VerticalAlign>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                              <ZIndex>55</ZIndex>
                              <CanGrow>true</CanGrow>
                              <Value>=Sum(Fields!COST.Value, "tb_RespAnalysis_group_ListNoG")</Value>
                            </Textbox>
                          </ReportItems>
                        </TableCell>
                        <TableCell>
                          <ReportItems>
                            <Textbox Name="textbox63">
                              <rd:DefaultName>textbox63</rd:DefaultName>
                              <Style>
                                <BackgroundColor>OldLace</BackgroundColor>
                                <BorderColor>
                                  <Default>DarkGray</Default>
                                </BorderColor>
                                <FontFamily>Tahoma</FontFamily>
                                <FontSize>7pt</FontSize>
                                <Format>n2</Format>
                                <TextAlign>Right</TextAlign>
                                <VerticalAlign>Middle</VerticalAlign>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                              <ZIndex>54</ZIndex>
                              <CanGrow>true</CanGrow>
                              <Value>=Sum(Fields!NET.Value, "tb_RespAnalysis_group_ListNoG")</Value>
                            </Textbox>
                          </ReportItems>
                        </TableCell>
                        <TableCell>
                          <ReportItems>
                            <Textbox Name="textbox64">
                              <rd:DefaultName>textbox64</rd:DefaultName>
                              <Style>
                                <BackgroundColor>OldLace</BackgroundColor>
                                <BorderColor>
                                  <Default>DarkGray</Default>
                                </BorderColor>
                                <FontFamily>Tahoma</FontFamily>
                                <FontSize>7pt</FontSize>
                                <Format>n2</Format>
                                <TextAlign>Right</TextAlign>
                                <VerticalAlign>Middle</VerticalAlign>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                              <ZIndex>53</ZIndex>
                              <CanGrow>true</CanGrow>
                              <Value>=IIF(Sum(Fields!MAILQTY.Value, "tb_RespAnalysis_group_ListNoG") = 0, 
	0,
	Sum(Fields!GROSS.Value, "tb_RespAnalysis_group_ListNoG")/Sum(Fields!MAILQTY.Value, "tb_RespAnalysis_group_ListNoG"))</Value>
                            </Textbox>
                          </ReportItems>
                        </TableCell>
                        <TableCell>
                          <ReportItems>
                            <Textbox Name="textbox65">
                              <rd:DefaultName>textbox65</rd:DefaultName>
                              <Style>
                                <BackgroundColor>OldLace</BackgroundColor>
                                <BorderColor>
                                  <Default>DarkGray</Default>
                                </BorderColor>
                                <FontFamily>Tahoma</FontFamily>
                                <FontSize>7pt</FontSize>
                                <Format>n2</Format>
                                <TextAlign>Right</TextAlign>
                                <VerticalAlign>Middle</VerticalAlign>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                              <ZIndex>52</ZIndex>
                              <CanGrow>true</CanGrow>
                              <Value>=IIF(Sum(Fields!MAILQTY.Value, "tb_RespAnalysis_group_ListNoG") = 0, 
	0,
	Sum(Fields!COST.Value, "tb_RespAnalysis_group_ListNoG")/Sum(Fields!MAILQTY.Value, "tb_RespAnalysis_group_ListNoG"))</Value>
                            </Textbox>
                          </ReportItems>
                        </TableCell>
                        <TableCell>
                          <ReportItems>
                            <Textbox Name="textbox66">
                              <rd:DefaultName>textbox66</rd:DefaultName>
                              <Style>
                                <BackgroundColor>OldLace</BackgroundColor>
                                <BorderColor>
                                  <Default>DarkGray</Default>
                                </BorderColor>
                                <FontFamily>Tahoma</FontFamily>
                                <FontSize>7pt</FontSize>
                                <Format>n2</Format>
                                <TextAlign>Right</TextAlign>
                                <VerticalAlign>Middle</VerticalAlign>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                              <ZIndex>51</ZIndex>
                              <CanGrow>true</CanGrow>
                              <Value>=IIF(Sum(Fields!MAILQTY.Value, "tb_RespAnalysis_group_ListNoG") = 0, 
	0,
	Sum(Fields!NET.Value, "tb_RespAnalysis_group_ListNoG")/Sum(Fields!MAILQTY.Value, "tb_RespAnalysis_group_ListNoG"))</Value>
                            </Textbox>
                          </ReportItems>
                        </TableCell>
                        <TableCell>
                          <ReportItems>
                            <Textbox Name="textbox67">
                              <rd:DefaultName>textbox67</rd:DefaultName>
                              <Style>
                                <BackgroundColor>OldLace</BackgroundColor>
                                <BorderColor>
                                  <Default>DarkGray</Default>
                                </BorderColor>
                                <FontFamily>Tahoma</FontFamily>
                                <FontSize>7pt</FontSize>
                                <Format>n2</Format>
                                <TextAlign>Right</TextAlign>
                                <VerticalAlign>Middle</VerticalAlign>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                              <ZIndex>50</ZIndex>
                              <CanGrow>true</CanGrow>
                              <Value>=IIF(Sum(Fields!DONATIONS.Value, "tb_RespAnalysis_group_ListNoG") = 0, 
	0,
	Sum(Fields!GROSS.Value, "tb_RespAnalysis_group_ListNoG")/Sum(Fields!DONATIONS.Value, "tb_RespAnalysis_group_ListNoG"))</Value>
                            </Textbox>
                          </ReportItems>
                        </TableCell>
                        <TableCell>
                          <ReportItems>
                            <Textbox Name="textbox68">
                              <rd:DefaultName>textbox68</rd:DefaultName>
                              <Style>
                                <BackgroundColor>OldLace</BackgroundColor>
                                <BorderColor>
                                  <Default>DarkGray</Default>
                                </BorderColor>
                                <FontFamily>Tahoma</FontFamily>
                                <FontSize>7pt</FontSize>
                                <Format>n2</Format>
                                <TextAlign>Right</TextAlign>
                                <VerticalAlign>Middle</VerticalAlign>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                              <ZIndex>49</ZIndex>
                              <CanGrow>true</CanGrow>
                              <Value>=IIF(Sum(Fields!COST.Value, "tb_RespAnalysis_group_ListNoG") = 0, 
	0,
	Sum(Fields!GROSS.Value, "tb_RespAnalysis_group_ListNoG")*1.0/Sum(Fields!COST.Value, "tb_RespAnalysis_group_ListNoG"))</Value>
                            </Textbox>
                          </ReportItems>
                        </TableCell>
                      </TableCells>
                      <Height>0.25707in</Height>
                    </TableRow>
                  </TableRows>
                </Footer>
              </TableGroup>
              <TableGroup>
                <Grouping Name="tb_RespAnalysis_group_ListNo">
                  <GroupExpressions>
                    <GroupExpression>=IIF(Parameters!show_subtotal_LISTNO.Value=True, Fields!LISTNO.Value, "")</GroupExpression>
                  </GroupExpressions>
                </Grouping>
                <Sorting>
                  <SortBy>
                    <SortExpression>=Fields!LISTNO.Value</SortExpression>
                    <Direction>Descending</Direction>
                  </SortBy>
                </Sorting>
                <Footer>
                  <TableRows>
                    <TableRow>
                      <Visibility>
                        <Hidden>=(Not Parameters!show_subtotal_LISTNO.Value OR Not Parameters!show_details.Value)</Hidden>
                      </Visibility>
                      <TableCells>
                        <TableCell>
                          <ColSpan>3</ColSpan>
                          <ReportItems>
                            <Textbox Name="textbox45">
                              <rd:DefaultName>textbox45</rd:DefaultName>
                              <Style>
                                <BackgroundColor>OldLace</BackgroundColor>
                                <BorderColor>
                                  <Default>DarkGray</Default>
                                </BorderColor>
                                <FontFamily>Tahoma</FontFamily>
                                <FontSize>7pt</FontSize>
                                <FontWeight>700</FontWeight>
                                <VerticalAlign>Middle</VerticalAlign>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                              <ZIndex>48</ZIndex>
                              <CanGrow>true</CanGrow>
                              <Value>=("             LIST#:  " + Fields!LISTNO.Value)</Value>
                            </Textbox>
                          </ReportItems>
                        </TableCell>
                        <TableCell>
                          <ReportItems>
                            <Textbox Name="textbox86">
                              <rd:DefaultName>textbox86</rd:DefaultName>
                              <Style>
                                <BackgroundColor>OldLace</BackgroundColor>
                                <BorderColor>
                                  <Default>DarkGray</Default>
                                </BorderColor>
                                <FontFamily>Tahoma</FontFamily>
                                <FontSize>7pt</FontSize>
                                <Format>MM/dd/yy</Format>
                                <TextAlign>Right</TextAlign>
                                <VerticalAlign>Middle</VerticalAlign>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                                <UnicodeBiDi>Embed</UnicodeBiDi>
                              </Style>
                              <ZIndex>47</ZIndex>
                              <CanGrow>true</CanGrow>
                              <Value>=Min(Fields!MAILDATE.Value, "tb_RespAnalysis_group_ListNo")</Value>
                            </Textbox>
                          </ReportItems>
                        </TableCell>
                        <TableCell>
                          <ReportItems>
                            <Textbox Name="textbox87">
                              <rd:DefaultName>textbox87</rd:DefaultName>
                              <Style>
                                <BackgroundColor>OldLace</BackgroundColor>
                                <BorderColor>
                                  <Default>DarkGray</Default>
                                </BorderColor>
                                <FontFamily>Tahoma</FontFamily>
                                <FontSize>7pt</FontSize>
                                <Format>n0</Format>
                                <TextAlign>Right</TextAlign>
                                <VerticalAlign>Middle</VerticalAlign>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                              <ZIndex>46</ZIndex>
                              <CanGrow>true</CanGrow>
                              <Value>=Sum(Fields!MAILQTY.Value, "tb_RespAnalysis_group_ListNo")</Value>
                            </Textbox>
                          </ReportItems>
                        </TableCell>
                        <TableCell>
                          <ReportItems>
                            <Textbox Name="textbox49">
                              <rd:DefaultName>textbox49</rd:DefaultName>
                              <Style>
                                <BackgroundColor>OldLace</BackgroundColor>
                                <BorderColor>
                                  <Default>DarkGray</Default>
                                </BorderColor>
                                <FontFamily>Tahoma</FontFamily>
                                <FontSize>7pt</FontSize>
                                <Format>MM/dd/yy</Format>
                                <TextAlign>Right</TextAlign>
                                <VerticalAlign>Middle</VerticalAlign>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                              <ZIndex>45</ZIndex>
                              <CanGrow>true</CanGrow>
                              <Value>=Min(Fields!FIRSTCAGE.Value, "tb_RespAnalysis_group_ListNo")</Value>
                            </Textbox>
                          </ReportItems>
                        </TableCell>
                        <TableCell>
                          <ReportItems>
                            <Textbox Name="textbox7">
                              <Style>
                                <BackgroundColor>OldLace</BackgroundColor>
                                <BorderColor>
                                  <Default>DarkGray</Default>
                                </BorderColor>
                                <FontFamily>Tahoma</FontFamily>
                                <FontSize>7pt</FontSize>
                                <Format>MM/dd/yy</Format>
                                <TextAlign>Right</TextAlign>
                                <VerticalAlign>Middle</VerticalAlign>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                              <ZIndex>44</ZIndex>
                              <CanGrow>true</CanGrow>
                              <Value>=Max(Fields!LASTCAGE.Value, "tb_RespAnalysis_group_ListNo")</Value>
                            </Textbox>
                          </ReportItems>
                        </TableCell>
                        <TableCell>
                          <ReportItems>
                            <Textbox Name="textbox51">
                              <rd:DefaultName>textbox51</rd:DefaultName>
                              <Style>
                                <BackgroundColor>OldLace</BackgroundColor>
                                <BorderColor>
                                  <Default>DarkGray</Default>
                                </BorderColor>
                                <FontFamily>Tahoma</FontFamily>
                                <FontSize>7pt</FontSize>
                                <Format>n0</Format>
                                <TextAlign>Right</TextAlign>
                                <VerticalAlign>Middle</VerticalAlign>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                              <ZIndex>43</ZIndex>
                              <CanGrow>true</CanGrow>
                              <Value>=Sum(Fields!DONATIONS.Value, "tb_RespAnalysis_group_ListNo")</Value>
                            </Textbox>
                          </ReportItems>
                        </TableCell>
                        <TableCell>
                          <ReportItems>
                            <Textbox Name="textbox52">
                              <rd:DefaultName>textbox52</rd:DefaultName>
                              <Style>
                                <BackgroundColor>OldLace</BackgroundColor>
                                <BorderColor>
                                  <Default>DarkGray</Default>
                                </BorderColor>
                                <FontFamily>Tahoma</FontFamily>
                                <FontSize>7pt</FontSize>
                                <Format>N2</Format>
                                <TextAlign>Right</TextAlign>
                                <VerticalAlign>Middle</VerticalAlign>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                              <ZIndex>42</ZIndex>
                              <CanGrow>true</CanGrow>
                              <Value>=IIF(Sum(Fields!MAILQTY.Value, "tb_RespAnalysis_group_ListNo") = 0, 
	0,
	Sum(Fields!DONATIONS.Value, "tb_RespAnalysis_group_ListNo")*100.0/Sum(Fields!MAILQTY.Value, "tb_RespAnalysis_group_ListNo"))</Value>
                            </Textbox>
                          </ReportItems>
                        </TableCell>
                        <TableCell>
                          <ReportItems>
                            <Textbox Name="textbox53">
                              <rd:DefaultName>textbox53</rd:DefaultName>
                              <ToggleImage>
                                <InitialState>true</InitialState>
                              </ToggleImage>
                              <Style>
                                <BackgroundColor>OldLace</BackgroundColor>
                                <BorderColor>
                                  <Default>DarkGray</Default>
                                </BorderColor>
                                <FontFamily>Tahoma</FontFamily>
                                <FontSize>7pt</FontSize>
                                <Format>n2</Format>
                                <TextAlign>Right</TextAlign>
                                <VerticalAlign>Middle</VerticalAlign>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                              <ZIndex>41</ZIndex>
                              <CanGrow>true</CanGrow>
                              <Value>=Sum(Fields!GROSS.Value, "tb_RespAnalysis_group_ListNo")</Value>
                            </Textbox>
                          </ReportItems>
                        </TableCell>
                        <TableCell>
                          <ReportItems>
                            <Textbox Name="textbox54">
                              <rd:DefaultName>textbox54</rd:DefaultName>
                              <Style>
                                <BackgroundColor>OldLace</BackgroundColor>
                                <BorderColor>
                                  <Default>DarkGray</Default>
                                </BorderColor>
                                <FontFamily>Tahoma</FontFamily>
                                <FontSize>7pt</FontSize>
                                <Format>n2</Format>
                                <TextAlign>Right</TextAlign>
                                <VerticalAlign>Middle</VerticalAlign>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                              <ZIndex>40</ZIndex>
                              <CanGrow>true</CanGrow>
                              <Value>=Sum(Fields!COST.Value, "tb_RespAnalysis_group_ListNo")</Value>
                            </Textbox>
                          </ReportItems>
                        </TableCell>
                        <TableCell>
                          <ReportItems>
                            <Textbox Name="textbox55">
                              <rd:DefaultName>textbox55</rd:DefaultName>
                              <Style>
                                <BackgroundColor>OldLace</BackgroundColor>
                                <BorderColor>
                                  <Default>DarkGray</Default>
                                </BorderColor>
                                <FontFamily>Tahoma</FontFamily>
                                <FontSize>7pt</FontSize>
                                <Format>n2</Format>
                                <TextAlign>Right</TextAlign>
                                <VerticalAlign>Middle</VerticalAlign>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                              <ZIndex>39</ZIndex>
                              <CanGrow>true</CanGrow>
                              <Value>=Sum(Fields!NET.Value, "tb_RespAnalysis_group_ListNo")</Value>
                            </Textbox>
                          </ReportItems>
                        </TableCell>
                        <TableCell>
                          <ReportItems>
                            <Textbox Name="textbox56">
                              <rd:DefaultName>textbox56</rd:DefaultName>
                              <Style>
                                <BackgroundColor>OldLace</BackgroundColor>
                                <BorderColor>
                                  <Default>DarkGray</Default>
                                </BorderColor>
                                <FontFamily>Tahoma</FontFamily>
                                <FontSize>7pt</FontSize>
                                <Format>n2</Format>
                                <TextAlign>Right</TextAlign>
                                <VerticalAlign>Middle</VerticalAlign>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                              <ZIndex>38</ZIndex>
                              <CanGrow>true</CanGrow>
                              <Value>=IIF(Sum(Fields!MAILQTY.Value, "tb_RespAnalysis_group_ListNo") = 0, 
	0,
	Sum(Fields!GROSS.Value, "tb_RespAnalysis_group_ListNo")/Sum(Fields!MAILQTY.Value, "tb_RespAnalysis_group_ListNo"))</Value>
                            </Textbox>
                          </ReportItems>
                        </TableCell>
                        <TableCell>
                          <ReportItems>
                            <Textbox Name="textbox57">
                              <rd:DefaultName>textbox57</rd:DefaultName>
                              <Style>
                                <BackgroundColor>OldLace</BackgroundColor>
                                <BorderColor>
                                  <Default>DarkGray</Default>
                                </BorderColor>
                                <FontFamily>Tahoma</FontFamily>
                                <FontSize>7pt</FontSize>
                                <Format>n2</Format>
                                <TextAlign>Right</TextAlign>
                                <VerticalAlign>Middle</VerticalAlign>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                              <ZIndex>37</ZIndex>
                              <CanGrow>true</CanGrow>
                              <Value>=IIF(Sum(Fields!MAILQTY.Value, "tb_RespAnalysis_group_ListNo") = 0, 
	0,
	Sum(Fields!COST.Value, "tb_RespAnalysis_group_ListNo")/Sum(Fields!MAILQTY.Value, "tb_RespAnalysis_group_ListNo"))</Value>
                            </Textbox>
                          </ReportItems>
                        </TableCell>
                        <TableCell>
                          <ReportItems>
                            <Textbox Name="textbox58">
                              <rd:DefaultName>textbox58</rd:DefaultName>
                              <Style>
                                <BackgroundColor>OldLace</BackgroundColor>
                                <BorderColor>
                                  <Default>DarkGray</Default>
                                </BorderColor>
                                <FontFamily>Tahoma</FontFamily>
                                <FontSize>7pt</FontSize>
                                <Format>n2</Format>
                                <TextAlign>Right</TextAlign>
                                <VerticalAlign>Middle</VerticalAlign>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                              <ZIndex>36</ZIndex>
                              <CanGrow>true</CanGrow>
                              <Value>=IIF(Sum(Fields!MAILQTY.Value, "tb_RespAnalysis_group_ListNo") = 0, 
	0,
	Sum(Fields!NET.Value, "tb_RespAnalysis_group_ListNo")/Sum(Fields!MAILQTY.Value, "tb_RespAnalysis_group_ListNo"))</Value>
                            </Textbox>
                          </ReportItems>
                        </TableCell>
                        <TableCell>
                          <ReportItems>
                            <Textbox Name="textbox59">
                              <rd:DefaultName>textbox59</rd:DefaultName>
                              <Style>
                                <BackgroundColor>OldLace</BackgroundColor>
                                <BorderColor>
                                  <Default>DarkGray</Default>
                                </BorderColor>
                                <FontFamily>Tahoma</FontFamily>
                                <FontSize>7pt</FontSize>
                                <Format>n2</Format>
                                <TextAlign>Right</TextAlign>
                                <VerticalAlign>Middle</VerticalAlign>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                              <ZIndex>35</ZIndex>
                              <CanGrow>true</CanGrow>
                              <Value>=IIF(Sum(Fields!DONATIONS.Value, "tb_RespAnalysis_group_ListNo") = 0, 
	0,
	Sum(Fields!GROSS.Value, "tb_RespAnalysis_group_ListNo")/Sum(Fields!DONATIONS.Value, "tb_RespAnalysis_group_ListNo"))</Value>
                            </Textbox>
                          </ReportItems>
                        </TableCell>
                        <TableCell>
                          <ReportItems>
                            <Textbox Name="textbox60">
                              <rd:DefaultName>textbox60</rd:DefaultName>
                              <Style>
                                <BackgroundColor>OldLace</BackgroundColor>
                                <BorderColor>
                                  <Default>DarkGray</Default>
                                </BorderColor>
                                <FontFamily>Tahoma</FontFamily>
                                <FontSize>7pt</FontSize>
                                <Format>n2</Format>
                                <TextAlign>Right</TextAlign>
                                <VerticalAlign>Middle</VerticalAlign>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                              <ZIndex>34</ZIndex>
                              <CanGrow>true</CanGrow>
                              <Value>=IIF(Sum(Fields!COST.Value, "tb_RespAnalysis_group_ListNo") = 0, 
	0,
	Sum(Fields!GROSS.Value, "tb_RespAnalysis_group_ListNo")*1.0/Sum(Fields!COST.Value, "tb_RespAnalysis_group_ListNo"))</Value>
                            </Textbox>
                          </ReportItems>
                        </TableCell>
                      </TableCells>
                      <Height>0.25707in</Height>
                    </TableRow>
                  </TableRows>
                </Footer>
              </TableGroup>
              <TableGroup>
                <Grouping Name="tb_RespAnalysis_group_Source">
                  <GroupExpressions>
                    <GroupExpression>=Fields!SRCECODE.Value</GroupExpression>
                  </GroupExpressions>
                </Grouping>
                <Footer>
                  <TableRows>
                    <TableRow>
                      <Visibility>
                        <Hidden>true</Hidden>
                      </Visibility>
                      <TableCells>
                        <TableCell>
                          <ReportItems>
                            <Textbox Name="textbox46">
                              <rd:DefaultName>textbox46</rd:DefaultName>
                              <Style>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                              <ZIndex>33</ZIndex>
                              <CanGrow>true</CanGrow>
                              <Value />
                            </Textbox>
                          </ReportItems>
                        </TableCell>
                        <TableCell>
                          <ReportItems>
                            <Textbox Name="textbox72">
                              <rd:DefaultName>textbox72</rd:DefaultName>
                              <Style>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                              <ZIndex>32</ZIndex>
                              <CanGrow>true</CanGrow>
                              <Value />
                            </Textbox>
                          </ReportItems>
                        </TableCell>
                        <TableCell>
                          <ReportItems>
                            <Textbox Name="textbox219">
                              <Style>
                                <FontSize>8pt</FontSize>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                              <ZIndex>31</ZIndex>
                              <CanGrow>true</CanGrow>
                              <Value />
                            </Textbox>
                          </ReportItems>
                        </TableCell>
                        <TableCell>
                          <ReportItems>
                            <Textbox Name="textbox74">
                              <rd:DefaultName>textbox74</rd:DefaultName>
                              <Style>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                              <ZIndex>30</ZIndex>
                              <CanGrow>true</CanGrow>
                              <Value />
                            </Textbox>
                          </ReportItems>
                        </TableCell>
                        <TableCell>
                          <ReportItems>
                            <Textbox Name="textbox75">
                              <rd:DefaultName>textbox75</rd:DefaultName>
                              <Style>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                              <ZIndex>29</ZIndex>
                              <CanGrow>true</CanGrow>
                              <Value />
                            </Textbox>
                          </ReportItems>
                        </TableCell>
                        <TableCell>
                          <ReportItems>
                            <Textbox Name="textbox77">
                              <rd:DefaultName>textbox77</rd:DefaultName>
                              <Style>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                              <ZIndex>28</ZIndex>
                              <CanGrow>true</CanGrow>
                              <Value />
                            </Textbox>
                          </ReportItems>
                        </TableCell>
                        <TableCell>
                          <ReportItems>
                            <Textbox Name="textbox81">
                              <rd:DefaultName>textbox81</rd:DefaultName>
                              <Style>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                              <ZIndex>27</ZIndex>
                              <CanGrow>true</CanGrow>
                              <Value />
                            </Textbox>
                          </ReportItems>
                        </TableCell>
                        <TableCell>
                          <ReportItems>
                            <Textbox Name="textbox83">
                              <rd:DefaultName>textbox83</rd:DefaultName>
                              <Style>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                              <ZIndex>26</ZIndex>
                              <CanGrow>true</CanGrow>
                              <Value />
                            </Textbox>
                          </ReportItems>
                        </TableCell>
                        <TableCell>
                          <ReportItems>
                            <Textbox Name="textbox85">
                              <rd:DefaultName>textbox85</rd:DefaultName>
                              <Style>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                              <ZIndex>25</ZIndex>
                              <CanGrow>true</CanGrow>
                              <Value />
                            </Textbox>
                          </ReportItems>
                        </TableCell>
                        <TableCell>
                          <ReportItems>
                            <Textbox Name="textbox101">
                              <rd:DefaultName>textbox101</rd:DefaultName>
                              <Style>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                              <ZIndex>24</ZIndex>
                              <CanGrow>true</CanGrow>
                              <Value />
                            </Textbox>
                          </ReportItems>
                        </TableCell>
                        <TableCell>
                          <ReportItems>
                            <Textbox Name="textbox102">
                              <rd:DefaultName>textbox102</rd:DefaultName>
                              <Style>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                              <ZIndex>23</ZIndex>
                              <CanGrow>true</CanGrow>
                              <Value />
                            </Textbox>
                          </ReportItems>
                        </TableCell>
                        <TableCell>
                          <ReportItems>
                            <Textbox Name="textbox103">
                              <rd:DefaultName>textbox103</rd:DefaultName>
                              <Style>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                              <ZIndex>22</ZIndex>
                              <CanGrow>true</CanGrow>
                              <Value />
                            </Textbox>
                          </ReportItems>
                        </TableCell>
                        <TableCell>
                          <ReportItems>
                            <Textbox Name="textbox104">
                              <rd:DefaultName>textbox104</rd:DefaultName>
                              <Style>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                              <ZIndex>21</ZIndex>
                              <CanGrow>true</CanGrow>
                              <Value />
                            </Textbox>
                          </ReportItems>
                        </TableCell>
                        <TableCell>
                          <ReportItems>
                            <Textbox Name="textbox105">
                              <rd:DefaultName>textbox105</rd:DefaultName>
                              <Style>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                              <ZIndex>20</ZIndex>
                              <CanGrow>true</CanGrow>
                              <Value />
                            </Textbox>
                          </ReportItems>
                        </TableCell>
                        <TableCell>
                          <ReportItems>
                            <Textbox Name="textbox106">
                              <rd:DefaultName>textbox106</rd:DefaultName>
                              <Style>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                              <ZIndex>19</ZIndex>
                              <CanGrow>true</CanGrow>
                              <Value />
                            </Textbox>
                          </ReportItems>
                        </TableCell>
                        <TableCell>
                          <ReportItems>
                            <Textbox Name="textbox107">
                              <rd:DefaultName>textbox107</rd:DefaultName>
                              <Style>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                              <ZIndex>18</ZIndex>
                              <CanGrow>true</CanGrow>
                              <Value />
                            </Textbox>
                          </ReportItems>
                        </TableCell>
                        <TableCell>
                          <ReportItems>
                            <Textbox Name="textbox108">
                              <rd:DefaultName>textbox108</rd:DefaultName>
                              <Style>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                              <ZIndex>17</ZIndex>
                              <CanGrow>true</CanGrow>
                              <Value />
                            </Textbox>
                          </ReportItems>
                        </TableCell>
                      </TableCells>
                      <Height>0.12854in</Height>
                    </TableRow>
                  </TableRows>
                </Footer>
              </TableGroup>
            </TableGroups>
            <Details>
              <TableRows>
                <TableRow>
                  <Visibility>
                    <Hidden>=(Not Parameters!show_details.Value)</Hidden>
                  </Visibility>
                  <TableCells>
                    <TableCell>
                      <ReportItems>
                        <Textbox Name="SRCECODE">
                          <rd:DefaultName>SRCECODE</rd:DefaultName>
                          <Style>
                            <BorderColor>
                              <Default>DarkGray</Default>
                            </BorderColor>
                            <FontFamily>Tahoma</FontFamily>
                            <FontSize>7pt</FontSize>
                            <VerticalAlign>Middle</VerticalAlign>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                          <ZIndex>16</ZIndex>
                          <CanGrow>true</CanGrow>
                          <Value>=Fields!SRCECODE.Value</Value>
                        </Textbox>
                      </ReportItems>
                    </TableCell>
                    <TableCell>
                      <ReportItems>
                        <Textbox Name="textbox3">
                          <rd:DefaultName>textbox3</rd:DefaultName>
                          <Style>
                            <BorderColor>
                              <Default>DarkGray</Default>
                            </BorderColor>
                            <FontFamily>Tahoma</FontFamily>
                            <FontSize>7pt</FontSize>
                            <VerticalAlign>Middle</VerticalAlign>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                          <ZIndex>15</ZIndex>
                          <CanGrow>true</CanGrow>
                          <Value>=(Fields!LISTNOG.Value + IIf(Fields!LISTNOG.Value&lt;&gt;"" AND Fields!LISTNO.Value&lt;&gt;"",".","") + Fields!LISTNO.Value)</Value>
                        </Textbox>
                      </ReportItems>
                    </TableCell>
                    <TableCell>
                      <ReportItems>
                        <Textbox Name="SRCEDESC">
                          <rd:DefaultName>SRCEDESC</rd:DefaultName>
                          <Style>
                            <BorderColor>
                              <Default>DarkGray</Default>
                            </BorderColor>
                            <FontFamily>Tahoma</FontFamily>
                            <FontSize>7pt</FontSize>
                            <VerticalAlign>Middle</VerticalAlign>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                          <ZIndex>14</ZIndex>
                          <CanGrow>true</CanGrow>
                          <Value>=Fields!SRCEDESC.Value</Value>
                        </Textbox>
                      </ReportItems>
                    </TableCell>
                    <TableCell>
                      <ReportItems>
                        <Textbox Name="MAILDATE">
                          <rd:DefaultName>MAILDATE</rd:DefaultName>
                          <Style>
                            <BorderColor>
                              <Default>DarkGray</Default>
                            </BorderColor>
                            <FontFamily>Tahoma</FontFamily>
                            <FontSize>7pt</FontSize>
                            <Format>MM/dd/yy</Format>
                            <VerticalAlign>Middle</VerticalAlign>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                          <ZIndex>13</ZIndex>
                          <CanGrow>true</CanGrow>
                          <Value>=Fields!MAILDATE.Value</Value>
                        </Textbox>
                      </ReportItems>
                    </TableCell>
                    <TableCell>
                      <ReportItems>
                        <Textbox Name="MAILQTY">
                          <rd:DefaultName>MAILQTY</rd:DefaultName>
                          <Style>
                            <BorderColor>
                              <Default>DarkGray</Default>
                            </BorderColor>
                            <FontFamily>Tahoma</FontFamily>
                            <FontSize>7pt</FontSize>
                            <Format>n0</Format>
                            <VerticalAlign>Middle</VerticalAlign>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                          <ZIndex>12</ZIndex>
                          <CanGrow>true</CanGrow>
                          <Value>=Fields!MAILQTY.Value</Value>
                        </Textbox>
                      </ReportItems>
                    </TableCell>
                    <TableCell>
                      <ReportItems>
                        <Textbox Name="FIRSTCAGE">
                          <rd:DefaultName>FIRSTCAGE</rd:DefaultName>
                          <Style>
                            <BorderColor>
                              <Default>DarkGray</Default>
                            </BorderColor>
                            <FontFamily>Tahoma</FontFamily>
                            <FontSize>7pt</FontSize>
                            <Format>MM/dd/yy</Format>
                            <VerticalAlign>Middle</VerticalAlign>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                          <ZIndex>11</ZIndex>
                          <CanGrow>true</CanGrow>
                          <Value>=Fields!FIRSTCAGE.Value</Value>
                        </Textbox>
                      </ReportItems>
                    </TableCell>
                    <TableCell>
                      <ReportItems>
                        <Textbox Name="LASTCAGE">
                          <rd:DefaultName>LASTCAGE</rd:DefaultName>
                          <Style>
                            <BorderColor>
                              <Default>DarkGray</Default>
                            </BorderColor>
                            <FontFamily>Tahoma</FontFamily>
                            <FontSize>7pt</FontSize>
                            <Format>MM/dd/yy</Format>
                            <VerticalAlign>Middle</VerticalAlign>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                          <ZIndex>10</ZIndex>
                          <CanGrow>true</CanGrow>
                          <Value>=Fields!LASTCAGE.Value</Value>
                        </Textbox>
                      </ReportItems>
                    </TableCell>
                    <TableCell>
                      <ReportItems>
                        <Textbox Name="DONATIONS">
                          <rd:DefaultName>DONATIONS</rd:DefaultName>
                          <Style>
                            <BorderColor>
                              <Default>DarkGray</Default>
                            </BorderColor>
                            <FontFamily>Tahoma</FontFamily>
                            <FontSize>7pt</FontSize>
                            <Format>n0</Format>
                            <VerticalAlign>Middle</VerticalAlign>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                          <ZIndex>9</ZIndex>
                          <CanGrow>true</CanGrow>
                          <Value>=Fields!DONATIONS.Value</Value>
                        </Textbox>
                      </ReportItems>
                    </TableCell>
                    <TableCell>
                      <ReportItems>
                        <Textbox Name="RESPONSE">
                          <rd:DefaultName>RESPONSE</rd:DefaultName>
                          <Style>
                            <BorderColor>
                              <Default>DarkGray</Default>
                            </BorderColor>
                            <FontFamily>Tahoma</FontFamily>
                            <FontSize>7pt</FontSize>
                            <Format>n2</Format>
                            <VerticalAlign>Middle</VerticalAlign>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                          <ZIndex>8</ZIndex>
                          <CanGrow>true</CanGrow>
                          <Value>=Fields!RESPONSE.Value</Value>
                        </Textbox>
                      </ReportItems>
                    </TableCell>
                    <TableCell>
                      <ReportItems>
                        <Textbox Name="GROSS">
                          <rd:DefaultName>GROSS</rd:DefaultName>
                          <Style>
                            <BorderColor>
                              <Default>DarkGray</Default>
                            </BorderColor>
                            <FontFamily>Tahoma</FontFamily>
                            <FontSize>7pt</FontSize>
                            <Format>n2</Format>
                            <VerticalAlign>Middle</VerticalAlign>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                          <ZIndex>7</ZIndex>
                          <CanGrow>true</CanGrow>
                          <Value>=Fields!GROSS.Value</Value>
                        </Textbox>
                      </ReportItems>
                    </TableCell>
                    <TableCell>
                      <ReportItems>
                        <Textbox Name="COST">
                          <rd:DefaultName>COST</rd:DefaultName>
                          <Style>
                            <BorderColor>
                              <Default>DarkGray</Default>
                            </BorderColor>
                            <FontFamily>Tahoma</FontFamily>
                            <FontSize>7pt</FontSize>
                            <Format>n2</Format>
                            <VerticalAlign>Middle</VerticalAlign>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                          <ZIndex>6</ZIndex>
                          <CanGrow>true</CanGrow>
                          <Value>=Fields!COST.Value</Value>
                        </Textbox>
                      </ReportItems>
                    </TableCell>
                    <TableCell>
                      <ReportItems>
                        <Textbox Name="NET">
                          <rd:DefaultName>NET</rd:DefaultName>
                          <Style>
                            <BorderColor>
                              <Default>DarkGray</Default>
                            </BorderColor>
                            <FontFamily>Tahoma</FontFamily>
                            <FontSize>7pt</FontSize>
                            <Format>n2</Format>
                            <VerticalAlign>Middle</VerticalAlign>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                          <ZIndex>5</ZIndex>
                          <CanGrow>true</CanGrow>
                          <Value>=Fields!NET.Value</Value>
                        </Textbox>
                      </ReportItems>
                    </TableCell>
                    <TableCell>
                      <ReportItems>
                        <Textbox Name="GPMAIL">
                          <rd:DefaultName>GPMAIL</rd:DefaultName>
                          <Style>
                            <BorderColor>
                              <Default>DarkGray</Default>
                            </BorderColor>
                            <FontFamily>Tahoma</FontFamily>
                            <FontSize>7pt</FontSize>
                            <Format>n2</Format>
                            <VerticalAlign>Middle</VerticalAlign>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                          <ZIndex>4</ZIndex>
                          <CanGrow>true</CanGrow>
                          <Value>=Fields!GPMAIL.Value</Value>
                        </Textbox>
                      </ReportItems>
                    </TableCell>
                    <TableCell>
                      <ReportItems>
                        <Textbox Name="CPMAIL">
                          <rd:DefaultName>CPMAIL</rd:DefaultName>
                          <Style>
                            <BorderColor>
                              <Default>DarkGray</Default>
                            </BorderColor>
                            <FontFamily>Tahoma</FontFamily>
                            <FontSize>7pt</FontSize>
                            <Format>n2</Format>
                            <VerticalAlign>Middle</VerticalAlign>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                          <ZIndex>3</ZIndex>
                          <CanGrow>true</CanGrow>
                          <Value>=Fields!CPMAIL.Value</Value>
                        </Textbox>
                      </ReportItems>
                    </TableCell>
                    <TableCell>
                      <ReportItems>
                        <Textbox Name="NPMAIL">
                          <rd:DefaultName>NPMAIL</rd:DefaultName>
                          <Style>
                            <BorderColor>
                              <Default>DarkGray</Default>
                            </BorderColor>
                            <FontFamily>Tahoma</FontFamily>
                            <FontSize>7pt</FontSize>
                            <Format>n2</Format>
                            <VerticalAlign>Middle</VerticalAlign>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                          <ZIndex>2</ZIndex>
                          <CanGrow>true</CanGrow>
                          <Value>=Fields!NPMAIL.Value</Value>
                        </Textbox>
                      </ReportItems>
                    </TableCell>
                    <TableCell>
                      <ReportItems>
                        <Textbox Name="GPDONTN">
                          <rd:DefaultName>GPDONTN</rd:DefaultName>
                          <Style>
                            <BorderColor>
                              <Default>DarkGray</Default>
                            </BorderColor>
                            <FontFamily>Tahoma</FontFamily>
                            <FontSize>7pt</FontSize>
                            <Format>n2</Format>
                            <VerticalAlign>Middle</VerticalAlign>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                          <ZIndex>1</ZIndex>
                          <CanGrow>true</CanGrow>
                          <Value>=Fields!GPDONTN.Value</Value>
                        </Textbox>
                      </ReportItems>
                    </TableCell>
                    <TableCell>
                      <ReportItems>
                        <Textbox Name="NPDONTN">
                          <rd:DefaultName>NPDONTN</rd:DefaultName>
                          <Style>
                            <BorderColor>
                              <Default>DarkGray</Default>
                            </BorderColor>
                            <FontFamily>Tahoma</FontFamily>
                            <FontSize>7pt</FontSize>
                            <Format>n2</Format>
                            <VerticalAlign>Middle</VerticalAlign>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                          <CanGrow>true</CanGrow>
                          <Value>=Fields!ROI.Value</Value>
                        </Textbox>
                      </ReportItems>
                    </TableCell>
                  </TableCells>
                  <Height>0.21594in</Height>
                </TableRow>
              </TableRows>
            </Details>
            <Style>
              <FontFamily>Tahoma</FontFamily>
              <FontSize>7pt</FontSize>
            </Style>
            <Header>
              <TableRows>
                <TableRow>
                  <TableCells>
                    <TableCell>
                      <ReportItems>
                        <Textbox Name="textbox125">
                          <rd:DefaultName>textbox125</rd:DefaultName>
                          <Style>
                            <Color>DimGray</Color>
                            <BorderColor>
                              <Default>DimGray</Default>
                            </BorderColor>
                            <BorderStyle>
                              <Left>Solid</Left>
                              <Top>Solid</Top>
                            </BorderStyle>
                            <FontFamily>Tahoma</FontFamily>
                            <FontSize>7pt</FontSize>
                            <FontWeight>700</FontWeight>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                          <ZIndex>217</ZIndex>
                          <CanGrow>true</CanGrow>
                          <Value>Program</Value>
                        </Textbox>
                      </ReportItems>
                    </TableCell>
                    <TableCell>
                      <ColSpan>2</ColSpan>
                      <ReportItems>
                        <Textbox Name="tx_program">
                          <Style>
                            <Color>SteelBlue</Color>
                            <BorderColor>
                              <Default>DimGray</Default>
                            </BorderColor>
                            <BorderStyle>
                              <Right>=IIF(Parameters!show_details.Value=True,"Solid","None")</Right>
                              <Top>Solid</Top>
                            </BorderStyle>
                            <FontFamily>Tahoma</FontFamily>
                            <FontSize>7pt</FontSize>
                            <FontWeight>700</FontWeight>
                            <VerticalAlign>Bottom</VerticalAlign>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                            <Language>af</Language>
                          </Style>
                          <ZIndex>216</ZIndex>
                          <CanGrow>true</CanGrow>
                          <Value>=(Trim(Fields!PROGTYPE.Value) + " - " + Fields!DESCRIP.Value)</Value>
                        </Textbox>
                      </ReportItems>
                    </TableCell>
                    <TableCell>
                      <ReportItems>
                        <Textbox Name="textbox128">
                          <rd:DefaultName>textbox128</rd:DefaultName>
                          <Style>
                            <BackgroundColor>WhiteSmoke</BackgroundColor>
                            <BorderColor>
                              <Default>DimGray</Default>
                            </BorderColor>
                            <BorderStyle>
                              <Left>=IIF(Parameters!show_details.Value=True,"Solid","None")</Left>
                            </BorderStyle>
                            <FontFamily>Tahoma</FontFamily>
                            <FontSize>7pt</FontSize>
                            <FontWeight>700</FontWeight>
                            <TextAlign>Center</TextAlign>
                            <VerticalAlign>Middle</VerticalAlign>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                          <ZIndex>215</ZIndex>
                          <CanGrow>true</CanGrow>
                          <Value />
                        </Textbox>
                      </ReportItems>
                    </TableCell>
                    <TableCell>
                      <ReportItems>
                        <Textbox Name="textbox129">
                          <rd:DefaultName>textbox129</rd:DefaultName>
                          <Style>
                            <BackgroundColor>WhiteSmoke</BackgroundColor>
                            <BorderColor>
                              <Default>DarkGray</Default>
                            </BorderColor>
                            <FontFamily>Tahoma</FontFamily>
                            <FontSize>7pt</FontSize>
                            <FontWeight>700</FontWeight>
                            <TextAlign>Center</TextAlign>
                            <VerticalAlign>Middle</VerticalAlign>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                          <ZIndex>214</ZIndex>
                          <CanGrow>true</CanGrow>
                          <Value />
                        </Textbox>
                      </ReportItems>
                    </TableCell>
                    <TableCell>
                      <ReportItems>
                        <Textbox Name="textbox130">
                          <rd:DefaultName>textbox130</rd:DefaultName>
                          <Style>
                            <BackgroundColor>=IIF(Parameters!show_details.Value=TRUE,"LightSalmon","SkyBlue")</BackgroundColor>
                            <BorderColor>
                              <Default>DarkGray</Default>
                            </BorderColor>
                            <FontFamily>Tahoma</FontFamily>
                            <FontSize>7pt</FontSize>
                            <FontWeight>700</FontWeight>
                            <TextAlign>Center</TextAlign>
                            <VerticalAlign>Middle</VerticalAlign>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                          <ZIndex>213</ZIndex>
                          <CanGrow>true</CanGrow>
                          <Value />
                        </Textbox>
                      </ReportItems>
                    </TableCell>
                    <TableCell>
                      <ReportItems>
                        <Textbox Name="textbox131">
                          <rd:DefaultName>textbox131</rd:DefaultName>
                          <Style>
                            <BackgroundColor>=IIF(Parameters!show_details.Value=TRUE,"LightSalmon","SkyBlue")</BackgroundColor>
                            <BorderColor>
                              <Default>DarkGray</Default>
                            </BorderColor>
                            <FontFamily>Tahoma</FontFamily>
                            <FontSize>7pt</FontSize>
                            <FontWeight>700</FontWeight>
                            <TextAlign>Center</TextAlign>
                            <VerticalAlign>Middle</VerticalAlign>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                          <ZIndex>212</ZIndex>
                          <CanGrow>true</CanGrow>
                          <Value />
                        </Textbox>
                      </ReportItems>
                    </TableCell>
                    <TableCell>
                      <ReportItems>
                        <Textbox Name="textbox132">
                          <rd:DefaultName>textbox132</rd:DefaultName>
                          <Style>
                            <BackgroundColor>WhiteSmoke</BackgroundColor>
                            <BorderColor>
                              <Default>DarkGray</Default>
                            </BorderColor>
                            <FontFamily>Tahoma</FontFamily>
                            <FontSize>7pt</FontSize>
                            <FontWeight>700</FontWeight>
                            <TextAlign>Center</TextAlign>
                            <VerticalAlign>Middle</VerticalAlign>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                          <ZIndex>211</ZIndex>
                          <CanGrow>true</CanGrow>
                          <Value />
                        </Textbox>
                      </ReportItems>
                    </TableCell>
                    <TableCell>
                      <ReportItems>
                        <Textbox Name="textbox133">
                          <rd:DefaultName>textbox133</rd:DefaultName>
                          <Style>
                            <BackgroundColor>WhiteSmoke</BackgroundColor>
                            <BorderColor>
                              <Default>DarkGray</Default>
                            </BorderColor>
                            <FontFamily>Tahoma</FontFamily>
                            <FontSize>7pt</FontSize>
                            <FontWeight>700</FontWeight>
                            <TextAlign>Center</TextAlign>
                            <VerticalAlign>Middle</VerticalAlign>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                          <ZIndex>210</ZIndex>
                          <CanGrow>true</CanGrow>
                          <Value />
                        </Textbox>
                      </ReportItems>
                    </TableCell>
                    <TableCell>
                      <ReportItems>
                        <Textbox Name="textbox134">
                          <rd:DefaultName>textbox134</rd:DefaultName>
                          <Style>
                            <BackgroundColor>=IIF(Parameters!show_details.Value=TRUE,"LightSalmon","SkyBlue")</BackgroundColor>
                            <BorderColor>
                              <Default>DarkGray</Default>
                            </BorderColor>
                            <FontFamily>Tahoma</FontFamily>
                            <FontSize>7pt</FontSize>
                            <FontWeight>700</FontWeight>
                            <TextAlign>Center</TextAlign>
                            <VerticalAlign>Middle</VerticalAlign>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                          <ZIndex>209</ZIndex>
                          <CanGrow>true</CanGrow>
                          <Value />
                        </Textbox>
                      </ReportItems>
                    </TableCell>
                    <TableCell>
                      <ReportItems>
                        <Textbox Name="textbox135">
                          <rd:DefaultName>textbox135</rd:DefaultName>
                          <Style>
                            <BackgroundColor>=IIF(Parameters!show_details.Value=TRUE,"LightSalmon","SkyBlue")</BackgroundColor>
                            <BorderColor>
                              <Default>DarkGray</Default>
                            </BorderColor>
                            <FontFamily>Tahoma</FontFamily>
                            <FontSize>7pt</FontSize>
                            <FontWeight>700</FontWeight>
                            <TextAlign>Center</TextAlign>
                            <VerticalAlign>Middle</VerticalAlign>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                          <ZIndex>208</ZIndex>
                          <CanGrow>true</CanGrow>
                          <Value />
                        </Textbox>
                      </ReportItems>
                    </TableCell>
                    <TableCell>
                      <ReportItems>
                        <Textbox Name="textbox136">
                          <rd:DefaultName>textbox136</rd:DefaultName>
                          <Style>
                            <BackgroundColor>=IIF(Parameters!show_details.Value=TRUE,"LightSalmon","SkyBlue")</BackgroundColor>
                            <BorderColor>
                              <Default>DarkGray</Default>
                            </BorderColor>
                            <FontFamily>Tahoma</FontFamily>
                            <FontSize>7pt</FontSize>
                            <FontWeight>700</FontWeight>
                            <TextAlign>Center</TextAlign>
                            <VerticalAlign>Middle</VerticalAlign>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                          <ZIndex>207</ZIndex>
                          <CanGrow>true</CanGrow>
                          <Value />
                        </Textbox>
                      </ReportItems>
                    </TableCell>
                    <TableCell>
                      <ReportItems>
                        <Textbox Name="textbox137">
                          <rd:DefaultName>textbox137</rd:DefaultName>
                          <Style>
                            <BackgroundColor>WhiteSmoke</BackgroundColor>
                            <BorderColor>
                              <Default>DarkGray</Default>
                            </BorderColor>
                            <FontFamily>Tahoma</FontFamily>
                            <FontSize>7pt</FontSize>
                            <FontWeight>700</FontWeight>
                            <TextAlign>Center</TextAlign>
                            <VerticalAlign>Middle</VerticalAlign>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                          <ZIndex>206</ZIndex>
                          <CanGrow>true</CanGrow>
                          <Value />
                        </Textbox>
                      </ReportItems>
                    </TableCell>
                    <TableCell>
                      <ReportItems>
                        <Textbox Name="textbox138">
                          <rd:DefaultName>textbox138</rd:DefaultName>
                          <Style>
                            <BackgroundColor>WhiteSmoke</BackgroundColor>
                            <BorderColor>
                              <Default>DarkGray</Default>
                            </BorderColor>
                            <FontFamily>Tahoma</FontFamily>
                            <FontSize>7pt</FontSize>
                            <FontWeight>700</FontWeight>
                            <TextAlign>Center</TextAlign>
                            <VerticalAlign>Middle</VerticalAlign>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                          <ZIndex>205</ZIndex>
                          <CanGrow>true</CanGrow>
                          <Value />
                        </Textbox>
                      </ReportItems>
                    </TableCell>
                    <TableCell>
                      <ReportItems>
                        <Textbox Name="textbox139">
                          <rd:DefaultName>textbox139</rd:DefaultName>
                          <Style>
                            <BackgroundColor>WhiteSmoke</BackgroundColor>
                            <BorderColor>
                              <Default>DarkGray</Default>
                            </BorderColor>
                            <FontFamily>Tahoma</FontFamily>
                            <FontSize>7pt</FontSize>
                            <FontWeight>700</FontWeight>
                            <TextAlign>Center</TextAlign>
                            <VerticalAlign>Middle</VerticalAlign>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                          <ZIndex>204</ZIndex>
                          <CanGrow>true</CanGrow>
                          <Value />
                        </Textbox>
                      </ReportItems>
                    </TableCell>
                    <TableCell>
                      <ReportItems>
                        <Textbox Name="textbox140">
                          <rd:DefaultName>textbox140</rd:DefaultName>
                          <Style>
                            <BackgroundColor>=IIF(Parameters!show_details.Value=TRUE,"LightSalmon","SkyBlue")</BackgroundColor>
                            <BorderColor>
                              <Default>DarkGray</Default>
                            </BorderColor>
                            <FontFamily>Tahoma</FontFamily>
                            <FontSize>7pt</FontSize>
                            <FontWeight>700</FontWeight>
                            <TextAlign>Center</TextAlign>
                            <VerticalAlign>Middle</VerticalAlign>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                          <ZIndex>203</ZIndex>
                          <CanGrow>true</CanGrow>
                          <Value />
                        </Textbox>
                      </ReportItems>
                    </TableCell>
                    <TableCell>
                      <ReportItems>
                        <Textbox Name="textbox141">
                          <rd:DefaultName>textbox141</rd:DefaultName>
                          <Style>
                            <BackgroundColor>=IIF(Parameters!show_details.Value=TRUE,"LightSalmon","SkyBlue")</BackgroundColor>
                            <BorderColor>
                              <Default>DimGray</Default>
                            </BorderColor>
                            <BorderStyle>
                              <Right>Solid</Right>
                            </BorderStyle>
                            <FontFamily>Tahoma</FontFamily>
                            <FontSize>7pt</FontSize>
                            <FontWeight>700</FontWeight>
                            <TextAlign>Center</TextAlign>
                            <VerticalAlign>Middle</VerticalAlign>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                          <ZIndex>202</ZIndex>
                          <CanGrow>true</CanGrow>
                          <Value />
                        </Textbox>
                      </ReportItems>
                    </TableCell>
                  </TableCells>
                  <Height>0.16in</Height>
                </TableRow>
                <TableRow>
                  <TableCells>
                    <TableCell>
                      <ReportItems>
                        <Textbox Name="textbox4">
                          <rd:DefaultName>textbox4</rd:DefaultName>
                          <Style>
                            <Color>DimGray</Color>
                            <BorderColor>
                              <Default>DimGray</Default>
                            </BorderColor>
                            <BorderStyle>
                              <Left>Solid</Left>
                              <Bottom>=IIF(Parameters!show_details.Value=True,"Solid","None")</Bottom>
                            </BorderStyle>
                            <FontFamily>Tahoma</FontFamily>
                            <FontSize>7pt</FontSize>
                            <FontWeight>700</FontWeight>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                          <ZIndex>201</ZIndex>
                          <CanGrow>true</CanGrow>
                          <Value />
                        </Textbox>
                      </ReportItems>
                    </TableCell>
                    <TableCell>
                      <ColSpan>2</ColSpan>
                      <ReportItems>
                        <Textbox Name="tx_package">
                          <Style>
                            <Color>SteelBlue</Color>
                            <BorderColor>
                              <Default>DimGray</Default>
                            </BorderColor>
                            <BorderStyle>
                              <Right>=IIF(Parameters!show_details.Value=True,"Solid","None")</Right>
                              <Bottom>=IIF(Parameters!show_details.Value=True,"Solid","None")</Bottom>
                            </BorderStyle>
                            <FontFamily>Tahoma</FontFamily>
                            <FontSize>7pt</FontSize>
                            <FontWeight>700</FontWeight>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                          <ZIndex>200</ZIndex>
                          <CanGrow>true</CanGrow>
                          <Value />
                        </Textbox>
                      </ReportItems>
                    </TableCell>
                    <TableCell>
                      <ColSpan>2</ColSpan>
                      <ReportItems>
                        <Textbox Name="textbox24">
                          <rd:DefaultName>textbox24</rd:DefaultName>
                          <Style>
                            <BackgroundColor>WhiteSmoke</BackgroundColor>
                            <BorderColor>
                              <Default>DimGray</Default>
                            </BorderColor>
                            <BorderStyle>
                              <Left>=IIF(Parameters!show_details.Value=True,"Solid","None")</Left>
                            </BorderStyle>
                            <FontFamily>Tahoma</FontFamily>
                            <FontSize>7pt</FontSize>
                            <FontWeight>700</FontWeight>
                            <TextAlign>Center</TextAlign>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                          <ZIndex>199</ZIndex>
                          <CanGrow>true</CanGrow>
                          <Value>Mail</Value>
                        </Textbox>
                      </ReportItems>
                    </TableCell>
                    <TableCell>
                      <ColSpan>2</ColSpan>
                      <ReportItems>
                        <Textbox Name="textbox69">
                          <rd:DefaultName>textbox69</rd:DefaultName>
                          <Style>
                            <BackgroundColor>=IIF(Parameters!show_details.Value=TRUE,"LightSalmon","SkyBlue")</BackgroundColor>
                            <BorderColor>
                              <Default>DarkGray</Default>
                            </BorderColor>
                            <FontFamily>Tahoma</FontFamily>
                            <FontSize>7pt</FontSize>
                            <FontWeight>700</FontWeight>
                            <TextAlign>Center</TextAlign>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                          <ZIndex>198</ZIndex>
                          <CanGrow>true</CanGrow>
                          <Value>Cage Date</Value>
                        </Textbox>
                      </ReportItems>
                    </TableCell>
                    <TableCell>
                      <ColSpan>2</ColSpan>
                      <ReportItems>
                        <Textbox Name="textbox71">
                          <rd:DefaultName>textbox71</rd:DefaultName>
                          <Style>
                            <BackgroundColor>WhiteSmoke</BackgroundColor>
                            <BorderColor>
                              <Default>DarkGray</Default>
                            </BorderColor>
                            <FontFamily>Tahoma</FontFamily>
                            <FontSize>7pt</FontSize>
                            <FontWeight>700</FontWeight>
                            <TextAlign>Center</TextAlign>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                          <ZIndex>197</ZIndex>
                          <CanGrow>true</CanGrow>
                          <Value>Response</Value>
                        </Textbox>
                      </ReportItems>
                    </TableCell>
                    <TableCell>
                      <ColSpan>3</ColSpan>
                      <ReportItems>
                        <Textbox Name="textbox70">
                          <rd:DefaultName>textbox70</rd:DefaultName>
                          <Style>
                            <BackgroundColor>=IIF(Parameters!show_details.Value=TRUE,"LightSalmon","SkyBlue")</BackgroundColor>
                            <BorderColor>
                              <Default>DarkGray</Default>
                            </BorderColor>
                            <FontFamily>Tahoma</FontFamily>
                            <FontSize>7pt</FontSize>
                            <FontWeight>700</FontWeight>
                            <TextAlign>Center</TextAlign>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                          <ZIndex>196</ZIndex>
                          <CanGrow>true</CanGrow>
                          <Value>Total ($)</Value>
                        </Textbox>
                      </ReportItems>
                    </TableCell>
                    <TableCell>
                      <ColSpan>3</ColSpan>
                      <ReportItems>
                        <Textbox Name="textbox76">
                          <rd:DefaultName>textbox76</rd:DefaultName>
                          <Style>
                            <BackgroundColor>WhiteSmoke</BackgroundColor>
                            <BorderColor>
                              <Default>DarkGray</Default>
                            </BorderColor>
                            <FontFamily>Tahoma</FontFamily>
                            <FontSize>7pt</FontSize>
                            <FontWeight>700</FontWeight>
                            <TextAlign>Center</TextAlign>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                          <ZIndex>195</ZIndex>
                          <CanGrow>true</CanGrow>
                          <Value>Per Letter ($)</Value>
                        </Textbox>
                      </ReportItems>
                    </TableCell>
                    <TableCell>
                      <ReportItems>
                        <Textbox Name="textbox82">
                          <rd:DefaultName>textbox82</rd:DefaultName>
                          <Style>
                            <BackgroundColor>=IIF(Parameters!show_details.Value=TRUE,"LightSalmon","SkyBlue")</BackgroundColor>
                            <BorderColor>
                              <Default>DimGray</Default>
                            </BorderColor>
                            <FontFamily>Tahoma</FontFamily>
                            <FontSize>7pt</FontSize>
                            <FontWeight>700</FontWeight>
                            <TextAlign>Center</TextAlign>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                          <ZIndex>194</ZIndex>
                          <CanGrow>true</CanGrow>
                          <Value>Averge</Value>
                        </Textbox>
                      </ReportItems>
                    </TableCell>
                    <TableCell>
                      <ReportItems>
                        <Textbox Name="textbox32">
                          <rd:DefaultName>textbox32</rd:DefaultName>
                          <Style>
                            <BackgroundColor>=IIF(Parameters!show_details.Value=TRUE,"LightSalmon","SkyBlue")</BackgroundColor>
                            <BorderColor>
                              <Default>DimGray</Default>
                            </BorderColor>
                            <BorderStyle>
                              <Right>Solid</Right>
                            </BorderStyle>
                            <FontFamily>Tahoma</FontFamily>
                            <FontSize>7pt</FontSize>
                            <FontWeight>700</FontWeight>
                            <TextAlign>Center</TextAlign>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                          <ZIndex>193</ZIndex>
                          <CanGrow>true</CanGrow>
                          <Value>ROI</Value>
                        </Textbox>
                      </ReportItems>
                    </TableCell>
                  </TableCells>
                  <Height>0.2in</Height>
                </TableRow>
                <TableRow>
                  <TableCells>
                    <TableCell>
                      <ReportItems>
                        <Textbox Name="textbox8">
                          <rd:DefaultName>textbox8</rd:DefaultName>
                          <Style>
                            <BackgroundColor>=IIF(Parameters!show_details.Value=TRUE,"LightSalmon","SkyBlue")</BackgroundColor>
                            <BorderColor>
                              <Default>DimGray</Default>
                            </BorderColor>
                            <BorderStyle>
                              <Top>=IIF(Parameters!show_details.Value=True,"Solid","None")</Top>
                              <Bottom>None</Bottom>
                            </BorderStyle>
                            <FontFamily>Tahoma</FontFamily>
                            <FontSize>7pt</FontSize>
                            <FontWeight>700</FontWeight>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                          <ZIndex>192</ZIndex>
                          <CanGrow>true</CanGrow>
                          <Value>Code</Value>
                        </Textbox>
                      </ReportItems>
                    </TableCell>
                    <TableCell>
                      <ReportItems>
                        <Textbox Name="textbox2">
                          <rd:DefaultName>textbox2</rd:DefaultName>
                          <Style>
                            <BackgroundColor>=IIF(Parameters!show_details.Value=TRUE,"LightSalmon","SkyBlue")</BackgroundColor>
                            <BorderColor>
                              <Default>DimGray</Default>
                            </BorderColor>
                            <BorderStyle>
                              <Top>=IIF(Parameters!show_details.Value=True,"Solid","None")</Top>
                              <Bottom>None</Bottom>
                            </BorderStyle>
                            <FontFamily>Tahoma</FontFamily>
                            <FontSize>7pt</FontSize>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                          <ZIndex>191</ZIndex>
                          <CanGrow>true</CanGrow>
                          <Value>#</Value>
                        </Textbox>
                      </ReportItems>
                    </TableCell>
                    <TableCell>
                      <ReportItems>
                        <Textbox Name="textbox9">
                          <rd:DefaultName>textbox9</rd:DefaultName>
                          <Style>
                            <BackgroundColor>=IIF(Parameters!show_details.Value=TRUE,"LightSalmon","SkyBlue")</BackgroundColor>
                            <BorderColor>
                              <Default>DimGray</Default>
                            </BorderColor>
                            <BorderStyle>
                              <Top>=IIF(Parameters!show_details.Value=True,"Solid","None")</Top>
                              <Bottom>None</Bottom>
                            </BorderStyle>
                            <FontFamily>Tahoma</FontFamily>
                            <FontSize>7pt</FontSize>
                            <FontWeight>700</FontWeight>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                          <ZIndex>190</ZIndex>
                          <CanGrow>true</CanGrow>
                          <Value>Description</Value>
                        </Textbox>
                      </ReportItems>
                    </TableCell>
                    <TableCell>
                      <ReportItems>
                        <Textbox Name="textbox10">
                          <rd:DefaultName>textbox10</rd:DefaultName>
                          <Style>
                            <BackgroundColor>WhiteSmoke</BackgroundColor>
                            <BorderColor>
                              <Default>DimGray</Default>
                            </BorderColor>
                            <BorderStyle>
                              <Bottom>None</Bottom>
                            </BorderStyle>
                            <FontFamily>Tahoma</FontFamily>
                            <FontSize>7pt</FontSize>
                            <TextAlign>Center</TextAlign>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                          <ZIndex>189</ZIndex>
                          <CanGrow>true</CanGrow>
                          <Value>Date</Value>
                        </Textbox>
                      </ReportItems>
                    </TableCell>
                    <TableCell>
                      <ReportItems>
                        <Textbox Name="textbox11">
                          <rd:DefaultName>textbox11</rd:DefaultName>
                          <Style>
                            <BackgroundColor>WhiteSmoke</BackgroundColor>
                            <BorderColor>
                              <Default>DimGray</Default>
                            </BorderColor>
                            <BorderStyle>
                              <Bottom>None</Bottom>
                            </BorderStyle>
                            <FontFamily>Tahoma</FontFamily>
                            <FontSize>7pt</FontSize>
                            <TextAlign>Center</TextAlign>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                          <ZIndex>188</ZIndex>
                          <CanGrow>true</CanGrow>
                          <Value>Qty.</Value>
                        </Textbox>
                      </ReportItems>
                    </TableCell>
                    <TableCell>
                      <ReportItems>
                        <Textbox Name="textbox12">
                          <rd:DefaultName>textbox12</rd:DefaultName>
                          <Style>
                            <BackgroundColor>=IIF(Parameters!show_details.Value=TRUE,"LightSalmon","SkyBlue")</BackgroundColor>
                            <BorderColor>
                              <Default>DimGray</Default>
                            </BorderColor>
                            <BorderStyle>
                              <Bottom>None</Bottom>
                            </BorderStyle>
                            <FontFamily>Tahoma</FontFamily>
                            <FontSize>7pt</FontSize>
                            <TextAlign>Center</TextAlign>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                          <ZIndex>187</ZIndex>
                          <CanGrow>true</CanGrow>
                          <Value>First</Value>
                        </Textbox>
                      </ReportItems>
                    </TableCell>
                    <TableCell>
                      <ReportItems>
                        <Textbox Name="textbox13">
                          <rd:DefaultName>textbox13</rd:DefaultName>
                          <Style>
                            <BackgroundColor>=IIF(Parameters!show_details.Value=TRUE,"LightSalmon","SkyBlue")</BackgroundColor>
                            <BorderColor>
                              <Default>DimGray</Default>
                            </BorderColor>
                            <BorderStyle>
                              <Bottom>None</Bottom>
                            </BorderStyle>
                            <FontFamily>Tahoma</FontFamily>
                            <FontSize>7pt</FontSize>
                            <TextAlign>Center</TextAlign>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                          <ZIndex>186</ZIndex>
                          <CanGrow>true</CanGrow>
                          <Value>Last</Value>
                        </Textbox>
                      </ReportItems>
                    </TableCell>
                    <TableCell>
                      <ReportItems>
                        <Textbox Name="textbox14">
                          <rd:DefaultName>textbox14</rd:DefaultName>
                          <Style>
                            <BackgroundColor>WhiteSmoke</BackgroundColor>
                            <BorderColor>
                              <Default>DimGray</Default>
                            </BorderColor>
                            <BorderStyle>
                              <Bottom>None</Bottom>
                            </BorderStyle>
                            <FontFamily>Tahoma</FontFamily>
                            <FontSize>7pt</FontSize>
                            <TextAlign>Right</TextAlign>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                          <ZIndex>185</ZIndex>
                          <CanGrow>true</CanGrow>
                          <Value>#Gifts</Value>
                        </Textbox>
                      </ReportItems>
                    </TableCell>
                    <TableCell>
                      <ReportItems>
                        <Textbox Name="textbox15">
                          <rd:DefaultName>textbox15</rd:DefaultName>
                          <Style>
                            <BackgroundColor>WhiteSmoke</BackgroundColor>
                            <BorderColor>
                              <Default>DimGray</Default>
                            </BorderColor>
                            <BorderStyle>
                              <Bottom>None</Bottom>
                            </BorderStyle>
                            <FontFamily>Tahoma</FontFamily>
                            <FontSize>7pt</FontSize>
                            <TextAlign>Right</TextAlign>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                          <ZIndex>184</ZIndex>
                          <CanGrow>true</CanGrow>
                          <Value>%</Value>
                        </Textbox>
                      </ReportItems>
                    </TableCell>
                    <TableCell>
                      <ReportItems>
                        <Textbox Name="textbox16">
                          <rd:DefaultName>textbox16</rd:DefaultName>
                          <Style>
                            <BackgroundColor>=IIF(Parameters!show_details.Value=TRUE,"LightSalmon","SkyBlue")</BackgroundColor>
                            <BorderColor>
                              <Default>DimGray</Default>
                            </BorderColor>
                            <BorderStyle>
                              <Bottom>None</Bottom>
                            </BorderStyle>
                            <FontFamily>Tahoma</FontFamily>
                            <FontSize>7pt</FontSize>
                            <TextAlign>Right</TextAlign>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                          <ZIndex>183</ZIndex>
                          <CanGrow>true</CanGrow>
                          <Value>Gross</Value>
                        </Textbox>
                      </ReportItems>
                    </TableCell>
                    <TableCell>
                      <ReportItems>
                        <Textbox Name="textbox17">
                          <rd:DefaultName>textbox17</rd:DefaultName>
                          <Style>
                            <BackgroundColor>=IIF(Parameters!show_details.Value=TRUE,"LightSalmon","SkyBlue")</BackgroundColor>
                            <BorderColor>
                              <Default>DimGray</Default>
                            </BorderColor>
                            <BorderStyle>
                              <Bottom>None</Bottom>
                            </BorderStyle>
                            <FontFamily>Tahoma</FontFamily>
                            <FontSize>7pt</FontSize>
                            <TextAlign>Right</TextAlign>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                          <ZIndex>182</ZIndex>
                          <CanGrow>true</CanGrow>
                          <Value>Cost</Value>
                        </Textbox>
                      </ReportItems>
                    </TableCell>
                    <TableCell>
                      <ReportItems>
                        <Textbox Name="textbox18">
                          <rd:DefaultName>textbox18</rd:DefaultName>
                          <Style>
                            <BackgroundColor>=IIF(Parameters!show_details.Value=TRUE,"LightSalmon","SkyBlue")</BackgroundColor>
                            <BorderColor>
                              <Default>DimGray</Default>
                            </BorderColor>
                            <BorderStyle>
                              <Bottom>None</Bottom>
                            </BorderStyle>
                            <FontFamily>Tahoma</FontFamily>
                            <FontSize>7pt</FontSize>
                            <TextAlign>Right</TextAlign>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                          <ZIndex>181</ZIndex>
                          <CanGrow>true</CanGrow>
                          <Value>Net</Value>
                        </Textbox>
                      </ReportItems>
                    </TableCell>
                    <TableCell>
                      <ReportItems>
                        <Textbox Name="textbox19">
                          <rd:DefaultName>textbox19</rd:DefaultName>
                          <Style>
                            <BackgroundColor>WhiteSmoke</BackgroundColor>
                            <BorderColor>
                              <Default>DimGray</Default>
                            </BorderColor>
                            <BorderStyle>
                              <Bottom>None</Bottom>
                            </BorderStyle>
                            <FontFamily>Tahoma</FontFamily>
                            <FontSize>7pt</FontSize>
                            <TextAlign>Right</TextAlign>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                          <ZIndex>180</ZIndex>
                          <CanGrow>true</CanGrow>
                          <Value>Gross</Value>
                        </Textbox>
                      </ReportItems>
                    </TableCell>
                    <TableCell>
                      <ReportItems>
                        <Textbox Name="textbox20">
                          <rd:DefaultName>textbox20</rd:DefaultName>
                          <Style>
                            <BackgroundColor>WhiteSmoke</BackgroundColor>
                            <BorderColor>
                              <Default>DimGray</Default>
                            </BorderColor>
                            <BorderStyle>
                              <Bottom>None</Bottom>
                            </BorderStyle>
                            <FontFamily>Tahoma</FontFamily>
                            <FontSize>7pt</FontSize>
                            <TextAlign>Right</TextAlign>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                          <ZIndex>179</ZIndex>
                          <CanGrow>true</CanGrow>
                          <Value>Cost</Value>
                        </Textbox>
                      </ReportItems>
                    </TableCell>
                    <TableCell>
                      <ReportItems>
                        <Textbox Name="textbox21">
                          <rd:DefaultName>textbox21</rd:DefaultName>
                          <Style>
                            <BackgroundColor>WhiteSmoke</BackgroundColor>
                            <BorderColor>
                              <Default>DimGray</Default>
                            </BorderColor>
                            <BorderStyle>
                              <Bottom>None</Bottom>
                            </BorderStyle>
                            <FontFamily>Tahoma</FontFamily>
                            <FontSize>7pt</FontSize>
                            <TextAlign>Right</TextAlign>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                          <ZIndex>178</ZIndex>
                          <CanGrow>true</CanGrow>
                          <Value>Net</Value>
                        </Textbox>
                      </ReportItems>
                    </TableCell>
                    <TableCell>
                      <ReportItems>
                        <Textbox Name="textbox22">
                          <rd:DefaultName>textbox22</rd:DefaultName>
                          <Style>
                            <BackgroundColor>=IIF(Parameters!show_details.Value=TRUE,"LightSalmon","SkyBlue")</BackgroundColor>
                            <BorderColor>
                              <Default>DimGray</Default>
                            </BorderColor>
                            <BorderStyle>
                              <Bottom>None</Bottom>
                            </BorderStyle>
                            <FontFamily>Tahoma</FontFamily>
                            <FontSize>7pt</FontSize>
                            <FontWeight>700</FontWeight>
                            <TextAlign>Center</TextAlign>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                          <ZIndex>177</ZIndex>
                          <CanGrow>true</CanGrow>
                          <Value>Gift($)</Value>
                        </Textbox>
                      </ReportItems>
                    </TableCell>
                    <TableCell>
                      <ReportItems>
                        <Textbox Name="textbox23">
                          <rd:DefaultName>textbox23</rd:DefaultName>
                          <Style>
                            <BackgroundColor>=IIF(Parameters!show_details.Value=TRUE,"LightSalmon","SkyBlue")</BackgroundColor>
                            <BorderColor>
                              <Default>DimGray</Default>
                            </BorderColor>
                            <BorderStyle>
                              <Right>Solid</Right>
                              <Bottom>None</Bottom>
                            </BorderStyle>
                            <FontFamily>Tahoma</FontFamily>
                            <FontSize>7pt</FontSize>
                            <TextAlign>Right</TextAlign>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                          <ZIndex>176</ZIndex>
                          <CanGrow>true</CanGrow>
                          <Value>Gros/Cos</Value>
                        </Textbox>
                      </ReportItems>
                    </TableCell>
                  </TableCells>
                  <Height>0.17in</Height>
                </TableRow>
                <TableRow>
                  <Visibility>
                    <Hidden>=(Parameters!show_details.Value=FALSE)</Hidden>
                  </Visibility>
                  <TableCells>
                    <TableCell>
                      <ReportItems>
                        <Textbox Name="textbox42">
                          <rd:DefaultName>textbox42</rd:DefaultName>
                          <Style>
                            <BackgroundColor>LightSalmon</BackgroundColor>
                            <BorderColor>
                              <Default>DimGray</Default>
                            </BorderColor>
                            <BorderStyle>
                              <Top>None</Top>
                              <Bottom>Solid</Bottom>
                            </BorderStyle>
                            <FontFamily>Tahoma</FontFamily>
                            <FontSize>7pt</FontSize>
                            <FontWeight>700</FontWeight>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                          <ZIndex>175</ZIndex>
                          <CanGrow>true</CanGrow>
                          <UserSort>
                            <SortExpression>=Fields!SRCECODE.Value</SortExpression>
                            <SortExpressionScope>tb_RespAnalysis_group_Source</SortExpressionScope>
                            <SortTarget>tb_RespAnalysis</SortTarget>
                          </UserSort>
                          <Value />
                        </Textbox>
                      </ReportItems>
                    </TableCell>
                    <TableCell>
                      <ReportItems>
                        <Textbox Name="textbox50">
                          <rd:DefaultName>textbox50</rd:DefaultName>
                          <Style>
                            <BackgroundColor>LightSalmon</BackgroundColor>
                            <BorderColor>
                              <Default>DimGray</Default>
                            </BorderColor>
                            <BorderStyle>
                              <Top>None</Top>
                              <Bottom>Solid</Bottom>
                            </BorderStyle>
                            <FontFamily>Tahoma</FontFamily>
                            <FontSize>7pt</FontSize>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                          <ZIndex>174</ZIndex>
                          <CanGrow>true</CanGrow>
                          <Value />
                        </Textbox>
                      </ReportItems>
                    </TableCell>
                    <TableCell>
                      <ReportItems>
                        <Textbox Name="textbox89">
                          <rd:DefaultName>textbox89</rd:DefaultName>
                          <Style>
                            <BackgroundColor>LightSalmon</BackgroundColor>
                            <BorderColor>
                              <Default>DimGray</Default>
                            </BorderColor>
                            <BorderStyle>
                              <Top>None</Top>
                              <Bottom>Solid</Bottom>
                            </BorderStyle>
                            <FontFamily>Tahoma</FontFamily>
                            <FontSize>7pt</FontSize>
                            <FontWeight>700</FontWeight>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                          <ZIndex>173</ZIndex>
                          <CanGrow>true</CanGrow>
                          <UserSort>
                            <SortExpression>=Fields!SRCEDESC.Value</SortExpression>
                            <SortExpressionScope>tb_RespAnalysis_group_Source</SortExpressionScope>
                            <SortTarget>list_RespAnalysis</SortTarget>
                          </UserSort>
                          <Value />
                        </Textbox>
                      </ReportItems>
                    </TableCell>
                    <TableCell>
                      <ReportItems>
                        <Textbox Name="textbox110">
                          <rd:DefaultName>textbox110</rd:DefaultName>
                          <Style>
                            <BackgroundColor>WhiteSmoke</BackgroundColor>
                            <BorderColor>
                              <Default>DimGray</Default>
                            </BorderColor>
                            <BorderStyle>
                              <Bottom>Solid</Bottom>
                            </BorderStyle>
                            <FontFamily>Tahoma</FontFamily>
                            <FontSize>7pt</FontSize>
                            <TextAlign>Center</TextAlign>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                          <ZIndex>172</ZIndex>
                          <CanGrow>true</CanGrow>
                          <UserSort>
                            <SortExpression>=Fields!MAILDATE.Value</SortExpression>
                            <SortExpressionScope>tb_RespAnalysis_group_Source</SortExpressionScope>
                            <SortTarget>tb_RespAnalysis</SortTarget>
                          </UserSort>
                          <Value />
                        </Textbox>
                      </ReportItems>
                    </TableCell>
                    <TableCell>
                      <ReportItems>
                        <Textbox Name="textbox145">
                          <rd:DefaultName>textbox145</rd:DefaultName>
                          <Style>
                            <BackgroundColor>WhiteSmoke</BackgroundColor>
                            <BorderColor>
                              <Default>DimGray</Default>
                            </BorderColor>
                            <BorderStyle>
                              <Bottom>Solid</Bottom>
                            </BorderStyle>
                            <FontFamily>Tahoma</FontFamily>
                            <FontSize>7pt</FontSize>
                            <TextAlign>Center</TextAlign>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                          <ZIndex>171</ZIndex>
                          <CanGrow>true</CanGrow>
                          <UserSort>
                            <SortExpression>=Fields!MAILQTY.Value</SortExpression>
                            <SortExpressionScope>tb_RespAnalysis_group_Source</SortExpressionScope>
                            <SortTarget>tb_RespAnalysis</SortTarget>
                          </UserSort>
                          <Value />
                        </Textbox>
                      </ReportItems>
                    </TableCell>
                    <TableCell>
                      <ReportItems>
                        <Textbox Name="textbox146">
                          <rd:DefaultName>textbox146</rd:DefaultName>
                          <Style>
                            <BackgroundColor>LightSalmon</BackgroundColor>
                            <BorderColor>
                              <Default>DimGray</Default>
                            </BorderColor>
                            <BorderStyle>
                              <Bottom>Solid</Bottom>
                            </BorderStyle>
                            <FontFamily>Tahoma</FontFamily>
                            <FontSize>7pt</FontSize>
                            <TextAlign>Center</TextAlign>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                          <ZIndex>170</ZIndex>
                          <CanGrow>true</CanGrow>
                          <UserSort>
                            <SortExpression>=Fields!FIRSTCAGE.Value</SortExpression>
                            <SortExpressionScope>tb_RespAnalysis_group_Source</SortExpressionScope>
                            <SortTarget>tb_RespAnalysis</SortTarget>
                          </UserSort>
                          <Value />
                        </Textbox>
                      </ReportItems>
                    </TableCell>
                    <TableCell>
                      <ReportItems>
                        <Textbox Name="textbox147">
                          <rd:DefaultName>textbox147</rd:DefaultName>
                          <Style>
                            <BackgroundColor>LightSalmon</BackgroundColor>
                            <BorderColor>
                              <Default>DimGray</Default>
                            </BorderColor>
                            <BorderStyle>
                              <Bottom>Solid</Bottom>
                            </BorderStyle>
                            <FontFamily>Tahoma</FontFamily>
                            <FontSize>7pt</FontSize>
                            <TextAlign>Center</TextAlign>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                          <ZIndex>169</ZIndex>
                          <CanGrow>true</CanGrow>
                          <UserSort>
                            <SortExpression>=Fields!LASTCAGE.Value</SortExpression>
                            <SortExpressionScope>tb_RespAnalysis_group_Source</SortExpressionScope>
                            <SortTarget>tb_RespAnalysis</SortTarget>
                          </UserSort>
                          <Value />
                        </Textbox>
                      </ReportItems>
                    </TableCell>
                    <TableCell>
                      <ReportItems>
                        <Textbox Name="textbox148">
                          <rd:DefaultName>textbox148</rd:DefaultName>
                          <Style>
                            <BackgroundColor>WhiteSmoke</BackgroundColor>
                            <BorderColor>
                              <Default>DimGray</Default>
                            </BorderColor>
                            <BorderStyle>
                              <Bottom>Solid</Bottom>
                            </BorderStyle>
                            <FontFamily>Tahoma</FontFamily>
                            <FontSize>7pt</FontSize>
                            <TextAlign>Right</TextAlign>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                          <ZIndex>168</ZIndex>
                          <CanGrow>true</CanGrow>
                          <UserSort>
                            <SortExpression>=Fields!DONATIONS.Value</SortExpression>
                            <SortExpressionScope>tb_RespAnalysis_group_Source</SortExpressionScope>
                            <SortTarget>tb_RespAnalysis</SortTarget>
                          </UserSort>
                          <Value />
                        </Textbox>
                      </ReportItems>
                    </TableCell>
                    <TableCell>
                      <ReportItems>
                        <Textbox Name="textbox149">
                          <rd:DefaultName>textbox149</rd:DefaultName>
                          <Style>
                            <BackgroundColor>WhiteSmoke</BackgroundColor>
                            <BorderColor>
                              <Default>DimGray</Default>
                            </BorderColor>
                            <BorderStyle>
                              <Bottom>Solid</Bottom>
                            </BorderStyle>
                            <FontFamily>Tahoma</FontFamily>
                            <FontSize>7pt</FontSize>
                            <TextAlign>Right</TextAlign>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                          <ZIndex>167</ZIndex>
                          <CanGrow>true</CanGrow>
                          <UserSort>
                            <SortExpression>=Fields!RESPONSE.Value</SortExpression>
                            <SortExpressionScope>tb_RespAnalysis_group_Source</SortExpressionScope>
                            <SortTarget>tb_RespAnalysis</SortTarget>
                          </UserSort>
                          <Value />
                        </Textbox>
                      </ReportItems>
                    </TableCell>
                    <TableCell>
                      <ReportItems>
                        <Textbox Name="textbox150">
                          <rd:DefaultName>textbox150</rd:DefaultName>
                          <Style>
                            <BackgroundColor>LightSalmon</BackgroundColor>
                            <BorderColor>
                              <Default>DimGray</Default>
                            </BorderColor>
                            <BorderStyle>
                              <Bottom>Solid</Bottom>
                            </BorderStyle>
                            <FontFamily>Tahoma</FontFamily>
                            <FontSize>7pt</FontSize>
                            <TextAlign>Right</TextAlign>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                          <ZIndex>166</ZIndex>
                          <CanGrow>true</CanGrow>
                          <UserSort>
                            <SortExpression>=Fields!GROSS.Value</SortExpression>
                            <SortExpressionScope>tb_RespAnalysis_group_Source</SortExpressionScope>
                            <SortTarget>tb_RespAnalysis</SortTarget>
                          </UserSort>
                          <Value />
                        </Textbox>
                      </ReportItems>
                    </TableCell>
                    <TableCell>
                      <ReportItems>
                        <Textbox Name="textbox151">
                          <rd:DefaultName>textbox151</rd:DefaultName>
                          <Style>
                            <BackgroundColor>LightSalmon</BackgroundColor>
                            <BorderColor>
                              <Default>DimGray</Default>
                            </BorderColor>
                            <BorderStyle>
                              <Bottom>Solid</Bottom>
                            </BorderStyle>
                            <FontFamily>Tahoma</FontFamily>
                            <FontSize>7pt</FontSize>
                            <TextAlign>Right</TextAlign>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                          <ZIndex>165</ZIndex>
                          <CanGrow>true</CanGrow>
                          <UserSort>
                            <SortExpression>=Fields!COST.Value</SortExpression>
                            <SortExpressionScope>tb_RespAnalysis_group_Source</SortExpressionScope>
                            <SortTarget>tb_RespAnalysis</SortTarget>
                          </UserSort>
                          <Value />
                        </Textbox>
                      </ReportItems>
                    </TableCell>
                    <TableCell>
                      <ReportItems>
                        <Textbox Name="textbox152">
                          <rd:DefaultName>textbox152</rd:DefaultName>
                          <Style>
                            <BackgroundColor>LightSalmon</BackgroundColor>
                            <BorderColor>
                              <Default>DimGray</Default>
                            </BorderColor>
                            <BorderStyle>
                              <Bottom>Solid</Bottom>
                            </BorderStyle>
                            <FontFamily>Tahoma</FontFamily>
                            <FontSize>7pt</FontSize>
                            <TextAlign>Right</TextAlign>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                          <ZIndex>164</ZIndex>
                          <CanGrow>true</CanGrow>
                          <UserSort>
                            <SortExpression>=Fields!NET.Value</SortExpression>
                            <SortExpressionScope>tb_RespAnalysis_group_Source</SortExpressionScope>
                            <SortTarget>tb_RespAnalysis</SortTarget>
                          </UserSort>
                          <Value />
                        </Textbox>
                      </ReportItems>
                    </TableCell>
                    <TableCell>
                      <ReportItems>
                        <Textbox Name="textbox153">
                          <rd:DefaultName>textbox153</rd:DefaultName>
                          <Style>
                            <BackgroundColor>WhiteSmoke</BackgroundColor>
                            <BorderColor>
                              <Default>DimGray</Default>
                            </BorderColor>
                            <BorderStyle>
                              <Bottom>Solid</Bottom>
                            </BorderStyle>
                            <FontFamily>Tahoma</FontFamily>
                            <FontSize>7pt</FontSize>
                            <TextAlign>Right</TextAlign>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                          <ZIndex>163</ZIndex>
                          <CanGrow>true</CanGrow>
                          <UserSort>
                            <SortExpression>=Fields!GPMAIL.Value</SortExpression>
                            <SortExpressionScope>tb_RespAnalysis_group_Source</SortExpressionScope>
                            <SortTarget>tb_RespAnalysis</SortTarget>
                          </UserSort>
                          <Value />
                        </Textbox>
                      </ReportItems>
                    </TableCell>
                    <TableCell>
                      <ReportItems>
                        <Textbox Name="textbox154">
                          <rd:DefaultName>textbox154</rd:DefaultName>
                          <Style>
                            <BackgroundColor>WhiteSmoke</BackgroundColor>
                            <BorderColor>
                              <Default>DimGray</Default>
                            </BorderColor>
                            <BorderStyle>
                              <Bottom>Solid</Bottom>
                            </BorderStyle>
                            <FontFamily>Tahoma</FontFamily>
                            <FontSize>7pt</FontSize>
                            <TextAlign>Right</TextAlign>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                          <ZIndex>162</ZIndex>
                          <CanGrow>true</CanGrow>
                          <UserSort>
                            <SortExpression>=Fields!CPMAIL.Value</SortExpression>
                            <SortExpressionScope>tb_RespAnalysis_group_Source</SortExpressionScope>
                            <SortTarget>tb_RespAnalysis</SortTarget>
                          </UserSort>
                          <Value />
                        </Textbox>
                      </ReportItems>
                    </TableCell>
                    <TableCell>
                      <ReportItems>
                        <Textbox Name="textbox155">
                          <rd:DefaultName>textbox155</rd:DefaultName>
                          <Style>
                            <BackgroundColor>WhiteSmoke</BackgroundColor>
                            <BorderColor>
                              <Default>DimGray</Default>
                            </BorderColor>
                            <BorderStyle>
                              <Bottom>Solid</Bottom>
                            </BorderStyle>
                            <FontFamily>Tahoma</FontFamily>
                            <FontSize>7pt</FontSize>
                            <TextAlign>Right</TextAlign>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                          <ZIndex>161</ZIndex>
                          <CanGrow>true</CanGrow>
                          <UserSort>
                            <SortExpression>=Fields!NPMAIL.Value</SortExpression>
                            <SortExpressionScope>tb_RespAnalysis_group_Source</SortExpressionScope>
                            <SortTarget>tb_RespAnalysis</SortTarget>
                          </UserSort>
                          <Value />
                        </Textbox>
                      </ReportItems>
                    </TableCell>
                    <TableCell>
                      <ReportItems>
                        <Textbox Name="textbox156">
                          <rd:DefaultName>textbox156</rd:DefaultName>
                          <Style>
                            <BackgroundColor>LightSalmon</BackgroundColor>
                            <BorderColor>
                              <Default>DimGray</Default>
                            </BorderColor>
                            <BorderStyle>
                              <Bottom>Solid</Bottom>
                            </BorderStyle>
                            <FontFamily>Tahoma</FontFamily>
                            <FontSize>7pt</FontSize>
                            <FontWeight>700</FontWeight>
                            <TextAlign>Center</TextAlign>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                          <ZIndex>160</ZIndex>
                          <CanGrow>true</CanGrow>
                          <UserSort>
                            <SortExpression>=Fields!GPDONTN.Value</SortExpression>
                            <SortExpressionScope>tb_RespAnalysis_group_Source</SortExpressionScope>
                            <SortTarget>tb_RespAnalysis</SortTarget>
                          </UserSort>
                          <Value />
                        </Textbox>
                      </ReportItems>
                    </TableCell>
                    <TableCell>
                      <ReportItems>
                        <Textbox Name="textbox157">
                          <rd:DefaultName>textbox157</rd:DefaultName>
                          <Style>
                            <BackgroundColor>LightSalmon</BackgroundColor>
                            <BorderColor>
                              <Default>DimGray</Default>
                            </BorderColor>
                            <BorderStyle>
                              <Right>Solid</Right>
                              <Bottom>Solid</Bottom>
                            </BorderStyle>
                            <FontFamily>Tahoma</FontFamily>
                            <FontSize>7pt</FontSize>
                            <TextAlign>Right</TextAlign>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                          <ZIndex>159</ZIndex>
                          <CanGrow>true</CanGrow>
                          <UserSort>
                            <SortExpression>=Fields!ROI.Value</SortExpression>
                            <SortExpressionScope>tb_RespAnalysis_group_Source</SortExpressionScope>
                            <SortTarget>tb_RespAnalysis</SortTarget>
                          </UserSort>
                          <Value />
                        </Textbox>
                      </ReportItems>
                    </TableCell>
                  </TableCells>
                  <Height>0.17in</Height>
                </TableRow>
                <TableRow>
                  <Visibility>
                    <Hidden>=(Parameters!show_details.Value=TRUE)</Hidden>
                  </Visibility>
                  <TableCells>
                    <TableCell>
                      <ReportItems>
                        <Textbox Name="textbox158">
                          <rd:DefaultName>textbox158</rd:DefaultName>
                          <Style>
                            <BackgroundColor>SkyBlue</BackgroundColor>
                            <BorderColor>
                              <Default>DimGray</Default>
                            </BorderColor>
                            <BorderStyle>
                              <Top>None</Top>
                              <Bottom>Solid</Bottom>
                            </BorderStyle>
                            <FontFamily>Tahoma</FontFamily>
                            <FontSize>7pt</FontSize>
                            <FontWeight>700</FontWeight>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                          <ZIndex>158</ZIndex>
                          <CanGrow>true</CanGrow>
                          <UserSort>
                            <SortExpression>=Fields!PKGECODE.Value</SortExpression>
                            <SortExpressionScope>tb_RespAnalysis_group_Package</SortExpressionScope>
                            <SortTarget>tb_RespAnalysis</SortTarget>
                          </UserSort>
                          <Value />
                        </Textbox>
                      </ReportItems>
                    </TableCell>
                    <TableCell>
                      <ReportItems>
                        <Textbox Name="textbox159">
                          <rd:DefaultName>textbox159</rd:DefaultName>
                          <Style>
                            <BackgroundColor>SkyBlue</BackgroundColor>
                            <BorderColor>
                              <Default>DimGray</Default>
                            </BorderColor>
                            <BorderStyle>
                              <Top>None</Top>
                              <Bottom>Solid</Bottom>
                            </BorderStyle>
                            <FontFamily>Tahoma</FontFamily>
                            <FontSize>7pt</FontSize>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                          <ZIndex>157</ZIndex>
                          <CanGrow>true</CanGrow>
                          <Value />
                        </Textbox>
                      </ReportItems>
                    </TableCell>
                    <TableCell>
                      <ReportItems>
                        <Textbox Name="textbox160">
                          <rd:DefaultName>textbox160</rd:DefaultName>
                          <Style>
                            <BackgroundColor>SkyBlue</BackgroundColor>
                            <BorderColor>
                              <Default>DimGray</Default>
                            </BorderColor>
                            <BorderStyle>
                              <Top>None</Top>
                              <Bottom>Solid</Bottom>
                            </BorderStyle>
                            <FontFamily>Tahoma</FontFamily>
                            <FontSize>7pt</FontSize>
                            <FontWeight>700</FontWeight>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                          <ZIndex>156</ZIndex>
                          <CanGrow>true</CanGrow>
                          <UserSort>
                            <SortExpression>=Fields!PKGEDESC.Value</SortExpression>
                            <SortExpressionScope>tb_RespAnalysis_group_Package</SortExpressionScope>
                            <SortTarget>tb_RespAnalysis</SortTarget>
                          </UserSort>
                          <Value />
                        </Textbox>
                      </ReportItems>
                    </TableCell>
                    <TableCell>
                      <ReportItems>
                        <Textbox Name="textbox161">
                          <rd:DefaultName>textbox161</rd:DefaultName>
                          <Style>
                            <BackgroundColor>WhiteSmoke</BackgroundColor>
                            <BorderColor>
                              <Default>DimGray</Default>
                            </BorderColor>
                            <BorderStyle>
                              <Bottom>Solid</Bottom>
                            </BorderStyle>
                            <FontFamily>Tahoma</FontFamily>
                            <FontSize>7pt</FontSize>
                            <TextAlign>Center</TextAlign>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                          <ZIndex>155</ZIndex>
                          <CanGrow>true</CanGrow>
                          <UserSort>
                            <SortExpression>=Min(Fields!MAILDATE.Value, "tb_RespAnalysis_group_Package")</SortExpression>
                            <SortExpressionScope>tb_RespAnalysis_group_Package</SortExpressionScope>
                            <SortTarget>tb_RespAnalysis</SortTarget>
                          </UserSort>
                          <Value />
                        </Textbox>
                      </ReportItems>
                    </TableCell>
                    <TableCell>
                      <ReportItems>
                        <Textbox Name="textbox162">
                          <rd:DefaultName>textbox162</rd:DefaultName>
                          <Style>
                            <BackgroundColor>WhiteSmoke</BackgroundColor>
                            <BorderColor>
                              <Default>DimGray</Default>
                            </BorderColor>
                            <BorderStyle>
                              <Bottom>Solid</Bottom>
                            </BorderStyle>
                            <FontFamily>Tahoma</FontFamily>
                            <FontSize>7pt</FontSize>
                            <TextAlign>Center</TextAlign>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                          <ZIndex>154</ZIndex>
                          <CanGrow>true</CanGrow>
                          <UserSort>
                            <SortExpression>=Sum(Fields!MAILQTY.Value, "tb_RespAnalysis_group_Package")</SortExpression>
                            <SortExpressionScope>tb_RespAnalysis_group_Package</SortExpressionScope>
                            <SortTarget>tb_RespAnalysis</SortTarget>
                          </UserSort>
                          <Value />
                        </Textbox>
                      </ReportItems>
                    </TableCell>
                    <TableCell>
                      <ReportItems>
                        <Textbox Name="textbox163">
                          <rd:DefaultName>textbox163</rd:DefaultName>
                          <Style>
                            <BackgroundColor>SkyBlue</BackgroundColor>
                            <BorderColor>
                              <Default>DimGray</Default>
                            </BorderColor>
                            <BorderStyle>
                              <Bottom>Solid</Bottom>
                            </BorderStyle>
                            <FontFamily>Tahoma</FontFamily>
                            <FontSize>7pt</FontSize>
                            <TextAlign>Center</TextAlign>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                          <ZIndex>153</ZIndex>
                          <CanGrow>true</CanGrow>
                          <UserSort>
                            <SortExpression>=MIN(Fields!FIRSTCAGE.Value, "tb_RespAnalysis_group_Package")</SortExpression>
                            <SortExpressionScope>tb_RespAnalysis_group_Package</SortExpressionScope>
                            <SortTarget>tb_RespAnalysis</SortTarget>
                          </UserSort>
                          <Value />
                        </Textbox>
                      </ReportItems>
                    </TableCell>
                    <TableCell>
                      <ReportItems>
                        <Textbox Name="textbox164">
                          <rd:DefaultName>textbox164</rd:DefaultName>
                          <Style>
                            <BackgroundColor>SkyBlue</BackgroundColor>
                            <BorderColor>
                              <Default>DimGray</Default>
                            </BorderColor>
                            <BorderStyle>
                              <Bottom>Solid</Bottom>
                            </BorderStyle>
                            <FontFamily>Tahoma</FontFamily>
                            <FontSize>7pt</FontSize>
                            <TextAlign>Center</TextAlign>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                          <ZIndex>152</ZIndex>
                          <CanGrow>true</CanGrow>
                          <UserSort>
                            <SortExpression>=MAX(Fields!LASTCAGE.Value, "tb_RespAnalysis_group_Package")</SortExpression>
                            <SortExpressionScope>tb_RespAnalysis_group_Package</SortExpressionScope>
                            <SortTarget>RespAnalysis</SortTarget>
                          </UserSort>
                          <Value />
                        </Textbox>
                      </ReportItems>
                    </TableCell>
                    <TableCell>
                      <ReportItems>
                        <Textbox Name="textbox165">
                          <rd:DefaultName>textbox165</rd:DefaultName>
                          <Style>
                            <BackgroundColor>WhiteSmoke</BackgroundColor>
                            <BorderColor>
                              <Default>DimGray</Default>
                            </BorderColor>
                            <BorderStyle>
                              <Bottom>Solid</Bottom>
                            </BorderStyle>
                            <FontFamily>Tahoma</FontFamily>
                            <FontSize>7pt</FontSize>
                            <TextAlign>Right</TextAlign>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                          <ZIndex>151</ZIndex>
                          <CanGrow>true</CanGrow>
                          <UserSort>
                            <SortExpression>=Sum(Fields!DONATIONS.Value, "tb_RespAnalysis_group_Package")</SortExpression>
                            <SortExpressionScope>tb_RespAnalysis_group_Package</SortExpressionScope>
                            <SortTarget>tb_RespAnalysis</SortTarget>
                          </UserSort>
                          <Value />
                        </Textbox>
                      </ReportItems>
                    </TableCell>
                    <TableCell>
                      <ReportItems>
                        <Textbox Name="textbox166">
                          <rd:DefaultName>textbox166</rd:DefaultName>
                          <Style>
                            <BackgroundColor>WhiteSmoke</BackgroundColor>
                            <BorderColor>
                              <Default>DimGray</Default>
                            </BorderColor>
                            <BorderStyle>
                              <Bottom>Solid</Bottom>
                            </BorderStyle>
                            <FontFamily>Tahoma</FontFamily>
                            <FontSize>7pt</FontSize>
                            <TextAlign>Right</TextAlign>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                          <ZIndex>150</ZIndex>
                          <CanGrow>true</CanGrow>
                          <UserSort>
                            <SortExpression>=IIF(Sum(Fields!MAILQTY.Value, "tb_RespAnalysis_group_Package") = 0, 
	0,
	Sum(Fields!DONATIONS.Value, "tb_RespAnalysis_group_Package")*100.0/Sum(Fields!MAILQTY.Value, "tb_RespAnalysis_group_Package"))</SortExpression>
                            <SortExpressionScope>tb_RespAnalysis_group_Package</SortExpressionScope>
                            <SortTarget>tb_RespAnalysis</SortTarget>
                          </UserSort>
                          <Value />
                        </Textbox>
                      </ReportItems>
                    </TableCell>
                    <TableCell>
                      <ReportItems>
                        <Textbox Name="textbox167">
                          <rd:DefaultName>textbox167</rd:DefaultName>
                          <Style>
                            <BackgroundColor>SkyBlue</BackgroundColor>
                            <BorderColor>
                              <Default>DimGray</Default>
                            </BorderColor>
                            <BorderStyle>
                              <Bottom>Solid</Bottom>
                            </BorderStyle>
                            <FontFamily>Tahoma</FontFamily>
                            <FontSize>7pt</FontSize>
                            <TextAlign>Right</TextAlign>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                          <ZIndex>149</ZIndex>
                          <CanGrow>true</CanGrow>
                          <UserSort>
                            <SortExpression>=Sum(Fields!GROSS.Value, "tb_RespAnalysis_group_Package")</SortExpression>
                            <SortExpressionScope>tb_RespAnalysis_group_Package</SortExpressionScope>
                            <SortTarget>tb_RespAnalysis</SortTarget>
                          </UserSort>
                          <Value />
                        </Textbox>
                      </ReportItems>
                    </TableCell>
                    <TableCell>
                      <ReportItems>
                        <Textbox Name="textbox168">
                          <rd:DefaultName>textbox168</rd:DefaultName>
                          <Style>
                            <BackgroundColor>SkyBlue</BackgroundColor>
                            <BorderColor>
                              <Default>DimGray</Default>
                            </BorderColor>
                            <BorderStyle>
                              <Bottom>Solid</Bottom>
                            </BorderStyle>
                            <FontFamily>Tahoma</FontFamily>
                            <FontSize>7pt</FontSize>
                            <TextAlign>Right</TextAlign>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                          <ZIndex>148</ZIndex>
                          <CanGrow>true</CanGrow>
                          <UserSort>
                            <SortExpression>=Sum(Fields!COST.Value, "tb_RespAnalysis_group_Package")</SortExpression>
                            <SortExpressionScope>tb_RespAnalysis_group_Package</SortExpressionScope>
                            <SortTarget>tb_RespAnalysis</SortTarget>
                          </UserSort>
                          <Value />
                        </Textbox>
                      </ReportItems>
                    </TableCell>
                    <TableCell>
                      <ReportItems>
                        <Textbox Name="textbox169">
                          <rd:DefaultName>textbox169</rd:DefaultName>
                          <Style>
                            <BackgroundColor>SkyBlue</BackgroundColor>
                            <BorderColor>
                              <Default>DimGray</Default>
                            </BorderColor>
                            <BorderStyle>
                              <Bottom>Solid</Bottom>
                            </BorderStyle>
                            <FontFamily>Tahoma</FontFamily>
                            <FontSize>7pt</FontSize>
                            <TextAlign>Right</TextAlign>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                          <ZIndex>147</ZIndex>
                          <CanGrow>true</CanGrow>
                          <UserSort>
                            <SortExpression>=Sum(Fields!NET.Value, "tb_RespAnalysis_group_Package")</SortExpression>
                            <SortExpressionScope>tb_RespAnalysis_group_Package</SortExpressionScope>
                            <SortTarget>tb_RespAnalysis</SortTarget>
                          </UserSort>
                          <Value />
                        </Textbox>
                      </ReportItems>
                    </TableCell>
                    <TableCell>
                      <ReportItems>
                        <Textbox Name="textbox170">
                          <rd:DefaultName>textbox170</rd:DefaultName>
                          <Style>
                            <BackgroundColor>WhiteSmoke</BackgroundColor>
                            <BorderColor>
                              <Default>DimGray</Default>
                            </BorderColor>
                            <BorderStyle>
                              <Bottom>Solid</Bottom>
                            </BorderStyle>
                            <FontFamily>Tahoma</FontFamily>
                            <FontSize>7pt</FontSize>
                            <TextAlign>Right</TextAlign>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                          <ZIndex>146</ZIndex>
                          <CanGrow>true</CanGrow>
                          <UserSort>
                            <SortExpression>=IIF(Sum(Fields!MAILQTY.Value, "tb_RespAnalysis_group_Package") = 0, 
	0,
	Sum(Fields!GROSS.Value, "tb_RespAnalysis_group_Package")/Sum(Fields!MAILQTY.Value, "tb_RespAnalysis_group_Package"))</SortExpression>
                            <SortExpressionScope>tb_RespAnalysis_group_Package</SortExpressionScope>
                            <SortTarget>tb_RespAnalysis</SortTarget>
                          </UserSort>
                          <Value />
                        </Textbox>
                      </ReportItems>
                    </TableCell>
                    <TableCell>
                      <ReportItems>
                        <Textbox Name="textbox171">
                          <rd:DefaultName>textbox171</rd:DefaultName>
                          <Style>
                            <BackgroundColor>WhiteSmoke</BackgroundColor>
                            <BorderColor>
                              <Default>DimGray</Default>
                            </BorderColor>
                            <BorderStyle>
                              <Bottom>Solid</Bottom>
                            </BorderStyle>
                            <FontFamily>Tahoma</FontFamily>
                            <FontSize>7pt</FontSize>
                            <TextAlign>Right</TextAlign>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                          <ZIndex>145</ZIndex>
                          <CanGrow>true</CanGrow>
                          <UserSort>
                            <SortExpression>=IIF(Sum(Fields!MAILQTY.Value, "tb_RespAnalysis_group_Package") = 0, 
	0,
	Sum(Fields!COST.Value, "tb_RespAnalysis_group_Package")/Sum(Fields!MAILQTY.Value, "tb_RespAnalysis_group_Package"))</SortExpression>
                            <SortExpressionScope>tb_RespAnalysis_group_Package</SortExpressionScope>
                            <SortTarget>tb_RespAnalysis</SortTarget>
                          </UserSort>
                          <Value />
                        </Textbox>
                      </ReportItems>
                    </TableCell>
                    <TableCell>
                      <ReportItems>
                        <Textbox Name="textbox172">
                          <rd:DefaultName>textbox172</rd:DefaultName>
                          <Style>
                            <BackgroundColor>WhiteSmoke</BackgroundColor>
                            <BorderColor>
                              <Default>DimGray</Default>
                            </BorderColor>
                            <BorderStyle>
                              <Bottom>Solid</Bottom>
                            </BorderStyle>
                            <FontFamily>Tahoma</FontFamily>
                            <FontSize>7pt</FontSize>
                            <TextAlign>Right</TextAlign>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                          <ZIndex>144</ZIndex>
                          <CanGrow>true</CanGrow>
                          <UserSort>
                            <SortExpression>=IIF(Sum(Fields!MAILQTY.Value, "tb_RespAnalysis_group_Package") = 0, 
	0,
	Sum(Fields!NET.Value, "tb_RespAnalysis_group_Package")/Sum(Fields!MAILQTY.Value, "tb_RespAnalysis_group_Package"))</SortExpression>
                            <SortExpressionScope>tb_RespAnalysis_group_Package</SortExpressionScope>
                            <SortTarget>tb_RespAnalysis</SortTarget>
                          </UserSort>
                          <Value />
                        </Textbox>
                      </ReportItems>
                    </TableCell>
                    <TableCell>
                      <ReportItems>
                        <Textbox Name="textbox173">
                          <rd:DefaultName>textbox173</rd:DefaultName>
                          <Style>
                            <BackgroundColor>SkyBlue</BackgroundColor>
                            <BorderColor>
                              <Default>DimGray</Default>
                            </BorderColor>
                            <BorderStyle>
                              <Bottom>Solid</Bottom>
                            </BorderStyle>
                            <FontFamily>Tahoma</FontFamily>
                            <FontSize>7pt</FontSize>
                            <FontWeight>700</FontWeight>
                            <TextAlign>Center</TextAlign>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                          <ZIndex>143</ZIndex>
                          <CanGrow>true</CanGrow>
                          <UserSort>
                            <SortExpression>=IIF(Sum(Fields!DONATIONS.Value, "tb_RespAnalysis_group_Package") = 0, 
	0,
	Sum(Fields!GROSS.Value, "tb_RespAnalysis_group_Package")/Sum(Fields!DONATIONS.Value, "tb_RespAnalysis_group_Package"))</SortExpression>
                            <SortExpressionScope>tb_RespAnalysis_group_Package</SortExpressionScope>
                            <SortTarget>tb_RespAnalysis</SortTarget>
                          </UserSort>
                          <Value />
                        </Textbox>
                      </ReportItems>
                    </TableCell>
                    <TableCell>
                      <ReportItems>
                        <Textbox Name="textbox174">
                          <rd:DefaultName>textbox174</rd:DefaultName>
                          <Style>
                            <BackgroundColor>SkyBlue</BackgroundColor>
                            <BorderColor>
                              <Default>DimGray</Default>
                            </BorderColor>
                            <BorderStyle>
                              <Right>Solid</Right>
                              <Bottom>Solid</Bottom>
                            </BorderStyle>
                            <FontFamily>Tahoma</FontFamily>
                            <FontSize>7pt</FontSize>
                            <TextAlign>Right</TextAlign>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                          <ZIndex>142</ZIndex>
                          <CanGrow>true</CanGrow>
                          <UserSort>
                            <SortExpression>=IIF(Sum(Fields!COST.Value, "tb_RespAnalysis_group_Package") = 0, 
	0,
	Sum(Fields!GROSS.Value, "tb_RespAnalysis_group_Package")*1.0/Sum(Fields!COST.Value, "tb_RespAnalysis_group_Package"))</SortExpression>
                            <SortExpressionScope>tb_RespAnalysis_group_Package</SortExpressionScope>
                            <SortTarget>tb_RespAnalysis</SortTarget>
                          </UserSort>
                          <Value />
                        </Textbox>
                      </ReportItems>
                    </TableCell>
                  </TableCells>
                  <Height>0.17in</Height>
                </TableRow>
              </TableRows>
              <RepeatOnNewPage>true</RepeatOnNewPage>
            </Header>
            <TableColumns>
              <TableColumn>
                <Width>0.6875in</Width>
              </TableColumn>
              <TableColumn>
                <Width>0.33in</Width>
              </TableColumn>
              <TableColumn>
                <Width>1.95in</Width>
              </TableColumn>
              <TableColumn>
                <Width>0.5in</Width>
              </TableColumn>
              <TableColumn>
                <Width>0.685in</Width>
              </TableColumn>
              <TableColumn>
                <Width>0.5in</Width>
              </TableColumn>
              <TableColumn>
                <Width>0.5in</Width>
              </TableColumn>
              <TableColumn>
                <Width>0.535in</Width>
              </TableColumn>
              <TableColumn>
                <Width>0.41in</Width>
              </TableColumn>
              <TableColumn>
                <Width>0.745in</Width>
              </TableColumn>
              <TableColumn>
                <Width>0.745in</Width>
              </TableColumn>
              <TableColumn>
                <Width>0.725in</Width>
              </TableColumn>
              <TableColumn>
                <Width>0.4175in</Width>
              </TableColumn>
              <TableColumn>
                <Width>0.4175in</Width>
              </TableColumn>
              <TableColumn>
                <Width>0.4175in</Width>
              </TableColumn>
              <TableColumn>
                <Width>0.4825in</Width>
              </TableColumn>
              <TableColumn>
                <Width>0.4175in</Width>
              </TableColumn>
            </TableColumns>
          </Table>
        </ReportItems>
        <Style>
          <Color>SlateGray</Color>
          <FontFamily>Tahoma</FontFamily>
          <FontSize>14pt</FontSize>
        </Style>
        <Grouping Name="list1_PROGTYPE">
          <GroupExpressions>
            <GroupExpression>=Fields!PROGTYPE.Value</GroupExpression>
          </GroupExpressions>
          <PageBreakAtEnd>true</PageBreakAtEnd>
        </Grouping>
      </List>
    </ReportItems>
    <Height>2.65371in</Height>
  </Body>
  <Language>en-US</Language>
  <PageFooter>
    <PrintOnFirstPage>true</PrintOnFirstPage>
    <ReportItems>
      <Textbox Name="textbox79">
        <Width>4in</Width>
        <Style>
          <Color>SteelBlue</Color>
          <FontFamily>Trebuchet MS</FontFamily>
          <FontSize>8pt</FontSize>
          <TextAlign>Left</TextAlign>
          <PaddingLeft>2pt</PaddingLeft>
          <PaddingRight>2pt</PaddingRight>
          <PaddingTop>2pt</PaddingTop>
          <PaddingBottom>2pt</PaddingBottom>
        </Style>
        <ZIndex>1</ZIndex>
        <CanGrow>true</CanGrow>
        <Height>0.1875in</Height>
        <Value>=(CStr(Globals!PageNumber) + " of " +CStr(Globals!TotalPages))</Value>
      </Textbox>
      <Textbox Name="textbox78">
        <Width>3.9375in</Width>
        <Style>
          <Color>SteelBlue</Color>
          <FontFamily>Trebuchet MS</FontFamily>
          <FontSize>8pt</FontSize>
          <TextAlign>Right</TextAlign>
          <PaddingLeft>2pt</PaddingLeft>
          <PaddingRight>2pt</PaddingRight>
          <PaddingTop>2pt</PaddingTop>
          <PaddingBottom>2pt</PaddingBottom>
        </Style>
        <CanGrow>true</CanGrow>
        <Left>6.4375in</Left>
        <Height>0.1875in</Height>
        <Value>=(Globals!ExecutionTime + ".  Prepared by CMDI")</Value>
      </Textbox>
    </ReportItems>
    <Height>0.25in</Height>
    <PrintOnLastPage>true</PrintOnLastPage>
  </PageFooter>
  <TopMargin>0.2in</TopMargin>
  <PageHeight>8.5in</PageHeight>
</Report>