﻿using cmdiapp.n.core._Domain.Models;
using cmdiapp.n.core._Domain.ViewModels;
using cmdiapp.n.core.Areas.crm.Core;
using cmdiapp.n.core.Areas.query.Domain.Models;
using cmdiapp.n.core.Domain.Data;
using cmdiapp.n.core.Domain.Models;
using DocumentFormat.OpenXml.ExtendedProperties;
using System;
using System.Collections.Generic;
using System.Data.Entity;
using System.Linq;
using System.Threading.Tasks;

namespace cmdiapp.n.core._Domain.Services.implementation
{
    public class EmailReportDataService : IEmailReportDataService
    {
        private readonly I_entity _entity;
        private readonly IUserSession _user;
        private readonly IReportInstanceService _reportInstanceService;
        private readonly int _minReportIntervalInHours = 24;
        private readonly int _bufferInSeconds = 30;

        public EmailReportDataService(I_entity entity, IUserSession user, IReportInstanceService reportInstanceService)
        {
            _entity = entity;
            _user = user;
            _reportInstanceService = reportInstanceService;
        }

        public Task<GenericSuccessResponse<EmailReport>> AddQueryEmailReportAsync(
            int savedQueryId,
            DateTime startAt,
            int intervalInHours)
        {
            return AddEmailReportWithCompilationSourceAsync(
                savedQueryId,
                EmailReportCompilationSourceTypeCode.Search,
                startAt,
                intervalInHours);
        }

        public Task<GenericSuccessResponse<EmailReport>> AddReportEmailReportAsync(
            int reportId,
            string reportInstanceName,
            searchParam searchParam,
            DateTime startAt,
            int intervalInHours)
        {
            var addResult = _reportInstanceService.Add(reportId, searchParam, reportInstanceName);

            if (!(addResult?.Success ?? false))
            {
                return Task.FromResult(new GenericSuccessResponse<EmailReport>
                {
                    Success = false,
                    Message = addResult?.Message ?? $"Failed to add ReportInstance for report {reportId}."
                });
            }

            return AddEmailReportWithCompilationSourceAsync(
                addResult.Results.Id,
                EmailReportCompilationSourceTypeCode.Report,
                startAt,
                intervalInHours);
        }

        public Task<GenericSuccessResponse<EmailReport>> AddQueryExportEmailReportAsync(int queryLogId)
        {
            return AddEmailReportWithCompilationSourceAsync(
                queryLogId,
                EmailReportCompilationSourceTypeCode.QueryExport,
                DateTime.Now,
                _minReportIntervalInHours,
                runOnce: true);
        }

        public async Task<GenericSuccessResponse<int>> DeleteEmailReportAsync(int id)
        {
            try
            {
                var report = await GetEmailReportAsync(id);
                if (report == null)
                {
                    return new GenericSuccessResponse<int>
                    {
                        Success = false,
                        Message = $"No EmailReport record found with id {id}."
                    };
                }

                var typeCode = await GetCompilationSourceTypeCodeAsync(report.CompilationSourceTypeId);
                if (typeCode == EmailReportCompilationSourceTypeCode.Report)
                {
                    await _reportInstanceService.DeleteAsync(report.CompilationSourceId);
                }

                _entity.Delete(report);
                _entity.CommitChanges();

                return new GenericSuccessResponse<int>
                {
                    Success = true,
                    Results = report.Id
                };
            }
            catch (Exception ex)
            {
                return new GenericSuccessResponse<int>
                {
                    Success = false,
                    Message = $"Encountered exception when attempting to " +
                        $"delete EmailReport {id}: {ex.GetType().Name}: {ex.Message}"
                };
            }
        }

        public Task<EmailReport> GetEmailReportAsync(int id)
        {
            return _entity.All<EmailReport>().SingleOrDefaultAsync(e => e.Id == id);
        }

        public Task<List<EmailReport>> GetReportsThatNeedToRunAsync()
        {
            // Treat "now" as some seconds later than now to prevent
            // "drift" over time.  For example, if lastStartedAt at is
            // 12:00:01 and the check is at 12:00, it won't be triggered until
            // the next check at 12:05 (or whatever the polling interval is).
            // And this won't actually update lastStartedAt until 12:05:01, so the next run won't
            // be until 12:10, etc.
            var now = DateTime.Now.Add(TimeSpan.FromSeconds(_bufferInSeconds));
            var candidates = _entity.All<EmailReport>()
                .Where(report => report.StartAt <= now && !(report.RunOnce == true && report.LastStartedAt != null))
                .ToList();
            var due = candidates
                .Where(report =>
                    report.LastStartedAt == null
                    || (
                        !(report.RunOnce ?? false)
                        && now >= report.LastStartedAt.Value
                                        .Date
                                        .Add(report.StartAt.TimeOfDay)
                                        .AddHours(report.IntervalInHours)
                    )
            )
                .ToList();
            return Task.Run(() => due);
            //return _entity.All<EmailReport>()
            //    .Where(report => report.StartAt <= now
            //            && (report.LastStartedAt == null                        
            //                || (!(report.RunOnce ?? false)
            //                    && now >= TestableDbFunctions.AddHours(
            //                        //use StartAt time part and LastStartedAt date part
            //                        report.LastStartedAt.Value.Date.Add(report.StartAt.TimeOfDay),
            //                        report.IntervalInHours))))
            //    .ToListAsync();
        }

        public async Task<GenericSuccessResponse<EmailReport>> UpdateEmailReportAsync(EmailReport emailReport)
        {
            try
            {
                if ((emailReport?.IntervalInHours ?? 0) < _minReportIntervalInHours)
                {
                    return new GenericSuccessResponse<EmailReport>
                    {
                        Success = false,
                        Message = $"EmailReport interval cannot be less than {_minReportIntervalInHours} hours."
                    };
                }

                var existingReport = await GetEmailReportAsync(emailReport?.Id ?? 0);
                if (existingReport == null)
                {
                    return new GenericSuccessResponse<EmailReport>
                    {
                        Success = false,
                        Message = $"There is no EmailReport record with id {emailReport?.Id}"
                    };
                }
                
                existingReport.StartAt = emailReport.StartAt;
                existingReport.IntervalInHours = emailReport.IntervalInHours;
                existingReport.LastStartedAt = emailReport.LastStartedAt;
                existingReport.LastCompletedAt = emailReport.LastCompletedAt;
                existingReport.LastModifiedAt = DateTime.Now;
                existingReport.LastRunSucceeded = emailReport.LastRunSucceeded;
                existingReport.LastRunErrorMessage = emailReport.LastRunErrorMessage;

                _entity.Update(existingReport);
                _entity.CommitChanges();

                return new GenericSuccessResponse<EmailReport>
                {
                    Success = true,
                    Results = existingReport
                };

            }
            catch (Exception ex)
            {
                return new GenericSuccessResponse<EmailReport>
                {
                    Success = false,
                    Message = $"Encountered exception when updating EmailReport" +
                        $" {emailReport?.Id}: {ex.GetType().Name}: {ex.Message}"
                };
            }
        }

        public async Task<string> GetCompilationSourceTypeCodeAsync(int compilationSourceTypeId)
        {
            var sourceType = await _entity.All<EmailReportCompilationSourceType>()
                .SingleOrDefaultAsync(type => type.Id == compilationSourceTypeId);
            return sourceType?.Code;
        }

        private async Task<ValidationResult> ValidateNewEmailReportAsync(EmailReport emailReport)
        {
            if (emailReport == null)
            {
                return new ValidationResult { IsValid = false, ErrorMessage = "EmailReport cannot be null." };
            }

            if (emailReport.IntervalInHours < _minReportIntervalInHours)
            {
                return new ValidationResult
                {
                    IsValid = false,
                    ErrorMessage = $"EmailReport interval cannot be less than {_minReportIntervalInHours} hours."
                };
            }

            // Should not be able to add report with same compilation source
            var reportWithSameCompilationSource = await _entity.All<EmailReport>()
                .Where(report => report.CompilationSourceId == emailReport.CompilationSourceId
                    && report.CompilationSourceTypeId == emailReport.CompilationSourceTypeId)
                .FirstOrDefaultAsync();
            if (reportWithSameCompilationSource != null)
            {
                return new ValidationResult
                {
                    IsValid = false,
                    ErrorMessage = $"An EmailReport (id: {reportWithSameCompilationSource.Id}) with the same " +
                        $"compilation source already exists."
                };
            }

            return new ValidationResult { IsValid = true };
        }

        private Task<EmailReportCompilationSourceType> GetCompilationSourceTypeByCode(string code)
        {
            return _entity.All<EmailReportCompilationSourceType>()
                .SingleOrDefaultAsync(t => t.Code == code);
        }

        private async Task<GenericSuccessResponse<EmailReport>> AddEmailReportWithCompilationSourceAsync(
            int compilationSourceId,
            string compilationSourceTypeCode,
            DateTime startAt,
            int intervalInHours,
            bool runOnce = false)
        {
            var compilationSourceType = await GetCompilationSourceTypeByCode(compilationSourceTypeCode);
            if (compilationSourceType == null)
            {
                return new GenericSuccessResponse<EmailReport>()
                {
                    Success = false,
                    Message = $"There is no CompilationSourceType with code {compilationSourceTypeCode}"

                };
            }

            var emailReport = new EmailReport
            {
                CompilationSourceId = compilationSourceId,
                CompilationSourceTypeId = compilationSourceType.Id,
                StartAt = startAt,
                IntervalInHours = intervalInHours,
                RunOnce = runOnce
            };

            return await AddEmailReportAsync(emailReport);
        }

        private async Task<GenericSuccessResponse<EmailReport>> AddEmailReportAsync(EmailReport emailReport)
        {
            try
            {
                var validationResult = await ValidateNewEmailReportAsync(emailReport);
                if (!validationResult.IsValid)
                {
                    return new GenericSuccessResponse<EmailReport>
                    {
                        Success = false,
                        Message = validationResult.ErrorMessage
                    };
                }

                // Ensure that we know that this report has never been run.
                emailReport.LastStartedAt = null;
                emailReport.LastCompletedAt = null;

                emailReport.LastModifiedAt = DateTime.Now;
                emailReport.CreatedAt = DateTime.Now;
                emailReport.UserId = _user.GetCurrentUserId();
                emailReport.ProjectId = _user.GetCurrentProjectId();

                _entity.Add(emailReport);
                _entity.CommitChanges();

                return new GenericSuccessResponse<EmailReport>
                {
                    Success = true,
                    Results = emailReport
                };
            }
            catch (Exception ex)
            {
                return new GenericSuccessResponse<EmailReport>
                {
                    Success = false,
                    Message = $"Encountered exception when adding EmailReport: {ex.GetType().Name}: {ex.Message}"
                };
            }
        }

        public async Task<string> GetProjectSubdomainAsync(int projectId)
        {
            var project = await _entity.All<project>().SingleOrDefaultAsync(p => p.projectId == projectId);
            return project?.code?.ToLower();
        }

        public Task<List<EmailReportDisplay>> GetAllEmailReportsForViewAsync()
        {
            string userId = _user.GetCurrentUserId();
            int projectId = _user.GetCurrentProjectId();

            return (from emailReport in _entity.All<EmailReport>()
                    join sourceType in _entity.All<EmailReportCompilationSourceType>()
                        on emailReport.CompilationSourceTypeId equals sourceType.Id
                    // left outer join saved saved queries
                    join query in _entity.All<SavedQuery>()
                        on emailReport.CompilationSourceId equals query.Id into queries
                    from query in queries.DefaultIfEmpty()
                    // left outer join reports
                    join report in _entity.All<ReportInstance>()
                        on emailReport.CompilationSourceId equals report.Id into reports
                    from report in reports.DefaultIfEmpty()
                    where emailReport.UserId == userId
                        && emailReport.ProjectId == projectId
                        && sourceType.Code != EmailReportCompilationSourceTypeCode.QueryExport
                    select new EmailReportDisplay
                    {
                        Id = emailReport.Id,
                        CompilationSourceTypeCode = sourceType.Code,
                        CompilationSourceId = emailReport.CompilationSourceId,
                        StartAt = emailReport.StartAt,
                        IntervalInHours = emailReport.IntervalInHours,
                        CreatedAt = emailReport.CreatedAt,
                        LastModifiedAt = emailReport.LastModifiedAt,
                        LastStartedAt = emailReport.LastStartedAt,
                        LastCompletedAt = emailReport.LastCompletedAt,
                        LastRunSucceeded = emailReport.LastRunSucceeded,
                        LastRunErrorMessage = emailReport.LastRunErrorMessage,
                        Name = sourceType.Code == "REPORT"
                            ? (report == null ? "" : report.Name)
                            : (query == null ? "" : query.Name)
                    }).ToListAsync();
        }

        public Task<string> GetEmailReportNameAsync(int id)
        {
            return (from emailReport in _entity.All<EmailReport>()
                    join sourceType in _entity.All<EmailReportCompilationSourceType>()
                        on emailReport.CompilationSourceTypeId equals sourceType.Id
                    // left outer join saved saved queries
                    join query in _entity.All<SavedQuery>()
                        on emailReport.CompilationSourceId equals query.Id into queries
                    from query in queries.DefaultIfEmpty()
                        // left outer join reports
                    join report in _entity.All<ReportInstance>()
                        on emailReport.CompilationSourceId equals report.Id into reports
                    from report in reports.DefaultIfEmpty()
                    where emailReport.Id == id
                    select sourceType.Code == "REPORT"
                            ? (report == null ? "" : report.Name)
                            : (query == null ? "" : query.Name)
                    ).FirstOrDefaultAsync();
        }

        private class ValidationResult
        {
            public bool IsValid { get; set; }
            public string ErrorMessage { get; set; }
        }
    }
}