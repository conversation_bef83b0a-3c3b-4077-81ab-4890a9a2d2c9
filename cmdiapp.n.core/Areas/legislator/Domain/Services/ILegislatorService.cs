﻿using System.Collections.Generic;

using cmdiapp.n.core.Areas.legislator.Domain.Models;
using cmdiapp.n.core.Areas.crm.Domain.Models;
using cmdiapp.n.core.Domain.ViewModels;
using cmdiapp.n.core.Areas.crm.ViewModels;

namespace cmdiapp.n.core.Areas.legislator.Domain.Services
{
    public interface ILegislatorService
    {
        Legislator GetLegislator(string legislatorId);

        LegislatorSocialMediaAccount GetSocialMedia(string legislatorId);

        CommitteeMembership[] GetCommitteeMemberships(string legislatorId);

        LegislatorPhoto GetPhoto(string legislatorId);

        LegislatorInfo GetCrmLegislatorInfo(string legislatorId);

        genericResponse SaveCrmLegislatorInfo(LegislatorInfo info);

        bool MemberTrackingIsEnabled();

        List<LegislatorDirectContributionTotal> GetLegislatorDirectContributionTotals(string legislatorId);

        List<Txn> GetTxnDetails(
            int entityId, int electionCodeId, string electionYear);

        List<PersonalContribLegislatorDisplay> GetPersonalContribs(int entityId, int year);

        List<LegislatorContributionTotal> GetLegislatorPersonalContributionTotals(string legislatorId);

        List<LegislatorAutocompleteDisplay> GetLegislatorsWithContactChanges(string searchText, int take);
    }
}
