﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Net;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Web.Http;
using System.Web.Script.Serialization;
using System.Drawing;
using System.IO;
using System.Data.SqlClient;
using System.Collections.ObjectModel;
using System.Xml.Linq;

using Ninject;
using Ninject.Web.Mvc;

using AutoMapper;
using cmdiapp.n.core.Core;
using cmdiapp.n.core.Core.App_Start;
using cmdiapp.n.core.Domain.ViewModels;
using cmdiapp.n.core.Areas.crm.Core;
using cmdiapp.n.core.Areas.crm.Domain.Models;
using cmdiapp.n.core.Areas.crm.Domain.ViewModels;
using cmdiapp.n.core.Areas.crm.Domain.Data;
using cmdiapp.n.core.Areas.crm.Domain.Repositories;
using cmdiapp.n.core.Areas.crm.Domain.Services;
using cmdiapp.n.core.Areas.dataService.Domain.Data;
using cmdiapp.n.core.Areas.dataService.Domain.Models;
using cmdiapp.n.core.Areas.dataService.Domain.Services;
using cmdiapp.n.core.Presentation.Controllers;
using cmdiapp.n.core.Areas.query.Domain.Models;
using cmdiapp.n.core.Domain.Models;

using System.Data;
using cmdiapp.n.core.Library;
using cmdiapp.n.core.Domain.Data;
using importRow = cmdiapp.n.core.Areas.crm.Presentation.Controllers.ImportController;
using System.Globalization;
using System.Text;

namespace cmdiapp.n.core.Areas.crm.Presentation.api
{
    [Authorize]
    public class ImportController : ApiController
    {

        #region [ Declaration ]

        private I_entity_crm _entity_crm;
        private I_entity _entity;
        private userSession _userSession;
        private readonly IwebIMPPREFService _webIMPPREFService;
        private readonly IimportService _importService;
        private readonly IReportDataService _reportService;

        #endregion

        public ImportController()
        {
            _entity_crm = I_entityManager_crm.getEntity();
            _entity = I_entityManager.getEntity();
            _userSession = session.userSession;
            _webIMPPREFService = NinjectMVC.kernel.Get<IwebIMPPREFService>();
            _importService = NinjectMVC.kernel.Get<IimportService>();
            _reportService = NinjectMVC.kernel.Get<IReportDataService>();
        }

        #region[[sql select fields]]
        //no business address for now
        //private const string sql_select_fields = @"SELECT ISNULL(RECNO,0) AS RECNO,STATUS ,
        //            ISNULL(ID,0) as ID,RECTYPE, PEOTYPE,PREFIX,FNAME,MNAME,LNAME,SUFFIX,EMPLOYER,
        //            OCCUPATION,TITLE,ADDRTYPE,STREET,ADDR1,ADDR2,CITY,STATE,ZIP,PLUS4,
        //            BSTREET,BADDR1,BADDR2,BCITY,BSTATE,BZIP,BPLUS4,
        //            HMPHONE,BSPHONE,FAX,CELLPHONE,EMAIL,SecondaryEMAIL,
        //            URL,SPOUSENAME,PRIMEMAIL,INFSALUT,SALUTATION,MAILSALUTATION,MAILNAME,
        //            ASSISTANT,ASSTBSPHONE, ASSTEMAIL,DOB, FECCMTEID,INDUSTRY,
        //            CPREFIX,CFNAME,CMNAME,CLNAME,CSUFFIX,CTITLE,CORGANIZATION,cSTREET,cADDR1,cADDR2,cCITY,cSTATE,cZIP,cPLUS4,cEMAIL,cHMPHONE,cBSPHONE,
        //            cFAX,cCELLPHONE,cINFSALUT,CLUBCODE,CLUBSTATUS,CANDIDATE,SOLICITOR,RNEWDTE,

        //            FLAG1,FLAG2,FLAG3,FLAG4,FLAG5,FLAG6,FLAG7,FLAG8,FLAG9,FLAG10,
        //            KEYWORD1,KEYWORD2,KEYWORD3,KEYWORD4,KEYWORD5,KEYWORD6,KEYWORD7,KEYWORD8,KEYWORD9,KEYWORD10                   

        //            ";
        private const string sql_select_fields = @"SELECT ISNULL(RECNO,0) AS RECNO,STATUS ,
                    ISNULL(ID,0) as ID,RECTYPE, PEOTYPE,PREFIX,FNAME,MNAME,LNAME,SUFFIX,EMPLOYER,
                    OCCUPATION,TITLE,ADDRTYPE,STREET,ADDR1,ADDR2,CITY,STATE,ZIP,PLUS4,
                    HMPHONE,vHMPHONE as HomePhoneVerificationDate,BSPHONE,vBSPHONE as BusinessPhoneVerificationDate,
                    FAX,vFAX as FaxVerifiedVerificationDate,CELLPHONE,vCELLPHONE as CellPhoneVerificationDate,
                    EMAIL,vEMAIL as EmailVerificationDate,URL,vURL as URLVerificationDate,
                    SPOUSENAME,PRIMEMAIL,INFSALUT,SALUTATION,MAILSALUTATION,MAILNAME,
                    ASSISTANT,ASSTBSPHONE, ASSTEMAIL,DOB, FECCMTEID,INDUSTRY,
                    CPREFIX,CFNAME,CMNAME,CLNAME,CSUFFIX,CTITLE,CORGANIZATION,cSTREET,cADDR1,cADDR2,cCITY,cSTATE,cZIP,cPLUS4,cEMAIL,cHMPHONE,cBSPHONE,
                    cFAX,cCELLPHONE,cINFSALUT,CLUBCODE,CLUBSTATUS,CANDIDATE,SOLICITOR,RNEWDTE,
                    CCREFNO,BSTREET,BADDR1,BADDR2,BCITY,BSTATE,BZIP,BPLUS4,
                    FLAG1,FLAG2,FLAG3,FLAG4,FLAG5,FLAG6,FLAG7,FLAG8,FLAG9,FLAG10,
                    KEYWORD1,KEYWORD2,KEYWORD3,KEYWORD4,KEYWORD5,KEYWORD6,KEYWORD7,KEYWORD8,KEYWORD9,KEYWORD10  
                    ,aNAME AS ATTRIBUTENAME,aSTARTDATE AS ATTRIBUTESTARTDATE,aENDDATE AS ATTRIBUTEENDDATE,aACTIVE AS ATTRIBUTEACTIVE
                    ";
        private string sql_select_fields_NP()
        {
            //for non profit
            if (session.currentDomain_project.projectType.appVersion.ToLower() == "nonprofit")
            {
                return sql_select_fields.Replace("FECCMTEID,", "");
            }
            else
                return sql_select_fields;
        }
        private const string sql_reference_select_field = @", REFERENCEID";

        private const string sql_people_select_field = @",pTRACKNO,RECRUITERNO,COMMITMENT";

        private const string sql_money_select_field = @",ISNULL(TXNNO,0) AS TXNNO,BATCHNO,ISNULL(BATCHDTE,'''') AS BATCHDTE,ISNULL(AMT,0) AS AMT,
                        FUNDCODE,SOURCE, MONYTYPE,COMMENT,ACCTNO,mTRACKNO,CKNO,REFERENCEID";

        private const string sql_CONDUIT_select_field = @",CONDUITNO1,CONDUITAMT1,CONDUITNO2,CONDUITAMT2,CONDUITNO3,CONDUITAMT3,CONDUITNO4,CONDUITAMT4,CONDUITNO5,CONDUITAMT5,
                    CONDUITNO6,CONDUITAMT6,CONDUITNO7,CONDUITAMT7,CONDUITNO8,CONDUITAMT8,CONDUITNO9,CONDUITAMT9,CONDUITNO10,CONDUITAMT10,
                    CONDUITNO11,CONDUITAMT11,CONDUITNO12,CONDUITAMT12,CONDUITNO13,CONDUITAMT13,CONDUITNO14,CONDUITAMT14,CONDUITNO15,CONDUITAMT15";

        //no business address for now
        //private const string sql_select_Top200fields = @"SELECT Top 200 ISNULL(RECNO,0) AS RECNO,STATUS ,
        //            ISNULL(ID,0) as ID,RECTYPE, PEOTYPE,PREFIX,FNAME,MNAME,LNAME,SUFFIX,EMPLOYER,
        //            OCCUPATION,TITLE,ADDRTYPE,STREET,ADDR1,ADDR2,CITY,STATE,ZIP,PLUS4,
        //            BSTREET,BADDR1,BADDR2,BCITY,BSTATE,BZIP,BPLUS4,
        //            HMPHONE,BSPHONE,FAX,CELLPHONE,EMAIL,SecondaryEMAIL,
        //            URL,SPOUSENAME,PRIMEMAIL,INFSALUT,SALUTATION,MAILSALUTATION,MAILNAME,
        //            ASSISTANT,ASSTBSPHONE, ASSTEMAIL,DOB, FECCMTEID,INDUSTRY,
        //            CPREFIX,CFNAME,CMNAME,CLNAME,CSUFFIX,CTITLE,CORGANIZATION,cSTREET,cADDR1,cADDR2,cCITY,cSTATE,cZIP,cPLUS4,cEMAIL,cHMPHONE,cBSPHONE,
        //            cFAX,cCELLPHONE,cINFSALUT,CLUBCODE,CLUBSTATUS,CANDIDATE,SOLICITOR,RNEWDTE,

        //            FLAG1,FLAG2,FLAG3,FLAG4,FLAG5,FLAG6,FLAG7,FLAG8,FLAG9,FLAG10,
        //            KEYWORD1,KEYWORD2,KEYWORD3,KEYWORD4,KEYWORD5,KEYWORD6,KEYWORD7,KEYWORD8,KEYWORD9,KEYWORD10                   

        //            ";

        private const string sql_select_Top200fields = @"SELECT Top 200 ISNULL(RECNO,0) AS RECNO,STATUS ,
                    ISNULL(ID,0) as ID,RECTYPE, PEOTYPE,PREFIX,FNAME,MNAME,LNAME,SUFFIX,EMPLOYER,
                    OCCUPATION,TITLE,ADDRTYPE,STREET,ADDR1,ADDR2,CITY,STATE,ZIP,PLUS4,
                    HMPHONE,BSPHONE,FAX,CELLPHONE,EMAIL,
                    URL,SPOUSENAME,PRIMEMAIL,INFSALUT,SALUTATION,MAILSALUTATION,MAILNAME,
                    ASSISTANT,ASSTBSPHONE, ASSTEMAIL,DOB, FECCMTEID,INDUSTRY,ACCTNO,
                    CPREFIX,CFNAME,CMNAME,CLNAME,CSUFFIX,CTITLE,CORGANIZATION,cSTREET,cADDR1,cADDR2,cCITY,cSTATE,cZIP,cPLUS4,cEMAIL,cHMPHONE,cBSPHONE,
                    cFAX,cCELLPHONE,cINFSALUT,CLUBCODE,CLUBSTATUS,CANDIDATE,SOLICITOR,RNEWDTE,
                    CCREFNO,BSTREET,BADDR1,BADDR2,BCITY,BSTATE,BZIP,BPLUS4,
                    FLAG1,FLAG2,FLAG3,FLAG4,FLAG5,FLAG6,FLAG7,FLAG8,FLAG9,FLAG10,
                    KEYWORD1,KEYWORD2,KEYWORD3,KEYWORD4,KEYWORD5,KEYWORD6,KEYWORD7,KEYWORD8,KEYWORD9,KEYWORD10                   
                    ,aNAME AS ATTRIBUTENAME,aSTARTDATE AS ATTRIBUTESTARTDATE,aENDDATE AS ATTRIBUTEENDDATE,aACTIVE AS ATTRIBUTEACTIVE
                    ";
        private string sql_select_Top200fields_NP()
        {
            if (session.currentDomain_project.projectType.appVersion.ToLower() == "nonprofit")
            {
                return sql_select_Top200fields.Replace("FECCMTEID,", "");
            }
            else
                return sql_select_Top200fields;
        }
        private const string sql_JFCselect_Top200fields = @"SELECT *";

        private const string sql_SRCCODEFieldsExport = @"SELECT [STATUS],	PKGEID ,	SRCECODE ,	SRCEDESC ,	SRCEDESC2 ,	SRCEDESC3,	LISTNOG,	LISTNO ,	MAILDTE ,
	sQTYMAIL,	COSTPROD1,	COSTPROD2 ,	COSTPOSTG ,	COSTRESP1,	COSTFLAT ,	COSTPERC ";

        private const string sql_intrn_select_fields = @"
                    , ACTIVITY1,ACTIVITY2,ACTIVITY3,ACTIVITY4,ACTIVITY5,ACTIVITY6,ACTIVITY7
                    , GROUP1,GROUPSTATUS1,GROUP2,GROUPSTATUS2,GROUP3,GROUPSTATUS3,GROUP4,GROUPSTATUS4,GROUP5,GROUPSTATUS5
                    , CHANNEL1";
        private const string sql_event_select_fields = @"SELECT ISNULL(RECNO,0) AS RECNO,STATUS,
                    ISNULL(ID,0) as ID,RECTYPE,PEOTYPE,PREFIX,FNAME,MNAME,LNAME,SUFFIX,EMPLOYER,OCCUPATION,
                    INFSALUT,SALUTATION,SPOUSENAME,ADDRTYPE,STREET,ADDR1,ADDR2,CITY,STATE,ZIP,PLUS4,HMPHONE,
                    BSPHONE,FAX,CELLPHONE,EMAIL,EVNTCODE,EVNTSTATUS,INVITEETYPE,ATTENDDTE,TABLENO,
                    ANSWER01,ANSWER02,ANSWER03,ANSWER04,ANSWER05,ANSWER06,ANSWER07,ANSWER08,ANSWER09,ANSWER10,
                    ANSWER11,ANSWER12,ANSWER13,ANSWER14,ANSWER15,ANSWER16,ANSWER17,ANSWER18,ANSWER19,ANSWER20,
                    ANSWER21,ANSWER22,ANSWER23,ANSWER24,ANSWER25";

        //private const string sql_event_select_fields = @"SELECT ISNULL(RECNO,0) AS RECNO,STATUS ,
        //            ISNULL(ID,0) as ID,RECTYPE, PEOTYPE,PREFIX,FNAME,MNAME,LNAME,SUFFIX,EMPLOYER,
        //            OCCUPATION,TITLE,ADDRTYPE,STREET,ADDR1,ADDR2,CITY,STATE,ZIP,PLUS4,
        //            BSTREET,BADDR1,BADDR2,BCITY,BSTATE,BZIP,BPLUS4,
        //            HMPHONE,BSPHONE,FAX,CELLPHONE,EMAIL,SecondaryEMAIL,
        //            URL,SPOUSENAME,PRIMEMAIL,INFSALUT,SALUTATION,MAILSALUTATION,MAILNAME,
        //            ASSISTANT,ASSTBSPHONE, ASSTEMAIL,DOB, INDUSTRY,
        //            CPREFIX,CFNAME,CMNAME,CLNAME,CSUFFIX,CTITLE,CORGANIZATION,cSTREET,cADDR1,cADDR2,cCITY,cSTATE,cZIP,cPLUS4,cEMAIL,cHMPHONE,cBSPHONE,cFAX,cCELLPHONE,cINFSALUT,
        //            EVNTCODE,EVNTSTATUS,INVITEETYPE,ATTENDDTE,TABLENO,
        //            ANSWER01,ANSWER02,ANSWER03,ANSWER04,ANSWER05,ANSWER06,ANSWER07,ANSWER08,ANSWER09,ANSWER10,
        //            ANSWER11,ANSWER12,ANSWER13,ANSWER14,ANSWER15,ANSWER16,ANSWER17,ANSWER18,ANSWER19,ANSWER20,
        //            ANSWER21,ANSWER22,ANSWER23,ANSWER24,ANSWER25,
        //            FLAG1,FLAG2,FLAG3,FLAG4,FLAG5,FLAG6,FLAG7,FLAG8,FLAG9,FLAG10,
        //            KEYWORD1,KEYWORD2,KEYWORD3,KEYWORD4,KEYWORD5,KEYWORD6,KEYWORD7,KEYWORD8,KEYWORD9,KEYWORD10                   
        //            ";
        private const string sql_adj_select_fields = @"                      
                SELECT RECTYPE,isnull(ID,0) as ID,ISNULL(STATUS ,'''') AS STATUS,ISNULL(PEOTYPE,'''') AS PEOTYPE,ISNULL(PREFIX,'''') AS PREFIX,ISNULL(FNAME,'''') AS FNAME,
                ISNULL(MNAME,'''') AS MNAME,ISNULL(LNAME,'''') AS LNAME,ISNULL(SUFFIX,'''') AS SUFFIX,ISNULL(EMPLOYER,'''') AS EMPLOYER,ISNULL(OCCUPATION,'''') AS OCCUPATION,
                ISNULL(SPOUSENAME,'''') AS SPOUSE,ISNULL(ADDRTYPE,'''') AS ADDRTYPE,ISNULL(STREET,'''') AS STREET,ISNULL(ADDR1,'''') AS ADDR1,ISNULL(ADDR2,'''') AS ADDR2,
                ISNULL(CITY,'''') AS CITY,ISNULL(STATE,'''') AS STATE,ISNULL(ZIP,'''') AS ZIP,ISNULL(PLUS4,'''') AS PLUS4,ISNULL(HMPHONE,'''') AS HMPHONE,
                ISNULL(BSPHONE,'''') AS BSPHONE,ISNULL(FAX,'''') AS FAX,ISNULL(CELLPHONE,'''') AS CELLPHONE,ISNULL(EMAIL,'''') AS EMAIL,ISNULL(MID,0) AS MID,ADJDTE AS ADJDTE,
                ISNULL(SRCECODE,'''') AS SRCECODE,ISNULL(AMT,0) AS AMT,ISNULL(TRACKNO,0) AS TRACKNO,ISNULL(RECNO,0) AS RECNO,ISNULL(CCREFNO,'''') AS CCREFNO,ISNULL(INFSALUT,'''') AS INFSALUT,
                ISNULL(SALUTATION,'''') AS SALUTATION,ISNULL(EARMARKED,'''') AS EARMARKED,ISNULL(FECCMTEID,'''') AS FECCMTEID,ISNULL(MONYCOMMENT,'''') AS MONYCOMMENT";

        private const string sql_entity_select_fields = @"
                      SELECT  RECTYPE,STATUS,ISNULL(ENTITYID,0) AS ENTITYID,ENTITYTYPE,ORGNAME,PREFIX,LNAME,FNAME,MNAME,SUFFIX,STREET1,STREET2,CITY,STATE,ZIP,PLUS4,PHONE,FAX,
                      CELL,EMAIL,CONTACT,EMPLOYER,OCCUPATION,TAXID,VENDACCTID,FECCMTEID,ISNULL(TXNNO,0) TXNNO,ISNULL(RECNO,0) RECNO,
                      LINENO3_EXP,LINENO3_REC,LINENO3P_EXP,LINENO3P_REC,LINENO3X_EXP,LINENO3X_REC";

        private const string sql_txn_select_fields = @"                      
                      ,ISNULL(TXNDTE,''1900-01-01'') AS TXNDTE,ISNULL(AMT,0) AS AMT,CHECKNO,TXNTYPE,FUNDCODE,BANKACCT,[LINENO],COMMENT,FEC_DESCRIPTION,ISMEMO,MEMOTXT,TRANSCAT,
                      TXNCODE,PAYTYPE,ELECTION,ELECTYR,ELECTOTHER,LINKTXNID,GLACCT_1,GLAMT_1,GLACCT_2,GLAMT_2,GLACCT_3,GLAMT_3,IS1099,
                      DISSEMDTE ,SOFECCANID ,SOCANPFX,SOCANFN,SOCANMN,SOCANLN,SOCANSFX,SOCANOFFICE,SOCANSTATE,SOCANDIST,SIGNDTE,SIGNPFX,SIGNFN,SIGNMN,SIGNLN,SIGNSFX,SOP,
                      EVENTNAME, EVENTTYPE";

        private const string sql_txnupdate_select_fields = @"                      
                      SELECT RECTYPE,STATUS,ISNULL(ENTITYID,0) AS ENTITYID,ISNULL(TXNNO,0) TXNNO,ISNULL(TXNDTE,''1900-01-01'') AS TXNDTE,ISNULL(AMT,0) AS AMT,FUNDCODE,PAYTYPE,[LINENO],FEC_DESCRIPTION";

        private const string sql_update_select_fields = @"SELECT 
                    RECTYPE, ISNULL(ID,0) as ID,  ISNULL(RECNO,0) as RECNO, ISNULL(TXNNO,0) as TXNNO, AMT, SOURCE, BATCHDTE, BATCHNO, FUNDCODE, CENTERCODE, MONYTYPE, CKNO, 
                    RECVDTE, mTRACKNO, TRACKAMT, REFERENCEID,EXECPTIONCODE, STATUS";
        private const string sql_action_select_fields = @" SELECT ISNULL(RECNO,0) AS RECNO,STATUS ,
                    ISNULL(ID,0) as ID,RECTYPE, PEOTYPE,PREFIX,FNAME,MNAME,LNAME,SUFFIX,EMPLOYER,
                    OCCUPATION,TITLE,ADDRTYPE,STREET,ADDR1,ADDR2,CITY,STATE,ZIP,PLUS4,
                    HMPHONE,BSPHONE,FAX,CELLPHONE,EMAIL,URL,ActionName,ActionDate,actionSRCECODE,ActionNote,
                    actionFIELD01,actionFIELD01VALUE,actionFIELD02,actionFIELD02VALUE,
                    actionFIELD03,actionFIELD03VALUE ";
        #region [[[ mass adjustment select sql ]]]
        private const string sql_select_fields_massAdjustmentResult = @" SELECT DISTINCT Z.STATUS,Z.STATUSINFO,P.PID,Z.KEYLINE AS MatchType,P.FNAME ,P.LNAME,
SD.LGIFTDTE AS MRCDate,SD.CTDAMT AS CTD,A.STREET,A.ADDR1 ,A.ADDR2,A.CITY,
A.[STATE] ,A.ZIP,A.PLUS4,PH.PHNNO AS EMAIL,P.SPOUSENAME,
Z.MID,Z.ADJDTE AS [Date],LT.ADJTYPE,Z.AMT AS Amount,Z.CCREFNO FROM {0} Z
INNER JOIN MONY M ON M.MID =Z.MID
LEFT OUTER JOIN MONY M2 ON M2.ORIGMID = M.MID
LEFT OUTER JOIN lkADJTYPE LT ON LT.ADJTYPEID=M2.ADJTYPEID
INNER JOIN PEOPLE P ON P.PID =M.PID
LEFT OUTER JOIN ADDRESS A ON A.PID =P.PID AND A.PRIME =1
LEFT OUTER JOIN PHONE PH ON PH.PID=P.PID AND PH.PHNTYPEID=4 AND PH.PRIME=1
LEFT OUTER JOIN SUMMARYD SD ON SD.PID=P.PID ";
        private const string sql_select_fields_massAdjustmentResultUnmatched = @" SELECT Z.STATUS,Z.STATUSINFO,Z.MID,Z.ADJDTE AS [Date],Z.AMT AS Amount,Z.CCREFNO ,Z.MONYCOMMENT AS AMTNOTMATCH
FROM {0} Z WHERE Z.STATUS='NOT MATCHED' AND Z.RECTYPE = 'D' ";
        private const string sql_select_fields_massAdjustmentResultProcessed = @" SELECT Z.STATUS,Z.STATUSINFO,Z.MID,Z.ADJDTE AS [Date],Z.AMT AS Amount,Z.CCREFNO ,Z.ID AS ADJMID
FROM zMONYADJIMPORTPROCESSING Z WHERE Z.TABLENAME='{0}' AND Z.RECTYPE = 'D' ";
        #endregion

        #region [[ Corp Match select sql ]]
        private const string sql_corpmatch_select_fields = @"                      
            SELECT [RECTYPE],[STATUS],[ID],[PEOTYPE],[CHAPCODE],[PREFIX],[FNAME],[MNAME],[LNAME],[SUFFIX],
            [EMPLOYER],[OCCUPATION],[TITLE],[ADDRTYPE],[STREET],[ADDR1],[ADDR2],[CITY],[STATE],[ZIP],[PLUS4],[HMPHONE],[BSPHONE],[FAX],[CELLPHONE],[EMAIL],
            [PEOSTR1],[FLAG1],[FLAG2],[FLAG3],[KEYWORD1],[KEYWORD2],[KEYWORD3],[MID],[GIFTTYPE],[BATCHNO],[BATCHDTE],[MONYCODE],[GIFTDTE],[AMT],[MONYTYPE],[FUNDCODE],[SRCECODE],
            [CENTERCODE],[CAMPGNCODE],[COMMENT],
	        [COMPID],[COMPPEOTYPE],[COMPCHAPCODE],[COMPNAME],[COMPSTREET],[COMPADDR1],[COMPADDR2],[COMPCITY],[COMPSTATE],[COMPZIP],[COMPPLUS4],
            [COMPBSPHN],[COMPFAX],
            [COMPFLAG1],[COMPFLAG2],[COMPFLAG3],[COMPKEYWORD1],[COMPKEYWORD2],[COMPKEYWORD3],[COMPBATCHNO],[COMPBATCHDTE],[COMPMONYCODE],[COMPGIFTDTE],[COMPAMT],[COMPMONYTYPE],
            [COMPFUNDCODE],[COMPSRCECODE],[COMPCENTERCODE],[COMPCAMPGNCODE],[COMPCOMMENT],[RECNO],[COMPRECNO],[TXNNO],[COMPTXNNO]";
        #endregion

        #region [[ Pledge Import select sql ]]
        private const string sql_pledge_select_fields = @"                      
            SELECT [RECTYPE],[ID],[PEOTYPE],[PREFIX],[FNAME],[MNAME],[LNAME],[SUFFIX],
            [EMPLOYER],[OCCUPATION],[ADDRTYPE],[STREET],[ADDR1],[ADDR2],[CITY],[STATE],[ZIP],[PLUS4],[HMPHONE],[BSPHONE],[FAX],[CELLPHONE],[EMAIL],
	        [PLEDGEAMT],[PLEDGEDTE],[EXPECTDTE],[HOUSEDTE],[HOUSEAMT],[FUNDCODE],[SRCECODE],[TRACKNO],[TRACKAMT],[COMMENT],[RECNO],[PLEDGEID],[STATUS]";
        #endregion

        #region [[ Tribute select sql ]]
        private const string sql_tribute_select_fields = @"                      
            SELECT [RECTYPE],[STATUS],[ID],[PEOTYPE],[CHAPCODE],[PREFIX],[FNAME],[MNAME],[LNAME],[SUFFIX],[SALUTATION],[INFSALUTATION],[MAILSALUTATION],
            [EMPLOYER],[OCCUPATION],[TITLE],[ADDRTYPE],[STREET],[ADDR1],[ADDR2],[CITY],[STATE],[ZIP],[PLUS4],[HMPHONE],[BSPHONE],[FAX],[CELLPHONE],[EMAIL],
            [PEOSTR1],[FLAG1],[FLAG2],[FLAG3],[KEYWORD1],[KEYWORD2],[KEYWORD3],[GIFTTYPE],[BATCHNO],[BATCHDTE],[MONYCODE],[GIFTDTE],[AMT],[MONYTYPE],[FUNDCODE],[SRCECODE],
            [CENTERCODE],[CAMPGNCODE],[COMMENT],
	        [MEMTYPE],[MEMPID],[MEMNAME],[ACKWPID],[RECNO],[TXNNO],[MEMRECNO],[MEMTXNNO],[ACKWRECNO],[ACKWTXNNO]";
        #endregion

        #region [[ Support Adjustment select sql ]]
        private const string sql_support_select_fields = @"                      
            SELECT [RECTYPE],[STATUS],[ORIGMID],[ID],[ADJDTE],[AMT],[MONYCOMMENT],[RECNO],[TXNNO]";
        #endregion

        #endregion

        [HttpGet, Route("api/Import/lkIMPTYPE")]
        public List<lkIMPTYPE> GetImportType()
        {
            List<lkIMPTYPE> result;
            if (session.can_i_do("cmdiapp.dms.Import_Rec(Interactions)", "v"))
            {
                // Only System Users can see Gift Update option
                // 2023-02-03 per Jeff allow all users to do Gift Update
                /*
                if (session.currentDomainAuthorizedProject_userGroupId == 5)
                    result = _entity_crm.All<lkIMPTYPE>().Where(x => x.IsActive == true).ToList();
                else
                    result = _entity_crm.All<lkIMPTYPE>().Where(x => x.IsActive == true && x.IMPTYPEID != 8).ToList();
                */
                result = _entity_crm.All<lkIMPTYPE>().Where(x => x.IsActive == true).ToList();
            }
            // the space is on purpose here 
            else if (session.can_i_do(" cmdiapp.dms.entity_qq(Vendors)", "v"))
            {
                result = _entity_crm.All<lkIMPTYPE>().Where(x => x.IsActive == false && (x.IMPTYPEID >= 30 && x.IMPTYPEID <= 31)).ToList();
            }
            else
            {
                // Only System Users can see Gift Update option
                // 2023-02-03 per Jeff allow all users to do Gift Update
                /*
                if (session.currentDomainAuthorizedProject_userGroupId == 5)
                    result = _entity_crm.All<lkIMPTYPE>().Where(x => x.IsActive == true && !(x.IMPTYPEID >= 4 && x.IMPTYPEID <= 5)).ToList();
                else
                    result = _entity_crm.All<lkIMPTYPE>().Where(x => x.IsActive == true && !(x.IMPTYPEID >= 4 && x.IMPTYPEID <= 5) && x.IMPTYPEID != 8).ToList();
                */
                result = _entity_crm.All<lkIMPTYPE>().Where(x => x.IsActive == true && !(x.IMPTYPEID >= 4 && x.IMPTYPEID <= 5)).ToList();
            }
            // remove Corp Match if no access
            if (!session.can_i_do("/crm/CorpMatch", "e"))
                result.RemoveAll(r => r.IMPTYPEID == 35);
            // remove Pledge if no access
            if (!session.can_i_do("cmdiapp.dms.People_Pledge", "e"))
                result.RemoveAll(r => r.IMPTYPEID == 38);
            if (!session.can_i_do("cmdiapp.dms.People_Money", "e"))
            {
                result.RemoveAll(r => r.IMPTYPEID == 39);
                result.RemoveAll(r => r.IMPTYPEID == 40);
            }
            //New Gift By Date And Source Code by config (import-gift-tally)
            if (crmSession.configValue(crmConstants.import_gift_tally) != "Y")
                result.RemoveAll(r => r.IMPTYPEID == 36);
            return result;
        }

        [HttpGet, Route("api/Import/TxnlkIMPTYPE")]
        public List<lkIMPTYPE> GetTxnImportType()
        {
            return _entity_crm.All<lkIMPTYPE>().Where(x => x.IMPTYPEID >= 30 && x.IMPTYPEID <= 31 || x.IMPTYPEID == 37).ToList();
        }

        //GetImportFieldForJFC

        [HttpGet, Route("api/Import/GetImportFieldForJFC/{importType}")]
        public List<lkImpField> GetImportField(DataSet ds)
        {
            List<lkImpField> _impfield = new List<lkImpField>();

            return _impfield;
        }



        [HttpGet, Route("api/Import/lkIMPField/{importType}")]
        public List<lkImpField> GetImportField(int importType)
        {
            List<lkImpField> _impfield = new List<lkImpField>();
            if (importType == 0)
            {
                //Gift Import Type - include money fields and exclude interaction fields, 
                //exclude People Track#,Recruiter Track#,Commitment Amt by sofia's request 2/6/2018
                if (crmSession.configValue(crmConstants.Money_custom_Reference_Id) == "Y"&&crmSession.configValue(crmConstants.ConduitModule) == "Y")
                {
                    _impfield = _entity_crm.All<lkImpField>().Where(x => x.IMPTYPE == "People" && x.IMPLISTID != 3 && x.IMPLISTID != 6).OrderBy(p => p.SEQ).ThenBy(p=>p.IMPFLDID).ToList();
                    
                }
                else if (crmSession.configValue(crmConstants.Money_custom_Reference_Id) == "Y" && crmSession.configValue(crmConstants.ConduitModule) != "Y")
                {
                    _impfield = _entity_crm.All<lkImpField>().Where(x => x.IMPTYPE == "People" && x.IMPLISTID != 3 && x.IMPLISTID != 6 && x.IMPLISTID != 7).OrderBy(p => p.SEQ).ThenBy(p => p.IMPFLDID).ToList();
                }
                else if (crmSession.configValue(crmConstants.Money_custom_Reference_Id) != "Y" && crmSession.configValue(crmConstants.ConduitModule) == "Y")
                {
                    _impfield = _entity_crm.All<lkImpField>().Where(x => x.IMPTYPE == "People" && x.IMPLISTID != 3 && x.IMPLISTID != 6 && x.IMPLISTID != 5).OrderBy(p => p.SEQ).ThenBy(p => p.IMPFLDID).ToList();
                }
                else
                {
                    _impfield = _entity_crm.All<lkImpField>().Where(x => x.IMPTYPE == "People" && x.IMPLISTID != 3 && x.IMPLISTID != 5 && x.IMPLISTID != 6 && x.IMPLISTID != 7).OrderBy(p => p.SEQ).ThenBy(p => p.IMPFLDID).ToList();
                }
            }
            else if ((importType == 4 || importType == 5) && (session.can_i_do("cmdiapp.dms.Import_Rec(Interactions)", "v")))
            {
                //Interaction Import Type - exclude money fields
                _impfield = _entity_crm.All<lkImpField>().Where(x => x.IMPTYPE == "People" && x.IMPLISTID != 2 && x.IMPLISTID != 5 && x.IMPLISTID != 7).OrderBy(p => p.SEQ).ThenBy(p => p.IMPFLDID).ToList();
            }
            else if (importType == 6)
            {
                //Event Import Type - include all event fields
                _impfield = _entity_crm.All<lkImpField>().Where(x => x.IMPTYPE == "Event").OrderBy(p => p.SEQ).ToList();
            }
            else if (importType == 7)
            {
                _impfield = _entity_crm.All<lkImpField>().Where(x => x.IMPTYPE == "Telemarketing").OrderBy(p => p.SEQ).ToList();
            }
            else if (importType == 21 || importType == 22 || importType == 23 || importType == 24)
            {
                _impfield = _entity_crm.All<lkImpField>().Where(x => x.IMPTYPE == "Adjustment").OrderBy(p => p.SEQ).ToList();
            }
            else if (importType == 30)
            {
                //return _entity_crm.All<lkImpField>().Where(x => x.IMPTYPE == "Transaction").OrderBy(p => p.SEQ).ToList();

                var fund = _entity_crm.All<dmFUND>().Where(a => a.ACTIVE == true).AsQueryable();
                var form3fund = fund.Where(a => a.FORM3 == "3").ToList();
                var form3pfund = fund.Where(a => a.FORM3 == "3P").ToList();
                var form3xfund = fund.Where(a => a.FORM3 == "3X").ToList();

                //check if it is nonprofit
                if (session.currentDomain_project.projectType.appVersion.ToLower() == "nonprofit")
                {
                    _impfield = _entity_crm.All<lkImpField>().Where(x => x.IMPTYPE == "Transaction" && x.IMPLISTID != 5).OrderBy(p => p.SEQ).ToList();
                }
                else
                {
                    _impfield = _entity_crm.All<lkImpField>().Where(x => x.IMPTYPE == "Transaction").OrderBy(p => p.SEQ).ToList();
                }                

                if (form3fund.Count > 0)
                {
                    _impfield.AddRange(_entity_crm.All<lkImpField>().Where(x => x.IMPTYPE == "Vendor" && x.IMPLISTID == 6).OrderBy(p => p.SEQ).ToList());
                }

                if (form3pfund.Count > 0)
                {
                    _impfield.AddRange(_entity_crm.All<lkImpField>().Where(x => x.IMPTYPE == "Vendor" && x.IMPLISTID == 7).OrderBy(p => p.SEQ).ToList());
                }

                if (form3xfund.Count > 0)
                {
                    _impfield.AddRange(_entity_crm.All<lkImpField>().Where(x => x.IMPTYPE == "Vendor" && x.IMPLISTID == 8).OrderBy(p => p.SEQ).ToList());
                }

            }
            else if (importType == 31)
            {
                //check if it is nonprofit
                if (session.currentDomain_project.projectType.appVersion.ToLower() == "nonprofit")
                {
                    _impfield = _entity_crm.All<lkImpField>().Where(x => x.IMPTYPE == "Transaction" && x.IMPLISTID != 5).OrderBy(p => p.SEQ).ToList();
                }
                else
                {
                    _impfield = _entity_crm.All<lkImpField>().Where(x => x.IMPTYPE == "Transaction").OrderBy(p => p.SEQ).ToList();
                }
            }
            else if (importType == 32)
            {
                _impfield = _entity_crm.All<lkImpField>().Where(x => x.IMPTYPE == "SRCCODE" && x.IMPLISTID == null).OrderBy(p => p.SEQ).ToList();
            }
            else if (importType == 33)
            {
                _impfield = _entity_crm.All<lkImpField>().Where(x => x.IMPTYPE == "MassAdjustment").OrderBy(p => p.SEQ).ToList();
            }
            else if (importType == 34)
            {
                _impfield = _entity_crm.All<lkImpField>().Where(x => x.IMPTYPE == "DataMatch").OrderBy(p => p.SEQ).ToList();
            }
            else if (importType == 99)
            {
                _impfield = _entity_crm.All<lkImpField>().Where(x => x.IMPTYPE == "JFCVETT").OrderBy(p => p.SEQ).ToList();
            }
            else if (importType == 8)
            {
                _impfield = _entity_crm.All<lkImpField>().Where(x => x.IMPTYPE == "Update").OrderBy(p => p.SEQ).ToList();
            }
            else if (importType == 9)
            {
                _impfield = _entity_crm.All<lkImpField>().Where(x => x.IMPTYPE == "Action").OrderBy(p => p.SEQ).ToList();
            }
            else if (importType == 35)
            {
                _impfield = _entity_crm.All<lkImpField>().Where(x => x.IMPTYPE == "CorpMatch").OrderBy(p => p.SEQ).ToList();
                // replace People STR1 Field with correct custom label
                if (String.IsNullOrEmpty(crmSession.people_str1_field()))
                    _impfield.RemoveAll(t => t.FLDNAME == "PEOSTR1");
                else
                    _impfield.Find(i => i.FLDNAME == "PEOSTR1").DISNAME = crmSession.people_str1_field();
            }
            else if (importType == 36)
            {
                _impfield = _entity_crm.All<lkImpField>().Where(x => x.IMPTYPE == "New Gift By Date And Source Code").OrderBy(p => p.SEQ).ToList();
            }
            else if (importType == 37)
            {
                _impfield = _entity_crm.All<lkImpField>().Where(x => x.IMPTYPE == "UpdateTxn" && x.IMPLISTID == null).OrderBy(p => p.SEQ).ToList();
            }
            else if (importType == 38)
            {
                _impfield = _entity_crm.All<lkImpField>().Where(x => x.IMPTYPE == "Pledge" && x.IMPLISTID == null).OrderBy(p => p.SEQ).ToList();
            }
            else if (importType == 39)
            {
                _impfield = _entity_crm.All<lkImpField>().Where(x => x.IMPTYPE == "Tribute").OrderBy(p => p.SEQ).ToList();
                // replace People STR1 Field with correct custom label
                if (String.IsNullOrEmpty(crmSession.people_str1_field()))
                    _impfield.RemoveAll(t => t.FLDNAME == "PEOSTR1");
                else
                    _impfield.Find(i => i.FLDNAME == "PEOSTR1").DISNAME = crmSession.people_str1_field();
                // replace Tribute with Hon/Mem for Liver
                if (crmSession.configValue(crmConstants.alf_nonprofit) == "Y")
                {
                    _impfield.Find(i => i.FLDNAME == "MEMTYPE").DISNAME = "Hon/Mem Type";
                }
            }
            else if (importType == 40)
            {
                _impfield = _entity_crm.All<lkImpField>().Where(x => x.IMPTYPE == "Support").OrderBy(p => p.SEQ).ToList();
            }
            else
            {
                //Contact, Email Sign up and Append Import Type - exclude money fields and interaction fields
                _impfield = _entity_crm.All<lkImpField>().Where(x => x.IMPTYPE == "People" && x.IMPLISTID != 2 && x.IMPLISTID != 3 && x.IMPLISTID != 5 && x.IMPLISTID != 7).OrderBy(p => p.SEQ).ThenBy(p => p.IMPFLDID).ToList();
                
            }
            //for non profit
            if (session.currentDomain_project.projectType.appVersion.ToLower() == "nonprofit")
            {
                _impfield.RemoveAll(t => t.FLDNAME == "FECCMTEID" || t.FLDNAME == "CANDIDATE" || t.FLDNAME == "EARMARKED" || t.FLDNAME.ToLower().Contains("conduit"));
            }

            //handle ACCTNO configuration
            Mapper.CreateMap<lkImpField, lkImpField>();
            List<lkImpField> result = _impfield.Select(a => Mapper.Map<lkImpField, lkImpField>(a)).ToList();
            try
            {
                if (result[0].IMPTYPE == "People")
                {
                    string acctNOConfig = crmSession.people_ACCTNO_field();
                    if (string.IsNullOrEmpty(acctNOConfig))
                    {
                        result.Remove(result.Find(i => i.FLDNAME == "ACCTNO"));
                    }
                    else
                    {
                        result.Find(i => i.FLDNAME == "ACCTNO").DISNAME = acctNOConfig;
                    }

                    // for non profilt
                    // change People Track# to Gift Officer Track#
                    // change Fundraiser Track# to Gift Track#
                    if (session.currentDomain_project.projectType.appVersion.ToLower() == "nonprofit")
                    {
                        result.Find(i => i.FLDNAME == "pTRACKNO").DISNAME = "Gift Officer Track#";
                        result.Find(i => i.FLDNAME == "mTRACKNO").DISNAME = "Gift Track#";
                    }                        
                }

                return result;
            }
            catch(Exception e)
            {
                return result;
            }            
        }

        [HttpGet, Route("api/Import/GetSavedwebIMPPREF/{imptype}")]
        public List<webIMPPREF> GetSavedMappingPrefbyImptype(int imptype)
        {
            Int16? uid = crmSession.UID();
            byte? _imptype = (byte?)imptype;
            //Note:IMPPREFID is taken as imptype+1 as distict of primary key are all values. We need unique combination of imptype and prefname
            string _sql = "select distinct imptype, prefname, (imptype+1) as IMPPREFID, UID, '' as FILECOLUMN, '' as DBCOLUMN, '' as MISCCOLUMN  from webimppref";

            return _entity_crm.getContext().Database.SqlQuery<webIMPPREF>(_sql).Where(x => x.IMPTYPE == _imptype && x.UID == uid).ToList();
        }

        [HttpGet, Route("api/Import/GetwebIMPPREFmappings/{prefname}/{imptype}")]
        public List<webIMPPREF> GetwebIMPPREFbyimptype(string prefname, int imptype)
        {
            Int16? uid = crmSession.UID();
            return _entity_crm.All<webIMPPREF>().Where(x => x.PREFNAME == prefname && x.UID == uid).ToList(); 
        }

        [HttpPost, Route("api/Import/DeleteMapping")]
        public genericResponse DeleteMappings(MappingData mappingdata)
        {
            //SelectListItem
            genericResponse response;
            int uid = (int)crmSession.UID();

            try
            {

                int q = _webIMPPREFService.DeleteMapping(uid, mappingdata.IMPTYPEID, mappingdata.PREFNAME);
                if (q >= 0) // the q will be 0 is no records are updated otherwise it will have count of number of records updated
                {
                    response = new genericResponse() { success = true, message = "Mapping deleted successfully." };
                    return response;
                }
                else
                {
                    response = new genericResponse() { success = false, message = "There is a problem in deleting Mapping. Please try again later." };
                    return response;
                }
            }
            catch
            {
                response = new genericResponse() { success = false, message = "There is a problem in deleting Mapping. Please try again later." };
                return response;
            }
        }

        [HttpPost, Route("api/Import/SaveMapping")]
        public genericResponse SaveMapping(MappingData mappingdata)
        {
            genericResponse response;
            int uid = (int)crmSession.UID();
            webIMPPREF impprefrecord;

            try
            {
                int q = _webIMPPREFService.DeleteMapping(uid, mappingdata.IMPTYPEID, mappingdata.PREFNAME);
                if (q >= 0)
                {
                    foreach (MappingRow row in mappingdata.MAPPING)
                    {
                        impprefrecord = new webIMPPREF();
                        impprefrecord.UID = (short)uid;
                        impprefrecord.IMPTYPE = (byte)mappingdata.IMPTYPEID;
                        impprefrecord.PREFNAME = mappingdata.PREFNAME;
                        impprefrecord.FILECOLUMN = row.FILECOLUMNNAME;
                        impprefrecord.DBCOLUMN = row.MAPPEDCOLUMNNAME;                        

                        _entity_crm.Add(impprefrecord);

                    }
                    _entity_crm.CommitChanges();
                }

                response = new genericResponse() { success = true, message = "Mapping saved Successfully." };
                return response;
            }
            catch
            {
                response = new genericResponse() { success = false, message = "Mapping could not be saved Successfully." };
                return response;
            }
        }

        public int ProcessTheRecords(MessageBoard msgBoard, ImportTypeData importtypedata, List<lkImpField> dbColumnMapList, string importTable, ObservableCollection<importRow.Row> rows, int completedcount, int totalCount)
        {
            int _upperLimit = completedcount + 50 < totalCount ? completedcount + 50 : totalCount;
            string _sql = CreateImportDataQuery(importtypedata, dbColumnMapList, importTable, rows, completedcount, _upperLimit);
            msgBoard = _importService.process_sql(_sql, "Import", msgBoard);
            //return completedCount
            return _upperLimit;
        }

        public int ProcessTheRecords2(MessageBoard msgBoard, ImportTypeData importtypedata, List<lkImpField> dbColumnMapList, string importTable, ObservableCollection<importRow.Row> rows, int completedcount, int totalCount)
        {
            int _upperLimit = completedcount + 50 < totalCount ? completedcount + 50 : totalCount;
            string _sql = CreateImportDataQueryJFC(importtypedata, dbColumnMapList, importTable, rows, completedcount, _upperLimit);
            msgBoard = _importService.process_sql(_sql, "Import", msgBoard);
            //return completedCount
            return _upperLimit;
        }

        [HttpPost, Route("api/Import/Validate")]
        public MessageBoard Validate(ImportTypeData importtypedata)
        {
            /*
             *  1. UI validation
             *  2. Data Validation
             *     2a. Create temp table from ZEventImport or crmImport
             *     2b. Insert Data in Temp table.
             *     2c. do data validation using zImportEventValidate or crmImportValidate
             *     2e. Send validated results back.
             */

            //[!_web_app].[_IMPBTHAKKAR515]
            
            MessageBoard msgBoard = new MessageBoard();
            msgBoard.status = false;
            msgBoard.message = "";

            string sql;
            string importTable = "";
            List<string> columns;
            ObservableCollection<importRow.Row> rows;
            string FUNDCODE;

            try
            {
                msgBoard = UIvalidation(importtypedata);

                if (msgBoard.status == true)
                {

                    //FUNDCODE = _entity_crm.All<dmFUND>().Where(a => a.FUNDID == importtypedata.FUNDID).Single<dmFUND>().FUNDCODE.ToString();

                    // Open Excel
                    var filePath = Path.Combine(System.Web.Hosting.HostingEnvironment.MapPath("~/Uploads"), importtypedata.UPLOADEDFILENAME);
                    //ImportExcelXls impExcel = new ImportExcelXls(filePath, true, true);
                    ImportExcelXls impExcel = new ImportExcelXls(filePath, true, true, true, importtypedata.WORKSHEET);
                    DataSet ds = impExcel.result;
                    //Get columns from the excel file.
                    columns = GetExcelColumnNames(ds);
                    //Get data in rows from the excel file.
                    rows = GetExcelRowData(ds);

                    //Get the list of columns for the import type
                    List<lkImpField> dbColumnMapList = GetImportField(importtypedata.IMPTYPEID);

                    //excel data validation
                    msgBoard = excelDataValidation(importtypedata, dbColumnMapList, rows);
                    if (msgBoard.status == false)
                    {
                        return msgBoard;
                    }

                    //Temp table name
                    importTable = GenerateImportTableName();

                    //Create import table query
                    sql = CreateImportTableQuery(importtypedata.IMPTYPEID, importTable);

                    //Append Header 
                    sql = sql + "\n\r" + CreateImportHeaderQuery(importtypedata, dbColumnMapList, importTable);

                    //Updating Header row for Interaction Loads
                    if ((importtypedata.IMPTYPEID > 3) && (importtypedata.IMPTYPEID != 6) && (importtypedata.IMPTYPEID != 21) && (importtypedata.IMPTYPEID != 22) && (importtypedata.IMPTYPEID != 23) && (importtypedata.IMPTYPEID != 24) && (importtypedata.IMPTYPEID != 30) && (importtypedata.IMPTYPEID != 31) && (importtypedata.IMPTYPEID != 8) && 
                        (importtypedata.IMPTYPEID != 32) && (importtypedata.IMPTYPEID != 9) && (importtypedata.IMPTYPEID != 35) && (importtypedata.IMPTYPEID != 36) && (importtypedata.IMPTYPEID != 37) && (importtypedata.IMPTYPEID != 38) && (importtypedata.IMPTYPEID != 39) && (importtypedata.IMPTYPEID != 40))
                    {
                        FUNDCODE = _entity_crm.All<dmFUND>().Where(a => a.FUNDID == importtypedata.FUNDID).Single<dmFUND>().FUNDCODE.ToString();
                        sql = sql + "\n\r" + string.Format(@" UPDATE {0} SET FUNDCODE = '{1}', SOURCE = '{2}',BATCHDTE = '{3}', CHANNEL1 ='{4}' WHERE RECTYPE = 'H' ",
                                                                   importTable,
                                                                   FUNDCODE,
                                                                   GetCode(importtypedata.SOURCECD),
                                                                   importtypedata.IMPTYPEID == 4 || importtypedata.IMPTYPEID == 5 ? importtypedata.INTERACTIONDATE : importtypedata.BATCHDATE,
                                                                   importtypedata.CHANNELID
                                                                   );
                    }

                    if (importtypedata.IMPTYPEID == 31)
                    {
                        importtypedata.FUNDCODE = _entity_crm.All<dmFUND>().Where(a => a.FUNDID == importtypedata.FUNDID).Single<dmFUND>().FUNDCODE.ToString();
                        importtypedata.CENTERCODE = _entity_crm.All<dmCENTER>().Where(a => a.CENTERID == importtypedata.CENTERID).Single<dmCENTER>().CENTERCODE.ToString();
                    }

                    //Get the first 50 rows
                    sql = sql + "\n\r" + CreateImportDataQuery(importtypedata, dbColumnMapList, importTable, rows, 0, 50);

                    cl_query.create_a_sqlLog(sql.Trim(), "Import Module(Before Validate Step)", importTable, crmSession.UID().Value, rows.Count, "", "", _entity_crm, 0);

                    //
                    System.Diagnostics.Stopwatch stopWatch = new System.Diagnostics.Stopwatch();
                    stopWatch.Start();

                    // Execute the first 50 rows.
                    if (sql != String.Empty)
                    {
                        msgBoard = _importService.process_sql(sql, "Import", msgBoard);
                    }

                    stopWatch.Stop();
                    #region [[ Save the SQL in Log Table ]]

                    cl_query.create_a_sqlLog(
                               sql.Trim(),
                               "Import Module(Validate Step)",
                               importTable,
                               crmSession.UID().Value,
                               rows.Count,
                               "",
                               "",
                               _entity_crm,
                               Convert.ToSingle(stopWatch.ElapsedMilliseconds / 1000.0));

                    #endregion
                    //


                    if (msgBoard.status == true)
                    {
                        //Get the rest of the rows.
                        if (rows.Count > 50)
                        {
                            //sql = CreateImportDataQuery(importtypedata, dbColumnMapList, importTable, rows, 50, 0);
                            //msgBoard = _importService.process_sql(sql, "Import", msgBoard);

                            try
                            {
                                int _compCount = 50;
                                while (_compCount < rows.Count)
                                {
                                    _compCount = ProcessTheRecords(msgBoard, importtypedata, dbColumnMapList, importTable, rows, _compCount, rows.Count);
                                }
                            }
                            catch (Exception ex)
                            {
                                throw new System.ApplicationException("There is some problem processing your request (Code: 1083). Please try again later.");
                            }

                        }
                    }
                    else
                    {
                        throw new System.ApplicationException("There is some problem processing your request (Code: 1083). Please try again later.");
                    }

                    //Validate Data 
                    if (msgBoard.status == true)
                    {
                        //[[[[skip for srccode import because it was done]]]]
                        if(importtypedata.IMPTYPEID == 32)
                        {
                            msgBoard.xmlresponse = "NoError";
                            msgBoard.tempTableName = importTable;
                            msgBoard.recordCount = rows.Count;
                        }
                        else
                        {
                            msgBoard = DataValidation(importtypedata, importTable);
                            //Parse the response - need to improve
                            if (msgBoard.status && !string.IsNullOrEmpty(msgBoard.xmlresponse))
                            {
                                System.IO.TextReader tr = new System.IO.StringReader(msgBoard.xmlresponse);

                                XDocument oDoc = XDocument.Load(tr);
                                var result = from a in oDoc.Descendants("Validate")
                                             select new Xmlresult
                                             {
                                                 result = Convert.ToString(a.Element("RESULT").Value),
                                             };
                                string msg = (result.First() as Xmlresult).result;
                                //Now check the Message
                                if (msg != "")
                                {
                                    msgBoard.message = msg + "\r\nPlease try again after all errors are fixed.";
                                    msgBoard.status = false;
                                    msgBoard.xmlresponse = "Error";
                                    msgBoard.tempTableName = importTable;
                                }
                                else
                                {
                                    msgBoard.message = "Success";
                                    msgBoard.status = true;
                                    msgBoard.xmlresponse = "NoError";
                                    msgBoard.tempTableName = importTable;
                                    msgBoard.recordCount = rows.Count;
                                }
                            }
                        }
                    }
                    else
                    {
                        throw new System.ApplicationException("There is some problem processing your request (Code: 1083). Please try again later.");
                    }

                }
                else
                {
                    throw new System.ApplicationException("\r\nPlease try again after all errors are fixed.");
                }
            }
            catch (Exception ex)
            {
                msgBoard.message = msgBoard.message + ex.Message;
                msgBoard.status = false;
            }

            return msgBoard;
        }

        [HttpPost, Route("api/Import/JFCVettValidate")]
        public MessageBoard JFCVettValidate(ImportTypeData importtypedata)
        {

            
            MessageBoard msgBoard = new MessageBoard();
            msgBoard.status = false;
            msgBoard.message = "";

            string sql;
            string importTable = "";
            List<string> columns;
            ObservableCollection<importRow.Row> rows;
            
            try
            {
                msgBoard = UIvalidation(importtypedata);

                if (msgBoard.status == true)
                {
                    // Open Excel
                    var filePath = Path.Combine(System.Web.Hosting.HostingEnvironment.MapPath("~/Uploads"), importtypedata.UPLOADEDFILENAME);
                    //ImportExcelXls impExcel = new ImportExcelXls(filePath, true, true);
                    ImportExcelXls impExcel = new ImportExcelXls(filePath, true, true, true, importtypedata.WORKSHEET);
                    DataSet ds = impExcel.result;
                    //Get columns from the excel file.
                    columns = GetExcelColumnNames(ds);
                    //Get data in rows from the excel file.
                    rows = GetExcelRowData(ds);

                    //Get the list of columns for the import type
                    List<lkImpField> dbColumnMapList = GetImportField(importtypedata.IMPTYPEID);

                    //Temp table name
                    importTable = GenerateImportTableName();

                    sql = CreateTempTableBasedOnInput(importtypedata, ds.Tables[0], importTable, dbColumnMapList);

                    //Append Header
                    sql = sql + "\n\r" + CreateImportHeaderQueryJFC(importtypedata, dbColumnMapList, importTable);

                    //Get the first 50 rows
                    sql = sql + "\n\r" + CreateImportDataQueryJFC(importtypedata, dbColumnMapList, importTable, rows, 0, 50);

                    cl_query.create_a_sqlLog(sql.Trim(), "Import Module(Before JFC Vett Validate Step)", importTable, crmSession.UID().Value, rows.Count, "", "", _entity_crm, 0);

                    System.Diagnostics.Stopwatch stopWatch = new System.Diagnostics.Stopwatch();
                    stopWatch.Start();

                    // Execute the first 50 rows.
                    if (sql != String.Empty)
                    {
                        msgBoard = _importService.process_sql(sql, "Import", msgBoard);
                    }

                    stopWatch.Stop();
                    #region [[ Save the SQL in Log Table ]]

                    cl_query.create_a_sqlLog(
                               sql.Trim(),
                               "Import Module(JFC Vett Validate Step)",
                               importTable,
                               crmSession.UID().Value,
                               rows.Count,
                               "",
                               "",
                               _entity_crm,
                               Convert.ToSingle(stopWatch.ElapsedMilliseconds / 1000.0));

                    #endregion
                    //

                    if (msgBoard.status == true)
                    {
                        //Get the rest of the rows.
                        if (rows.Count > 50)
                        {
                            try
                            {
                                int _compCount = 50;
                                while (_compCount < rows.Count)
                                {
                                    _compCount = ProcessTheRecords2(msgBoard, importtypedata, dbColumnMapList, importTable, rows, _compCount, rows.Count);
                                }
                            }
                            catch (Exception ex)
                            {
                                throw new System.ApplicationException("There is some problem processing your request (Code: 1083). Please try again later.");
                            }

                        }
                    }
                    else
                    {
                        throw new System.ApplicationException("There is some problem processing your request (Code: 1083). Please try again later.");
                    }
                    
                    //Now the temp table is created and we have all rows inserted...so let us check if we have necessary columns in Temp Table or not..
                    bool IsProcessed = CreateNecessaryColumnsifMissing(importTable);
                    
                    //Validate Data : Check if all records have Last Name, Street and Zip specified...
                    if (msgBoard.status == true)
                    {
                        msgBoard = ProcessJFCDataValidation(importtypedata, importTable);
                    }
                    else
                    {
                        throw new System.ApplicationException("There is some problem processing your request (Code: 1083). Please try again later.");
                    }

                    //Parse the response - need to improve
                    if (msgBoard.status && !string.IsNullOrEmpty(msgBoard.xmlresponse))
                    {
                        System.IO.TextReader tr = new System.IO.StringReader(msgBoard.xmlresponse);

                        XDocument oDoc = XDocument.Load(tr);
                        var result = from a in oDoc.Descendants("Validate")
                                     select new Xmlresult
                                     {
                                         result = Convert.ToString(a.Element("RESULT").Value),
                                     };
                        string msg = (result.First() as Xmlresult).result;
                        //Now check the Message
                        if (msg != "")
                        {
                            msgBoard.message = msg + "\r\nPlease try again after all errors are fixed.";
                            msgBoard.status = false;
                            msgBoard.xmlresponse = "Error";
                            msgBoard.tempTableName = importTable;
                        }
                        else
                        {
                            //We are good...Validation is fine so let us go ahead and Process the records for JFC Vetting......
                            importtypedata.IMPORTTABLENAME = importTable;
                            msgBoard = ProcessJFCVettData(importtypedata);

                            if (msgBoard.status)
                            {
                                msgBoard.message = "Success";
                                msgBoard.status = true;
                                msgBoard.xmlresponse = "NoError";
                                msgBoard.tempTableName = importTable;
                                msgBoard.recordCount = rows.Count;
                            }
                            else
                            {
                                msgBoard.message = msg + "\r\nError while processing JFC Vett Donation Total pull.";
                                msgBoard.status = false;
                                msgBoard.xmlresponse = "Error";
                                msgBoard.tempTableName = importTable;
                            }

                        }
                    }
                    
                }
                else
                {
                    throw new System.ApplicationException("\r\nPlease try again after all errors are fixed.");
                }
            }
            catch (Exception ex)
            {
                msgBoard.message = msgBoard.message + ex.Message;
                msgBoard.status = false;
            }

            return msgBoard;
        }
        
        [HttpGet, Route("api/Import/PeopleResult/{imptype}/{importTable}")]
        public List<ImportStep5ViewModel> PeopleResult(int imptype, string importTable)
        {
            List<ImportStep5ViewModel> impData;
            string sql = "";
            if (imptype >= 4 && imptype != 6)
            {
                sql = sql_select_Top200fields_NP() + sql_intrn_select_fields; //showing only top 200
            }
            if (imptype == 99)
            {
                sql = sql_JFCselect_Top200fields; 
            }
            else
            {
                if (imptype == 0)//new gift
                {
                    sql = sql_select_Top200fields_NP() + sql_money_select_field;
                    if (crmSession.configValue(crmConstants.Money_custom_Reference_Id) == "Y")
                    {
                        sql = sql + sql_reference_select_field;
                    }
                    if (crmSession.configValue(crmConstants.ConduitModule) == "Y")
                    {
                        sql = sql + sql_CONDUIT_select_field;
                    }
                }
                else
                {
                    sql = sql_select_Top200fields_NP() + sql_people_select_field; //showing only top 200
                }
                
            }

            sql = sql + " from " + importTable + " where [STATUS] != '''' and rectype = ''D''";

            impData = _importService.read_all_import_data(sql);

            return impData;
        }

        [HttpGet, Route("api/Import/SRCCODEResult/{imptype}/{importTable}")]
        public List<ImportStep5ViewModelSRCCODE> SRCCODEResult(int imptype, string importTable)
        {
            List<ImportStep5ViewModelSRCCODE> impData;
            string sql = sql_SRCCODEFieldsExport;

            sql = sql + " from " + importTable + " where [STATUS] != ''NOT PROCESSED'' and rectype = ''D''";

            impData = _importService.read_all_import_dataSRCCODE(sql);

            return impData;
        }

        [HttpGet, Route("api/Import/PeopleResultJFC/{imptype}/{importTable}")]
        public List<ImportStep5ViewModelJFC> PeopleResultJFC(int imptype, string importTable)
        {
            List<ImportStep5ViewModelJFC> impData;
            importTable = importTable.Contains("!_web_app") ? importTable : "[!_web_app]." + importTable;
            string sql = "";

            sql = sql_JFCselect_Top200fields;

            sql = sql + " from " + importTable;// + " where [STATUS] != '''' and rectype = ''D''";

            impData = _importService.read_all_import_dataJFC(sql);

            return impData;
        }

        [HttpGet, Route("api/Import/TransactionResult/{imptype}/{importTable}")]
        public List<ImportTxnStep5ViewModel> TransactionResult(int imptype, string importTable)
        {
            List<ImportTxnStep5ViewModel> impData;
            string sql = "";

            if (imptype == 30)
            {
                sql = sql_entity_select_fields;
            }
            else if (imptype == 31)
            {
                sql = sql_entity_select_fields + sql_txn_select_fields;
            }

            sql = sql + " from " + importTable + " where [STATUS] != '''' and rectype = ''D''";

            impData = _importService.read_all_txn_import_data(sql);

            return impData;
        }

        [HttpGet, Route("api/Import/TransactionUpdateResult/{imptype}/{importTable}")]
        public List<ImportTxnUpdateStep5ViewModel> TransactionUpdateResult(int imptype, string importTable)
        {
            List<ImportTxnUpdateStep5ViewModel> impData;
            string sql = sql_txnupdate_select_fields;

            sql = sql + " from " + importTable + " where [STATUS] != '''' and rectype = ''D''";

            impData = _importService.read_all_txnupdate_import_data(sql);

            return impData;
        }

        [HttpGet, Route("api/Import/EventResult/{imptype}/{importTable}")]
        public List<ImportEventStep5ViewModel> EventResult(int imptype, string importTable)
        {
            List<ImportEventStep5ViewModel> impData;
            string sql = "";

            if (imptype == 6)
            {
                sql = sql_event_select_fields;
            }

            sql = sql + " from " + importTable + " where [STATUS] != '''' and rectype = ''D''";

            impData = _importService.read_all_event_import_data(sql);

            return impData;
        }

        [HttpGet, Route("api/Import/AttributionResult/{imptype}/{importTable}")]
        public List<ImportAdjStep5ViewModel> AttributionResult(int imptype, string importTable)
        {
            List<ImportAdjStep5ViewModel> impData;
            string sql = "";

            if (imptype == 21 || imptype == 22 || imptype == 23 || imptype == 24)
            {
                sql = sql_adj_select_fields;
            }

            sql = sql + " from " + importTable + " where [STATUS] != '''' and rectype = ''D''";

            impData = _importService.read_all_adj_import_data(sql);

            return impData;
        }

        [HttpGet, Route("api/Import/UpdateResult/{imptype}/{importTable}")]
        public List<ImportUpdateStep5ViewModel> UpdateResult(int imptype, string importTable)
        {
            List<ImportUpdateStep5ViewModel> impData;
            string sql = "";

            if (imptype == 8)
            {
                sql = sql_update_select_fields;
            }

            sql = sql + " from " + importTable + " where [STATUS] != '''' and rectype = ''D''";

            impData = _importService.read_all_update_import_data(sql);

            return impData;
        }

        [HttpGet, Route("api/Import/ActionResult/{imptype}/{importTable}")]
        public List<ImportStep5ViewModelAction> ActionResult(int imptype, string importTable)
        {
            if (imptype == 9)
            {
                List<ImportStep5ViewModelAction> impData;
                string sql = "";
                sql = sql_action_select_fields;
                sql = sql + " from " + importTable + " where [STATUS] != '''' and rectype = ''D''";

                impData = _importService.read_all_action_import_data_by_sql(sql);
                return impData;
            }
            return null;
        }

        [HttpGet, Route("api/Import/CorpMatchResult/{imptype}/{importTable}")]
        public List<ImportCorpMatchStep5ViewModel> CorpMatchResult(int imptype, string importTable)
        {
            List<ImportCorpMatchStep5ViewModel> impData;
            string sql = "";

            if (imptype == 35)
            {
                sql = sql_corpmatch_select_fields;
            }

            sql = sql + " from " + importTable + " where [STATUS] != '''' and rectype = ''D''";

            impData = _importService.read_all_corpmatch_import_data(sql);

            return impData;
        }

        [HttpGet, Route("api/Import/PledgeResult/{imptype}/{importTable}")]
        public List<ImportPledgeStep5ViewModel> PledgeResult(int imptype, string importTable)
        {
            List<ImportPledgeStep5ViewModel> impData;
            string sql = "";

            if (imptype == 38)
            {
                sql = sql_pledge_select_fields;
            }

            sql = sql + " from " + importTable + " where [STATUS] != '''' and rectype = ''D''";

            impData = _importService.read_all_pledge_import_data(sql);

            return impData;
        }

        [HttpGet, Route("api/Import/TributeResult/{imptype}/{importTable}")]
        public List<ImportTributeStep5ViewModel> TributeResult(int imptype, string importTable)
        {
            List<ImportTributeStep5ViewModel> impData;
            string sql = "";

            if (imptype == 39)
            {
                sql = sql_tribute_select_fields;
            }

            sql = sql + " from " + importTable + " where [STATUS] != '''' and rectype = ''D''";

            impData = _importService.read_all_tribute_import_data(sql);

            return impData;
        }

        [HttpGet, Route("api/Import/SupportResult/{imptype}/{importTable}")]
        public List<ImportSupportStep5ViewModel> SupportResult(int imptype, string importTable)
        {
            List<ImportSupportStep5ViewModel> impData;
            string sql = "";

            if (imptype == 40)
            {
                sql = sql_support_select_fields;
            }

            sql = sql + " from " + importTable + " where [STATUS] != '''' and rectype = ''D''";

            impData = _importService.read_all_support_import_data(sql);

            return impData;
        }

        [HttpPost, Route("api/Import/ImportData")]
        public MessageBoard ImportData(ImportTypeData importtypedata)
        {
            MessageBoard msgBoard = new MessageBoard();
            //set initial values
            msgBoard.message = "";
            msgBoard.tempTableName = importtypedata.IMPORTTABLENAME;
            msgBoard.xmlresponse = "";
            msgBoard.impTypeSelection = importtypedata.IMPTYPEID;

            string FUNDCODE;

            try
            {
                //FUNDCODE = _entity_crm.All<dmFUND>().Where(a => a.FUNDID == importtypedata.FUNDID).Single<dmFUND>().FUNDCODE.ToString();

                #region [[ Create sql Statement ]]
                string _sql = "";
                if (importtypedata.IMPTYPEID == 2)
                {
                    //FileName
                    _sql = "EXEC zImportEmail '[!_web_app]." + importtypedata.IMPORTTABLENAME + "','" + importtypedata.UPLOADEDFILENAME + "'";
                }
                else if (importtypedata.IMPTYPEID == 6)
                {
                    //[dbo].[zImportEventData] 
                    //@IMPORTTYPE		VARCHAR(1),
                    //@RECORDS		INTEGER,
                    //@EVNTCODE		VARCHAR(15),
                    //@TABLENAME		VARCHAR(50),
                    //@PROCESSDATE		VARCHAR(50),				-- THIS SHOULD INCLUDE HH:MM:SS:mmm !!!
                    //@MESSAGE		VARCHAR(150) 	OUTPUT,			-- OUTPUT MESSAGE "1 OF 2 RECORDS PROCESSED" & "CHECK FILE FOR ERRORS"
                    //@LOGID			INTEGER		OUTPUT
                    _sql = "DECLARE @Message varchar(100);\n";
                    _sql += "EXEC zImportEventData ";
                    if (importtypedata.ISOVERWRITE == true)
                        _sql += "A";
                    else
                        _sql += "N";

                    _sql += ",0";

                    //_sql += ",'" + GetCode(importtypedata.EVENTCD) + "'";
                    _sql += ",'" + importtypedata.EVENTCD + "'";
                    _sql += ",'[!_web_app]." + importtypedata.IMPORTTABLENAME + "'";
                    //_sql += ",'" + DateTime.Now.ToString("MM/dd/yyyy HH:mm:ss") + "'";
                    _sql += ",'" + DateTime.Now.ToString("MM/dd/yyyy HH:mm:ss.fff") + "'";
                    _sql += ",@Message OUTPUT,null;";
                    _sql += "\n Select @Message as MESSAGE;";
                }
                else if (importtypedata.IMPTYPEID == 21 || importtypedata.IMPTYPEID == 22 || importtypedata.IMPTYPEID == 23 || importtypedata.IMPTYPEID == 24)
                {
                    //[dbo].[zImportMonyAdjustment_v5]
                    //@IMPORTTYPE		VARCHAR(1),
                    //@ORIGMID		    INTEGER,
                    //@TABLENAME		VARCHAR(50),
                    //@PROCESSDATE	    VARCHAR(50),			-- THIS SHOULD INCLUDE HH:MM:SS:mmm !!!
                    //@MESSAGE		    VARCHAR(150) OUTPUT	,	-- OUTPUT MESSAGE "1 OF 2 RECORDS PROCESSED" & "CHECK FILE FOR ERRORS"
                    //@LOGID			INTEGER,			    -- ssIMPORTLOG ID
                    //@IMPTYPECD		VARCHAR(1)		        -- P - Partnership 
                    string ImportCode = "";
                    if (importtypedata.IMPTYPEID.Equals(22))
                    {
                        ImportCode = "P";
                    }
                    else if (importtypedata.IMPTYPEID.Equals(23))
                    {
                        ImportCode = "V";
                    }
                    else if (importtypedata.IMPTYPEID.Equals(24))
                    {
                        ImportCode = "W";
                    }
                    else
                    {
                        ImportCode = "A";
                    }

                    _sql = "DECLARE @Message varchar(100);\n";
                    _sql += "EXEC zImportMonyAdjustment_v5 ";
                    if (importtypedata.ISOVERWRITE == true)
                        _sql += "'A'";
                    else
                        _sql += "'N'";
                    _sql += "," + importtypedata.ORIGMID;
                    _sql += ",'[!_web_app]." + importtypedata.IMPORTTABLENAME + "'";
                    _sql += ",'" + DateTime.Now.ToString("MM/dd/yyyy HH:mm:ss.fff") + "'";
                    _sql += ",@Message OUTPUT";
                    _sql += "," + 0;
                    _sql += "," + ImportCode + ";";
                    _sql += "\n SELECT @Message as MESSAGE;";
                }
                else if (importtypedata.IMPTYPEID == 30 || importtypedata.IMPTYPEID == 31)
                {
                    //[dbo].[zImportExpenditureData]
                    //               @IMPORTTYPE		VARCHAR(1),	
                    //@TABLENAME		VARCHAR(50),
                    //@PROCESSDATE	VARCHAR(50),				-- THIS SHOULD INCLUDE HH:MM:SS:mmm !!!
                    //@TRANSTYPE		VARCHAR(1),
                    //@FUNDCODE		VARCHAR(5),
                    //@BANKACCT       VARCHAR(10),
                    //@LINENO			VARCHAR(8),
                    //@UID			INT,
                    //@MESSAGE		VARCHAR(150) OUTPUT			-- OUTPUT MESSAGE "1 OF 2 RECORDS PROCESSED" & "CHECK FILE FOR ERRORS"	
                    if (importtypedata.FUNDID == 0)
                    {
                        FUNDCODE = null;
                    }
                    else
                    {
                        FUNDCODE = _entity_crm.All<dmFUND>().Where(a => a.FUNDID == importtypedata.FUNDID).Single<dmFUND>().FUNDCODE.ToString();
                    }
                    
                    string CENTERCODE = _entity_crm.All<dmCENTER>().Where(a => a.CENTERID == importtypedata.CENTERID).Single<dmCENTER>().CENTERCODE.ToString();
                    string ExpImportCode = "";
                    if (importtypedata.IMPTYPEID.Equals(30))
                    {
                        ExpImportCode = "E";
                    }
                    else if (importtypedata.IMPTYPEID.Equals(31))
                    {
                        ExpImportCode = "T";
                    }

                    _sql = "DECLARE @Message varchar(100);\n";
                    _sql += "EXEC zImportExpenditureData";
                    _sql += "'" + ExpImportCode + "'";
                    _sql += ",'[!_web_app]." + importtypedata.IMPORTTABLENAME + "'";
                    _sql += ",'" + DateTime.Now.ToString("MM/dd/yyyy HH:mm:ss.fff") + "'";
                    _sql += ",'" + importtypedata.TXNTYPE + "'";
                    //_sql += ",'" + (importtypedata.FUNDCODE != null ? importtypedata.FUNDCODE : "null") + "'";
                    _sql += ",'" + (FUNDCODE != null ? FUNDCODE : "null") + "'";
                    //_sql += ",'" + (importtypedata.CENTERCODE != null ? importtypedata.CENTERCODE : "null") + "'";
                    _sql += ",'" + (CENTERCODE != null ? CENTERCODE : "null") + "'";
                    _sql += ",'" + (importtypedata.LINE != null ? importtypedata.LINE : "null") + "'";
                    _sql += ",'" + crmSession.UID() + "'";
                    _sql += ",@Message OUTPUT;";
                    _sql += "\n SELECT @Message as MESSAGE;";
                }
                else if (importtypedata.IMPTYPEID == 32)
                {
                    //[dbo].[zImportSRCcode]
                    //               @IMPORTTYPE		VARCHAR(1),	
                    //@TABLENAME		VARCHAR(50),
                    //@PROCESSDATE	VARCHAR(50),				-- THIS SHOULD INCLUDE HH:MM:SS:mmm !!!
                    //@PKGEID		INT,
                    //@UID			INT,
                    //@MESSAGE		VARCHAR(150) OUTPUT			-- OUTPUT MESSAGE "1 OF 2 RECORDS PROCESSED" & "CHECK FILE FOR ERRORS"	
                    string SRCIMPORTTYPE = "N";
                    if (importtypedata.OVERWRITESRCCODE) SRCIMPORTTYPE = "O";
                    if (importtypedata.OVERWRITESRCCODE2) SRCIMPORTTYPE = "I";
                    _sql = "DECLARE @Message varchar(100);\n";
                    _sql += "EXEC zImportSRCcode";
                    _sql += "'" + SRCIMPORTTYPE + "'";
                    _sql += ",'[!_web_app]." + importtypedata.IMPORTTABLENAME + "'";
                    _sql += ",'" + DateTime.Now.ToString("MM/dd/yyyy HH:mm:ss.fff") + "'";
                    _sql += "," + importtypedata.PKGEID + " ";
                    _sql += ",'" + crmSession.UID() + "'";
                    _sql += ",@Message OUTPUT;";
                    _sql += "\n SELECT @Message as MESSAGE;";
                }
                else if (importtypedata.IMPTYPEID == 35)
                {
                    _sql = "DECLARE @Message varchar(100);\n";
                    _sql += "EXEC crmImportCorpMatchData ";
                    if (importtypedata.ISOVERWRITE == true)
                        _sql += "A";
                    else
                        _sql += "N";
                    _sql += ",'" + crmSession.UID() + "'";
                    _sql += ",'[!_web_app]." + importtypedata.IMPORTTABLENAME + "'";
                    _sql += ",'" + DateTime.Now.ToString("MM/dd/yyyy HH:mm:ss.fff") + "'";
                    _sql += ",@Message OUTPUT,null;";
                    _sql += "\n Select @Message as MESSAGE;";
                }
                else if (importtypedata.IMPTYPEID == 36)
                {
                    _sql = "DECLARE @MSG varchar(100);\n";
                    _sql += "EXEC [iLoadTally] ";                    
                    _sql += "'[!_web_app]." + importtypedata.IMPORTTABLENAME + "'";
                    _sql += ",'" + importtypedata.MonyType + "'";
                    _sql += ",@MSG OUTPUT;";
                    _sql += "\n Select @MSG as MESSAGE;";
                }
                else if (importtypedata.IMPTYPEID == 37)
                {
                    //[dbo].[zImportTxnUpdate]
                    //@IMPORTTYPE		VARCHAR(1),	
                    //@TABLENAME		VARCHAR(50),
                    //@PROCESSDATE	VARCHAR(50),				-- THIS SHOULD INCLUDE HH:MM:SS:mmm !!!
                    //@TRANSTYPE		VARCHAR(1),
                    //@UID			INT,
                    //@MESSAGE		VARCHAR(150) OUTPUT			-- OUTPUT MESSAGE "1 OF 2 RECORDS PROCESSED" & "CHECK FILE FOR ERRORS"	

                    string ExpImportCode = "U";

                    _sql = "DECLARE @Message varchar(100);\n";
                    _sql += "EXEC zImportTxnUpdate";
                    _sql += "'" + ExpImportCode + "'";
                    _sql += ",'[!_web_app]." + importtypedata.IMPORTTABLENAME + "'";
                    _sql += ",'" + DateTime.Now.ToString("MM/dd/yyyy HH:mm:ss.fff") + "'";
                    _sql += ",'" + importtypedata.TXNTYPE + "'";
                    _sql += ",'" + crmSession.UID() + "'";
                    _sql += ",@Message OUTPUT;";
                    _sql += "\n SELECT @Message as MESSAGE;";
                }
                else if (importtypedata.IMPTYPEID == 38)
                {
                    _sql = "DECLARE @Message varchar(100);\n";
                    _sql += "EXEC crmImportPledgeData ";
                    if (importtypedata.ISOVERWRITE == true)
                        _sql += "A";
                    else
                        _sql += "N";
                    _sql += ",'" + crmSession.UID() + "'";
                    _sql += ",'[!_web_app]." + importtypedata.IMPORTTABLENAME + "'";
                    _sql += ",'" + DateTime.Now.ToString("MM/dd/yyyy HH:mm:ss.fff") + "'";
                    _sql += ",@Message OUTPUT,null;";
                    _sql += "\n Select @Message as MESSAGE;";
                }
                else if (importtypedata.IMPTYPEID == 39)
                {
                    _sql = "DECLARE @Message varchar(100);\n";
                    _sql += "EXEC crmImportHonMemData ";
                    if (importtypedata.ISOVERWRITE == true)
                        _sql += "A";
                    else
                        _sql += "N";
                    _sql += ",'" + crmSession.UID() + "'";
                    _sql += ",'[!_web_app]." + importtypedata.IMPORTTABLENAME + "'";
                    _sql += ",'" + DateTime.Now.ToString("MM/dd/yyyy HH:mm:ss.fff") + "'";
                    _sql += ",@Message OUTPUT,null;";
                    _sql += "\n Select @Message as MESSAGE;";
                }
                else if (importtypedata.IMPTYPEID == 40)
                {
                    _sql = "DECLARE @Message varchar(100);\n";
                    _sql += "EXEC zImportMonyAttribAdjust ";
                    _sql += " '[!_web_app]." + importtypedata.IMPORTTABLENAME + "'";
                    _sql += ",'SU'," + crmSession.UID() ;
                    _sql += ",@Message OUTPUT;";
                    _sql += "\n Select @Message as MESSAGE;";
                }
                else
                {
                    _sql = "DECLARE @Message varchar(100);\n";
                    _sql += "EXEC crmImportData ";
                    if (importtypedata.IMPTYPEID == 0)
                        _sql += "'G'";
                    else if ((importtypedata.IMPTYPEID == 1 && importtypedata.PEOCODEID == 5) || importtypedata.IMPTYPEID == 4)
                        _sql += "'D'";
                    else if (importtypedata.IMPTYPEID == 1 && importtypedata.PEOCODEID == 2)
                        _sql += "'V'";
                    else if (importtypedata.IMPTYPEID == 3 || importtypedata.IMPTYPEID == 5)
                    {
                        if (importtypedata.ISOVERWRITE == true)
                            _sql += "A";
                        else
                            _sql += "N";
                    }
                    else if (importtypedata.IMPTYPEID == 9)
                        _sql += "'C'";
                    else if (importtypedata.IMPTYPEID == 8)
                        _sql += "'$'";
                    if (importtypedata.IMPTYPEID == 0)
                    {
                        FUNDCODE = _entity_crm.All<dmFUND>().Where(a => a.FUNDID == importtypedata.FUNDID).Single<dmFUND>().FUNDCODE.ToString();
                        _sql += ",'" + FUNDCODE + "'" +
                                ",'" + importtypedata.BATCHNO + "'" +
                                ",'" + importtypedata.BATCHDATE.ToShortDateString() + "'" +
                                "," + importtypedata.NOOFGIFTS +
                                "," + importtypedata.TOTALAMOUNT;
                    }
                    else if (importtypedata.IMPTYPEID == 4 || importtypedata.IMPTYPEID == 5)
                    {
                        _sql += ",null ,null" +
                                ",'" + importtypedata.INTERACTIONDATE + "'" +
                                ",null,null";
                    }
                    else
                    {
                        _sql += ",null,null,null,null,null";
                    }

                    _sql += ",'[!_web_app]." + importtypedata.IMPORTTABLENAME + "'";
                    //_sql += ",'" + DateTime.Now.ToString("MM/dd/yyyy HH:mm:ss") + "'";
                    _sql += ",'" + DateTime.Now.ToString("MM/dd/yyyy HH:mm:ss.fff") + "'";
                    _sql += ",@Message OUTPUT,null;";
                    _sql += "\n Select @Message as MESSAGE;";

                }
                #endregion
                
                cl_query.create_a_sqlLog(_sql.Trim(),"Import Module(Before Import Step)",msgBoard.tempTableName,crmSession.UID().Value,0,"","",_entity_crm, 0);

                System.Diagnostics.Stopwatch stopWatch = new System.Diagnostics.Stopwatch();
                stopWatch.Start();

                // Execute
                if (_sql != String.Empty)
                {
                    msgBoard = _importService.process_sql(_sql, "Process", msgBoard);
                }

                stopWatch.Stop();
                #region [[ Save the SQL in Log Table ]]

                cl_query.create_a_sqlLog(
                           _sql.Trim(),
                           "Import Module(Import Step)",
                           msgBoard.tempTableName,
                           crmSession.UID().Value,
                           0,
                           "",
                           "",
                           _entity_crm,
                           Convert.ToSingle(stopWatch.ElapsedMilliseconds / 1000.0));

                #endregion

                if (msgBoard.status && !string.IsNullOrEmpty(msgBoard.xmlresponse))
                {
                    //Let us parse the response
                    System.IO.TextReader tr = new System.IO.StringReader(msgBoard.xmlresponse);
                    XDocument oDoc = XDocument.Load(tr);

                    var _elem = (from a in oDoc.Descendants("MESSAGE")
                                 select a).FirstOrDefault();
                    if (_elem != null)
                    {
                        msgBoard.message = _elem.Value;
                        msgBoard.status = true;
                        msgBoard.xmlresponse = "NoError";
                    }
                    else
                    {
                        msgBoard.status = false;
                        msgBoard.xmlresponse = "Error";
                        msgBoard.message = "There is a problem in Importing the data (Code: 730). Please try again later.";
                    }
                }
                else
                {
                    throw new System.ApplicationException("\r\nPlease try again after all errors are fixed.");
                }
            }
            catch (System.Exception ex)
            {
                msgBoard.message = "There is some problem in processing Import (ImportData step : Code 763) request. Please try again later. Details: " + ex.Message;
                msgBoard.status = false;
            }

            return msgBoard;
        }

        [HttpPost, Route("api/Import/ImportQueue")]
        public MessageBoard ImportQueue(ImportTypeData importtypedata)
        {
            MessageBoard msgBoard = new MessageBoard();
            //set initial values
            msgBoard.message = "";
            msgBoard.tempTableName = importtypedata.IMPORTTABLENAME;
            msgBoard.xmlresponse = "";
            msgBoard.impTypeSelection = importtypedata.IMPTYPEID;

            string sqlText = "";
            try
            {

                #region [[ Create sqlText Statement ]]

                if (importtypedata.IMPTYPEID == 2)
                {
                    sqlText += "EXEC zImportEmail ";
                    sqlText += "'[!_web_app].[" + importtypedata.IMPORTTABLENAME + "]'";
                    sqlText += ", '" + importtypedata.UPLOADEDFILENAME + "'";
                }
                else if (importtypedata.IMPTYPEID == 6)
                {
                    sqlText += "EXEC zImportEventData ";
                    if (importtypedata.ISOVERWRITE == true)
                    {
                        sqlText += "'A'";
                    }
                    else
                    {
                        sqlText += "'N'";
                    }
                    sqlText += ", '0'";
                    sqlText += ", '" + importtypedata.EVENTCD + "'";
                    sqlText += ", '[!_web_app].[" + importtypedata.IMPORTTABLENAME + "]'";
                    //sqlText += ", '" + DateTime.Now.ToString("MM/dd/yyyy HH:mm:ss.fff") + "'";
                    sqlText += ", '.:|:.'";
                    sqlText += ", @MESSAGE OUTPUT, @LOGID OUTPUT";
                }
                else if (importtypedata.IMPTYPEID == 30 || importtypedata.IMPTYPEID == 31)
                {
                    string FUNDCODE = importtypedata.IMPTYPEID == 30 ? null : _entity_crm.All<dmFUND>().Where(a => a.FUNDID == importtypedata.FUNDID).Single<dmFUND>().FUNDCODE.ToString();
                    string CENTERCODE = importtypedata.IMPTYPEID == 30 ? null : _entity_crm.All<dmCENTER>().Where(a => a.CENTERID == importtypedata.CENTERID).Single<dmCENTER>().CENTERCODE.ToString();
                    string ExpImportCode = "";
                    if (importtypedata.IMPTYPEID.Equals(30))
                    {
                        ExpImportCode = "E";
                    }
                    else if (importtypedata.IMPTYPEID.Equals(31))
                    {
                        ExpImportCode = "T";
                    }
                    sqlText += "EXEC zImportExpenditureData ";
                    sqlText += "'" + ExpImportCode + "'";
                    sqlText += ",'[!_web_app].[" + importtypedata.IMPORTTABLENAME + "]'";
                    sqlText += ",'.:|:.'";
                    sqlText += ",'" + importtypedata.TXNTYPE + "'";
                    sqlText += ",'" + (FUNDCODE != null ? FUNDCODE : "null") + "'";
                    sqlText += ",'" + (CENTERCODE != null ? CENTERCODE : "null") + "'";
                    sqlText += ",'" + (importtypedata.LINE != null ? importtypedata.LINE : "null") + "'";
                    sqlText += "," + crmSession.UID();
                    sqlText += ", @MESSAGE OUTPUT";
                }
                else if (importtypedata.IMPTYPEID == 21 || importtypedata.IMPTYPEID == 22 || importtypedata.IMPTYPEID == 23 || importtypedata.IMPTYPEID == 24)
                {
                    string ImportCode = "";
                    if (importtypedata.IMPTYPEID.Equals(22))
                    {
                        ImportCode = "P";
                    }
                    else if (importtypedata.IMPTYPEID.Equals(23))
                    {
                        ImportCode = "V";
                    }
                    else if (importtypedata.IMPTYPEID.Equals(24))
                    {
                        ImportCode = "W";
                    }
                    else
                    {
                        ImportCode = "A";
                    }
                    sqlText += "EXEC zImportMonyAdjustment_v5 ";
                    if (importtypedata.ISOVERWRITE == true)
                    {
                        sqlText += "'A'";
                    }
                    else
                    {
                        sqlText += "'N'";
                    }
                    sqlText += "," + importtypedata.ORIGMID;
                    sqlText += ",'[!_web_app].[" + importtypedata.IMPORTTABLENAME + "]'";
                    sqlText += ",'.:|:.'";
                    sqlText += ", @MESSAGE OUTPUT";
                    sqlText += "," + 0;
                    sqlText += ",'" + ImportCode + "'";
                }
                else
                {
                    sqlText += "EXEC crmImportData ";
                    if (importtypedata.IMPTYPEID == 0)
                        sqlText += "'G'";
                    else if ((importtypedata.IMPTYPEID == 1 && importtypedata.PEOCODEID == 5) || importtypedata.IMPTYPEID == 4)
                        sqlText += "'D'";
                    else if (importtypedata.IMPTYPEID == 1 && importtypedata.PEOCODEID == 2)
                        sqlText += "'V'";
                    else if (importtypedata.IMPTYPEID == 3 || importtypedata.IMPTYPEID == 5)
                    {
                        if (importtypedata.ISOVERWRITE == true)
                            sqlText += "'A'";
                        else
                            sqlText += "'N'";
                    }
                    else if (importtypedata.IMPTYPEID == 9)
                        sqlText += "'C'";
                    else if (importtypedata.IMPTYPEID == 8)
                        sqlText += "'$'";
                    if (importtypedata.IMPTYPEID == 0)
                    {
                        string FUNDCODE = _entity_crm.All<dmFUND>().Where(a => a.FUNDID == importtypedata.FUNDID).Single<dmFUND>().FUNDCODE.ToString();
                        sqlText += ", '" + FUNDCODE + "'";
                        sqlText += ", '" + importtypedata.BATCHNO + "'";
                        sqlText += ", '" + importtypedata.BATCHDATE.ToShortDateString() + "'";
                        sqlText += ", '" + importtypedata.NOOFGIFTS + "'";
                        sqlText += ", '" + importtypedata.TOTALAMOUNT + "'";
                    }
                    else if (importtypedata.IMPTYPEID == 4 || importtypedata.IMPTYPEID == 5)
                    {
                        sqlText += ", null";
                        sqlText += ", null";
                        sqlText += ", '" + importtypedata.INTERACTIONDATE + "'";
                        sqlText += ", null";
                        sqlText += ", null";
                    }
                    else
                    {
                        sqlText += ", null";
                        sqlText += ", null";
                        sqlText += ", null";
                        sqlText += ", null";
                        sqlText += ", null";
                    }

                    sqlText += ", '[!_web_app].[" + importtypedata.IMPORTTABLENAME + "]'";
                    sqlText += ", '.:|:.'";
                    sqlText += ", @MESSAGE OUTPUT, @LOGID OUTPUT";

                }

                #endregion

                DateTime? _starton = null;
                if (importtypedata.QUEUESTART != null)
                {
                    DateTime _time = (DateTime)importtypedata.QUEUESTART;
                    _starton = new DateTime(DateTime.Today.Year, DateTime.Today.Month, DateTime.Today.Day, _time.Hour, _time.Minute, _time.Second);
                }

                crmIMPORTQUE crmImportQueue = new crmIMPORTQUE();
                crmImportQueue.UID = crmSession.UID();
                crmImportQueue.QUEDATE = DateTime.Now;
                crmImportQueue.TABLENAME = "[!_web_app].[" + importtypedata.IMPORTTABLENAME + "]";
                crmImportQueue.SQLTEXT = sqlText;
                crmImportQueue.IMPTYPEID = importtypedata.IMPTYPEID;
                crmImportQueue.QUETYPEID = 4; /* to queue in JOB table */
                crmImportQueue.PROCESSDATE = _starton;
                _entity_crm.Add(crmImportQueue);
                _entity_crm.CommitChanges();

                #region [[ queue import in JOB table ]]
                JOB _job = new JOB();
                _job.JOBTYPEID = 7;
                _job.UID = crmSession.UID();
                _job.SCRIPT = String.Format("EXEC dbo.crmImportDataFromQueById {0}", crmImportQueue.crmIMPORTQUEID);
                _job.JOBSTATID = 1;
                _job.QUEUEDON = DateTime.Now;
                _job.NOTIFYUSERID = @session.userSession.UserName;
                _job.QUEUED = 1;
                _job.DESCRIP = "Import Queue";
                _job.STARTON = _starton;
                _entity_crm.Add(_job);
                _entity_crm.CommitChanges();
                #endregion

                msgBoard.message = "The import has been queued succesfully.";
                msgBoard.status = true;
                msgBoard.xmlresponse = "NoError";

                /* Need to keep until Management decides email is not necessary
                try
                {
                    //Check in jtSPCEVNT Table
                    string sql = string.Format("SELECT Count(*) as recordCount FROM {0}", "[!_web_app].[" + importtypedata.IMPORTTABLENAME + "]");
                    int recCount = _importService.getCountFromTempTable(sql);
                    
                    #region [[ Let us send email to Crimson Staff and Jeremy so they know it is scheduled ]]
                    //Process to send Message
                    System.Net.Mail.MailMessage oMsg = new System.Net.Mail.MailMessage();
                    //From
                    oMsg.From = new System.Net.Mail.MailAddress("<EMAIL>");
                    //To
                    //oMsg.To.Add("<EMAIL>");
                    //oMsg.To.Add("<EMAIL>");
                    oMsg.To.Add("<EMAIL>");

                    //CC notiEmail_newUser_to
                    //oMsg.CC.Add(System.Configuration.ConfigurationManager.AppSettings["notiEmail_newUser_to"]);
                    //Email
                    oMsg.Subject = "Queue Import Scheduled";
                    //Body with Created By Information
                    oMsg.Body = string.Format("Queue Import Scheduled By: {0}\r\nProject: {1}\r\nUsername: {2} ({3})\r\nFirst Name: {4}\r\nLast Name: {5}\r\nTemp Table Name: {6}\r\nServer: {7}\r\nDatabase Name: {8}\r\nRecord Count: {9}\r\n",
                        session.userSession.FName + " " + session.userSession.LName,
                        session.currentDomain_project.name, session.userSession.UserName,
                        session.userSession.Email, session.userSession.FName, 
                        session.userSession.LName, importtypedata.IMPORTTABLENAME,
                        session.currentDomain_project.db_datasource, session.currentDomain_project.db_initialcatalog, recCount-1);
                                        
                    //process
                    oMsg.Body = HttpContext.Current.Server.HtmlEncode(oMsg.Body.Trim()).Replace("\r\n", "<br/>").Replace(" ", "&nbsp;");
                    //Prepare before sending
                    oMsg.IsBodyHtml = true;
                    System.Net.Mail.SmtpClient smtp = new System.Net.Mail.SmtpClient();// ("CMDI60");
                    smtp.Timeout = 30000000;
                    //Sent the Email...
                    smtp.Send(oMsg);
                    #endregion
                }
                catch
                {
                    //Email will not be sent but user can continue
                }
                */
            }
            catch (System.Exception ex)
            {
                msgBoard.message = "There is some problem in queing Import. Please try again later. Details: " + ex.Message;
                msgBoard.status = false;
            }

            return msgBoard;
        }

        [HttpPost, Route("crm/api/Import/ExportResultKey")]
        public string ExportResultKey(string searchText)
        {
            string key_ = System.Guid.NewGuid().ToString();
            HttpRuntime.Cache.Insert(key_, searchText, null, DateTime.UtcNow.Add(new TimeSpan(1, 0, 1)), System.Web.Caching.Cache.NoSlidingExpiration);

            return key_;

        }

        [HttpGet, Route("crm/api/Import/ExportResult")]
        public string ExportResult(string key)
        {
            string param = (string)HttpRuntime.Cache[key];

            //0 - Import file name
            //1 - import type id
            string[] values = param.Split(new char[] { '$' });
            int importTypeId = Int32.Parse(values[1]);
            string sToday = String.Format("{0:yyyyMMMdd__hh_mm_ss tt}", DateTime.Now);
            string moduleName = "IMPORT";
            string fileName = moduleName + "_" + System.Guid.NewGuid() + "_" + _userSession.UserName + "_" + sToday;

            DataSet ds = null;
            string sql = "";

            string select_sql = "";
            //SQL select statement
            if (importTypeId >= 4 && importTypeId != 6 && importTypeId != 21 && importTypeId != 22 && importTypeId != 23 && importTypeId != 24
                && importTypeId != 30 && importTypeId != 31 && importTypeId != 35 && importTypeId != 37
                 && importTypeId != 32 && importTypeId != 99 && importTypeId != 8 && importTypeId != 9 && importTypeId != 38 && importTypeId != 39 && importTypeId != 40)
            {
                select_sql = sql_select_fields_NP() + sql_intrn_select_fields;
            }
            else if (importTypeId == 6)
            {
                select_sql = sql_event_select_fields;
            }
            else if (importTypeId == 21 || importTypeId == 22 || importTypeId == 23 || importTypeId == 24)
            {
                select_sql = sql_adj_select_fields;
            }
            else if (importTypeId == 30)
            {
                select_sql = sql_entity_select_fields;
            }
            else if (importTypeId == 31)
            {
                select_sql = sql_entity_select_fields + sql_txn_select_fields;
            }
            else if (importTypeId == 32)
            {
                select_sql = sql_SRCCODEFieldsExport;
            }
            else if (importTypeId == 99)
            {
                select_sql = sql_JFCselect_Top200fields;
            }
            else if (importTypeId == 8)
            {
                select_sql = sql_update_select_fields;
            }
            else if (importTypeId == 9)
            {
                select_sql = sql_action_select_fields;
            }
            else if (importTypeId == 35)
            {
                select_sql = sql_corpmatch_select_fields;
            }
            else if (importTypeId == 37)
            {
                select_sql = sql_txnupdate_select_fields;
            }
            else if (importTypeId == 38)
            {
                select_sql = sql_pledge_select_fields;
            }
            else if (importTypeId == 39)
            {
                select_sql = sql_tribute_select_fields;
            }
            else if (importTypeId == 40)
            {
                select_sql = sql_support_select_fields;
            }
            else
            {
                if (importTypeId == 0)//new gift
                {
                    select_sql = sql_select_fields_NP() + sql_money_select_field;
                    if (crmSession.configValue(crmConstants.Money_custom_Reference_Id) == "Y")
                    {
                        select_sql = select_sql + sql_reference_select_field;
                    }
                    if (crmSession.configValue(crmConstants.ConduitModule) == "Y")
                    {
                        select_sql = select_sql + sql_CONDUIT_select_field;
                    }
                }
                else
                {
                    select_sql = sql_select_fields_NP() + sql_people_select_field; //showing only top 200
                }
            }

            if (importTypeId == 99)
            {
                sql = select_sql + " FROM " + values[0];
            }
            else
            {
                sql = select_sql + " FROM " + values[0] + " WHERE RECTYPE = ''D'' " + " ORDER BY RECTYPE DESC, STATUS ";
            }

            //replace '' with '
            sql = sql.Replace("''", "'");

            cl_query.create_a_sqlLog(sql.Trim(), "Import Module(Before Export Step)", values[0].ToString(), crmSession.UID().Value, 0, "", "", _entity_crm, 0);
            
            System.Diagnostics.Stopwatch stopWatch = new System.Diagnostics.Stopwatch();
            stopWatch.Start();

            ds = _reportService.get_dataset_w_sql__single(sql, "Import");

            stopWatch.Stop();
            #region [[ Save the SQL in Log Table ]]

            cl_query.create_a_sqlLog(
                       sql.Trim(),
                       "Import Module(Export Step)",
                       values[0].ToString(),
                       crmSession.UID().Value,
                       0,
                       "",
                       "",
                       _entity_crm,
                       Convert.ToSingle(stopWatch.ElapsedMilliseconds / 1000.0));

            #endregion

            if (ds != null)
            {
                //CreateExcelFile.CreateExcelDocument(ds.Copy(), fileName, HttpContext.Current.Response);
                string key_ = System.Guid.NewGuid().ToString();
                HttpRuntime.Cache.Insert(key_, ds, null, DateTime.UtcNow.Add(new TimeSpan(1, 0, 1)), System.Web.Caching.Cache.NoSlidingExpiration);
                //return the Key
                return key_;

            }
            else
            {
                return null;
            }
        }

        #region [[[ mass adjustment export ]]
        [HttpPost, Route("crm/api/Import/ExportMassAdjustmentKey")]
        public string ExportMassAdjustmentKey(string searchText)
        {
            string key_ = System.Guid.NewGuid().ToString();
            HttpRuntime.Cache.Insert(key_, searchText, null, DateTime.UtcNow.Add(new TimeSpan(1, 0, 1)), System.Web.Caching.Cache.NoSlidingExpiration);

            return key_;

        }

        [HttpGet, Route("crm/api/Import/ExportMassAdjustment")]
        public string ExportMassAdjustment(string key)
        {
            string param = (string)HttpRuntime.Cache[key];

            //0 - Import file name
            //1 - filter
            string[] values = param.Split(new char[] { '$' });
            string sToday = String.Format("{0:yyyyMMMdd__hh_mm_ss tt}", DateTime.Now);
            string moduleName = "MassAdjustment";
            string fileName = moduleName + "_" + System.Guid.NewGuid() + "_" + _userSession.UserName + "_" + sToday;

            DataSet ds = null;
            string sql = "";
            //SQL select statement
            if (values[1] == "Matched")
            {
                sql = string.Format(sql_select_fields_massAdjustmentResult, values[0]);
                sql += " WHERE Z.STATUS='MATCHED' AND Z.RECTYPE = 'D'";
            }
            else if(values[1] == "Adjusted")
            {
                sql = string.Format(sql_select_fields_massAdjustmentResult, values[0]);
                sql += " WHERE Z.STATUS='ALREADY ADJUSTED' AND Z.RECTYPE = 'D'";
            }
            else if (values[1] == "UnMatched")
            {
                sql = string.Format(sql_select_fields_massAdjustmentResultUnmatched, values[0]);
            }
            else if (values[1] == "Processed")
            {
                sql = string.Format(sql_select_fields_massAdjustmentResultProcessed, values[0]);
            }
            //replace '' with '
            sql = sql.Replace("''", "'");

            cl_query.create_a_sqlLog(sql.Trim(), "Import Module(Before Export Step)", values[0].ToString(), crmSession.UID().Value, 0, "", "", _entity_crm, 0);

            System.Diagnostics.Stopwatch stopWatch = new System.Diagnostics.Stopwatch();
            stopWatch.Start();

            ds = _reportService.get_dataset_w_sql__single(sql, "Import");

            stopWatch.Stop();
            #region [[ Save the SQL in Log Table ]]

            cl_query.create_a_sqlLog(
                       sql.Trim(),
                       "Import Module(Export Step)",
                       values[0].ToString(),
                       crmSession.UID().Value,
                       0,
                       "",
                       "",
                       _entity_crm,
                       Convert.ToSingle(stopWatch.ElapsedMilliseconds / 1000.0));

            #endregion

            if (ds != null)
            {
                //CreateExcelFile.CreateExcelDocument(ds.Copy(), fileName, HttpContext.Current.Response);
                string key_ = System.Guid.NewGuid().ToString();
                HttpRuntime.Cache.Insert(key_, ds, null, DateTime.UtcNow.Add(new TimeSpan(1, 0, 1)), System.Web.Caching.Cache.NoSlidingExpiration);
                //return the Key
                return key_;

            }
            else
            {
                return null;
            }
        }
        #endregion

        private MessageBoard UIvalidation(ImportTypeData importtypedata)
        {
            MessageBoard msgBoard = new MessageBoard();
            msgBoard.status = true;

            try
            {
                if (importtypedata.IMPTYPEID == 0)
                {
                    #region [[ New Gifts ]]

                    if (importtypedata.FUNDID == 0)
                    {
                        msgBoard.message = "Step2: Import Type - Fund must be selected!";
                        msgBoard.status = false;
                    }

                    if (string.IsNullOrEmpty(importtypedata.BATCHNO))
                    {
                        msgBoard.message = msgBoard.message + "\nStep2: Import Type - Default Batch# must be entered!";
                        msgBoard.status = false;
                    }


                    if (importtypedata.BATCHDATE == default(System.DateTime))
                    {
                        msgBoard.message = msgBoard.message + "\nStep2: Import Type - Default Batch Date must be entered!";
                        msgBoard.status = false;
                    }

                    if (importtypedata.NOOFGIFTS == 0)
                    {
                        msgBoard.message = msgBoard.message + "\nStep2: Import Type - # of Gifts must be entered!";
                        msgBoard.status = false;
                    }

                    if (importtypedata.TOTALAMOUNT == 0)
                    {
                        msgBoard.message = msgBoard.message + "\nStep2: Import Type - Total Amount must be entered!";
                        msgBoard.status = false;
                    }

                    // Check if Source, Amt and Payment Type are mapped
                    if (RequiredMapping(importtypedata.MAPPING, "Source Code") == false)
                    {
                        msgBoard.message = msgBoard.message + "\nStep3: Map Fields - Source Code must be mapped!";
                        msgBoard.status = false;
                    }

                    if (RequiredMapping(importtypedata.MAPPING, "Gift Amount") == false)
                    {
                        msgBoard.message = msgBoard.message + "\nStep3: Map Fields - Gift Amount must be mapped!";
                        msgBoard.status = false;
                    }

                    if (RequiredMapping(importtypedata.MAPPING, "Payment Type") == false)
                    {
                        msgBoard.message = msgBoard.message + "\nStep3: Map Fields - Payment type must be mapped!";
                        msgBoard.status = false;
                    }

                    #endregion
                }

                if (importtypedata.IMPTYPEID == 1)
                {
                    #region [[ New Contact ]]

                    if (importtypedata.PEOCODEID == 0)
                    {
                        msgBoard.message = "Step2: Import Type - People Code must be selected!";
                        msgBoard.status = false;
                    }

                    #endregion
                }

                if (importtypedata.IMPTYPEID == 3)
                {
                    #region [[ Append to Existing Records ]]

                    if (RequiredMapping(importtypedata.MAPPING, "ID") == false)
                    {
                        msgBoard.message = "Step3: Map Fields - ID must be mapped!";
                        msgBoard.status = false;
                    }

                    #endregion
                }

                if (importtypedata.IMPTYPEID == 4 || importtypedata.IMPTYPEID == 5)
                {
                    #region [[ Interactions ]]

                    if (importtypedata.FUNDID == 0)
                    {
                        msgBoard.message = "Step2: Import Type - Fund must be selected!";
                        msgBoard.status = false;
                    }

                    if (importtypedata.INTERACTIONDATE == default(System.DateTime))
                    {
                        msgBoard.message = msgBoard.message + "\nStep2: Import Type - Interaction Batch Date must be entered!";
                        msgBoard.status = false;
                    }

                    if (importtypedata.CHANNELID == 0)
                    {
                        msgBoard.message = "\nStep2: Import Type - Channel must be selected!";
                        msgBoard.status = false;
                    }

                    if (importtypedata.SOURCECD == null)
                    {
                        msgBoard.message = "\nStep2: Import Type - Default Source Code must be selected!";
                        msgBoard.status = false;
                    }

                    if (RequiredMapping(importtypedata.MAPPING, "ID") == false)
                    {
                        msgBoard.message = "\nStep3: Map Fields - ID must be mapped!";
                        msgBoard.status = false;
                    }


                    #endregion
                }

                if (importtypedata.IMPTYPEID == 6)
                {
                    #region [[ Event ]]

                    if (importtypedata.EVENTCD == null)
                    {
                        msgBoard.message = "Step2: Import Type - Event Code must be selected!";
                        msgBoard.status = false;
                    }

                    if (RequiredMapping(importtypedata.MAPPING, "Event Status") == false)
                    {
                        msgBoard.message = "\nStep3: Map Fields - Event Status must be mapped!";
                        msgBoard.status = false;
                    }

                    #endregion
                }

                if (importtypedata.IMPTYPEID == 9)
                {
                    #region [[ Action ]]
                    bool actName = RequiredMapping(importtypedata.MAPPING, "Action Name");
                    bool actDte = RequiredMapping(importtypedata.MAPPING, "Action Date");
                    if (actName == false && actDte == false)
                    {
                        msgBoard.message = "\nStep3: Map Fields - Action Name and Action Date must be mapped!";
                        msgBoard.status = false;
                    }
                    else if (actName == false)
                    {
                        msgBoard.message = "\nStep3: Map Fields - Action Name must be mapped!";
                        msgBoard.status = false;
                    }
                    else if (actDte == false)
                    {
                        msgBoard.message = "\nStep3: Map Fields - Action Date must be mapped!";
                        msgBoard.status = false;
                    }
                    #endregion
                }

                if (importtypedata.IMPTYPEID == 31)
                {
                    #region [[ Expenditure ]]

                    if (importtypedata.FUNDID == 0)
                    {
                        msgBoard.message = "Step2: Import Type - Fund must be selected!";
                        msgBoard.status = false;
                    }

                    if (importtypedata.LINE == "" || importtypedata.LINE == null)
                    {
                        msgBoard.message = msgBoard.message + "\nStep2: Import Type - Line No must be selected!";
                        msgBoard.status = false;
                    }

                    if (importtypedata.CENTERID < 0)
                    {
                        msgBoard.message = msgBoard.message + "\nStep2: Import Type - Bank Code must be selected!";
                        msgBoard.status = false;
                    }

                    // Check if Source, Amt and Payment Type are mapped
                    if (RequiredMapping(importtypedata.MAPPING, "Txn Date") == false)
                    {
                        msgBoard.message = msgBoard.message + "\nStep3: Map Fields - Date must be mapped!";
                        msgBoard.status = false;
                    }

                    if (RequiredMapping(importtypedata.MAPPING, "Amount") == false)
                    {
                        msgBoard.message = msgBoard.message + "\nStep3: Map Fields - Amount must be mapped!";
                        msgBoard.status = false;
                    }

                    #endregion
                }

                if (importtypedata.IMPTYPEID == 32)
                {
                    #region [[ SRCCODE ]]

                    // Check if SRCCODE, DESCIPTION are mapped
                    if (RequiredMapping(importtypedata.MAPPING, "Source Code") == false)
                    {
                        msgBoard.message = msgBoard.message + "\nStep3: Map Fields - Source Code must be mapped!";
                        msgBoard.status = false;
                    }

                    if (RequiredMapping(importtypedata.MAPPING, "Description1") == false)
                    {
                        msgBoard.message = msgBoard.message + "\nStep3: Map Fields - Description1 must be mapped!";
                        msgBoard.status = false;
                    }

                    #endregion
                }
                if (importtypedata.IMPTYPEID == 36)
                {
                    #region [[ New Gift By Date And Source Code ]]

                    if (RequiredMapping(importtypedata.MAPPING, "Mail Code") == false)
                    {
                        msgBoard.message = msgBoard.message + "\nStep3: Map Fields - Mail Code must be mapped!";
                        msgBoard.status = false;
                    }

                    if (RequiredMapping(importtypedata.MAPPING, "Gift Date") == false)
                    {
                        msgBoard.message = msgBoard.message + "\nStep3: Map Fields - Gift Date must be mapped!";
                        msgBoard.status = false;
                    }
                    if (RequiredMapping(importtypedata.MAPPING, "Donors") == false)
                    {
                        msgBoard.message = msgBoard.message + "\nStep3: Map Fields - Donors must be mapped!";
                        msgBoard.status = false;
                    }
                    if (RequiredMapping(importtypedata.MAPPING, "NonDonors") == false)
                    {
                        msgBoard.message = msgBoard.message + "\nStep3: Map Fields - NonDonors must be mapped!";
                        msgBoard.status = false;
                    }
                    if (RequiredMapping(importtypedata.MAPPING, "GiftAmount") == false)
                    {
                        msgBoard.message = msgBoard.message + "\nStep3: Map Fields - GiftAmount must be mapped!";
                        msgBoard.status = false;
                    }

                    #endregion
                }
                if (importtypedata.IMPTYPEID == 8)
                {
                    if (RequiredMapping(importtypedata.MAPPING, "Pid") == false)
                    {
                        msgBoard.message = "Step3: Map Fields - Pid must be mapped!";
                        msgBoard.status = false;
                    }
                    if (RequiredMapping(importtypedata.MAPPING, "Mid") == false)
                    {
                        msgBoard.message += "\nStep3: Map Fields - Mid must be mapped!";
                        msgBoard.status = false;
                    }
                    if (RequiredMapping(importtypedata.MAPPING, "Amount") == false)
                    {
                        msgBoard.message += msgBoard.message + "\nStep3: Map Fields - Amount must be mapped!";
                        msgBoard.status = false;
                    }
                }

                //Expenditure
                if (importtypedata.IMPTYPEID == 31 || importtypedata.IMPTYPEID == 30)
                {
                    //last Name or Org name or Vendor Account
                    if ((RequiredMapping(importtypedata.MAPPING, "Last Name") == false)
                        && (RequiredMapping(importtypedata.MAPPING, "Org Name") == false)
                        && (RequiredMapping(importtypedata.MAPPING, "Vendor Account ID") == false)
                        )
                    {
                        msgBoard.message = msgBoard.message + "\nStep3: Map Fields - Organization or Last Name or Vendor Account ID must be mapped!";
                        msgBoard.status = false;
                    }
                }
                else if (importtypedata.IMPTYPEID == 37)
                {
                    //Entity Id and Txn Id
                    if ((RequiredMapping(importtypedata.MAPPING, "Txn Id") == false)
                        || (RequiredMapping(importtypedata.MAPPING, "Entity Id") == false))
                    {
                        msgBoard.message = msgBoard.message + "\nStep3: Map Fields - Entity Id and Txn Id must be mapped!";
                        msgBoard.status = false;
                    }
                }
                else if (importtypedata.IMPTYPEID != 99 && importtypedata.IMPTYPEID != 8 && importtypedata.IMPTYPEID != 33 && importtypedata.IMPTYPEID != 32 && importtypedata.IMPTYPEID != 35 && importtypedata.IMPTYPEID != 36 && importtypedata.IMPTYPEID != 39 && importtypedata.IMPTYPEID != 40)
                {
                    //Last Name
                    if (RequiredMapping(importtypedata.MAPPING, "Last Name") == false)
                    {
                        msgBoard.message = msgBoard.message + "\nStep3: Map Fields - Last Name must be mapped!";
                        msgBoard.status = false;
                    }
                }

                if (importtypedata.IMPTYPEID == 99)
                {
                    if (RequiredMapping(importtypedata.MAPPING, "LASTNAME") == false)
                    {
                        msgBoard.message = msgBoard.message + "\nStep3: Map Fields - Last Name must be mapped!";
                        msgBoard.status = false;
                    }

                    if (RequiredMapping(importtypedata.MAPPING, "STREET") == false)
                    {
                        msgBoard.message = msgBoard.message + "\nStep3: Map Fields - Street must be mapped!";
                        msgBoard.status = false;
                    }
                    if (RequiredMapping(importtypedata.MAPPING, "ZIP") == false)
                    {
                        msgBoard.message = msgBoard.message + "\nStep3: Map Fields - Zip must be mapped!";
                        msgBoard.status = false;
                    }
                }
                // Corp Match
                if (importtypedata.IMPTYPEID == 35)
                {
                    if (importtypedata.NOOFGIFTS == 0)
                    {
                        msgBoard.message = msgBoard.message + "\nStep2: Import Type - # of Gifts must be entered!";
                        msgBoard.status = false;
                    }

                    if (importtypedata.TOTALAMOUNT == 0)
                    {
                        msgBoard.message = msgBoard.message + "\nStep2: Import Type - Total Amount must be entered!";
                        msgBoard.status = false;
                    }

                    // Check if Source, Amt and Payment Type are mapped
                    if (RequiredMapping(importtypedata.MAPPING, "Individual Match Gift Id") == false)
                    {
                        if (RequiredMapping(importtypedata.MAPPING, "Fund Code") == false)
                        {
                            msgBoard.message = msgBoard.message + "\nStep3: Map Fields - Fund Code must be mapped!";
                            msgBoard.status = false;
                        }
                        if (RequiredMapping(importtypedata.MAPPING, "Source Code") == false)
                        {
                            msgBoard.message = msgBoard.message + "\nStep3: Map Fields - Source Code must be mapped!";
                            msgBoard.status = false;
                        }
                        if (RequiredMapping(importtypedata.MAPPING, "Batch#") == false)
                        {
                            msgBoard.message = msgBoard.message + "\nStep3: Map Fields - Batch# must be mapped!";
                            msgBoard.status = false;
                        }
                        if (RequiredMapping(importtypedata.MAPPING, "Batch Date") == false)
                        {
                            msgBoard.message = msgBoard.message + "\nStep3: Map Fields - Batch Date must be mapped!";
                            msgBoard.status = false;
                        }
                        if (RequiredMapping(importtypedata.MAPPING, "Gift Amount") == false)
                        {
                            msgBoard.message = msgBoard.message + "\nStep3: Map Fields - Gift Amount must be mapped!";
                            msgBoard.status = false;
                        }
                    }
                    else
                    {
                        // Corp Match records only
                        if (RequiredMapping(importtypedata.MAPPING, "Corp Fund Code") == false)
                        {
                            msgBoard.message = msgBoard.message + "\nStep3: Map Fields - Corp Fund Code must be mapped!";
                            msgBoard.status = false;
                        }
                        if (RequiredMapping(importtypedata.MAPPING, "Corp Source Code") == false)
                        {
                            msgBoard.message = msgBoard.message + "\nStep3: Map Fields - Corp Source Code must be mapped!";
                            msgBoard.status = false;
                        }
                        if (RequiredMapping(importtypedata.MAPPING, "Corp Batch#") == false)
                        {
                            msgBoard.message = msgBoard.message + "\nStep3: Map Fields - Corp Batch# must be mapped!";
                            msgBoard.status = false;
                        }
                        if (RequiredMapping(importtypedata.MAPPING, "Corp Batch Date") == false)
                        {
                            msgBoard.message = msgBoard.message + "\nStep3: Map Fields - Corp Batch Date must be mapped!";
                            msgBoard.status = false;
                        }
                        if (RequiredMapping(importtypedata.MAPPING, "Corp Gift Amount") == false)
                        {
                            msgBoard.message = msgBoard.message + "\nStep3: Map Fields - Corp Gift Amount must be mapped!";
                            msgBoard.status = false;
                        }
                    }
                }
                // Tribute
                if (importtypedata.IMPTYPEID == 39)
                {
                    if (importtypedata.NOOFGIFTS == 0)
                    {
                        msgBoard.message = msgBoard.message + "\nStep2: Import Type - # of Gifts must be entered!";
                        msgBoard.status = false;
                    }

                    if (importtypedata.TOTALAMOUNT == 0)
                    {
                        msgBoard.message = msgBoard.message + "\nStep2: Import Type - Total Amount must be entered!";
                        msgBoard.status = false;
                    }
                }
                // Support Adjustment
                if (importtypedata.IMPTYPEID == 40)
                {
                    if (importtypedata.NOOFGIFTS == 0)
                    {
                        msgBoard.message = msgBoard.message + "\nStep2: Import Type - # of Gifts must be entered!";
                        msgBoard.status = false;
                    }

                    if (importtypedata.TOTALAMOUNT == 0)
                    {
                        msgBoard.message = msgBoard.message + "\nStep2: Import Type - Total Amount must be entered!";
                        msgBoard.status = false;
                    }
                }
            }
            catch (Exception ex)
            {
                msgBoard.status = false;
                msgBoard.message = "Error in validation!!";
            }

            return msgBoard;
        }

        private MessageBoard excelDataValidation(ImportTypeData importtypedata, List<lkImpField> dbColumnMapList, ObservableCollection<importRow.Row> rows)
        {
            MessageBoard msgBoard = new MessageBoard();
            msgBoard.status = true;
            msgBoard.message = "";
            try
            {
                if (importtypedata.IMPTYPEID == 32)
                {
                    #region [[ SRCCODE ]]
                    
                    lkImpField dgc = new lkImpField();
                    int rowindex = -1;
                    string fieldvalue;
                    string val = "";
                    string srcs = "";
                    List<string> vals = new List<string>();
                    for (int rowcount = 0; rowcount < rows.Count(); rowcount++)
                    {                    
                        for (int fieldcount = 0; fieldcount < dbColumnMapList.Count; fieldcount++)
                        {
                            dgc = dbColumnMapList[fieldcount];
                            //check if this field is mapped
                            if (dgc.DISNAME== "Source Code" && RequiredMapping(importtypedata.MAPPING, dgc.DISNAME, out rowindex) == true)
                            {
                                importRow.Row row = rows[rowcount];
                                fieldvalue = row[rowindex.ToString()].ToString();
                                if (!string.IsNullOrEmpty(fieldvalue))
                                {
                                    vals.Add(fieldvalue);//check dupe
                                    if (fieldvalue.Length > dgc.COLUMNLENGTH)
                                    {
                                        val = val + ",'" + fieldvalue + "'";//length>15
                                    }
                                    if (srcs != "") srcs += ",";
                                    srcs += "'" + fieldvalue + "'";//check if srccode in crimson
                                }
                                else
                                {//empty source code found
                                    msgBoard.status = false;
                                    msgBoard.message = "Empty source code found, please fix and try again.";
                                    return msgBoard;
                                }
                            }
                        }
                    }
                    if (val != "")
                    {
                        msgBoard.status = false;
                        msgBoard.message += "Source codes cannot exceed 15 characters" + val +". ";
                    }
                    List<string> duplicateKeys = vals.GroupBy(x => x.ToLower())
                        .Where(group => group.Count() > 1)
                        .Select(group => group.Key).ToList();
                    string dupes = "";
                    foreach (string d in duplicateKeys)
                    {
                        dupes = dupes + ",'" + d + "'";
                    }
                    if (dupes != "")
                    {
                        msgBoard.status = false;
                        msgBoard.message += "Duplicate source codes in your file" + dupes + ". ";
                    }
                    if (msgBoard.status == false)
                    {
                        msgBoard.message = msgBoard.message+ " Please fix and try again. ";
                    }
                    else //check if source codes already in crimson
                    {
                        string checkSrcSql = String.Format(@"SELECT * FROM SOURCE WHERE SRCECODE IN ({0})", srcs);
                        List<SOURCE> srcList= _entity_crm.getContext().Database.SqlQuery<SOURCE>(checkSrcSql).ToList();
                        if (srcList.Count > 0)
                        {
                            //source code under other initiatives/packages
                            List<SOURCE> srcInOtherIList = srcList.FindAll(s => s.PKGEID != importtypedata.PKGEID).ToList();
                            if (srcInOtherIList.Count > 0)
                            {
                                if (importtypedata.OVERWRITESRCCODE2)
                                {
                                    msgBoard.status = true;
                                    msgBoard.message = "At least one source code in file exists under a different initiative/package. " + srcInOtherIList.Count.ToString() + " record(s).";
                                }
                                else
                                {
                                    msgBoard.status = false;
                                    string srcsfordisplay = "";
                                    for (int i = 0; i < srcInOtherIList.Count && i < 5; i++)
                                    {
                                        srcsfordisplay += srcInOtherIList[i].SRCECODE + ", ";
                                    }
                                    if (srcInOtherIList.Count > 5)
                                    {
                                        srcsfordisplay += "and more.";
                                    }
                                    else
                                    {
                                        srcsfordisplay = srcsfordisplay.Substring(0, srcsfordisplay.Length - 2);
                                    }
                                    msgBoard.message = srcInOtherIList.Count.ToString() + " source code(s) in file exist under a different initiative/package. (" + srcsfordisplay + ")";
                                    srcsfordisplay = "";
                                    for (int i = 0; i < srcList.Count && i < 5; i++)
                                    {
                                        srcsfordisplay += srcList[i].SRCECODE + ", ";
                                    }
                                    if (srcList.Count > 5)
                                    {
                                        srcsfordisplay += "and more.";
                                    }
                                    else
                                    {
                                        srcsfordisplay = srcsfordisplay.Substring(0, srcsfordisplay.Length - 2);
                                    }
                                    msgBoard.message = srcList.Count.ToString() + " source code(s) in file already exist in Crimson. (eg. " + srcsfordisplay + ") And "+ msgBoard.message;
                                    
                                }

                            }
                            else //srccode exists in crimson
                            {
                                if (importtypedata.OVERWRITESRCCODE)
                                {
                                    msgBoard.status = true;
                                    msgBoard.message = "At least one source code in file already exists in Crimson. " + srcList.Count.ToString() + " record(s).";
                                }
                                else
                                {
                                    msgBoard.status = false;
                                    string srcsfordisplay2 = "";
                                    for (int i = 0; i < srcList.Count && i < 5; i++)
                                    {
                                        srcsfordisplay2 += srcList[i].SRCECODE + ", ";
                                    }
                                    if (srcList.Count > 5)
                                    {
                                        srcsfordisplay2 += "and more.";
                                    }
                                    else
                                    {
                                        srcsfordisplay2 = srcsfordisplay2.Substring(0, srcsfordisplay2.Length - 2);
                                    }
                                    msgBoard.message = srcList.Count.ToString() + " source code(s) in file already exist in Crimson. (eg. " + srcsfordisplay2 + ")";
                                }
                            }


                            //cache error source code
                            srceImportErrorData data = new srceImportErrorData()
                            {
                                srcList = srcList,
                                srcInOtherIList = srcInOtherIList,
                                fileName = importtypedata.UPLOADEDFILENAME,
                                WORKSHEET = importtypedata.WORKSHEET,
                                rowindex = rowindex
                            };
                            string key_ = System.Guid.NewGuid().ToString();
                            HttpRuntime.Cache.Insert(key_, data, null, DateTime.UtcNow.Add(new TimeSpan(1, 0, 1)), System.Web.Caching.Cache.NoSlidingExpiration);
                            msgBoard.dataKey = key_;
                        }
                    }
                    #endregion
                }
                if (importtypedata.IMPTYPEID == 22|| importtypedata.IMPTYPEID == 21 || importtypedata.IMPTYPEID == 23 || importtypedata.IMPTYPEID == 24)
                {
                    #region [[ Attributions import check record count and total amt ]]

                    int rowindex;
                    if (RequiredMapping(importtypedata.MAPPING, "Gift Amount", out rowindex) == true)
                    {
                        decimal totalAmt = 0;
                        for (int rowcount = 0; rowcount < rows.Count(); rowcount++)
                        {
                            string fieldValue = rows[rowcount][rowindex.ToString()].ToString();
                            if (fieldValue.Contains("$"))
                                fieldValue = fieldValue.Replace("$", "");
                            decimal x = 0;
                            decimal.TryParse(fieldValue, out x);
                            totalAmt += x;
                        }

                        // Allow earmark attribution import > 10000 records if configurable value is set
                        Boolean canEarmarkQueueImport = EarmarkImportQueue() == "Y" ? true : false;
                        if (importtypedata.IMPTYPEID == 21 && rows.Count() > 10000 && !canEarmarkQueueImport && !session.userSession.is_sysAdmin)
                        {
                            msgBoard.status = false;
                            msgBoard.message = "The Earmark Attribution file you are attempting to import contains more than 10000 records for import. Please limit the import to no more than 10000 records at a time. If you have questions, <NAME_EMAIL> for additional assistance.";
                        }
                        else if(totalAmt == importtypedata.TOTALAMOUNT && importtypedata.NOOFGIFTS == rows.Count())
                        {
                            msgBoard.status = true;
                            msgBoard.message = "";
                        }
                        else
                        {
                            msgBoard.status = false;
                            msgBoard.message = "No. of Gifts or Total Amount does not match. Please fix and try again.";
                        }
                        
                    }
                    else
                    {
                        msgBoard.status = false;
                        msgBoard.message = "Gift Amount is not mapped. Please fix and try again.";
                    }
                    
                    
                    #endregion
                }
                if (importtypedata.IMPTYPEID == 33)
                {
                    #region [[ mass adjustment ]]
                    Decimal testnum;
                    lkImpField dgc = new lkImpField();
                    int rowindex = -1;
                    string fieldvalue;
                    for (int rowcount = 0; rowcount < rows.Count(); rowcount++)
                    {
                        for (int fieldcount = 0; fieldcount < dbColumnMapList.Count; fieldcount++)
                        {
                            dgc = dbColumnMapList[fieldcount];
                            //check if this field is mapped
                            if (dgc.DISNAME == "Adjustment Date" && RequiredMapping(importtypedata.MAPPING, dgc.DISNAME, out rowindex) == true)
                            {
                                importRow.Row row = rows[rowcount];
                                fieldvalue = row[rowindex.ToString()].ToString();
                                if (string.IsNullOrEmpty(fieldvalue))
                                {
                                    msgBoard.status = false;
                                    msgBoard.message = "Empty Adjustment Date found, please fix and try again.";
                                    return msgBoard;
                                }
                            }
                            if (dgc.DISNAME == "Gift Amount" && RequiredMapping(importtypedata.MAPPING, dgc.DISNAME, out rowindex) == true)
                            {
                                importRow.Row row = rows[rowcount];                                
                                fieldvalue = row[rowindex.ToString()].ToString().Replace(",", "");
                                fieldvalue = fieldvalue.TrimStart('$');
                                if (string.IsNullOrEmpty(fieldvalue))
                                {
                                    msgBoard.status = false;
                                    msgBoard.message = "Empty Gift Amount found, please fix and try again.";
                                    return msgBoard;
                                }
                                if (Decimal.TryParse(fieldvalue, out testnum))
                                {

                                }
                                else
                                {
                                    msgBoard.status = false;
                                    msgBoard.message = "Gift Amount " + fieldvalue + " cannot be parsed, please fix and try again.";
                                    return msgBoard;
                                }                                
                            }
                        }
                    }
                    #endregion
                }
            }
            catch(Exception ex)
            {
                msgBoard.status = false;
                msgBoard.message = "Error in validation!!";
            }

            return msgBoard;
        }

        private bool CreateNecessaryColumnsifMissing(string importTable)
        {

            //Let us check for first column...PREFIX
            List<ExcelInputData> data = new List<ExcelInputData>();
            importTable = "[!_web_app].[" + importTable + "]";
            MessageBoard msgBoard = new MessageBoard();

            //PREFIX, FIRSTNAME, MIDDLENAME, SUFFIX, EMAIL, HMPHN, CELL, ADDR1

            try
            {
                data = _importService.CheckIfColumnExists("PREFIX", importTable);
            }
            catch (Exception ex)
            {
                //Looks like column is missing..so we need to add one...
                if (ex.Message.Contains("Invalid column name"))
                {
                    msgBoard = AddColumnForTable(importTable, "PREFIX");
                }
            }

            try
            {
                data = _importService.CheckIfColumnExists("FIRSTNAME", importTable);
            }
            catch (Exception ex)
            {
                //Looks like column is missing..so we need to add one...
                if (ex.Message.Contains("Invalid column name"))
                {
                    msgBoard = AddColumnForTable(importTable, "FIRSTNAME");
                }
            }

            try
            {
                data = _importService.CheckIfColumnExists("MIDDLENAME", importTable);
            }
            catch (Exception ex)
            {
                //Looks like column is missing..so we need to add one...
                if (ex.Message.Contains("Invalid column name"))
                {
                    msgBoard = AddColumnForTable(importTable, "MIDDLENAME");
                }
            }

            try
            {
                data = _importService.CheckIfColumnExists("SUFFIX", importTable);
            }
            catch (Exception ex)
            {
                //Looks like column is missing..so we need to add one...
                if (ex.Message.Contains("Invalid column name"))
                {
                    msgBoard = AddColumnForTable(importTable, "SUFFIX");
                }
            }

            try
            {
                data = _importService.CheckIfColumnExists("EMAIL", importTable);
            }
            catch (Exception ex)
            {
                //Looks like column is missing..so we need to add one...
                if (ex.Message.Contains("Invalid column name"))
                {
                    msgBoard = AddColumnForTable(importTable, "EMAIL");
                }
            }

            try
            {
                data = _importService.CheckIfColumnExists("HMPHN", importTable);
            }
            catch (Exception ex)
            {
                //Looks like column is missing..so we need to add one...
                if (ex.Message.Contains("Invalid column name"))
                {
                    msgBoard = AddColumnForTable(importTable, "HMPHN");
                }
            }

            try
            {
                data = _importService.CheckIfColumnExists("CELL", importTable);
            }
            catch (Exception ex)
            {
                //Looks like column is missing..so we need to add one...
                if (ex.Message.Contains("Invalid column name"))
                {
                    msgBoard = AddColumnForTable(importTable, "CELL");
                }
            }

            try
            {
                data = _importService.CheckIfColumnExists("ADDR1", importTable);
            }
            catch (Exception ex)
            {
                //Looks like column is missing..so we need to add one...
                if (ex.Message.Contains("Invalid column name"))
                {
                    msgBoard = AddColumnForTable(importTable, "ADDR1");
                }
            }

            return true;
        }

        private MessageBoard AddColumnForTable(string importTable, string columnName)
        {
            MessageBoard msgBoard = new MessageBoard();
            string _sql = "";

            _sql = "EXEC JFCVettProcessSub ";

            _sql += "'" + importTable + "'";

            _sql += ",'" + columnName;
            _sql += "'";

            //Execute the Validation step
            if (_sql != String.Empty)
            {
                msgBoard = _importService.process_sql(_sql, "AddColumn", msgBoard);
            }

            //return the Message Board
            return msgBoard;
        }

        private MessageBoard ProcessJFCDataValidation(ImportTypeData importtypedata, string importTable)
        {
            MessageBoard msgBoard = new MessageBoard();
            string _sql = "";
            
            _sql = "EXEC JFCVettProcess ";
            
            _sql += "'";
            _sql += importtypedata.FUNDIDS;
            _sql += "'";

            _sql += ",'[!_web_app]." + importTable + "'";
                        
            _sql += "," + (importtypedata.DONTOTOPTION == true ? 1 : 0);

            //Execute the Validation step
            if (_sql != String.Empty)
            {
                msgBoard = _importService.process_sql(_sql, "Validate", msgBoard);
            }

            //return the Message Board
            return msgBoard;
        }
        
        private MessageBoard ProcessJFCVettData(ImportTypeData importtypedata)
        {
                        
            MessageBoard msgBoard = new MessageBoard();
            //set initial values
            msgBoard.message = "";
            msgBoard.tempTableName = importtypedata.IMPORTTABLENAME;
            msgBoard.xmlresponse = "";
            msgBoard.impTypeSelection = importtypedata.IMPTYPEID;
            
            try
            {
                
                #region [[ Create sql Statement ]]
                string _sql = "";

                _sql += "EXEC JFCVettImportData ";
                //JFCVettImportData_v2

                _sql += "'";
                _sql += importtypedata.FUNDIDS;
                _sql += "'";

                _sql += "," + (importtypedata.DONTOTOPTION == true ? 1 : 0);

                _sql += ",'!_web_app'";
                _sql += ",'" + importtypedata.IMPORTTABLENAME + "'"; ;
                
                _sql += ",'" + DateTime.Now.ToString("MM/dd/yyyy HH:mm:ss.fff") + "'";
                _sql += ",1";
                #endregion

                // Execute
                if (_sql != String.Empty)
                {
                    msgBoard = _importService.process_sql(_sql, "Process", msgBoard);
                }
                
                //One more try with only 3 columns...FOR TESTING ONLY
                //if (!msgBoard.status)
                //{
                //    //Let us retry
                //     _sql = "";

                //    _sql += "EXEC JFCVettImportData ";
                    
                //    //_sql += "'";
                //    _sql += importtypedata.FUNDID;
                //    //_sql += "'";

                //    _sql += "," + (importtypedata.DONTOTOPTION == true ? 1 : 0);

                //    _sql += ",'!_web_app'";
                //    _sql += ",'" + importtypedata.IMPORTTABLENAME + "'"; ;

                //    _sql += ",'" + DateTime.Now.ToString("MM/dd/yyyy HH:mm:ss.fff") + "'";
                //    _sql += ",0";

                //    msgBoard = _importService.process_sql(_sql, "Process", msgBoard);

                //}



                if (msgBoard.status && !string.IsNullOrEmpty(msgBoard.xmlresponse))
                {
                    //Let us parse the response
                    System.IO.TextReader tr = new System.IO.StringReader(msgBoard.xmlresponse);
                    XDocument oDoc = XDocument.Load(tr);

                    //var _elem = (from a in oDoc.Descendants("MESSAGE")
                    //             select a).FirstOrDefault();
                    if (oDoc != null)
                    {
                        msgBoard.message = "Success";
                        msgBoard.status = true;
                        msgBoard.xmlresponse = "NoError";
                    }
                    else
                    {
                        msgBoard.status = false;
                        msgBoard.xmlresponse = "Error";
                        msgBoard.message = "There is a problem in Processing the data (Code: 730). Please try again later.";
                    }
                }
                else
                {
                    throw new System.ApplicationException("\r\nPlease try again after all errors are fixed.");
                }
            }
            catch (System.Exception ex)
            {
                msgBoard.message = "There is some problem in processing JFC Vett (ImportData step : Code 763) request. Please try again later. Details: " + ex.Message;
                msgBoard.status = false;
            }

            return msgBoard;
        }

        private MessageBoard DataValidation(ImportTypeData importtypedata, string importTable)
        {
            MessageBoard msgBoard = new MessageBoard();
            string _sql = "";
            string ImportCode = "";
            if (importtypedata.IMPTYPEID.Equals(30))
            {
                ImportCode = "E";
            }
            else if (importtypedata.IMPTYPEID.Equals(31))
            {

                ImportCode = "T";
            }
            else
            {
            }

            //execute the appropriate stored procedure
            if (importtypedata.IMPTYPEID == 6)
            {
                _sql = "EXEC zImportEventValidate ";
            }
            else if (importtypedata.IMPTYPEID == 21 || importtypedata.IMPTYPEID == 22 || importtypedata.IMPTYPEID == 23 || importtypedata.IMPTYPEID == 24)
            {
                _sql = "EXEC zImportMonyAdjustValidate ";
            }
            else if (importtypedata.IMPTYPEID == 30 || importtypedata.IMPTYPEID == 31)
            {
                _sql = "EXEC zImportExpValidate ";
            }
            else if (importtypedata.IMPTYPEID == 35)
            {
                _sql = "EXEC crmImportCorpMatchValidate ";
            }
            else if (importtypedata.IMPTYPEID == 37)
            {
                _sql = "EXEC zImportTxnUpdateValidate ";
            }
            else if (importtypedata.IMPTYPEID == 38)
            {
                _sql = "EXEC crmImportPledgeValidate ";
            }
            else if (importtypedata.IMPTYPEID == 39)
            {
                _sql = "EXEC crmImportHonMemValidate ";
            }
            else if (importtypedata.IMPTYPEID == 40)
            {
                _sql = "EXEC zImportMonyAttribValidate ";
            }
            else
            {
                _sql = "EXEC crmImportValidate ";
            }

            //first parameter of stored procedure
            if (importtypedata.IMPTYPEID == 0) //New Gift
            {
                _sql += "'G'";
            }
            else if ((importtypedata.IMPTYPEID == 1 && importtypedata.PEOCODEID == 5) || importtypedata.IMPTYPEID == 4) //New Contact [People code(New Non-donor)] & Append existing record
            {
                _sql += "'D'";
            }
            else if (importtypedata.IMPTYPEID == 1 && importtypedata.PEOCODEID == 2) // New Contact [People code(New Volunteer)]
            {
                _sql += "'V'";
            }
            else if (importtypedata.IMPTYPEID == 2) // New Email Signup
            {
                _sql += "'E'";
            }
            else if (importtypedata.IMPTYPEID == 9) // Action import
            {
                _sql += "'C'";
            }
            else if (importtypedata.IMPTYPEID == 8) // Gift Update
            {
                _sql += "'$'";
            }
            //Append Existing record , Interaction Append to existing records, Event
            else if (importtypedata.IMPTYPEID == 3 || importtypedata.IMPTYPEID == 5 || importtypedata.IMPTYPEID == 6 || importtypedata.IMPTYPEID == 21 || importtypedata.IMPTYPEID == 22 || importtypedata.IMPTYPEID == 23 || importtypedata.IMPTYPEID == 24)
            {
                if (importtypedata.ISOVERWRITE == true)
                    _sql += "'A'";
                else
                    _sql += "'N'";
            }


            if (importtypedata.IMPTYPEID == 0) // New Gift
            {
                _sql += "," + importtypedata.NOOFGIFTS.ToString() +
                        "," + importtypedata.TOTALAMOUNT.ToString();
            }
            else if (importtypedata.IMPTYPEID == 6) // Event
            {
                _sql += "";
            }
            else if (importtypedata.IMPTYPEID == 21 || importtypedata.IMPTYPEID == 22 || importtypedata.IMPTYPEID == 23 || importtypedata.IMPTYPEID == 24)
            {
                _sql += "'";
                _sql += importtypedata.IMPTYPEID;
                _sql += "'";
            }
            else if (importtypedata.IMPTYPEID == 30 || importtypedata.IMPTYPEID == 31)
            {
                _sql += "'" + ImportCode + "'";
            }
            else if (importtypedata.IMPTYPEID == 35 || importtypedata.IMPTYPEID == 38 || importtypedata.IMPTYPEID == 39 || importtypedata.IMPTYPEID == 40) // Corp Match or Pledge or Tribute or Support Adjustment
            {
                _sql += importtypedata.NOOFGIFTS.ToString() +
                        "," + importtypedata.TOTALAMOUNT.ToString();
            }
            else if (importtypedata.IMPTYPEID == 37)
            {
                _sql += "'" + ImportCode + "'";
            }
            else
            {
                _sql += ",null,null";
            }

            _sql += ",'[!_web_app]." + importTable + "'";

            if (importtypedata.IMPTYPEID == 6) // Event
            {
                // added single quote to handle hyphen and numeric
                //since the input to store procedure is varchar
                //_sql += ",'" + GetCode(importtypedata.EVENTCD) + "'";
                _sql += ",'" + importtypedata.EVENTCD + "'";
            }

            if (importtypedata.IMPTYPEID == 30 || importtypedata.IMPTYPEID == 31)
            {
                _sql += ",'" + (importtypedata.TXNTYPE != null ? importtypedata.TXNTYPE : "null") + "'";
                _sql += ",'" + (importtypedata.FUNDCODE != null ? importtypedata.FUNDCODE : "null") + "'";
                _sql += ",'" + (importtypedata.CENTERCODE != null ? importtypedata.CENTERCODE : "null") + "'";
                _sql += ",'" + (importtypedata.LINE != null ? importtypedata.LINE : "null") + "'";
            }

            if (importtypedata.IMPTYPEID == 36)
            {
                _sql = "EXEC SWAImportValidate___SWA '[!_web_app]." + importTable + "' ";
            }

            if (importtypedata.IMPTYPEID == 37)
            {
                _sql += ",'" + (importtypedata.TXNTYPE != null ? importtypedata.TXNTYPE : "null") + "'";
            }

            //Execute the Validation step
            if (_sql != String.Empty)
            {
                msgBoard = _importService.process_sql(_sql, "Validate", msgBoard);
            }

            //return the Message Board
            return msgBoard;
        }

        private bool RequiredMapping2(List<MappingRow> MAPPING, string text)
        {
            int index;

            return RequiredMapping2(MAPPING, text, out index);
        }

        private bool RequiredMapping2(List<MappingRow> MAPPING, string text, out int index)
        {
            bool returnvalue = false;
            index = -1;
            int count = 0;

            foreach (MappingRow m in MAPPING)
            {
                if (m.FILECOLUMNNAME.ToUpper() == text.ToUpper())
                {
                    returnvalue = true;
                    index = count;
                    break;
                }

                count = count + 1;
            }

            return returnvalue;
        }


        private bool RequiredMapping(List<MappingRow> MAPPING, string text)
        {
            int index;

            return RequiredMapping(MAPPING, text, out index);
        }

        private bool RequiredMapping(List<MappingRow> MAPPING, string text, out int index)
        {
            bool returnvalue = false;
            index = -1;
            int count = 0;

            foreach (MappingRow m in MAPPING)
            {
                if (m.MAPPEDCOLUMNNAME.ToUpper() == text.ToUpper())
                {
                    returnvalue = true;
                    index = count;
                    break;
                }

                count = count + 1;
            }

            return returnvalue;
        }

        private string GenerateImportTableName()
        {
            Guid _id = Guid.NewGuid();
            string num = _id.ToString();
            return "_IMP" + session.userSession.UserName.ToUpper() + num.Replace("-","").Substring(0,5);
        }

        private string CreateImportTableQuery(int importTypeId, string importTable)
        {
            string sql;

            if (importTypeId == 6)
            {
                sql = "SELECT * INTO " + importTable + " FROM zEventImport \n\r TRUNCATE TABLE " + importTable;
            }
            else if (importTypeId == 21 || importTypeId == 22 || importTypeId == 23 || importTypeId == 24)
            {
                sql = "SELECT * INTO " + importTable + " FROM zMONYADJIMPORT \n\r TRUNCATE TABLE " + importTable;
            }
            else if (importTypeId == 30 || importTypeId == 31 || importTypeId == 37)
            {
                sql = "SELECT * INTO " + importTable + " FROM zEXPIMPORT \n\r TRUNCATE TABLE " + importTable;
            }
            else if (importTypeId == 99)
            {
                sql = "SELECT * INTO " + importTable + " FROM JFCVettIMPORT \n\r TRUNCATE TABLE " + importTable;
            }
            else if (importTypeId == 32)
            {
                sql = "SELECT * INTO " + importTable + " FROM srccodeImport \n\r TRUNCATE TABLE " + importTable;
            }
            else if (importTypeId == 35)
            {
                sql = "SELECT * INTO " + importTable + " FROM crmIMPORTCORPMATCH \n\r TRUNCATE TABLE " + importTable;
            }
            else if (importTypeId == 36)
            {
                sql = "SELECT * INTO " + importTable + " FROM crmImportDirectMail___SWA \n\r TRUNCATE TABLE " + importTable;
            }
            else if (importTypeId == 38)
            {
                sql = "SELECT * INTO " + importTable + " FROM crmIMPORTPLEDGE \n\r TRUNCATE TABLE " + importTable;
            }
            else if (importTypeId == 39)
            {
                sql = "SELECT * INTO " + importTable + " FROM crmIMPORTHONMEM \n\r TRUNCATE TABLE " + importTable;
            }
            else if (importTypeId == 40)
            {
                sql = "SELECT * INTO " + importTable + " FROM zMONYADJIMPORT \n\r TRUNCATE TABLE " + importTable;
            }
            else
            {
                sql = "SELECT * INTO " + importTable + " FROM crmImport \n\r TRUNCATE TABLE " + importTable;
            }

            return sql;
        }

        private string CreateImportHeaderQuery(ImportTypeData importtypedata, List<lkImpField> dbColumnMapList, string importTable)
        {
            string sql;
            string fld;
            string val;
            bool fieldMapped;
            lkImpField dgc = new lkImpField();

            fld = "RECTYPE";
            val = "'H'";

            for (int fieldcount = 0; fieldcount < dbColumnMapList.Count; fieldcount++)
            {
                dgc = dbColumnMapList[fieldcount];
                fld = fld + "," + dgc.FLDNAME;
                fieldMapped = false;

                //check if this field is mapped
                if (RequiredMapping(importtypedata.MAPPING, dgc.DISNAME) == true)
                {
                    fieldMapped = true;
                }

                if (dgc.COLUMNTYPE == "i")
                    val = val + (fieldMapped == true ? ",1" : ",0");
                else if (dgc.COLUMNTYPE == "d")
                    val = val + (fieldMapped == true ? ",'12/7/1941'" : ",null");
                else if (dgc.COLUMNTYPE == "c")
                    val = val + (fieldMapped == true ? ",1" : ",0");
                else
                    val = val + (fieldMapped == true ? ",'1'" : ",'0'");
            }

            sql = "INSERT " + importTable + " (" + fld + ") values (" + val + ")";

            return sql;

        }

        private string CreateImportDataQuery(ImportTypeData importtypedata, List<lkImpField> dbColumnMapList, string importTable, ObservableCollection<importRow.Row> rows, int fromrowno, int torowno)
        {
            string sql = "";
            string fld;
            string val;
            lkImpField dgc = new lkImpField();
            int _fromrowno;
            int _torowno;
            int rowindex;
            string fieldvalue;
            Decimal testnum;
            DateTime testdate;

            if (fromrowno > rows.Count)
            {
                //Instead of throwing an error.
                //Logic set is, Get all rows
                _fromrowno = 0;
                _torowno = rows.Count;
            }
            else if ((torowno == 0) || (torowno > rows.Count))
            {
                //logic is if the no of rows asked is greater than
                //no of rows in the excel file. Get up to the last row.
                _fromrowno = fromrowno;
                _torowno = rows.Count;
            }
            else
            {
                _fromrowno = fromrowno;
                _torowno = torowno;
            }

            for (int rowcount = _fromrowno; rowcount < _torowno; rowcount++)
            {
                //INSERT INTO MAPPED COLUMNS ONLY.
                fld = "RECTYPE";
                val = "'D'";
                for (int fieldcount = 0; fieldcount < dbColumnMapList.Count; fieldcount++)
                {
                    dgc = dbColumnMapList[fieldcount];

                    //check if this field is mapped
                    if (RequiredMapping(importtypedata.MAPPING, dgc.DISNAME, out rowindex) == true)
                    {
                        //Set the fld and val
                        //col = columns.IndexOf(mapList[idx].inField); //not sure if this is required.
                        fld = fld + "," + dgc.FLDNAME;
                        importRow.Row row = rows[rowcount];

                        if (dgc.COLUMNTYPE == "i" || dgc.COLUMNTYPE == "c")
                        {
                            fieldvalue = row[rowindex.ToString()].ToString().Replace(",", "");
                            fieldvalue = fieldvalue.TrimStart('$');
                            if (Decimal.TryParse(fieldvalue, out testnum))
                            {
                                val = val + "," + fieldvalue;
                            }
                            else
                            {
                                val = val + "," + "NULL";
                            }
                        }
                        else if (dgc.COLUMNTYPE == "d")
                        {
                            if (DateTime.TryParse(row[rowindex.ToString()].ToString(), out testdate))
                            {
                                val = val + ",'" + testdate.ToString("MM/dd/yyyy") + "'";
                            }
                            else
                            {
                                val += ",null";
                            }
                        }
                        else
                        {
                            fieldvalue = row[rowindex.ToString()].ToString();
                            if (fieldvalue.Length > dgc.COLUMNLENGTH)
                            {
                                fieldvalue = fieldvalue.Substring(0, dgc.COLUMNLENGTH);
                            }
                            fieldvalue = fieldvalue.Replace("'", "''").ToString();

                            val = val + ",'" + fieldvalue + "'";
                        }

                    }
                }

                sql = sql + "INSERT " + importTable + " (" + fld + ") values (" + val + ")" + "\n\r";
            }



            return sql.TrimEnd(new char[] { '\n', '\r' });

        }

        //private string CreateImportDataQueryJFCExt(ImportTypeData importtypedata, List<lkImpField> dbColumnMapList, string importTable, ObservableCollection<importRow.Row> rows, int fromrowno, int torowno)
        //{
        //    string sql = "";
        //    string fld;
        //    string val;
        //    //lkImpField dgc = new lkImpField();
        //    int _fromrowno;
        //    int _torowno;
        //    //int rowindex;
        //    string fieldvalue;
        //    MappingRow mpr = new MappingRow();
                        
        //    if (fromrowno > rows.Count)
        //    {
        //        //Instead of throwing an error.
        //        //Logic set is, Get all rows
        //        _fromrowno = 0;
        //        _torowno = rows.Count;
        //    }
        //    else if ((torowno == 0) || (torowno > rows.Count))
        //    {
        //        //logic is if the no of rows asked is greater than
        //        //no of rows in the excel file. Get up to the last row.
        //        _fromrowno = fromrowno;
        //        _torowno = rows.Count;
        //    }
        //    else
        //    {
        //        _fromrowno = fromrowno;
        //        _torowno = torowno;
        //    }

        //    for (int rowcount = _fromrowno; rowcount < _torowno; rowcount++)
        //    {
        //        //INSERT INTO MAPPED COLUMNS ONLY.
        //        fld = "RECTYPE";
        //        val = "'D'";
        //        for (int fieldcount = 0; fieldcount < dbColumnMapList.Count; fieldcount++)
        //        {

        //            //dgc = dbColumnMapList[fieldcount];
        //            mpr = importtypedata.MAPPING[fieldcount];

        //            fld = fld + "," + mpr.MAPPEDCOLUMNNAME.ToUpper();

        //            importRow.Row row = rows[rowcount];

        //            fieldvalue = row[fieldcount.ToString()].ToString();

        //            fieldvalue = fieldvalue.Replace("'", "''").ToString();

        //            val = val + ",'" + fieldvalue + "'";
                    
        //        }

        //        sql = sql + "INSERT " + importTable + " (" + fld + ") values (" + val + ")" + "\n\r";
        //    }

        //    return sql.TrimEnd(new char[] { '\n', '\r' });

        //}

        //http://stackoverflow.com/questions/1348712/creating-a-sql-server-table-from-a-c-sharp-datatable
        
        public string GetCorrectNameNow(string clnName, bool IsColumnReq)
        {
            //DIRECT CHANGE IF NEEDED.....
            if (clnName == "MIDDLE" || clnName == "MIDDLE NAME" || clnName == "MNAME")
            {
                clnName = "MIDDLENAME";
            }
            if (clnName == "FIRST" || clnName == "FIRST NAME" || clnName == "FNAME")
            {
                clnName = "FIRSTNAME";
            }
            if (clnName == "LAST" || clnName == "LAST NAME" || clnName == "LNAME")
            {
                clnName = "LASTNAME";
            }
            if (clnName == "HOMEPHN" || clnName == "HONE PHONE" || clnName == "HOMEPHONE")
            {
                clnName = "HMPHN";
            }
            if (clnName == "CELLPHN" || clnName == "CELL PHONE" || clnName == "CELLPHONE")
            {
                clnName = "CELL";
            }
            if (clnName == "WORKPHONE" || clnName == "WORK PHONE" || clnName == "BUSPHN")
            {
                clnName = "BSPHN";
            }
            if (IsColumnReq)
                return "[" + clnName + "]";
            else
                return clnName;
        }
        
        public  string CreateTempTableBasedOnInput(ImportTypeData importtypedata, DataTable table, string tempTableName, List<lkImpField> dbColumnMapList)
        {
            
            StringBuilder sql = new StringBuilder();
            StringBuilder alterSql = new StringBuilder();
            string columnNameForTable = "";
            int rowindex;

            //table name with Create Statement...
            sql.AppendFormat("CREATE TABLE [{0}] (", tempTableName);

            for (int i = 0; i < table.Columns.Count; i++)
            {

                //check if this field is mapped
                if (RequiredMapping2(importtypedata.MAPPING, table.Columns[i].ColumnName.ToUpper(), out rowindex) == true)
                {
                    if (!string.IsNullOrEmpty(importtypedata.MAPPING[rowindex].MAPPEDCOLUMNNAME))
                    {
                        columnNameForTable = importtypedata.MAPPING[rowindex].MAPPEDCOLUMNNAME.ToUpper();
                    }
                    else
                    {
                        columnNameForTable = importtypedata.MAPPING[rowindex].FILECOLUMNNAME.ToUpper();
                    }
                    
                    columnNameForTable = GetCorrectNameNow(columnNameForTable,false);
                    
                }
                
                //sql.AppendFormat("\n\t[{0}]", table.Columns[i].ColumnName.ToUpper());
                sql.AppendFormat("\n\t[{0}]", columnNameForTable);

                switch (table.Columns[i].DataType.ToString().ToUpper())
                {
                    case "SYSTEM.INT16":
                        sql.Append(" smallint");
                        //isNumeric = true;
                        break;
                    case "SYSTEM.INT32":
                        sql.Append(" int");
                        //isNumeric = true;
                        break;
                    case "SYSTEM.INT64":
                        sql.Append(" bigint");
                        //isNumeric = true;
                        break;
                    case "SYSTEM.DATETIME":
                        sql.Append(" datetime");
                        //usesColumnDefault = false;
                        break;
                    case "SYSTEM.STRING":
                        sql.AppendFormat(" varchar({0})", table.Columns[i].MaxLength > 0 ? table.Columns[i].MaxLength : 100);
                        break;
                    case "SYSTEM.SINGLE":
                        sql.Append(" single");
                        //isNumeric = true;
                        break;
                    case "SYSTEM.DOUBLE":
                        sql.Append(" double");
                        //isNumeric = true;
                        break;
                    case "SYSTEM.DECIMAL":
                        sql.AppendFormat(" decimal(18, 6)");
                        //isNumeric = true;
                        break;
                    default:
                        sql.AppendFormat(" nvarchar({0})", table.Columns[i].MaxLength > 0 ? table.Columns[i].MaxLength : 100);
                        break;
                }

                if (!table.Columns[i].AllowDBNull)
                {
                    sql.Append(" NOT NULL");
                }

                sql.Append(",");

            }
            //Additional Columns...
            //sql.Append("[RECTYPE] [varchar](1) NULL, [ID] [int] NULL, [_PID] [int] NULL,[_CTD] [int] NULL,[_YTD] [int] NULL,[STATUS] [varchar](150) NULL,");
            
            if (!sql.ToString().Contains("FECCMTEID")) sql.Append(@" FECCMTEID varchar(100),");

            sql.Append("[RECTYPE] [varchar](1) NULL, [ID] [int] NULL, [_PID] [int] NULL,[_CTD] [money] NULL,[_YTD] [money] NULL,[STATUS] [varchar](150) NULL, [MatchFound] bit NULL,[_asOf] datetime,");

            //remove last , as we are done with the columns.
            sql.Remove(sql.Length - 1, 1);
            //Now end the ) as we need to finish the SQL
            sql.AppendFormat("\n);\n{0}", alterSql.ToString());
            //return now..
            return sql.ToString();

        }

        private string CreateImportHeaderQueryJFC(ImportTypeData importtypedata, List<lkImpField> dbColumnMapList, string importTable)
        {
            string sql;
            string fld;
            string val;
            bool fieldMapped;
            
            MappingRow mpr = new MappingRow();

            fld = "RECTYPE";
            val = "'H'";

            for (int c = 0; c < importtypedata.MAPPING.Count; c++)
            {
                mpr = importtypedata.MAPPING[c];

                if (!string.IsNullOrEmpty(mpr.MAPPEDCOLUMNNAME))
                {
                    fld = fld + "," + GetCorrectNameNow(mpr.MAPPEDCOLUMNNAME.ToUpper(), true);
                }
                else
                {
                    fld = fld + "," + GetCorrectNameNow(mpr.FILECOLUMNNAME.ToUpper(), true);
                }
                
                fieldMapped = true;

                val = val + (fieldMapped == true ? ",'1'" : ",'0'");
            }

            sql = "INSERT " + importTable + " (" + fld + ") values (" + val + ")";

            return sql;

        }

        private string CreateImportDataQueryJFC(ImportTypeData importtypedata, List<lkImpField> dbColumnMapList, string importTable, ObservableCollection<importRow.Row> rows, int fromrowno, int torowno)
        {
            string sql = "";
            string fld;
            string val;
            //lkImpField dgc = new lkImpField();
            int _fromrowno;
            int _torowno;
            string fieldvalue;
            MappingRow mpr = new MappingRow();

            if (fromrowno > rows.Count)
            {
                //Instead of throwing an error.
                //Logic set is, Get all rows
                _fromrowno = 0;
                _torowno = rows.Count;
            }
            else if ((torowno == 0) || (torowno > rows.Count))
            {
                //logic is if the no of rows asked is greater than
                //no of rows in the excel file. Get up to the last row.
                _fromrowno = fromrowno;
                _torowno = rows.Count;
            }
            else
            {
                _fromrowno = fromrowno;
                _torowno = torowno;
            }

            for (int rowcount = _fromrowno; rowcount < _torowno; rowcount++)
            {
                //INSERT INTO MAPPED COLUMNS ONLY.
                fld = "RECTYPE";
                val = "'D'";
                for (int fieldcount = 0; fieldcount < importtypedata.MAPPING.Count; fieldcount++)
                {
                    //dgc = dbColumnMapList[fieldcount];
                    mpr = importtypedata.MAPPING[fieldcount];

                    if (!string.IsNullOrEmpty(mpr.MAPPEDCOLUMNNAME))
                    {
                        fld = fld + "," + GetCorrectNameNow(mpr.MAPPEDCOLUMNNAME.ToUpper(),true);
                    }
                    else
                    {
                        fld = fld + "," + GetCorrectNameNow(mpr.FILECOLUMNNAME.ToUpper(),true);
                    }

                    importRow.Row row = rows[rowcount];

                    fieldvalue = row[fieldcount.ToString()].ToString();
                    
                    fieldvalue = fieldvalue.Replace("'", "''").ToString();

                    val = val + ",'" + fieldvalue + "'";
                }

                sql = sql + "INSERT " + importTable + " (" + fld + ") values (" + val + ")" + "\n\r";
            }

            return sql.TrimEnd(new char[] { '\n', '\r' });

        }

        public string CreateTempTableBasedOnInputForDataMatch(ImportTypeData importtypedata, DataTable table, string tempTableName, List<lkImpField> dbColumnMapList)
        {

            StringBuilder sql = new StringBuilder();
            StringBuilder alterSql = new StringBuilder();
            string columnNameForTable = "";
            int rowindex;

            //table name with Create Statement...
            sql.AppendFormat("CREATE TABLE [{0}] (", tempTableName);

            for (int i = 0; i < table.Columns.Count; i++)
            {
                //check if this field is mapped
                if (RequiredMapping2(importtypedata.MAPPING, table.Columns[i].ColumnName.ToUpper(), out rowindex) == true)
                {
                    if (!string.IsNullOrEmpty(importtypedata.MAPPING[rowindex].MAPPEDCOLUMNNAME))
                    {
                        columnNameForTable = importtypedata.MAPPING[rowindex].MAPPEDCOLUMNNAME;
                    }
                    else
                    {
                        //not mapped skip
                        continue;
                        //columnNameForTable = importtypedata.MAPPING[rowindex].FILECOLUMNNAME;
                    }

                    //columnNameForTable = "[" + columnNameForTable + "]";

                }

                //sql.AppendFormat("\n\t[{0}]", table.Columns[i].ColumnName.ToUpper());
                sql.AppendFormat("\n\t[{0}]", columnNameForTable);

                switch (table.Columns[i].DataType.ToString().ToUpper())
                {
                    case "SYSTEM.INT16":
                        sql.Append(" smallint");
                        //isNumeric = true;
                        break;
                    case "SYSTEM.INT32":
                        sql.Append(" int");
                        //isNumeric = true;
                        break;
                    case "SYSTEM.INT64":
                        sql.Append(" bigint");
                        //isNumeric = true;
                        break;
                    case "SYSTEM.DATETIME":
                        sql.Append(" datetime");
                        //usesColumnDefault = false;
                        break;
                    case "SYSTEM.STRING":
                        sql.AppendFormat(" varchar({0})", table.Columns[i].MaxLength > 0 ? table.Columns[i].MaxLength : 100);
                        break;
                    case "SYSTEM.SINGLE":
                        sql.Append(" single");
                        //isNumeric = true;
                        break;
                    case "SYSTEM.DOUBLE":
                        sql.Append(" double");
                        //isNumeric = true;
                        break;
                    case "SYSTEM.DECIMAL":
                        sql.AppendFormat(" decimal(18, 6)");
                        //isNumeric = true;
                        break;
                    default:
                        sql.AppendFormat(" nvarchar({0})", table.Columns[i].MaxLength > 0 ? table.Columns[i].MaxLength : 100);
                        break;
                }

                if (!table.Columns[i].AllowDBNull)
                {
                    sql.Append(" NOT NULL");
                }

                sql.Append(",");

            }
            if (!sql.ToString().Contains("FECCMTEID")) sql.Append(@" FECCMTEID varchar(100),");
            if (!sql.ToString().Contains("Email")) sql.Append(@" Email varchar(100),");
            if (!sql.ToString().Contains("Phone")) sql.Append(@" Phone varchar(100),");
            if (!sql.ToString().Contains("PREFIX")) sql.Append(@" PREFIX varchar(100),");
            if (!sql.ToString().Contains("FNAME")) sql.Append(@" FNAME varchar(100),");
            if (!sql.ToString().Contains("MNAME")) sql.Append(@" MNAME varchar(100),");
            if (!sql.ToString().Contains("LNAME")) sql.Append(@" LNAME varchar(100),");
            if (!sql.ToString().Contains("SUFFIX")) sql.Append(@" SUFFIX varchar(100),");
            if (!sql.ToString().Contains("STREET")) sql.Append(@" STREET varchar(100),");
            if (!sql.ToString().Contains("ADDR1")) sql.Append(@" ADDR1 varchar(100),");
            if (!sql.ToString().Contains("ZIP")) sql.Append(@" ZIP varchar(100),");
            sql.Append(@" M_PID int,M_FNAME varchar(30),M_MNAME varchar (20),M_LNAME varchar(80),
M_EMPLOYER varchar(70), M_OCCUPATION varchar(50), M_STREET varchar(60), M_ADDR1 varchar(60), M_CITY varchar(50),M_STATE varchar(2), M_ZIP varchar(5),");

            //remove last , as we are done with the columns.
            sql.Remove(sql.Length - 1, 1);
            //Now end the ) as we need to finish the SQL
            sql.AppendFormat("\n);\n{0}", alterSql.ToString());
            //return now..
            return sql.ToString();

        }
        private string CreateImportDataQueryDataMatch(ImportTypeData importtypedata, List<lkImpField> dbColumnMapList, string importTable, ObservableCollection<importRow.Row> rows, int fromrowno, int torowno)
        {
            string sql = "";
            string fld;
            string val;
            //lkImpField dgc = new lkImpField();
            int _fromrowno;
            int _torowno;
            string fieldvalue;
            MappingRow mpr = new MappingRow();

            if (fromrowno > rows.Count)
            {
                //Instead of throwing an error.
                //Logic set is, Get all rows
                _fromrowno = 0;
                _torowno = rows.Count;
            }
            else if ((torowno == 0) || (torowno > rows.Count))
            {
                //logic is if the no of rows asked is greater than
                //no of rows in the excel file. Get up to the last row.
                _fromrowno = fromrowno;
                _torowno = rows.Count;
            }
            else
            {
                _fromrowno = fromrowno;
                _torowno = torowno;
            }

            for (int rowcount = _fromrowno; rowcount < _torowno; rowcount++)
            {
                //INSERT INTO MAPPED COLUMNS ONLY.
                fld = "";
                val = "";
                for (int fieldcount = 0; fieldcount < importtypedata.MAPPING.Count; fieldcount++)
                {
                    //dgc = dbColumnMapList[fieldcount];
                    mpr = importtypedata.MAPPING[fieldcount];

                    if (!string.IsNullOrEmpty(mpr.MAPPEDCOLUMNNAME))
                    {
                        fld = fld + "," + mpr.MAPPEDCOLUMNNAME;
                    }
                    else
                    {
                        //not mapped skip
                        continue;
                        //fld = fld + "," + mpr.FILECOLUMNNAME;
                    }

                    importRow.Row row = rows[rowcount];

                    fieldvalue = row[fieldcount.ToString()].ToString();

                    fieldvalue = fieldvalue.Replace("'", "''").ToString();

                    val = val + ",'" + fieldvalue + "'";
                }

                sql = sql + "INSERT " + importTable + " (" + fld.Substring(1,fld.Length-1) + ") values (" + val.Substring(1, val.Length - 1) + ")" + "\n\r";
            }

            return sql.TrimEnd(new char[] { '\n', '\r' });

        }
        /// <summary>
        /// Gets the data in the form of rows in excel file.
        /// </summary>
        /// <param name="ds"></param>
        /// <returns></returns>
        private ObservableCollection<importRow.Row> GetExcelRowData(DataSet ds)
        {
            ObservableCollection<importRow.Row> rows = new ObservableCollection<importRow.Row>();

            List<string> columns = GetExcelColumnNames(ds);

            for (int rowcount = 0; rowcount < ds.Tables[0].Rows.Count; rowcount++)
            {
                importRow.Row row = new importRow.Row();
                List<string> data = new List<string>();
                data.Clear();
                data = ParseXlsLine(ds.Tables[0].Rows[rowcount]);
                if (ValidateEmptyRow(data))
                {
                    for (int columncount = 0; columncount < columns.Count; columncount++)
                    {
                        if (columns[columncount].ToLower().IndexOf("dte") >= 0 || columns[columncount].ToLower().IndexOf("date") >= 0)
                        {
                            DateTime dt;
                            if (DateTime.TryParse(data[columncount], out dt))
                            {
                                row[columncount.ToString()] = dt.ToShortDateString();
                            }
                            else
                            {
                                row[columncount.ToString()] = data[columncount];
                            }
                        }
                        else
                        {
                            //Date field does not exist
                            row[columncount.ToString()] = data[columncount];
                        }
                    }

                    rows.Add(row);
                }
            }

            return rows;
        }

        /// <summary>
        /// Gets the column names in the excel file.
        /// </summary>
        /// <param name="ds"></param>
        /// <returns></returns>
        private List<string> GetExcelColumnNames(DataSet ds)
        {
            List<string> columns = new List<string>();

            foreach (DataColumn dc in ds.Tables[0].Columns)
            {
                columns.Add(dc.ToString());
            }

            return columns;
        }

        /// <summary>
        /// This is used to Parse the row
        /// </summary>
        /// <param name="_row"></param>
        /// <returns></returns>
        private List<string> ParseXlsLine(DataRow _row)
        {
            try
            {
                List<string> cols = new List<string>();
                cols.Clear();
                if (_row != null)
                {
                    foreach (var col in _row.ItemArray)
                    {
                        if (col != null)
                            cols.Add(col.ToString());
                        else
                            cols.Add(string.Empty);
                    }
                }
                return cols;
            }
            catch (System.Exception ex)
            {

            }
            return null;
        }

        /// <summary>
        /// This is used to validate Empty row
        /// </summary>
        /// <param name="data"></param>
        /// <returns></returns>
        private Boolean ValidateEmptyRow(List<string> data)
        {
            foreach (string item in data)
            {
                if (!String.IsNullOrEmpty(item))
                    return true;
            }
            return false;
        }

        private string GetCode(string _source)
        {
            string[] _actualData = _source.Split('-');
            if (_actualData.Length >= 1)
            {
                return _actualData[0].ToString().Trim();
            }
            else
            {
                return "";
            }
        }

        private class Xmlresult
        {
            public string result { get; set; }
        }

        [HttpGet, Route("api/Import/EventCodes")]
        [Queryable(PageSize = 10)]
        public IQueryable<pmSPCEVNT> ImportEventCodes()
        {
            string _sql = "SELECT SPCEVNTID, EVNTCODE, EVNTCODE + ' - ' + EVNTDESC AS EVNTDESC FROM pmSPCEVNT";

            return _entity_crm.getContext().Database.SqlQuery<pmSPCEVNT>(_sql).AsQueryable();
        }

        [HttpGet, Route("api/Import/Threshold")]
        public string ImportThreshold()
        {
            if (_userSession.configItems.Where(a => a.Key == crmConstants.import_threshold).FirstOrDefault() != null)
            {
                return (_userSession.configItems.Where(a => a.Key == crmConstants.import_threshold).FirstOrDefault().Val.ToString());
            }
            else
            {
                return "";
            }
        }


        #region [[ Import Queue Export SQL Query]]

        const string _sql_exportQueueImportResult = @"
                   SELECT   Z.[RECTYPE], Z.[ID], Z.[PEOTYPE], Z.[PREFIX], Z.[FNAME], Z.[MNAME], Z.[LNAME], Z.[SUFFIX], Z.[EMPLOYER], Z.[OCCUPATION], Z.[CPREFIX], Z.[CFNAME], 
                            Z.[CMNAME], Z.[CLNAME], Z.[CSUFFIX], Z.[CTITLE],Z.[CORGANIZATION], Z.[ADDRTYPE], Z.[STREET], Z.[ADDR1], Z.[ADDR2], Z.[CITY], Z.[STATE], Z.[ZIP], Z.[PLUS4], Z.[HMPHONE], 
                            Z.[BSPHONE], Z.[FAX], Z.[CELLPHONE], Z.[EMAIL], Z.[URL], Z.[SOURCE], Z.[BATCHNO] , Z.[BATCHDTE] , Z.[AMT], Z.[MONYTYPE], Z.[FLAG1], Z.[FLAG2], Z.[FLAG3],
                            Z.[FLAG4], Z.[FLAG5], Z.[KEYWORD1], Z.[KEYWORD2], Z.[KEYWORD3], Z.[KEYWORD4], Z.[KEYWORD5], Z.[KEYWORD6], Z.[KEYWORD7], Z.[KEYWORD8], Z.[KEYWORD9], 
                            Z.[KEYWORD10], Z.[TXNNO], Z.[RECNO], Z.[STATUS], Z.[COMMENT] , Z.[CENTERCODE], Z.[RECVDTE], Z.[EVNTCODE1], Z.[EVNTCODE2], Z.[EVNTCODE3], Z.[ISSUE1], 
                            Z.[ISSUE2], Z.[ISSUE3], Z.[ACTIONCODE1], Z.[ACTIONCODE2], Z.[ACTIONCODE3], Z.[CAMPGNCODE], Z.[GIFTTYPE], Z.[MONYCODE], Z.[JOINDTE],Z.[ACTIVITY1],
                            Z.[ACTIVITY2],Z.[ACTIVITY3],Z.[ACTIVITY4],Z.[ACTIVITY5],Z.[ACTIVITY6],Z.[ACTIVITY7],Z.[GROUP1],Z.[GROUPSTATUS1],Z.[GROUP2],Z.[GROUPSTATUS2],Z.[GROUP3],
                            Z.[GROUPSTATUS3],Z.[GROUP4],Z.[GROUPSTATUS4],Z.[GROUP5],Z.[GROUPSTATUS5],Z.[CHANNEL1],Z.[cSTREET],Z.[cADDR1],Z.[cADDR2],Z.[cCITY],Z.[cSTATE],Z.[cZIP],
                            Z.[cPLUS4],Z.[cEMAIL],Z.[cHMPHONE],Z.[cBSPHONE],Z.[cFAX],Z.[cCELLPHONE],Z.[cINFSALUT],Z.[PRIMEMAIL],Z.[MAILSALUTATION],Z.[FLAG6], Z.[FLAG7], Z.[FLAG8], 
                            Z.[FLAG9], Z.[FLAG10],Z.[CLUBCODE],Z.[CLUBSTATUS],Z.[CANDIDATE],Z.[SOLICITOR],Z.[RNEWDTE],Z.[CDCODE],Z.[LDCODE],Z.[SDCODE],Z.[COMMITMENT],Z.[RECRUITERNO],
                            Z.[CKNO],Z.[EARMARKED],Z.[mTRACKNO],Z.[ACCTNO],Z.[ASSISTANT],Z.[FECCMTEID],Z.[INDUSTRY],Z.[INFSALUT],Z.[MAILNAME],Z.[SALUTATION],Z.[SPOUSENAME],Z.[TITLE],
                            Z.[pTRACKNO],Z.[FUNDCODE],Z.[DOB],Z.[ASSTBSPHONE],Z.[ASSTEMAIL]  
                   FROM     CRMIMPORTQUE C 
                   JOIN     jtCRMIMPORTQUE JT ON C.CRMIMPORTQUEID = JT.CRMIMPORTQUEID
                   JOIN     CRMIMPORTPROCESSING Z ON C.PROCESSDATE = Z.PROCESSDATE AND C.TABLENAME = Z.TABLENAME AND Z.ROWID = JT.ROWID
                   WHERE    C.crmIMPORTQUEID = {0} AND Z.RECTYPE = 'D'
                   ORDER BY Z.RECTYPE DESC, Z.STATUS ";

        const string _sql_exportQueueImportEventResult = @"
                   SELECT 
                            Z.[RECTYPE],ISNULL(Z.[ID],0) as ID,Z.[PEOTYPE],Z.[PREFIX],Z.[FNAME],Z.[MNAME],Z.[LNAME],Z.[SUFFIX],Z.[EMPLOYER],Z.[OCCUPATION],
                            Z.[INFSALUT],Z.[SALUTATION],Z.[SPOUSENAME],Z.[ADDRTYPE],Z.[STREET],Z.[ADDR1],Z.[ADDR2],Z.[CITY],Z.[STATE],Z.[ZIP],Z.[PLUS4],Z.[HMPHONE],
                            Z.[BSPHONE],Z.[FAX],Z.[CELLPHONE],Z.[EMAIL],Z.[EVNTCODE],Z.[EVNTSTATUS],ISNULL(Z.[ATTENDDTE],'1900-01-01') AS ATTENDDTE,Z.[TABLENO],
                            Z.[ANSWER01],Z.[ANSWER02],Z.[ANSWER03],Z.[ANSWER04],Z.[ANSWER05],Z.[ANSWER06],Z.[ANSWER07],Z.[ANSWER08],Z.[ANSWER09],Z.[ANSWER10],
                            Z.[ANSWER11],Z.[ANSWER12],Z.[ANSWER13],Z.[ANSWER14],Z.[ANSWER15],Z.[ANSWER16],Z.[ANSWER17],Z.[ANSWER18],Z.[ANSWER19],Z.[ANSWER20],
                            Z.[ANSWER21],Z.[ANSWER22],Z.[ANSWER23],Z.[ANSWER24],Z.[ANSWER25],ISNULL(Z.[TXNNO],0) AS TXNNO,ISNULL(Z.[RECNO],0) AS RECNO,Z.[STATUS]
                    FROM     CRMIMPORTQUE C 
                    JOIN     jtCRMIMPORTQUE JT ON C.CRMIMPORTQUEID = JT.CRMIMPORTQUEID
                    JOIN     zEVENTIMPORTPROCESSING Z ON C.PROCESSDATE = Z.PROCESSDATE AND C.TABLENAME = Z.TABLENAME AND Z.ROWID = JT.ROWID
                    WHERE    C.crmIMPORTQUEID = {0} AND Z.RECTYPE = 'D'
                    ORDER BY Z.RECTYPE DESC, Z.STATUS";

        const string _sql_exportQueueImportTreasuryTxnResult = @"
                   SELECT 
                            Z.[RECTYPE],ISNULL(Z.[ENTITYID],0) as ENTITYID,Z.[ENTITYTYPE],Z.[ORGNAME],Z.[PREFIX],Z.[LNAME],Z.[FNAME],Z.[MNAME],Z.[SUFFIX],
                            Z.[STREET1],Z.[STREET2],Z.[CITY],Z.[STATE],Z.[ZIP],Z.[PLUS4],Z.[PHONE],Z.[FAX],Z.[CELL],Z.[EMAIL],Z.[CONTACT],Z.[EMPLOYER],Z.[OCCUPATION],
                            Z.[TAXID],Z.[VENDACCTID],Z.[FECCMTEID],ISNULL(Z.[TXNNO],0) AS TXNNO,ISNULL(Z.[RECNO],0) AS RECNO,Z.[STATUS],
                            ISNULL(Z.[TXNDTE],'1900-01-01') AS TXNDTE,ISNULL(Z.[AMT],0) AS AMT,Z.[CHECKNO],Z.[TXNTYPE],Z.[FUNDCODE],Z.[LINENO],Z.[COMMENT],Z.[FEC_DESCRIPTION],
                            Z.[ISMEMO],Z.[MEMOTXT],Z.[TRANSCAT],Z.[TXNCODE],Z.[PAYTYPE],Z.[ELECTION],Z.[ELECTYR],Z.[ELECTOTHER],Z.[LINKTXNID],Z.[GLACCT_1],Z.[GLAMT_1],
                            Z.[GLACCT_2],Z.[GLAMT_2],Z.[GLACCT_3],Z.[GLAMT_3],Z.[IS1099]
                    FROM     CRMIMPORTQUE C 
                    JOIN     jtCRMIMPORTQUE JT ON C.CRMIMPORTQUEID = JT.CRMIMPORTQUEID
                    JOIN     zEXPIMPORTProcessing Z ON C.PROCESSDATE = Z.PROCESSDATE AND C.TABLENAME = Z.TABLENAME AND Z.ROWID = JT.ROWID
                    WHERE    C.crmIMPORTQUEID = {0} AND Z.RECTYPE = 'D'
                    ORDER BY Z.RECTYPE DESC, Z.STATUS";

        const string _sql_exportQueueImportTreasuryVendorResult = @"
                   SELECT 
                            Z.[RECTYPE],ISNULL(Z.[ENTITYID],0) as ENTITYID,Z.[ENTITYTYPE],Z.[ORGNAME],Z.[PREFIX],Z.[LNAME],Z.[FNAME],Z.[MNAME],Z.[SUFFIX],
                            Z.[STREET1],Z.[STREET2],Z.[CITY],Z.[STATE],Z.[ZIP],Z.[PLUS4],Z.[PHONE],Z.[FAX],Z.[CELL],Z.[EMAIL],Z.[CONTACT],Z.[EMPLOYER],Z.[OCCUPATION],
                            Z.[TAXID],Z.[VENDACCTID],Z.[FECCMTEID],ISNULL(Z.[TXNNO],0) AS TXNNO,ISNULL(Z.[RECNO],0) AS RECNO,Z.[STATUS]                            
                    FROM     CRMIMPORTQUE C 
                    JOIN     jtCRMIMPORTQUE JT ON C.CRMIMPORTQUEID = JT.CRMIMPORTQUEID
                    JOIN     zEXPIMPORTProcessing Z ON C.PROCESSDATE = Z.PROCESSDATE AND C.TABLENAME = Z.TABLENAME AND Z.ROWID = JT.ROWID
                    WHERE    C.crmIMPORTQUEID = {0} AND Z.RECTYPE = 'D'
                    ORDER BY Z.RECTYPE DESC, Z.STATUS";

        const string _sql_exportQueueImportAdjustmentResult = @"
                   SELECT 
                            Z.[RECTYPE],ISNULL(Z.[ID],0) as ID,Z.[STATUS],Z.[PEOTYPE],Z.[PREFIX],Z.[LNAME],Z.[FNAME],Z.[MNAME],Z.[SUFFIX],Z.[EMPLOYER],Z.[OCCUPATION],
                            Z.[SPOUSENAME],Z.[ADDRTYPE],Z.[STREET],Z.[ADDR1],Z.[ADDR2],Z.[CITY],Z.[STATE],Z.[ZIP],Z.[PLUS4],Z.[HMPHONE],Z.[FAX],Z.[CELLPHONE],Z.[EMAIL],
                            ISNULL(Z.[MID],0) as MID,ISNULL(Z.[ADJDTE],'1900-01-01') AS ADJDTE,Z.[SRCECODE],ISNULL(Z.[AMT],0) AS AMT,ISNULL(Z.[TRACKNO],0) AS TRACKNO,ISNULL(Z.[RECNO],0) AS RECNO,
                            Z.[INFSALUT],Z.[SALUTATION],Z.[EARMARKED]                             
                    FROM     CRMIMPORTQUE C 
                    JOIN     jtCRMIMPORTQUE JT ON C.CRMIMPORTQUEID = JT.CRMIMPORTQUEID
                    JOIN     zMONYADJIMPORTPROCESSING Z ON C.PROCESSDATE = Z.PROCESSDATE AND C.TABLENAME = Z.TABLENAME AND Z.ROWID = JT.ROWID
                    WHERE    C.crmIMPORTQUEID = {0} AND Z.RECTYPE = 'D'
                    ORDER BY Z.RECTYPE DESC, Z.STATUS";
         
        const string _sql_exportQueueImportUpdateResult = @"
                   SELECT   Z.[RECTYPE], Z.[ID], Z.[RECNO], Z.[TXNNO], Z.[AMT], Z.[SOURCE], Z.[BATCHDTE], Z.[BATCHNO], Z.[FUNDCODE], 
                            Z.[CENTERCODE], Z.[MONYTYPE], Z.[CKNO], Z.[RECVDTE], Z.[mTRACKNO], Z.[TRACKAMT], Z.[REFERENCEID], Z.[STATUS]
                   FROM     CRMIMPORTQUE C 
                   JOIN     jtCRMIMPORTQUE JT ON C.CRMIMPORTQUEID = JT.CRMIMPORTQUEID
                   JOIN     CRMIMPORTPROCESSING Z ON C.PROCESSDATE = Z.PROCESSDATE AND C.TABLENAME = Z.TABLENAME AND Z.ROWID = JT.ROWID
                   WHERE    C.crmIMPORTQUEID = {0} AND Z.RECTYPE = 'D'
                   ORDER BY Z.RECTYPE DESC, Z.STATUS ";
        #endregion

        [HttpPost, Route("crm/api/Import/ExportImpQueueKey")]
        public string ExportImpQueueKey(string searchText)
        {
            string key_ = System.Guid.NewGuid().ToString();
            HttpRuntime.Cache.Insert(key_, searchText, null, DateTime.UtcNow.Add(new TimeSpan(1, 0, 1)), System.Web.Caching.Cache.NoSlidingExpiration);

            return key_;

        }

        [HttpGet, Route("crm/api/Import/ExportImpQueue")]
        public string ExportImpQueue(string key)
        {
            string param = (string)HttpRuntime.Cache[key];

            //0 - Import QUEUE Id
            //1 - import type id
            string[] values = param.Split(new char[] { '$' });
            int importQueueId = Int32.Parse(values[0]);
            int importTypeId = Int32.Parse(values[1]);

            string sqlQuery = "";
            if (importTypeId == 6)
            {
                sqlQuery = _sql_exportQueueImportEventResult;
            }
            else if (importTypeId == 30)
            {
                sqlQuery = _sql_exportQueueImportTreasuryVendorResult;
            }
            else if (importTypeId == 31)
            {
                sqlQuery = _sql_exportQueueImportTreasuryTxnResult;
            }
            else if (importTypeId == 21 || importTypeId == 22 || importTypeId == 23 || importTypeId == 24)
            {
                sqlQuery = _sql_exportQueueImportAdjustmentResult;
            }
            else if (importTypeId == 8)
            {
                sqlQuery = _sql_exportQueueImportUpdateResult;
            }
            else
            {
                sqlQuery = _sql_exportQueueImportResult;
            }

            string sToday = String.Format("{0:yyyyMMMdd__hh_mm_ss tt}", DateTime.Now);
            string moduleName = "IMPORT";
            string fileName = moduleName + "_" + System.Guid.NewGuid() + "_" + _userSession.UserName + "_" + sToday;

            cl_query.create_a_sqlLog(sqlQuery.Trim(), "Import Module(Before ExportImpQueue)", importQueueId.ToString(), crmSession.UID().Value, 0, "", "", _entity_crm, 0);

            DataSet ds = null;

            System.Diagnostics.Stopwatch stopWatch = new System.Diagnostics.Stopwatch();
            stopWatch.Start();

            ds = _reportService.get_dataset_w_sql__single(string.Format(sqlQuery, importQueueId), "ImportQueueResult");

            stopWatch.Stop();
            #region [[ Save the SQL in Log Table ]]

            cl_query.create_a_sqlLog(
                       sqlQuery.Trim(),
                       "Import Module(ExportImpQueue)",
                       importQueueId.ToString(),
                       crmSession.UID().Value,
                       0,
                       "",
                       "",
                       _entity_crm,
                       Convert.ToSingle(stopWatch.ElapsedMilliseconds / 1000.0));

            #endregion

            if (ds != null)
            {
                string key_ = System.Guid.NewGuid().ToString();
                HttpRuntime.Cache.Insert(key_, ds, null, DateTime.UtcNow.Add(new TimeSpan(1, 0, 1)), System.Web.Caching.Cache.NoSlidingExpiration);
                return key_;
            }
            else
            {
                return null;
            }

        }

        [HttpGet, Route("api/Import/EarmarkImportQueue")]
        public string EarmarkImportQueue()
        {
            if (_userSession.configItems.Where(a => a.Key == crmConstants.earmark_queue_import).FirstOrDefault() != null)
            {
                return (_userSession.configItems.Where(a => a.Key == crmConstants.earmark_queue_import).FirstOrDefault().Val.ToString());
            }
            else
            {
                return "";
            }
        }


        [HttpGet, Route("api/Import/SRCEIMPERRORFILE")]
        public string SRCEIMPERRORFILE(string key)
        {
            srceImportErrorData data = (srceImportErrorData)HttpRuntime.Cache[key];
            var filePath = Path.Combine(System.Web.Hosting.HostingEnvironment.MapPath("~/Uploads"), data.fileName);
            
            ImportExcelXls impExcel = new ImportExcelXls(filePath, true, true, true, data.WORKSHEET);
            DataSet ds = impExcel.result;
            if (ds != null)
            {
                try
                {
                    ds.Tables[0].Columns.Add("Status", typeof(String));
                    int StatusIdx = ds.Tables[0].Columns.Count - 1;
                    foreach (DataRow row in ds.Tables[0].Rows)
                    {
                        string val = row[data.rowindex].ToString();
                        int countsrcInOtherIList = data.srcInOtherIList.Where(s => s.SRCECODE == val).ToList().Count;
                        if (countsrcInOtherIList > 0)
                        {
                            row[StatusIdx] = "Invalid: Already exists in Crimson and under different Initiative/Package.";
                        }
                        else
                        {
                            int countsrc = data.srcList.Where(s => s.SRCECODE == val).ToList().Count;
                            if (countsrc > 0)
                            {
                                row[StatusIdx] = "Invalid: Already exists in Crimson.";
                            }
                            else
                            {
                                if (!string.IsNullOrEmpty(val))
                                {
                                    row[StatusIdx] = "Valid";
                                }                                
                            }
                        }
                    }
                    string key_ = System.Guid.NewGuid().ToString();
                    HttpRuntime.Cache.Insert(key_, ds, null, DateTime.UtcNow.Add(new TimeSpan(1, 0, 1)), System.Web.Caching.Cache.NoSlidingExpiration);
                    return key_;
                }
                catch(Exception ex)
                {
                    return null;
                }
            }
            else
            {
                return null;
            }
        }

        [HttpPost, Route("crm/api/Import/massAdjustmentForReview")]
        public MessageBoard massAdjustmentForReview(ImportTypeData importtypedata)
        {
            MessageBoard msgBoard = new MessageBoard();
            msgBoard.status = false;
            msgBoard.message = "";

            string sql;
            string importTable = "";
            List<string> columns;
            ObservableCollection<importRow.Row> rows;

            try
            {
                // Open Excel
                var filePath = Path.Combine(System.Web.Hosting.HostingEnvironment.MapPath("~/Uploads"), importtypedata.UPLOADEDFILENAME);
                //ImportExcelXls impExcel = new ImportExcelXls(filePath, true, true);
                ImportExcelXls impExcel = new ImportExcelXls(filePath, true, true, true, importtypedata.WORKSHEET);
                DataSet ds = impExcel.result;
                //Get columns from the excel file.
                columns = GetExcelColumnNames(ds);
                //Get data in rows from the excel file.
                rows = GetExcelRowData(ds);

                //normal user only allow up to 2500
                if (!session.userSession.is_sysAdmin && rows.Count() > 2500)
                {
                    msgBoard.status = false;
                    msgBoard.message = "Only up to 2500 records are allowed to import at a time.";
                    return msgBoard;
                }

                //Get the list of columns for the import type
                List<lkImpField> dbColumnMapList = GetImportField(importtypedata.IMPTYPEID);

                //validate excel value
                msgBoard = excelDataValidation(importtypedata, dbColumnMapList, rows);
                if (msgBoard.status == false)
                {
                    return msgBoard;
                }

                //Temp table name
                importTable = GenerateImportTableName();

                //Create import table query
                sql = "SELECT TOP 100 * INTO " + importTable + " FROM zMONYADJIMPORTPROCESSING \n\r TRUNCATE TABLE " + importTable;

                //Append Header
                sql = sql + "\n\r" + CreateImportHeaderQuery(importtypedata, dbColumnMapList, importTable);

                sql = sql + "\n\r" + CreateImportDataQuery(importtypedata, dbColumnMapList, importTable, rows, 0, rows.Count);

                cl_query.create_a_sqlLog(sql.Trim(), "Import Module(Mass Adjustment Review)", importTable, crmSession.UID().Value, rows.Count, "", "", _entity_crm, 0);

                //
                System.Diagnostics.Stopwatch stopWatch = new System.Diagnostics.Stopwatch();
                stopWatch.Start();

                // Execute 
                if (sql != String.Empty)
                {
                    msgBoard = _importService.process_sql(sql, "Import", msgBoard);
                }

                stopWatch.Stop();
                #region [[ Save the SQL in Log Table ]]

                cl_query.create_a_sqlLog(
                           sql.Trim(),
                           "Import Module(Mass Adjustment Review)",
                           importTable,
                           crmSession.UID().Value,
                           rows.Count,
                           "",
                           "",
                           _entity_crm,
                           Convert.ToSingle(stopWatch.ElapsedMilliseconds / 1000.0));

                #endregion
                //processing for review
                if (msgBoard.status)
                {
                    string _sql = string.Format("exec [dbo].[massAdjustmentValidate] '{0}'", importTable);
                    msgBoard = _entity_crm.getContext().Database.SqlQuery<MessageBoard>(_sql).FirstOrDefault();
                    msgBoard.tempTableName = importTable;
                }                

            }
            catch (Exception ex)
            {
                msgBoard.message = msgBoard.message + ex.Message;
                msgBoard.status = false;
            }

            return msgBoard;
        }

        [HttpGet, Route("api/Import/massAdjustmentResult/{datastring}/{importTable}")]
        public List<ImportStep5ViewModelMassAdjustment> massAdjustmentResult(string datastring, string importTable)
        {
            List<ImportStep5ViewModelMassAdjustment> impData;
            string sql = string.Format(sql_select_fields_massAdjustmentResult.Replace("SELECT DISTINCT", "SELECT DISTINCT TOP 20 "), importTable);
            if (datastring == "matched")
            {
                sql += " WHERE Z.STATUS=''MATCHED'' AND Z.RECTYPE = ''D''";
            }
            else if(datastring == "adjusted")
            {
                sql += " WHERE Z.STATUS=''ALREADY ADJUSTED'' AND Z.RECTYPE = ''D''";
            }

            impData = _importService.read_all_massAdjustment_import_data(sql);

            return impData;
        }

        [HttpGet, Route("api/Import/massAdjustmentResultUnmatched/{importTable}")]
        public List<ImportStep5ViewModelMassAdjustmentUnmatched> massAdjustmentResultUnmatched(string importTable)
        {
            List<ImportStep5ViewModelMassAdjustmentUnmatched> impData;
            string sql = string.Format(sql_select_fields_massAdjustmentResultUnmatched.Replace("SELECT", "SELECT TOP 20 ").Replace("'","''"), importTable);
            impData = _importService.read_all_massAdjustmentUnmatched_import_data(sql);

            return impData;
        }
        [HttpGet, Route("api/Import/massAdjustmentProcessedPreview/{importTable}")]
        public List<ImportStep5ViewModelMassAdjustmentProcessed> massAdjustmentProcessedPreview(string importTable)
        {
            List<ImportStep5ViewModelMassAdjustmentProcessed> impData;
            string sql = string.Format(sql_select_fields_massAdjustmentResultProcessed.Replace("SELECT", "SELECT TOP 20 ").Replace("'", "''"), importTable);
            impData = _importService.read_all_massAdjustmentProcessed_import_data(sql);

            return impData;
        }
        [HttpGet, Route("api/Import/massAdjustment/getCounts/{importTable}")]
        public MassAdjustmentCounts massAdjustmentGetCounts(string importTable)
        {
            MassAdjustmentCounts CountData;
            string sql = string.Format(@"
SELECT 
(SELECT COUNT(*) FROM {0} WHERE RECTYPE ='D' AND STATUS ='MATCHED') AS Matched,
(SELECT COUNT(*) FROM {0} WHERE RECTYPE ='D' AND STATUS ='NOT MATCHED') AS UnMatched,
(SELECT COUNT(*) FROM {0} WHERE RECTYPE ='D' AND STATUS ='ALREADY ADJUSTED') AS Adjusted", importTable);
            CountData = _entity_crm.getContext().Database.SqlQuery<MassAdjustmentCounts>(sql).FirstOrDefault();
            return CountData;
        }

        [HttpPost, Route("api/Import/massAdjustment/ImportNow")]
        public MessageBoard massAdjustmentImportNow(ImportTypeData importtypedata)
        {
            MessageBoard msgBoard = new MessageBoard();
            
            if (importtypedata.ADJTYPEID <= 0)
            {
                msgBoard.message = "A valid Adjustment Type is required";
                msgBoard.status = false;
                return msgBoard;
            }
            
            try
            {
                string _sql = string.Format("EXEC massAdjustmentImportNow '{0}',{1}", importtypedata.IMPORTTABLENAME, importtypedata.ADJTYPEID);
                cl_query.create_a_sqlLog(_sql.Trim(), "Import Module(Mass Adjustment Before Import Step)", importtypedata.IMPORTTABLENAME, crmSession.UID().Value, 0, "", "", _entity_crm, 0);

                System.Diagnostics.Stopwatch stopWatch = new System.Diagnostics.Stopwatch();
                stopWatch.Start();

                // Execute
                if (_sql != String.Empty)
                {
                    msgBoard = _entity_crm.getContext().Database.SqlQuery<MessageBoard>(_sql).FirstOrDefault();
                    msgBoard.tempTableName = importtypedata.IMPORTTABLENAME;
                }

                stopWatch.Stop();
                #region [[ Save the SQL in Log Table ]]
                cl_query.create_a_sqlLog(
                           _sql.Trim(),
                           "Import Module(Mass Adjustment Import Step)",
                           importtypedata.IMPORTTABLENAME,
                           crmSession.UID().Value,
                           0,
                           "",
                           "",
                           _entity_crm,
                           Convert.ToSingle(stopWatch.ElapsedMilliseconds / 1000.0));

                #endregion
            }
            catch (System.Exception ex)
            {
                msgBoard.message = "There is some problem in processing Import (ImportData step : Code 763) request. Please try again later. Details: " + ex.Message;
                msgBoard.status = false;
            }

            return msgBoard;
        }


        [HttpPost, Route("crm/api/Import/dataMatchExport")]
        public string dataMatchExport(ImportTypeData importtypedata)
        {
            MessageBoard msgBoard = new MessageBoard();
            msgBoard.status = false;
            msgBoard.message = "";

            string sql;
            string importTable = "";
            List<string> columns;
            ObservableCollection<importRow.Row> rows;

            try
            {
                // Open Excel
                var filePath = Path.Combine(System.Web.Hosting.HostingEnvironment.MapPath("~/Uploads"), importtypedata.UPLOADEDFILENAME);
                //ImportExcelXls impExcel = new ImportExcelXls(filePath, true, true);
                ImportExcelXls impExcel = new ImportExcelXls(filePath, true, true, true, importtypedata.WORKSHEET);
                DataSet ds = impExcel.result;
                //Get columns from the excel file.
                columns = GetExcelColumnNames(ds);
                //Get data in rows from the excel file.
                rows = GetExcelRowData(ds);

                //Get the list of columns for the import type
                List<lkImpField> dbColumnMapList = GetImportField(importtypedata.IMPTYPEID);

                //Temp table name
                importTable = GenerateImportTableName();

                //Create import table query
                sql = CreateTempTableBasedOnInputForDataMatch(importtypedata, ds.Tables[0], importTable, dbColumnMapList);                

                sql = sql + "\n\r" + CreateImportDataQueryDataMatch(importtypedata, dbColumnMapList, importTable, rows, 0, rows.Count);
                cl_query.create_a_sqlLog(sql.Trim(), "Import Module(Data Match)", importTable, crmSession.UID().Value, rows.Count, "", "", _entity_crm, 0);

                //
                System.Diagnostics.Stopwatch stopWatch = new System.Diagnostics.Stopwatch();
                stopWatch.Start();

                // Execute 
                if (sql != String.Empty)
                {
                    msgBoard = _importService.process_sql(sql, "Import", msgBoard);
                }

                stopWatch.Stop();
                #region [[ Save the SQL in Log Table ]]

                cl_query.create_a_sqlLog(
                           sql.Trim(),
                           "Import Module(Data Match)",
                           importTable,
                           crmSession.UID().Value,
                           rows.Count,
                           "",
                           "",
                           _entity_crm,
                           Convert.ToSingle(stopWatch.ElapsedMilliseconds / 1000.0));

                #endregion
                if (msgBoard.status)
                {
                    string _sql = string.Format("exec dbo.dataMatchProcessing '{0}'", importTable);
                    DataSet ds2 = _reportService.get_dataset_w_sql__single(_sql, "Import");
                    string key_ = System.Guid.NewGuid().ToString();
                    HttpRuntime.Cache.Insert(key_, ds2, null, DateTime.UtcNow.Add(new TimeSpan(1, 0, 1)), System.Web.Caching.Cache.NoSlidingExpiration);
                    return key_;
                }
            }
            catch (Exception ex)
            {
                return null;
            }

            return null;
        }
    }
}
