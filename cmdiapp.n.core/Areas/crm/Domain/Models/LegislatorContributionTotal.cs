﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Web;

namespace cmdiapp.n.core.Areas.crm.Domain.Models
{
    [NotMapped]
    public class LegislatorContributionTotal
    {
        public int entityId { get; set; }
        public string entityCode { get; set; }
        public decimal total { get; set; }
        public string orgName { get; set; }
        public string electionYear { get; set; }
    }

    [NotMapped]
    public class LegislatorDirectContributionTotal : LegislatorContributionTotal
    {
        public string electionCode { get; set; }
        public string electionCodeDescription { get; set; }
        public int electionCodeId { get; set; }        
    }
}