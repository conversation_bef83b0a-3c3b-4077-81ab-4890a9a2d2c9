﻿using System;
using System.Linq;
using System.Xml.Serialization;
using System.Data;
using System.Data.SqlClient;
using System.Xml.Linq;

using cmdiapp.n.core.Core;
using cmdiapp.n.core.Library;
using System.Threading.Tasks;
using System.Threading;

namespace cmdiapp.n.core.Areas.dashboard.Domain.Models
{
    public static class DashboardLib
    {
        // Command timeout after 30 minutes.
        private const int CommandTimeout = 1800000;

        public static dynamic runSqlXmlFunc(
            string dbConnStr,
            string sqlFunc,
            string sqlParams)
        {
            try
            {
                using (var connection = new SqlConnection(dbConnStr))
                using (var command = GetCommand(connection, SqlCallable.Function, sqlFunc, sqlParams))
                {
                    connection.Open();
                    var result = (string)command.ExecuteScalar();
                    connection.Close();
                    return ConvertToXmlAndHandleAccess(result);
                }
            }
            catch (Exception ex)
            {
                string errorSource = "";
                try
                {
                    errorSource = dbConnStr.Substring(dbConnStr.IndexOf("Initial Catalog=")).Replace("Initial Catalog=", "");
                    errorSource = errorSource.Substring(0, errorSource.IndexOf(";"));
                    errorSource = $"({errorSource}.{sqlFunc}({sqlParams}))";
                }
                catch
                {
                    errorSource = $"({sqlFunc}({sqlParams}))";
                }
                util.recordInErrorLog($"Exception encountered when retrieving " +
                    $"dashboard data {ex.GetType().Name}: {ex.Message} {errorSource}");
                return null;
            }
        }

        public async static Task<dynamic> RunSqlXmlFuncAsync(
            string dbConnStr,
            string sqlFunc,
            string sqlParams,
            CancellationToken cancellationToken = default)
        {
            try
            {
                using (var connection = new SqlConnection(dbConnStr))
                using (var command = GetCommand(connection, SqlCallable.Function, sqlFunc, sqlParams))
                {
                    connection.Open();
                    var result = await command.ExecuteScalarAsync(cancellationToken);
                    connection.Close();
                    return ConvertToXmlAndHandleAccess((string)result);
                }
            }
            catch (OperationCanceledException canceledEx) when (canceledEx.CancellationToken == cancellationToken)
            {
                return null;
            }
            catch (Exception ex)
            {
                // ignore dashboard task cancel error
                if (sqlFunc.Contains("dsb_") && ex.GetType().Name == "TaskCanceledException" && ex.Message == "A task was canceled.") return null;

                string errorSource = "";
                try
                {
                    errorSource = dbConnStr.Substring(dbConnStr.IndexOf("Initial Catalog=")).Replace("Initial Catalog=", "");
                    errorSource = errorSource.Substring(0, errorSource.IndexOf(";"));
                    errorSource = $"({errorSource}.{sqlFunc}({sqlParams}))";
                }
                catch
                {
                    errorSource = $"({sqlFunc}({sqlParams}))";
                }
                util.recordInErrorLog($"Exception encountered when retrieving " +
                    $"dashboard data {ex.GetType().Name}: {ex.Message} {errorSource}");
                return null;
            }
        }

        public async static Task<dynamic> RunSqlXmlProcAsync(
            string connectionString,
            string procName,
            string parameters,
            CancellationToken cancellationToken = default)
        {
            try
            {
                var outputParameter = new SqlParameter("@result", SqlDbType.Xml)
                { 
                    Direction = ParameterDirection.Output
                };
                using (var connection = new SqlConnection(connectionString))
                using (var command = GetCommand(
                    connection,
                    SqlCallable.Procedure,
                    procName,
                    parameters,
                    outputParameter))
                {
                    connection.Open();
                    await command.ExecuteNonQueryAsync(cancellationToken);
                    connection.Close();
                    var result = (string)outputParameter.Value;
                    return ConvertToXmlAndHandleAccess(result);
                }
            }
            catch (OperationCanceledException canceledEx) when (canceledEx.CancellationToken == cancellationToken)
            {
                return null;
            }
            catch (Exception ex)
            {
                string errorSource = "";
                try
                {
                    errorSource = connectionString.Substring(connectionString.IndexOf("Initial Catalog=")).Replace("Initial Catalog=", "");
                    errorSource = errorSource.Substring(0, errorSource.IndexOf(";"));
                    errorSource = $"({errorSource}.{procName}({parameters}))";
                }
                catch
                {
                    errorSource = $"({procName}({parameters}))";
                }
                util.recordInErrorLog($"Exception encountered when retrieving " +
                    $"dashboard data {ex.GetType().Name}: {ex.Message} {errorSource}");
                return null;
            }
        }

        private static SqlCommand GetCommand(
            SqlConnection connection,
            SqlCallable callableType,
            string objectName,
            string parameters,
            SqlParameter returnParameter = null)
        {
            string callPrefix = GetObjectCallPrefix(objectName, callableType);
            string[] paramValues = parameters?.Split(
                        new char[1] { '.' }, StringSplitOptions.RemoveEmptyEntries) ?? new string[0];
            string[] paramNames = paramValues.Select((v, i) => $"@{i}").ToArray();

            string commandText = $"{callPrefix}{string.Join(",", paramNames)}";

            var command = new SqlCommand(commandText, connection)
            {
                CommandTimeout = CommandTimeout,
            };
            var sqlParameters = paramNames.Select((name, i) => new SqlParameter(name, paramValues[i])).ToList();

            if (returnParameter != null)
            {
                sqlParameters.Add(returnParameter);
                command.CommandText += $",{returnParameter.ParameterName} OUTPUT";
            }

            if (sqlParameters.Any())
            {
                command.Parameters.AddRange(sqlParameters.ToArray());
            }

            command.CommandText += GetObjectCallSuffix(callableType);

            return command;
        }

        private static string GetObjectCallPrefix(string objectName, SqlCallable callableType)
        {
            if (callableType == SqlCallable.Procedure)
            {
                return $"EXEC {objectName} ";
            }
            else
            {
                return $"SELECT {objectName}(";
            }
        }

        private static string GetObjectCallSuffix(SqlCallable callableType)
        {
            return callableType == SqlCallable.Function ? ")" : "";
        }

        private static dynamic ConvertToXmlAndHandleAccess(
            string xmlString,
            CancellationToken cancellationToken = default)
        {
            bool xDocIsChanged = false;
            XDocument xDoc = XDocument.Parse(xmlString);
            XNode accessNode = xDoc.Descendants("access").FirstOrDefault();
            while (accessNode != null)
            {
                cancellationToken.ThrowIfCancellationRequested();

                bool removeAppliedElement = false;

                string[] accessItems = (accessNode as XElement).Value.Split('|');
                foreach (string accessItem in accessItems)
                {
                    string[] accessInfo = accessItem.Split('>');
                    DashboardAccess access = new DashboardAccess()
                    {
                        handle = accessInfo[0],
                        key = accessInfo[1],
                        value = accessInfo[2]
                    };

                    if (!access.yes)
                    {
                        removeAppliedElement = true;
                        break;
                    }
                }
                // Even if user is eligible to access, no need to see the access condition > Remove.
                if (removeAppliedElement)
                    accessNode.Parent.Remove();
                else
                    accessNode.Remove();
                xDocIsChanged = true;

                accessNode = xDoc.Descendants("access").FirstOrDefault();
            }
            if (xDocIsChanged)
                xmlString = xDoc.ToString();

            return Library.util.castFromXmlToDynamic(xmlString);
        }

        private enum SqlCallable
        {
            Function,
            Procedure
        }

        public class DashboardAccess
        {
            [XmlAttribute("h")]
            public string handle { get; set; }
            [XmlAttribute("k")]
            public string key { get; set; }
            [XmlAttribute("v")]
            public string value { get; set; }
            public bool yes
            {
                get
                {
                    switch (handle)
                    {
                        //||||| Version |||||||||||
                        case "v":
                            return value.Split(',').Any(a => session.currentDomain_project.projectType.appVersion.Contains(a));

                        //||||| Access Right ||||||
                        case "a":
                            return session.can_i_do(key, value);

                        //||||| Configuration |||||
                        case "c":
                            string _cv = applica.getConfigVal(key, session.currentDomain_project.appId, session.currentDomain_projectId);
                            return (value.Contains('|') && value.Split('|').Contains(_cv))
                                || (value == "!NULL" && !String.IsNullOrEmpty(_cv))
                                || _cv.ToLower() == value.ToLower();
                    }
                    return true;
                }
            }
        }
    }
}