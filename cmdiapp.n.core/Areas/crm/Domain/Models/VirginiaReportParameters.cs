﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.Serialization;
using System.Web;

namespace cmdiapp.n.core.Areas.crm.Domain.Models
{
    [DataContract]
    public class VirginiaReportParameters
    {
        [DataMember(Name = "fundIds")]
        public int[] FundIds { get; set; }

        [DataMember(Name = "filerId")]
        public int FilerId { get; set; }

        [DataMember(Name = "reportStart")]
        public DateTime ReportStart { get; set; }

        [DataMember(Name = "reportEnd")]
        public DateTime ReportEnd { get; set; }

        [DataMember(Name = "filingDate")]
        public DateTime FilingDate { get; set; }

        [DataMember(Name = "reportTypeId")]
        public int ReportTypeId { get; set; }

        [DataMember(Name = "isAmendment")]
        public bool IsAmendment { get; set; }

        [DataMember(Name = "amendmentNumber")]
        public int AmendmentNumber { get; set; }

        [DataMember(Name = "saveThisReport")]
        public bool SaveThisReport { get; set; }
    }
}