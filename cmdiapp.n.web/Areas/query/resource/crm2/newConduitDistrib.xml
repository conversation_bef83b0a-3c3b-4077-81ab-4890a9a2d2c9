<qDef name="newConduitDistrib" caption="New Conduit Distribution">
  <aElementIdentifier>Conduit</aElementIdentifier>
  <sq_uniqId>[MC].MONYCONDUITID</sq_uniqId>
  <def_export>xlsx</def_export>
  <runDefault>false</runDefault>
  
  <!--||||| Action Buttons ||||||||||||||||||||||||||||||||||||||||||||||||||-->
  <qab>
    <ab type="result" caption="Export" accessLevel="e">
      <a>export</a>
    </ab>
    <ab type="result" caption="Create New Distribution" accessLevel="a">
      <a>createDistribution</a>
    </ab>
  </qab>

  <!--||||| Table Columns |||||||||||||||||||||||||||||||||||||||||||||||||||-->
  <qtc>
    <c key="PID" label="Donor Id" format="s">
      <f>[P].PID</f>
    </c>
    <c key="PEOTYPE" label="Type" format="s">
      <f>[PT].PEOTYPE</f>
    </c>
    <c key="FNAME" label="First" format="s">
      <f>[P].FNAME</f>
    </c>
    <c key="MNAME" label="Middle" format="s">
      <f>[P].MNAME</f>
    </c>
    <c key="LNAME" label="Last" format="s">
      <f>[P].LNAME</f>
    </c>
    <c key="MID" label="Tran Id" format="s">
      <f>[M].MID</f>
    </c>
    <c key="FUNDCODE" label="Fund Code" format="s">
      <f>[F].FUNDCODE</f>
    </c>
    <c key="BATCHDTE" label="Batch Date" format="d">
      <f>[M].BATCHDTE</f>
    </c>
    <c key="CONDUITNAME" label="Conduit" format="s">
      <f>[V].CONDUITNAME</f>
    </c>
    <c key="CONDUITAMT" label="Conduit Amt" format="c">
      <f>[MC].AMT AS CONDUITAMT</f>
    </c>
    <c key="CONDUITFEE" label="Conduit Fee" format="c">
      <f>[MC].FEE AS CONDUITFEE</f>
    </c>
    <c key="CONDUITNET" label="Conduit Net" format="c">
      <f>[MC].NET AS CONDUITNET</f>
    </c>
    <c key="STREET" label="Street" format="s">
      <f>[A].STREET</f>
    </c>
    <c key="CITY" label="City" format="s">
      <f>[A].CITY</f>
    </c>
    <c key="STATE" label="State" format="s">
      <f>[A].STATE</f>
    </c>
    <c key="ZIP" label="Zip" format="s">
      <f>[A].ZIP</f>
    </c>
    <c key="MONYCONDUITID" label="Gift Conduit Id" format="s">
      <f>[MC].MONYCONDUITID</f>
    </c>
  </qtc>

  <!--||||| Table Buttons |||||||||||||||||||||||||||||||||||||||||||||||||||-->

  <qtb>
    <tb caption="Edit">
      <a>edit</a>
      <k>MID</k>
      <g>
        <i k="isOld" v="false" />
        <i k="componentKey" v="money"/>
        <i k="componentName" v="Money" />
      </g>
    </tb>
  </qtb>

  <!--||||| FROM ||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||-->

  <qf>

    <!--||||| MONEY ||||||||||||||||||||||||||||||||||||||-->
    <w f="[DC].dtDISTRIBID">[DC].dtDISTRIBID IS NULL</w>
    <f>
      <c>true</c>
      <t>
        <![CDATA[
            MONY [M]
	            INNER JOIN dmFUND [F] ON M.FUNDID = F.FUNDID
	            INNER JOIN PEOPLE [P] ON M.PID = P.PID
		            LEFT OUTER JOIN ADDRESS [A] ON P.PID = A.PID AND A.PRIME =1
		            INNER JOIN lKPEOTYPE [PT] ON P.PEOTYPEID = PT.PEOTYPEID
              INNER JOIN MONYCONDUIT [MC] 
            			    INNER JOIN v_conduitno [V] ON MC.CONDUITNO = V.CONDUITNO 
                      LEFT JOIN dtCONDUITDIST [DC] ON MC.MONYCONDUITID = DC.MONYCONDUITID
              ON M.MID = MC.MID
              INNER JOIN lkMONYTYPE [MT] ON M.MONYTYPEID = MT.MONYTYPEID
        ]]>
      </t>
    </f>
  </qf>

  <!--||||| Export Ouput Field Groups |||||||||||||||||||||||||||||||||||||||-->

  <qo>
    <eg name="nwdteg_conduitdistributGroup">
      <g>
        <i>nwdteg_conduitdistrib</i>
        <c>Distribution</c>
        <d></d>
        <s>true</s>
        <f>
          <![CDATA[
              [MC].MONYCONDUITID,    
	            [M].MID,
              [P].PID,
              ISNULL([PT].PEOTYPE,'') AS TYPE,
              ISNULL([P].FNAME,'') FNAME,ISNULL(P.MNAME,'') MNAME,ISNULL(P.LNAME,'') LNAME,
              ISNULL([P].OCCUPATION,'') as OCCUPATION,
              ISNULL([P].EMPLOYER,'') as EMPLOYER,
              ISNULL([F].FUNDCODE,'') as FUNDCODE,
              [M].BATCHDTE AS BATCHDTE,
              ISNULL([V].CONDUITNAME,'') as CONDUIT,
              ISNULL([MT].DESCRIP,'') as PAYMENT, 
              ISNULL([MC].AMT,0) AS CONDUITAMT,
              ISNULL([MC].FEE,0) AS CONDUITFEE,
              ISNULL([MC].NET,0) AS CONDUITNET,
              ISNULL([A].STREET,'') AS STREET, 
              ISNULL([A].CITY,'') AS CITY,
              ISNULL([A].STATE,'') AS [STATE],
              ISNULL([A].ZIP,'') AS ZIP
          ]]>
        </f>
      </g>
    </eg>
  </qo>

  <!--||||| Quick Text Search Configuration |||||||||||||||||||||||||||||||||-->

  <!--||||| Filter Groups |||||||||||||||||||||||||||||||||||||||||||||||||||-->

  <qsfg>
    <qs name="nwdtfg_conduitdistributGroup" caption="Gift">
      <q>
        <i>nwdt_fundcode</i>
        <t>s</t>
        <c>Fund Code</c>
        <n>[F].FUNDCODE</n>
        <o>0</o>
        <lookup id='new_conduitdistrib_fund'><![CDATA[EXEC dbo.z_get_lookup_in_xml__fund3X]]></lookup>
      </q>
      <q>
        <i>nwdt_batchdte</i>
        <t>d</t>
        <c>Batch Date</c>
        <d>Deposit Date</d>
        <n>[M].BATCHDTE</n>
        <o>0</o>
      </q>
      <q>
        <i>nwdt_conduitno</i>
        <t>s</t>
        <c>Conduit#</c>
        <d>Enter Conduit#</d>
        <n>[MC].CONDUITNO</n>
        <o>0</o>
        <lookup id='new_conduitdistrib_conduitno'><![CDATA[EXEC dbo.z_get_lookup_in_xml__Conduits]]></lookup>
      </q>
    </qs>
  </qsfg>
</qDef>
