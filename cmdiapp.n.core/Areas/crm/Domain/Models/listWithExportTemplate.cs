﻿using System;
using System.Collections.Generic;

namespace cmdiapp.n.core.Areas.crm.Domain.Models
{
    public class listWithExportTemplateDef
    {
        public string id { get; set; }
        public string title { get; set; }
        public string url { get; set; }
        public string exportUrl { get; set; }
        public string exportSearchText { get; set; }
        public List<actionBtn> buttons { get; set; }
        public List<fieldFormat> fieldFormats { get; set; }
        public List<actionBtn> actionButtons { get; set; }
        public listWithExportTemplateDef()
        {

        }
        public listWithExportTemplateDef(string _id, string _title, string _url, string _exportUrl, List<actionBtn> _buttons, List<fieldFormat> _fieldFormats,string _exportSearchText = "", List<actionBtn> _actionButtons = null)
        {
            id = _id;
            title = _title;
            url = _url;
            exportUrl = _exportUrl;
            buttons = _buttons;
            fieldFormats = _fieldFormats;
            exportSearchText = _exportSearchText;
            actionButtons = _actionButtons ?? new List<actionBtn>();
        }
    }
    public class actionBtn
    {
        public string label { get; set; }
        public string cssClass { get; set; }
        public bool bindFuncToDblClick { get; set; }
        public string onClickFunction { get; set; }
        public string argKey { get; set; }
        public string title { get; set; }

        public actionBtn(string _label, string _cssClass, string _onClickFunction, string _argKey, string _title, bool _bindFuncToDblClick = false)
        {
            label = _label;
            cssClass = _cssClass;
            bindFuncToDblClick = _bindFuncToDblClick;
            onClickFunction = _onClickFunction;
            argKey = _argKey;
            title = _title;
        }
    }
    public class fieldFormat
    {
        public string key { get; set; }
        public string label { get; set; }
        public string format { get; set; }
        public string templatekey { get; set; }

        public fieldFormat(string KEY, string LABEL, string FORMAT = null, string TEMPLATEKEY = null)
        {
            key = KEY;
            label = LABEL;
            format = FORMAT;
            templatekey = TEMPLATEKEY;
        }
    }
}