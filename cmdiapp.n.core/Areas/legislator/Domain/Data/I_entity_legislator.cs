﻿using System;
using System.Collections.Generic;
using System.Data.Entity;
using System.Linq;
using System.Linq.Expressions;

namespace cmdiapp.n.core.Areas.legislator.Domain.Data
{
    public interface I_entity_legislator
    {
        DbContext getContext();

        IQueryable<TSource> All<TSource>() where TSource : _entityBase_legislator, new();

        void Add<TSource>(IEnumerable<TSource> items) where TSource : _entityBase_legislator, new();
        void Add<TSource>(TSource item) where TSource : _entityBase_legislator, new();

        void CommitChanges();

        void Delete<TSource>(Expression<Func<TSource, bool>> expression) where TSource : _entityBase_legislator, new();
        void Delete<TSource>(TSource item) where TSource : _entityBase_legislator, new();

        void Dispose();

        TSource Single<TSource>(Expression<Func<TSource, bool>> expression) where TSource : _entityBase_legislator, new();

        void Update<TSource>(TSource item) where TSource : _entityBase_legislator, new();

        void Refresh<TSource>(TSource item) where TSource : _entityBase_legislator, new();
    }
}
