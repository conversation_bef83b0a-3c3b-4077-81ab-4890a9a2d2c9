﻿using cmdiapp.n.core.Areas.datax.Domain.Models;
using cmdiapp.n.core.Areas.datax.Domain.Models.ViewModels;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace cmdiapp.n.core.Areas.datax.Domain.Services
{
    /// <summary>
    /// Provides methods for setting up a datax <see cref="Account"/> with Cremir
    /// </summary>
    public interface ICremirSetupService
    {
        /// <summary>
        /// Starts the account setup process
        /// </summary>
        /// <param name="callerId">the <see cref="Caller.Id"/> to set account up with</param>
        /// <returns>
        /// a <see cref="Task"/> resulting in the temporary token
        /// to identify this setup process
        /// </returns>
        Task<string> StartAsync(int callerId);

        /// <summary>
        /// Checks the status of a Cremir account setup
        /// </summary>
        /// <param name="token">the temporary token for this setup</param>
        /// <returns>a <see cref="Task"/> resulting in the current status of the setup process</returns>
        /// <exception cref="ArgumentException">if <paramref name="token"/> is null or empty</exception>
        Task<CremirSetupStatus> CheckStatusAsync(string token);

        /// <summary>
        /// Returns url of authorization request page for third-party service.
        /// </summary>
        /// <param name="token">the temporary token for this setup</param>
        /// <returns><see cref="Task"/> of url</returns>
        Task<string> GetAuthorizationRequestUrlAsync(string token);
    }
}
