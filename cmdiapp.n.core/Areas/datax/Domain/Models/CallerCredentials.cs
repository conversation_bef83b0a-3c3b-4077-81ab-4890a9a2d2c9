﻿using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Web;

namespace cmdiapp.n.core.Areas.datax.Domain.Models
{
    /// <summary>
    /// Contains credentials to pass to caller/integrator for retrieving data, e.g., accounts.
    /// </summary>
    public class CallerCredentials
    {
        /// <summary>
        /// User name for the user account associated with the caller
        /// </summary>
        public string UserName { get; set; }

        /// <summary>
        /// Password or key for this user account
        /// </summary>
        public string PassKey { get; set; }

        /// <summary>
        /// Encodes <see cref="UserName"/> and <see cref="PassKey"/> to Base64String for use with
        /// HTTP requests.  Returns empty <see cref="string"/> if either <see cref="UserName"/> or <see cref="PassKey"/> are empty.
        /// </summary>
        [JsonIgnore]
        public string Base64EncodedCredentials
        {
            get
            {
                if (string.IsNullOrEmpty(UserName) || string.IsNullOrEmpty(PassKey))
                {
                    return "";
                }
                return Convert.ToBase64String(Encoding.UTF8.GetBytes($"{UserName}:{PassKey}"));
            }
                
        }
    }
}