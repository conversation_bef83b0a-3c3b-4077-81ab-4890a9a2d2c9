﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.Serialization;
using System.Web;

namespace cmdiapp.n.core.Areas.crm.ViewModels
{
    /// <summary>
    /// Contains data for displaying account panels for file drop
    /// </summary>
    [DataContract]
    public class FileDropAccountPanel
    {
        /// <summary>
        /// Gets or sets the <see cref="datax.Domain.Models.Account.Id"/>
        /// </summary>
        [DataMember(Name = "accountId")]
        public int AccountId { get; set; }
        /// <summary>
        /// Gets or sets the <see cref="datax.Domain.Models.Account.client"/>,
        /// i.e., the display name for the account
        /// </summary>
        [DataMember(Name = "name")]
        public string Name { get; set; }
        /// <summary>
        /// Gets or sets the <see cref="datax.Domain.Models.Account.appKey"/>
        /// </summary>
        [DataMember(Name = "appKey")]
        public string AppKey { get; set; }
    }
}