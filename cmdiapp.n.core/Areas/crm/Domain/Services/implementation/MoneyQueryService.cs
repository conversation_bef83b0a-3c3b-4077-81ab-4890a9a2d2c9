﻿using cmdiapp.n.core.Areas.crm.Domain.Models;
using cmdiapp.n.core.Areas.crm.ViewModels;
using cmdiapp.n.core.Areas.query.Domain.Models;
using cmdiapp.n.core.Library;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace cmdiapp.n.core.Areas.crm.Domain.Services.implementation
{
    public class MoneyQueryService : IMoneyQueryService
    {
        private readonly IQueryService _queryService;
        private readonly IErrorLogger _errorLogger;

        public MoneyQueryService(IQueryService queryService, IErrorLogger errorLogger)
        {
            _queryService = queryService;
            _errorLogger = errorLogger;
        }

        public Task<PagedExternalApiResult<List<object>>> GetMoneyRecordsAsync(
            ExternalApiSpecification spec,
            CancellationToken cancellationToken)
        {
            if (spec == null)
            {
                throw new ArgumentNullException(nameof(spec));
            }

            cancellationToken.ThrowIfCancellationRequested();

            try
            {
                if (!spec.Filters.Any())
                {
                    spec.Filters.Add(new MoneyNoFilterSpecificationFilter());
                }

                var queryInstance = ConvertSpecToQueryInstance(spec);
                var result = _queryService.ToList(
                    queryInstance,
                    new QueryRuntimeParams
                    {
                        resultType = QueryResultType.pagedExport,
                        page = spec.PageNumber,
                        pageSize = spec.PageSize
                    });
                return Task.FromResult(new PagedExternalApiResult<List<object>>
                {
                    IsSuccessful = true,
                    Result = result,
                    PageNumber = spec.PageNumber,
                    PageSize = spec.PageSize
                });
            }
            catch (Exception ex)
            {
                _errorLogger.Log(
                    $"Encountered exception when attempting to run MoneyQuery {ex.GetType().Name}: {ex.Message}");
                return Task.FromResult(new PagedExternalApiResult<List<object>>
                {
                    IsSuccessful = false,
                    Message = "An error occurred."
                });
            }
        }

        private QueryInstanceInfo ConvertSpecToQueryInstance(ExternalApiSpecification spec)
        {
            var instance = new QueryInstanceInfo()
            {
                defKey = "moneySearch",
                filters = spec.Filters.Select(f => f.ConvertToQueryInstanceFilter()).ToList(),
                exportOptions = GetDefaultExportOptions()
            };

            return instance;
        }

        private List<QueryExportOptionInstance> GetDefaultExportOptions()
        {
            return new List<QueryExportOptionInstance>
            {
                new QueryExportOptionInstance 
                {
                    key = "mseg_idRecordType",
                    groupKey = "mseg_idRecordTypeGroup"
                },
                new QueryExportOptionInstance 
                {
                    key = "pseg_Salutation",
                    groupKey = "pseg_Salutation"
                },
                new QueryExportOptionInstance 
                {
                    key = "mseg_Individual_Personal_Details",
                    groupKey = "mseg_Individual_Personal_Details"
                },
                new QueryExportOptionInstance 
                {
                    key = "mseg_addressPrimary",
                    groupKey = "mseg_addressGroup"
                },
                new QueryExportOptionInstance 
                {
                    key = "mseg_phones",
                    groupKey = "mseg_phonesGroup"
                },
                new QueryExportOptionInstance 
                {
                    key = "mseg_batch",
                    groupKey = "mseg_batchGroup"
                },
                new QueryExportOptionInstance 
                {
                    key = "mseg_Fundraising_Codes",
                    groupKey = "mseg_Fundraising_Codes"
                },
                new QueryExportOptionInstance 
                {
                    key = "mseg_conduit",
                    groupKey = "mseg_conduitGroup"
                },
                new QueryExportOptionInstance 
                {
                    key = "mseg_chapter",
                    groupKey = "mseg_chapterGroup"
                }
            };
        }
    }
}