﻿using System;
using System.Collections.Generic;
using System.Configuration;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Runtime.Serialization;
using System.Text;
using System.Threading.Tasks;
using System.Web;
using cmdiapp.n.core._Domain.Services;
using cmdiapp.n.core.Areas.datax.Domain.Models;
using cmdiapp.n.core.Areas.datax.Domain.Models.ViewModels;
using cmdiapp.n.core.Core;
using cmdiapp.n.core.Library;
using Newtonsoft.Json;

namespace cmdiapp.n.core.Areas.datax.Domain.Services.implementation
{
    /// <summary>
    /// An implementation of <see cref="ICremirSetupService"/>
    /// </summary>
    public class CremirSetupService : ICremirSetupService
    {
        private readonly ICremirUtilService _util;
        private IDataxService _coreService;
        private IAccountService _accountService;

        public CremirSetupService(ICremirUtilService util, IDataxService coreService, IAccountService accountService)
        {
            _util = util;
            _coreService = coreService;
            _accountService = accountService;
        }
        /// <summary>
        /// Checks the status of a Cremir account setup
        /// </summary>
        /// <param name="token">the temporary token for this setup</param>
        /// <returns>a <see cref="Task"/> resulting in the current status of the setup process</returns>
        /// <exception cref="ArgumentException">if <paramref name="token"/> is null or empty</exception>
        public async Task<CremirSetupStatus> CheckStatusAsync(string token)
        {
            if (string.IsNullOrEmpty(token))
            {
                throw new ArgumentException($"{nameof(token)} cannot be null or empty.", nameof(token));
            }

            SetTls();
            using (var request = new HttpRequestMessage(
                HttpMethod.Get,
                $"{_util.BaseUrl}/api/SetupOAuth/Status/{token}"))
            using (var http = new HttpClient())
            {
                _util.AddAuthToRequest(request);
                try
                {
                    HttpResponseMessage response = await http.SendAsync(request);
                    if (response.IsSuccessStatusCode && response.Content != null)
                    {
                        var result = await response.Content.ReadAsAsync<CremirSetupStatus>();
                        if ((result?.Status == "Success") && result?.AccountId != null)
                        {
                            Account account = _accountService.Get(result.AccountId ?? 0);
                            if (account == null)
                            {
                                return new CremirSetupStatus()
                                {
                                    Status = "Failed",
                                    Message = "Could not find Account record.  Account creation failed."
                                };
                            }
                            _coreService.AddAccountIdForCurrentProject(account.Id, account.callerId ?? 0);
                        }
                        return result;
                    }
                    else
                    {
                        LogErrorStatusResponse(response, request.RequestUri.OriginalString);
                        return new CremirSetupStatus()
                        {
                            StatusId = 0,
                            Status = "Failed",
                            Message = "An error occurred. Unable to setup account."
                        };
                    }
                }
                catch (Exception ex)
                {
                    LogResponseException(request.RequestUri.OriginalString, ex);
                    return new CremirSetupStatus()
                    {
                        StatusId = 0,
                        Status = "Failed",
                        Message = "An error occurred. Unable to setup account."
                    };
                }
            }
        }
        /// <summary>
        /// Starts the account setup process
        /// </summary>
        /// <param name="callerId">the <see cref="Caller.Id"/> to set account up with</param>
        /// <returns>
        /// a <see cref="Task"/> resulting in the temporary token
        /// to identify this setup process
        /// </returns>
        public async Task<string> StartAsync(int callerId)
        {
            SetTls();
            var request = new HttpRequestMessage(HttpMethod.Post, $"{_util.BaseUrl}/api/SetupOAuth")
            {
                Content = new StringContent(
                    JsonConvert.SerializeObject(new CremirSetupInfo()
                    {
                        CallerId = callerId,
                        CrimsonProjectId = session.currentDomain_projectId
                    }),
                    Encoding.UTF8,
                    "application/json")
            };

            using (request)
            using (var http = new HttpClient())
            {
                _util.AddAuthToRequest(request);
                try
                {
                    HttpResponseMessage response = await http.SendAsync(request);
                    if (response.IsSuccessStatusCode && response.Content != null)
                    {
                        return await response.Content.ReadAsStringAsync();
                    }
                    else
                    {
                        LogErrorStatusResponse(response, request.RequestUri.OriginalString);
                        return null;
                    }
                }
                catch (Exception ex)
                {
                    LogResponseException(request.RequestUri.OriginalString, ex);
                    return null;
                }
            }
        }

        private string GetCredentialsUrl(string token)
        {
            return $"{_util.BaseUrl}/api/SetupOAuth/Start/{token}";
        }

        /// <summary>
        /// Returns url of authorization request page for third-party service.
        /// </summary>
        /// <param name="token">the temporary token for this setup</param>
        /// <returns><see cref="Task"/> of url. If empty, an error occurred.</returns>
        public async Task<string> GetAuthorizationRequestUrlAsync(string token)
        {
            SetTls();
            using (var request = new HttpRequestMessage(HttpMethod.Get, GetCredentialsUrl(token)))
            using (var http = new HttpClient())
            {
                _util.AddAuthToRequest(request);
                try
                {
                    HttpResponseMessage response = await http.SendAsync(request);
                    if (!response.IsSuccessStatusCode)
                    {
                        LogErrorStatusResponse(response, request.RequestUri.OriginalString);
                        return string.Empty;
                    }

                    var result = response.Content != null
                        ? await response.Content.ReadAsAsync<OAuthUrlResponse>()
                        : null;
                    if (result?.Success ?? false && !string.IsNullOrEmpty(result?.Url))
                    {
                        return result.Url;
                    }

                    util.recordInErrorLog($"Cremir responded to request for authorization url " +
                        $"at {request.RequestUri.OriginalString} with {result?.Message ?? "No error message"}");
                    return string.Empty;
                }
                catch (Exception ex)
                {
                    LogResponseException(request.RequestUri.OriginalString, ex);
                    return string.Empty;
                }
            }

        }

        private void LogErrorStatusResponse(HttpResponseMessage response, string url)
        {
            util.recordInErrorLog($"Cremir responded with {response.StatusCode} {response.ReasonPhrase} to {url}");
        }

        private void LogResponseException(string url, Exception ex)
        {
            util.recordInErrorLog(
                        $"Encountered exception when checking Cremir setup status at " +
                        $"{url}{Environment.NewLine}" +
                        $"{ex.GetType().Name}: {ex.Message}{Environment.NewLine}{ex.StackTrace}");
        }

        private void SetTls()
        {
            // required to establish SSL/TLS connection
            ServicePointManager.Expect100Continue = true;
            ServicePointManager.SecurityProtocol = SecurityProtocolType.Tls12;
        }
        

        [DataContract]
        private class CremirSetupInfo
        {
            [DataMember(Name = "callerId")]
            public int CallerId { get; set; }
            [DataMember(Name = "crimsonProjectId")]
            public int CrimsonProjectId { get; set; }
        }

        [DataContract]
        private class OAuthUrlResponse
        {
            [DataMember(Name = "success")]
            public bool Success { get; set; }
            [DataMember(Name = "message")]
            public string Message { get; set; }
            [DataMember(Name = "url")]
            public string Url { get; set; }
        }
    }
}