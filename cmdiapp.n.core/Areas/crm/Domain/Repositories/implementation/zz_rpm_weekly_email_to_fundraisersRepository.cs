﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Data.Linq;
using cmdiapp.n.core.Areas.crm.Domain.Models;

namespace cmdiapp.n.core.Areas.crm.Domain.Repositories
{
    public class zz_rpm_weekly_email_to_fundraisersRepository : _baseRepository<zz_rpm_weekly_email_to_fundraisers>, Izz_rpm_weekly_email_to_fundraisersRepository
    {
        public zz_rpm_weekly_email_to_fundraisersRepository(I__DBFactory databaseFactory)
            : base(databaseFactory)
        {

        }
    }
}
