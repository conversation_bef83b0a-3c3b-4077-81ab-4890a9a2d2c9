﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Data;
using System.Linq;
using System.Web.Mvc;
using System.Data.Linq;
using System.ComponentModel.DataAnnotations.Schema;

using cmdiapp.n.core.Domain.ViewModels;
using cmdiapp.n.core.Areas.crm.Domain.Data;

namespace cmdiapp.n.core.Areas.crm.Domain.Models
{
    public class PayRequestQR : _entityBase_crm, iItemType
    {
        public int PAYREQID { get; set; }

        public DateTime? REQDTE { get; set; }

        public string TYPE { get; set; }

        public string SUBMITTER { get; set; }

        public int VENDORID { get; set; }

        public string PAYEECODE { get; set; }

        public string VENDORTYPE { get; set; }

        public string PAYEE { get; set; }

        public string STREET { get; set; }

        public string ADDR1 { get; set; }

        public string CITY { get; set; }

        public string STATE { get; set; }

        public string ZIP { get; set; }

        public decimal AMT { get; set; }

        public string FUNDCODE { get; set; }

        public string STATUS { get; set; }

        public Nullable<DateTime> APPROVEDTE { get; set; }

        public Nullable<DateTime> TXNDTE { get; set; }
    }

    public class PayRequestQR_ext1 : PayRequestQR
    {
        public int count_ { get; set; }

        public Int64 rowNo { get; set; }
    }

    [Table("PAYREQ")]
    public class PAYREQ : _entityBase_crm, iItemType
    {
        [Column]
        [Key]
        public int PAYREQID { get; set; }

        [Column]
        public Int16 TYPEID { get; set; }

        [Column]
        public Int16 STATUSID { get; set; }

        [Column]
        public int ENTITYID { get; set; }

        [Column]
        public Nullable<Int16> FUNDID { get; set; }

        [Column]
        public Nullable<Int16> FECTXNTYPEID { get; set; }

        [Column]
        public string FECDESCRIP { get; set; }

        [Column]
        public Nullable<Int16> TXNCODEID { get; set; }

        [Column]
        public Nullable<Int16> TXNCATID { get; set; }

        [Column]
        public Nullable<Int16> ELECTCDID { get; set; }

        [Column]
        public string ELECTYR { get; set; }

        [Column]
        public string ELECTOTH { get; set; }

        [Column]
        public bool ISMEMO { get; set; }

        [Column]
        public string MEMOTXT { get; set; }
        [Column]
        public bool IS1099 { get; set; }

        [Column]
        public string DESCRIP { get; set; }

        [Column]
        public Nullable<decimal> AMT { get; set; }

        [Column]
        public Nullable<DateTime> REQDTE { get; set; }

        [Column]
        public Nullable<DateTime> SUBMITDTE { get; set; }

        [Column]
        public Nullable<DateTime> APPROVEDTE { get; set; }

        [Column]
        public Nullable<short> APPROVEBY { get; set; }

        [Column]
        public Nullable<int> INVOICEID { get; set; }

        [Column]
        public Nullable<int> TXNID { get; set; }

        [Column]
        public Nullable<DateTime> DELIVERDTE { get; set; }

        [Column]
        public string DELIVERBY { get; set; }

        [Column]
        public string DELIVERTO { get; set; }

        [Column]
        public Nullable<int> REFUNDMID { get; set; }

        [Column]
        public Nullable<int> LINKPAYREQID { get; set; }

        [Column]
        public Nullable<decimal> LINKSUM { get; set; }

        [Column]
        public string COMMENT { get; set; }

        [Column]
        public short? UID { get; set; }

        [Column("_updating_uid")]
        public short? updating_uid { get; set; }

        [Column]
        public Nullable<DateTime> CREATEDON { get; set; }
    }

    [Table("lkPAYREQSTATUS")]
    public class lkPAYREQSTATUS : _entityBase_crm, iItemType
    {
        [Column]
        [Key]
        public Int16 STATUSID { get; set; }

        [Column]
        public string CODE { get; set; }

        [Column]
        public string DESCRIP { get; set; }
    }

    [Table("lkPAYREQTYPE")]
    public class lkPAYREQTYPE : _entityBase_crm, iItemType
    {
        [Column]
        [Key]
        public Int16 TYPEID { get; set; }

        [Column]
        public string CODE { get; set; }

        [Column]
        public string DESCRIP { get; set; }
    }

    public class payreqLookUpData : iItemType
    {
        public List<dmFUND> FUNDCODE;
        public List<lkFECTxnType> LINENO;
        public List<lkElectCD> ELECTIONCD;
        public List<lkFECDesc> FECDESCRIPTION;
        public List<lkPAYREQSTATUS> PAYREQSTATUS;
        public List<lkPAYREQTYPE> PAYREQTYPES;
    }

    [Table("PAYREQDOC")]
    public class PAYREQDOC : _entityBase_crm, iItemType
    {
        [Column]
        [Key]
        public int PAYREQDOCID { get; set; }

        [Column]
        public int? PAYREQID { get; set; }

        [Column]
        public string FILENAME { get; set; }

        [Column]
        public byte[] CONTENT { get; set; }

        [Column("_updating_uid")]
        public short? updating_uid { get; set; }
    }

    public class v_ultimate_vendor_payreq
    {
        [Column]
        [Key]
        public int PAYREQID { get; set; }

        [Column]
        public string NAME { get; set; }

        [Column]
        public Nullable<DateTime> REQDTE { get; set; }

        [Column]
        public Nullable<decimal> AMT { get; set; }

        [Column]
        public int LINKEDPAYREQID { get; set; }
    }

    public class payreqUVdata : iItemType
    {
        public List<v_ultimate_vendor_payreq> UVDATA;
        public Decimal? LINKSUM;
        public Decimal? AMT;
    }
    public class PayRequestUV : _entityBase_crm, iItemType
    {
        public int PAYREQID { get; set; }

        public int VENDORID { get; set; }

        public string PAYEECODE { get; set; }

        public string PAYEE { get; set; }

        public DateTime REQDTE { get; set; }

        public Decimal AMT { get; set; }

        public string LINE { get; set; }

        public string DESCRIP { get; set; }

        public string FECDESCRIP { get; set; }

        public string FILENAME { get; set; }

        public Int16 STATUSID { get; set; }
    }
}