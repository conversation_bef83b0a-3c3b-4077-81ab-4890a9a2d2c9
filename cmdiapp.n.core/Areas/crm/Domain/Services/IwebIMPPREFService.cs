﻿using System.Collections.Generic;
using System.Web.Mvc;

using cmdiapp.n.core.Areas.crm.Core;
using cmdiapp.n.core.Areas.crm.Domain.Models;
using cmdiapp.n.core.Areas.crm.Domain.ViewModels;
using cmdiapp.n.core.Areas.crm.Domain.Repositories;

namespace cmdiapp.n.core.Areas.crm.Domain.Services
{
    public interface IwebIMPPREFService
    {
        //Used for Mapping preference saved/used by the user
        List<webIMPPREF> read_all_mapping_pref(int UID, int impTypeSel);
        
        int DeleteMapping(int UID, int impType, string prefName);

        List<webIMPPREF> get_mapping_for_prefname(int UID, int impTypeSel, string prefName);
    }
}