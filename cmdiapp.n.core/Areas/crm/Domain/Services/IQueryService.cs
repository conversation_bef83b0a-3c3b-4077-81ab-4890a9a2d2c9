﻿using cmdiapp.n.core.Areas.query.Domain.Models;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace cmdiapp.n.core.Areas.crm.Domain.Services
{
    public interface IQueryService
    {
        DataTable ToDataTable(QueryInstanceInfo instanceInfo, QueryRuntimeParams runtimeParams);

        List<object> ToList(QueryInstanceInfo instanceInfo, QueryRuntimeParams runtimeParams);
    }
}
