﻿using System.Collections.Generic;
using System.Web.Mvc;

using cmdiapp.n.core.Areas.crm.Domain.Models;
using cmdiapp.n.core.Areas.crm.Domain.ViewModels;
using cmdiapp.n.core.Areas.crm.Domain.Repositories;

namespace cmdiapp.n.core.Areas.crm.Domain.Services
{
    public interface IclubService
    {

        bool check_unqiueness(pmCLUB _pmClub);
        bool check_record_exists(int clubId);

        void Add(pmCLUB pmCLUB);
        void Update(pmCLUB pmCLUB);
        void Delete(pmCLUB pmCLUB);

        List<pmCLUB> read_all_clubs();
        pmCLUB get_club(int clubId);

        List<pmCLUB_ext> get_all_clubs(string where, string orderBy, int pageSize, int pageNo);

    }
}