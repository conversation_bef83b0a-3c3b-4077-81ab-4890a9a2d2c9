﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Web.Http;
using System.IO;

using cmdiapp.n.core.Core;
using cmdiapp.n.core.Domain.Data;
using cmdiapp.n.core.Domain.Models;

using cmdiapp.n.core.Areas.crm.Core;
using cmdiapp.n.core.Areas.crm.Domain.Models;
using cmdiapp.n.core.Areas.crm.Domain.Data;
using cmdiapp.n.core.Library;

namespace cmdiapp.n.core.Areas.apix.Presentation.api
{
    public class notificController : ApiController
    {
        private I_entity _entity;

        public notificController()
        {
            _entity = I_entityManager.getEntity();
        }

        #region [[ Crimson Notification Email - Image Header ]] v2/notification/{notifcIdEnc}.png
        // eg. https://apix.cmdi.com/v2/notification/image/0x0100000077cd886d722e043b8bb67193a1395996087cd2a704ee566a.png
        [HttpGet, Route("v2/notification/image/{notificIdEnc}.png")]
        public HttpResponseMessage image(string notificIdEnc)
        {
            #region [ Mark the notification "Read" if yet unread. ]
            try
            {
                notificIdEnc = Library.util.kill_sqlBlacklistWord(notificIdEnc);
                string sql_MarkRead = string.Format("UPDATE A SET readAt = GETDATE() FROM notific A WHERE notificId = dbo.crmEncryptDecode1('{0}') AND ISNULL(readAt,0)=0", notificIdEnc);
                _entity.getContext().Database.ExecuteSqlCommand(sql_MarkRead);
            }
            catch { }
            #endregion

            #region [ Return Crimson Logo ]
            try
            {
                HttpResponseMessage response = new HttpResponseMessage(HttpStatusCode.OK);

                string crimsonLogo_path = Path.Combine(System.Web.Hosting.HostingEnvironment.MapPath(@"~/contents/_shared/image/Crimson-Logo-Small-Original.png"));

                var fileStream = new System.IO.FileStream(crimsonLogo_path, System.IO.FileMode.Open, FileAccess.Read, FileShare.ReadWrite);
                response.Content = new StreamContent(fileStream);
            
                response.Content.Headers.ContentType = new System.Net.Http.Headers.MediaTypeHeaderValue("image/jpeg");
                response.Headers.CacheControl = new System.Net.Http.Headers.CacheControlHeaderValue
                {
                    MustRevalidate = true,
                    Private = true,
                    MaxAge = TimeSpan.FromHours(1),
                };
                return response;
            }
            catch
            {
                return new HttpResponseMessage(HttpStatusCode.OK);
            }
            #endregion

        }
        #endregion

        #region [[ Julep Notification Email - Image Header ]] v2/notification/{notifcIdEnc}.png
        // eg. https://apix.cmdi.com/v2/notification/image/0x0100000077cd886d722e043b8bb67193a1395996087cd2a704ee566a.png
        [HttpGet, Route("v2/notification/julepimage/{notificIdEnc}.png")]
        public HttpResponseMessage julepimage(string notificIdEnc)
        {
            #region [ Mark the notification "Read" if yet unread. ]
            try
            {
                notificIdEnc = Library.util.kill_sqlBlacklistWord(notificIdEnc);
                string sql_MarkRead = string.Format("UPDATE A SET readAt = GETDATE() FROM notific A WHERE notificId = dbo.crmEncryptDecode1('{0}') AND ISNULL(readAt,0)=0", notificIdEnc);
                _entity.getContext().Database.ExecuteSqlCommand(sql_MarkRead);
            }
            catch { }
            #endregion

            #region [ Return Julep Logo ]
            try
            {
                HttpResponseMessage response = new HttpResponseMessage(HttpStatusCode.OK);

                string JulepLogo_path = Path.Combine(System.Web.Hosting.HostingEnvironment.MapPath(@"~/contents/_shared/image/JulepGradientBkgrd_200x73.png"));

                var fileStream = new System.IO.FileStream(JulepLogo_path, System.IO.FileMode.Open, FileAccess.Read, FileShare.ReadWrite);
                response.Content = new StreamContent(fileStream);

                response.Content.Headers.ContentType = new System.Net.Http.Headers.MediaTypeHeaderValue("image/jpeg");
                response.Headers.CacheControl = new System.Net.Http.Headers.CacheControlHeaderValue
                {
                    MustRevalidate = true,
                    Private = true,
                    MaxAge = TimeSpan.FromHours(1),
                };
                return response;
            }
            catch
            {
                return new HttpResponseMessage(HttpStatusCode.OK);
            }
            #endregion

        }
        #endregion
    }
}
