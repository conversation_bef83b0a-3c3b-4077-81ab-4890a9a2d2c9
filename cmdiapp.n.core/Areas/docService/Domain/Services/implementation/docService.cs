﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Data.SqlClient;
using System.Data;
using System.Data.Entity;

using Ninject;
using Ninject.Web.Mvc;

using cmdiapp.n.core.Areas.docService.Domain.Data;
using cmdiapp.n.core.Areas.docService.Domain.Models;

using cmdiapp.n.core.Domain.Models;
using cmdiapp.n.core.Domain.ViewModels;
using cmdiapp.n.core.Areas.crm.Domain.Data;


namespace cmdiapp.n.core.Areas.docService.Domain.Services
{
    public class docService : IdocService
    {
        private I_entity_docService _entity;

        public docService(I_entity_docService entity)
        {
            _entity = entity;
        }

        public int countRecord(string email,string searchText)
        {
            string _sql = string.Format("select * from emailMsgData where FromEmail ='{0}' and SUBJECT like '%{1}%'", email,searchText);
            return _entity.getContext().Database.SqlQuery<emailMsgData>(_sql).Count();
        }

        public genericResponse get_emailMsgData(string email, string searchText)
        {
            string _sql = string.Format("select * from emailMsgData where FromEmail ='{0}' and SUBJECT like '%{1}%' ", email,searchText);
            var emData= _entity.getContext().Database.SqlQuery<emailMsgData>(_sql).ToList();
            return new genericResponse() { success = true, __count = emData.Count, results = emData.ToList<iItemType>() };
        }

        public List<emailMsgData> getList_emailMsgData(string email,string searchText)
        {
            string _sql = string.Format("select * from emailMsgData where FromEmail ='{0}' and SUBJECT like '%{1}%' ", email,searchText);
            return _entity.getContext().Database.SqlQuery<emailMsgData>(_sql).ToList();
        }
        public emailMsgData get_emailMsgData(int msgdataid)
        {
            string _sql = string.Format("select * from emailMsgData where MSGDATAID ='{0}'", msgdataid);
            return _entity.getContext().Database.SqlQuery<emailMsgData>(_sql).FirstOrDefault();
        }

    }
}