﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Data;
using System.Linq;
using System.Web.Mvc;
using System.Data.Linq;
using System.ComponentModel.DataAnnotations.Schema;

using cmdiapp.n.core.Domain.ViewModels;
using cmdiapp.n.core.Areas.crm.Domain.Data;

namespace cmdiapp.n.core.Areas.crm.Domain.Models
{
    // (view)v_people_1  -  Used for a single PEOPLE View ]]

    [Table("v_people_1")]
    public class PeopleR : _entityBase_crm, iItemType
    {
	    [Column] 
        [Key]
	    public int PID { get; set; }
	        
        [Column] 
        public string PREFIX { get; set; }  // 40

        [Column] 
        public string FNAME { get; set; }   // 30

        [Column] 
        public string MNAME { get; set; }   // 20

        [Column] 
        public string LNAME { get; set; }   // 80

        [Column]
        public string SUFFIX { get; set; }  // 20

        [Column]
        public string FULLNAME_ { get; set; }  // 20

        [Column]
        public string SPOUSENAME { get; set; }  // 50

        [Column]
        public string EMPLOYER { get; set; }    // 70

        [Column]
        public string OCCUPATION { get; set; }   // 50

        [Column]
        public string TITLE { get; set; }

        [Column]
        public string ASSISTANT { get; set; }

        [Column]
        public int? TRACKNO { get; set; }

        [Column] 
        public string HPHONE { get; set; }   

        [Column]
        public string WPHONE { get; set; }   

        [Column]
        public string CPHONE { get; set; }   

        [Column]
        public string FAX { get; set; }    

        [Column]
        public string EMAIL { get; set; }

        [Column]
        public string ADDR1 { get; set; }

        [Column]
        public string ADDR2 { get; set; }

        [Column]
        public string STREET { get; set; }

        [Column]
        public string CITY { get; set; }

        [Column]
        public string STATE { get; set; }

        [Column]
        public string ZIP { get; set; }

        [Column]
        public string PLUS4 { get; set; }

        [Column]
        public string Address_ { get; set; }

        [Column]
        public decimal? FGIFT { get; set; }

        [Column]
        public DateTime? FGIFTDTE { get; set; }

        [Column]
        public decimal? HPC { get; set; }

        [Column]
        public DateTime? HPCDTE { get; set; }

        [Column]
        public decimal? LGIFT { get; set; }

        [Column]
        public DateTime? LGIFTDTE { get; set; }

        [Column]
        public int? NOGIFTS { get; set; }

        [Column]
        public decimal? CUMTOT { get; set; }

        [Column]
        public string CTD_LABEL { get; set; }

        [Column]
        public decimal? CTD_AMT { get; set; }

        [Column]
        public byte[] PICTURE { get; set; }

        [Column]
        public string BIO { get; set; }

        [Column]
        public decimal? LATITUDE { get; set; }

        [Column]
        public decimal? LONGITUDE { get; set; }
    }

    [NotMapped]
    public class PeopleR_w_distance : PeopleR
    {
        public double? miles { get; set; }
    }

    [NotMapped]
    public class v_people_1_ext1 : PeopleR
    {
        public int count_ { get; set; }

        public Int64 rowNo { get; set; }
    }
     
}
