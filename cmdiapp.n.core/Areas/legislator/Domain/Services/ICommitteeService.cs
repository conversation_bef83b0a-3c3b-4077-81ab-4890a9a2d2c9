﻿using cmdiapp.n.core.Areas.legislator.Domain.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace cmdiapp.n.core.Areas.legislator.Domain.Services
{
    interface ICommitteeService
    {
        Committee GetCommittee(string committeeId);

        List<CommitteeMemberView> GetCommitteeMembers(string committeeId);

        List<Committee> GetSubcommittees(string parentCommitteeId);
    }
}
