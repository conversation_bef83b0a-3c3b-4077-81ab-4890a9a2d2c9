﻿using System.Collections.Generic;
using System.Web.Mvc;

using cmdiapp.n.core.Areas.crm.Domain.Models;
using cmdiapp.n.core.Areas.crm.Domain.ViewModels;
using cmdiapp.n.core.Areas.crm.Domain.Repositories;

namespace cmdiapp.n.core.Areas.crm.Domain.Services
{
    public interface IreceiptService
    {
        List<v_receipts_ext> get_v_receipts(string from, string where, string orderBy, int pageSize, int pageNo);
        List<v_receipts_ext> get_v_receipts_unlink_memo(string where, string orderBy, int pageSize, int pageNo);
        int saveReceipt(Txn _txn);
        int addReceipt(Txn _txn);
        int deleteReceipt(string _sql);
    }
}
