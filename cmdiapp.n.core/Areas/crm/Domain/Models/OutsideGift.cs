﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Data;
using System.Linq;
using System.Web.Mvc;
using System.Data.Linq;
using System.ComponentModel.DataAnnotations.Schema;
using cmdiapp.n.core.Areas.crm.Domain.Data;

using cmdiapp.n.core.Domain.ViewModels;

namespace cmdiapp.n.core.Areas.crm.Domain.Models
{

    [Table("OutsideGift")]
    public class OUTSIDEGIFT : _entityBase_crm, iItemType
    {
        [Column]
        [Key]
        public int ID { get; set; }
        [Column]
        public int PID { get; set; }
        [Column]
        public String ORGANIZATION { get; set; }
        [Column]
        public DateTime? GIFTDATE { get; set; }
        [Column]
        public decimal? GIFTAMOUNT { get; set; }
        [Column]
        public string NOTE { get; set; }
    }
}