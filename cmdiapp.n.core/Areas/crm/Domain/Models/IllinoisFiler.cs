﻿using cmdiapp.n.core.Areas.crm.Domain.Data;
using System;
using System.ComponentModel.DataAnnotations.Schema;
using System.Runtime.Serialization;

namespace cmdiapp.n.core.Areas.crm.Domain.Models
{
	[Table("x_il_filerInfo")]
	[DataContract]
    public class IllinoisFiler : _entityBase_crm
    {
        [Column("id")]
		[DataMember(Name = "id")]
		public int Id { get; set; }

		[Column("balanceAtStart")]
		[DataMember(Name = "balanceAtStart")]
		public decimal BalanceAtStart { get; set; }

		[Column("FilerID")]
		[DataMember(Name = "FilerID")]
		public string FilerID { get; set; }

		[Column("committeeName")]
		[DataMember(Name = "committeeName")]
		public string CommitteeName { get; set; }

		[Column("address1")]
		[DataMember(Name = "address1")]
		public string Address1 { get; set; }

		[Column("address2")]
		[DataMember(Name = "address2")]
		public string Address2 { get; set; }

		[Column("city")]
		[DataMember(Name = "city")]
		public string City { get; set; }

		[Column("state")]
		[DataMember(Name = "state")]
		public string State { get; set; }

		[Column("zip")]
		[DataMember(Name = "zip")]
		public string Zip { get; set; }

		[Column("createdOn")]
		[DataMember(Name = "createdOn")]
		public DateTime CreatedOn { get; set; }

		[Column("updatedOn")]
		[DataMember(Name = "updatedOn")]
		public DateTime UpdatedOn { get; set; }

		[Column("updatedByUid")]
		[DataMember(Name = "updatedByUid")]
		public int UpdatedByUid { get; set; }

		[Column("itemizationAmount")]
		[DataMember(Name = "itemizationAmount")]
		public decimal ItemizationAmount { get; set; }

    }
}