﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Data;
using System.Web.Mvc;
using cmdiapp.n.core.Domain.Models;
using cmdiapp.n.core.Areas.crm.Domain.Models;

namespace cmdiapp.n.core.Areas.crm.Core
{
    public class ImportStep5ViewModel
    {
        public int? RECNO { get; set; }
        public string STATUS { get; set; }
        public int? ID { get; set; }
        public string RECTYPE { get; set; }
        public string PEOTYPE { get; set; }
        public string PREFIX { get; set; }
        public string FNAME { get; set; }
        public string MNAME { get; set; }
        public string LNAME { get; set; }
        public string SUFFIX { get; set; }
        public string EMPLOYER { get; set; }
        public string OCCUPATION { get; set; }
        public string TITLE { get; set; }
        public string ADDRTYPE { get; set; }
        public string STREET { get; set; }
        public string ADDR1 { get; set; }
        public string ADDR2 { get; set; }
        public string CITY { get; set; }
        public string STATE { get; set; }
        public string ZIP { get; set; }
        public string PLUS4 { get; set; }
        public string BSTREET { get; set; }
        public string BADDR1 { get; set; }
        public string BADDR2 { get; set; }
        public string BCITY { get; set; }
        public string BSTATE { get; set; }
        public string BZIP { get; set; }
        public string BPLUS4 { get; set; }
        public string HMPHONE { get; set; }
        public DateTime? HomePhoneVerificationDate { get; set; }
        public string BSPHONE { get; set; }
        public DateTime? BusinessPhoneVerificationDate { get; set; }
        public string FAX { get; set; }
        public DateTime? FaxVerifiedVerificationDate { get; set; }
        public string CELLPHONE { get; set; }
        public DateTime? CellPhoneVerificationDate { get; set; }
        public string EMAIL { get; set; }
        public DateTime? EmailVerificationDate { get; set; }
        public string SecondaryEMAIL { get; set; }
        public string URL { get; set; }
        public DateTime? URLVerificationDate { get; set; }
        public string SPOUSENAME { get; set; }
        public string PRIMEMAIL { get; set; }
        public string INFSALUT { get; set; }
        public string SALUTATION { get; set; }
        public string MAILSALUTATION { get; set; }
        public string MAILNAME { get; set; }
        public string ASSISTANT { get; set; }
        public string ASSTBSPHONE { get; set; }
        public string ASSTEMAIL { get; set; }
        public DateTime? DOB { get; set; }
        public string FECCMTEID { get; set; }
        public string CPREFIX { get; set; }
        public string CFNAME { get; set; }
        public string CMNAME { get; set; }
        public string CLNAME { get; set; }
        public string CSUFFIX { get; set; }
        public string CTITLE { get; set; }
        public string cSTREET { get; set; }
        public string cADDR1 { get; set; }
        public string cADDR2 { get; set; }
        public string cCITY { get; set; }
        public string cSTATE { get; set; }
        public string cZIP { get; set; }
        public string cPLUS4 { get; set; }
        public string cEMAIL { get; set; }
        public string cHMPHONE { get; set; }
        public string cBSPHONE { get; set; }
        public string cFAX { get; set; }
        public string cCELLPHONE { get; set; }
        public string cINFSALUT { get; set; }
        public int? TXNNO { get; set; }
        public string BATCHNO { get; set; }
        public DateTime? BATCHDTE { get; set; }
        public DateTime? RECVDTE { get; set; }
        public decimal? AMT { get; set; }
        public string FUNDCODE { get; set; }
        public string SOURCE { get; set; }
        public string MONYTYPE { get; set; }
        public string COMMENT { get; set; }
        public string CKNO { get; set; }
        public string ACCTNO { get; set; }
        public int? mTRACKNO { get; set; }
        public string REFERENCEID { get; set; }
        public string CCREFNO { get; set; }


        public string CHANNEL1 { get; set; }
        public string CLUBCODE { get; set; }
        public string CLUBSTATUS { get; set; }
        public string CANDIDATE { get; set; }
        public string SOLICITOR { get; set; }
        public DateTime? RNEWDTE { get; set; }

        public decimal? COMMITMENT { get; set; }
        public int? RECRUITERNO { get; set; }
        public string EARMARKED { get; set; }
        public string INDUSTRY { get; set; }
        public int? pTRACKNO { get; set; }
        public int? FUNDID { get; set; }
        public string CENTERCODE { get; set; }
        public string FLAG1 { get; set; }
        public string FLAG2 { get; set; }
        public string FLAG3 { get; set; }
        public string FLAG4 { get; set; }
        public string FLAG5 { get; set; }
        public string FLAG6 { get; set; }
        public string FLAG7 { get; set; }
        public string FLAG8 { get; set; }
        public string FLAG9 { get; set; }
        public string FLAG10 { get; set; }
        public string KEYWORD1 { get; set; }
        public string KEYWORD2 { get; set; }
        public string KEYWORD3 { get; set; }
        public string KEYWORD4 { get; set; }
        public string KEYWORD5 { get; set; }
        public string KEYWORD6 { get; set; }
        public string KEYWORD7 { get; set; }
        public string KEYWORD8 { get; set; }
        public string KEYWORD9 { get; set; }
        public string KEYWORD10 { get; set; }
        public string KEYWORD11 { get; set; }
        public string KEYWORD12 { get; set; }
        public string KEYWORD13 { get; set; }
        public string KEYWORD14 { get; set; }
        public string KEYWORD15 { get; set; }
        public string KEYWORD16 { get; set; }
        public string KEYWORD17 { get; set; }
        public string KEYWORD18 { get; set; }
        public string KEYWORD19 { get; set; }
        public string KEYWORD20 { get; set; }
        public string EVNTCODE1 { get; set; }
        public string EVNTCODE2 { get; set; }
        public string EVNTCODE3 { get; set; }
        public string ISSUE1 { get; set; }
        public string ISSUE2 { get; set; }
        public string ISSUE3 { get; set; }
        public string ACTIONCODE1 { get; set; }
        public string ACTIONCODE2 { get; set; }
        public string ACTIONCODE3 { get; set; }
        public string CAMPGNCODE { get; set; }
        public string GIFTTYPE { get; set; }
        public string MONYCODE { get; set; }

        //public DateTime? JOINDTE { get; set; }
        public string ACTIVITY1 { get; set; }
        public string ACTIVITY2 { get; set; }
        public string ACTIVITY3 { get; set; }
        public string ACTIVITY4 { get; set; }
        public string ACTIVITY5 { get; set; }
        public string ACTIVITY6 { get; set; }
        public string ACTIVITY7 { get; set; }
        public string GROUP1 { get; set; }
        public string GROUP2 { get; set; }
        public string GROUP3 { get; set; }
        public string GROUP4 { get; set; }
        public string GROUP5 { get; set; }
        public string GROUPSTATUS1 { get; set; }
        public string GROUPSTATUS2 { get; set; }
        public string GROUPSTATUS3 { get; set; }
        public string GROUPSTATUS4 { get; set; }
        public string GROUPSTATUS5 { get; set; }

        //Added on 8/17/2015 for Conduit
        public int? CONDUITNO1 { get; set; }
        public int? CONDUITNO2 { get; set; }
        public int? CONDUITNO3 { get; set; }
        public int? CONDUITNO4 { get; set; }
        public int? CONDUITNO5 { get; set; }
        public int? CONDUITNO6 { get; set; }
        public int? CONDUITNO7 { get; set; }
        public int? CONDUITNO8 { get; set; }
        public int? CONDUITNO9 { get; set; }
        public int? CONDUITNO10 { get; set; }
        public int? CONDUITNO11 { get; set; }
        public int? CONDUITNO12 { get; set; }
        public int? CONDUITNO13 { get; set; }
        public int? CONDUITNO14 { get; set; }
        public int? CONDUITNO15 { get; set; }
        public decimal? CONDUITAMT1 { get; set; }
        public decimal? CONDUITAMT2 { get; set; }
        public decimal? CONDUITAMT3 { get; set; }
        public decimal? CONDUITAMT4 { get; set; }
        public decimal? CONDUITAMT5 { get; set; }
        public decimal? CONDUITAMT6 { get; set; }
        public decimal? CONDUITAMT7 { get; set; }
        public decimal? CONDUITAMT8 { get; set; }
        public decimal? CONDUITAMT9 { get; set; }
        public decimal? CONDUITAMT10 { get; set; }
        public decimal? CONDUITAMT11 { get; set; }
        public decimal? CONDUITAMT12 { get; set; }
        public decimal? CONDUITAMT13 { get; set; }
        public decimal? CONDUITAMT14 { get; set; }
        public decimal? CONDUITAMT15 { get; set; }

        public string ATTRIBUTENAME { get; set; }
        public DateTime? ATTRIBUTESTARTDATE { get; set; }
        public DateTime? ATTRIBUTEENDDATE { get; set; }
        public string ATTRIBUTEACTIVE { get; set; }

    }

    public class ImportStep5ViewModelJFC
    {
        public string PREFIX { get; set; }
        public string FIRSTNAME { get; set; }
        public string MIDDLENAME { get; set; }
        public string LASTNAME { get; set; }
        public string SUFFIX { get; set; }
        public string EMPLOYER { get; set; }
        public string OCCUPATION { get; set; }
        public string ADDRTYPE { get; set; }
        public string STREET { get; set; }
        public string ADDR1 { get; set; }
        public string ADDR2 { get; set; }
        public string CITY { get; set; }
        public string STATE { get; set; }
        public string ZIP { get; set; }
        public string PLUS4 { get; set; }
        public string HMPHN { get; set; }
        public string BSPHN { get; set; }
        public string FAX { get; set; }
        public string CELL { get; set; }
        public string EMAIL { get; set; }
        
    }

    public class ImportStep5ViewModelSRCCODE
    {
        //FOR SRCCODE IMPORT
        public string STATUS { get; set; }
        public int? PKGEID { get; set; }
        public string SRCECODE { get; set; }
        public string SRCEDESC { get; set; }
        public string SRCEDESC2 { get; set; }
        public string SRCEDESC3 { get; set; }
        public string LISTNOG { get; set; }
        public string LISTNO { get; set; }
        public DateTime? MAILDTE { get; set; }
        public int? sQTYMAIL { get; set; }
        public decimal? COSTPROD1 { get; set; }
        public decimal? COSTPROD2 { get; set; }
        public decimal? COSTPOSTG { get; set; }
        public decimal? COSTRESP1 { get; set; }
        public decimal? COSTFLAT { get; set; }
        public decimal? COSTPERC { get; set; }

    }

    public class ImportStep5ViewModelAction
    {
        public int? RECNO { get; set; }
        public string STATUS { get; set; }
        public int? ID { get; set; }
        public string RECTYPE { get; set; }
        public string PEOTYPE { get; set; }
        public string PREFIX { get; set; }
        public string FNAME { get; set; }
        public string MNAME { get; set; }
        public string LNAME { get; set; }
        public string SUFFIX { get; set; }
        public string EMPLOYER { get; set; }
        public string OCCUPATION { get; set; }
        public string TITLE { get; set; }
        public string ADDRTYPE { get; set; }
        public string STREET { get; set; }
        public string ADDR1 { get; set; }
        public string ADDR2 { get; set; }
        public string CITY { get; set; }
        public string STATE { get; set; }
        public string ZIP { get; set; }
        public string PLUS4 { get; set; }
        public string HMPHONE { get; set; }
        public string BSPHONE { get; set; }
        public string FAX { get; set; }
        public string CELLPHONE { get; set; }
        public string EMAIL { get; set; }
        public string URL { get; set; }

        public string ActionName { get; set; }
        public DateTime? ActionDate { get; set; }
        public string actionSRCECODE { get; set; }
        public string ActionNote { get; set; }
        public string actionFIELD01 { get; set; }
        public string actionFIELD01VALUE { get; set; }
        public string actionFIELD02 { get; set; }
        public string actionFIELD02VALUE { get; set; }
        public string actionFIELD03 { get; set; }
        public string actionFIELD03VALUE { get; set; }

    }

    public class ImportStep5ViewModelMassAdjustment
    {
        public string STATUS { get; set; }
        public string STATUSINFO { get; set; }
        public int? PID { get; set; }
        public string MatchType { get; set; }
        public string FNAME { get; set; }
        public string LNAME { get; set; }
        public DateTime? MRCDate { get; set; }
        public decimal? CTD { get; set; }
        public string STREET { get; set; }
        public string ADDR1 { get; set; }
        public string ADDR2 { get; set; }
        public string CITY { get; set; }
        public string STATE { get; set; }
        public string ZIP { get; set; }
        public string PLUS4 { get; set; }
        public string EMAIL { get; set; }
        public string SPOUSENAME { get; set; }
        public int? MID { get; set; }
        public DateTime? Date { get; set; }
        public string ADJTYPE { get; set; }
        public decimal? Amount { get; set; }
        public string CCREFNO { get; set; }
        public string MONYCOMMENT { get; set; }

    }

    public class ImportStep5ViewModelMassAdjustmentUnmatched
    {
        public string STATUS { get; set; }
        public string STATUSINFO { get; set; }
        public int? MID { get; set; }
        public DateTime? Date { get; set; }
        public decimal? Amount { get; set; }
        public string CCREFNO { get; set; }
        public string AMTNOTMATCH { get; set; }
    }
    public class ImportStep5ViewModelMassAdjustmentProcessed
    {
        public string STATUS { get; set; }
        public string STATUSINFO { get; set; }
        public int? MID { get; set; }
        public DateTime? Date { get; set; }
        public decimal? Amount { get; set; }
        public string CCREFNO { get; set; }
        public int? ADJMID { get; set; }

    }
    public class MassAdjustmentCounts
    {
        public int Matched { get; set; }
        public int Adjusted { get; set; }
        public int UnMatched { get; set; }
    }

    public class ImportCorpMatchStep5ViewModel
    {
        public int? RECNO { get; set; }
        public int? TXNNO { get; set; }
        public string STATUS { get; set; }
        public int? ID { get; set; }
        public string PEOTYPE { get; set; }
        public string CHAPCODE { get; set; }
        public string PREFIX { get; set; }
        public string FNAME { get; set; }
        public string MNAME { get; set; }
        public string LNAME { get; set; }
        public string SUFFIX { get; set; }
        public string EMPLOYER { get; set; }
        public string OCCUPATION { get; set; }
        public string TITLE { get; set; }
        public string PEOSTR1 { get; set; }
        public string ADDRTYPE { get; set; }
        public string STREET { get; set; }
        public string ADDR1 { get; set; }
        public string ADDR2 { get; set; }
        public string CITY { get; set; }
        public string STATE { get; set; }
        public string ZIP { get; set; }
        public string PLUS4 { get; set; }
        public string HMPHONE { get; set; }
        public string BSPHONE { get; set; }
        public string FAX { get; set; }
        public string CELLPHONE { get; set; }
        public string EMAIL { get; set; }
        public string FLAG1 { get; set; }
        public string FLAG2 { get; set; }
        public string FLAG3 { get; set; }
        public string KEYWORD1 { get; set; }
        public string KEYWORD2 { get; set; }
        public string KEYWORD3 { get; set; }
        public string GIFTTYPE { get; set; }
        public string BATCHNO { get; set; }
        public DateTime? BATCHDTE { get; set; }
        public Int16? MONYCODE { get; set; }
        public DateTime? GIFTDTE { get; set; }
        public decimal? AMT { get; set; }
        public string MONYTYPE { get; set; }
        public string FUNDCODE { get; set; }
        public string SRCECODE { get; set; }
        public string CENTERCODE { get; set; }
        public string CAMPGNCODE { get; set; }
        public string COMMENT { get; set; }
        public int? COMPRECNO { get; set; }
        public int? COMPTXNNO { get; set; }
        public int? COMPID { get; set; }
        public string COMPPEOTYPE { get; set; }
        public string COMPCHAPCODE { get; set; }
        public string COMPNAME { get; set; }
        public string COMPADDRTYPE { get; set; }
        public string COMPSTREET { get; set; }
        public string COMPADDR1 { get; set; }
        public string COMPADDR2 { get; set; }
        public string COMPCITY { get; set; }
        public string COMPSTATE { get; set; }
        public string COMPZIP { get; set; }
        public string COMPPLUS4 { get; set; }
        public string COMPBSPHN { get; set; }
        public string COMPFAX { get; set; }
        public string COMPEMAIL { get; set; }
        public string COMPFLAG1 { get; set; }
        public string COMPFLAG2 { get; set; }
        public string COMPFLAG3 { get; set; }
        public string COMPKEYWORD1 { get; set; }
        public string COMPKEYWORD2 { get; set; }
        public string COMPKEYWORD3 { get; set; }
        public string COMPBATCHNO { get; set; }
        public DateTime? COMPBATCHDTE { get; set; }
        public Int16? COMPMONYCODE { get; set; }
        public DateTime? COMPGIFTDTE { get; set; }
        public decimal? COMPAMT { get; set; }
        public string COMPMONYTYPE { get; set; }
        public string COMPFUNDCODE { get; set; }
        public string COMPSRCECODE { get; set; }
        public string COMPCENTERCODE { get; set; }
        public string COMPCAMPGNCODE { get; set; }
        public string COMPCOMMENT { get; set; }
    }

    public class ImportPledgeStep5ViewModel
    {
        public int? RECNO { get; set; }
        public int? PLEDGEID { get; set; }
        public string STATUS { get; set; }
        public int? ID { get; set; }
        public string PEOTYPE { get; set; }
        public string PREFIX { get; set; }
        public string FNAME { get; set; }
        public string MNAME { get; set; }
        public string LNAME { get; set; }
        public string SUFFIX { get; set; }
        public string EMPLOYER { get; set; }
        public string OCCUPATION { get; set; }
        public string ADDRTYPE { get; set; }
        public string STREET { get; set; }
        public string ADDR1 { get; set; }
        public string ADDR2 { get; set; }
        public string CITY { get; set; }
        public string STATE { get; set; }
        public string ZIP { get; set; }
        public string PLUS4 { get; set; }
        public string HMPHONE { get; set; }
        public string BSPHONE { get; set; }
        public string FAX { get; set; }
        public string CELLPHONE { get; set; }
        public string EMAIL { get; set; }
        public DateTime? PLEDGEDTE { get; set; }
        public decimal? PLEDGEAMT { get; set; }
        public DateTime? EXPECTDTE { get; set; }
        public DateTime? HOUSEDTE { get; set; }
        public decimal? HOUSEAMT { get; set; }
        public string FUNDCODE { get; set; }
        public string SRCRCODE { get; set; }
        public int? TRACKNO { get; set; }
        public decimal? TRACKAMT { get; set; }
        public string COMMENT { get; set; }
    }

    public class ImportTributeStep5ViewModel
    {
        public int? RECNO { get; set; }
        public int? TXNNO { get; set; }
        public string STATUS { get; set; }
        public int? ID { get; set; }
        public string PEOTYPE { get; set; }
        public string CHAPCODE { get; set; }
        public string PREFIX { get; set; }
        public string FNAME { get; set; }
        public string MNAME { get; set; }
        public string LNAME { get; set; }
        public string SUFFIX { get; set; }
        public string EMPLOYER { get; set; }
        public string OCCUPATION { get; set; }
        public string TITLE { get; set; }
        public string PEOSTR1 { get; set; }
        public string SALUTATION { get; set; }
        public string INFSALUTATION { get; set; }
        public string MAILSALUTATION { get; set; }
        public string ADDRTYPE { get; set; }
        public string STREET { get; set; }
        public string ADDR1 { get; set; }
        public string ADDR2 { get; set; }
        public string CITY { get; set; }
        public string STATE { get; set; }
        public string ZIP { get; set; }
        public string PLUS4 { get; set; }
        public string HMPHONE { get; set; }
        public string BSPHONE { get; set; }
        public string FAX { get; set; }
        public string CELLPHONE { get; set; }
        public string EMAIL { get; set; }
        public string FLAG1 { get; set; }
        public string FLAG2 { get; set; }
        public string FLAG3 { get; set; }
        public string KEYWORD1 { get; set; }
        public string KEYWORD2 { get; set; }
        public string KEYWORD3 { get; set; }
        public string GIFTTYPE { get; set; }
        public string BATCHNO { get; set; }
        public DateTime? BATCHDTE { get; set; }
        public Int16? MONYCODE { get; set; }
        public DateTime? GIFTDTE { get; set; }
        public decimal? AMT { get; set; }
        public string MONYTYPE { get; set; }
        public string FUNDCODE { get; set; }
        public string SRCECODE { get; set; }
        public string CENTERCODE { get; set; }
        public string CAMPGNCODE { get; set; }
        public string COMMENT { get; set; }
        public string MEMTYPE { get; set; }
        public int? MEMPID { get; set; }
        public string MEMNAME { get; set; }
        public int? ACKWPID { get; set; }
        public int? MEMRECNO { get; set; }
        public int? MEMTXNNO { get; set; } 
        public int? ACKWRECNO { get; set; }
        public int? ACKWTXNNO { get; set; }
        
    }

    public class ImportSupportStep5ViewModel
    {
        public int? RECNO { get; set; }
        public int? TXNNO { get; set; }
        public string STATUS { get; set; }
        public int? ORIGMID { get; set; }
        public int? ID { get; set; }
        public DateTime? ADJDTE { get; set; }
        public decimal? AMT { get; set; }
        public string MONYCOMMENT { get; set; }

    }

}

