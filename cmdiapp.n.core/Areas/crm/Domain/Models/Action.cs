﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Data;
using System.Linq;
using System.Web.Mvc;
using System.Data.Linq;
using System.ComponentModel.DataAnnotations.Schema;
using cmdiapp.n.core.Areas.crm.Domain.Data;

using cmdiapp.n.core.Domain.ViewModels;
using Newtonsoft.Json;

namespace cmdiapp.n.core.Areas.crm.Domain.Models
{   
    [Table("Action")]
    public class Action_ : _entityBase_crm, iItemType
    {
        [Column]
        [Key]
        public int id { get; set; }

        [Column]
        public int categoryId { get; set; }
        [ForeignKey("categoryId")]
        public virtual ActionCategory Category { get; set; }
        [Column]
        public int? attributeId { get; set; }
        [Column]
        public string name { get; set; }

        [DatabaseGenerated(DatabaseGeneratedOption.Computed)]
        public string fullNameC { get; set; }

        [Column]
        public int? defaultSourceId { get; set; }
        [Column]
        public DateTime? startDate { get; set; }

        [Column]
        public DateTime? endDate { get; set; }
        [Column]
        public string note { get; set; }

        [Column]
        public string customizedColumns { get; set; }
        
        [NotMapped]
        public List<customizedColumnsData> customizedColumnsData
        {
            get
            {
                if (this.customizedColumns != null)
                    return JsonConvert.DeserializeObject<List<customizedColumnsData>>(this.customizedColumns);
                else
                    return new List<customizedColumnsData>();
            }
        }

        [Column]
        public string fromOtherDataInfo { get; set; }

        [Column]
        public DateTime? createdAtUtc { get; set; }

        [Column]
        public DateTime? updatedAtUtc { get; set; }

    }

    [Table("ActionCategory")]
    public class ActionCategory : _entityBase_crm, iItemType
    {
        [Column]
        [Key]
        public int id { get; set; }

        [Column]
        public string name { get; set; }

        [Column]
        public string labeling { get; set; }

        [Column]
        public string note { get; set; }
        
        [Column]
        public DateTime? createdAtUtc { get; set; }

        [Column]
        public DateTime? updatedAtUtc { get; set; }
        [Column]
        public string customizedColumns { get; set; }

        [NotMapped]
        public List<customizedColumnsData> customizedColumnsData
        {
            get
            {
                if (this.customizedColumns != null)
                    return JsonConvert.DeserializeObject<List<customizedColumnsData>>(this.customizedColumns);
                else
                    return new List<customizedColumnsData>();
            }
        }
    }
       
    [Table("ActionPeople")]
    public class ActionPeople : _entityBase_crm, iItemType
    {
        [Column]
        [Key]
        public int id { get; set; }

        [Column]
        public int PID { get; set; }

        [Column]
        public int actionId { get; set; }
        [ForeignKey("actionId")]
        public virtual Action_ Action { get; set; }

        [NotMapped]
        public string ActionName
        {
            get
            {
                return Action != null && Action.name != null ? Action.name : null;
            }
        }
        [NotMapped]
        public string CategoryName
        {
            get
            {
                return Action != null && Action.Category != null && Action.Category.name != null ? Action.Category.name : null;
            }
        }
        [Column]
        public DateTime actionDate { get; set; }

        [Column]
        public int? srceId { get; set; }

        [NotMapped]
        public string srccode { get; set; }
        [Column]
        public string note { get; set; }

        [Column]
        public string customizedData { get; set; }
        
        [NotMapped]
        public List<customizedColumnsData> customizedColumnsData
        {
            get
            {
                if (this.customizedData != null)
                    return JsonConvert.DeserializeObject<List<customizedColumnsData>>(this.customizedData);
                else
                    return new List<customizedColumnsData>();
            }
        }
        [NotMapped]
        public string customizedColumnsDataString
        {
            get
            {
                if (this.customizedColumnsData.Count > 0)
                {
                    return this.customizedColumnsData[0].name;// + " - " + this.customizedColumnsData[0].value != null ? this.customizedColumnsData[0].value.ToString() : "";
                }
                else
                {
                    return "";
                }
            }
        }

        [DatabaseGenerated(DatabaseGeneratedOption.Computed)]
        public int? actionYear { get; set; }

        [Column]
        public DateTime? createdAtUtc { get; set; }

        [Column]
        public DateTime? updatedAtUtc { get; set; }

    }


    [Table("ACTIONPEOPLEDOC")]
    public class ACTIONPEOPLEDOC : _entityBase_crm, iItemType
    {
        [Column]
        public int ACTIONPEOPLEDOCID { get; set; }

        [Column]
        public int? ACTIONPEOPLEID { get; set; }

        [Column]
        public string FILENAME { get; set; }

        [Column]
        public byte[] CONTENT { get; set; }

        [Column("_updating_uid")]
        public short? updating_uid { get; set; }
    }
    public class ActionPeople_export : _entityBase_crm, iItemType
    {
        public int id { get; set; }
        public int PID { get; set; }
        public int actionId { get; set; }
        public string ActionName { get; set; }
        public string CategoryName { get; set; }
        public DateTime actionDate { get; set; }
        public int? srceId { get; set; }
        public string srccode { get; set; }
        public string note { get; set; }
        public string customizedData { get; set; }
        public int? actionYear { get; set; }
        public DateTime? createdAtUtc { get; set; }
        public DateTime? updatedAtUtc { get; set; }
    }

    public class ActionPeople_ext: ActionPeople_export
    {
        public int count_ { get; set; }
        public Int64 rowNo { get; set; }
    }

    public class Action_ext : _entityBase_crm, iItemType
    {
        public int id { get; set; }
        public int categoryId { get; set; }
        public int? attributeId { get; set; }
        public string name { get; set; }
        public string fullNameC { get; set; }
        public int? defaultSourceId { get; set; }
        public DateTime? startDate { get; set; }
        public DateTime? endDate { get; set; }
        public string note { get; set; }
        public string customizedColumns { get; set; }
        public string fromOtherDataInfo { get; set; }
        public DateTime? createdAtUtc { get; set; }

        public int count_ { get; set; }
        public Int64 rowNo { get; set; }
    }
    public class ActionView : _entityBase_crm, iItemType
    {
        public int id { get; set; }
        public int categoryId { get; set; }
        public int? attributeId { get; set; }
        public string name { get; set; }
        public string fullNameC { get; set; }
        public int? defaultSourceId { get; set; }
        public DateTime? startDate { get; set; }
        public DateTime? endDate { get; set; }
        public string note { get; set; }
        public string customizedColumns { get; set; }
        public string fromOtherDataInfo { get; set; }
        public DateTime? createdAtUtc { get; set; }
    }
    public class ActionCategory_ext : _entityBase_crm, iItemType
    {
        public int id { get; set; }
        public string name { get; set; }
        public string labeling { get; set; }
        public string note { get; set; }
        public DateTime? createdAtUtc { get; set; }
        public DateTime? updatedAtUtc { get; set; }
        public int count_ { get; set; }
        public Int64 rowNo { get; set; }

    }
    public class ActionCategoryView : _entityBase_crm, iItemType
    {
        public int id { get; set; }
        public string name { get; set; }
        public string labeling { get; set; }
        public string note { get; set; }
        public DateTime? createdAtUtc { get; set; }
        public DateTime? updatedAtUtc { get; set; }

    }

    public class customizedColumnsData
    {
        public string name { get; set; }
        public string type { get; set; }
        public List<string> lookup { get; set; }
        public string valid { get; set; }
        public int? min { get; set; }
        public int? max { get; set; }
        public object value { get; set; }
    }
}