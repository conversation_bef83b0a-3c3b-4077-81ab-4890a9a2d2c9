﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

using Ninject;
using Ninject.Web.Mvc;

using cmdiapp.n.core.Core;
using cmdiapp.n.core.Domain.Models;
using cmdiapp.n.core.Core.App_Start;
using cmdiapp.n.core.Areas.crm.Core;
using cmdiapp.n.core.Areas.crm.Domain.Models;
using cmdiapp.n.core.Areas.crm.Domain.ViewModels;
using cmdiapp.n.core.Areas.crm.Domain.Repositories;

using System.Reflection.Emit;
using System.Reflection;
using cmdiapp.n.core.Areas.crm.Domain.Data;
using cmdiapp.n.core.Areas.legislator.Domain.Models;
using cmdiapp.n.core.Domain.ViewModels;
using System.Data.Entity.Infrastructure;
using System.Data.SqlClient;
using System.Linq.Expressions;
using cmdiapp.n.core.Library;
using cmdiapp.n.core.Areas.crm.ViewModels;

namespace cmdiapp.n.core.Areas.crm.Domain.Services
{
    public class vendorService : IvendorService
    {
        private I___unitOfWork _unitOfWork;
        private I__DBFactory _dbFactory;
        private I_entity_crm _entity_crm;
        private ICrmLegislatorService _crmLegislatorService;

        public vendorService(
            I___unitOfWork unitOfWork, 
            I__DBFactory dbFactory,
            I_entity_crm entity_crm, 
            ICrmLegislatorService crmLegislatorService)
        {
            _unitOfWork = unitOfWork;
            _dbFactory = dbFactory;
            _entity_crm = entity_crm;
            _crmLegislatorService = crmLegislatorService;
        }

        public List<v_entity_ext> get_v_vendor_list(string where, string orderBy, int pageSize, int pageNo)
        {
            string sql = string.Format("SELECT * FROM v_entity_list WHERE {0}", where);

            var q = _dbFactory.getContext().Database.SqlQuery<v_entity_ext>(String.Format("EXEC dbo.get_pagedDataSet '{0}', '{1}', {2}, {3}", sql, orderBy, pageSize, pageNo));

            return q.ToList();
        }

        public int addVendor(Vendor _vendor)
        {
            linkLegislatorIfCandFecIdMatches(_vendor);
            _entity_crm.Add(_vendor);
            _entity_crm.CommitChanges();
            return _vendor.ENTITYID;
        }

        public int saveVendor(Vendor _vendor)
        {
            linkLegislatorIfCandFecIdMatches(_vendor);
            _entity_crm.Update(_vendor);
            _entity_crm.CommitChanges();
            return _vendor.ENTITYID;
        }

        private void linkLegislatorIfCandFecIdMatches(Vendor vendor)
        {
            if (!string.IsNullOrEmpty(vendor.CANDFECID))
            {
                Legislator matchedLegislator = _crmLegislatorService.GetLegislatorByFecId(vendor.CANDFECID);
                if (matchedLegislator != null)
                {
                    vendor.LEGISLATORID = matchedLegislator.Id;
                }
                else
                {
                    // if none found, we should delete any link to member
                    if (!string.IsNullOrEmpty(vendor.LEGISLATORID))
                    {
                        vendor.LEGISLATORID = null;
                    }
                }
            }
        }

        public bool check_TxnExistsForVendor(int entityid)
        {
            string sql = string.Format("select (select Count(*) FROM TXN Where ENTITYID = {0}) + (select count(*) FROM INVOICE where ENTITYID = {0}) as recordCount", entityid);

            var q = _dbFactory.getContext().Database.SqlQuery<UniquenessCheck>(sql);
            int isZero = q.FirstOrDefault().recordCount;
            return !(isZero == 0);
        }

        public int deleteVendor(string _sql)
        {
            var q = _dbFactory.getContext().Database.SqlQuery<Int32>(_sql);
            return q.FirstOrDefault();
        }

        public byte[] pictureOf(int entityid)
        {
            var q = _dbFactory.getContext().Database.SqlQuery<entityPicture>(String.Format("SELECT TOP 1 entityid, picture FROM ENTITY WHERE ENTITYID={0}", entityid));
            entityPicture _result = q.FirstOrDefault();
            if (_result != null)
                return q.FirstOrDefault().PICTURE;
            else
                return null;
        }

        public List<DataTableFilter> GetTxnStatementFilters(int entityId, string txnType)
        {
            DataTableFilterOption[] funds = (from fund in _entity_crm.All<dmFUND>()
                                             join txn in _entity_crm.All<Txn>()
                                                on fund.FUNDID equals txn.FUNDID
                                             join type in _entity_crm.All<lkTxnType>()
                                                on txn.TXNTYPEID equals type.TXNTYPEID
                                             where txn.ENTITYID == entityId && type.CODE == txnType
                                             select new DataTableFilterOption()
                                             {
                                                 label = fund.FUNDCODE,
                                                 value = fund.FUNDCODE
                                             }).Distinct().ToArray();

            DataTableFilterOption[] adjTypes = (from adjType in _entity_crm.All<lkADJTYPE>()
                                                join txn in _entity_crm.All<Txn>()
                                                    on adjType.ADJTYPEID equals txn.ADJTYPEID
                                                join type in _entity_crm.All<lkTxnType>()
                                                    on txn.TXNTYPEID equals type.TXNTYPEID
                                                where txn.ENTITYID == entityId && type.CODE == txnType
                                                select new DataTableFilterOption()
                                                {
                                                    label = adjType.DESCRIP,
                                                    value = adjType.DESCRIP
                                                }).Distinct().ToArray();
            return new List<DataTableFilter>()
            {
                new DataTableFilter
                {
                    label = "Fund",
                    options = funds,
                    paramName = "fundCode"
                },
                new DataTableFilter
                {
                    label = "Adjustment Type",
                    options = adjTypes,
                    paramName = "adjType"
                },
                DateFilterFactory<iItemType>.GetDataTableFilter(
                    txnType == "R" ? "Receipt Date" : "Disbursement Date", "date"),
                DateFilterFactory<iItemType>.GetDataTableFilter(
                    "Adjustment Date", "adjDate")
            };
        }

        public Tuple<int, List<v_entity_txn>> GetTxnStatement(
            int entityId, string txntype, int page, int pageSize,
            string fundCode = "", string adjType = "", string txnDate = "", string adjDate = "")
        {
            // need to build query twice since SqlParameters are not automatically
            // disposed with each execution
            int count = GetMainTxnStatementQuery(entityId, txntype, fundCode, adjType, txnDate, adjDate).Count();

            List<v_entity_txn> results = GetMainTxnStatementQuery(
                entityId, txntype, fundCode, adjType, txnDate, adjDate)
                .OrderByDescending(t => t.SORTDTE)
                .ThenBy(t => string.IsNullOrEmpty(t.ROOT) ? -1 : 1)
                .Skip((page - 1) * pageSize)
                .Take(pageSize)
                .ToList();

            return new Tuple<int, List<v_entity_txn>>(count, results);
        }

        private Expression<Func<IGrouping<string, v_entity_txn>, bool>> TxnStatementFilter(
            string fundCode, string adjType, string txnDate, string adjDate)
        {
            // split all multi-value filter inputs (separated by '|')
            IEnumerable<string> fundSplit = fundCode?.Split('|')?.Select(f => f.Trim()).ToList() ?? new List<string>();
            IEnumerable<string> adjSplit = adjType?.Split('|')?.Select(a => a.Trim()).ToList() ?? new List<string>();

            Expression<Func<IGrouping<string, v_entity_txn>, bool>> expression =
                g => (string.IsNullOrEmpty(fundCode)
                        || g.Any(t =>
                            !string.IsNullOrEmpty(t.FUNDCODE) && fundSplit.Contains(t.FUNDCODE)))
                    && (string.IsNullOrEmpty(adjType)
                        || g.Any(t => adjSplit.Contains(t.ADJTYPE)));

            // conjoin date expressions if specified
            if (!string.IsNullOrEmpty(txnDate))
            {
                Expression<Func<IGrouping<string, v_entity_txn>, bool>> batchDateExpression =
                    g => g.AsQueryable()
                        .Any(new DateFilterFactory<v_entity_txn>(txnDate, "TXNDTE", false, "TXNDTE").Predicate);
                expression = expression.AndAlsoSafe(batchDateExpression);
            }

            if (!string.IsNullOrEmpty(adjDate))
            {
                Expression<Func<IGrouping<string, v_entity_txn>, bool>> adjDateExpression =
                    g => g.AsQueryable()
                    .Any(new DateFilterFactory<v_entity_txn>(adjDate, "ADJDTE", false, "ADJDTE").Predicate);
                expression = expression.AndAlsoSafe(adjDateExpression);
            }

            return expression;
        }

        private IQueryable<v_entity_txn> GetMainTxnStatementQuery(
            int entityId, string txntype, string fundCode, string adjType, string txnDate, string adjDate)
        {
            return GetTxnStatementSqlQuery(entityId, txntype)
                // group by SORTSEQ since we want to select all TXNS in group if one txn passes filter
                .GroupBy(m => m.SORTSEQ)
                .AsQueryable()
                // apply filter
                .Where(TxnStatementFilter(fundCode, adjType, txnDate, adjDate))
                .AsEnumerable()
                // flatten back into an IEnumerable<TxnStatement>
                .SelectMany(g => g)
                .AsQueryable();
        }

        private DbRawSqlQuery<v_entity_txn> GetTxnStatementSqlQuery(int entityId, string txnType)
        {
            return _dbFactory.getContext().Database.SqlQuery<v_entity_txn>(
                @"SELECT
                ID,
                TXNID,
                ROOT,
                ENTITYID,
                VENDOR,
                CODE,
                TXNDTE,
                AMT,
                ADJTYPE,
                ADJDTE,
                FUNDCODE,
                ISMEMO,
                DESCRIP,
                FECTYPE,
                CHKNO,
                ELECTYR,
                SORTSEQ,
                SORTDTE,
                FECDESCRIP
                FROM fn_GenTxnStatement(@entityId, @txnType)",
                new SqlParameter("@entityId", entityId),
                new SqlParameter("@txnType", txnType));
        }
    }
}