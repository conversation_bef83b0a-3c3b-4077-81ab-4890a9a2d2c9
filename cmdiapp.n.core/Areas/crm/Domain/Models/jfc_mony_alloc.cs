﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Data;
using System.Linq;
using System.Web.Mvc;
using System.Data.Linq;
using System.ComponentModel.DataAnnotations.Schema;

using cmdiapp.n.core.Domain.ViewModels;
using cmdiapp.n.core.Areas.crm.Domain.Data;
using System.ComponentModel;

namespace cmdiapp.n.core.Areas.crm.Domain.Models
{
    [Table("MONYALLOC")]
    public class MONYALLOC : _entityBase_crm, iItemType
    {
        [Column]
        [Key]
        public int ALLOCID { get; set; }

        [Column]
        public int MID { get; set; }

        [Column]
        public Int16 JFCCMTEID { get; set; }

        [Column]
        public Nullable<decimal> AMT { get; set; }

        [Column("_updating_UID")]
        public Nullable<Int16> UPDATING_UID { get; set; }

        [Column]
        public bool LOCKED { get; set; }

    }

    public class jfc_mony_alloc
    {
        public Int16 jfccmteid { get; set; }

        public string cmtename { get; set; }

        public Int16 seq { get; set; }

        public Nullable<int> allocid { get; set; }

        public int mid { get; set; }

        public Nullable<decimal> amt { get; set; }

        public bool active { get; set; }

        public bool locked { get; set; }
    }
}
