﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Data;
using System.Linq;
using System.Web.Mvc;
using System.Data.Linq;
using System.ComponentModel.DataAnnotations.Schema;
using cmdiapp.n.core.Areas.crm.Domain.Data;

using cmdiapp.n.core.Domain.ViewModels;

namespace cmdiapp.n.core.Areas.crm.Domain.Models
{   
    [Table("fileAttachment")]
    public class fileAttachment : _entityBase_crm, iItemType
    {
        [Column]
        [Key]
        public int id { get; set; }

        [Column]
        public int fileAttachmentTypeId { get; set; }

        [Column]
        public string name { get; set; }

        [Column]
        public string filename { get; set; }

        [Column]
        public string tableIdFieldValue { get; set; }

        [Column]
        public DateTime updatedAt { get; set; }

        [Column]
        public byte[] fileBinary { get; set; }

        [Column]
        public int? uploadedby { get; set; }
    }

    public class fileAttachment2 : _entityBase_crm, iItemType
    {
        public int id { get; set; }

        public int fileAttachmentTypeId { get; set; }

        public string name { get; set; }

        public string filename { get; set; }

        public string tableIdFieldValue { get; set; }

        public DateTime updatedAt { get; set; }

        public string updatedAts { get; set; }

        public byte[] fileBinary { get; set; }

        public int? uploadedby { get; set; }

        public string fileUploadedBy { get; set; }
    }
}