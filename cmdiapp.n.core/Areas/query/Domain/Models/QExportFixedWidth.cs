﻿using System;
using System.Collections.Generic;
using System.Data;
using System.IO;
using System.Text;
using System.Linq;
using System.Web;
using System.Web.Hosting;

using cmdiapp.n.core.Core;

namespace cmdiapp.n.core.Areas.query.Domain.Models
{
    public class QExportFixedWidth : IQExportFormat
    {
        public string FileFormat { get; set; }
        public bool IncludeHeader { get; set; }
        public int[] FieldWidths { get; set; }

        public QExportFixedWidth(int[] fieldWidths, bool includeHeader=true, string fileExtension="sdf")
        {
            FileFormat = fileExtension;
            IncludeHeader = includeHeader;
            FieldWidths = fieldWidths;
        }

        public Tuple<string, string> ToFile(DataTable result, string sheetName)
        {
            Tuple<string, string> fileNames = GetFileNames(sheetName);
            SaveFile(result, fileNames.Item1);

            return fileNames;
        }

        private void SaveFile(DataTable result, string fileName)
        {
            string fileString = GetFileString(result, FieldWidths, IncludeHeader);
            using (StreamWriter writer = new StreamWriter(HostingEnvironment.MapPath($"~/tmp/{fileName}")))
            {
                writer.Write(fileString);
            }
        }

        private string GetFileString(DataTable table, int[] widths, bool includeHeader)
        {
            
            // check to make sure we don't have more columns than widths
            if (table.Columns.Count > widths.Count())
            {
                throw new Exception($"Got {table.Columns.Count} DataColumns but only {widths.Count()} field lengths.");
            }
            // check to make sure all widths >= 0
            if (widths.Any(x => x < 0))
            {
                throw new Exception("Got a negative field width.");
            }
            StringBuilder sb = new StringBuilder();
            // add header if specified
            if (includeHeader)
            {
                foreach (DataColumn col in table.Columns)
                {
                    sb.Append(FixWidth(col.ColumnName?.ToString() ?? "", widths[col.Ordinal]));
                }
                sb.Append(Environment.NewLine);
            }
            // add data
            foreach (DataRow row in table.Rows)
            {
                foreach (DataColumn col in table.Columns)
                {
                    string valString = row[col.ColumnName]?.ToString() ?? "";
                    sb.Append(FixWidth(valString, widths[col.Ordinal]));
                }

                sb.Append(Environment.NewLine);
            }

            return sb.ToString();
        }

        private static string FixWidth(string str, int width)
        {
            string result = str;
            if (result.Length >= width)
            {
                result = result.Substring(0, width);
            }
            else
            {
                result = result.PadRight(width);
            }

            return result;
        }

        private Tuple<string, string> GetFileNames(string sheetName)
        {
            string sToday = String.Format("{0:yyyyMMMdd__hh_mm_ss tt}", DateTime.Now);
            string clientFileName =
                $"{sheetName.Trim().Replace(" ", "")}_{session.userSession.UserName}_{sToday}.{FileFormat}";
            string serverFileName =
                $"{sheetName.Trim().Replace(" ", "")}_{Guid.NewGuid()}_{session.userSession.UserName}_{sToday}.{FileFormat}";

            return new Tuple<string, string>(serverFileName, clientFileName);
        }
    }

}