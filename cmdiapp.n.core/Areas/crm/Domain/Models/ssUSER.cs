﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Data;
using System.Linq;
using System.Web.Mvc;
using System.Data.Linq;
using System.ComponentModel.DataAnnotations.Schema;
using cmdiapp.n.core.Areas.crm.Domain.Data;

using cmdiapp.n.core.Domain.ViewModels;

namespace cmdiapp.n.core.Areas.crm.Domain.Models
{
    // Updated by Lydia 2013-10-22
    [Table("ssUSER")]
    public class ssUSER : _entityBase_crm, iItemType
    {
        [Column]
        [Key]
        public short UID { get; set; }
        [Column]
        public int UGROUPID { get; set; }
        [Column]
        public string USERID { get; set; }
        [Column]
        public string FNAME { get; set; }
        [Column]
        public string MNAME { get; set; }
        [Column]
        public string LNAME { get; set; }
        [Column]
        public string eMail { get; set; }
        [Column]
        public string FAXNO { get; set; }
        [Column]
        public Nullable<System.DateTime> LASTLOGON { get; set; }
        [Column]
        public bool ACTIVE { get; set; }
        [Column]
        public Nullable<System.DateTime> CREATEDON { get; set; }
        [Column]
        public string VERSION { get; set; }
        [Column]
        public Nullable<int> rs_default { get; set; }
        [Column]
        public string rs_user_param { get; set; }
        [Column]
        public Nullable<int> TRACKNO { get; set; }
        [Column]
        public string et_user_param { get; set; }
        [Column]
        public Nullable<int> PID { get; set; }
        [Column]
        public Nullable<int> CLUBID { get; set; }
        [Column]
        public byte[] PICTURE { get; set; }
    }


    [Table("v_gateway_users")]
    public class v_gateway_users : _entityBase_crm, iItemType
    {
        [Column]
        public int projectid { get; set; }
        [Column]
        [Key]
        public short uid { get; set; }
        [Column]
        public string username { get; set; }
        [Column]
        public string fname { get; set; }
        [Column]
        public string lname { get; set; }
        [Column]
        public string fullname { get; set; }
        [Column]
        public string usergroup { get; set; }
        [Column]
        public string email { get; set; }
        [Column]
        public DateTime? lastlogindate { get; set; }
        [NotMapped]
        public string LASTLOGONDTE
        {
            set => lastlogindate = Convert.ToDateTime(value);
            get => lastlogindate == null ? "" : ((DateTime)lastlogindate).ToShortDateString();
        }
    }

    public class activeUsersSel : _entityBase_crm, iItemType
    {
        public string USERID { get; set; }
        public string FULLNAME { get; set; }
    }
}