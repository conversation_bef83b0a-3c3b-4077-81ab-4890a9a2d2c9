﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Net;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Web.Http;
using System.Web.Script.Serialization;
using System.Drawing;
using System.IO;
using System.Runtime.Serialization.Json;

using Ninject;
using Ninject.Web.Mvc;

using AutoMapper;

using cmdiapp.n.core.Core;
using cmdiapp.n.core.Core.App_Start;
using cmdiapp.n.core.Library;
using cmdiapp.n.core.Domain.ViewModels;
using cmdiapp.n.core.Presentation.Controllers;
using cmdiapp.n.core.Areas.crm.Core;
using cmdiapp.n.core.Areas.crm.Domain.Models;
using cmdiapp.n.core.Areas.crm.Domain.Data;
using cmdiapp.n.core.Areas.crm.Domain.Services;
using cmdiapp.n.core.Areas.query.Domain.Models;
using cmdiapp.n.core.Areas.query;
using cmdiapp.n.core.Areas.dataService.Domain.Data;
using cmdiapp.n.core.Areas.dataService.Domain.Models;
using cmdiapp.n.core.Areas.dataService.Domain.Services;

namespace cmdiapp.n.core.Areas.crm.Presentation.Controllers.api
{
    ///  (controller) InitiativeQ

    [Authorize]
    public class InitiativeQController : ApiController
    {
        #region [[ Declaration ]]
        private I_entity_crm _entity_crm;
        private IdataService _dataService;
        #endregion

        #region [[ (constructor)  InitiativeQController ]]
        public InitiativeQController()
        {
            _entity_crm = I_entityManager_crm.getEntity();
            _dataService = I_entityManager_ds.getService();
        }
        #endregion

        #region [[ sub routines for Search & Export ]]

        #region [ (private-List<PACKAGE_ext>) Quick Text Search ]
        private List<PACKAGE_ext> quickTextSearch(string searchText, int pageSize, int pageNo, string sortOptions, string qDefName = "crm_initiativeSearch")
        {

            string _where = "";

            #region [[ Search by ID if numeric ]]
            int id;
            if (int.TryParse(searchText, out id))
            {
                //nrsc's PKGECODE might be numerical
                //when input is numerical, search both PKGEID and PKGECODE
                _where = string.Format("F.PKGEID = {0} OR F.PKGECODE LIKE '%{0}%'", id);
            }
            #endregion

            #region [[ Otherwise, Search by String ]]
            else if (!string.IsNullOrEmpty(searchText))
            {

                #region [ Split Search Text > string1, string2 & string3 and create "Where" statement ]
                char[] delimiterChars = { ' ', ',', '.', ';', ':', '|', '\t' };

                string[] words = searchText.Split(delimiterChars);

                if (words.Count() > 0)
                {
                    string string1 = words[0];
                    string string2 = (words.Count() > 1 ? words[1] : "");
                    string string3 = (words.Count() > 2 ? words[2] : "");
                                     
                    #region [ Where Clause ]                   

                    _where = string.Format("PKGECODE LIKE '%{0}%' OR PKGEDESC LIKE '%{0}%'", words[0]);
                    for (int i = 1; i < words.Length; i++)
                    {
                        _where = _where + string.Format("OR PKGECODE LIKE '%{0}%' OR PKGEDESC LIKE '%{0}%'", words[i]);
                    }


                    //_where = string.Format("PKGECODE LIKE '%{0}%'", searchText);
                    
                    #endregion

                }
                else
                    return new List<PACKAGE_ext>();
                #endregion

            }
            #endregion

            // No search text is given, return an empty dataset
            else
                return new List<PACKAGE_ext>();

            #region [[ Run a Query ]]
            if (string.IsNullOrEmpty(sortOptions)) sortOptions = "PKGEID";

            // SQL Definition
            q_def_S _def = q_library.get_q_def_fr_cache(qDefName);

            // Compose SQL
            string sql = string.Format(@"
                            SELECT DISTINCT 
                            {0}
                            FROM {1}
                            WHERE {2}
                        ", _def.sq_fieldsV, _def.sq_from, _where);

            sql = sql.Trim().Replace("'", "''");

            System.Diagnostics.Stopwatch stopWatch = new System.Diagnostics.Stopwatch();
            stopWatch.Start();
            var q = _entity_crm.getContext().Database.SqlQuery<PACKAGE_ext>(String.Format("EXEC dbo.get_pagedDataSet '{0}', '{1}', {2}, {3}", sql, sortOptions, pageSize, pageNo));

            List<PACKAGE_ext> result = q.ToList();
            stopWatch.Stop();

            cl_query.create_a_sqlLog(
                        sql.Trim(),
                        "crm/api/InitiativeQ/Search(Adv)",
                        util.serialize_toXmlString(_where),
                        crmSession.UID().Value,
                        (result != null && result.Count() > 0 ? result.FirstOrDefault().count_ : 0),
                        "initiative Search - View Columns",
                        "",
                        _entity_crm,
             Convert.ToSingle(stopWatch.ElapsedMilliseconds / 1000.0));

            return result;
            #endregion
        }
        #endregion

        private System.Data.DataSet Export_QuickSearch(string searchText, string qDefName = "crm_initiativeSearch")
        {
            string _where = "";

            #region [[ Search by ID if numeric ]]
            int id;
            if (int.TryParse(searchText, out id))
            {
                _where = string.Format("F.PKGEID = {0}", id);
            }
            #endregion

            #region [[ Otherwise, Search by String ]]
            else if (!string.IsNullOrEmpty(searchText))
            {

                #region [ Split Search Text > string1, string2 & string3 and create "Where" statement ]
                char[] delimiterChars = { ' ', ',', '.', ';', ':', '|', '\t' };

                string[] words = searchText.Split(delimiterChars);

                if (words.Count() > 0)
                {
                    string string1 = words[0];
                    string string2 = (words.Count() > 1 ? words[1] : "");
                    string string3 = (words.Count() > 2 ? words[2] : "");

                    #region [ Where Clause ]

                    _where = string.Format("PKGECODE LIKE '%{0}%' OR PKGEDESC LIKE '%{0}%'", words[0].Replace("'", "''"));
                    for (int i = 1; i < words.Length; i++)
                    {
                        _where = _where + string.Format("OR PKGECODE LIKE '%{0}%' OR PKGEDESC LIKE '%{0}%'", words[i].Replace("'", "''"));
                    }

                    #endregion

                }
                
                #endregion

            }
            else
            {
                _where = " 1 = 1";
            }
            #endregion

            // SQL Definition
            q_def_S _def = q_library.get_q_def_fr_cache(qDefName);

            // Compose SQL
            string sql = string.Format(@"
                            SELECT DISTINCT 
                            {0}
                            FROM {1}
                            WHERE {2}
                        ", _def.sq_fieldsV, _def.sq_from, _where);

            System.Data.DataSet _dataSet = q_library.get_dataset_w_sql__single(session.currentDomain_project._connectionString(), sql, "result", 0, 0);
            //return _dataSet.Tables[0];
            return _dataSet;
            
        }


        #region [ (private) Advanced Search - SQL Filtering ]

        #region [ func.string.get_sqlFIELDs ] Enable optional OUTPUT fields if used.
        private string get_sqlFIELDs(string p_sql_where, string p_default_fields)
        {

            #region if Political (In other words, Not a Non-profit), include
            if (!string.IsNullOrEmpty(session.userSession.getConfigVal(crmConstants.nonProfit))
                || session.userSession.getConfigVal(crmConstants.nonProfit).ToUpper() != "Y")
            {
                p_default_fields = p_default_fields.Replace("/*ADDR-PoliticalOnly", "");
                p_default_fields = p_default_fields.Replace("ADDR-PoliticalOnly*/", "");
            }
            #endregion

            #region if Political (GIfts), include
            if (!string.IsNullOrEmpty(session.userSession.getConfigVal(crmConstants.nonProfit))
                || session.userSession.getConfigVal(crmConstants.nonProfit).ToUpper() != "Y")
            {
                p_default_fields = p_default_fields.Replace("/*GIFT-PoliticalOnly", "");
                p_default_fields = p_default_fields.Replace("GIFT-PoliticalOnly*/", "");
            }
            #endregion

            return p_default_fields;
        }
        #endregion

        #endregion

        #region [ (private-List<PACKAGE_ext>) Advanced Search ]
        private List<PACKAGE_ext> sqlSearch(List<searchItem> filters, int pageSize, int pageNo, string sortOptions, string qDefName = "crm_initiativeSearch")
        {
            if (string.IsNullOrEmpty(sortOptions)) sortOptions = "PKGEID";

            // SQL Definition
            q_def_S _def = q_library.get_q_def_fr_cache(qDefName);

            // Compose SQL
            string sql = q_library.getSQL(_def, filters, session.currentDomain_project._connectionString(), null, get_sqlFIELDs, true);

            string sqlParam = sql.Trim().Replace("'", "''");
            System.Diagnostics.Stopwatch stopWatch = new System.Diagnostics.Stopwatch();
            stopWatch.Start();
            var q = _entity_crm.getContext().Database.SqlQuery<PACKAGE_ext>(String.Format("EXEC dbo.get_pagedDataSet '{0}', '{1}', {2}, {3}", sqlParam, sortOptions, pageSize, pageNo));

            List<PACKAGE_ext> result = q.ToList();
            stopWatch.Stop();

            cl_query.create_a_sqlLog(
                        sql.Trim(),
                        "crm/api/InitiativeQ/Search(Adv)",
                        util.serialize_toXmlString(filters),
                        crmSession.UID().Value,
                        (result != null && result.Count() > 0 ? result.FirstOrDefault().count_ : 0),
                        "initiative Search - View Columns",
                        "",
                        _entity_crm,
             Convert.ToSingle(stopWatch.ElapsedMilliseconds / 1000.0));

            return result;
        }
        #endregion

        #region [ (private-System.Data.DataTable) Advanced Search - Export ]

        //private System.Data.DataTable sqlSearch_forExport__dataset(List<searchItem> filters)
        private System.Data.DataSet  sqlSearch_forExport__dataset(List<searchItem> filters)
        {

            // SQL Definition
            q_def_S _def = q_library.get_q_def_fr_cache("crm_initiativeSearch");

            // Compose SQL
            string sql = q_library.getSQL(_def, filters, session.currentDomain_project._connectionString(), null, get_sqlFIELDs, false);
            sql = sql.Replace("SELECT DISTINCT", "SELECT DISTINCT TOP 100000"); // ############ LIMIT TO 100K FOR NOW)           
            System.Diagnostics.Stopwatch stopWatch = new System.Diagnostics.Stopwatch();
            stopWatch.Start();

            System.Data.DataSet _dataSet = q_library.get_dataset_w_sql__single(session.currentDomain_project._connectionString(), sql, "result", 0, 0);
            stopWatch.Stop();

            cl_query.create_a_sqlLog(
            sql.Trim(),
            "crm/api/InitiativeQ/Export",
            util.serialize_toXmlString(filters),
            crmSession.UID().Value,
            (_dataSet != null && _dataSet.Tables.Count > 0 && _dataSet.Tables[0].Rows.Count > 0 ? _dataSet.Tables[0].Rows.Count : 0),
            "Initiative Search - Export Columns",
            "HTTP",     // Saved Path: HTTP Flush 
            _entity_crm,
             Convert.ToSingle(stopWatch.ElapsedMilliseconds / 1000.0));

            //return _dataSet.Tables[0];
            return _dataSet;
        }

        private List<PACKAGE_ext> sqlSearch_forExport__list(List<searchItem> filters)
        {
            // SQL Definition
            q_def_S _def = q_library.get_q_def_fr_cache("crm_initiativeSearch");

            // Compose SQL
            string sql = q_library.getSQL(_def, filters, session.currentDomain_project._connectionString(), null, get_sqlFIELDs, false);
            sql = sql.Replace("SELECT DISTINCT", "SELECT DISTINCT TOP 100000"); // ############ LIMIT TO 100K FOR NOW)        

            var q = _entity_crm.getContext().Database.SqlQuery<PACKAGE_ext>(String.Format("{0}", sql));
            return q.ToList();
        }
        #endregion

        #region [ (private-List<PACKAGE_ext>) All Text Search ]
        private List<PACKAGE_ext> allTextSearch(int pageSize, int pageNo, string sortOptions, string qDefName = "crm_initiativeSearch")
        {
            string _where = "";

            #region [[ Run a Query ]]
            if (string.IsNullOrEmpty(sortOptions)) sortOptions = "PKGEID";

            // SQL Definition
            q_def_S _def = q_library.get_q_def_fr_cache(qDefName);

            // Compose SQL
            string sql = string.Format(@"
                            SELECT DISTINCT 
                            {0}
                            FROM {1}
                            ", _def.sq_fieldsV, _def.sq_from);

            sql = sql.Trim().Replace("'", "''");

            System.Diagnostics.Stopwatch stopWatch = new System.Diagnostics.Stopwatch();
            stopWatch.Start();
            var q = _entity_crm.getContext().Database.SqlQuery<PACKAGE_ext>(String.Format("EXEC dbo.get_pagedDataSet '{0}', '{1}', {2}, {3}", sql, sortOptions, pageSize, pageNo));

            List<PACKAGE_ext> result = q.ToList();
            stopWatch.Stop();

            cl_query.create_a_sqlLog(
                        sql.Trim(),
                        "crm/api/InitiativeQ/Search(Adv)",
                        util.serialize_toXmlString(_where),
                        crmSession.UID().Value,
                        (result != null && result.Count() > 0 ? result.FirstOrDefault().count_ : 0),
                        "initiative Search - View Columns",
                        "",
                        _entity_crm,
             Convert.ToSingle(stopWatch.ElapsedMilliseconds / 1000.0));

            return result;
            #endregion
        }
        #endregion

        #endregion

        #region [[ (genericResponse) Search[GET] - crm/api/InitiativeQ/Search ]]
        [HttpGet, Route("crm/api/InitiativeQ/Search")]
        [apiAuthorize(AccessElement = "cmdiapp.dms.packages", AccessLevel = "v")]
        public genericResponse Search(string searchText, int? page, int? pageSize, string qDefName = "crm_initiativeSearch")
        {
            #region [ Retrieve Input ]
            string _searchText = Library.util.kill_sqlBlacklistWord((searchText ?? "").Trim().Replace("'", "''''"));

            if (string.IsNullOrEmpty(_searchText))
                return new genericResponse() { __count = 0, success = true };

            int _pageSize = (pageSize == null || pageSize.Value == 0 ? 10 : pageSize.Value);
            int _pageNo = (page == null || page.Value == 0 ? 1 : page.Value);

            string sortField = HttpContext.Current.Request.QueryString["sort[0][field]"];
            string sortDir = HttpContext.Current.Request.QueryString["sort[0][dir]"];
            string _sortOptions = "";
            if (!string.IsNullOrEmpty(sortField))
                _sortOptions = sortField;
            if (!string.IsNullOrEmpty(sortField) && !string.IsNullOrEmpty(sortDir))
                _sortOptions = _sortOptions + " " + sortDir;
            #endregion

            #region [ Quick Text Search ]
            if (!string.IsNullOrEmpty(_searchText))
            {
                List<PACKAGE_ext> _list = quickTextSearch(_searchText, _pageSize, _pageNo, _sortOptions, qDefName);
                if (_list != null && _list.Count() > 0)
                {
                    Mapper.CreateMap<PACKAGE_ext, packageR>();
                    IEnumerable<iItemType> results = _list.Select(a => Mapper.Map<PACKAGE_ext, packageR>(a)).ToList();
                    return new genericResponse() { success = true, __count = _list.FirstOrDefault().count_, results = results.ToList() };
                }
            }
            #endregion

            return new genericResponse() { success = true, __count = 0 };
        }
        #endregion

        #region [[ (genericResponse) Search[POST] - crm/api/InitiativeQ/Search ]]
        [HttpPost, Route("crm/api/InitiativeQ/Search")]
        [apiAuthorize(AccessElement = "cmdiapp.dms.packages", AccessLevel = "v")]
        public genericResponse Search(searchParam _param, string qDefName = "crm_initiativeSearch")
        {
            #region [ Retrieve Input ]
            string _searchText = _param.searchText_;

            //if (string.IsNullOrEmpty(_searchText)
            //    && (_param.searchData == null || _param.searchData.Count() == 0 || string.IsNullOrEmpty(_param.searchData[0].valuef)))
            //{
            //    return new genericResponse() { __count = 0, success = true };
            //}

            int _pageSize = _param.pageSize_;
            int _pageNo = _param.page_;
            string _sortOptions = _param.sortOption1_;
            #endregion

            #region [ HTTPGET - Retrieve "Sort" options ]
            /*
            string sortField = HttpContext.Current.Request.QueryString["sort[0][field]"];
            string sortDir = HttpContext.Current.Request.QueryString["sort[0][dir]"];
            */
            #endregion

            List<PACKAGE_ext> _list;

            #region [ 0. All Text Search ]
            if (string.IsNullOrEmpty(_searchText)
                && (_param.searchData == null || _param.searchData.Count() == 0 || string.IsNullOrEmpty(_param.searchData[0].valuef)))
                _list = allTextSearch(_pageSize, _pageNo, _sortOptions);
            #endregion

            #region [ 1. Quick Text Search ]
            else if (!string.IsNullOrEmpty(_searchText))
                _list = quickTextSearch(_searchText, _pageSize, _pageNo, _sortOptions);
            #endregion

            #region [ 2. Search Data ]
            else
                _list = sqlSearch(_param.searchData, _pageSize, _pageNo, _sortOptions, qDefName);
            #endregion

            if (_list != null && _list.Count() > 0)
            {
                Mapper.CreateMap<PACKAGE_ext, packageR>();
                IEnumerable<iItemType> results = _list.Select(a => Mapper.Map<PACKAGE_ext, packageR>(a)).ToList();
                return new genericResponse() { success = true, __count = _list.FirstOrDefault().count_, results = results.ToList() };
            }

            return new genericResponse() { success = true, __count = 0 };
        }
        #endregion

        #region [[ Export ]]
        [HttpPost, Route("crm/api/InitiativeQ/ExportKey")]
        [apiAuthorize(AccessElement = "cmdiapp.dms.packages", AccessLevel = "e")]
        public string ExportKey(searchParam param)
        {
            string key_ = System.Guid.NewGuid().ToString();
            HttpRuntime.Cache.Insert(key_, param, null, DateTime.UtcNow.Add(new TimeSpan(1, 0, 1)), System.Web.Caching.Cache.NoSlidingExpiration);

            return key_;
        }

        [HttpGet, Route("crm/api/InitiativeQ/Export")]
        [apiAuthorize(AccessElement = "cmdiapp.dms.packages", AccessLevel = "e")]
        //public bool Export(string key)
        public string Export(string key)
        {
            key = key.Replace("\"", "").Trim();
            
            searchParam _param = (searchParam)HttpRuntime.Cache[key];           

            System.Data.DataSet _ds = sqlSearch_forExport__dataset(_param.searchData);

            if (_ds != null)
            {
                string key_ = System.Guid.NewGuid().ToString();
                HttpRuntime.Cache.Insert(key_, _ds, null, DateTime.UtcNow.Add(new TimeSpan(1, 0, 1)), System.Web.Caching.Cache.NoSlidingExpiration);
                return key_;
            }
            else
            {
                return null;
            }
            //return true;
        }
        #endregion

        #region [[ Quick Search Export ]]

        [HttpPost, Route("crm/api/InitiativeQ/QExportKey")]
        [apiAuthorize(AccessElement = "cmdiapp.dms.packages", AccessLevel = "e")]
        public string QExportKey(searchParam param)
        {
            string key_ = System.Guid.NewGuid().ToString();
            HttpRuntime.Cache.Insert(key_, param.searchText, null, DateTime.UtcNow.Add(new TimeSpan(1, 0, 1)), System.Web.Caching.Cache.NoSlidingExpiration);
            //back
            return key_;
        }

        [HttpGet, Route("crm/api/InitiativeQ/QExport")]
        [apiAuthorize(AccessElement = "cmdiapp.dms.packages", AccessLevel = "e")]
        public string QExport(string key)
        {
            key = key.Replace("\"", "").Trim();
            //get the SearchText Back...
            string searchText = (string)HttpRuntime.Cache[key];
            //Create Dataset now...
            System.Data.DataSet _ds = Export_QuickSearch(searchText);
            //go ahead...
            if (_ds != null)
            {
                string key_ = System.Guid.NewGuid().ToString();
                HttpRuntime.Cache.Insert(key_, _ds, null, DateTime.UtcNow.Add(new TimeSpan(1, 0, 1)), System.Web.Caching.Cache.NoSlidingExpiration);
                //return the Key
                return key_;
            }
            else
            {
                return null;
            }
        }


        #endregion


        [HttpGet, Route("crm/api/InitiativeQ/GetPackageData")]
        [apiAuthorize(AccessElement = "cmdiapp.dms.packages", AccessLevel = "v")]
        public genericResponse GetPackageData()
        {

            #region [[ Work with the Data ]]
            List<PACKAGERR_ext> _list = new List<PACKAGERR_ext>();
            var q = _entity_crm.getContext().Database.SqlQuery<PACKAGERR_ext>("SELECT P.PROGID, P.PROGTYPE, P.DESCRIP AS PROGRAM, K.PKGEID, K.PKGECODE, K.PKGEDESC, SUM(S.sQTYMAIL) AS MAILQTY, MIN(S.MAILDTE) AS MAILDTE, SUM(S.sMONY) AS NOGIFTS, SUM(S.sGROSS) AS GROSS, SUM(S.sCOST) AS COST, SUM(S.sNET) AS NET, MIN(S.FIRSTCAGE) AS FIRSTCAGE, MAX(S.LASTCAGE) AS LASTCAGE, ISNULL((SELECT COUNT(*) FROM SOURCE WHERE PKGEID = k.PKGEID ), 0) AS SOURCECOUNT, ISNULL( (SELECT COUNT(*) FROM SOURCE WHERE PKGEID = k.PKGEID and teleM = 1 ), 0) AS TELECOUNT FROM PROGRAM P INNER JOIN PACKAGE K ON P.PROGID=K.PROGID LEFT OUTER JOIN [SOURCE] S ON K.PKGEID=S.PKGEID WHERE K.MAILDTE>=dbo.oFDateMonth(DATEADD(M,-3, GETDATE())) GROUP BY P.PROGID, P.PROGTYPE, P.DESCRIP, K.PKGEID, K.PKGECODE, K.PKGEDESC ORDER BY MAILDTE DESC");
            _list = q.ToList();
            //let us process
            Mapper.CreateMap<PACKAGERR_ext, packageRR>();
            IEnumerable<iItemType> results = _list.Select(a => Mapper.Map<PACKAGERR_ext, packageRR>(a)).ToList();

            #endregion

            if (results.Count() > 0)
            {
                return new genericResponse() { success = true, __count = _list.FirstOrDefault().count_, results = results.ToList() };
            }
            else
            {
                return new genericResponse() { success = true, __count = 0 };
            }

        }

        [apiAuthorize(AccessElement = "cmdiapp.dms.packages", AccessLevel = "v")]
        [HttpPost, Route("crm/api/InitiativeQ/GetFilteredPackages")]
        public genericResponse GetFilteredPackages(packageBack _parm)
        {
            try
            {
                packageBack pkgFilter = new packageBack();
                string _where = "";

                string sql = "SELECT P.PROGID, P.PROGTYPE, P.DESCRIP AS PROGRAM, K.PKGEID, K.PKGECODE, K.PKGEDESC, SUM(S.sQTYMAIL) AS MAILQTY, MIN(S.MAILDTE) AS MAILDTE, SUM(S.sMONY) AS NOGIFTS, SUM(S.sGROSS) AS GROSS, SUM(S.sCOST) AS COST, SUM(S.sNET) AS NET, MIN(S.FIRSTCAGE) AS FIRSTCAGE, MAX(S.LASTCAGE) AS LASTCAGE, ISNULL((SELECT COUNT(*) FROM SOURCE WHERE PKGEID = k.PKGEID ), 0) AS SOURCECOUNT, ISNULL( (SELECT COUNT(*) FROM SOURCE WHERE PKGEID = k.PKGEID and teleM = 1 ), 0) AS TELECOUNT FROM PROGRAM P INNER JOIN PACKAGE K ON P.PROGID=K.PROGID LEFT OUTER JOIN [SOURCE] S ON K.PKGEID=S.PKGEID WHERE {0} GROUP BY P.PROGID, P.PROGTYPE, P.DESCRIP, K.PKGEID, K.PKGECODE, K.PKGEDESC ORDER BY MAILDTE DESC";

                if (_parm != null)
                {
                    //Let us check if PKGID is available....
                    if (_parm.PKGEID != null && _parm.PKGEID > 0)
                    {
                        _where = string.Format(" K.PKGEID IN ({0})", _parm.PKGEID);
                    }
                    if (_parm.PKGEID == null && !string.IsNullOrEmpty(_parm.PKGEDESC))
                    {
                        //Let us prepare...
                        _where =  string.Format(" K.PKGECODE LIKE '%{0}%'", _parm.PKGEDESC);
                    }
                    if (!string.IsNullOrEmpty(_parm.PKGEDESCRIP))
                    {
                        if (!string.IsNullOrEmpty(_where))
                            _where = _where + " AND ";
                        //Let us prepare...
                        _where = _where + string.Format(" K.PKGEDESC LIKE '%{0}%'", _parm.PKGEDESCRIP);
                    }
                    if (_parm.MAILDTE != null)
                    {
                        if (!string.IsNullOrEmpty(_where))
                            _where = _where + " AND ";
                        //Let us prepare...
                        _where = _where + string.Format(" K.MAILDTE >= '{0}'", _parm.MAILDTE);
                    }
                }

                sql = string.Format(sql, _where);
                
                List<PACKAGERR_ext> _list = new List<PACKAGERR_ext>();
                var q = _entity_crm.getContext().Database.SqlQuery<PACKAGERR_ext>(sql);
                _list = q.ToList();

                //let us process
                Mapper.CreateMap<PACKAGERR_ext, packageRR>();
                IEnumerable<iItemType> results = _list.Select(a => Mapper.Map<PACKAGERR_ext, packageRR>(a)).ToList();

                if (results.Count() > 0)
                {
                    return new genericResponse() { success = true, __count = _list.FirstOrDefault().count_, results = results.ToList() };
                }
                else
                {
                    return new genericResponse() { success = true, __count = 0 };
                }
            }
            catch(Exception ex)
            {
                return new genericResponse() { success = false, message = "Problem Finding Package data based on Filters." };
            }
        }
        
        const string _sql_base_source = @"SELECT 
                        (SELECT COUNT(DISTINCT SRCEID) FROM SOURCE S, PACKAGE P) AS CNT,
                        ISNULL(S.SRCEID,0) AS SRCEID,
                        ISNULL(S.PKGEID,0) AS PKGEID,
                        ISNULL(K.PKGECODE,'') AS PKGECODE,
                        ISNULL(K.PKGEDESC,'') AS PKGEDESC,
                        ISNULL(S.SRCECODE,'') AS SRCECODE,
                        ISNULL(S.SRCEDESC,'') AS SRCEDESC,
                        ISNULL(S.LISTNO,'') AS LISTNO,
                        -- For Mail Date
                        Case When S.MAILDTE = '1/1/1900' Then null
                        Else
                        ISNULL(S.MAILDTE,null) End AS MAILDTE,
                        -- For First Cage
                        Case When S.FIRSTCAGE = '1/1/1900' Then null
                        Else
                        ISNULL(S.FIRSTCAGE,null) End AS FIRSTCAGE,
                        -- For Last Cage
                        Case When S.LASTCAGE = '1/1/1900' Then null
                        Else
                        ISNULL(S.LASTCAGE,null) End AS LASTCAGE,
                        ISNULL(S.COSTPROD1,0.00) AS COSTPROD1,
                        ISNULL(S.COSTPROD2,0.00) AS COSTPROD2,
                        ISNULL(S.COSTPROD3,0.00) AS COSTPROD3,
                        ISNULL(S.COSTPOSTG,0.00) AS COSTPOSTG,
                        ISNULL(S.COSTRESP1,0.00) AS COSTRESP1,
                        ISNULL(S.COSTRESP2,0.00) AS COSTRESP2,
                        ISNULL(S.COSTRESP3,0.00) AS COSTRESP3,
                        ISNULL(S.sQTYMAIL,0) AS sQTYMAIL,
                        ISNULL(S.sMONY,0) AS sMONY,
                        ISNULL(S.sPEOPLE,0) AS sPEOPLE,
                        ISNULL(S.sGROSS,0.00) AS sGROSS,
                        ISNULL(S.sNET,0.00) AS sNET,
                        ISNULL(S.sCOST,0.00) AS sCOST,
                        ISNULL(S.sGROSSPM,0.00) AS sGROSSPM,
                        ISNULL(S.sNETPM,0.00) AS sNETPM,
                        ISNULL(S.sCOSTPM,0.00) AS sCOSTPM,
                        ISNULL(S.sGROSSRSP,0.00) AS sGROSSRSP,
                        ISNULL(S.sNETRSP,0.00) AS sNETRSP,
                        ISNULL(S.sCOSTRSP,0.00) AS sCOSTRSP,
                        ISNULL(S.sRSPPCT,0.00) AS sRSPPCT,
                        ISNULL(S.COMMENT,'') AS COMMENT,
                        ISNULL(S.UPDATEDON,null) AS UPDATEDON,
                        -- For Last Activity
                        Case When S.LASTACTI = '1/1/1900' Then null
                        Else
                        ISNULL(S.LASTACTI,null) End AS LASTACTI,
                        ISNULL(S.SACTIVITY,0) AS SACTIVITY
                        FROM SOURCE S
				        INNER JOIN PACKAGE K ON K.PKGEID = S.PKGEID 
                        {0} ORDER BY MAILDTE DESC
                  ";

        [HttpGet, Route("crm/api/InitiativeQ/GetSourceForPkg")]
        [apiAuthorize(AccessElement = "cmdiapp.dms.sources", AccessLevel = "v")]
        public genericResponse GetSourceForPkg(string pkgId)
        {
            
            List<SOURCE_ext> _list = new List<SOURCE_ext>();

            string sqlText = _sql_base_source;
            string _where = "";

            //Compose SQL query to get Source based on this Package
            _where = string.Format("WHERE S.PKGEID={0}", Convert.ToInt32(pkgId));
            sqlText = String.Format(sqlText, _where);

            var q = _entity_crm.getContext().Database.SqlQuery<SOURCE_ext>(sqlText);
            _list = q.ToList();

            //let us process
            Mapper.CreateMap<SOURCE_ext, rSOURCE>();
            IEnumerable<iItemType> results = _list.Select(a => Mapper.Map<SOURCE_ext, rSOURCE>(a)).ToList();

            if (results.Count() > 0)
            {
                return new genericResponse() { success = true, __count = _list.FirstOrDefault().count_, results = results.ToList() };
            }
            else
            {
                return new genericResponse() { success = true, __count = 0 };
            }
            
        }

    }
}
