﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Net;
using System.Web.Http;

using System.Data.SqlClient;
using System.Xml.Linq;
using System.Data.Linq.SqlClient;
using System.Data;


using Ninject;
using Ninject.Web.Mvc;
using cmdiapp.n.core.Core;
using cmdiapp.n.core.Domain.Models;
using cmdiapp.n.core.Domain.ViewModels;
using cmdiapp.n.core.Areas.crm.Core;
using cmdiapp.n.core.Areas.crm.Domain.Models;
using cmdiapp.n.core.Areas.crm.Domain.ViewModels;
using cmdiapp.n.core.Areas.crm.Domain.Data;
using cmdiapp.n.core.Areas.crm.Domain.Repositories;
using cmdiapp.n.core.Presentation.Controllers;

namespace cmdiapp.n.core.Areas.crm.Presentation.Controllers.api
{
    [apiAuthorize(AccessElement = "cmdiapp.dms.people_qq.mass_apend", AccessLevel = "v")]
    public class BatchAppendController : ApiController
    {
        int queueCt = 500;

        private I_entity_crm _entity_crm;

        public class flagreq
        {
            public int donorct { get; set; }
            public List<int> flags { get; set; }
            public string selectsql { get; set; }
            public string descrip { get; set; }
        }

        public class keywordreq
        {
            public int donorct { get; set; }
            public List<int> keywords { get; set; }
            public string selectsql { get; set; }
            public string descrip { get; set; }
        }

        public class notereq
        {
            public int donorct { get; set; }
            public string subject { get; set; }
            public string note { get; set; }
            public DateTime histdate { get; set; }
            public string selectsql { get; set; }
            public string descrip { get; set; }
            public string askMemo { get; set; }

        }

        public class taskreq
        {
            public int donorct { get; set; }
            public string subject { get; set; }
            public int acttypeid { get; set; }
            public int purposeid { get; set; }
            public int priorityid { get; set; }
            public DateTime actdate { get; set; }
            public int schedby { get; set; }
            public int schedfor { get; set; }
            public DateTime schedon { get; set; }
            public string note { get; set; }
            public string selectsql { get; set; }
            public string descrip { get; set; }
            public string askMemo { get; set; }

        }

        public class eventreq
        {
            public int donorct { get; set; }
            public int spcevntid { get; set; }
            public int status { get; set; }
            public string comment { get; set; }
            public DateTime attenddte { get; set; }
            public string tableno { get; set; }
            public string selectsql { get; set; }
            public string descrip { get; set; }
        }

        public class exceptionreq
        {
            public int giftct { get; set; }
            public int excepid { get; set; }
            public DateTime excepdte { get; set; }
            public string selectsql { get; set; }
            public string descrip { get; set; }
        }
        public class channelreq
        {
            public int giftct { get; set; }
            public int channelId { get; set; }
            public string selectsql { get; set; }
            public string descrip { get; set; }
        }
        public class attributereq
        {
            public string selectsql { get; set; }
            public string descrip { get; set; }
            public int donorct { get; set; }
            public List<int> attrIds { get; set; }
            public DateTime? startDate { get; set; }
            public DateTime? endDate { get; set; }
        }

        public class tracknoreq
        {
            public int giftct { get; set; }
            public int trackno { get; set; }
            public string selectsql { get; set; }
            public string descrip { get; set; }
        }

        public class createjob_result
        {
            public int JOBID { get; set; }
        }

        public BatchAppendController()
        {
            _entity_crm = I_entityManager_crm.getEntity();
        }

        [HttpPost, Route("crm/api/BatchAppend/appendFlag")]
        [apiAuthorize(AccessElement = "cmdiapp.dms.people_qq.mass_apend", AccessLevel = "e")]
        public genericResponse appendFlag(flagreq _flagreq)
        {
            try
            {
                string _selectsql = (string)HttpRuntime.Cache[_flagreq.selectsql];
                _selectsql = _selectsql.Replace("'", "''");
                _selectsql = HttpUtility.HtmlDecode(_selectsql);
                //_selectsql = "SELECT DISTINCT P.PID " + _selectsql.Substring(_selectsql.IndexOf("FROM"), _selectsql.Length - _selectsql.IndexOf("FROM"));
                string _insertsql = "";
                foreach (int flagid in _flagreq.flags)
                {
                    _insertsql += "INSERT jtFLAG (PID, FLAGID, _updating_uid) SELECT PID, " + flagid.ToString() + "," + crmSession.UID() + 
                        " FROM jtJOB WHERE JOBID = @JOBID AND PID NOT IN (SELECT PID FROM jtFLAG WHERE FLAGID = " + flagid.ToString() + "); ";
                }
                string createsql = "EXEC dbo.wp_create_job {0}, {1}, '{2}', '{3}', '{4}', {5}, '{6}'";
                createjob_result _createjob_result = _entity_crm.getContext().Database.SqlQuery<createjob_result>(string.Format(createsql, 1, @crmSession.UID(), _insertsql,
                    _selectsql, @session.userSession.UserName, _flagreq.donorct > queueCt ? 1 : 0, _flagreq.descrip)).FirstOrDefault();
                if (_createjob_result.JOBID > 0)
                    if (_flagreq.donorct <= queueCt)
                    {
                        string procsql = "EXEC @ROWCOUNT = dbo.wp_process_job {0}";
                        SqlParameter parm = new SqlParameter()
                        {
                            ParameterName = "@ROWCOUNT",
                            SqlDbType = SqlDbType.Int,
                            Direction = System.Data.ParameterDirection.Output
                        }; 
                        _entity_crm.getContext().Database.ExecuteSqlCommand(string.Format(procsql, _createjob_result.JOBID), parm);
                        int _rowcount = (int)parm.Value;
                        if (_rowcount > 0)
                            return new genericResponse() { success = true, message = _rowcount.ToString()+" Flag records created successfully" };
                        else if (_rowcount == 0)
                            return new genericResponse() { success = true, message = "Flag(s) already assigned to donors" };
                        else
                            return new genericResponse() { success = false, message = "Problem assigning Flags" };
                    }
                    else 
                    {
                        return new genericResponse() { success = true, message = "Flags are queued successfully to be processed" };
                    }
                    else
                        return new genericResponse() { success = false, message = "Problem assigning Flags" };
            }
            catch (Exception ex)
            {
                return new genericResponse() { success = false, message = "Problem assigning Flags" };
            }
        }

        [HttpPost, Route("crm/api/BatchAppend/appendKeyword")]
        [apiAuthorize(AccessElement = "cmdiapp.dms.people_qq.mass_apend", AccessLevel = "e")]
        public genericResponse appendKeyword(keywordreq _keywordreq)
        {
            try
            {
                string _selectsql = (string)HttpRuntime.Cache[_keywordreq.selectsql];
                _selectsql = _selectsql.Replace("'", "''");
                _selectsql = HttpUtility.HtmlDecode(_selectsql);
                //_selectsql = "SELECT DISTINCT P.PID " + _selectsql.Substring(_selectsql.IndexOf("FROM"), _selectsql.Length - _selectsql.IndexOf("FROM"));
                string _insertsql = "";
                foreach (int keywordid in _keywordreq.keywords)
                {
                    _insertsql += "INSERT jtKWRD (PID, KWRDID, _updating_uid) SELECT PID, " + keywordid.ToString() + "," + crmSession.UID() +
                        " FROM jtJOB WHERE JOBID = @JOBID AND PID NOT IN (SELECT PID FROM jtKWRD WHERE KWRDID = " + keywordid.ToString() + "); ";
                }
                string createsql = "EXEC dbo.wp_create_job {0}, {1}, '{2}', '{3}', '{4}', {5}, '{6}'";
                createjob_result _createjob_result = _entity_crm.getContext().Database.SqlQuery<createjob_result>(string.Format(createsql, 1, @crmSession.UID(), _insertsql,
                    _selectsql, @session.userSession.UserName, _keywordreq.donorct > queueCt ? 1 : 0, _keywordreq.descrip)).FirstOrDefault();
                if (_createjob_result.JOBID > 0)
                    if (_keywordreq.donorct <= queueCt)
                    {
                        string procsql = "EXEC @ROWCOUNT = dbo.wp_process_job {0}";
                        SqlParameter parm = new SqlParameter()
                        {
                            ParameterName = "@ROWCOUNT",
                            SqlDbType = SqlDbType.Int,
                            Direction = System.Data.ParameterDirection.Output
                        };
                        _entity_crm.getContext().Database.ExecuteSqlCommand(string.Format(procsql, _createjob_result.JOBID), parm);
                        int _rowcount = (int)parm.Value;
                        if (_rowcount > 0)
                            return new genericResponse() { success = true, message = _rowcount.ToString() + " Keyword records created successfully" };
                        else if (_rowcount == 0)
                            return new genericResponse() { success = true, message = "Keyword(s) already assigned to donors" };
                        else
                            return new genericResponse() { success = false, message = "Problem assigning Keywords" };
                    }
                    else
                    {
                        return new genericResponse() { success = true, message = "Keywords are queued successfully to be processed" };
                    }
                else
                    return new genericResponse() { success = false, message = "Problem assigning Keywords" };
            }
            catch (Exception ex)
            {
                return new genericResponse() { success = false, message = "Problem assigning Keywords" };
            }
        }

        [HttpPost, Route("crm/api/BatchAppend/appendNote")]
        [apiAuthorize(AccessElement = "cmdiapp.dms.people_qq.mass_apend", AccessLevel = "e")]
        public genericResponse appendNote(notereq _notereq)
        {
            try
            {
                string _selectsql = (string)HttpRuntime.Cache[_notereq.selectsql];
                _selectsql = _selectsql.Replace("'", "''");
                _selectsql = HttpUtility.HtmlDecode(_selectsql);
                //_selectsql = "SELECT DISTINCT P.PID " + _selectsql.Substring(_selectsql.IndexOf("FROM"), _selectsql.Length - _selectsql.IndexOf("FROM"));
                string _insertsql = String.Format("INSERT ACTHIST (PID, HISTTYPEID, HISTDATE, SUBJECT, NOTE, ASKMEMO, UID, _updating_uid) SELECT PID, 1, ''{0}'', ''{1}'', ''{2}'', ''{3}'', {4}, {5} " + 
                        "FROM jtJOB WHERE JOBID = @JOBID",
                        _notereq.histdate.ToShortDateString(), _notereq.subject, _notereq.note.Replace("'","''''"), _notereq.askMemo.Replace("'", "''''"), @crmSession.UID(), @crmSession.UID());
                string createsql = "EXEC dbo.wp_create_job {0}, {1}, '{2}', '{3}', '{4}', {5}, '{6}'";
                createjob_result _createjob_result = _entity_crm.getContext().Database.SqlQuery<createjob_result>(string.Format(createsql, 1, @crmSession.UID(), _insertsql,
                    _selectsql, @session.userSession.UserName, _notereq.donorct > queueCt ? 1 : 0, _notereq.descrip)).FirstOrDefault();
                if (_createjob_result.JOBID > 0)
                    if (_notereq.donorct <= queueCt)
                    {
                        string procsql = "EXEC @ROWCOUNT = dbo.wp_process_job {0}";
                        SqlParameter parm = new SqlParameter()
                        {
                            ParameterName = "@ROWCOUNT",
                            SqlDbType = SqlDbType.Int,
                            Direction = System.Data.ParameterDirection.Output
                        };
                        _entity_crm.getContext().Database.ExecuteSqlCommand(string.Format(procsql, _createjob_result.JOBID), parm);
                        int _rowcount = (int)parm.Value;
                        if (_rowcount > 0)
                            return new genericResponse() { success = true, message = _rowcount.ToString() + " Note records created successfully" };
                        else if (_rowcount == 0)
                            return new genericResponse() { success = true, message = "Note already assigned to donors" };
                        else
                            return new genericResponse() { success = false, message = "Problem assigning Note" };
                    }
                    else
                    {
                        return new genericResponse() { success = true, message = "Note is queued successfully to be processed" };
                    }
                else
                    return new genericResponse() { success = false, message = "Problem assigning Note" };
            }
            catch (Exception ex)
            {
                return new genericResponse() { success = false, message = "Problem assigning Note" };
            }
        }

        [HttpPost, Route("crm/api/BatchAppend/appendTask")]
        [apiAuthorize(AccessElement = "cmdiapp.dms.people_qq.mass_apend", AccessLevel = "e")]
        public genericResponse appendTask(taskreq _taskreq)
        {
            try
            {
                string _selectsql = (string)HttpRuntime.Cache[_taskreq.selectsql];
                _selectsql = _selectsql.Replace("'", "''");
                _selectsql = HttpUtility.HtmlDecode(_selectsql);
                //_selectsql = "SELECT DISTINCT P.PID " + _selectsql.Substring(_selectsql.IndexOf("FROM"), _selectsql.Length - _selectsql.IndexOf("FROM"));
                string _insertsql = String.Format("INSERT ACTIVITY (PID, SUBJECT, ACTTYPEID, PURPOSEID, PRIORITYID, ACTDATE, SCHEDBY, SCHEDFOR, SCHEDON, NOTE, ASKMEMO, _updating_uid, REMIND) " +
                        "SELECT PID, ''{0}'', {1}, {2}, {3}, ''{4}'', {5}, {6}, ''{7}'', ''{8}'', ''{9}'', {10}, 1 " +
                        "FROM jtJOB WHERE JOBID = @JOBID",
                        _taskreq.subject, _taskreq.acttypeid, _taskreq.purposeid, _taskreq.priorityid, _taskreq.actdate.ToShortDateString(), _taskreq.schedby, _taskreq.schedfor,
                        _taskreq.schedon.ToShortDateString(), _taskreq.note, _taskreq.askMemo, @crmSession.UID());
                string createsql = "EXEC dbo.wp_create_job {0}, {1}, '{2}', '{3}', '{4}', {5}, '{6}'";
                createjob_result _createjob_result = _entity_crm.getContext().Database.SqlQuery<createjob_result>(string.Format(createsql, 1, @crmSession.UID(), _insertsql,
                    _selectsql, @session.userSession.UserName, _taskreq.donorct > queueCt ? 1 : 0, _taskreq.descrip)).FirstOrDefault();
                if (_createjob_result.JOBID > 0)
                    if (_taskreq.donorct <= queueCt)
                    {
                        string procsql = "EXEC @ROWCOUNT = dbo.wp_process_job {0}";
                        SqlParameter parm = new SqlParameter()
                        {
                            ParameterName = "@ROWCOUNT",
                            SqlDbType = SqlDbType.Int,
                            Direction = System.Data.ParameterDirection.Output
                        };
                        _entity_crm.getContext().Database.ExecuteSqlCommand(string.Format(procsql, _createjob_result.JOBID), parm);
                        int _rowcount = (int)parm.Value;
                        if (_rowcount > 0)
                            return new genericResponse() { success = true, message = _rowcount.ToString() + " Task records created successfully" };
                        else if (_rowcount == 0)
                            return new genericResponse() { success = true, message = "Task already assigned to donors" };
                        else
                            return new genericResponse() { success = false, message = "Problem assigning Task" };
                    }
                    else
                    {
                        return new genericResponse() { success = true, message = "Task is queued successfully to be processed" };
                    }
                else
                    return new genericResponse() { success = false, message = "Problem assigning Task" };
            }
            catch (Exception ex)
            {
                return new genericResponse() { success = false, message = "Problem assigning Task" };
            }
        }

        [HttpPost, Route("crm/api/BatchAppend/appendEvent")]
        [apiAuthorize(AccessElement = "cmdiapp.dms.people_qq.mass_apend", AccessLevel = "e")]
        public genericResponse appendEvent(eventreq _eventreq)
        {
            try
            {
                string _selectsql = (string)HttpRuntime.Cache[_eventreq.selectsql];
                _selectsql = _selectsql.Replace("'", "''");
                _selectsql = HttpUtility.HtmlDecode(_selectsql);
                //_selectsql = "SELECT DISTINCT P.PID " + _selectsql.Substring(_selectsql.IndexOf("FROM"), _selectsql.Length - _selectsql.IndexOf("FROM"));
                string _insertsql = String.Format("INSERT jtSPCEVNT (PID, SPCEVNTID, ATTENDDTE, COMMENT, STATUS, TABLENO) " +
                        "SELECT PID, {0}, ''{1}'', ''{2}'', {3}, ''{4}'' " +
                        "FROM jtJOB WHERE JOBID = @JOBID AND PID NOT IN (SELECT PID FROM jtSPCEVNT WHERE SPCEVNTID = {5})",
                        _eventreq.spcevntid, _eventreq.attenddte.ToShortDateString(), _eventreq.comment, _eventreq.status, _eventreq.tableno,_eventreq.spcevntid);
                string createsql = "EXEC dbo.wp_create_job {0}, {1}, '{2}', '{3}', '{4}', {5}, '{6}'";
                createjob_result _createjob_result = _entity_crm.getContext().Database.SqlQuery<createjob_result>(string.Format(createsql, 1, @crmSession.UID(), _insertsql,
                    _selectsql, @session.userSession.UserName, _eventreq.donorct > queueCt ? 1 : 0, _eventreq.descrip)).FirstOrDefault();
                if (_createjob_result.JOBID > 0)
                    if (_eventreq.donorct <= queueCt)
                    {
                        string procsql = "EXEC @ROWCOUNT = dbo.wp_process_job {0}";
                        SqlParameter parm = new SqlParameter()
                        {
                            ParameterName = "@ROWCOUNT",
                            SqlDbType = SqlDbType.Int,
                            Direction = System.Data.ParameterDirection.Output
                        };
                        _entity_crm.getContext().Database.ExecuteSqlCommand(string.Format(procsql, _createjob_result.JOBID), parm);
                        int _rowcount = (int)parm.Value;
                        if (_rowcount > 0)
                            return new genericResponse() { success = true, message = _rowcount.ToString() + " Event records created successfully" };
                        else if (_rowcount == 0)
                            return new genericResponse() { success = true, message = "Event already assigned to donors" };
                        else
                            return new genericResponse() { success = false, message = "Problem assigning Event" };
                    }
                    else
                    {
                        return new genericResponse() { success = true, message = "Event is queued successfully to be processed" };
                    }
                else
                    return new genericResponse() { success = false, message = "Problem assigning Event" };
            }
            catch (Exception ex)
            {
                return new genericResponse() { success = false, message = "Problem assigning Event" };
            }
        }


        [HttpPost, Route("crm/api/BatchAppend/appendAttribute")]
        [apiAuthorize(AccessElement = "People/Profile/Attributes", AccessLevel = "a")]
        public genericResponse appendAttribute(attributereq _attributereq)
        {
            try
            {
                string _selectsql = (string)HttpRuntime.Cache[_attributereq.selectsql];
                _selectsql = _selectsql.Replace("'", "''");
                _selectsql = HttpUtility.HtmlDecode(_selectsql);
                string _insertsql = "";
                foreach (int attrId in _attributereq.attrIds)
                {
                    if (_attributereq.endDate == null)
                    {
                        _insertsql += string.Format(@" INSERT INTO AttributePeople (PID,attributeId,startDate,endDate) 
                SELECT PID,{0},''{1}'',null FROM jtJOB WHERE JOBID= @JOBID  
                AND PID NOT IN (SELECT PID FROM AttributePeople WHERE attributeId ={0} AND active=1) ", attrId, _attributereq.startDate);
                    }
                    else
                        _insertsql += string.Format(@" INSERT INTO AttributePeople (PID,attributeId,startDate,endDate) 
                SELECT PID,{0},''{1}'',''{2}'' FROM jtJOB WHERE JOBID= @JOBID  
                AND PID NOT IN (SELECT PID FROM AttributePeople WHERE attributeId ={0} AND active=1) ", attrId, _attributereq.startDate, _attributereq.endDate);
                }
                string createsql = "EXEC dbo.wp_create_job {0}, {1}, '{2}', '{3}', '{4}', {5}, '{6}'";
                createjob_result _createjob_result = _entity_crm.getContext().Database.SqlQuery<createjob_result>(string.Format(createsql, 1, @crmSession.UID(), _insertsql,
                    _selectsql, @session.userSession.UserName, _attributereq.donorct > queueCt ? 1 : 0, _attributereq.descrip)).FirstOrDefault();
                if (_createjob_result.JOBID > 0)
                    if (_attributereq.donorct <= queueCt)
                    {
                        string procsql = "EXEC @ROWCOUNT = dbo.wp_process_job {0}";
                        SqlParameter parm = new SqlParameter()
                        {
                            ParameterName = "@ROWCOUNT",
                            SqlDbType = SqlDbType.Int,
                            Direction = System.Data.ParameterDirection.Output
                        };
                        _entity_crm.getContext().Database.ExecuteSqlCommand(string.Format(procsql, _createjob_result.JOBID), parm);
                        int _rowcount = (int)parm.Value;
                        if (_rowcount > 0)
                            return new genericResponse() { success = true, message = _rowcount.ToString() + " Attribute records created successfully" };
                        else if (_rowcount == 0)
                            return new genericResponse() { success = true, message = "Attribute already assigned to donors" };
                        else
                            return new genericResponse() { success = false, message = "Problem assigning Attribute" };
                    }
                    else
                    {
                        return new genericResponse() { success = true, message = "Attribute is queued successfully to be processed" };
                    }
                else
                    return new genericResponse() { success = false, message = "Problem assigning Attribute" };
            }
            catch (Exception ex)
            {
                return new genericResponse() { success = false, message = "Problem assigning Attribute" };
            }
        }

        [HttpGet, Route("crm/api/BatchAppend/MEventCodes")]
        //[Queryable(PageSize = 10)]
        public IQueryable<v_eventcode> MEventCodes()
        {
            return _entity_crm.All<v_eventcode>().AsQueryable();
        }

        #region[Money Search - Mass Append]
        [HttpPost, Route("crm/api/BatchAppend/appendException")]
        [apiAuthorize(AccessElement = "cmdiapp.dms.people_qq.mass_apend", AccessLevel = "e")]
        public genericResponse appendException(exceptionreq _exceptionreq)
        {
            try
            {
                string _selectsql = (string)HttpRuntime.Cache[_exceptionreq.selectsql];
                _selectsql = _selectsql.Replace("'", "''");
                _selectsql = HttpUtility.HtmlDecode(_selectsql);

                //to use wp_create_job change select query 
                //1. remove PID - replace PID with ""
                //2. make MID as PID
                //_selectsql = _selectsql.Replace("M.PID,", "").Replace("M.MID,", "M.MID AS PID,");
                _selectsql = _selectsql.Replace("DISTINCT [M].MID ", "DISTINCT [M].MID AS PID ");

                string _updatesql = "";
                _updatesql = String.Format("UPDATE MONY ") +
                        "SET EXCEPID = " + _exceptionreq.excepid + ",EXCEPDTE = ''" + _exceptionreq.excepdte.ToShortDateString() + "''  " +
                        "WHERE MID in (SELECT PID FROM jtJOB WHERE JOBID = @JOBID)";

                string createsql = "EXEC dbo.wp_create_job {0}, {1}, '{2}', '{3}', '{4}', {5}, '{6}'";
                createjob_result _createjob_result = _entity_crm.getContext().Database.SqlQuery<createjob_result>(string.Format(createsql, 12, @crmSession.UID(), _updatesql,
                    _selectsql, @session.userSession.UserName, _exceptionreq.giftct > queueCt ? 1 : 0, _exceptionreq.descrip)).FirstOrDefault();
                if (_createjob_result.JOBID > 0)
                    if (_exceptionreq.giftct <= queueCt)
                    {
                        string procsql = "EXEC @ROWCOUNT = dbo.wp_process_job {0}";
                        SqlParameter parm = new SqlParameter()
                        {
                            ParameterName = "@ROWCOUNT",
                            SqlDbType = SqlDbType.Int,
                            Direction = System.Data.ParameterDirection.Output
                        };
                        _entity_crm.getContext().Database.ExecuteSqlCommand(string.Format(procsql, _createjob_result.JOBID), parm);
                        int _rowcount = (int)parm.Value;
                        if (_rowcount > 0)
                            return new genericResponse() { success = true, message = _rowcount.ToString() + " Exception Details records created successfully" };
                        else if (_rowcount == 0)
                            return new genericResponse() { success = true, message = "Exception Details already assigned to gift" };
                        else
                            return new genericResponse() { success = false, message = "Problem assigning Exception Details" };
                    }
                    else
                    {
                        return new genericResponse() { success = true, message = "Exception Details is queued successfully to be processed" };
                    }
                else
                    return new genericResponse() { success = false, message = "Problem assigning Exception Details" };
            }
            catch (Exception ex)
            {
                return new genericResponse() { success = false, message = "Problem assigning Exception Details" };
            }
        }        

        [HttpGet, Route("crm/api/BatchAppend/ExcepCode")]        
        public IQueryable<lkEXCEP> ExcepCode()
        {
            return _entity_crm.All<lkEXCEP>().AsQueryable();
        }

        [HttpPost, Route("crm/api/BatchAppend/appendTrackno")]
        [apiAuthorize(AccessElement = "cmdiapp.dms.people_qq.mass_apend", AccessLevel = "e")]
        public genericResponse appendTrackno(tracknoreq _tracknoreq)
        {
            try
            {
                string _selectsql = (string)HttpRuntime.Cache[_tracknoreq.selectsql];
                _selectsql = _selectsql.Replace("'", "''");
                _selectsql = HttpUtility.HtmlDecode(_selectsql);

                //to use wp_create_job change select query 
                //1. remove PID - replace PID with ""
                //2. make MID as PID
                //_selectsql = _selectsql.Replace("M.PID,", "").Replace("M.MID,", "M.MID AS PID,");
                _selectsql = _selectsql.Replace("DISTINCT [M].MID ", "DISTINCT [M].MID AS PID ");

                string _updatesql = "";
                _updatesql = String.Format("UPDATE MONY ") +
                        "SET TRACKNO = " + _tracknoreq.trackno.ToString() + " " +
                        "WHERE MID in (SELECT PID FROM jtJOB WHERE JOBID = @JOBID)";

                string createsql = "EXEC dbo.wp_create_job {0}, {1}, '{2}', '{3}', '{4}', {5}, '{6}'";
                createjob_result _createjob_result = _entity_crm.getContext().Database.SqlQuery<createjob_result>(string.Format(createsql, 13, @crmSession.UID(), _updatesql,
                    _selectsql, @session.userSession.UserName, _tracknoreq.giftct > queueCt ? 1 : 0, _tracknoreq.descrip)).FirstOrDefault();
                if (_createjob_result.JOBID > 0)
                    if (_tracknoreq.giftct <= queueCt)
                    {
                        string procsql = "EXEC @ROWCOUNT = dbo.wp_process_job {0}";
                        SqlParameter parm = new SqlParameter()
                        {
                            ParameterName = "@ROWCOUNT",
                            SqlDbType = SqlDbType.Int,
                            Direction = System.Data.ParameterDirection.Output
                        };
                        _entity_crm.getContext().Database.ExecuteSqlCommand(string.Format(procsql, _createjob_result.JOBID), parm);
                        int _rowcount = (int)parm.Value;
                        if (_rowcount > 0)
                            return new genericResponse() { success = true, message = _rowcount.ToString() + " Mass Append Tracking# processed successfully" };
                        else if (_rowcount == 0)
                            return new genericResponse() { success = true, message = "Tracking# already assigned to gift" };
                        else
                            return new genericResponse() { success = false, message = "Problem Mass Append Tracking#" };
                    }
                    else
                    {
                        return new genericResponse() { success = true, message = "Mass Append Tracking# job is queued successfully to be processed" };
                    }
                else
                    return new genericResponse() { success = false, message = "Problem creating Mass Append Tracking# job" };
            }
            catch (Exception ex)
            {
                return new genericResponse() { success = false, message = "Problem with Mass Append Tracking#" };
            }
        }

        [HttpPost, Route("crm/api/BatchAppend/appendChannel")]
        [apiAuthorize(AccessElement = "cmdiapp.dms.people_qq.mass_apend", AccessLevel = "e")]
        public genericResponse appendChannel(channelreq _channelreq)
        {
            try
            {
                string _selectsql = (string)HttpRuntime.Cache[_channelreq.selectsql];
                _selectsql = _selectsql.Replace("'", "''");
                _selectsql = HttpUtility.HtmlDecode(_selectsql);

                //to use wp_create_job change select query 
                //1. remove PID - replace PID with ""
                //2. make MID as PID
                //_selectsql = _selectsql.Replace("M.PID,", "").Replace("M.MID,", "M.MID AS PID,");
                _selectsql = _selectsql.Replace("DISTINCT [M].MID ", "DISTINCT [M].MID AS PID ");

                string _updatesql = "";
                _updatesql = String.Format("UPDATE MONY ") +
                        "SET channelId = " + _channelreq.channelId +
                        "WHERE MID in (SELECT PID FROM jtJOB WHERE JOBID = @JOBID)";

                string createsql = "EXEC dbo.wp_create_job {0}, {1}, '{2}', '{3}', '{4}', {5}, '{6}'";
                createjob_result _createjob_result = _entity_crm.getContext().Database.SqlQuery<createjob_result>(string.Format(createsql, 12, @crmSession.UID(), _updatesql,
                    _selectsql, @session.userSession.UserName, _channelreq.giftct > queueCt ? 1 : 0, _channelreq.descrip)).FirstOrDefault();
                if (_createjob_result.JOBID > 0)
                    if (_channelreq.giftct <= queueCt)
                    {
                        string procsql = "EXEC @ROWCOUNT = dbo.wp_process_job {0}";
                        SqlParameter parm = new SqlParameter()
                        {
                            ParameterName = "@ROWCOUNT",
                            SqlDbType = SqlDbType.Int,
                            Direction = System.Data.ParameterDirection.Output
                        };
                        _entity_crm.getContext().Database.ExecuteSqlCommand(string.Format(procsql, _createjob_result.JOBID), parm);
                        int _rowcount = (int)parm.Value;
                        if (_rowcount > 0)
                            return new genericResponse() { success = true, message = _rowcount.ToString() + " records processed. " };
                        else if (_rowcount == 0)
                            return new genericResponse() { success = true, message = "Channel already assigned to gift" };
                        else
                            return new genericResponse() { success = false, message = "Problem assigning Channel." };
                    }
                    else
                    {
                        return new genericResponse() { success = true, message = "Successfully Queued." };
                    }
                else
                    return new genericResponse() { success = false, message = "Problem assigning Channel." };
            }
            catch (Exception ex)
            {
                return new genericResponse() { success = false, message = "Problem assigning Channel." };
            }
        }
        #endregion
    }
}
