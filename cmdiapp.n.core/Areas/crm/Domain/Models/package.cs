﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Data;
using System.Linq;
using System.Web.Mvc;
using System.Data.Linq;
using System.ComponentModel.DataAnnotations.Schema;
using cmdiapp.n.core.Areas.crm.Domain.Data;
using cmdiapp.n.core.Domain.ViewModels;

namespace cmdiapp.n.core.Areas.crm.Domain.Models
{
    [Table("PACKAGE")]
    public class PACKAGE : _entityBase_crm, iItemType
    {
        [Column]
        [Key]
        public int PKGEID { get; set; }

        [Column]
        public Int16? PROGID { get; set; }
        [ForeignKey("PROGID")]
        public virtual PROGRAM PROGRAM { get; set; }

        [Column]
        public int? dtPROGID { get; set; }
               
        [Column]
        [Required(ErrorMessage = "The Package Code is required.")]
        public string PKGECODE { get; set; }

        [Column]
        [Required(ErrorMessage = "The Package Description is required.")]
        public string PKGEDESC { get; set; }

        [Column]
        public byte? SENDACKW { get; set; }

        [Column]
        public byte? SENDRECV { get; set; }

        [Column]
        public string COMMENT { get; set; }

        [Column] 
        public DateTime? MAILDTE { get; set; }

        [Column] 
        public DateTime? LASTCAGE { get; set; }
        
        [Column] 
        public DateTime? UPDATEDON { get; set; }
        
        [Column]
        public Int16? PROGSETID { get; set; }
                        
        [Column]
        public Int16? channelId { get; set; }
        
        [Column]
        public Int16? initiativeTypeId { get; set; }

        [Column]
        public string docFileName { get; set; }

        [Column]
        public byte[] docFileBinaries { get; set; }

        [Column] 
        public DateTime? docFileDate { get; set; }

        [Column]
        public string docFileBy { get; set; }
                      
    }


    public class packageImage 
    {
        public int PKGEID { get; set; }
        public byte[] docFileBinaries { get; set; }
        public string docFileName { get; set; }
    }

    [Table("v_Package")]
    public class v_Package : _entityBase_crm
    {
        [Column]
        [Key]
        public int PKGEID { get; set; }

        [Column]
        public Int16 PROGID { get; set; }

        [Column]
        public int? dtPROGID { get; set; }

        [Column]
        public string PKGEDESC { get; set; }

        [Column]
        public DateTime? UPDATEDON { get; set; }

    }

    public class PACKAGE_ext : packageR
    {
        //public int count_ { get; set; }
        //public Int64 rowNo { get; set; }
        //public int CNT { get; set; }
        //public int PKGEID { get; set; }
        //public Int16 PROGID { get; set; }
        //public string DESCRIP { get; set; }
        //public string PKGECODE { get; set; }
        //public string PKGEDESC { get; set; }
        //public int MAILQTY { get; set; }
        //public DateTime? MAILDTE { get; set; }
        //public DateTime? FIRSTCAGE { get; set; }
        //public DateTime? LASTCAGE { get; set; }
        //public int NOGIFT { get; set; }
        //public Decimal GROSS { get; set; }
        //public Decimal COST { get; set; }
        //public Decimal NET { get; set; }
        //public byte SENDACKW { get; set; }
        //public byte SENDRECV { get; set; }
        //public string COMMENT { get; set; }
        //public DateTime? UPDATEDON { get; set; }
        //public Int16 INITIATIVETYPEID { get; set; }
        //public DateTime? LASTACTI { get; set; }
        //public int SACTIVITY { get; set; }
        //public int SOURCECOUNT { get; set; }
        //public int TELECOUNT { get; set; }
        
    }

    public class packageR : _entityBase_crm, iItemType
    {
        public int count_ { get; set; }
        public Int64 rowNo { get; set; }
        public int CNT { get; set; }
        public int PKGEID { get; set; }
        public Int16 PROGID { get; set; }
        public string DESCRIP { get; set; }
        public string PKGECODE { get; set; }
        public string PKGEDESC { get; set; }
        public int MAILQTY { get; set; }
        public DateTime? MAILDTE { get; set; }
        public DateTime? FIRSTCAGE { get; set; }
        public DateTime? LASTCAGE { get; set; }
        public int NOGIFT { get; set; }
        public Decimal GROSS { get; set; }
        public Decimal COST { get; set; }
        public Decimal NET { get; set; }
        public byte SENDACKW { get; set; }
        public byte SENDRECV { get; set; }
        public string COMMENT { get; set; }
        public DateTime? UPDATEDON { get; set; }
        public Int16 INITIATIVETYPEID { get; set; }
        public DateTime? LASTACTI { get; set; }
        public int SACTIVITY { get; set; }
        public int SOURCECOUNT { get; set; }
        public int TELECOUNT { get; set; }

    }

    [Table("lkInitiativeType")]
    public class lkInitiativeType
    {
        [Column]
        [Key]
        public Int16 initiativeTypeId { get; set; }

        [Column]
        public string initiativeType { get; set; }

        [Column]
        public string descrip { get; set; }

    }


    public class packageRR : _entityBase_crm, iItemType
    {
        public Int16? PROGID { get; set; }
        public string PROGTYPE { get; set; }
        public string PROGRAM { get; set; }
        public int PKGEID { get; set; }
        public string PKGECODE { get; set; }
        public string PKGEDESC { get; set; }
        public int? MAILQTY { get; set; }
        public DateTime? MAILDTE { get; set; }
        public int? NOGIFTS { get; set; }
        public Decimal? GROSS { get; set; }
        public Decimal? COST { get; set; }
        public DateTime? FIRSTCAGE { get; set; }
        public DateTime? LASTCAGE { get; set; }
        public Decimal? NET { get; set; }
        public int? SOURCECOUNT { get; set; }
        public int? TELECOUNT { get; set; }

    }

    public class packageBack : _entityBase_crm, iItemType
    {
        public string PKGEDESC { get; set; }
        public string PKGEDESCRIP { get; set; }
        public int? PKGEID { get; set; }
        public DateTime? MAILDTEc { get; set; }
        public DateTime? MAILDTE { get; set; }

    }

    public class packageBack_ext : packageBack
    {
        public int count_ { get; set; }
        public Int64 rowNo { get; set; }
    }

    public class PACKAGERR_ext : packageRR
    {
        public int count_ { get; set; }
        public Int64 rowNo { get; set; }
        

    }


}