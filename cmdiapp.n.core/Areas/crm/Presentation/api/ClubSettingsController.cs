﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Net;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Web.Http;
using System.Web.Script.Serialization;
using System.Drawing;
using System.IO;
using System.Data.SqlClient;
using System.Collections.ObjectModel;
using System.Xml.Linq;

using Ninject;
using Ninject.Web.Mvc;

using AutoMapper;
using cmdiapp.n.core.Core;
using cmdiapp.n.core.Core.App_Start;
using cmdiapp.n.core.Domain.ViewModels;
using cmdiapp.n.core.Areas.crm.Core;
using cmdiapp.n.core.Areas.crm.Domain.Models;
using cmdiapp.n.core.Areas.crm.Domain.ViewModels;
using cmdiapp.n.core.Areas.crm.Domain.Data;
using cmdiapp.n.core.Areas.crm.Domain.Repositories;
using cmdiapp.n.core.Areas.crm.Domain.Services;
using cmdiapp.n.core.Areas.dataService.Domain.Data;
using cmdiapp.n.core.Areas.dataService.Domain.Models;
using cmdiapp.n.core.Areas.dataService.Domain.Services;
using cmdiapp.n.core.Presentation.Controllers;
using cmdiapp.n.core.Areas.query.Domain.Models;
using cmdiapp.n.core.Domain.Models;
using System.Data;
using cmdiapp.n.core.Library;

namespace cmdiapp.n.core.Areas.crm.Presentation.Controllers.api
{
    [Authorize]
    [apiAuthorize(AccessElement = "cmdiapp.dms.entities", AccessLevel = "v")]
    public class ClubSettingsController : ApiController
    {
        #region [[ Declaration ]]

        private I_entity_crm _entity_crm;
        private readonly IclubService _clubService;
        private readonly IclubStatusService _clubStatusService;
        private readonly IReportDataService _reportService;
        private userSession _userSession;
        #endregion

        #region [[ (constructor) SettingsController ]]
        public ClubSettingsController()
        {
            _entity_crm = I_entityManager_crm.getEntity();
            _clubService = NinjectMVC.kernel.Get<IclubService>();
            _reportService = NinjectMVC.kernel.Get<IReportDataService>();
            _userSession = session.userSession;
        }
        #endregion

        #region [[ SQL Query]]
        string clubLabel = crmSession.is_nonProfit_version() ? "Membership" : "Club";
        const string _sql_exportClub = @"SELECT CLUBCODE as CODE FROM pmCLUB";

        const string _sql_exportClubs = @"SELECT CLUBCODE FROM pmCLUB Order By ClubId";

        #endregion

        #region[PEOPLE->SETTINGS->CLUB by Tanvir]
        [HttpPost, Route("crm/api/ClubSettings/ExportClubKey")]
        [apiAuthorize(AccessElement = "cmdiapp.dms.entities", AccessLevel = "v")]
        public string ExportClubKey(string searchText)
        {
            if (string.IsNullOrEmpty(searchText)) searchText = "";

            searchParam param = new searchParam();
            param.searchText = searchText;

            string key_ = System.Guid.NewGuid().ToString();
            HttpRuntime.Cache.Insert(key_, param, null, DateTime.UtcNow.Add(new TimeSpan(1, 0, 1)), System.Web.Caching.Cache.NoSlidingExpiration);

            return key_;

        }

        [HttpGet, Route("crm/api/ClubSettings/ExportClub")]
        [apiAuthorize(AccessElement = "cmdiapp.dms.entities", AccessLevel = "v")]
        public string ExportClub(string key)
        {
            searchParam _param = (searchParam)HttpRuntime.Cache[key];
            string sToday = String.Format("{0:yyyyMMMdd__hh_mm_ss tt}", DateTime.Now);
            string moduleName = clubLabel;
            string fileName = moduleName + "_" + System.Guid.NewGuid() + "_" + _userSession.UserName + "_" + sToday;

            //if (_param == null)
            //    return false;

            DataSet ds = null;

            if (string.IsNullOrEmpty(_param.searchText))
            {
                ds = _reportService.get_dataset_w_sql__single(_sql_exportClub, clubLabel);
            }
            else
            {
                _param.searchText = Library.util.kill_sqlBlacklistWord(_param.searchText);

                string _where = "";

                if (!string.IsNullOrEmpty(_param.searchText))
                {
                    #region [ Split Search Text > string1, string2 & string3 and create "Where" statement ]
                    char[] delimiterChars = { ' ', ',', '.', ';', ':', '|', '\t' };

                    string[] words = _param.searchText.Split(delimiterChars);

                    if (words.Count() > 0)
                    {
                        string string1 = words[0];

                        #region [ Where Clause ]
                        switch (words.Count())
                        {
                            case 1: // CLUBCODE
                                _where = string.Format("CLUBCODE LIKE '%{0}%'", string1);
                                break;

                            default:
                                break;
                        }
                        #endregion
                    }
                    #endregion
                }
                //We need SQL with Where condition
                string sql = _sql_exportClub + " Where {0}" + "Order By ClubId"; 
                //Compose the SQL
                sql = String.Format(sql, _where);

                ds = _reportService.get_dataset_w_sql__single(sql, clubLabel);

            }


            if (ds != null)
            {
                //HttpResponse _resp = CreateExcelFile.CreateExcelDocument(_dt.Copy(), fileName, HttpContext.Current.Response);
                string key_ = System.Guid.NewGuid().ToString();
                HttpRuntime.Cache.Insert(key_, ds, null, DateTime.UtcNow.Add(new TimeSpan(1, 0, 1)), System.Web.Caching.Cache.NoSlidingExpiration);
                //return the Key
                return key_;
            }
            else
            {
                return null;
            }
        }

        [HttpPost, Route("crm/api/ClubSettings/DestroyClub")]
        [apiAuthorize(AccessElement = "cmdiapp.dms.entities", AccessLevel = "d")]
        public genericResponse DestroyClub(int clubid)
        {

            try
            {
                var viewModel = _clubService.get_club(clubid);
                if (!_clubService.check_record_exists(viewModel.CLUBID))
                {
                    _clubService.Delete(viewModel);
                    return new genericResponse{ success = true, message = clubLabel+" deleted successfully." };
                }
                else
                {
                    //Can not delete as this is being used
                    return new genericResponse{ success = false, message = "The "+ clubLabel + " is being referenced so it can not be deleted." };
                }

            }
            catch
            {
                //To do: Log the Exception here
                return new genericResponse{ success = false, message = "There is a problem processing your request. Please try again later." };
            }

        }

        [HttpPost, Route("crm/api/ClubSettings/ManageClub")]
        [apiAuthorize(AccessElement = "cmdiapp.dms.entities", AccessLevel = "a")]
        public genericResponse ManageClub(pmCLUB model)
        {
            if (ModelState.IsValid)
            {
                try
                {
                    //check for Uniqueness of the Contact Flag
                    if (!_clubService.check_unqiueness(model))
                    {
                        //return 
                        return new genericResponse { success = false, message = clubLabel+" code already exists. Please enter unique code and try again." };
                    }
                    else
                    {
                        if (model.CLUBID > 0)
                        {
                            _clubService.Update(model);
                            return new genericResponse { success = true, message = clubLabel+" " + model.CLUBCODE + " updated successfully." };
                        }
                        else
                        {
                            _clubService.Add(model);
                            return new genericResponse{ success = true, message = clubLabel+" " + model.CLUBCODE + " added successfully." };
                        }

                    }

                }
                catch
                {
                    //To Do: Log the Exception here
                    return new genericResponse{ success = false, message = "There is a problem processing your request. Please try again later." };
                }
            }
            else
            {
                //Model is not valid - We can not go ahead
                return new genericResponse{ success = false, message = "Missing Required Field(s). Please try again." };
            }

        }

        [HttpGet, Route("crm/api/ClubSettings/GetClub")]
        [apiAuthorize(AccessElement = "cmdiapp.dms.entities", AccessLevel = "v")]
        public genericResponse GetClub(string searchText, int? page, int? pageSize)
        {
            #region [ Retrieve Input ]
            string _searchText = Library.util.kill_sqlBlacklistWord((searchText ?? "").Trim().Replace("'", "''''"));

            int _pageSize = (pageSize == null || pageSize.Value == 0 ? 10 : pageSize.Value);
            int _pageNo = (page == null || page.Value == 0 ? 1 : page.Value);

            string sortField = HttpContext.Current.Request.QueryString["sort[0][field]"];
            string sortDir = HttpContext.Current.Request.QueryString["sort[0][dir]"];
            string _sortOptions = "";
            if (!string.IsNullOrEmpty(sortField))
                _sortOptions = sortField;
            if (!string.IsNullOrEmpty(sortField) && !string.IsNullOrEmpty(sortDir))
                _sortOptions = _sortOptions + " " + sortDir;
            #endregion

            List<pmCLUB_ext> _list = new List<pmCLUB_ext>();
            genericResponse _response;

            if (!string.IsNullOrEmpty(_searchText))
            {
                _searchText = Library.util.kill_sqlBlacklistWord(_searchText);

                int clubid;

                if (string.IsNullOrEmpty(_sortOptions)) _sortOptions = "CLUBID";

                if (int.TryParse(_searchText, out clubid))
                    _list = _clubService.get_all_clubs(string.Format("CLUBID={0}", clubid), _sortOptions, _pageSize, _pageNo);
                else
                {
                    string _where = "";

                    if (!string.IsNullOrEmpty(_searchText))
                    {
                        #region [ Split Search Text > string1, string2 & string3 and create "Where" statement ]
                        char[] delimiterChars = { ' ', ',', '.', ';', ':', '|', '\t' };

                        string[] words = _searchText.Split(delimiterChars);

                        if (words.Count() > 0)
                        {
                            string string1 = words[0];

                            #region [ Where Clause ]
                            switch (words.Count())
                            {
                                case 1: // CLUBCODE
                                    _where = string.Format("CLUBCODE LIKE ''%{0}%''", string1);
                                    break;

                                default:
                                    break;
                            }
                            #endregion

                            if (string.IsNullOrEmpty(_sortOptions)) _sortOptions = "CLUBID ASC";
                            _list = _clubService.get_all_clubs(_where, _sortOptions, _pageSize, _pageNo);
                        }
                        #endregion
                    }
                }

            }
            else
            {
                //Coming here when SearchText is null
                if (string.IsNullOrEmpty(_sortOptions)) _sortOptions = "CLUBID ASC";
                _list = _clubService.get_all_clubs("", _sortOptions, _pageSize, _pageNo);
            }

            Mapper.CreateMap<pmCLUB_ext, rpmClub>();
            IEnumerable<iItemType> results = _list.Select(a => Mapper.Map<pmCLUB_ext, rpmClub>(a)).ToList();
            if (results.Count() > 0)
            {
                _response = new genericResponse() { success = true, __count = _list.FirstOrDefault().count_, results = results.ToList() };
            }
            else
            {
                _response = new genericResponse() { success = true, __count = 0 };
            }

            return _response;//Json(_response, JsonRequestBehavior.AllowGet);
        }
        #endregion

    }
}
