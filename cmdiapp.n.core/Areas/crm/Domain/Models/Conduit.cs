﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Data;
using System.Linq;
using System.Web.Mvc;
using System.Data.Linq;
using System.ComponentModel.DataAnnotations.Schema;

using cmdiapp.n.core.Domain.ViewModels;
using cmdiapp.n.core.Areas.crm.Domain.Data;

namespace cmdiapp.n.core.Areas.crm.Domain.Models
{
    [Table("CONDUIT")]
    public class CONDUIT : _entityBase_crm, iItemType
    {
        [Column]
        [Key]
        public int CONDUITID { get; set; }

        [Column]
        public int PID { get; set; }

        [Column]
        public int CONDUITNO { get; set; }

        [Column]
        public string COMMITTEE { get; set; }

        [Column]
        public string FECCMTEID { get; set; }

        [Column]
        public string ELECTCD { get; set; }

        [Column]
        public string ELECTYR { get; set; }

        [Column]
        public string ELECTOTH { get; set; }

        [Column]
        public string OFFICE { get; set; }

        [Column]
        public string STATE { get; set; }

        [Column]
        public string DISTRICT { get; set; }

        [Column]
        public string COMMENT { get; set; }

        [Column]
        public bool ACTIVE { get; set; }

        [Column("_updating_uid")]
        public Int16? updating_uid { get; set; }

        [Column]
        public string CANDPREFIX { get; set; }

        [Column]
        public string CANDFNAME { get; set; }

        [Column]
        public string CANDMNAME { get; set; }

        [Column]
        public string CANDLNAME { get; set; }

        [Column]
        public string CANDSUFFIX { get; set; }

        [Column]
        public string CANDFECID { get; set; }
    }

    [Table("MONYCONDUIT")]
    public class MONYCONDUIT : _entityBase_crm, iItemType
    {
        [Column]
        [Key]
        public int MONYCONDUITID { get; set; }

        [Column]
        public int MID { get; set; }

        [Column]
        public decimal? AMT { get; set; }

        [Column]
        public int CONDUITNO { get; set; }

        [Column]
        public bool DISTRIB { get; set; }

        [Column]
        public decimal? FEE { get; set; }

        [Column]
        public decimal? NET { get; set; }

        [Column]
        public bool MEMO { get; set; }
    }

    [Table("CONDUITDIST")]
    public class CONDUITDIST : _entityBase_crm, iItemType
    {
        [Column]
        [Key]
        public int DISTRIBID { get; set; }

        [Column]
        public DateTime DISTRIBDTE { get; set; }
        
        [Column]
        public int CONDUITNO { get; set; }

        [Column]
        public string COMMITTEE { get; set; }

        [Column]
        public string FECCMTEID { get; set; }

        [Column]
        public string ELECTCD { get; set; }

        [Column]
        public string ELECTYR { get; set; }

        [Column]
        public string ELECTOTH { get; set; }

        [Column]
        public string OFFICE { get; set; }

        [Column]
        public string STATE { get; set; }

        [Column]
        public string DISTRICT { get; set; }

        [Column]
        public Int16 UID { get; set; }

        [Column]
        public string COMMENT { get; set; }

        [Column]
        public bool COMPLETE { get; set; }

        [Column]
        public DateTime UPDATEDON { get; set; }

        [Column]
        public string CANDPREFIX { get; set; }

        [Column]
        public string CANDFNAME { get; set; }

        [Column]
        public string CANDMNAME { get; set; }

        [Column]
        public string CANDLNAME { get; set; }

        [Column]
        public string CANDSUFFIX { get; set; }

        [Column]
        public string CANDFECID { get; set; }
    }

    [Table("dtCONDUITDIST")]
    public class dtCONDUITDIST : _entityBase_crm, iItemType
    {
        [Column]
        [Key]
        public int dtDISTRIBID { get; set; }

        [Column]
        public int DISTRIBID { get; set; }

        [Column]
        public int MONYCONDUITID { get; set; }

        [Column]
        public decimal? DISTRIBAMT { get; set; }

        [Column]
        public decimal? DISTRIBFEE { get; set; }

        [Column]
        public decimal? DISTRIBNET { get; set; }
    }

    [Table("v_conduitno")]
    public class ConduitnoR : _entityBase_crm, iItemType
    {
        [Column]
        [Key]
        public int PID { get; set; }

        [Column]
        public int CONDUITNO { get; set; }

        [Column]
        public string CONDUITNAME { get; set; }
    }

    [Table("v_conduit_distribution")]
    public class conduitDistribR : _entityBase_crm, iItemType
    {
        [Column]
        [Key]
        public int DISTRIBID { get; set; }

        [Column]
        public int CONDUITNO { get; set; }

        [Column]
        public DateTime DISTRIBDTE { get; set; }

        [Column]
        public string CONDUITNAME { get; set; }

        [Column]
        public string ELECTCD { get; set; }

        [Column]
        public string ELECTYR { get; set; }

        [Column]
        public string ELECTOTH { get; set; }

        [Column]
        public string OFFICE { get; set; }

        [Column]
        public string STATE { get; set; }

        [Column]
        public string DISTRICT { get; set; }

        [Column]
        public string STATUS { get; set; }

        [Column]
        public int TOTALNO { get; set; }

        [Column]
        public decimal? TOTALAMT { get; set; }
    }

    [NotMapped]
    public class conduitDistribR_ext : conduitDistribR
    {
        public int count_ { get; set; }

        public Int64 rowNo { get; set; }
        public int Id { get; set; }
    }

    public class newConduitDistrib : _entityBase_crm, iItemType 
    {
        [Column]
        public int CONDUITID { get; set; }

        [Column]
        public int MID { get; set; }

        [Column]
        public int PID { get; set; }

        [Column]
        public string TYPE { get; set; }

        [Column]
        public string FNAME { get; set; }

        [Column]
        public string MNAME { get; set; }

        [Column]
        public string LNAME { get; set; }

        [Column]
        public string FUNDCODE { get; set; }

        [Column]
        public DateTime BATCHDTE { get; set; }

        [Column]
        public string CONDUIT { get; set; }

        [Column]
        public decimal? CONDUITAMT { get; set; }

        [Column]
        public decimal? CONDUITFEE { get; set; }

        [Column]
        public decimal? CONDUITNET { get; set; }

        [Column]
        public string STREET { get; set; }

        [Column]
        public string CITY { get; set; }

        [Column]
        public string STATE { get; set; }

        [Column]
        public string ZIP { get; set; }
    }

    [NotMapped]
    public class NewConduitDistrib_ext : newConduitDistrib
    {
        public int count_ { get; set; }

        public Int64 rowNo { get; set; }
    }

    public class DistributionDetail : _entityBase_crm, iItemType 
    {
        public int DISTRIBID { get; set; }

        public int MID { get; set; }

        public int PID { get; set; }

        public string TYPE { get; set; }

        public string PREFIX { get; set; }

        public string FNAME { get; set; }

        public string MNAME { get; set; }

        public string LNAME { get; set; }

        public string SUFFIX { get; set; }

        public string EMPLOYER { get; set; }

        public string OCCUPATION { get; set; }

        public string STREET { get; set; }

        public string ADDR1 { get; set; }

        public string ADDR2 { get; set; }

        public string CITY { get; set; }

        public string STATE { get; set; }

        public string ZIP { get; set; }

        public string EMAIL { get; set; }

        public string HMPHN { get; set; }

        public DateTime BATCHDTE { get; set; }

        public int CONDUITNO { get; set; }

        public decimal? AMT { get; set; }

        public decimal? FEE { get; set; }

        public decimal? NET { get; set; }

        public bool MEMO { get; set; }

        public int MONYCONDUITID { get; set; }
    }

    [NotMapped]
    public class DistributionDetail_ext : DistributionDetail
    {
        public int count_ { get; set; }

        public Int64 rowNo { get; set; }
    }
    [NotMapped]
    public class ConduitTopNData : iItemType
    {
        public string COMMITTEE { get; set; }
        public string FECCMTEID { get; set; }
        public decimal Amount { get; set; }
        public int count_ { get; set; }
        public Int64 rowNo { get; set; }
    }
    [NotMapped]
    public class ConduitGiftNData : iItemType
    {
        public string COMMITTEE { get; set; }
        public decimal AMT { get; set; }
        public int MID { get; set; }
        public DateTime? BATCHDTE { get; set; }
        public string FNAME { get; set; }
        public string MNAME { get; set; }
        public string LNAME { get; set; }
        public string STREET { get; set; }
        public string ADDR1 { get; set; }
        public string STATE { get; set; }
        public string ZIP { get; set; }
        public string EMPLOYER { get; set; }
        public string OCCUPATION { get; set; }
        public int count_ { get; set; }
        public Int64 rowNo { get; set; }
    }

    [Table("dtBATCHCONDUIT")]
    public class dtBATCHCONDUIT : _entityBase_crm, iItemType
    {
        [Column]
        [Key]
        public int dtBATCHCONDUITID { get; set; }

        [Column]
        public int dtBATCHID { get; set; }

        [Column]
        public decimal? AMT { get; set; }

        [Column]
        public int CONDUITNO { get; set; }

        [Column]
        public decimal? FEE { get; set; }

        [Column]
        public decimal? NET { get; set; }

        [Column]
        public bool MEMO { get; set; }
    }
    public class conduit_dist_export_model : _entityBase_crm, iItemType
    {
        public int orgId { get; set; }
        public string orgName { get;set; }
        public string orgStatement { get; set; }
        public DateTime transmitted_date {  get; set; }
        public decimal cc_processing_fee { get; set; }
        public int[] conduit_dist_ids {  get; set; }
    }
}