﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Net;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Web.Http;
using System.Web.Script.Serialization;
using System.Drawing;
using System.IO;

using AutoMapper;
using Ninject;
using Ninject.Web.Mvc;

using cmdiapp.n.core.Core;
using cmdiapp.n.core.Core.App_Start;
using cmdiapp.n.core.Domain.ViewModels;
using cmdiapp.n.core.Areas.crm.Core;
using cmdiapp.n.core.Areas.crm.Domain.Models;
using cmdiapp.n.core.Areas.crm.Domain.ViewModels;
using cmdiapp.n.core.Areas.crm.Domain.Data;
using cmdiapp.n.core.Areas.crm.Domain.Repositories;
using cmdiapp.n.core.Areas.crm.Domain.Services;
using cmdiapp.n.core.Areas.dataService.Domain.Data;
using cmdiapp.n.core.Areas.dataService.Domain.Models;
using cmdiapp.n.core.Areas.dataService.Domain.Services;
using cmdiapp.n.core.Presentation.Controllers;

using cmdiapp.n.core.Domain.Models;
using cmdiapp.n.core.Areas.query.Domain.Models;
using cmdiapp.n.core.Library;
using System.Data;
using cmdiapp.n.core.Areas.query;
using System.Web.Http.OData;

namespace cmdiapp.n.core.Areas.crm.Presentation.api
{
    [Authorize]
    public class ConduitController : ApiController
    {
        #region [[ Declaration ]]
        private I_entity_crm _entity_crm;
        private userSession _userSession;
        private readonly IReportDataService _reportService;

        public class NewConduitDistribParm
        {
            public QueryInstanceInfo qinstance { get; set; }
            public string conduitNo { get; set; }
            public DateTime distribDte { get; set; }
            public string comment { get; set; }
        }
        public class exportDistributionParm
        {
            public string searchVal { get; set; }
        }

        private class iCreateConduitDistrib_Result
        {
            public string MESSAGE { get; set; }
            public Nullable<int> DISTRIBID { get; set; }
        }

        private class wp_delete_conduitdist_Result
        {
            public bool SUCCEED { get; set; }
            public string MESSAGE { get; set; }
        }

        private class wp_complete_conduitdist_Result
        {
            public bool SUCCEED { get; set; }
            public string MESSAGE { get; set; }
        }

        #endregion

        #region [[ (constructor) ConduitController ]]
        public ConduitController()
        {
            _entity_crm = I_entityManager_crm.getEntity();
            _userSession = session.userSession;
            _reportService = NinjectMVC.kernel.Get<IReportDataService>();
        }
        #endregion

        #region [[ Get Conduit Info ]]
        [apiAuthorize(AccessElement = "Conduit", AccessLevel = "v")]
        [HttpGet, Route("crm/api/Conduit/GetConduitInfo/{id}")]
        public CONDUIT GetConduitInfo(int id)
        {
            return _entity_crm.Single<CONDUIT>(c => c.PID == id);
        }
        #endregion

        #region [[ Save Conduit Info ]]
        [apiAuthorize(AccessElement = "Conduit", AccessLevel = "e")]
        [HttpPost, Route("crm/api/Conduit/SaveConduitInfo")]
        public genericResponse SaveConduitInfo(CONDUIT _parm)
        {
            try
            {
                CONDUIT _conduit = new CONDUIT();
                // Test for already assigned Conduit# in new record
                CONDUIT _dupconduit = _entity_crm.Single<CONDUIT>(c => c.CONDUITNO == _parm.CONDUITNO && c.PID != _parm.PID);
                if (_dupconduit != null)
                {
                    return new genericResponse() { success = false, message = "Conduit# already assigned" };
                }

                if (_parm.CONDUITID != null && _parm.CONDUITID > 0)
                { 
                    _conduit = _entity_crm.Single<CONDUIT>(c => c.CONDUITID == _parm.CONDUITID);
                    if (_conduit == null)
                        _conduit = new CONDUIT();
                }
                _conduit.PID = _parm.PID;
                _conduit.CONDUITNO = _parm.CONDUITNO;
                _conduit.COMMITTEE = _parm.COMMITTEE;
                _conduit.FECCMTEID = _parm.FECCMTEID;
                _conduit.ELECTCD = _parm.ELECTCD;
                _conduit.ELECTYR = _parm.ELECTYR;
                _conduit.ELECTOTH = _parm.ELECTOTH;
                _conduit.OFFICE = _parm.OFFICE;
                _conduit.STATE = _parm.STATE;
                _conduit.DISTRICT = _parm.DISTRICT;
                _conduit.COMMENT = _parm.COMMENT;
                _conduit.ACTIVE = _parm.ACTIVE;
                _conduit.updating_uid = crmSession.UID().Value;
                _conduit.CANDPREFIX = _parm.CANDPREFIX;
                _conduit.CANDFNAME = _parm.CANDFNAME;
                _conduit.CANDMNAME = _parm.CANDMNAME;
                _conduit.CANDLNAME = _parm.CANDLNAME;
                _conduit.CANDSUFFIX = _parm.CANDSUFFIX;
                _conduit.CANDFECID = _parm.CANDFECID;

                if (_conduit.CONDUITID == null || _conduit.CONDUITID == 0)
                    _entity_crm.Add(_conduit);
                else
                    _entity_crm.Update(_conduit);
                _entity_crm.CommitChanges();

                return new genericResponse() { success = true };
            }
            catch(Exception ex)
            {
                return new genericResponse() { success = false, message = "Problem saving Conduit" };
            }
        }
        #endregion

        #region [[ Get Conduit Allocation Info ]]
        [apiAuthorize(AccessElement = "Conduit", AccessLevel = "v")]
        [HttpGet, Route("crm/api/Conduit/GetConduitAlloc/{id}")]
        public IEnumerable<MONYCONDUIT> GetConduitAlloc(int id)
        {
            return _entity_crm.All<MONYCONDUIT>().Where(m => m.MID == id).AsEnumerable();
        }
        #endregion

        #region [[ Get Conduit Name ]]
        [apiAuthorize(AccessElement = "Conduit", AccessLevel = "v")]
        [HttpGet, Route("crm/api/Conduit/GetConduit")]
        public ConduitnoR GetConduit(int conduitno)
        {
            return _entity_crm.Single<ConduitnoR>(a => a.CONDUITNO == conduitno);
        }
        #endregion

        #region [[ All Conduits ]]
        [HttpGet, Route("crm/api/Conduit/Conduitnos")]
        [EnableQuery]
        public IQueryable<ConduitnoR> Conduitnos()
        {
            return _entity_crm.All<ConduitnoR>().AsQueryable();

        }
        #endregion

        #region [[ Get Conduit Distributions ]]
        [HttpGet, Route("crm/api/Conduit/GetDistribution")]
        [apiAuthorize(AccessElement = "Conduit", AccessLevel = "v")]
        public genericResponse SearchDistributions(string searchText, int? page, int? pageSize)
        {
            #region [ Retrieve "Sort" options ]
            string _searchText = Library.util.kill_sqlBlacklistWord((searchText ?? "").Trim().Replace("'", "''''"));

            int _pageSize = (pageSize == null || pageSize.Value == 0 ? 10 : pageSize.Value);
            int _pageNo = (page == null || page.Value == 0 ? 1 : page.Value);

            string sortField = HttpContext.Current.Request.QueryString["sort[0][field]"];
            string sortDir = HttpContext.Current.Request.QueryString["sort[0][dir]"];
            string _sortOptions = "";
            if (!string.IsNullOrEmpty(sortField))
                _sortOptions = sortField;
            if (!string.IsNullOrEmpty(sortField) && !string.IsNullOrEmpty(sortDir))
                _sortOptions = _sortOptions + " " + sortDir;
            #endregion

            List<conduitDistribR_ext> _list = new List<conduitDistribR_ext>();

            string _sql = "SELECT *, DISTRIBID as Id FROM v_conduit_distribution";
            if (!string.IsNullOrEmpty(searchText))
            {
                searchText = Library.util.kill_sqlBlacklistWord(searchText);
                int _conduitno;
                if (!string.IsNullOrEmpty(searchText))
                {
                    if (int.TryParse(searchText, out _conduitno))
                    {
                        _sql += " WHERE CONDUITNO = " + _conduitno.ToString();
                    }
                    else
                    {
                        _sql += String.Format(" WHERE CONDUITNAME like ''%{0}%''", searchText);
                    }
                }
            }
            if (string.IsNullOrEmpty(_sortOptions)) _sortOptions = "DISTRIBDTE DESC";

            var q = _entity_crm.getContext().Database.SqlQuery<conduitDistribR_ext>(String.Format("EXEC dbo.get_pagedDataSet '{0}', '{1}', {2}, {3}", _sql, _sortOptions, pageSize, page));
            _list = q.ToList();

            Mapper.CreateMap<conduitDistribR_ext, conduitDistribR>();
            IEnumerable<iItemType> results = _list.Select(a => Mapper.Map<conduitDistribR_ext, conduitDistribR>(a)).ToList();
            if (results.Count() > 0)
            {
                return new genericResponse() { success = true, __count = _list.FirstOrDefault().count_, results = results.ToList() };
            }
            else
            {
                return new genericResponse() { success = true, __count = 0 };
            }
        }
        #endregion

        #region [[ Search ]]
        #region [ (private) Advanced Search - SQL Filtering ]

        #region [ func.string.get_sqlFIELDs ] Enable optional OUTPUT fields if used.
        private string get_sqlFIELDs(string p_sql_where, string p_default_fields)
        {
            return p_default_fields;
        }
        #endregion

        #endregion

        #region [ (private-List<NewConduitDistrib_ext>) Advanced Search ]
        private List<NewConduitDistrib_ext> sqlSearch(List<searchItem> filters, int pageSize, int pageNo, string sortOptions, string qDefName = "crm_newConduitDistrib")
        {
            if (string.IsNullOrEmpty(sortOptions)) sortOptions = "LNAME, FNAME";

            // SQL Definition
            q_def_S _def = q_library.get_q_def_fr_cache(qDefName);

            // Compose SQL
            string sql = q_library.getSQL(_def, filters, session.currentDomain_project._connectionString(), null, get_sqlFIELDs, true);

            System.Diagnostics.Stopwatch stopWatch = new System.Diagnostics.Stopwatch();
            stopWatch.Start();
            string sqlParam = sql.Trim().Replace("'", "''");
            var q = _entity_crm.getContext().Database.SqlQuery<NewConduitDistrib_ext>(String.Format("EXEC dbo.get_pagedDataSet '{0}', '{1}', {2}, {3}", sqlParam, sortOptions, pageSize, pageNo));

            List<NewConduitDistrib_ext> result = q.ToList();
            stopWatch.Stop();

            cl_query.create_a_sqlLog(
                        sql.Trim(),
                        "crm/api/Conduit/SearchnewDistrib(Adv)",
                        util.serialize_toXmlString(filters),
                        crmSession.UID().Value,
                        (result != null && result.Count() > 0 ? result.FirstOrDefault().count_ : 0),
                        "New Distribution Search - View Columns",
                        "",
                        _entity_crm,
             Convert.ToSingle(stopWatch.ElapsedMilliseconds / 1000.0));

            return result;
        }

        private System.Data.DataSet sqlSearch_forExport__dataset(List<searchItem> filters)
        {

            // SQL Definition
            q_def_S _def = q_library.get_q_def_fr_cache("crm_newConduitDistrib");

            // Compose SQL
            string sql = q_library.getSQL(_def, filters, session.currentDomain_project._connectionString(), null, get_sqlFIELDs, false);

            System.Data.DataSet _dataSet = q_library.get_dataset_w_sql__single(session.currentDomain_project._connectionString(), sql, "result", 0, 0);

            return _dataSet;
        }
        #endregion

        #region [[ (genericResponse) Search[POST] - crm/api/Conduit/SearchnewDistrib ]]
        [HttpPost, Route("crm/api/Conduit/SearchnewDistrib")]
        [apiAuthorize(AccessElement = "Conduit", AccessLevel = "a")]
        public genericResponse SearchnewDistrib(searchParam _param, string qDefName = "crm_newConduitDistrib")
        {
            #region [ Retrieve Input ]
            string _searchText = _param.searchText_;

            if (string.IsNullOrEmpty(_searchText)
                && (_param.searchData == null || _param.searchData.Count() == 0 || string.IsNullOrEmpty(_param.searchData[0].valuef)))
                return new genericResponse() { __count = 0, success = true };

            int _pageSize = _param.pageSize_;
            int _pageNo = _param.page_;
            string _sortOptions = _param.sortOption1_;
            #endregion

            List<NewConduitDistrib_ext> _list;

            _list = sqlSearch(_param.searchData, _pageSize, _pageNo, _sortOptions, qDefName);

            if (_list != null && _list.Count() > 0)
            {
                Mapper.CreateMap<NewConduitDistrib_ext, newConduitDistrib>();
                IEnumerable<iItemType> results = _list.Select(a => Mapper.Map<NewConduitDistrib_ext, newConduitDistrib>(a)).ToList();
                return new genericResponse() { success = true, __count = _list.FirstOrDefault().count_, results = results.ToList() };
            }

            return new genericResponse() { success = true, __count = 0 };
        }
        #endregion
        #endregion

        #region [[ Export new Distribution ]]
        [HttpPost, Route("crm/api/Conduit/ExportnewDistribKey")]
        [apiAuthorize(AccessElement = "Conduit", AccessLevel = "e")]
        public string ExportnewDistribKey(searchParam param)
        {
            string key_ = System.Guid.NewGuid().ToString();
            HttpRuntime.Cache.Insert(key_, param, null, DateTime.UtcNow.Add(new TimeSpan(0, 0, 1)), System.Web.Caching.Cache.NoSlidingExpiration);
            return key_;
        }

        [HttpGet, Route("crm/api/Conduit/ExportnewDistrib")]
        [apiAuthorize(AccessElement = "Conduit", AccessLevel = "e")]
        public string ExportnewDistrib(string key)
        {
            key = key.Replace("\"", "").Trim();

            searchParam _param = (searchParam)HttpRuntime.Cache[key];

            System.Data.DataSet _ds = sqlSearch_forExport__dataset(_param.searchData);

            if (_ds != null)
            {
                string key_ = System.Guid.NewGuid().ToString();
                HttpRuntime.Cache.Insert(key_, _ds, null, DateTime.UtcNow.Add(new TimeSpan(1, 0, 1)), System.Web.Caching.Cache.NoSlidingExpiration);
                return key_;
            }
            else
            {
                return null;
            }
        }
        #endregion

        #region [[ Create Distribution ]]
        [HttpPost, Route("crm/api/Conduit/CreateDistribution")]
        [apiAuthorize(AccessElement = "cmdiapp.dms.distributions", AccessLevel = "a")]
        public genericResponse CreateDistribution(string conduitno, string distribdte, string comment, searchParam param)
        {
            // SQL Definition
            q_def_S _def = q_library.get_q_def_fr_cache("crm_newConduitDistrib");

            // Compose SQL
            string sql = q_library.getSQL(_def, param.searchData, session.currentDomain_project._connectionString(), null, get_sqlFIELDs, false);

            string dis_sql = String.Format("DECLARE @Message varchar(100), @DISTRIBID INT;\n" +
            "EXEC dbo.iCreateConduitDistrib '{0}','{1}','{2}','{3}','{4}', @Message OUTPUT,@DISTRIBID OUTPUT;" +
            "\n Select @Message as MESSAGE, @DISTRIBID AS DISTRIBID;",
                                   distribdte,
                                   conduitno,
                                   comment,
                                   crmSession.UID().Value,
                                   sql.Replace("'", "''"));

            iCreateConduitDistrib_Result result = _entity_crm.getContext().Database.SqlQuery<iCreateConduitDistrib_Result>(String.Format("{0}", dis_sql)).FirstOrDefault();

            if (result.DISTRIBID == null)
                return new genericResponse() { success = false, message = result.MESSAGE, UniqueId = 0 };
            else
                return new genericResponse() { success = true, message = "Distribution created successfully.", UniqueId = (Int32)result.DISTRIBID };
        }

        [HttpPost, Route("crm/api/Conduit/CreateDistribution2")]
        [apiAuthorize(AccessElement = "cmdiapp.dms.distributions", AccessLevel = "a")]
        public genericResponse CreateDistribution2(NewConduitDistribParm prompt)
        {
            try
            {
                // Get Search query
                QueryRuntime qr = new QueryRuntime(prompt.qinstance);
                string sql = qr.sql();

                string dis_sql = String.Format("DECLARE @Message varchar(100), @DISTRIBID INT;\n" +
                "EXEC dbo.iCreateConduitDistrib '{0}','{1}','{2}','{3}','{4}', @Message OUTPUT,@DISTRIBID OUTPUT;" +
                "\n Select @Message as MESSAGE, @DISTRIBID AS DISTRIBID;",
                                       prompt.distribDte.Date,
                                       prompt.conduitNo,
                                       prompt.comment,
                                       crmSession.UID().Value,
                                       sql.Replace("'", "''"));

                iCreateConduitDistrib_Result result = _entity_crm.getContext().Database.SqlQuery<iCreateConduitDistrib_Result>(String.Format("{0}", dis_sql)).FirstOrDefault();

                if (result.DISTRIBID == null)
                    return new genericResponse() { success = false, message = result.MESSAGE, UniqueId = 0 };
                else
                    return new genericResponse() { success = true, message = "Conduit Distribution created successfully.", UniqueId = (int)result.DISTRIBID };
            }
            catch (Exception ex)
            {
                return new genericResponse() { success = false, message = ex.Message, UniqueId = 0 };

            }
        }
        #endregion

        #region [[ Delete Distribution ]]
        [HttpPost, Route("crm/api/Conduit/DeleteDistrib")]
        [apiAuthorize(AccessElement = "Conduit", AccessLevel = "d")]
        public genericResponse DeleteDistrib(conduitDistribR _distrib)
        {
            genericResponse _response;

            try
            {
                string _sql = String.Format("DECLARE @SUCCEED bit, @MESSAGE VARCHAR(200);\n" +
                    "EXEC dbo.wp_delete_conduitdistrib {0}, {1}, @SUCCEED OUTPUT, @MESSAGE OUTPUT;" +
                    "\n Select @SUCCEED as SUCCEED, @MESSAGE AS MESSAGE;",
                                           _distrib.DISTRIBID,
                                           crmSession.UID().Value);

                wp_delete_conduitdist_Result result = _entity_crm.getContext().Database.SqlQuery<wp_delete_conduitdist_Result>(String.Format("{0}", _sql)).FirstOrDefault();

                if (!result.SUCCEED)
                    return new genericResponse() { success = false, message = result.MESSAGE };
                else
                    return new genericResponse() { success = true, message = "Distribution deleted successfully." };
            }
            catch
            {
                _response = new genericResponse() { success = false, message = "There is a problem in deleting this distribution." };
                return _response;
            }
        }
        #endregion

        #region [[ Export Distribution ]]
        [HttpPost, Route("crm/api/Conduit/ExportDistributionKey")]
        [apiAuthorize(AccessElement = "Conduit", AccessLevel = "v")]
        public string ExportKey(string searchText)
        {
            if (string.IsNullOrEmpty(searchText)) searchText = "";

            searchParam param = new searchParam();
            param.searchText = searchText;

            string key_ = System.Guid.NewGuid().ToString();
            HttpRuntime.Cache.Insert(key_, param, null, DateTime.UtcNow.Add(new TimeSpan(1, 0, 1)), System.Web.Caching.Cache.NoSlidingExpiration);

            return key_;

        }

        [HttpGet, Route("crm/api/Conduit/ExportDistribution")]
        [apiAuthorize(AccessElement = "Conduit", AccessLevel = "v")]
        public string ExportDistribution(string key)
        {
            searchParam _param = (searchParam)HttpRuntime.Cache[key];
            string sToday = String.Format("{0:yyyyMMMdd__hh_mm_ss tt}", DateTime.Now);
            string moduleName = "ConduitDistrib";
            string fileName = moduleName + "_" + System.Guid.NewGuid() + "_" + _userSession.UserName + "_" + sToday;

            DataSet ds = null;

            string _sql = "SELECT * FROM v_conduit_distribution";
            if (!string.IsNullOrEmpty(_param.searchText))
            {
                _param.searchText = Library.util.kill_sqlBlacklistWord(_param.searchText);
                int _conduitno;
                if (!string.IsNullOrEmpty(_param.searchText))
                {
                    if (int.TryParse(_param.searchText, out _conduitno))
                    {
                        _sql += " WHERE CONDUITNO = " + _conduitno.ToString();
                    }
                    else
                    {
                        _sql += String.Format(" WHERE CONDUITNAME like '%{0}%'", _param.searchText);
                    }
                }
            }
            _sql += " ORDER BY CONDUITNO, DISTRIBDTE";

            ds = _reportService.get_dataset_w_sql__single(_sql, "ConduitDistrib");

            if (ds != null)
            {
                string key_ = System.Guid.NewGuid().ToString();
                HttpRuntime.Cache.Insert(key_, ds, null, DateTime.UtcNow.Add(new TimeSpan(1, 0, 1)), System.Web.Caching.Cache.NoSlidingExpiration);
                //return the Key
                return key_;
            }
            else
            {
                return null;
            }
        }

        [HttpPost, Route("crm/api/Conduit/ExportDistribution2")]
        [apiAuthorize(AccessElement = "Conduit", AccessLevel = "v")]
        public object ExportDistribution2(exportDistributionParm parm)
        {
            string export_sql = "SELECT * FROM v_conduit_distribution";
            if (!string.IsNullOrEmpty(parm.searchVal))
            {
                int _conduitno;
                if (!string.IsNullOrEmpty(parm.searchVal))
                {
                    if (int.TryParse(parm.searchVal, out _conduitno))
                    {
                        export_sql += " WHERE CONDUITNO = " + _conduitno.ToString();
                    }
                    else
                    {
                        export_sql += String.Format(" WHERE CONDUITNAME like '%{0}%'", parm.searchVal);
                    }
                }
            }
            export_sql += " ORDER BY CONDUITNO, DISTRIBDTE";

            DataTable table = q_library.get_dataset_w_sql__single(
                session.currentDomain_project._connectionString(), export_sql, "ConduitDistrib", 0, 0).Tables[0];

            // Log Export
            System.Diagnostics.Stopwatch stopWatch = new System.Diagnostics.Stopwatch();
            stopWatch.Start();
            stopWatch.Stop();
            cl_query.create_a_sqlLog(
                export_sql.Trim(),
                "crm/api/Conduit/ExportDistribution2",
                util.serialize_toXmlString(""),
                crmSession.UID().Value,
                (table != null && table.Rows.Count > 0 ? table.Rows.Count : 0),
                "Conduit Distribution - Export",
                "",
                _entity_crm,
                Convert.ToSingle(stopWatch.ElapsedMilliseconds / 1000.0));

            if (table != null)
            {
                // get export format from qdef
                string exportFormat = "xlsx";
                // use factory to generate correct QExportFormat class
                IQExportFormat qExport;
                try
                {
                    qExport = new QExportFormatFactory().GetQExportFormat(exportFormat);
                }
                catch (ArgumentException)
                {
                    qExport = new QExportFormatFactory().GetQExportFormat();
                }
                // call QExportFormat's ToFile method, which saves file and returns file names
                Tuple<string, string> fileNames = qExport.ToFile(table, "ConduitDistrib");
                // send file names back to client
                return new { serverFileName = fileNames.Item1, clientFileName = fileNames.Item2 };
            }
            else
            {
                return null;
            }
        }
        #endregion

        #region [[ Get Distribution by ID ]]
        [HttpGet, Route("crm/api/Conduit/Distribution/Get/{id}")]
        [apiAuthorize(AccessElement = "Conduit", AccessLevel = "v")]
        public CONDUITDIST GetDistributionById(int id)
        {
            return _entity_crm.Single<CONDUITDIST>(a => a.DISTRIBID == id);
        }
        #endregion

        #region [[ Save Distribution ]]
        [HttpPost, Route("crm/api/Conduit/SaveDistrib")]
        [apiAuthorize(AccessElement = "Conduit", AccessLevel = "e")]
        public genericResponse SaveDistribution(CONDUITDIST _conduitdist)
        {
            try
            {
                CONDUITDIST _record = _entity_crm.Single<CONDUITDIST>(a => a.DISTRIBID == _conduitdist.DISTRIBID);

                if (_record != null)
                {
                    _record.DISTRIBDTE = _conduitdist.DISTRIBDTE;
                    _record.CONDUITNO = _conduitdist.CONDUITNO;
                    _record.COMMITTEE = _conduitdist.COMMITTEE;
                    _record.FECCMTEID = _conduitdist.FECCMTEID;
                    _record.ELECTCD = _conduitdist.ELECTCD;
                    _record.ELECTYR = _conduitdist.ELECTYR;
                    _record.ELECTOTH = _conduitdist.ELECTOTH;
                    _record.OFFICE = _conduitdist.OFFICE;
                    _record.STATE = _conduitdist.STATE;
                    _record.DISTRICT = _conduitdist.DISTRICT;
                    _record.COMMENT = _conduitdist.COMMENT;
                    _record.UID = crmSession.UID().Value;
                    _record.UPDATEDON = DateTime.Now;
                    _record.CANDPREFIX = _conduitdist.CANDPREFIX;
                    _record.CANDFNAME = _conduitdist.CANDFNAME;
                    _record.CANDMNAME = _conduitdist.CANDMNAME;
                    _record.CANDLNAME = _conduitdist.CANDLNAME;
                    _record.CANDSUFFIX = _conduitdist.CANDSUFFIX;
                    _record.CANDFECID = _conduitdist.CANDFECID;

                    _entity_crm.Update(_record);
                    _entity_crm.CommitChanges();


                    List<iItemType> _dataset = new List<iItemType>();
                    _dataset.Add(_record);

                    genericResponse _response = new genericResponse() { success = true, __count = _dataset.Count, results = _dataset.ToList() };
                    return _response;
                }
                else
                {
                    genericResponse _response = new genericResponse() { success = false, __count = 0 };
                    return _response;
                }
            }
            catch (Exception ex)
            {
                genericResponse _response = new genericResponse() { success = false, __count = 0, message = ex.Message };
                return _response;
            }
        }
        #endregion

        #region [[ Complete Distribution ]]
        [HttpPost, Route("crm/api/Conduit/CompleteDistrib")]
        [apiAuthorize(AccessElement = "Conduit", AccessLevel = "a")]
        public genericResponse CompleteDistribution(CONDUITDIST _conduitdist)
        {
            try
            {
                CONDUITDIST _record = _entity_crm.Single<CONDUITDIST>(a => a.DISTRIBID == _conduitdist.DISTRIBID);

                if (_record != null)
                {
                    _record.DISTRIBDTE = _conduitdist.DISTRIBDTE;
                    _record.CONDUITNO = _conduitdist.CONDUITNO;
                    _record.COMMITTEE = _conduitdist.COMMITTEE;
                    _record.FECCMTEID = _conduitdist.FECCMTEID;
                    _record.ELECTCD = _conduitdist.ELECTCD;
                    _record.ELECTYR = _conduitdist.ELECTYR;
                    _record.ELECTOTH = _conduitdist.ELECTOTH;
                    _record.OFFICE = _conduitdist.OFFICE;
                    _record.STATE = _conduitdist.STATE;
                    _record.DISTRICT = _conduitdist.DISTRICT;
                    _record.COMMENT = _conduitdist.COMMENT;
                    _record.UID = crmSession.UID().Value;
                    _record.UPDATEDON = DateTime.Now;
                    _record.CANDPREFIX = _conduitdist.CANDPREFIX;
                    _record.CANDFNAME = _conduitdist.CANDFNAME;
                    _record.CANDMNAME = _conduitdist.CANDMNAME;
                    _record.CANDLNAME = _conduitdist.CANDLNAME;
                    _record.CANDSUFFIX = _conduitdist.CANDSUFFIX;
                    _record.CANDFECID = _conduitdist.CANDFECID;

                    _entity_crm.Update(_record);
                    _entity_crm.CommitChanges();

                    string _sql = String.Format("DECLARE @SUCCEED bit, @MESSAGE VARCHAR(200);\n" +
                        "EXEC dbo.wp_complete_conduitdistrib {0}, {1}, @SUCCEED OUTPUT, @MESSAGE OUTPUT;" +
                        "\n Select @SUCCEED as SUCCEED, @MESSAGE AS MESSAGE;",
                           _record.DISTRIBID,
                           crmSession.UID().Value);

                    wp_complete_conduitdist_Result result = _entity_crm.getContext().Database.SqlQuery<wp_complete_conduitdist_Result>(String.Format("{0}", _sql)).FirstOrDefault();

                    if (result.SUCCEED)
                    {
                        List<iItemType> _dataset = new List<iItemType>();
                        _dataset.Add(_record);

                        genericResponse _response = new genericResponse() { success = true, __count = _dataset.Count, results = _dataset.ToList() };
                        return _response;
                    }
                    else
                    {
                        genericResponse _response = new genericResponse() { success = false, __count = 0 };
                        return _response;
                    }
                }
                else
                {
                    genericResponse _response = new genericResponse() { success = false, __count = 0 };
                    return _response;
                }
            }
            catch (Exception ex)
            {
                genericResponse _response = new genericResponse() { success = false, __count = 0, message = ex.Message };
                return _response;
            }
        }
        #endregion

        #region [[ Get Distribution Details ]]
        [HttpGet, Route("crm/api/Conduit/DistributionDetails/{distribid}")]
        [apiAuthorize(AccessElement = "Conduit", AccessLevel = "v")]
        public genericResponse SearchContributions(int distribid, int? page, int? pageSize)
        {
            #region [ Retrieve "Sort" options ]
            int _pageSize = (pageSize == null || pageSize.Value == 0 ? 10 : pageSize.Value);
            int _pageNo = (page == null || page.Value == 0 ? 1 : page.Value);

            string sortField = HttpContext.Current.Request.QueryString["sort[0][field]"];
            string sortDir = HttpContext.Current.Request.QueryString["sort[0][dir]"];
            string _sortOptions = "";
            if (!string.IsNullOrEmpty(sortField))
                _sortOptions = sortField;
            if (!string.IsNullOrEmpty(sortField) && !string.IsNullOrEmpty(sortDir))
                _sortOptions = _sortOptions + " " + sortDir;
            #endregion

            List<DistributionDetail_ext> _list = new List<DistributionDetail_ext>();

            string _sql = String.Format("SELECT * FROM v_conduit_distrib_detail WHERE DISTRIBID = {0}", distribid);

            if (string.IsNullOrEmpty(_sortOptions)) _sortOptions = "LNAME, FNAME";

            var q = _entity_crm.getContext().Database.SqlQuery<DistributionDetail_ext>(String.Format("EXEC dbo.get_pagedDataSet '{0}', '{1}', {2}, {3}", _sql, _sortOptions, pageSize, page));
            _list = q.ToList();

            Mapper.CreateMap<DistributionDetail_ext, DistributionDetail>();
            IEnumerable<iItemType> results = _list.Select(a => Mapper.Map<DistributionDetail_ext, DistributionDetail>(a)).ToList();
            if (results.Count() > 0)
            {
                return new genericResponse() { success = true, __count = _list.FirstOrDefault().count_, results = results.ToList() };
            }
            else
            {
                return new genericResponse() { success = true, __count = 0 };
            }
        }
        #endregion

        #region [[ Remove contributin fro distribution ]]
        [HttpPost, Route("crm/api/Conduit/RemoveContribDistrib")]
        [apiAuthorize(AccessElement = "Conduit", AccessLevel = "e")]
        public genericResponse RemoveContribDistrib(DistributionDetail _data)
        {
            try
            {
                dtCONDUITDIST _record = _entity_crm.Single<dtCONDUITDIST>(a => a.DISTRIBID == _data.DISTRIBID && a.MONYCONDUITID == _data.MONYCONDUITID);

                if (_record != null)
                {
                   
                    _entity_crm.Delete(_record);
                    _entity_crm.CommitChanges();

                    genericResponse _response = new genericResponse() { success = true };
                    return _response;
                }
                else
                {
                    genericResponse _response = new genericResponse() { success = false, message = "Problem removing record" };
                    return _response;
                }
            }
            catch (Exception ex)
            {
                genericResponse _response = new genericResponse() { success = false, message = ex.Message };
                return _response;
            }
        }
        #endregion

        #region [[ Export Distribution Details ]]
        [HttpPost, Route("crm/api/Conduit/ExportContributionKey")]
        [apiAuthorize(AccessElement = "Conduit", AccessLevel = "v")]
        public string ExportContributionKey(string searchText)
        {
            if (string.IsNullOrEmpty(searchText)) searchText = "";

            searchParam param = new searchParam();
            param.searchText = searchText;

            string key_ = System.Guid.NewGuid().ToString();
            HttpRuntime.Cache.Insert(key_, param, null, DateTime.UtcNow.Add(new TimeSpan(1, 0, 1)), System.Web.Caching.Cache.NoSlidingExpiration);

            return key_;

        }

        [HttpGet, Route("crm/api/Conduit/ExportContribution")]
        [apiAuthorize(AccessElement = "Conduit", AccessLevel = "v")]
        public string ExportContribution(string key)
        {
            searchParam _param = (searchParam)HttpRuntime.Cache[key];
            string sToday = String.Format("{0:yyyyMMMdd__hh_mm_ss tt}", DateTime.Now);
            string moduleName = "ConduitDistrib";
            string fileName = moduleName + "_" + System.Guid.NewGuid() + "_" + _userSession.UserName + "_" + sToday;

            DataSet ds = null;

            string _sql = String.Format("SELECT * FROM v_conduit_distrib_detail WHERE DISTRIBID = {0}", _param.searchText);

            ds = _reportService.get_dataset_w_sql__single(_sql, "Contribution");

            if (ds != null)
            {
                //CreateExcelFile.CreateExcelDocument(ds.Copy(), fileName, HttpContext.Current.Response);
                string key_ = System.Guid.NewGuid().ToString();
                HttpRuntime.Cache.Insert(key_, ds, null, DateTime.UtcNow.Add(new TimeSpan(1, 0, 1)), System.Web.Caching.Cache.NoSlidingExpiration);
                //return the Key
                return key_;

            }
            else
            {
                return null;
            }
        }

        [HttpPost, Route("crm/api/Conduit/ExportContribution2/{id}")]
        [apiAuthorize(AccessElement = "Conduit", AccessLevel = "v")]
        public object ExportContribution2(int id, string conduitName = "")
        {
            // Compose Export SQL
            String export_sql = String.Format("SELECT * FROM v_conduit_distrib_detail WHERE DISTRIBID = {0}", id);

            DataTable table = q_library.get_dataset_w_sql__single(
                session.currentDomain_project._connectionString(), export_sql, "result", 0, 0).Tables[0];

            // Log Export
            System.Diagnostics.Stopwatch stopWatch = new System.Diagnostics.Stopwatch();
            stopWatch.Start();
            stopWatch.Stop();
            cl_query.create_a_sqlLog(
                export_sql.Trim(),
                "crm/api/Conduit/ExportContribution2",
                util.serialize_toXmlString(""),
                crmSession.UID().Value,
                (table != null && table.Rows.Count > 0 ? table.Rows.Count : 0),
                "Conduit Distribution Detail - Export",
                "",
                _entity_crm,
                Convert.ToSingle(stopWatch.ElapsedMilliseconds / 1000.0));

            if (table != null)
            {
                // get export format from qdef
                string exportFormat = "xlsx";
                // use factory to generate correct QExportFormat class
                IQExportFormat qExport;
                try
                {
                    qExport = new QExportFormatFactory().GetQExportFormat(exportFormat);
                }
                catch (ArgumentException)
                {
                    qExport = new QExportFormatFactory().GetQExportFormat();
                }
                // call QExportFormat's ToFile method, which saves file and returns file names
                Tuple<string, string> fileNames = qExport.ToFile(table, "ConduitDistribDetail_" +conduitName);
                // send file names back to client
                return new { serverFileName = fileNames.Item1, clientFileName = fileNames.Item2 };
            }
            else
            {
                return null;
            }
        }
        #endregion

        #region [[ Print Conduit Distribution Detail ]]

        [HttpPost, Route("crm/api/Conduit/PrintConduitDistributionDetailKey")]
        [apiAuthorize(AccessElement = "Conduit", AccessLevel = "v")]
        public genericResponseWithKey PrintConduitDistributionDetailKey(conduit_dist_export_model _data)
        {
            System.Xml.XmlNode _node;
            System.Collections.Generic.List<string> ids = new System.Collections.Generic.List<string>();
            System.Collections.Generic.List<string> sqls = new System.Collections.Generic.List<string>();
            System.Collections.Generic.List<int> zeros = new System.Collections.Generic.List<int>();
            System.Xml.XmlNodeList xlist;
            System.Data.DataSet dSet;
            supportMethods _supportMethods = new supportMethods();
            string key_ = System.Guid.NewGuid().ToString();

            string sql = string.Format(@"SELECT CD.DISTRIBID, CD.COMMITTEE,
SUM(CASE WHEN MT.MONYTYPE IN ('CH','CB') THEN D.AMT ELSE 0 END) AS CHECKAMT,
SUM(CASE WHEN MT.MONYTYPE IN ('CC','AN') THEN D.AMT ELSE 0 END) AS CCAMT,
COUNT(M.MID) AS TOTALCOUNT,
'{0}' as orgName, '{1}' as transmitted_date, '{2}' as orgStatement, {3} as cc_processing_fee,
(select imageContent from x_gs_org where id = {5}) AS headerImg
FROM CONDUITDIST CD 
LEFT OUTER JOIN v_conduit_distrib_detail D ON D.DISTRIBID = CD.DISTRIBID
LEFT OUTER JOIN MONY M ON M.MID = D.MID
LEFT OUTER JOIN lkMONYTYPE MT ON MT.MONYTYPEID = M.MONYTYPEID
WHERE MONYTYPE IN ('CH','CB','CC','AN')
AND D.DISTRIBID IN ({4})
GROUP BY CD.DISTRIBID,CD.COMMITTEE

", _data.orgName, _data.transmitted_date.ToString("MM/dd/yyyy"),
_data.orgStatement.Replace("'","''"), _data.cc_processing_fee, string.Join(",", _data.conduit_dist_ids), _data.orgId);

            string _xml = _supportMethods.get_report_data_sourceDef_in_xml_ConduitDistDetail(sql, string.Join(",", _data.conduit_dist_ids),_data.cc_processing_fee);

            #region [[ For Collection ]]
            //Create collection
            System.Xml.XmlDocument doc = new System.Xml.XmlDocument();
            doc.LoadXml(_xml);
            _node = doc.DocumentElement.SelectSingleNode("/ReportParameters");

            System.Collections.Specialized.NameValueCollection _collection = new System.Collections.Specialized.NameValueCollection();
            _collection.Add("cc", _node.Attributes["clientcode"].Value);
            _collection.Add("rn", _node.Attributes["reportcode"].Value);
            _collection.Add("XML", _node.OuterXml);
            //Save the Report Parameter collection in the Session
            HttpRuntime.Cache.Insert(key_ + "_c", _collection, null, DateTime.UtcNow.Add(new TimeSpan(1, 0, 1)), System.Web.Caching.Cache.NoSlidingExpiration);
            #endregion

            #region [[ For Queries DataSet - First Dataset ]]
            //Get Queries Dataset
            xlist = _node.SelectNodes("Queries/Query");

            foreach (System.Xml.XmlNode node in xlist)
            {
                ids.Add(node.Attributes["id"].Value);
                sqls.Add(node.InnerText);
                zeros.Add(0);
            }
            dSet = _reportService.get_dataset_w_sql__multi(sqls, ids, zeros, zeros);
            //First Dataset
            HttpRuntime.Cache.Insert(key_ + "_fd", dSet, null, DateTime.UtcNow.Add(new TimeSpan(1, 0, 1)), System.Web.Caching.Cache.NoSlidingExpiration);
            #endregion
            #region [[ For SubQueries Dataset ]]
            //First set all for null and then Get SubQueries DataSet
            xlist = null;
            dSet = null;
            xlist = _node.SelectNodes("Queries/SubQuery");
            ids.Clear();
            sqls.Clear();
            zeros.Clear();
            foreach (System.Xml.XmlNode node in xlist)
            {
                ids.Add(node.Attributes["id"].Value);
                sqls.Add(node.InnerText);
                zeros.Add(0);
            }
            dSet = _reportService.get_dataset_w_sql__multi(sqls, ids, zeros, zeros);
            //Second Dataset
            HttpRuntime.Cache.Insert(key_ + "_sd", dSet, null, DateTime.UtcNow.Add(new TimeSpan(1, 0, 1)), System.Web.Caching.Cache.NoSlidingExpiration);
            #endregion
            return new genericResponseWithKey() { success = true, __count = 0, Key = key_ };

        }

        #endregion

        #region [[ Get Conduit Allocation Info in dtBATCH ]]
        [apiAuthorize(AccessElement = "Conduit", AccessLevel = "v")]
        [HttpGet, Route("crm/api/Conduit/GetdtBatchConduitAlloc/{id}")]
        public IEnumerable<dtBATCHCONDUIT> GetdtBatchConduitAlloc(int id)
        {
            return _entity_crm.All<dtBATCHCONDUIT>().Where(m => m.dtBATCHID == id).AsEnumerable();
        }
        #endregion


    }
}
