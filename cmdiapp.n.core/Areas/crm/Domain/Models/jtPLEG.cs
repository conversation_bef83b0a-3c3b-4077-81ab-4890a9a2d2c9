﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Data;
using System.Linq;
using System.Web.Mvc;
using System.Data.Linq;
using System.ComponentModel.DataAnnotations.Schema;

using cmdiapp.n.core.Domain.ViewModels;
using cmdiapp.n.core.Areas.crm.Domain.Data;

namespace cmdiapp.n.core.Areas.crm.Domain.Models
{

    [Table("jtPLEG")]
    public class jtPLEG : _entityBase_crm, iItemType
    {
        [Column]
        [Key]
        public int JTPLEGID { get; set; }

        [Column]
        public int PID { get; set; }

        [Column]
        public int PLEGID { get; set; }

        [Column]
        public DateTime? PLEGSTART { get; set; }

        [Column]
        public DateTime? PLEGEND { get; set; }

        [Column]
        public decimal? PLEGAMT { get; set; }

        [Column]
        public decimal? PLEGTOTAL { get; set; }

        [Column]
        public Int16? DUEFREQID { get; set; }

        [Column]
        public Int16? NUMPAY { get; set; }

        [Column]
        public Int16? DUEMTHDID { get; set; }

        [Column]
        public string ROUTNO { get; set; }

        [Column]
        public string ACCTNO { get; set; }

        [Column]
        public string CARDTYPE { get; set; }

        [Column]
        public string CARDNO { get; set; }

        [Column]
        public string CARDNAME { get; set; }

        [Column]
        public Int16? CARDEXPMO { get; set; }

        [Column]
        public Int16? CARDEXPYR { get; set; }

        [Column]
        public bool? VERIFIED { get; set; }

        [Column]
        public byte? CLOSED { get; set; }

        [Column]
        public decimal? cPAIDAMT { get; set; }

        [Column]
        public Int16? cPAIDGIFTS { get; set; }

        [Column]
        public DateTime? cPAIDLAST { get; set; }

        [Column]
        public decimal? cDUEAMT { get; set; }

        [Column]
        public Int16? cDUEGIFTS { get; set; }

        [Column]
        public DateTime? cDUELAST { get; set; }

        [Column]
        public decimal? cLEFTAMT { get; set; }

        [Column]
        public Int16? cLEFTGIFTS { get; set; }

        [Column]
        public DateTime? cLEFTLAST { get; set; }

        [Column]
        public decimal? cBALAMT { get; set; }

        [Column]
        public DateTime? cBALDTE { get; set; }

        [Column]
        public string COMMENT { get; set; }

        [Column]
        public DateTime? CREATEDON { get; set; }

        [Column]
        public DateTime? UPDATEDON { get; set; }

        [Column]
        public int? SRCEID { get; set; }

        [Column("_updating_uid")]
        public Int16? updating_uid { get; set; }
    }

    public class jtPLEGList : _entityBase_crm, iItemType
    {
        public int JTPLEGID { get; set; }
        public DateTime? PLEGSTART { get; set; }
        public DateTime? PLEGEND { get; set; }
        public decimal PLEGAMT { get; set; }
        public bool VERIFIED { get; set; }
        public byte CLOSED { get; set;  }
        public string ROUTNO { get; set; }
        public string ACCTNO { get; set; }
        public string SRCECODE { get; set; }
    }

    [NotMapped]
    public class jtPLEGList_ext : jtPLEGList
    {
        public int count_ { get; set; }

        public Int64 rowNo { get; set; }
    }
}