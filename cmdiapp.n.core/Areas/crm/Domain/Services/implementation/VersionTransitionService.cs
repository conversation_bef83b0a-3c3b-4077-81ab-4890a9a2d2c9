﻿using cmdiapp.n.core.Core;
using cmdiapp.n.core.Domain.Models;
using cmdiapp.n.core.Library;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.Globalization;
using System.Linq;
using System.Net;
using System.Web;

namespace cmdiapp.n.core.Areas.crm.Domain.Services.implementation
{
    public class VersionTransitionService : IVersionTransitionService
    {
        private readonly string REDIRECT_UNTIL_KEY = "redirectExternalIPUntil";
        private readonly string REDIRECT_FROM_DOMAIN_KEY = "redirectExternalIPFromDomain";
        private readonly string REDIRECT_TO_DOMAIN_KEY = "redirectExternalIPToDomain";

        /*
         Caches user's last login time previous to the current session
             */
        public void CachePreviousLastLogin(DateTime dateTime, string userId)
        {
            if (!string.IsNullOrEmpty(userId))
            {
                cacheManager.insert(GetCacheKeyFromUser(userId), dateTime);                
            }
        }

        /*
         Returns from cache user's last login time previous to current session.
             */
        public DateTime? GetPreviousLastLogin(string userId)
        {
            return (DateTime?)cacheManager.get(GetCacheKeyFromUser(userId));
        }

        private string GetCacheKeyFromUser(string userId)
        {
            return $"{userId}_prevLastLogin";
        }

        /*
         Returns boolean whether redirect is necessary
            */
        public bool RedirectIsRequired(HttpRequestBase request)
        {
            string domain = string.Join(".", request.Url.Host.Split('.').Skip(1));
            string redirectFrom = ConfigurationManager.AppSettings[REDIRECT_FROM_DOMAIN_KEY] ?? "";
            if (domain != redirectFrom)
            {
                return false;
            }

            DateTime redirectUntil = GetRedirectUntilDate();
            if (redirectUntil == null || DateTime.Now > redirectUntil)
            {
                return false;
            }

            return !util.IsPrivateIpAddress(request?.UserHostAddress ?? "");            
        }
        /*
         Returns url to redirect to
            */
        public string RedirectURL(HttpRequestBase request)
        {
            string toDomain = ConfigurationManager.AppSettings[REDIRECT_TO_DOMAIN_KEY] ?? "";
            if (string.IsNullOrEmpty(toDomain))
            {
                return request.Url.AbsoluteUri;
            }
            return $"http://{util.get_subDomain(request.Url.AbsoluteUri)}.{toDomain}/Account/LogOn?Redirected=true&ReturnUrl=";
        }

        private DateTime GetRedirectUntilDate()
        {
            string configValue = ConfigurationManager.AppSettings[REDIRECT_UNTIL_KEY] ?? "";
            if (!DateTime.TryParseExact(
                    configValue,
                    "MM/dd/yyyy hh:mm:ss tt",
                    new CultureInfo("en-US"),
                    DateTimeStyles.None, out DateTime untilDate))
            {
                return DateTime.MinValue;
            }

            return untilDate;
        }
    }
}