﻿using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace cmdiapp.n.core.Areas.query.Domain.Models
{
    public class UserQueryConfig
    {
        [JsonProperty("queryName")]
        public string QueryName { get; set; }

        [JsonProperty("hiddenColumns")]
        public string[] HiddenColumns { get; set; }

        [JsonProperty("columnSequence")]
        public Dictionary<string, int> ColumnSequence { get; set; }

        public UserQueryConfig(string queryName)
        {
            QueryName = queryName;
            HiddenColumns = new string[0];
            ColumnSequence = new Dictionary<string, int>();
        }
    }
}