﻿using cmdiapp.n.core.Areas.query.Domain.Models;
using System;

namespace cmdiapp.n.core.Areas.crm.Domain.Models
{
    public class PeopleLastActOnDateSpecificationFilter : IExternalApiSpecificationFilter
    {
        private const string LastActOnDateFilterId = "pspp_LASTACTON";
        private const int DateOnAfterIndex = 2;
        private const int DateOnBeforeIndex = 3;

        public DateTime? LastActOnDate { get; }

        public PeopleLastActOnDateSpecificationFilter(DateTime? _LastActOnDate)
        {
            LastActOnDate = _LastActOnDate;
        }

        public QueryFilterInstance ConvertToQueryInstanceFilter()
        {
            var filter = new QueryFilterInstance { key = LastActOnDateFilterId };
            filter.operatorIdx = DateOnAfterIndex;
            if (LastActOnDate == null)
            {
                // Choose a date that will not filter out any records.
                filter.values = new string[1] { FormatDate(new DateTime(1800, 1, 1)) };
            }
            else
            {
                filter.values = new string[1] { FormatDate(LastActOnDate.Value) };
            }
            return filter;
        }

        private string FormatDate(DateTime date)
        {
            return ExternalApiSpecificationUtil.FormatDate(date);
        }
    }
}