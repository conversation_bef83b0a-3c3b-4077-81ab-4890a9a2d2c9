﻿using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

using cmdiapp.n.core.Areas.crm.Domain.Data;
using cmdiapp.n.core.Domain.ViewModels;
using System.Collections.Generic;

namespace cmdiapp.n.core.Areas.crm.Domain.Models
{
    [Table("SPCEVNTBRIEF")]
    public class SPCEVNTBRIEF : _entityBase_crm, iItemType
    {
        [Key]
        [Column]
        public int SPCEVNTBRIEFID { get; set; }

        [Column]
        public int SPCEVNTID { get; set; }

        [Column]
        public DateTime UPDATEDON { get; set; }

        [Column]
        public string CONFIG { get; set; }

        public SPCEVNTBRIEF()
        {
        }

        public SPCEVNTBRIEF(int spcevntId)
        {
            SPCEVNTID = spcevntId;
            UPDATEDON = DateTime.Now;
        }
    }

    [Table("jtSPCEVNTPEOPLE")]
    public class jtSPCEVNTPEOPLE : _entityBase_crm, iItemType
    {
        [Column]
        [Key]
        public int jtSPCEVNTPEOPLEID { get; set; }

        [Column]
        public int SPCEVNTID { get; set; }

        [Column]
        public int PID { get; set; }

        [Column]
        public string DESCRIP { get; set; }
    }

    [Table("SPCEVNTTIMELINE")]
    public class SPCEVNTTIMELINE : _entityBase_crm, iItemType
    {
        [Column]
        [Key]
        public int SPCEVNTTIMELINEID { get; set; }

        [Column]
        public int SPCEVNTBRIEFID { get; set; }

        [Column]
        public DateTime STARTTIME { get; set; }

        [Column]
        public string DESCRIP { get; set; }
    }

    [Table("SPCEVNTBRIEFCONTENT")]
    public class SPCEVNTBRIEFCONTENT : _entityBase_crm, iItemType
    {
        [Column]
        [Key]
        public int SPCEVNTBRIEFCONTENTID { get; set; }

        [Column]
        public int SPCEVNTBRIEFID { get; set; }

        [Column]
        public string TITLE { get; set; }

        [Column]
        public byte[] PICTURE { get; set; }

        [Column]
        public string CONTENT { get; set; }
    }

    [NotMapped]
    public class EventContactWithPeopleR : iItemType
    {
        public jtSPCEVNTPEOPLE contact { get; set; }
        public PeopleR peopleR { get; set; }
    }

    [NotMapped]
    public class EventBriefing : iItemType
    {
        public int SPCEVNTBRIEFID { get; set; }
        public string CONFIG { get; set; }
        public List<EventContactWithPeopleR> CONTACTS { get; set; }
        public List<SPCEVNTTIMELINE> TIMELINE { get; set; }
        public List<SPCEVNTBRIEFCONTENT> CONTENTS { get; set; }
    }
}