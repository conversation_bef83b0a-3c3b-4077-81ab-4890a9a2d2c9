﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

using Ninject;
using Ninject.Web.Mvc;

using cmdiapp.n.core.Core.App_Start;
using cmdiapp.n.core.Areas.crm.Domain.Models;
using cmdiapp.n.core.Areas.crm.Domain.ViewModels;
using cmdiapp.n.core.Areas.crm.Domain.Repositories;

namespace cmdiapp.n.core.Areas.crm.Domain.Services
{
    public class customDataService : IcustomDataService
    {
        private I___unitOfWork _unitOfWork;
        private I__DBFactory _dbFactory;

        public customDataService(I___unitOfWork unitOfWork, I__DBFactory dbFactory)
        {
            _unitOfWork = unitOfWork;
            _dbFactory = dbFactory;
        }
        public IEnumerable<fundraiser_snapshot_weekly> get_fundraiser_snapshot_weekly(int trackno)
        {
            var q = _dbFactory.getContext().Database.SqlQuery<fundraiser_snapshot_weekly>(String.Format("SELECT TOP 1 * FROM dbo.fundraiser_snapshot_weekly({0})",trackno));

            return q;
        }
    }
}