﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using cmdiapp.n.core.Areas.crm.Domain.Models;

namespace cmdiapp.n.core.Areas.crm.Domain.Repositories
{
    public interface IBatchRepository : I_baseRepository<BATCH>
    {
    }

    public interface IExceptionViewRepository : I_baseRepository<v_lkEXCEP>
    {
    }

    public interface ISourceViewRepository : I_baseRepository<v_SOURCE>
    {
    }
}