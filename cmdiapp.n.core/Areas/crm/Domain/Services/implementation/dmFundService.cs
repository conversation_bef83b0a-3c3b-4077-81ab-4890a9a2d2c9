﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

using Ninject;
using Ninject.Web.Mvc;

using cmdiapp.n.core.Core.App_Start;
using cmdiapp.n.core.Areas.crm.Domain.Models;
using cmdiapp.n.core.Areas.crm.Domain.ViewModels;
using cmdiapp.n.core.Areas.crm.Domain.Repositories;


namespace cmdiapp.n.core.Areas.crm.Domain.Services
{
    public class dmFundService : IdmFundService
    {
        private I___unitOfWork _unitOfWork;
        private I__DBFactory _dbFactory;
        private IdmFundRepository _dmFundRepository;

        public dmFundService(I___unitOfWork unitOfWork, I__DBFactory dbFactory, IdmFundRepository dmFundRepository)
        {
            _unitOfWork = unitOfWork;
            _dbFactory = dbFactory;
            _dmFundRepository = dmFundRepository;
        }

        //Read all dmFunds
        public List<dmFUND> get_all_funds()
        {
            var _allRecords = _dmFundRepository.All();
            //return data
            return _allRecords.OrderBy(p => p.FUNDID).ToList();
        }


    }


}