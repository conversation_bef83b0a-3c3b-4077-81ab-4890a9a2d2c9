﻿using OfficeOpenXml;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Web;
using System.Web.Hosting;

using cmdiapp.n.core.Core;
using System.IO;
using System.Threading.Tasks;

namespace cmdiapp.n.core.Areas.query.Domain.Models
{
    public class QExportExcel : IQExportFormat
    {
        public string FileFormat { get; set; }
        public bool IncludeHeader { get; set; }

        public QExportExcel(bool includeHeader=true)
        {
            FileFormat = "xlsx";
            IncludeHeader = includeHeader;
        }

        public Tuple<string, string> ToFile(DataTable result, string sheetName)
        {
            Tuple<string, string> fileNames = GetFileNames(sheetName);
            using (ExcelPackage pkg = new ExcelPackage())
            {
                FillExcelPackage(pkg, result, sheetName);
                SaveFile(pkg, fileNames.Item1);
            }
            return fileNames;
        }

        public Stream ToStream(DataTable result, string sheetName)
        {
            using (ExcelPackage pkg = new ExcelPackage())
            {
                FillExcelPackage(pkg, result, sheetName);
                return new MemoryStream(pkg.GetAsByteArray());
            }
        }

        private void FillExcelPackage(ExcelPackage pkg, DataTable result, string sheetName)
        {
            //Set the Document properties
            pkg.Workbook.Properties.Author = "Crimson Web Application";
            pkg.Workbook.Properties.Title = sheetName;
            //Create a sheet
            pkg.Workbook.Worksheets.Add(sheetName);
            ExcelWorksheet ws = pkg.Workbook.Worksheets[1];
            ws.Cells.Style.Font.Size = 11;
            ws.Cells.Style.Font.Name = "Calibri";
            ws.View.ShowGridLines = true;

            if (result.Columns.Count < 1)
            {
                result.Columns.Add("");
            }
            // load data
            ws.Cells["A1"].LoadFromDataTable(result, IncludeHeader);
            if (IncludeHeader)
            {
                // bold header
                ws.Row(1).Style.Font.Bold = true;
            }
            //if cell is empty set to null
            foreach (var cell in ws.Cells)
            {
                if (cell.Value.ToString() == "")
                {
                    cell.Value = null;
                }
            }

            FormatColumns(result.Columns.Cast<DataColumn>(), ws);
        }

        private void FormatColumns(IEnumerable<DataColumn> columns, ExcelWorksheet ws)
        {
            // Excel indexes are 1-based
            IEnumerable<int> dateColIdxs = columns
                .Where(col => col.DataType == typeof(DateTime))
                .Select(col => col.Ordinal + 1);
            IEnumerable<int> decimalColIdxs = columns
                .Where(col => col.DataType == typeof(Decimal))
                .Select(col => col.Ordinal + 1);

            for (int i = 1; i <= columns.Count(); i++)
            {
                if (dateColIdxs.Contains(i))
                {
                    ws.Column(i).Style.Numberformat.Format = "mm/dd/yyyy";
                }

                if (decimalColIdxs.Contains(i))
                {
                    ws.Column(i).Style.Numberformat.Format = "$#,##0.00";
                }

                ws.Column(i).AutoFit();
            }
        }

        private void SaveFile(ExcelPackage pkg, string fileName)
        {
            byte[] bytes = pkg.GetAsByteArray();
            using (Stream stream = File.Create(HostingEnvironment.MapPath($"~/tmp/{fileName}")))
            {
                stream.Write(bytes, 0, bytes.Length);
            }
        }

        private Tuple<string, string> GetFileNames(string sheetName)
        {
            string sToday = String.Format("{0:yyyyMMMdd__hh_mm_ss tt}", DateTime.Now);
            string clientFileName =
                $"{sheetName.Trim().Replace(" ", "")}_{session.currentDomain_project.db_initialcatalog}_{session.userSession.UserName}_{sToday}.{FileFormat}";
            string serverFileName =
                $"{sheetName.Trim().Replace(" ", "")}_{Guid.NewGuid()}_{session.userSession.UserName}_{sToday}.{FileFormat}";

            return new Tuple<string, string>(serverFileName, clientFileName);
        }
    }
}