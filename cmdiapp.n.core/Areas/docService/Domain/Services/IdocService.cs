﻿using System.Collections.Generic;
using System.Web.Mvc;

using cmdiapp.n.core.Areas.docService.Domain.Models;
using cmdiapp.n.core.Domain.ViewModels;

namespace cmdiapp.n.core.Areas.docService.Domain.Services
{
    public interface IdocService
    {
        genericResponse get_emailMsgData(string email,string searchText);
        List<emailMsgData> getList_emailMsgData(string email,string searchText);
        emailMsgData get_emailMsgData(int msgdataid);
        int countRecord(string email,string searchText);
    }
}
