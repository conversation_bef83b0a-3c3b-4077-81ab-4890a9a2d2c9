﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using cmdiapp.n.core.Areas.crm.Domain.Models;

namespace cmdiapp.n.core.Domain.ViewModels
{
    public class vwClubStatus
    {
        public lkCLUBSTAT lkCLUBSTAT { get; set; }
        public IEnumerable<pmCLUB> pmCLUB { get; set; }
       
    }

    public class vwFlagWindow
    {
        public dmFLAG dmFLAG { get; set; }
        public IEnumerable<lkTargetChannel> lkTargetChannels { get; set; } //All Possible channels from Lookup Table
        public bool IsSuppFlag { get; set; } //Hide and show 
        public int[] SelectedChannels { get; set; }
        
    }

    public class vwFlagWindowCatch
    {
        public dmFLAG dmFLAG { get; set; }
        public bool IsSuppFlag { get; set; } //Hide and show 
        public int[] SelectedChannels { get; set; }

    }
}