﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

using cmdiapp.n.core.Domain.ViewModels;

namespace cmdiapp.n.core.Areas.crm.Domain.ViewModels
{
    public class rYtdTotal : iItemType
    {

        public string RUNTIME { get; set; }

        public string YEAR_LABEL { get; set; }

        // Formatted Decimals
        public string YTD_TOTAL { get; set; }
        public string QTD_TOTAL { get; set; }
        public string MTD_TOTAL { get; set; }
        public string WTD_TOTAL { get; set; }
        public string TOTAL_PLEDGE { get; set; }
        public string TOTAL_INHOUSE { get; set; }
        public string TOTAL_FULFILLED { get; set; }
        public string TOTAL_OUTSTANDING { get; set; }

        public bool UP_FROM_PREVQTR { get; set; }
        public bool UP_FROM_PREVMONTH { get; set; }
        public bool UP_FROM_PREVWEEK { get; set; }
    }

}