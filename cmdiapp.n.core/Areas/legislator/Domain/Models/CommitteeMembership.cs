﻿using System.Collections.Generic;

namespace cmdiapp.n.core.Areas.legislator.Domain.Models
{
    /*
     Class for convenient API consumption of legislator's
     committee memberships, since it represents parent > child
     relationship of committee > subcommittee.
         */
    public class CommitteeMembership
    {
        public string committee { get; set; }
        public string committeeId { get; set; }
        public string party { get; set; }
        public int rank { get; set; }
        public string title { get; set; }
        public string url { get; set; }
        public List<SubcommitteeMembership> subcommittees;
    }
}