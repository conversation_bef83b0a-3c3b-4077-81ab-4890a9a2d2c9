﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Data;
using System.Linq;
using System.Web.Mvc;
using System.Data.Linq;
using System.ComponentModel.DataAnnotations.Schema;

using cmdiapp.n.core.Domain.ViewModels;
using cmdiapp.n.core.Areas.crm.Domain.Data;

namespace cmdiapp.n.core.Areas.crm.Domain.Models
{
    public class NewDistribution : _entityBase_crm, iItemType
    {
        public int MID { get; set; }

        public int PID { get; set; }

        public string TYPE { get; set; }

        public string FNAME { get; set; }

        public string MENAME { get; set; }

        public string LNAME { get; set; }

        public DateTime? BATCHDTE { get; set; }        

        public Decimal? AMT { get; set; }

        public string EXCEPCODE { get; set; }

        public string JFCCODE { get; set; }

        public string STREET { get; set; }

        public string CITY { get; set; }

        public string STATE { get; set; }

        public string ZIP { get; set; }
    }

    public class NewDistribution_ext1 : NewDistribution
    {
        public int count_ { get; set; }

        public Int64 rowNo { get; set; }
    }
     
}
