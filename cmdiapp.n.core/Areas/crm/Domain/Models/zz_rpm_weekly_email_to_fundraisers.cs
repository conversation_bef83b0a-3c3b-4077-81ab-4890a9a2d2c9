﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Data;
using System.Linq;
using System.Web.Mvc;
using System.Data.Linq;
using System.Xml.Linq;
using System.ComponentModel.DataAnnotations.Schema;

namespace cmdiapp.n.core.Areas.crm.Domain.Models
{
    [Table("zz_rpm_weekly_email_to_fundraisers")]
    public class zz_rpm_weekly_email_to_fundraisers
    {
        [Key]
        public int weeklyEmail_Id { get; set; }

        #region [ Email Data ]
        public string FromName { get; set; }
        public string FromAddress { get; set; }
        public string ToName { get; set; }
        public string ToAddress { get; set; }
        public string Subject { get; set; }
        public string Body { get; set; }
        #endregion

        #region [ Report Data ]
        public int trackno { get; set; }
        
        public string FNAME { get; set; }
        public string LNAME { get; set; }
        public string FULLNAME { get; set; }

        public decimal? cDonated { get; set; }
        public decimal? cRaisedNet { get; set; }
        public decimal? cRollup { get; set; }
        public decimal? commitment { get; set; }
        public int? no_donors { get; set; }
        public int? no_fundraisers { get; set; }
        public decimal? qtr_total { get; set; }
        public int? qtr_no_donations { get; set; }
        public decimal? week_total { get; set; }
        public int? week_no_donations { get; set; }
        public DateTime week_startDate { get; set; }
        public DateTime week_endDate { get; set; }

        public string week_dateRange
        {
            get
            {
                return string.Format("{0:MMM dd, yy} - {1:MMM dd, yy}", week_startDate, week_endDate);
            }
        }

        #region [ Stores a sub dataset of "Donations of this week" ]
        public string weeklyDonations { get; set; }
        public List<zz_rpm_weekly_email_to_fundraisers_donations> weeklyDonations_()
        {
            System.IO.TextReader tr = new System.IO.StringReader(weeklyDonations);
            XDocument oDoc = XDocument.Load(tr);

            var q = from a in oDoc.Descendants("d")
                    select new zz_rpm_weekly_email_to_fundraisers_donations
                    {
                        mid = (a.Element("i")!=null ? Convert.ToInt32(a.Element("i").Value) : (int?)null),
                        donorName = (a.Element("n")!=null ? Convert.ToString(a.Element("n").Value) : ""),
                        giftDate = (a.Element("t")!=null ? Convert.ToDateTime(a.Element("t").Value) : (DateTime?)null),
                        giftAmount = (a.Element("a")!=null ? Convert.ToDecimal(a.Element("a").Value) : (decimal?)null),
                        source = (a.Element("e")!=null ? Convert.ToString(a.Element("e").Value) : ""),
                    };
            return q.ToList();
        }
        #endregion

        public decimal? prevWeek_total { get; set; }
        public int? prevWeek_no_donations { get; set; }
        public string footer { get; set; }
        #endregion

        #region [ Stores a sub dataset of "Downline" ]
        public string downline { get; set; }
        public List<zz_rpm_weekly_email_to_fundraisers_downline> downline_()
        {
            System.IO.TextReader tr = new System.IO.StringReader(downline);
            XDocument oDoc = XDocument.Load(tr);

            var q = from a in oDoc.Descendants("d")
                    select new zz_rpm_weekly_email_to_fundraisers_downline
                    {
                        level = (a.Element("lvl") != null ? Convert.ToInt32(a.Element("lvl").Value) : (int?)null),
                        trackNo = (a.Element("tkn") != null ? Convert.ToInt32(a.Element("tkn").Value) : (int?)null),
                        pid = (a.Element("pid") != null ? Convert.ToInt32(a.Element("pid").Value) : (int?)null),
                        name = (a.Element("nam") != null ? Convert.ToString(a.Element("nam").Value) : ""),
                        recruiterNo = (a.Element("rtn") != null ? Convert.ToInt32(a.Element("rtn").Value) : (int?)null),
                        totalRaised = (a.Element("rai") != null ? Convert.ToDecimal(a.Element("rai").Value) : (decimal?)null),
                        totalRollup = (a.Element("rol") != null ? Convert.ToDecimal(a.Element("rol").Value) : (decimal?)null),
                        weeklyTotal = (a.Element("rge") != null ? Convert.ToDecimal(a.Element("rge").Value) : (decimal?)null),
                    };
            return q.ToList();
        }
        #endregion

        #region [ Processing Data ]
        public string attachmentFolder { get; set; }
        public DateTime? reportBaseDate { get; set; }
        public DateTime? reportData_as_of { get; set; }
        public bool? attachment_created { get; set; }
        public bool? email_queued { get; set; }
        #endregion

        #region [ misc. fields ]
        public string status_current { get; set; }
        public string status_next { get; set; }
        public string status_nextNote { get; set; }
        
        public string httpPath_reportImage1 { get; set; }
        public string httpPath_reportImage2 { get; set; }
        public string httpPath_reportImage3 { get; set; }
        #endregion
    }
    
    public class zz_rpm_weekly_email_to_fundraisers_donations
    {
        public int? mid { get; set; }
        public string donorName { get; set; }
        public DateTime? giftDate { get; set; }
        public decimal? giftAmount { get; set; }
        public string source { get; set; }
    }

    public class zz_rpm_weekly_email_to_fundraisers_downline
    {
        public int? level { get; set; }
        public int? trackNo { get; set; }
        public int? pid { get; set; }
        public string name { get; set; }

        public int? recruiterNo { get; set; }

        public decimal? totalRaised { get; set; }
        public decimal? totalRollup { get; set; }
        public decimal? weeklyTotal { get; set; }
    }
}