﻿using System.Collections.Generic;
using System.Web.Mvc;

using cmdiapp.n.core.Areas.crm.Domain.Models;
using cmdiapp.n.core.Areas.crm.Domain.ViewModels;
using cmdiapp.n.core.Areas.crm.Domain.Repositories;

namespace cmdiapp.n.core.Areas.crm.Domain.Services
{
    public interface IkeywordService
    {
        bool check_unqiueness(dmKWRD _dmKWRD);
        bool check_record_exists(int kwrdid);

        int get_kwrdId(dmKWRD _dmKWRD);

        void Add(dmKWRD _dmKWRD);
        void Update(dmKWRD _dmKWRD);
        void Delete(dmKWRD _dmKWRD);

        string MassDeleteKwrd(int FlagId, int UID, int maxAllowed);

        List<dmKWRD> read_all_keywords();
        dmKWRD get_keyword(int kwrdid);

        List<dmKWRD_ext> get_all_keywords(string where, string orderBy, int pageSize, int pageNo);
        int getKwrdID(string kwrdDesc);

    }
}