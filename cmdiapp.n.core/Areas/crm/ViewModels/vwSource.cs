﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using cmdiapp.n.core.Areas.crm.Domain.Models;

namespace cmdiapp.n.core.Domain.ViewModels
{
    public class vwSource:iItemType
    {
        public SOURCE SOURCE { get; set; }
        public ActivityTotalsFinal ActivityTotalsFinal { get; set; }
        public DonationTotalsFinal DonationTotalsFinal { get; set; }
    }

    public class vwSource_Ext:vwSource
    { 
        public string packageid { get; set; }
        public string selEvntId { get; set; }
    }

    public class vwSource_Apply2Others : vwSource
    {
        public string COSTPROD2isChecked{get;set;} 
        public string COSTPROD1isChecked{get;set;} 
        public string COSTPOSTGisChecked{get;set;}
        public string packageid { get; set; } 
    }

    public class source_lineCHARTdata:iItemType
    {
        public int _week { get; set; }
        public DateTime? _batchdte { get; set; }
        public string _date { get; set; }
        public string _date1 { get; set; }
        public int _donationCOUNT { get; set; }
        public decimal _donationDOLLAR { get; set; }
        public string _donationCOUNT1 { get; set; }
        public string _donationDOLLAR1 { get; set; }
    }

    public class _pieCHARTdata:iItemType
    {
        public Int16 _activityGROUPid { get; set; }
        public string _activityGROUP { get; set; }
        public int _activityCODEid { get; set; }
        public string _activityCODE { get; set; }
        public int _activityYvalue { get; set; }
        public string _activityXlabel { get; set; }
    }
    public class _interactionLineCHARTdata:iItemType
    {
        public DateTime _date { get; set; }
        public string _monthYear { get; set; }
        public int _signUpCount { get; set; }
        public int _uniqActivityCount { get; set; }
    }
}