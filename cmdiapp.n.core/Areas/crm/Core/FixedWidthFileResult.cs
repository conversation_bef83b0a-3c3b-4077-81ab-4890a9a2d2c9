﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;
using System.Web;
using System.Web.Mvc;

namespace cmdiapp.n.core.Areas.crm.Core
{
    public class FixedWidthFileResult : ActionResult
    {
        public DataSet Dataset { get; set; }
        public string FileName { get; set; }
        // comma delimited string of ints (e.g., "10,80,90")
        public string FieldWidthsString { get; set; }
        public bool IncludeHeader { get; set; }

        public FixedWidthFileResult(DataSet dataset, string fileName, string fieldWidthsString, bool includeHeader=false)
        {
            Dataset = dataset;
            FileName = Regex.IsMatch(fileName, @"\.\w+$") ? fileName : fileName + ".txt";
            FieldWidthsString = fieldWidthsString;
            IncludeHeader = includeHeader;
        }

        public override void ExecuteResult(ControllerContext context)
        {
            HttpResponseBase response = context.HttpContext.Response;
            string fileString;
            try
            {
                fileString = GetFileString(Dataset, FieldWidthsString, IncludeHeader);
            }
            catch (Exception ex)
            {
                fileString = ex.Message;
            }
            response.Clear();
            response.Buffer = true;
            response.ContentType = "text/plain";
            response.AddHeader("content-disposition", $"attachment;  filename={FileName}");
            response.BinaryWrite(Encoding.ASCII.GetBytes(fileString));
            response.End();
        }

        private string GetFileString(DataSet dataset, string widthString, bool includeHeader)
        {
            DataTable table = dataset.Tables[0];
            string[] widthStrings = widthString.Split(',');
            // parse strings to ints
            List<int> widths = new List<int>();
            foreach (string widthStr in widthStrings)
            {
                int width;
                if (int.TryParse(widthStr.Trim(), out width))
                {
                    if (width < 0)
                    {
                        throw new Exception("Field length cannot be less than 0.");
                    }
                    else
                    {
                        widths.Add(width);
                    }
                }
                else
                {
                    throw new FormatException($"Length string \"{widthStr}\" could not be parsed.");
                }
            }
            // check to make sure we don't have more columns than widthss
            if (table.Columns.Count > widths.Count)
            {
                throw new Exception($"Got {table.Columns.Count} DataColumns but only {widths.Count} field lengths.");
            }
            StringBuilder sb = new StringBuilder();
            // add header if specified
            if (includeHeader)
            {
                foreach (DataColumn col in table.Columns)
                {
                    sb.Append(FixWidth(col.ColumnName?.ToString() ?? "", widths[col.Ordinal]));
                }
                sb.Append(Environment.NewLine);
            }
            // add data
            foreach (DataRow row in table.Rows)
            {
                foreach (DataColumn col in table.Columns)
                {
                    string valString = row[col.ColumnName]?.ToString() ?? "";
                    sb.Append(FixWidth(valString, widths[col.Ordinal]));
                }

                sb.Append(Environment.NewLine);
            }

            return sb.ToString();
        }

        private static string FixWidth(string str, int width)
        {
            string result = str;
            if (result.Length >= width)
            {
                result = result.Substring(0, width);
            }
            else
            {
                result = result.PadRight(width);
            }

            return result;
        }
    }
}