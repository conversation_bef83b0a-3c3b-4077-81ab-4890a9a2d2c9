﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

using Ninject;
using Ninject.Web.Mvc;

using cmdiapp.n.core.Core.App_Start;
using cmdiapp.n.core.Areas.crm.Domain.Models;
using cmdiapp.n.core.Areas.crm.Domain.ViewModels;
using cmdiapp.n.core.Areas.crm.Domain.Repositories;

namespace cmdiapp.n.core.Areas.crm.Domain.Services
{
    public class clubService : IclubService
    {
        private I___unitOfWork _unitOfWork;
        private I__DBFactory _dbFactory;
        private IclubRepository _clubRepository;

        public clubService(I___unitOfWork unitOfWork, I__DBFactory dbFactory, IclubRepository ClubRepository)
        {
            _unitOfWork = unitOfWork;
            _dbFactory = dbFactory;
            _clubRepository = ClubRepository;
        }

        public pmCLUB get_club(int clubId)
        {
            string sql = string.Format("SELECT * FROM pmCLUB WHERE CLUBID={0}", clubId);
            var q = _dbFactory.getContext().Database.SqlQuery<pmCLUB>(sql);
            return q.FirstOrDefault();
        }

        public bool check_unqiueness(pmCLUB _pmCLUB)
        {
            // take care of apostrophe
            string v = _pmCLUB.CLUBCODE.Replace("'", "''");
            string sql = "";

            if (_pmCLUB.CLUBID > 0)   // Existing Record
                sql = String.Format("SELECT Count(*) as recordCount FROM pmCLUB WHERE CLUBCODE = '{0}' AND CLUBID <> {1}", v, _pmCLUB.CLUBID);
            else
                sql = String.Format("SELECT Count(*) as recordCount FROM pmCLUB WHERE CLUBCODE = '{0}'", v);

            var q = _dbFactory.getContext().Database.SqlQuery<UniquenessCheck>(sql);
            //return q.ToList().Count == 0 ? true : false;
            return q.FirstOrDefault().recordCount == 0 ? true : false;
        }

        public bool check_record_exists(int clubId)
        {
            //For jtCLUB
            string sql = string.Format("SELECT Count(*) as recordCount FROM jtCLUB WHERE CLUBID='{0}'", clubId);
            var q = _dbFactory.getContext().Database.SqlQuery<UniquenessCheck>(sql);
            int isZero = q.FirstOrDefault().recordCount;
            

            //For LKCLUBSTAT
            string sql1 = string.Format("SELECT Count(*) as recordCount FROM LKCLUBSTAT WHERE CLUBID='{0}'", clubId);
            var q1 = _dbFactory.getContext().Database.SqlQuery<UniquenessCheck>(sql1);
            int isZero1 = q1.FirstOrDefault().recordCount;

            return (isZero > 0 || isZero1 > 0) ? true : false;
        }
        
        public void Add(pmCLUB _pmCLUB)
        {
            _clubRepository.Add(_pmCLUB);
            _unitOfWork.commit();
        }

        public void Update(pmCLUB _pmCLUB)
        {
            _clubRepository.Update(_pmCLUB);
            _unitOfWork.commit();
        }

        public void Delete(pmCLUB _pmCLUB)
        {
            _clubRepository.Delete(_pmCLUB);
            _unitOfWork.commit();
        }

        //Read all Clubs
        public List<pmCLUB> read_all_clubs()
        {
            return _clubRepository.All().OrderBy(x => x.CLUBID).ToList();
        }

        public List<pmCLUB_ext> get_all_clubs(string where, string orderBy, int pageSize, int pageNo)
        {
            string sql = (!string.IsNullOrEmpty(where) ? string.Format("SELECT * FROM pmCLUB WHERE {0}", where) : "SELECT * FROM pmCLUB");

            var q = _dbFactory.getContext().Database.SqlQuery<pmCLUB_ext>(String.Format("EXEC dbo.get_pagedDataSet '{0}', '{1}', {2}, {3}", sql, orderBy, pageSize, pageNo));

            return q.ToList();
        }
    }
}