﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Data;
using System.Linq;
using System.Web.Mvc;
using System.Data.Linq;
using System.ComponentModel.DataAnnotations.Schema;

using cmdiapp.n.core.Domain.ViewModels;
using cmdiapp.n.core.Areas.crm.Domain.Data;

namespace cmdiapp.n.core.Areas.crm.Domain.Models
{
    [Table("BudgetAlloc")]
    public class BudgetAlloc : _entityBase_crm, iItemType
    {
        [Column]
        [Key]
        public int budgetAllocId { get; set; }
        [Column]
        public int ENTITYID { get; set; }
        [Column]
        public DateTime expectedDate { get; set; }
        [Column]
        public decimal amount { get; set; }
        [Column]
        public DateTime? createdAt { get; set; }
        [Column]
        public Int16? createdBy { get; set; }

    }
    [NotMapped]
    public class budgetAlloc_ext: BudgetAlloc
    {
        public int count_ { get; set; }

        public Int64 rowNo { get; set; }

        public string payee { get; set; }

        public decimal YTDContribution { get; set; }

        public string createdByName { get; set; }
    }

    public class Txn_forbudget : iItemType
    {
        public int count_ { get; set; }

        public Int64 rowNo { get; set; }

        public int TXNID { get; set; }

        public int? ENTITYID { get; set; }

        public string payee { get; set; }

        public decimal? AMT { get; set; }

        public DateTime? TXNDTE { get; set; }

        public string ELECCODE { get; set; }

        public string ELECTYR { get; set; }

        public string ELECTOTH { get; set; }
    }

    [Table("PersonalContrib")]
    public class PersonalContrib : _entityBase_crm, iItemType
    {
        [Column]
        [Key]
        public int personalContribId { get; set; }
        [Column]
        public int personalContribTypeId { get; set; }
        [Column]
        public int PID { get; set; }
        [Column]
        public string honoraryName { get; set; }
        [Column]
        public DateTime contribDate { get; set; }
        [Column]
        public decimal amount { get; set; }
        [Column]
        public int? SPCEVNTID { get; set; }
        [Column]
        public int? ENTITYID { get; set; }

    }
    [NotMapped]
    public class PersonalContrib_ext : PersonalContrib
    {
        public int count_ { get; set; }

        public Int64 rowNo { get; set; }

        public string payee { get; set; }

        public string personalContribType { get; set; }

        public string EVNTCODE { get; set; }

    }

    [Table("lkPersonalContribType")]
    public class lkPersonalContribType : _entityBase_crm, iItemType
    {
        [Column]
        [Key]
        public int personalContribTypeId { get; set; }
        [Column]
        public string personalContribType { get; set; }
        [Column]
        public bool? isDefault { get; set; }
        [Column]
        public bool? isSystem { get; set; }

    }

    [NotMapped]
    public class undesignatedContributions : iItemType
    {
        public int count_ { get; set; }
        public Int64 rowNo { get; set; }
        public int TXNID { get; set; }
        public string VendorType { get; set; }
        public string VendorName { get; set; }
        public DateTime? Date { get; set; }
        public decimal Amount { get; set; }
        public string FUNDCODE { get; set; }
    }
    [NotMapped]
    public class PACVendorsMissingFECID : iItemType
    {
        public int count_ { get; set; }
        public Int64 rowNo { get; set; }
        public int VendorID { get; set; }
        public string VendorType { get; set; }
        public string VendorName { get; set; }
        public decimal? Exp_YTD { get; set; }
        public decimal? Exp_CTD { get; set; }
    }

    [NotMapped]
    public class unspentBudget : iItemType
    {
        public int ENTITYID { get; set; }
        public string Name { get; set; }
        public decimal? TotalBudget { get; set; }
        public decimal? YTDContribution { get; set; }
        public decimal? UnspentBudget { get; set; }
        public int count_ { get; set; }
    }  
}