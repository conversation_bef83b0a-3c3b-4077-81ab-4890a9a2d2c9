﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Data;
using System.Linq;
using System.Web.Mvc;
using System.Data.Linq;
using System.ComponentModel.DataAnnotations.Schema;

using cmdiapp.n.core.Domain.ViewModels;
using cmdiapp.n.core.Areas.crm.Domain.Data;

namespace cmdiapp.n.core.Areas.crm.Domain.Models
{
    [Table("PEOPLE")]
    public class people : _entityBase_crm, iItemType
    {
	    [Column] 
        [Key]
	    public int PID { get; set; }
	
	    [Column] 
	    public string CHKDGT { get; set; }
	
        [Column] 
        public bool ACTIVE { get; set; }
        
	    [Column] 
        public Int16 PEOTYPEID { get; set; }
        
        [Column]
        public Int16 PEOCODEID { get; set; }

        [Column] 
        public string ACCTNO { get; set; }

        [Column] 
        public string TITLE { get; set; }

        [Column] 
        public string PREFIX { get; set; }  // 40

        [Column] 
        public string FNAME { get; set; }   // 30

        [Column] 
        public string MNAME { get; set; }   // 20

        [Column] 
        public string LNAME { get; set; }   // 80

        [Column]
        public string SUFFIX { get; set; }  // 20

        [Column] 
        public string SALUTATION { get; set; }  // 70

        [Column] 
        public string INFSALUT { get; set; }    // 50

        [Column] 
        public string PRESSALUT { get; set; }   // 75

        [Column] 
        public string SPOUSENAME { get; set; }  // 50

        [Column] 
        public DateTime? DOB  { get; set; } 

        [Column] 
        public string GENDER { get; set; } // 1

        [Column] 
        public DateTime? SPECDATE { get; set; }

        [Column] 
        public string ETHNICITY { get; set; }   // 30

        [Column] 
        public string RELIGION { get; set; }    // 30

        [Column] 
        public string EMPLOYER { get; set; }    // 70

        [Column] 
        public string OCCUPATION { get; set; }   // 50

        [Column] 
        public string FECCMTEID { get; set; }  // 9

        [Column] 
        public int? TrackNo { get; set; }

        [Column] 
        public string cPREFIX { get; set; } // 40

        [Column] 
        public string cFNAME { get; set; } // 30

        [Column] 
        public string cMNAME { get; set; }  // 20

        [Column] 
        public string cLNAME { get; set; } // 60

        [Column] 
        public string cSUFFIX { get; set; }    // 20
    
        [Column] 
        public string cTITLE { get; set; } // 75

        [Column] 
        public string COMMENT { get; set; }    // Memo

        [Column] 
        public DateTime? LASTACTON { get; set; }   

        [Column] 
        public int? LASTACTUID { get; set; }

        [Column] 
        public DateTime? UPDATEDON { get; set; }

        [Column] 
        public Int16? TAG { get; set; }   

        [Column] 
        public Int16? PEOCLASSID { get; set; }

        [Column] 
        public string ASSISTANT { get; set; }  // 50

        [Column] 
        public byte[] PICTURE { get; set; }

        [Column] 
        public string BIO { get; set; }    // text

        [Column] 
        public Int16? UID{ get; set; }

        [Column] 
        public int? NUM1 { get; set; }

        [Column] 
        public decimal? MONY1 { get; set; }

        [Column] 
        public decimal? MONY2 { get; set; }

        [Column] 
        public Int16? CHAPCODEID { get; set; }

        [Column] 
        public DateTime? DOD { get; set; }

        [Column]
        public string MAILNAME { get; set; }  // 100

        [Column]
        public string STR1 { get; set; }

        [Column]
        public string STR2 { get; set; }

        [Column("_updating_uid")]
        public Nullable<short> updating_uid { get; set; }

        #region [ Exists but NOT implemented ]
        /*
        [Column] 
        public string SSN { get; set; } // 9
        */
        #endregion
    }

    public class peoplePicture
    {
        public int PID { get; set; }
        public byte[] PICTURE { get; set; }
    }

    public class PopOverInfo
    {
        public string name { get; set; }
        public int pk { get; set; }
        public int value { get; set; }
    }
   
    public class lkPEOTYPEForDataEntry
    {
        public Int16 PEOTYPEID { get; set; }

        public string PEOTYPE { get; set; }

        public string DESCRIP { get; set; }

    }


    public class lkPEOCODEForDataEntry
    {
        public Int16 PEOCODEID { get; set; }

        public string PEOCODE { get; set; }

        public string DESCRIP { get; set; }

    }

    public class peopleTimeLine : _entityBase_crm, iItemType
    {
        public int pid { get; set; }
        public DateTime? date { get; set; }
        public string type { get; set; }
        public int id { get; set; }
        public string code { get; set; }
        public decimal? amt { get; set; }
        public string detail { get; set; }
    }

    public class peopleTimeLine_ext: peopleTimeLine
    {
        public int count_ { get; set; }
        public Int64 rowNo { get; set; }
    }
}
