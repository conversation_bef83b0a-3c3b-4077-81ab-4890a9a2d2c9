﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace cmdiapp.n.core.Domain.ViewModels
{
    public class rBatch : iItemType
    {
        public int BATCHID { get; set; }
        public string BATCHNO { get; set; }
        public DateTime? BATCHDTE { get; set; }
        public Int16 BATTYPEID { get; set; }
        public string BATTYPEDESC { get; set; }
        public Int16 FUNDID { get; set; }
        public string FUNDDESC { get; set; }
        public Int16 DEFCENTER { get; set; }
        public int DEFSRCEID { get; set; }
        public int BATCHCNT { get; set; }
        public decimal BATCHAMT { get; set; }
        public int cENTRYCNT { get; set; }
        public decimal cENTRYAMT { get; set; }
        public Byte cBALANCED { get; set; }
        public int sNOMONY { get; set; }
        public decimal sAMTMONY { get; set; }
        public int cLOADCNT { get; set; }
        public decimal cLOADAMT { get; set; }
        public Byte LOADED { get; set; }
        public DateTime? cLOADDTE { get; set; }
        public int TOTALCOUNT { get; set; }
        public int count_ { get; set; }
        public Int64 rowNo { get; set; }
    }

    public class rpeopleMatchingRecords : iItemType
    {
        public int PID { get; set; }
        public string PEOCODE { get; set; }
        public string PEOTYPE { get; set; }
        public string PREFIX { get; set; }
        public string NAME { get; set; }
        public int ADDRESSID { get; set; }
        public string ADDRTYPE { get; set; }
        public string STREET { get; set; }
        public string CITY { get; set; }
        public string STATE { get; set; }
        public string ZIP { get; set; }
        public string KEYLINE3 { get; set; }
        public Int16 PEOCODEID { get; set; }
        public int count_ { get; set; }
        public Int64 rowNo { get; set; }
    }

    public class rbatchKeyedRecords : iItemType
    {
        public int BATCHID { get; set; }
        public int DTBATCHID { get; set; }
        public int PID { get; set; }
        public string NAME { get; set; }
        public string STREET { get; set; }
        public string CITY { get; set; }
        public string STATE { get; set; }
        public string ZIP { get; set; }
        public string SRCECODE { get; set; }
        public Decimal AMT { get; set; }
        public string CCRESPMSG { get; set; }
        public DateTime ENTRYDTE { get; set; }
        public string ENTEREDBY { get; set; }
        public int count_ { get; set; }
        public Int64 rowNo { get; set; }
        public int MID { get; set; }
        public string ADJ { get; set; }

        public int? OdtBATCHID { get; set; }
        public Boolean? HASSPLIT { get; set; }
        public Boolean? REATTRIBUTE { get; set; }
        public Boolean? PARTIAL_REDESIGNATION { get; set; }
        public int? ADJCNT { get; set; }
        //this controls if remove Adjustment button will be shown or not in the UI...
        public Boolean HideOrShow
        {
            get
            {
                if (ADJCNT > 0)
                    return false;
                else if (ADJCNT == 0 && (HASSPLIT == true || REATTRIBUTE == true || PARTIAL_REDESIGNATION == true))
                    return true;
                else
                    return false;
            }
        }

    }

    public class rPeopleFundDetails : iItemType
    {
        public string Fund { get; set; }
        public Decimal Total { get; set; }
        public Decimal? Remaining { get; set; }
        public string CUMTOTS { get; set; }
        public string REMAINS { get; set; }
        public int count_ { get; set; }
        public Int64 rowNo { get; set; }
    }

}