﻿namespace cmdiapp.n.core.Areas.dashboard.Domain.Services
{
    /// <summary>
    /// Provides methods for checking for customizations of dashboards
    /// according to current environment (e.g., app version, project, user, etc.)
    /// </summary>
    public interface IDashboardEnvironmentService
    {
        /// <summary>
        /// Checks if a different dashboard id should be used for this environment and returns it,
        /// otherwise it returns the given default id.
        /// </summary>
        /// <param name="defaultId">The default dashboard id requested by client</param>
        /// <returns>The dashboard id to use for this environment</returns>
        string GetDashboardIdForEnvironment(string defaultId);
    }
}
