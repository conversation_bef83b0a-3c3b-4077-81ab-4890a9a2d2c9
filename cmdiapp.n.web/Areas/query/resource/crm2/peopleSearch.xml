<qDef name="peopleSearch" caption="People Search">

  <aElementIdentifier>cmdiapp.dms.people_qq</aElementIdentifier>
  <sq_uniqId>[P].PID</sq_uniqId>
  <def_export>xlsx</def_export>
  <runDefault>false</runDefault>
  <analysisDashboardId>peopleSearch</analysisDashboardId>
  <!--<saving>2</saving> 0(No) | 1(RecentOnly) | 2(Recent+Save)-->

  <!--||||| Action Buttons ||||||||||||||||||||||||||||||||||||||||||||||||||-->

  <qab>
    <ab caption="Add" position="header" accessLevel="a">
      <a>Add</a>
    </ab>
    <ab type="result" caption="Export" accessLevel="e">
      <a>export</a>
    </ab>
    <ab type="result" caption="Export by Channel" position="footer" accessLevel="e">
      <!-- position = header|footer(default if not specified) -->
      <a>exportByChannel</a>
    </ab>
    <ab type="result"
        caption="Mass Append"
        accessElement="cmdiapp.dms.people_qq.mass_apend"
        accessLevel="e">
      <a>mass_append</a>
    </ab>
    <ab type="result" caption="Print" accessLevel="e">
      <a>print</a>
    </ab>
    <ab type="result" caption="Print Summary" accessLevel="e">
      <a>print_summary</a>
    </ab>
    <ab type="result" caption="Map It" accessLevel="v">
      <a>mapIt</a>
    </ab>
    <ab type="result" caption="Mail Merge" accessLevel="e">
      <a>mailMerge</a>
    </ab>
    <ab type="result"
        caption="Create Email List"
        accessElement="CreateEmailList"
        accessLevel="e">
      <a>emailList</a>
    </ab>
    <ab type="result" caption="Profile Series" accessLevel="v">
      <a>profileSeries</a>
    </ab>
  </qab>

  <!--||||| Table Columns |||||||||||||||||||||||||||||||||||||||||||||||||||-->

  <qtc>
    <c key="PID" label="ID" format="s">
      <f>[P].PID</f>
    </c>
    <c key="PREFIX" label="Prefix" format="s">
      <f>[P].PREFIX</f>
    </c>
    <c key="FNAME" label="First" format="s">
      <f>[P].FNAME</f>
    </c>
    <c key="MNAME" label="Middle" format="s">
      <f>[P].MNAME</f>
    </c>
    <c key="LNAME" label="Last" format="s">
      <f>[P].LNAME</f>
    </c>
    <c key="SUFFIX" label="Suffix" format="s">
      <f>[P].SUFFIX</f>
    </c>
    <c key="STREET" label="Street" format="s">
      <f>[OA].STREET</f>
    </c>
    <c key="CITY" label="City" format="s">
      <f>[OA].CITY</f>
    </c>
    <c key="STATE" label="State" format="s">
      <f>[OA].STATE</f>
    </c>
    <c key="ZIP" label="Zip" format="s">
      <f>[OA].ZIP</f>
    </c>
    <c key="HPHONE" label="Home#" format="s">
      <f>dbo.oPHONEwMASK([PH].PHNNO) AS HPHONE</f>
    </c>
    <c key="HPHONEVerified" label="Home#Verified" format="s">
      <f>CASE WHEN [PH].VERIFIEDAT IS NULL THEN 'N' ELSE CONVERT(VARCHAR,[PH].VERIFIEDAT,101) END AS HPHONEVerified</f>
    </c>
    <c key="WPHONE" label="Work#" format="s">
      <f>dbo.oPHONEwMASK([PW].PHNNO) AS WPHONE</f>
    </c>
    <c key="WPHONEVerified" label="Work#Verified" format="s">
      <f>CASE WHEN [PW].VERIFIEDAT IS NULL THEN 'N' ELSE CONVERT(VARCHAR,[PW].VERIFIEDAT,101) END AS WPHONEVerified</f>
    </c>
    <c key="EMAIL" label="Email" format="s">
      <f>[PE].PHNNO AS EMAIL</f>
    </c>
    <c key="EMAILVerified" label="Email#Verified" format="s">
      <f>CASE WHEN [PE].VERIFIEDAT IS NULL THEN 'N' ELSE CONVERT(VARCHAR,[PE].VERIFIEDAT,101) END AS EMAILVerified</f>
    </c>
    <c key="EMPLOYER" label="Employer" format="s">
      <f>[P].EMPLOYER</f>
    </c>
    <c key="OCCUPATION" label="Occupation" format="s">
      <f>[P].OCCUPATION</f>
    </c>
    <c key="SPOUSENAME" label="Spouse" format="s">
      <f>[P].SPOUSENAME</f>
    </c>
    <c key="LGIFT" label="MRC" format="c">
      <f>[SD].LGIFT</f>
    </c>
    <c key="LGIFTDTE" label="MRC Date" format="d">
      <f>[SD].LGIFTDTE</f>
    </c>
    <c key="FGIFT" label="First Gift" format="c">
      <f>[SD].FGIFT</f>
    </c>
    <c key="FGIFTDTE" label="First Gift Date" format="d">
      <f>[SD].FGIFTDTE</f>
    </c>
    <c key="HPC" label="Highest $" format="c">
      <f>[SD].HPC</f>
    </c>
    <c key="HPCDTE" label="Highest $ Date" format="d">
      <f>[SD].HPCDTE</f>
    </c>
    <c key="NOGIFTS" label="#Gifts" format="n">
      <f>[SD].NOGIFTS</f>
    </c>
    <c key="CUMTOT" label="Total($)" format="c">
      <f>[SD].CUMTOT</f>
    </c>
    <c key="AVERAGEGIFT" label="Average Gift Amount" format="c">
      <f>CASE WHEN ISNULL([SD].CUMTOT,0)=0 OR ISNULL([SD].NOGIFTS,0)=0 THEN NULL ELSE   [SD].CUMTOT/[SD].NOGIFTS END AS AVERAGEGIFT</f>
    </c>
    <c key="CTDAMT" label="CTD $" format="c">
      <f>[SD].CTDAMT</f>
      <ch>v|Federal,State|Y|</ch>
    </c>
    <c key="CTDAMT" label="FTD $" format="c">
      <f>[SD].CTDAMT</f>
      <ch>v|Nonprofit|Y|</ch>
    </c>    
    <c key="hiddenId" label="" format="n" hidden="true">
      <f>0 AS hiddenId</f>
    </c>
  </qtc>

  <!--||||| Table Buttons |||||||||||||||||||||||||||||||||||||||||||||||||||-->

  <qtb>
    <tb caption="Edit">
      <a>edit</a>
      <k>PID</k>
      <g>
        <i k="isOld" v="false" />
        <i k="componentKey" v="people"/>
        <i k="componentName" v="People Profile"/>
      </g>
    </tb>
  </qtb>

  <!--||||| FROM ||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||-->

  <qf>

    <!--||||| PEOPLE ||||||||||||||||||||||||||||||||||||||-->
    <w f="[P].ACTIVE"><![CDATA[[P].ACTIVE=1]]></w>

    <f>
      <c>true</c>
      <t>People [P] WITH (NOLOCK)</t>
    </f>

    <!--||||| Record Code/Type ||||||||||||||||||||||||||||-->
    <f>
      <c>false</c>
      <d>[P]</d>
      <t>
        <![CDATA[
          OUTER APPLY (SELECT * FROM lkPEOCLASS [PEOCLASS] WHERE P.PEOCLASSID=PEOCLASS.PEOCLASSID) [PEOCLASS] 
          OUTER APPLY (SELECT * FROM lkPEOTYPE [PEOTYPE] WHERE P.PEOTYPEID=PEOTYPE.PEOTYPEID) [PEOTYPE] 
          OUTER APPLY (SELECT * FROM lkPEOCODE [PEOCODE] WHERE P.PEOCODEID=PEOCODE.PEOCODEID) [PEOCODE] 
      ]]>
      </t>
    </f>

    <!--||||| Address for Filtering ||||||||||||||||||||||||-->
    <f>
      <c>false</c>
      <d>[P]</d>
      <t>LEFT OUTER JOIN ADDRESS [A] ON  P.PID=A.PID</t>
      <w f="[A].PRIME">A.PRIME=1</w>
      <!-- (Default Condition) Insert into the JOIN condition if not used in filtering/output -->
    </f>
    <f>
      <c>false</c>
      <d>[P],[A]</d>
      <t>
        <![CDATA[
          OUTER APPLY (SELECT * FROM lkCOUNTY [KC] WHERE KC.STATE=A.STATE AND KC.FIPS=A.COUNTY) [KC] 
          OUTER APPLY (SELECT * FROM lkMetroStatArea [KMSA] WHERE KC.CSACode = KMSA.CSACode) [KMSA]
          OUTER APPLY (SELECT * FROM lkSTATE [RS] WHERE RS.[STATE]=A.STATE) [RS]
          OUTER APPLY (SELECT * FROM lkZip [RZ] WHERE RZ.ZIP=A.ZIP) [RZ] 
        ]]>
      </t>
    </f>

    <!--||||| Address for Export |||||||||||||||||||||||||||-->
    <f>
      <c>false</c>
      <d>[P]</d>
      <t>LEFT OUTER JOIN ADDRESS [OA] ON P.PID=OA.PID</t>
      <w f="[AllAddresses]">OA.PRIME=1</w>
    </f>
    <f>
      <c>false</c>
      <d>[P],[OA]</d>
      <t>
        <![CDATA[
          OUTER APPLY (SELECT * FROM lkADDRTYPE [OAAT] WHERE OAAT.ADDRTYPEID=OA.ADDRTYPEID) [OAAT]
          OUTER APPLY (SELECT * FROM lkCOUNTY [OAKC] WHERE OAKC.STATE=OA.STATE AND OAKC.FIPS=OA.COUNTY) [OAKC]
          OUTER APPLY (SELECT * FROM lkMetroStatArea [OAKMSA] WHERE OAKC.CSACode = OAKMSA.CSACode) [OAKMSA]
          OUTER APPLY (SELECT * FROM lkSTATE [OARS] WHERE OARS.[STATE]=OA.STATE) [OARS]
          OUTER APPLY (SELECT * FROM lkZip [OARZ] WHERE OARZ.ZIP=OA.ZIP) [OARZ]
          OUTER APPLY (SELECT * FROM lkCOUNTRY [OACTRY] WHERE OACTRY.COUNTRYID=OA.COUNTRYID) [OACTRY]
        ]]>
      </t>
    </f>

    <!--||||| Phones & Email |||||||||||||||||||||||||||||||-->
    <f>
      <c>false</c>
      <d>[P]</d>
      <t>
        <![CDATA[
            OUTER APPLY (SELECT * FROM PHONE [PH] WHERE P.PID=PH.PID AND PH.PHNTYPEID=1 AND PH.PRIME=1) [PH]
            OUTER APPLY (SELECT * FROM PHONE [PW] WHERE P.PID=PW.PID AND PW.PHNTYPEID=2 AND PW.PRIME=1) [PW] 
            OUTER APPLY (SELECT * FROM PHONE [PC] WHERE P.PID=PC.PID AND PC.PHNTYPEID=3 AND PC.PRIME=1) [PC] 
            OUTER APPLY (SELECT * FROM PHONE [PF] WHERE P.PID=PF.PID AND PF.PHNTYPEID=5 AND PF.PRIME=1) [PF] 
            OUTER APPLY (SELECT * FROM PHONE [PE] WHERE P.PID=PE.PID AND PE.PHNTYPEID=4 AND PE.PRIME=1) [PE]
      ]]>
      </t>
    </f>
    
    <!--||||| Socal Media for Export |||||||||||||||||||||||||||-->
    <f>
      <c>false</c>
      <d>[P]</d>
      <t>
        <![CDATA[
          OUTER APPLY (SELECT * FROM PHONE [UR] WHERE P.PID=UR.PID AND UR.PHNTYPEID=6 AND UR.PRIME=1) [UR]
          OUTER APPLY (SELECT * FROM PHONE [FB] WHERE P.PID=FB.PID AND FB.PHNTYPEID=21 AND FB.PRIME=1) [FB]
          OUTER APPLY (SELECT * FROM PHONE [TW] WHERE P.PID=TW.PID AND TW.PHNTYPEID=22 AND TW.PRIME=1) [TW]
          OUTER APPLY (SELECT * FROM PHONE [LI] WHERE P.PID=LI.PID AND LI.PHNTYPEID=23 AND LI.PRIME=1) [LI]
      ]]>
      </t>
    </f>

    <!--||||| Any Phone (For Filtering) |||||||||||||||||||||||||||||||-->
    <f>
      <c>false</c>
      <d>[P]</d>
      <t>
        <![CDATA[
            OUTER APPLY (SELECT * FROM PHONE [APH] WHERE P.PID=APH.PID AND APH.PHNTYPEID IN (1,2,3,11,13)) [APH]
      ]]>
      </t>
    </f>

    <!--||||| Assitant Contact Info ||||||||||||||||||||||||-->
    <f>
      <c>false</c>
      <d>[P]</d>
      <t>
        <![CDATA[
            OUTER APPLY (SELECT * FROM PHONE [ASTP] WHERE P.PID=ASTP.PID AND ASTP.PHNTYPEID=11 AND ASTP.PRIME=1) [ASTP] 
            OUTER APPLY (SELECT * FROM PHONE [ASTE] WHERE P.PID=ASTE.PID AND ASTE.PHNTYPEID=12 AND ASTE.PRIME=1) [ASTE] 
            OUTER APPLY (SELECT * FROM PHONE [DIRP] WHERE P.PID=DIRP.PID AND DIRP.PHNTYPEID=13 AND DIRP.PRIME=1) [DIRP] 
      ]]>
      </t>
    </f>

    <!--||||| Contacts for Filtering |||||||||||||||||||||||-->
    <f>
      <c>false</c>
      <d>[P]</d>
      <t>OUTER APPLY (SELECT * FROM CONTACT [CNT] WHERE P.PID=CNT.PID AND CNT.INACTIVE=0) [CNT]</t>
    </f>

    <!--||||| Contacts for Export ||||||||||||||||||||||||||-->
    <f>
      <c>false</c>
      <d>[P]</d>
      <t>LEFT OUTER JOIN CONTACT [OCNT] ON P.PID=OCNT.PID AND OCNT.INACTIVE=0</t>
      <w f="[AllContacts]">OCNT.PRIME=1</w>
    </f>

    <!--||||| Contact Flag for Filtering ||||||||||||||||||||||||||||||-->
    <f>
      <c>false</c>
      <d>[P]</d>
      <t>
        OUTER APPLY (SELECT CNT.PID, JCF.CONTACTID, DCF.CONTFLAG FROM CONTACT [CNT] INNER JOIN jtCONTFLAG [JCF] ON CNT.CONTACTID = JCF.CONTACTID INNER JOIN lkCONTFLAG [DCF] ON JCF.CONTFLAGID = DCF.CONTFLAGID WHERE P.PID = CNT.PID AND CNT.INACTIVE=0) [DCF]
      </t>
    </f>

    <!--||||| Giving Summary |||||||||||||||||||||||||||||||-->
    <f>
      <c>false</c>
      <d>[P]</d>
      <t>
        <![CDATA[
            OUTER APPLY (SELECT * FROM SUMMARYD [SD] WHERE P.PID=SD.PID) [SD]       
        ]]>
      </t>
    </f>

    <!--||||| Gift |||||||||||||||||||||||||||||||||||||||||-->
    <f>
      <c>false</c>
      <d>[P]</d>
      <t>
        <![CDATA[
          OUTER APPLY (SELECT * FROM MONY [M] WHERE P.PID=M.PID AND M.COUNTER=1) [M] 
          OUTER APPLY (SELECT * FROM dmFUND [F] WHERE M.FUNDID=F.FUNDID) [F]
          OUTER APPLY (SELECT * FROM SOURCE [S] WHERE M.SRCEID=S.SRCEID) [S]
          OUTER APPLY (SELECT * FROM PACKAGE [K] WHERE S.PKGEID=K.PKGEID) [K]
          OUTER APPLY (SELECT * FROM PROGRAM [G] WHERE K.PROGID=G.PROGID) [G]
          OUTER APPLY (SELECT * FROM lkMONYTYPE [KM] WHERE M.MONYTYPEID=KM.MONYTYPEID) [KM]
          OUTER APPLY (SELECT * FROM lkEXCEP [KE] WHERE M.EXCEPID=KE.EXCEPID) [KE]
          OUTER APPLY (SELECT * FROM dmCENTER [CE] WHERE M.CENTERID =CE.CENTERID) [CE]
          OUTER APPLY (SELECT * FROM dmCAMPGN [CP] WHERE M.CAMPGNID =CP.CAMPGNID) [CP]
          OUTER APPLY (SELECT * FROM MONYADDI [MD] WHERE MD.MID = M.MID) [MD]
          OUTER APPLY (SELECT * FROM lkChannel [LC] WHERE LC.channelId=M.channelId) [LC]
        ]]>
      </t>
    </f>
    <!--||||| lkGIFTTYPE ||||||||||||||||||||||||||||-->
    <f>
      <c>false</c>
      <d>[M]</d>
      <t>
        <![CDATA[
		      OUTER APPLY (SELECT * FROM lkGIFTTYPE [lkGT] with (nolock) WHERE M.GIFTTYPEID=lkGT.GIFTTYPEID) [lkGT]
      ]]>
      </t>
    </f>
    <!--||||| Flag & Keywords ||||||||||||||||||||||||||||||-->
    <f>
      <c>false</c>
      <d>[P]</d>
      <t>
        <![CDATA[
          OUTER APPLY (SELECT * FROM jtFLAG [JF] WHERE P.PID=JF.PID) [JF]
          OUTER APPLY (SELECT * FROM dmFLAG [DF] WHERE JF.FLAGID = DF.FLAGID) [DF]
          OUTER APPLY (SELECT * FROM jtKWRD [JK] WHERE P.PID=JK.PID) [JK]
          OUTER APPLY (SELECT * FROM dmKWRD [DK] WHERE JK.KWRDID = DK.KWRDID) [DK]
        ]]>
      </t>
    </f>
    <!--||||| Attribute Category & Attribute & Attribute People ||||||||||||||||||||||||||||||-->
    <f>
      <c>false</c>
      <d>[P]</d>
      <t>
        <![CDATA[
          OUTER APPLY (SELECT * FROM AttributePeople [AttrPeo] WHERE P.PID=AttrPeo.PID) [AttrPeo]
          OUTER APPLY (SELECT * FROM Attribute [Attr] WHERE Attr.id = AttrPeo.attributeId) [Attr]
          OUTER APPLY (SELECT * FROM AttributeCategory [AttrCat] WHERE AttrCat.id=Attr.categoryId) [AttrCat]
        ]]>
      </t>
    </f>
    <!--||||| Action Category & Action & Action People ||||||||||||||||||||||||||||||-->
    <f>
      <c>false</c>
      <d>[P]</d>
      <t>
        <![CDATA[
          OUTER APPLY (SELECT * FROM ActionPeople [ActPeo] WHERE P.PID=ActPeo.PID) [ActPeo]
          OUTER APPLY (SELECT * FROM Action [Act] WHERE Act.id = ActPeo.actionId) [Act]
          OUTER APPLY (SELECT * FROM ActionCategory [ActCat] WHERE ActCat.id=Act.categoryId) [ActCat]
          OUTER APPLY (SELECT * FROM SOURCE [ACTSRC] WHERE ACTSRC.SRCEID=ActPeo.srceId) [ACTSRC]
        ]]>
      </t>
    </f>
    <!--||||| Club for filtering |||||||||||||||||||||||||||||||||||||||||-->
    <f>
      <c>false</c>
      <d>[P]</d>
      <t>
        <![CDATA[
          OUTER APPLY (SELECT * FROM jtCLUB [JC] WHERE P.PID=JC.PID AND ISNULL(JC.CLOSED,0)=0) [JC]
          OUTER APPLY (SELECT * FROM lkCLUBSTAT [LKC] WHERE JC.cSTATUS=LKC.CLUBSTATID) [LKC]
          OUTER APPLY (SELECT * FROM pmCLUB [DC] WHERE JC.CLUBID=DC.CLUBID) [DC]
        ]]>
      </t>
    </f>
    <!--||||| Club for export |||||||||||||||||||||||||||||||||||||||||-->
    <f>
      <c>false</c>
      <d>[P]</d>
      <t>
        <![CDATA[
          LEFT OUTER JOIN jtCLUB [OJC] ON P.PID = OJC.PID AND ISNULL(OJC.CLOSED,0)=0       
        ]]>
      </t>
      <w f="[AllClubs]">OJC.PRIME=1</w>
    </f>
    <f>
      <c>false</c>
      <d>[OJC]</d>
      <t>
        <![CDATA[
          OUTER APPLY (SELECT * FROM lkCLUBSTAT [OLKC] WHERE OJC.cSTATUS = OLKC.CLUBSTATID) [OLKC]
          OUTER APPLY (SELECT * FROM pmCLUB [ODC] WHERE OJC.CLUBID = ODC.CLUBID) [ODC]
        ]]>
      </t>
    </f>

    <!--||||| Event ||||||||||||||||||||||||||||||||||||||||-->
    <f>
      <c>false</c>
      <d>[P]</d>
      <t>
        <![CDATA[
          OUTER APPLY (SELECT * FROM jtSPCEVNT [JS] WHERE P.PID = JS.PID) [JS]
          OUTER APPLY (SELECT * FROM pmSPCEVNT [PS] WHERE JS.SPCEVNTID = PS.SPCEVNTID) [PS]
          OUTER APPLY (SELECT * FROM lkEVNTSTATUS [LS] WHERE JS.STATUS = LS.STATUS) [LS]
        ]]>
      </t>
    </f>

    <!--||||| Pledge |||||||||||||||||||||||||||||||||||||||-->
    <f>
      <c>false</c>
      <d>[P]</d>
      <t>
        <![CDATA[
          OUTER APPLY (SELECT * FROM v_pledge_summary [PG] WHERE P.PID=PG.PID) [PG]
        ]]>
      </t>
    </f>

    <!--||||| Chapter ||||||||||||||||||||||||||||||||||||||-->
    <f>
      <c>false</c>
      <d>[P]</d>
      <t>
        <![CDATA[
          OUTER APPLY (SELECT * FROM lkCHAPCODE [lkCC] WHERE P.CHAPCODEID=lkCC.CHAPCODEID) [lkCC]
        ]]>
      </t>
    </f>

    <!--||||| FEC Search |||||||||||||||||||||||||||||||||||-->
    <f>
      <c>false</c>
      <d>[P]</d>
      <t>
        <![CDATA[
          OUTER APPLY (SELECT * FROM x_fecMasterFile [FEC] WHERE P.FECCMTEID=FEC.FEC_ID) [FEC]
        ]]>
      </t>
    </f>

    <!--||||| Bundler Info ||||||||||||||||||||||||||||||||||-->
    <f>
      <c>false</c>
      <d>[P]</d>
      <t>
        <![CDATA[
		      OUTER APPLY (SELECT * FROM FUNDRAISE [BUFR] WHERE P.TRACKNO=BUFR.TRACKNO) [BUFR]
		      OUTER APPLY (SELECT * FROM lkLOBBYIST [BULKL] WHERE BUFR.LOBBYISTID = BULKL.LOBBYISTID) [BULKL]
      ]]>
      </t>
    </f>
    <!--||||| Gift Officer Assigned ||||||||||||||||||||||||||||||||||-->
    <f>
      <c>false</c>
      <d>[P]</d>
      <t>
        <![CDATA[
		      OUTER APPLY (SELECT * FROM jtFUNDRAISE [GOJ] WHERE P.PID=GOJ.PID) [GOJ]
      ]]>
      </t>
    </f>
    <!--||||| Conduit ||||||||||||||||||||||||||||||||||||||-->
    <f>
      <c>false</c>
      <d>[P]</d>
      <t>
        <![CDATA[
            OUTER APPLY (SELECT * FROM CONDUIT [CND] WHERE P.PID=CND.PID) [CND]
        ]]>
      </t>
    </f>

    <!--||||| Interactions (Not currently used) ||||||||||||-->
    <f>
      <c>false</c>
      <d>[P]</d>
      <t>
        <![CDATA[
            OUTER APPLY (SELECT * FROM gActivity [gA] WHERE P.PID=gA.pid) [gA]
        		OUTER APPLY (SELECT * FROM SOURCE [gS] WHERE gA.srceId=gS.SRCEID) [gS]
        		OUTER APPLY (SELECT * FROM dmFUND [gF] WHERE gA.fundId=gF.FUNDID) [gF]
        		OUTER APPLY (SELECT * FROM lkChannel [gH] WHERE gA.channelId=gH.channelId) [gH]
    		    OUTER APPLY (SELECT * FROM gActivityCode [gAC] WHERE gA.activityCodeId=gAC.activityCodeId) [gAC]
    		    OUTER APPLY (SELECT * FROM gActivityGroup [gAG] WHERE gAC.activityGroupId=gAG.activityGroupId) [gAG]
        ]]>
      </t>
    </f>

    <!--||||| FLS Voter (Not currently used) ||||||||||||||||-->
    <f>
      <c>false</c>
      <d>[P]</d>
      <t>
        <![CDATA[
	          OUTER APPLY (SELECT * FROM dbo.zFLSVOTER [FV] WHERE P.PID=FV.PID) [FV]
		        OUTER APPLY (SELECT * FROM dbo.zFLSVOTEHIST [FVH] WHERE FV.FLSVOTERID=FVH.FLSVOTERID) [FVH]
		        OUTER APPLY (SELECT * FROM dbo.zFLSVOTETAG [FVT] WHERE FV.FLSVOTERID=FVT.FLSVOTERID) [FVT]
        ]]>
      </t>
    </f>

  </qf>

  <!--||||| Export Ouput Field Groups |||||||||||||||||||||||||||||||||||||||-->

  <qo>
    <eg name="pseg_idRecordTypeGroup">
      <g>
        <i>pseg_idRecordType</i>
        <c>Record Type</c>
        <d></d>
        <s>true</s>
        <f>
          <![CDATA[
          dbo.crmPeopleToken1(<r>s|projectId|</r>, P.PID, NULL) AS IdToken,
            [P].PID,
            ISNULL([PEOTYPE].DESCRIP,'') AS PEOTYPE,
            ISNULL([PEOCODE].DESCRIP,'') AS PEOCODE
          ]]>
        </f>
      </g>
    </eg>
    <eg name="pseg_pidCheckDigitGroup">
      <g>
        <i>pseg_pidCheckDigit</i>
        <c>PID Check Digit</c>
        <d></d>
        <s>false</s>
        <f>
          <![CDATA[
            ISNULL([P].CHKDGT,'') AS PIDCheckDigit
          ]]>
        </f>
      </g>
    </eg>
    <eg name="pseg_nameGroup">
      <g>
        <i>pseg_name</i>
        <c>Name</c>
        <d></d>
        <s>true</s>
        <f>
          <![CDATA[
          dbo.oFullName(0, [P].PREFIX, [P].FNAME, [P].MNAME, [P].LNAME, [P].SUFFIX) AS NAME,
            ISNULL([P].Prefix,'') AS PREFIX,
            ISNULL([P].FName,'') AS FNAME,
            ISNULL([P].MName,'') AS MNAME,
            ISNULL([P].LName,'') AS LNAME,
            ISNULL([P].Suffix,'') AS SUFFIX
          ]]>
        </f>
      </g>
    </eg>

    <eg name="pseg_emplGroup">
      <g>
        <i>pseg_empl</i>
        <c>Employment</c>
        <d></d>
        <s>true</s>
        <f>
          <![CDATA[
            ISNULL([P].Title,'') AS TITLE,
            ISNULL([P].EMPLOYER,'') AS EMPLOYER,
            ISNULL([P].Occupation,'') AS OCCUPATION,
            [P].DOB AS DOB,
            [P].TRACKNO 
          ]]>
        </f>
      </g>
    </eg>

    <eg name="pseg_mailGroup">
      <g>
        <i>pseg_mail</i>
        <c>Salutation</c>
        <d></d>
        <s>true</s>
        <f>
          <![CDATA[
          ISNULL([P].SpouseName,'') AS SPOUSENAME,
          CASE WHEN LTRIM(ISNULL([P].SPOUSENAME, '')) = '' THEN '' WHEN CHARINDEX(p.LNAME, p.SPOUSENAME, 0) = 0 THEN p.SPOUSENAME + ' ' + p.LNAME ELSE p.SPOUSENAME END AS FULLSPOUSENAME,
            ISNULL([P].Salutation,'') AS FORMSALUT,
            ISNULL([P].Infsalut,'') AS INFSALUT,
            ISNULL([P].PRESSALUT,'') AS MAILSALUT,
            ISNULL([P].MAILNAME,'') AS MAILNAME,
            CAST(CASE WHEN [P].PRIMEMAIL=1 THEN 'Y' ELSE '' END AS VARCHAR(1)) AS PRIMEMAIL,
            ISNULL([P].ASSISTANT,'') AS ASSISTANT
          ]]>
        </f>
      </g>
    </eg>
    <eg name="pseg_addressGroup">
      <g>
        <i>pseg_addressPrimary</i>
        <c>Primary Address</c>
        <d></d>
        <s>true</s>
        <f>
          <![CDATA[
            ISNULL([OAAT].DESCRIP,'') AS ADDRTYPE,
            ISNULL([OA].STREET,'') AS STREET,
            ISNULL([OA].ADDR1,'') AS ADDR1,
            ISNULL([OA].ADDR2,'') AS ADDR2,
            ISNULL([OA].CITY,'') AS CITY,
            ISNULL([OA].STATE,'') AS STATE,
            ISNULL([OA].ZIP,'') AS ZIP,
            ISNULL([OA].PLUS4,'') AS PLUS4,
            ISNULL([OAKC].COUNTY,'') AS COUNTY,
            ISNULL([OACTRY].DESCRIP,'') AS COUNTRY,
            CASE WHEN ISNULL([OARZ].REGION,'')='' THEN [OARS].REGION ELSE [OARZ].REGION END AS REGION,
            ISNULL([OAKMSA].CSATitle,'') AS METROAREA,
            ISNULL([OA].CDCODE,'') AS CD,
            ISNULL([OA].SDCODE,'') AS SDCODE,
            ISNULL([OA].LDCODE,'') AS LDCODE
          ]]>
        </f>
        <ch>v|Federal,State|Y|</ch>
      </g>
      <g>
        <i>pseg_addressAll</i>
        <c>All Addresses</c>
        <d>People record will be listed multiple times. Once per address.</d>
        <s>false</s>
        <f>
          <![CDATA[
            /*IMPORTANT.[AllAddresses]*/
            ISNULL([OAAT].DESCRIP,'') AS ADDRTYPE,
            ISNULL([OA].STREET,'') AS STREET,
            ISNULL([OA].ADDR1,'') AS ADDR1,
            ISNULL([OA].ADDR2,'') AS ADDR2,
            ISNULL([OA].CITY,'') AS CITY,
            ISNULL([OA].STATE,'') AS STATE,
            ISNULL([OA].ZIP,'') AS ZIP,
            ISNULL([OA].PLUS4,'') AS PLUS4,
            ISNULL([OAKC].COUNTY,'') AS COUNTY,
            CASE WHEN ISNULL([OARZ].REGION,'')='' THEN [OARS].REGION ELSE [OARZ].REGION END AS REGION,
            ISNULL([OAKMSA].CSATitle,'') AS METROAREA,
            ISNULL([OA].CDCODE,'') AS CD,
            ISNULL([OA].SDCODE,'') AS SDCODE,
            ISNULL([OA].LDCODE,'') AS LDCODE
          ]]>
        </f>
        <ch>v|Federal,State|Y|</ch>
      </g>

      <g>
        <i>pseg_addressPrimary</i>
        <c>Primary Address</c>
        <d></d>
        <s>true</s>
        <f>
          <![CDATA[
            ISNULL([OAAT].DESCRIP,'') AS ADDRTYPE,
            ISNULL([OA].STREET,'') AS STREET,
            ISNULL([OA].ADDR1,'') AS ADDR1,
            ISNULL([OA].ADDR2,'') AS ADDR2,
            ISNULL([OA].CITY,'') AS CITY,
            ISNULL([OA].STATE,'') AS STATE,
            ISNULL([OA].ZIP,'') AS ZIP,
            ISNULL([OA].PLUS4,'') AS PLUS4,
            ISNULL([OAKC].COUNTY,'') AS COUNTY,
            ISNULL([OACTRY].DESCRIP,'') AS COUNTRY,
            CASE WHEN ISNULL([OARZ].REGION,'')='' THEN [OARS].REGION ELSE [OARZ].REGION END AS REGION,
            ISNULL([OAKMSA].CSATitle,'') AS METROAREA
          ]]>
        </f>
        <ch>v|Nonprofit|Y|</ch>
      </g>
      <g>
        <i>pseg_addressAll</i>
        <c>All Addresses</c>
        <d>People record will be listed multiple times. Once per address.</d>
        <s>false</s>
        <f>
          <![CDATA[
            /*IMPORTANT.[AllAddresses]*/
            ISNULL([OAAT].DESCRIP,'') AS ADDRTYPE,
            ISNULL([OA].STREET,'') AS STREET,
            ISNULL([OA].ADDR1,'') AS ADDR1,
            ISNULL([OA].ADDR2,'') AS ADDR2,
            ISNULL([OA].CITY,'') AS CITY,
            ISNULL([OA].STATE,'') AS STATE,
            ISNULL([OA].ZIP,'') AS ZIP,
            ISNULL([OA].PLUS4,'') AS PLUS4,
            ISNULL([OAKC].COUNTY,'') AS COUNTY,
            ISNULL([OACTRY].DESCRIP,'') AS COUNTRY,
            CASE WHEN ISNULL([OARZ].REGION,'')='' THEN [OARS].REGION ELSE [OARZ].REGION END AS REGION,
            ISNULL([OAKMSA].CSATitle,'') AS METROAREA
          ]]>
        </f>
        <ch>v|Nonprofit|Y|</ch>
      </g>
    </eg>

    <eg name="pseg_phonesGroup">
      <g>
        <i>pseg_phones</i>
        <c>Phones &amp; Email</c>
        <d></d>
        <s>true</s>
        <f>
          <![CDATA[
            ISNULL([PH].PHNNO,'') AS HMPHN,
            CASE WHEN [PH].VERIFIEDAT IS NULL THEN 'N' ELSE CONVERT(VARCHAR,[PH].VERIFIEDAT,101) END AS HMPHNVERIFIED,
            ISNULL([PW].PHNNO,'') AS BSPHN,
            CASE WHEN [PW].VERIFIEDAT IS NULL THEN 'N' ELSE CONVERT(VARCHAR,[PW].VERIFIEDAT,101) END AS BSPHNVERIFIED,
            ISNULL([PF].PHNNO,'') AS FAX,
            CASE WHEN [PF].VERIFIEDAT IS NULL THEN 'N' ELSE CONVERT(VARCHAR,[PF].VERIFIEDAT,101) END AS FAXVERIFIED,
            ISNULL([PC].PHNNO,'') AS CELL,
            CASE WHEN [PC].VERIFIEDAT IS NULL THEN 'N' ELSE CONVERT(VARCHAR,[PC].VERIFIEDAT,101) END AS CELLVERIFIED,
            ISNULL([PE].PHNNO,'') AS EMAIL,
            CASE WHEN [PE].VERIFIEDAT IS NULL THEN 'N' ELSE CONVERT(VARCHAR,[PE].VERIFIEDAT,101) END AS EMAILVERIFIED,
            (SELECT top 1 ISNULL(PHNNO,'') FROM PHONE WHERE PID=P.PID and PHNTYPEID=4 and PRIME=0 order by UPDATEDON desc) AS SECONDARYEMAIL,
            ISNULL([ASTP].PHNNO,'') AS ASSISTANTPHN,
            CASE WHEN [ASTP].VERIFIEDAT IS NULL THEN 'N' ELSE CONVERT(VARCHAR,[ASTP].VERIFIEDAT,101) END AS ASSISTANTPHNVERIFIED,
            ISNULL([ASTE].PHNNO,'') AS ASSISTANTEMAIL,
            CASE WHEN [ASTE].VERIFIEDAT IS NULL THEN 'N' ELSE CONVERT(VARCHAR,[ASTE].VERIFIEDAT,101) END AS ASSISTANTEMAILVERIFIED,
            ISNULL([DIRP].PHNNO,'') AS DIRECTPHN,
            CASE WHEN [DIRP].VERIFIEDAT IS NULL THEN 'N' ELSE CONVERT(VARCHAR,[DIRP].VERIFIEDAT,101) END AS DIRECTPHNVERIFIED
           ]]>
        </f>
      </g>
    </eg>
    <eg name="pseg_contactGroup">
      <g>
        <i>pseg_contactPrimary</i>
        <c>Primary Contact</c>
        <d></d>
        <s>true</s>
        <f>
          <![CDATA[
            [OCNT].CONTACTID,
            ISNULL([OCNT].PREFIX,'') AS CPREFIX,
            ISNULL([OCNT].FNAME,'') AS CFNAME,
            ISNULL([OCNT].MNAME,'') AS CMNAME,
            ISNULL([OCNT].LNAME,'') AS CLNAME,
            ISNULL([OCNT].SUFFIX,'') AS CSUFFIX,
            ISNULL([OCNT].STREET,'') AS CSTREET,
            ISNULL([OCNT].ADDR1,'') as CADDR1,
            ISNULL([OCNT].CITY,'') as CCITY,
            ISNULL([OCNT].STATE,'') as CSTATE,
            ISNULL([OCNT].ZIP,'') as CZIP,
            ISNULL([OCNT].EMAIL,'') as CEMAIL,
            ISNULL([OCNT].CELL,'') as CCELL,
            ISNULL([OCNT].BSPHN,'') as CBSPHN,
            ISNULL([OCNT].HMPHN,'') as CHMPHN,
            dbo.[oContactFlagsString_]([OCNT].CONTACTID) AS CONTACTFLAGS,
            CASE WHEN [OCNT].PRIME=1 THEN 'Y' ELSE '' END AS PrimaryContact
          ]]>
        </f>
      </g>
      <g>
        <i>pseg_contactAll</i>
        <c>All Contacts</c>
        <d></d>
        <s>false</s>
        <f>
          <![CDATA[
            /*IMPORTANT.[AllContacts]*/
            [OCNT].CONTACTID,
            ISNULL([OCNT].PREFIX,'') AS CPREFIX,
            ISNULL([OCNT].FNAME,'') AS CFNAME,
            ISNULL([OCNT].MNAME,'') AS CMNAME,
            ISNULL([OCNT].LNAME,'') AS CLNAME,
            ISNULL([OCNT].SUFFIX,'') AS CSUFFIX,
            ISNULL([OCNT].STREET,'') AS CSTREET,
            ISNULL([OCNT].ADDR1,'') as CADDR1,
            ISNULL([OCNT].CITY,'') as CCITY,
            ISNULL([OCNT].STATE,'') as CSTATE,
            ISNULL([OCNT].ZIP,'') as CZIP,
            ISNULL([OCNT].EMAIL,'') as CEMAIL,
            ISNULL([OCNT].CELL,'') as CCELL,
            ISNULL([OCNT].BSPHN,'') as CBSPHN,
            ISNULL([OCNT].HMPHN,'') as CHMPHN,
            dbo.[oContactFlagsString_]([OCNT].CONTACTID) AS CONTACTFLAGS,
            CASE WHEN [OCNT].PRIME=1 THEN 'Y' ELSE '' END AS PrimaryContact 
          ]]>
        </f>
      </g>
    </eg>

    <eg name="pseg_biography">
      <g>
        <i>pseg_bio1</i>
        <c>Biography</c>
        <d></d>
        <s>false</s>
        <f>
          <![CDATA[
              ISNULL([P].BIO,'') AS BIOGRAPHY
              ]]>
        </f>
        <ch>c|show dod|NULL|</ch>
      </g>
      <g>
        <i>pseg_bio2</i>
        <c>Biography</c>
        <d></d>
        <s>false</s>
        <f>
          <![CDATA[
              [P].DOD AS DOD,
              ISNULL([P].BIO,'') AS BIOGRAPHY
              ]]>
        </f>
        <ch>c|show dod|!NULL|</ch>
      </g>
    </eg>

    <eg name="pseg_givingGroup">
      <g>
        <i>pseg_giving</i>
        <c>Giving Summary</c>
        <d></d>
        <s>true</s>
        <f>
          <![CDATA[
            [SD].FGIFTDTE AS FIRSTGIFTDATE,
            [SD].FGIFT AS FIRSTGIFT,
            [SD].LGIFTDTE AS LASTGIFTDATE,
            [SD].LGIFT AS LASTGIFT,
            [SD].HPC AS HIGHESTGIFT,
            [SD].HPCDTE AS HIGHESTGIFTDTE,
            [SD].NOGIFTS AS NO_GIFTS,
            [SD].CUMTOT AS TOTALGIFT,
            CASE WHEN ISNULL([SD].CUMTOT,0)=0 OR ISNULL([SD].NOGIFTS,0)=0 THEN NULL ELSE   [SD].CUMTOT/[SD].NOGIFTS END AS AVERAGEGIFT,
            [SD].YTDAMT AS YTDGIFT,
            [SD].CTDAMT AS CTDGIFT,
            [SD].PREV1YRAMT,
            [SD].PREV2YRAMT,
            [SD].PREV3YRAMT,
            [SD].PREV4YRAMT,
            [SD].PREV5YRAMT
          ]]>
        </f>
        <ch>v|Federal,State|Y|</ch>
      </g>
      <g>
        <i>pseg_giving</i>
        <c>Giving Summary</c>
        <d></d>
        <s>true</s>
        <f>
          <![CDATA[
            [SD].FGIFTDTE AS FIRSTGIFTDATE,
            [SD].FGIFT AS FIRSTGIFT,
            [SD].LGIFTDTE AS LASTGIFTDATE,
            [SD].LGIFT AS LASTGIFT,
            [SD].HPC AS HIGHESTGIFT,
            [SD].HPCDTE AS HIGHESTGIFTDTE,
            [SD].NOGIFTS AS NO_GIFTS,
            [SD].CUMTOT AS TOTALGIFT,
            CASE WHEN ISNULL([SD].CUMTOT,0)=0 OR ISNULL([SD].NOGIFTS,0)=0 THEN NULL ELSE   [SD].CUMTOT/[SD].NOGIFTS END AS AVERAGEGIFT,
            [SD].YTDAMT AS YTDGIFT,
            [SD].CTDAMT AS FTDGIFT,
            [SD].LFTDAMT AS LASTFTDGIFT,
            [SD].L2FTDAMT AS LAST2FTDGIFT,
            [SD].L3FTDAMT AS LAST3FTDGIFT 
          ]]>
        </f>
        <ch>v|Nonprofit|Y|</ch>
      </g>
    </eg>

    <eg name="pseg_jfcGroup">
      <g>
        <i>pseg_jfc</i>
        <c>JFC Summary</c>
        <d></d>
        <s>true</s>
        <f>
          <![CDATA[
            [SD].CTDNETAMT AS CTDGIFTALL,
            [SD].CTDAMT_P AS DIRECTP,
            [SD].CTDAMT_G AS DIRECTG  
          ]]>
        </f>
        <ch>v|Federal,State|Y|</ch>
      </g>
    </eg>

    <eg name="pseg_sumByFundGroup">
      <g>
        <i>pseg_sumByFund</i>
        <c>Total - Active Funds</c>
        <d></d>
        <s>true</s>
        <f>
          <![CDATA[
            <r>d|SELECT dbo.o_activeFund__sqlSelectFields_for_PdotPID()|</r>
          ]]>
        </f>
      </g>
    </eg>

    <eg name="pseg_sumByFundGroupSpouse">
      <g>
        <i>pseg_sumByFundSpouse</i>
        <c>Total - Spouse Active Funds</c>
        <d></d>
        <s>false</s>
        <f>
          <![CDATA[
            ISNULL((SELECT TOP 1 PID FROM dbo.people_latestSpouseInfo([P].PID)),0) as SPOUSEPID,
            <r>d|SELECT dbo.o_activeFund__sqlSelectFields_for_PdotSPOUSEPID()|</r>
          ]]>
        </f>
      </g>
    </eg>

    <eg name="pseg_ytdGroup">
      <g>
        <i>pseg_ytdByFund</i>
        <c>YTD Total - Active Funds</c>
        <d></d>
        <s>false</s>
        <f>
          <![CDATA[
            <r>d|SELECT dbo.o_activeFund__sqlSelectFieldsYTD_for_PdotPID()|</r>
          ]]>
        </f>
        <ch>c|People Search - YTD by Fund|Y|</ch>
      </g>
    </eg>

    <eg name="pseg_ytdGroupSpouse">
      <g>
        <i>pseg_ytdByFundSpouse</i>
        <c>YTD Total - Spouse Active Funds</c>
        <d></d>
        <s>false</s>
        <f>
          <![CDATA[
            <r>d|SELECT dbo.o_activeFund__sqlSelectFieldsYTD_for_PdotSPOUSEPID()|</r>
          ]]>
        </f>
        <ch>c|People Search - YTD by Fund|Y|</ch>
      </g>
    </eg>
    
    <eg name="pseg_prevCGroup">
      <g>
        <i>pseg_prevC</i>
        <c>Previous Cycle Total</c>
        <d></d>
        <s>false</s>
        <f>
          <![CDATA[
            [SD].PCYCLE_TOTAL AS PREVCYCTOTAL,
            [SD].PCYCLE_NO AS PREVCYC_NOGIFTS
          ]]>
        </f>
        <ch>c|Output Prev Cycle|Y|true</ch>
      </g>
    </eg>

    <eg name="pseg_politicalGroup">
      <g>
        <i>pseg_political</i>
        <c>FEC ID</c>
        <d></d>
        <s>false</s>
        <f>
          <![CDATA[
            ISNULL([P].FECCMTEID,'') AS FECID
          ]]>
        </f>
        <ch>v|Federal,State|Y|</ch>
      </g>
    </eg>
    <eg name="pseg_conduitGroup">
      <g>
        <i>pseg_conduit</i>
        <c>Conduit#</c>
        <d></d>
        <s>false</s>
        <f>
          <![CDATA[
            ISNULL([CND].CONDUITNO,'') AS CONDUITNO
          ]]>
        </f>
        <ch>c|Conduit Module|!NULL|</ch>
      </g>
    </eg>

    <eg name="pseg_peoClassGroup">
      <g>
        <i>pseg_peoClass</i>
        <c>People Optional Code</c>
        <d></d>
        <s>false</s>
        <f>
          <![CDATA[
            [PEOCLASS].DESCRIP AS <r>c|People Class Field|PEOOPTCODE</r>
          ]]>
        </f>
        <ch>c|People Class Field|!NULL|true</ch>
      </g>
    </eg>

    <eg name="pseg_peoStr1Group">
      <g>
        <i>pseg_str1</i>
        <c>People Optional String Field</c>
        <d></d>
        <s>true</s>
        <f>
          <![CDATA[        
            [P].STR1 AS <r>c|People STR1 Field|PEOSTR1</r>
          ]]>
        </f>
        <ch>c|People STR1 Field|!NULL|true</ch>
      </g>
    </eg>

    <eg name="pseg_RecurringGifts">
      <g>
        <i>pseg_RecurringGifts</i>
        <c>Recurring Gifts</c>
        <d></d>
        <s>false</s>
        <f>
          <![CDATA[
            CASE WHEN [MD].RECURENDDTE IS NOT NULL AND [MD].RECURENDDTE<GETDATE()  THEN 'Expired' 
WHEN [MD].RECURRED=1 THEN 'Active' ELSE '' END AS RECURRINGSTATUS,
            CASE WHEN [MD].RECURRED = 1 THEN [M].AMT ELSE NULL END AS RECURRINGAMT,
            [MD].RECURENDDTE AS RECURENDDTE
          ]]>
        </f>
      </g>
    </eg>

    <eg name="pseg_clubGroup">
      <g>
        <i>pseg_club</i>
        <c>Primary Club Info</c>
        <d></d>
        <s>false</s>
        <f>
          <![CDATA[
            [ODC].CLUBCODE AS CLUB,
            [OLKC].DESCRIP AS CLUBSTATUS,
            [OJC].SOLICITOR AS SOLICITOR,
            [OJC].CANDIDATE AS CANDIDATENAME,
            [OJC].RNEWDTE AS CLUBRENEW,
            CASE WHEN [OJC].PRIME = 1 THEN 'Y' ELSE '' END AS PrimaryClub
          ]]>
        </f>
        <ch>v|Federal,State|Y|</ch>
      </g>
      <g>
        <i>pseg_allclubs</i>
        <c>All Club Info</c>
        <d>People record will be listed multiple times. Once per club.</d>
        <s>false</s>
        <f>
          <![CDATA[
            /*IMPORTANT.[AllClubs]*/
            [ODC].CLUBCODE AS CLUB,
            [OLKC].DESCRIP AS CLUBSTATUS,
            [OJC].SOLICITOR AS SOLICITOR,
            [OJC].CANDIDATE AS CANDIDATENAME,
            [OJC].RNEWDTE AS CLUBRENEW,
            CASE WHEN [OJC].PRIME = 1 THEN 'Y' ELSE '' END AS PrimaryClub
          ]]>
        </f>
        <ch>v|Federal,State|Y|</ch>
      </g>
      <g>
        <i>pseg_club</i>
        <c>Primary Membership Info</c>
        <d></d>
        <s>false</s>
        <f>
          <![CDATA[
            [ODC].CLUBCODE AS MEMBERSHIP,
            [OLKC].DESCRIP AS MEMBERSHIPSTATUS,
            [OJC].SOLICITOR AS SOLICITOR,
            [OJC].CANDIDATE AS CANDIDATENAME,
            [OJC].RNEWDTE AS MEMBERSHIPRENEW,
            CASE WHEN [OJC].PRIME = 1 THEN 'Y' ELSE '' END AS PrimaryMembership
          ]]>
        </f>
        <ch>v|Nonprofit|Y|</ch>
      </g>
      <g>
        <i>pseg_allclubs</i>
        <c>All Membership Info</c>
        <d>People record will be listed multiple times. Once per membership.</d>
        <s>false</s>
        <f>
          <![CDATA[
            /*IMPORTANT.[AllClubs]*/
            [ODC].CLUBCODE AS MEMBERSHIP,
            [OLKC].DESCRIP AS MEMBERSHIPSTATUS,
            [OJC].SOLICITOR AS SOLICITOR,
            [OJC].CANDIDATE AS CANDIDATENAME,
            [OJC].RNEWDTE AS MEMBERSHIPRENEW,
            CASE WHEN [OJC].PRIME = 1 THEN 'Y' ELSE '' END AS PrimaryMembership
          ]]>
        </f>
        <ch>v|Nonprofit|Y|</ch>
      </g>
    </eg>

    <eg name="pseg_chapterGroup">
      <g>
        <i>pseg_chapter</i>
        <c>Chapter</c>
        <d></d>
        <s>false</s>
        <f>
          <![CDATA[
            ISNULL([lkCC].CHAPCODE,'') AS <r>c|chapter|Chapter</r>
          ]]>
        </f>
        <ch>c|chapter|!NULL|true</ch>
      </g>
    </eg>

    <eg name="pseg_pledgeGroup">
      <g>
        <i>pseg_pledge</i>
        <c>Pledge Info</c>
        <d></d>
        <s>false</s>
        <f>
          <![CDATA[
            ISNULL([PG].PLEDGEAMT,0) AS PLEDGEAMT,
            ISNULL([PG].FULFILLED,0) AS FULFILLED,
            ISNULL([PG].OUTSTANDING,0) AS OUTSTANDING
          ]]>
        </f>
      </g>
    </eg>

    <eg name="pseg_fecSearchGroup">
      <g>
        <i>pseg_fecSearch</i>
        <c>FEC Report Data</c>
        <d>(eg. Last Report-date &amp; Cash-on-hand)</d>
        <s>false</s>
        <f>
          <![CDATA[
            [FEC].LATEST_REPORT_COVERAGE_DATE AS FEC_LATESTREPORT,
            [FEC].LATEST_REPORT_CASH_ON_HAND AS FEC_LATESTREPORT_CASHONHAND
          ]]>
        </f>
        <ch>c|FEC Search|Y|</ch>
      </g>
    </eg>
    <eg name="pseg_AttributeGroup">
      <g>
        <i>pseg_AttributeGroup</i>
        <c>Attribute</c>
        <d></d>
        <s>false</s>
        <f>
          <![CDATA[
            dbo.oGetAttributeSTR(P.[PID],10) AS Attributes
          ]]>
        </f>
        <ch>a|People/Profile/Attributes|v,e,a,d|</ch>
      </g>
    </eg>
    <eg name="pseg_AssignFundraiserGroup">
      <g>
        <i>pseg_AssignFundraiserGroup</i>
        <c>Assigned Fundraiser</c>
        <d></d>
        <s>false</s>
        <f>
          <![CDATA[
            (SELECT SUBSTRING((SELECT ' · ' + fp.FUNDRAISER  
	            FROM jtFUNDRAISE f 
	            LEFT JOIN v_trackno fp ON f.TRACKNO = fp.TRACKNO 
              WHERE f.PID = P.[PID] ORDER BY f.TRACKNO FOR XML PATH('')), 4, 5000)) as AssignedFundraiser
          ]]>
        </f>
        <ch>v|Federal,State|Y|</ch>
      </g>
      <g>
        <i>pseg_AssignGiftOfficerGroup</i>
        <c>Assigned Gift Officer</c>
        <d></d>
        <s>false</s>
        <f>
          <![CDATA[
            (SELECT SUBSTRING((SELECT ' · ' + fp.FUNDRAISER  
	            FROM jtFUNDRAISE f 
	            LEFT JOIN v_trackno fp ON f.TRACKNO = fp.TRACKNO 
              WHERE f.PID = P.[PID] ORDER BY f.TRACKNO FOR XML PATH('')), 4, 5000)) as AssignedGiftOfficer
          ]]>
        </f>
        <ch>v|Nonprofit|Y|</ch>
      </g>
    </eg>

    <!--||||| Bundler Info |||||||||||||||||||||||||||||||||
    <eg name="pseg_bundlerGroup">
      <g>
        <i>pseg_bundler</i>
        <c>Bundler Info</c>
        <d></d>
        <s>false</s>
        <f>
          <![CDATA[
            ISNULL([P].TRACKNO,'') AS TRACKNO,
            ISNULL([BULKL].LOBBYIST,'') AS LOBBYIST
          ]]>
        </f>
        <ch>v|Federal,State|Y|</ch>
      </g>
    </eg>
-->


    <eg name="pseg_eviteGroup">
      <t>true</t>
      <x>csv</x>
      <g>
        <i>pseg_evite</i>
        <c>Name &amp; Email List</c>
        <d>(eg. Evite)</d>
        <s>false</s>
        <f>
          <![CDATA[
            dbo.oFullName(0,[P].PREFIX, [P].FNAME, [P].MNAME, [P].LNAME, [P].SUFFIX) AS NAME,
            ISNULL([PE].PHNNO, '') AS EMAIL
          ]]>
        </f>
        <w f="[PE].PHNNO">dbo.oValidEmail([PE].PHNNO)=1</w>
      </g>
    </eg>
    <eg name="pseg_onlineadGroup">
      <t>true</t>
      <g>
        <i>pseg_onlinead</i>
        <c>Email List</c>
        <d>(eg. Online Advertising)</d>
        <s>false</s>
        <f>
          <![CDATA[
            ISNULL([PE].PHNNO, '') AS EMAIL
          ]]>
        </f>
        <w f="[PE].PHNNO">dbo.oValidEmail([PE].PHNNO)=1</w>
      </g>
    </eg>
    <eg name="pseg_exportByChannelSysGrp">
      <t>false</t>
      <sys>true</sys>
      <g>
        <i>pseg_exportByChannel</i>
        <c>Export By Channel</c>
        <s>false</s>
        <f>
          <![CDATA[
            [P].PID,
            ISNULL([OA].STREET,'') AS STREET,
            ISNULL([OA].CITY,'') AS CITY,
            ISNULL([OA].STATE,'') AS STATE,
            ISNULL([OA].ZIP,'') AS ZIP,
            ISNULL([PH].PHNNO,'') AS HMPHN,
            ISNULL([PC].PHNNO,'') AS CELL,
            ISNULL([PE].PHNNO,'') AS EMAIL
          ]]>
        </f>
      </g>
    </eg>

    <eg name="pseg_mediaGroup">
      <g>
        <i>pseg_media</i>
        <c>Social Media</c>
        <d></d>
        <s>false</s>
        <f>
          <![CDATA[
            ISNULL([UR].PHNNO,'') AS WEBSITE,
            ISNULL([FB].PHNNO,'') AS FACEBOOK,
            ISNULL([TW].PHNNO,'') AS TWITTER,
            ISNULL([LI].PHNNO,'') AS LINKEDIN
           ]]>
        </f>
      </g>
    </eg>

  </qo>

  <!--||||| Quick Text Search Configuration |||||||||||||||||||||||||||||||||-->

  <qtsc>
    <c>
      <e><![CDATA[:f\[\s*[\w\.\-\']+(,\s*[\w\.\-\']*)*\s*\]]]></e>
      <co>true</co>
      <cl>Flag</cl>
      <cc>f</cc>
      <fs>
        <f key="pspg_flag" idx="6"/>
      </fs>
    </c>
    <c>
      <e><![CDATA[:k\[\s*[\s\w\.\-\']+(,\s*[\s\w\.\-\']*)*\s*\]]]></e>
      <co>true</co>
      <cl>Keyword</cl>
      <cc>k</cc>
      <fs>
        <f key="pspg_kwrd" idx="6"/>
      </fs>
    </c>
    <c>
      <e><![CDATA[:s\[\s*[a-zA-Z]{2}(,\s*[a-zA-Z]{2})*\s*\]]]></e>
      <co>true</co>
      <cl>State (Abbreviation)</cl>
      <cc>s</cc>
      <fs>
        <f key="pspc_state" idx="6"/>
      </fs>
    </c>
    <c>
      <e><![CDATA[:z\[\s*\d{5}(,\s*\d{5})*\s*\]]]></e>
      <co>true</co>
      <cl>Zip Code (5 digits)</cl>
      <cc>z</cc>
      <fs>
        <f key="pspc_zip" idx="6"/>
      </fs>
    </c>
    <c>
      <e><![CDATA[[a-zA-Z0-9._-]+@[a-zA-Z0-9._-]+\.[a-zA-Z0-9_-]+]]></e>
      <co>false</co>
      <fs>
        <f key="pspc_email" idx="1"/>
      </fs>
    </c>
    <c>
      <e><![CDATA[\(\s*[\s\w\.\-\']+,\s*[a-zA-Z]{2}\s+\d{5}\s*\)]]></e>
      <co>false</co>
      <fs>
        <f key="pspc_city" idx="1"/>
        <f key="pspc_state" idx="1"/>
        <f key="pspc_zip" idx="1"/>
      </fs>
    </c>
    <c>
      <e><![CDATA[\(\s*[\s\w\.\-\']+,\s*[a-zA-Z]{2}\s*\)]]></e>
      <co>false</co>
      <fs>
        <f key="pspc_city" idx="1"/>
        <f key="pspc_state" idx="1"/>
      </fs>
    </c>
    <c>
      <e><![CDATA[\(\s*\d{5}\s*\)]]></e>
      <co>false</co>
      <fs>
        <f key="pspc_zip" idx="1"/>
      </fs>
    </c>
    <c>
      <e><![CDATA[\(\s*[a-zA-Z]{2}\s*\)]]></e>
      <co>false</co>
      <fs>
        <f key="pspc_state" idx="1"/>
      </fs>
    </c>
    <c>
      <e><![CDATA[\(\s*[\s\w\.\-\']{3,}\s*\)]]></e>
      <co>false</co>
      <fs>
        <f key="pspc_city" idx="1"/>
      </fs>
    </c>
    <c>
      <e><![CDATA[^[\w\.\-\']+(\s+[\w\.\-\']+){2}$]]></e>
      <co>false</co>
      <fs>
        <f key="pspp_name" idx="0"/>
        <!-- b4 pspp_name (General/Normalized Name Search)
        <f key="pspp_fn" idx="6"/>
        <f key="pspp_mn" idx="1"/>
        <f key="pspp_ln" idx="1"/>
        -->
      </fs>
    </c>
    <c>
      <e><![CDATA[^[\w\.\-\']+\s+[\w\.\-\']+$]]></e>
      <co>false</co>
      <fs>
        <f key="pspp_name" idx="0"/>
        <!-- b4 pspp_name (General/Normalized Name Search)
        <f key="pspp_fn" idx="6"/>
        <f key="pspp_ln" idx="1"/>
        -->
      </fs>
    </c>
    <c>
      <e><![CDATA[^\d+$]]></e>
      <co>false</co>
      <fs>
        <f key="pspp_pid" idx="0"/>
      </fs>
    </c>
    <c>
      <e><![CDATA[^[\w\.\-\']+$]]></e>
      <co>false</co>
      <fs>
        <f key="pspp_name" idx="0"/>
        <!-- b4 pspp_name (General/Normalized Name Search)
        <f key="pspp_ln" idx="1"/>
        -->
      </fs>
    </c>
    <c>
      <e><![CDATA[.*]]></e>
      <co>false</co>
      <fs>
        <f key="pspp_name" idx="0"/>
        <!-- b4 pspp_name (General/Normalized Name Search)
        <f key="pspp_ln" idx="1"/>
        -->
      </fs>
    </c>
  </qtsc>

  <!--||||| Filter Groups |||||||||||||||||||||||||||||||||||||||||||||||||||-->

  <qsfg>
    <qs name="people_profile" caption="Profile">
      <q>
        <i>pspp_pid</i>
        <t>n</t>
        <c>PID</c>
        <n>[P].PID</n>
        <d>CMDI Record#</d>
        <o>0</o>
      </q>
      <q>
        <i>pspp_name</i>
        <t>x</t>
        <c>All Name Fields</c>
        <d>Normalized name search</d>
        <n>will-be-replaced-by-post-processing</n>
        <o>0</o>
        <p><![CDATA[d|SELECT [dbo].[crimson_peopleNameSearchSql]('<r>name string(s)</r>','[P]')|field|]]></p>
      </q>
      <q>
        <i>pspp_pfx</i>
        <t>s</t>
        <c>Prefix</c>
        <n>[P].PREFIX</n>
        <o>1</o>
      </q>
      <q>
        <i>pspp_fn</i>
        <t>s</t>
        <c>First Name</c>
        <d>Normalized name search w/ InListOf option (eg. Robert=Bob)</d>
        <n>[P].FNAME</n>
        <o>1</o>
        <p><![CDATA[d|SELECT [dbo].[same_nickNames]('<r>fname(s)-comma-separated-if-needed</r>')|value|IN]]></p>
      </q>
      <q>
        <i>pspp_mn</i>
        <t>s</t>
        <c>Middle Name</c>
        <n>[P].MNAME</n>
        <o>1</o>
      </q>
      <q>
        <i>pspp_ln</i>
        <t>s</t>
        <c>Last Name</c>
        <d>or Company Name</d>
        <n>[P].LNAME</n>
        <o>1</o>
      </q>
      <q>
        <i>pspp_inf</i>
        <t>s</t>
        <c>Informal Name</c>
        <n>[P].INFSALUT</n>
        <o>1</o>
      </q>
      <q>
        <i>pspp_emp</i>
        <t>s</t>
        <c>Employer</c>
        <n>[P].EMPLOYER</n>
        <o>1</o>
      </q>
      <q>
        <i>pspp_occ</i>
        <t>s</t>
        <c>Occupation</c>
        <n>[P].OCCUPATION</n>
        <o>1</o>
      </q>
      <q>
        <i>pspp_title</i>
        <t>s</t>
        <c>Title</c>
        <d>Professional Title</d>
        <n>[P].TITLE</n>
        <o>1</o>
      </q>
      <q>
        <i>pspp_spouse</i>
        <t>s</t>
        <c>Spouse</c>
        <n>[P].SPOUSENAME</n>
        <o>1</o>
      </q>
      <q>
        <i>pspp_str1</i>
        <t>s</t>
        <c>Optional Code</c>
        <n>[P].STR1</n>
        <o>1</o>
        <ch>c|People STR1 Field|!NULL|true</ch>
      </q>
      <q>
        <i>pspp_acctno</i>
        <t>s</t>
        <c>Optional Code</c>
        <n>[P].ACCTNO</n>
        <o>1</o>
        <ch>c|people.acctno|!NULL|true</ch>
      </q>
      <q>
        <i>pspp_trackno</i>
        <t>n</t>
        <c>Fundraiser Track#</c>
        <n>[P].TRACKNO</n>
        <o>0</o>
        <ch>v|Federal,State|Y|</ch>
      </q>
      <q>
        <i>pspp_trackno</i>
        <t>n</t>
        <c>Gift Officer Track#</c>
        <n>[P].TRACKNO</n>
        <o>0</o>
        <ch>v|Nonprofit|Y|</ch>
      </q>
      <q>
        <i>pspp_lobbyist</i>
        <t>b</t>
        <c>Lobbyist</c>
        <n>[BULKL].LOBBYIST = 'Yes'</n>
        <ch>v|Federal,State||</ch>
      </q>
      <q>
        <i>pspp_age</i>
        <t>n</t>
        <c>Age</c>
        <n>[P].age</n>
        <o>0</o>
      </q>
      <q>
        <i>pspp_dobmo</i>
        <t>s</t>
        <c>Birthday Month</c>
        <n>[P].dob_mo</n>
        <o>0</o>
        <l>
          <i>
            <s>1</s>
            <r>1 - January</r>
          </i>
          <i>
            <s>2</s>
            <r>2 - February</r>
          </i>
          <i>
            <s>3</s>
            <r>3 - March</r>
          </i>
          <i>
            <s>4</s>
            <r>4 - April</r>
          </i>
          <i>
            <s>5</s>
            <r>5 - May</r>
          </i>
          <i>
            <s>6</s>
            <r>6 - June</r>
          </i>
          <i>
            <s>7</s>
            <r>7 - July</r>
          </i>
          <i>
            <s>8</s>
            <r>8 - August</r>
          </i>
          <i>
            <s>9</s>
            <r>9 - September</r>
          </i>
          <i>
            <s>10</s>
            <r>10 - October</r>
          </i>
          <i>
            <s>11</s>
            <r>11 - November</r>
          </i>
          <i>
            <s>12</s>
            <r>12 - December</r>
          </i>
        </l>
      </q>
      <q>
        <i>pspp_dob</i>
        <t>d</t>
        <c>DOB</c>
        <n>[P].DOB</n>
        <o>1</o>
      </q>
      <q>
        <i>pspp_dod</i>
        <t>d</t>
        <c>DOD</c>
        <n>[P].DOD</n>
        <o>1</o>
        <ch>c|show dod|Y|</ch>
      </q>
      <q>
        <i>pspp_fecid</i>
        <t>s</t>
        <c>FEC Committee ID</c>
        <n>[P].FECCMTEID</n>
        <o>1</o>
        <ch>v|Federal,State||</ch>
      </q>
      <q>
        <i>pspp_fecsearch_date</i>
        <t>d</t>
        <c>FEC - Latest Report Date</c>
        <n>[FEC].LATEST_REPORT_COVERAGE_DATE</n>
        <o>0</o>
        <ch>c|FEC Search|Y|</ch>
      </q>
      <q>
        <i>pspp_fecsearch_coh</i>
        <t>c</t>
        <c>FEC - Cash on Hand</c>
        <c>Cash on Hand in the latest FEC Report</c>
        <n>[FEC].LATEST_REPORT_CASH_ON_HAND</n>
        <o>0</o>
        <ch>c|FEC Search|Y|</ch>
      </q>
      <q>
        <i>pspp_conduitno</i>
        <t>n</t>
        <c>Conduit #</c>
        <n>[CND].CONDUITNO</n>
        <o>0</o>
        <ch>c|Conduit Module|Y|</ch>
      </q>
      <q>
        <i>pspp_CHAPCODE</i>
        <t>s</t>
        <c>Chapter Code</c>
        <n>[lkCC].CHAPCODE</n>
        <o>0</o>
        <lookup id='people_profile_chapcode'><![CDATA[EXEC dbo.z_get_lookup_in_xml__chapcode]]></lookup>
        <ch>c|chapter|!NULL|true</ch>
      </q>
      <q>
        <i>pspp_CHAPCODE_A</i>
        <t>a</t>
        <c>Chapter Access</c>
        <o>0</o>
        <n>sys-processing-chapter-pid-access</n>
        <p><![CDATA[n|[P].PID IN (SELECT PID FROM dbo.havingTheChapterCodePid('<r>chap-code</r>'))|field|]]></p>
        <ch>c|chapter|!NULL|</ch>
        <h>true</h>
      </q>
      <q>
        <i>pspp_LASTACTON</i>
        <t>d</t>
        <c>Last Activity Date</c>
        <n>[P].LASTACTON</n>
        <o>0</o>
      </q>
      <q>
        <i>pspp_archived</i>
        <t>b</t>
        <c>Search Archived</c>
        <n>ISNULL([P].ACTIVE,0)=0</n>
      </q>
    </qs>

    <qs name="people_contact" caption="Contact">
      <q>
        <i>pspc_alladdr</i>
        <t>b</t>
        <c>Search All Addresses</c>
        <d></d>
        <n>
          <![CDATA[ISNULL([A].PRIME,0)>=0]]>
        </n>
      </q>
      <q>
        <i>pspc_NoAddr</i>
        <t>b</t>
        <c>People w/ No Address</c>
        <n>
          <![CDATA[[P].PID NOT IN (SELECT PID FROM ADDRESS WHERE ISNULL(ZIP,'')<>'')]]>
        </n>
      </q>
      <q>
        <i>pspc_street</i>
        <t>s</t>
        <c>Street</c>
        <n>[A].STREET</n>
        <o>1</o>
      </q>
      <q>
        <i>pspc_city</i>
        <t>s</t>
        <c>City</c>
        <n>[A].CITY</n>
        <o>1</o>
      </q>
      <q>
        <i>pspc_state</i>
        <t>s</t>
        <c>State</c>
        <n>[A].STATE</n>
        <o>1</o>
      </q>
      <q>
        <i>pspc_zip</i>
        <t>s</t>
        <c>Zip</c>
        <n>[A].ZIP</n>
        <o>1</o>
      </q>
      <q>
        <i>pspc_cntyfips</i>
        <t>s</t>
        <c>County FIPS</c>
        <d>3-digit County FIPS</d>
        <n>[KC].FIPS</n>
        <o>1</o>
      </q>
      <q>
        <i>pspc_cnty</i>
        <t>s</t>
        <c>County</c>
        <d>County Name</d>
        <n>[KC].COUNTY</n>
        <o>1</o>
      </q>
      <q>
        <i>pspc_cd</i>
        <t>s</t>
        <c>CD Code</c>
        <d>2-digit Congressional District Code</d>
        <n>[A].CDCODE</n>
        <o>1</o>
        <ch>v|Federal,State||</ch>
      </q>
      <q>
        <i>pspc_sd</i>
        <t>s</t>
        <c>SD Code</c>
        <d>2-digit Senatorial District Code</d>
        <n>[A].SDCODE</n>
        <o>1</o>
        <ch>v|Federal,State||</ch>
      </q>
      <q>
        <i>pspc_ld</i>
        <t>s</t>
        <c>LD Code</c>
        <d>3-digit Legislative District Code</d>
        <n>[A].LDCODE</n>
        <o>1</o>
        <ch>v|Federal,State||</ch>
      </q>
      <q>
        <i>pspc_region</i>
        <t>s</t>
        <c>Region</c>
        <d></d>
        <n>ISNULL([RZ].REGION,[RS].REGION)</n>
        <o>1</o>
      </q>
      <q>
        <i>pspc_metro</i>
        <t>s</t>
        <c>Metro Area</c>
        <d></d>
        <n>[KMSA].CSATitle</n>
        <o>1</o>
      </q>
      <q>
        <i>pspc_homeph</i>
        <t>s</t>
        <c>Home Phone</c>
        <d>Enter numbers only</d>
        <n>[PH].PHNNO</n>
        <o>1</o>
      </q>
      <q>
        <i>pspc_homephverified</i>
        <t>b</t>
        <c>Verified Home Phone Only</c>
        <n>
          <![CDATA[[PH].VERIFIEDAT IS NOT NULL]]>
        </n>
      </q>
      <q>
        <i>pspc_workph</i>
        <t>s</t>
        <c>Work Phone</c>
        <d>Enter numbers only</d>
        <n>[PW].PHNNO</n>
        <o>1</o>
      </q>
      <q>
        <i>pspc_workphverified</i>
        <t>b</t>
        <c>Verified Work Phone Only</c>
        <n>
          <![CDATA[[PW].VERIFIEDAT IS NOT NULL]]>
        </n>
      </q>
      <q>
        <i>pspc_cell</i>
        <t>s</t>
        <c>Cell Phone</c>
        <d>Enter numbers only</d>
        <n>[PC].PHNNO</n>
        <o>1</o>
      </q>
      <q>
        <i>pspc_cellphverified</i>
        <t>b</t>
        <c>Verified Cell Phone Only</c>
        <n>
          <![CDATA[[PC].VERIFIEDAT IS NOT NULL]]>
        </n>
      </q>
      <q>
        <i>pspc_anyphone</i>
        <t>s</t>
        <c>Any Phone</c>
        <d>Enter numbers only</d>
        <n>[APH].PHNNO</n>
        <o>1</o>
      </q>
      <q>
        <i>pspc_anyphoneverified</i>
        <t>b</t>
        <c>Verified Any Phone Only</c>
        <n>
          <![CDATA[[APH].VERIFIEDAT IS NOT NULL]]>
        </n>
      </q>
      <q>
        <i>pspc_nophone</i>
        <t>b</t>
        <c>No Telephone#</c>
        <n>
          <![CDATA[[P].PID NOT IN (SELECT PID FROM PHONE WHERE PHNTYPEID IN (1,2,3) AND PHNNO<>'')]]>
        </n>
      </q>
      <q>
        <i>pspc_email</i>
        <t>s</t>
        <c>Email</c>
        <n>[PE].PHNNO</n>
        <o>1</o>
      </q>
      <q>
        <i>pspc_emailverified</i>
        <t>b</t>
        <c>Verified Email Only</c>
        <n>
          <![CDATA[[PE].VERIFIEDAT IS NOT NULL]]>
        </n>
      </q>
      <q>
        <i>pspc_noemail</i>
        <t>b</t>
        <c>No Email Address</c>
        <n>
          <![CDATA[[P].PID NOT IN (SELECT PID FROM PHONE WHERE PHNTYPEID=4 AND PHNNO<>'')]]>
        </n>
      </q>
      <q>
        <i>pspc_cfn</i>
        <t>s</t>
        <c>Contact's First Name</c>
        <n>[CNT].FNAME</n>
        <o>1</o>
      </q>
      <q>
        <i>pspc_cln</i>
        <t>s</t>
        <c>Contact's Last Name</c>
        <n>[CNT].LNAME</n>
        <o>1</o>
      </q>
      <q>
        <i>pspc_cfg</i>
        <t>s</t>
        <c>Contact's Flag</c>
        <n>[DCF].CONTFLAG</n>
        <o>1</o>
      </q>
      <q>
        <i>pspc_AssName</i>
        <t>s</t>
        <c>Assistant's Name</c>
        <n>[P].ASSISTANT</n>
        <o>1</o>
      </q>
      <q>
        <i>pspc_AssPhone</i>
        <t>s</t>
        <c>Assistant's Phone</c>
        <n>[ASTP].PHNNO</n>
        <o>1</o>
      </q>
      <q>
        <i>pspc_AssPhoneverified</i>
        <t>b</t>
        <c>Verified Assistant's Phone Only</c>
        <n>
          <![CDATA[[ASTP].VERIFIEDAT IS NOT NULL]]>
        </n>
      </q>
      <q>
        <i>pspc_AssEmail</i>
        <t>s</t>
        <c>Assistant's Email</c>
        <n>[ASTE].PHNNO</n>
        <o>1</o>
      </q>
      <q>
        <i>pspc_AssEmailverified</i>
        <t>b</t>
        <c>Verified Assistant's Email Only</c>
        <n>
          <![CDATA[[ASTE].VERIFIEDAT IS NOT NULL]]>
        </n>
      </q>
    </qs>

    <qs name="people_group" caption="Group">
      <q>
        <i>pspg_nonDonor</i>
        <t>b</t>
        <c>Non-Donor</c>
        <n>
          <![CDATA[[P].PID NOT IN (SELECT PID FROM SUMMARYD)]]>
        </n>
      </q>

      <q>
        <i>pspg_pcode</i>
        <t>s</t>
        <c>People Code</c>
        <d>Enter People Record Code</d>
        <n>[PEOCODE].PEOCODE</n>
        <o>0</o>
        <lookup id='people_group_peocode'><![CDATA[EXEC dbo.z_get_lookup_in_xml__peopleCode]]></lookup>
      </q>
      <q>
        <i>pspg_ptype</i>
        <t>s</t>
        <c>People Type</c>
        <d>Enter People Record Type</d>
        <n>[PEOTYPE].PEOTYPE</n>
        <o>0</o>
        <lookup id='people_group_peotype'><![CDATA[EXEC dbo.z_get_lookup_in_xml__peopleType]]></lookup>
      </q>
      <q>
        <i>pspg_pclass</i>
        <t>s</t>
        <c>Optional Code</c>
        <n>[PEOCLASS].PEOCLASS</n>
        <o>0</o>
        <ch>c|People Class Field|!NULL|true</ch>
        <lookup id='people_group_peoclass'><![CDATA[EXEC dbo.z_get_lookup_in_xml__peopleCLASS]]></lookup>
      </q>
      <q>
        <i>pspg_tflag</i>
        <t>b</t>
        <c>Top Flag</c>
        <n>
          <![CDATA[[DF].TOPFLAG=1]]>
        </n>
      </q>
      <q>
        <i>pspg_flag</i>
        <t>s</t>
        <c>Flag</c>
        <n>[DF].FLAG</n>
        <o>6</o>
        <optionsSearch>
          <url>crm/api/FlagSettings/GetFlag?pageSize=10&amp;page=1&amp;searchText=</url>
          <valueField>FLAG</valueField>
          <nameField>FLAG</nameField>
          <valueFieldType>string</valueFieldType>
        </optionsSearch>
      </q>
      <q>
        <i>pspg_fdate</i>
        <t>d</t>
        <c>Flag Date</c>
        <n>[JF].UPDATEDON</n>
        <o>0</o>
      </q>
      <q>
        <i>pspg_allFlags</i>
        <t>a</t>
        <c>Having All Flags</c>
        <o>0</o>
        <optionsSearch>
          <url>crm/api/FlagSettings/GetFlag?pageSize=10&amp;page=1&amp;searchText=</url>
          <valueField>FLAG</valueField>
          <nameField>FLAG</nameField>
          <valueFieldType>string</valueFieldType>
        </optionsSearch>
        <n>will-be-replaced-by-post-processing</n>
        <p><![CDATA[n|[P].PID IN (SELECT PID FROM dbo.havingTheseFlags('<r>comma-separated</r>',1))|field|]]></p>
      </q>
      <q>
        <i>pspg_kwrd</i>
        <t>s</t>
        <c>Keyword</c>
        <n>[DK].KWRD</n>
        <o>6</o>
        <optionsSearch>
          <url>crm/api/KeywordSettings/GetKeyword?pageSize=100000&amp;page=1&amp;searchText=</url>
          <valueField>KWRD</valueField>
          <nameField>KWRD</nameField>
          <valueFieldType>string</valueFieldType>
        </optionsSearch>
      </q>
      <q>
        <i>pspg_allKwrds</i>
        <t>a</t>
        <c>Having All Keywords</c>
        <o>0</o>
        <optionsSearch>
          <url>crm/api/KeywordSettings/GetKeyword?pageSize=100000&amp;page=1&amp;searchText=</url>
          <valueField>KWRD</valueField>
          <nameField>KWRD</nameField>
          <valueFieldType>string</valueFieldType>
        </optionsSearch>
        <n>will-be-replaced-by-post-processing</n>
        <p><![CDATA[n|[P].PID IN (SELECT PID FROM dbo.havingTheseKeywords('<r>comma-separated</r>',1))|field|]]></p>
      </q>
      <q>
        <i>pspg_ecode</i>
        <t>s</t>
        <c>Event Code</c>
        <n>[PS].EVNTCODE</n>
        <o>1</o>
      </q>
      <q>
        <i>pspg_estatus</i>
        <t>s</t>
        <c>Event Status</c>
        <n>[LS].DESCRIP</n>
        <o>1</o>
      </q>
      <q>
        <i>pspg_edate</i>
        <t>d</t>
        <c>Event Start Date</c>
        <n>[PS].STARTDTE</n>
        <o>0</o>
      </q>      
      <q>
        <i>pspg_ccode</i>
        <t>s</t>
        <c>Club</c>
        <n>[DC].CLUBCODE</n>
        <o>1</o>
        <ch>v|Federal,State|Y|</ch>
      </q>
      <q>
        <i>pspg_cstatus</i>
        <t>s</t>
        <c>Club Status</c>
        <n>[LKC].DESCRIP</n>
        <o>1</o>
        <ch>v|Federal,State|Y|</ch>
      </q>
      <q>
        <i>pspg_allClubs</i>
        <t>a</t>
        <c>Having All Clubs</c>
        <o>0</o>
        <n>will-be-replaced-by-post-processing</n>
        <p><![CDATA[n|[P].PID IN (SELECT PID FROM dbo.havingTheseClubs('<r>comma-separated</r>',1))|field|]]></p>
        <ch>v|Federal,State|Y|</ch>
      </q>
      <q>
        <i>pspg_crenew</i>
        <t>d</t>
        <c>Club Renewal Date</c>
        <n>[JC].RNEWDTE</n>
        <o>0</o>
        <ch>v|Federal,State|Y|</ch>
      </q>
      <q>
        <i>pspg_ccode</i>
        <t>s</t>
        <c>Membership</c>
        <n>[DC].CLUBCODE</n>
        <o>1</o>
        <ch>v|Nonprofit|Y|</ch>
      </q>
      <q>
        <i>pspg_cstatus</i>
        <t>s</t>
        <c>Membership Status</c>
        <n>[LKC].DESCRIP</n>
        <o>1</o>
        <ch>v|Nonprofit|Y|</ch>
      </q>
      <q>
        <i>pspg_allClubs</i>
        <t>a</t>
        <c>Having All Memberships</c>
        <o>0</o>
        <n>will-be-replaced-by-post-processing</n>
        <p><![CDATA[n|[P].PID IN (SELECT PID FROM dbo.havingTheseClubs('<r>comma-separated</r>',1))|field|]]></p>
        <ch>v|Nonprofit|Y|</ch>
      </q>
      <q>
        <i>pspg_crenew</i>
        <t>d</t>
        <c>Membership Renewal Date</c>
        <n>[JC].RNEWDTE</n>
        <o>0</o>
        <ch>v|Nonprofit|Y|</ch>
      </q>
      <q>
        <i>pspg_giftOfficerAssigned</i>
        <t>s</t>
        <c>Gift Officer Assigned</c>
        <n>[GOJ].TRACKNO</n>
        <o>6</o>
        <optionsSearch>
          <url>crm/api/PeopleF/GetFundraiserList?pageSize=10&amp;page=1&amp;searchText=</url>
          <valueField>TRACKNO</valueField>
          <nameField>FUNDRAISER</nameField>
          <valueFieldType>string</valueFieldType>
        </optionsSearch>
        <ch>v|Federal,State|C|Fundraiser Assigned</ch>
      </q>
      <q>
        <i>pspg_category</i>
        <t>s</t>
        <c>Attribute Category</c>
        <n>[AttrCat].name</n>
        <o>6</o>
        <optionsSearch>
          <url>crm/api/AttributeCategory/GetAttributeCategory?pageSize=10&amp;page=1&amp;searchText=</url>
          <valueField>name</valueField>
          <nameField>name</nameField>
          <valueFieldType>string</valueFieldType>
        </optionsSearch>
        <ch>a|People/Profile/Attributes|v,e,a,d|</ch>
      </q>
      <q>
        <i>pspg_allAttributeCatrgorys</i>
        <t>a</t>
        <c>Having All Attribute Categories</c>
        <o>0</o>
        <optionsSearch>
          <url>crm/api/AttributeCategory/GetAttributeCategory?pageSize=10&amp;page=1&amp;searchText=</url>
          <valueField>name</valueField>
          <nameField>name</nameField>
          <valueFieldType>string</valueFieldType>
        </optionsSearch>
        <n>will-be-replaced-by-post-processing</n>
        <p><![CDATA[n|[P].PID IN (SELECT PID FROM dbo.havingTheseAttributeCategorys('<r>comma-separated</r>',1))|field|]]></p>
        <ch>a|People/Profile/Attributes|v,e,a,d|</ch>
      </q>
      <q>
        <i>pspg_attribute</i>
        <t>s</t>
        <c>Attribute</c>
        <n>[Attr].name</n>
        <o>6</o>
        <optionsSearch>
          <url>crm/api/Attribute/GetAttribute?categoryId=0&amp;pageSize=10&amp;page=1&amp;searchText=</url>
          <valueField>name</valueField>
          <nameField>fullNameC</nameField>
          <valueFieldType>string</valueFieldType>
        </optionsSearch>
        <ch>a|People/Profile/Attributes|v,e,a,d|</ch>
      </q>
      <q>
        <i>pspg_allAttributes</i>
        <t>a</t>
        <c>Having All Attributes</c>
        <o>0</o>
        <optionsSearch>
          <url>crm/api/Attribute/GetAttribute?categoryId=0&amp;pageSize=10&amp;page=1&amp;searchText=</url>
          <valueField>name</valueField>
          <nameField>fullNameC</nameField>
          <valueFieldType>string</valueFieldType>
        </optionsSearch>
        <n>will-be-replaced-by-post-processing</n>
        <p><![CDATA[n|[P].PID IN (SELECT PID FROM dbo.havingTheseAttributes('<r>comma-separated</r>',1))|field|]]></p>
        <ch>a|People/Profile/Attributes|v,e,a,d|</ch>
      </q>
      <q>
        <i>pspg_attrstartdte</i>
        <t>d</t>
        <c>Attribute Start Date</c>
        <n>[AttrPeo].startDate</n>
        <o>0</o>
        <ch>a|People/Profile/Attributes|v,e,a,d|</ch>
      </q>
      <q>
        <i>pspg_attrenddte</i>
        <t>d</t>
        <c>Attribute End Date</c>
        <n>[AttrPeo].endDate</n>
        <o>0</o>
        <ch>a|People/Profile/Attributes|v,e,a,d|</ch>
      </q>
      <q>
        <i>pspg_attractive</i>
        <t>b</t>
        <c>Active Attributes</c>
        <n>[AttrPeo].active=1</n>
        <ch>a|People/Profile/Attributes|v,e,a,d|</ch>
      </q>
      <q>
        <i>pspg_actcat</i>
        <t>s</t>
        <c>Action Category</c>
        <n>[ActCat].name</n>
        <o>6</o>
        <optionsSearch>
          <url>crm/api/ActionCategory/GetActionCategory?pageSize=10&amp;page=1&amp;searchText=</url>
          <valueField>name</valueField>
          <nameField>name</nameField>
          <valueFieldType>string</valueFieldType>
        </optionsSearch>
        <ch>a|People/Profile/Actions|v,e,a,d|</ch>
      </q>
      <q>
        <i>pspg_allActionCategories</i>
        <t>a</t>
        <c>Having All Action Categories</c>
        <o>0</o>
        <optionsSearch>
          <url>crm/api/ActionCategory/GetActionCategory?pageSize=10&amp;page=1&amp;searchText=</url>
          <valueField>name</valueField>
          <nameField>name</nameField>
          <valueFieldType>string</valueFieldType>
        </optionsSearch>
        <n>will-be-replaced-by-post-processing</n>
        <p><![CDATA[n|[P].PID IN (SELECT PID FROM dbo.havingTheseActionCategorys('<r>comma-separated</r>',1))|field|]]></p>
        <ch>a|People/Profile/Actions|v,e,a,d|</ch>
      </q>
      <q>
        <i>pspg_action</i>
        <t>s</t>
        <c>Action</c>
        <n>[Act].name</n>
        <o>6</o>
        <optionsSearch>
          <url>crm/api/Action/GetAction?categoryId=0&amp;pageSize=10&amp;page=1&amp;searchText=</url>
          <valueField>name</valueField>
          <nameField>fullNameC</nameField>
          <valueFieldType>string</valueFieldType>
        </optionsSearch>
        <ch>a|People/Profile/Actions|v,e,a,d|</ch>
      </q>
      <q>
        <i>pspg_allActions</i>
        <t>a</t>
        <c>Having All Actions</c>
        <o>0</o>
        <optionsSearch>
          <url>crm/api/Action/GetAction?categoryId=0&amp;pageSize=10&amp;page=1&amp;searchText=</url>
          <valueField>name</valueField>
          <nameField>fullNameC</nameField>
          <valueFieldType>string</valueFieldType>
        </optionsSearch>
        <n>will-be-replaced-by-post-processing</n>
        <p><![CDATA[n|[P].PID IN (SELECT PID FROM dbo.havingTheseActions('<r>comma-separated</r>',1))|field|]]></p>
        <ch>a|People/Profile/Actions|v,e,a,d|</ch>
      </q>
      <q>
        <i>pspg_actdte</i>
        <t>d</t>
        <c>Action Date</c>
        <n>[ActPeo].actionDate</n>
        <o>0</o>
        <ch>a|People/Profile/Actions|v,e,a,d|</ch>
      </q>
      <q>
        <i>pspg_actsrcecode</i>
        <t>s</t>
        <c>Action Source Code</c>
        <n>[ACTSRC].SRCECODE</n>
        <o>6</o>
        <optionsSearch>
          <url>crm/api/SourceCodes</url>
          <isQueryable>true</isQueryable>
          <valueField>SRCECODE</valueField>
          <nameField>SRCECODE</nameField>
          <valueFieldType>string</valueFieldType>
        </optionsSearch>
        <ch>a|People/Profile/Actions|v,e,a,d|</ch>
      </q>
      <q>
        <i>pspg_anyCode</i>
        <t>a</t>
        <c>Having Any Code</c>
        <d>Includes Flags, Keywords, Actions, Attributes, Clubs, and Events</d>
        <o>0</o>
        <optionsSearch>
          <url>crm/api/CodeQuery?page=1&amp;pageSize=10&amp;searchText=</url>
          <valueField>code</valueField>
          <nameField>description</nameField>
          <valueFieldType>string</valueFieldType>
        </optionsSearch>
        <n>will-be-replaced-by-post-processing</n>
        <p><![CDATA[n|[P].PID IN (SELECT PID FROM dbo.havingAllCodes('<r>comma-separated</r>',0))|field|]]></p>
      </q>
    </qs>

    <qs name="people_giftSummary" caption="Giving Summary">
      <q>
        <i>pspgs_nogifts</i>
        <t>n</t>
        <c>Total # of Gifts</c>
        <n>ISNULL([SD].NOGIFTS,0)</n>
        <o>0</o>
      </q>
      <q>
        <i>pspgs_ctot</i>
        <t>c</t>
        <c>Cumulative Total</c>
        <n>[SD].CUMTOT</n>
        <o>0</o>
      </q>
      <q>
        <i>pspgs_idate</i>
        <t>d</t>
        <c>Inception Date</c>
        <d>First Gift Date</d>
        <n>[SD].FGIFTDTE</n>
        <o>0</o>
      </q>
      <q>
        <i>pspgs_igift</i>
        <t>c</t>
        <c>Inception Gift</c>
        <n>[SD].FGIFT</n>
        <d>First Gift Amount</d>
        <o>0</o>
      </q>
      <q>
        <i>pspgs_ldte</i>
        <t>d</t>
        <c>Most Recent Gift Date</c>
        <n>[SD].LGIFTDTE</n>
        <d>Last Gift</d>
        <o>0</o>
      </q>
      <q>
        <i>pspgs_lgift</i>
        <t>c</t>
        <c>Most Recent Gift</c>
        <n>[SD].LGIFT</n>
        <d>Last Gift</d>
        <o>0</o>
      </q>
      <q>
        <i>pspgs_hgift</i>
        <t>c</t>
        <c>Highest Gift</c>
        <n>[SD].HPC</n>
        <o>0</o>
      </q>
      <q>
        <i>pspgs_ctd</i>
        <t>c</t>
        <c>Cycle-to-Date Total</c>
        <n>[SD].CTDAMT</n>
        <o>0</o>
        <ch>v|Nonprofit|C|Fiscal-to-Date Total</ch>
      </q>
      <q>
        <i>pspgs_ytd</i>
        <t>c</t>
        <c>Year-to-Date Total</c>
        <n>[SD].YTDAMT</n>
        <o>0</o>
      </q>
      <q>
        <i>pspgs_plgout</i>
        <t>c</t>
        <c>Pledge Outstanding</c>
        <n>[PG].OUTSTANDING</n>
        <d></d>
        <o>1</o>
      </q>
      <q>
        <i>pspgs_jfc</i>
        <t>c</t>
        <c>JFC Total</c>
        <n>[SD].JFCAMT</n>
        <d></d>
        <o>1</o>
        <ch>v|Federal,State|Y|</ch>
      </q>
    </qs>

    <qs name="people_gifts" caption="Gifts">
      <q>
        <i>pspm_rcvdte</i>
        <t>d</t>
        <c>Received On</c>
        <d>Received Date</d>
        <n>[M].RECVDTE</n>
        <o>0</o>
        <ch>c|Show Received Date|Y|</ch>
      </q>
      <q>
        <i>pspm_entdate</i>
        <t>d</t>
        <c>Entry Date</c>
        <n>[M].ENTRYDTE</n>
        <o>0</o>
      </q>
      <q>
        <i>pspm_batdte</i>
        <t>d</t>
        <c>Batch Date</c>
        <d>Deposit Date</d>
        <n>[M].BATCHDTE</n>
        <o>0</o>
      </q>
      <q>
        <i>pspm_batno</i>
        <t>s</t>
        <c>Batch No.</c>
        <d>or Code</d>
        <n>[M].BATCHNO</n>
        <o>0</o>
      </q>
      <q>
        <i>pspm_amt</i>
        <t>c</t>
        <c>Gift Amount</c>
        <n>[M].AMT</n>
        <o>0</o>
      </q>
      <q>
        <i>pspm_esc</i>
        <t>b</t>
        <c>Exclude Soft Credit</c>
        <n>
          <![CDATA[[M].SOFTMONEY=0]]>
        </n>
      </q>
      <q>
        <i>pspm_fca</i>
        <t>b</t>
        <c>Included in Dashboard</c>
        <n>
          <![CDATA[[F].INCLUDE_IN_REPORT=1]]>
        </n>
      </q>
      <q>
        <i>pspm_fc</i>
        <t>s</t>
        <c>Fund Code</c>
        <n>[F].FUNDCODE</n>
        <o>0</o>
        <lookup id='people_gifts_fund'><![CDATA[EXEC dbo.z_get_lookup_in_xml__fund]]></lookup>
      </q>
      <q>
        <i>pspm_prog</i>
        <t>s</t>
        <c>Program</c>
        <n>[G].PROGTYPE</n>
        <o>0</o>
        <lookup id='people_gifts_program'><![CDATA[EXEC dbo.z_get_lookup_in_xml__program]]></lookup>
      </q>
      <q>
        <i>pspm_pkcode</i>
        <t>s</t>
        <c>Initiative Code</c>
        <n>[K].PKGECODE</n>
        <o>1</o>
      </q>
      <q>
        <i>pspm_srcode</i>
        <t>s</t>
        <c>Source Code</c>
        <n>[S].SRCECODE</n>
        <o>6</o>
        <optionsSearch>
          <url>crm/api/SourceCodes</url>
          <isQueryable>true</isQueryable>
          <valueField>SRCECODE</valueField>
          <nameField>SRCECODE</nameField>
          <valueFieldType>string</valueFieldType>
        </optionsSearch>
      </q>
      <q>
        <i>pspm_monytype</i>
        <t>s</t>
        <c>Pay Method</c>
        <n>[KM].MONYTYPE</n>
        <o>1</o>
        <lookup id='money_gifts_monyType'><![CDATA[EXEC dbo.z_get_lookup_in_xml__monyType]]></lookup>
      </q>
      <q>
        <i>pspm_checkno</i>
        <t>s</t>
        <c>Check/Card No.</c>
        <n>[M].CKNO</n>
        <o>0</o>
      </q>
      <q>
        <i>pspm_trk#</i>
        <t>s</t>
        <c>Gift Track#</c>
        <n>[P].PID IN (SELECT PID FROM MONY M JOIN vwMONYTRACK VM ON M.MID = VM.MID WHERE VM.TRACKNO</n>
        <o>0</o>
      </q>
      <q>
        <i>pspm_accode</i>
        <t>s</t>
        <c>Revenue Code</c>
        <n>[CE].CENTERCODE</n>
        <o>0</o>
        <lookup id='people_gift_acct'><![CDATA[EXEC dbo.z_get_lookup_in_xml__e_AcctCode]]></lookup>
        <ch>c|ALF nonprofit|Y|</ch>
      </q>
      <q>
        <i>pspm_accode2</i>
        <t>s</t>
        <c>Account Code</c>
        <n>[CE].CENTERCODE</n>
        <o>0</o>
        <lookup id='people_gift_acct'><![CDATA[EXEC dbo.z_get_lookup_in_xml__e_AcctCode]]></lookup>
        <ch>c|ALF nonprofit|NULL|</ch>
      </q>
      <q>
        <i>pspm_campgncode</i>
        <t>s</t>
        <c>Campgn Code</c>
        <n>[CP].CAMPGNCODE</n>
        <o>0</o>
        <lookup id='money_gifts_CampgnCode'><![CDATA[ EXEC dbo.z_get_lookup_in_xml__CampgnCode]]></lookup>
        <ch>c|dmCampaign|!NULL|true</ch>
      </q>
      <q>
        <i>pspm_exccode</i>
        <t>s</t>
        <c>Exception Code</c>
        <n>[KE].EXCEP</n>
        <o>1</o>
        <ch>v|Federal,State||</ch>
      </q>
      <q>
        <i>msg_refid</i>
        <t>s</t>
        <c>Reference ID</c>
        <n>[MD].REFERENCEID</n>
        <o>1</o>
        <ch>c|Money custom Reference Id|Y|</ch>
      </q>
      <q>
        <i>msg_MONYCODE</i>
        <t>s</t>
        <c>Credit Code</c>
        <n>[M].MONYCODE</n>
        <o>0</o>
        <lookup id='mony_gifts_monycode'><![CDATA[EXEC dbo.z_get_lookup_in_xml__monycode]]></lookup>
        <ch>c|monycode|!NULL|true</ch>
      </q>
      <q>
        <i>msg_GIFTTYPE</i>
        <t>s</t>
        <c>Gift Type</c>
        <n>[lkGT].GIFTTYPE</n>
        <o>0</o>
        <lookup id='people_profile_gifttype'><![CDATA[EXEC dbo.z_get_lookup_in_xml__gifttype]]></lookup>
        <ch>c|ALF nonprofit|Y|</ch>
      </q>
      <q>
        <i>lkChannel_channel</i>
        <t>s</t>
        <c>Channel</c>
        <n>[LC].descrip</n>
        <o>0</o>
        <lookup id='money_gifts_channel'><![CDATA[EXEC dbo.z_get_lookup_in_xml__Channel]]></lookup>
      </q>
      <q>
        <i>msg_Recurred</i>
        <t>b</t>
        <c>Active Recurring Donation</c>
        <n>
          <![CDATA[[MD].RECURRED = 1 AND ([MD].RECURENDDTE IS NULL OR [MD].RECURENDDTE > GETDATE())]]>
        </n>
      </q>
      <q>
        <i>msg_iRecurred</i>
        <t>b</t>
        <c>Inactive Recurring Donation</c>
        <n>
          <![CDATA[[MD].RECURRED = 1 AND ([MD].RECURENDDTE IS NOT NULL AND [MD].RECURENDDTE <= GETDATE())]]>
        </n>
      </q>
    </qs>


    <qs name="people_givingAggregate1" caption="Giving Aggregate">
      <subquery>true</subquery>
      <sq_uniqId>[SSM].PID</sq_uniqId>
      <sq_groupBy>[SSM].PID</sq_groupBy>
      <sq_from>
        <![CDATA[
          MONY SSM INNER JOIN dmFUND SSF ON SSM.FUNDID = SSF.FUNDID AND SSM.COUNTER=1 ]]>
      </sq_from>
      <q>
        <i>pspga1_gdate</i>
        <t>d</t>
        <c>Agg: Gift Date</c>
        <d></d>
        <n>[SSM].BATCHDTE</n>
        <o>0</o>
      </q>
      <q>
        <i>pspga1_fund</i>
        <t>s</t>
        <c>Agg: Fund Code</c>
        <n>[SSF].FUNDCODE</n>
        <o>0</o>
        <lookup id='people_gifts_fund'><![CDATA[EXEC dbo.z_get_lookup_in_xml__fund]]></lookup>
      </q>
      <q groupbyHaving="true">
        <i>pspga1_nogifts</i>
        <t>n</t>
        <c>Agg: # of Gifts</c>
        <d></d>
        <n>COUNT(*)</n>
        <o>0</o>
      </q>
      <q groupbyHaving="true">
        <i>pspga1_cumul</i>
        <t>c</t>
        <c>Agg: Cumulative $</c>
        <d></d>
        <n>SUM([SSM].AMT)</n>
        <o>0</o>
      </q>
      <q groupbyHaving="true">
        <i>pspga1_hpc</i>
        <t>c</t>
        <c>Agg: Highest $</c>
        <d></d>
        <n>MAX([SSM].AMT)</n>
        <o>0</o>
      </q>
      <q>
        <i>pspga1_esc</i>
        <t>b</t>
        <c>Agg: Exclude Soft Credit</c>
        <n>
          <![CDATA[[SSM].SOFTMONEY=0]]>
        </n>
      </q>
    </qs>

    <qs name="people_givingAggregateSupp" caption="Giving Aggregate (Supp)">
      <subquery>true</subquery>
      <andSuppress>true</andSuppress>
      <sq_uniqId>[SSM2].PID</sq_uniqId>
      <sq_groupBy>[SSM2].PID</sq_groupBy>
      <sq_from>
        <![CDATA[
          MONY SSM2 INNER JOIN dmFUND SSF2 ON SSM2.FUNDID = SSF2.FUNDID AND SSM2.COUNTER=1 ]]>
      </sq_from>
      <q>
        <i>pspga2_gdate</i>
        <t>d</t>
        <c>SuppAgg: Gift Date</c>
        <d></d>
        <n>[SSM2].BATCHDTE</n>
        <o>0</o>
      </q>
      <q>
        <i>pspga2_fund</i>
        <t>s</t>
        <c>SuppAgg: Fund Code</c>
        <n>[SSF2].FUNDCODE</n>
        <o>0</o>
        <lookup id='people_gifts_fund'><![CDATA[EXEC dbo.z_get_lookup_in_xml__fund]]></lookup>
      </q>
      <q groupbyHaving="true">
        <i>pspga2_nogifts</i>
        <t>n</t>
        <c>SuppAgg: # of Gifts</c>
        <d></d>
        <n>COUNT(*)</n>
        <o>0</o>
      </q>
      <q groupbyHaving="true">
        <i>pspga2_cumul</i>
        <t>c</t>
        <c>SuppAgg: Cumulative $</c>
        <d></d>
        <n>SUM([SSM2].AMT)</n>
        <o>0</o>
      </q>
      <q groupbyHaving="true">
        <i>pspga2_hpc</i>
        <t>c</t>
        <c>SuppAgg: Highest $</c>
        <d></d>
        <n>MAX([SSM2].AMT)</n>
        <o>0</o>
      </q>
    </qs>

    <qs name="people_suppress" caption="Suppression">
      <subquery>true</subquery>
      <forceSuppress>true</forceSuppress>
      <sq_uniqId>[SP].PID</sq_uniqId>
      <sq_from>
        <![CDATA[
          People [SP] WITH (NOLOCK) 
              LEFT OUTER JOIN SUMMARYD [SSD] ON SP.PID=SSD.PID
              LEFT OUTER JOIN MONY [SM] ON SM.PID=SP.PID
              LEFT OUTER JOIN SOURCE [SS] ON SM.SRCEID =SS.SRCEID 
              LEFT OUTER JOIN PACKAGE [SPK] ON SS.PKGEID =SPK.PKGEID 
              LEFT OUTER JOIN PROGRAM [SPG] ON SPK.PROGID=SPG.PROGID
              LEFT OUTER JOIN jtFLAG [SJF] ON SP.PID=SJF.PID LEFT OUTER JOIN dmFLAG [SDF] ON SJF.FLAGID = SDF.FLAGID
              LEFT OUTER JOIN jtKWRD [SJK] ON SP.PID=SJK.PID LEFT OUTER JOIN dmKWRD [SDK] ON SJK.KWRDID = SDK.KWRDID
              LEFT OUTER JOIN jtSPCEVNT [SJS] ON SP.PID = SJS.PID
              LEFT OUTER JOIN pmSPCEVNT [SPS] ON SJS.SPCEVNTID = SPS.SPCEVNTID
              LEFT OUTER JOIN lkEVNTSTATUS [SLS] ON SJS.STATUS = SLS.STATUS
              LEFT OUTER JOIN ATTRIBUTEPEOPLE [SATTRPEO] ON SATTRPEO.PID=P.PID LEFT OUTER JOIN ATTRIBUTE [SATTR] ON SATTR.id=SATTRPEO.attributeId
              LEFT OUTER JOIN ACTIONPEOPLE [SACTPEO] ON SACTPEO.PID=P.PID LEFT OUTER JOIN ACTION [SACT] ON SACT.id=SACTPEO.actionId
              LEFT OUTER JOIN jtFUNDRAISE [SFR] ON SP.PID = SFR.PID LEFT OUTER JOIN v_trackno [SFP] ON SFR.TRACKNO = SFP.TRACKNO
              LEFT OUTER JOIN lkMONYTYPE [SMT] ON SM.MONYTYPEID = SMT.MONYTYPEID
]]>
      </sq_from>
      <q>
        <i>pspx_suppflag</i>
        <t>b</t>
        <c>Apply Channel Suppression Flags</c>
        <n>
          <![CDATA[SDF.FLAGID IN (SELECT FLAGID FROM jtChannelSuppressionFlag)]]>
        </n>
      </q>
      <q>
        <i>pspx_fcode</i>
        <t>s</t>
        <c>Supp: Flag</c>
        <n>[SDF].FLAG</n>
        <o>6</o>
        <optionsSearch>
          <url>crm/api/FlagSettings/GetFlag?pageSize=10&amp;page=1&amp;searchText=</url>
          <valueField>FLAG</valueField>
          <nameField>FLAG</nameField>
          <valueFieldType>string</valueFieldType>
        </optionsSearch>
      </q>
      <q>
        <i>pspx_allFlags</i>
        <t>a</t>
        <c>Supp: Having All Flags</c>
        <o>0</o>
        <optionsSearch>
          <url>crm/api/FlagSettings/GetFlag?pageSize=10&amp;page=1&amp;searchText=</url>
          <valueField>FLAG</valueField>
          <nameField>FLAG</nameField>
          <valueFieldType>string</valueFieldType>
        </optionsSearch>
        <n>will-be-replaced-by-post-processing</n>
        <p><![CDATA[n|[SP].PID IN (SELECT PID FROM dbo.havingTheseFlags('<r>comma-separated</r>',1))|field|]]></p>
      </q>
      <q>
        <i>pspx_kwrd</i>
        <t>s</t>
        <c>Supp: Keyword</c>
        <n>[SDK].KWRD</n>
        <o>6</o>
        <optionsSearch>
          <url>crm/api/KeywordSettings/GetKeyword?pageSize=100000&amp;page=1&amp;searchText=</url>
          <valueField>KWRD</valueField>
          <nameField>KWRD</nameField>
          <valueFieldType>string</valueFieldType>
        </optionsSearch>
      </q>
      <q>
        <i>pspx_allKwrds</i>
        <t>a</t>
        <c>Supp: Having All Keywords</c>
        <o>0</o>
        <optionsSearch>
          <url>crm/api/KeywordSettings/GetKeyword?pageSize=100000&amp;page=1&amp;searchText=</url>
          <valueField>KWRD</valueField>
          <nameField>KWRD</nameField>
          <valueFieldType>string</valueFieldType>
        </optionsSearch>
        <n>will-be-replaced-by-post-processing</n>
        <p><![CDATA[n|[SP].PID IN (SELECT PID FROM dbo.havingTheseKeywords('<r>comma-separated</r>',1))|field|]]></p>
      </q>
      <q>
        <i>pspx_ecode</i>
        <t>s</t>
        <c>Supp: Event Code</c>
        <n>[SPS].EVNTCODE</n>
        <o>1</o>
      </q>
      <q>
        <i>pspx_nogifts</i>
        <t>n</t>
        <c>Supp: Total # of Gifts</c>
        <n>[SSD].NOGIFTS</n>
        <o>0</o>
      </q>
      <q>
        <i>pspx_cumul</i>
        <t>n</t>
        <c>Supp: Cumulative Total</c>
        <n>[SSD].CUMTOT</n>
        <o>0</o>
      </q>
      <q>
        <i>pspx_fgiftdte</i>
        <t>d</t>
        <c>Supp: Inception Date</c>
        <d>First Gift</d>
        <n>[SSD].FGIFTDTE</n>
        <o>0</o>
      </q>
      <q>
        <i>pspx_fgift</i>
        <t>n</t>
        <c>Supp: Inception Gift</c>
        <n>[SSD].FGIFT</n>
        <d>First Gift</d>
        <o>0</o>
      </q>
      <q>
        <i>pspx_lgiftdte</i>
        <t>d</t>
        <c>Supp: Most Recent Gift Date</c>
        <n>[SSD].LGIFTDTE</n>
        <d>Last Gift Date</d>
        <o>0</o>
      </q>
      <q>
        <i>pspx_lgift</i>
        <t>n</t>
        <c>Supp: Most Recent Gift</c>
        <n>[SSD].LGIFT</n>
        <d>Last Gift</d>
        <o>0</o>
      </q>
      <q>
        <i>pspx_prog</i>
        <t>s</t>
        <c>Supp: Program</c>
        <n>[SPG].PROGTYPE</n>
        <o>0</o>
        <lookup id='people_gifts_program'><![CDATA[EXEC dbo.z_get_lookup_in_xml__program]]></lookup>
      </q>
      <q>
        <i>pspx_monytype</i>
        <t>s</t>
        <c>Supp: Pay Method</c>
        <n>[SMT].MONYTYPE</n>
        <o>0</o>
        <lookup id='people_gifts_monyType'><![CDATA[EXEC dbo.z_get_lookup_in_xml__monyType]]></lookup>
      </q>
      <q>
        <i>pspx_attribute</i>
        <t>s</t>
        <c>Supp: Attribute</c>
        <n>[SAttr].name</n>
        <o>6</o>
        <optionsSearch>
          <url>crm/api/Attribute/GetAttribute?categoryId=0&amp;pageSize=10&amp;page=1&amp;searchText=</url>
          <valueField>name</valueField>
          <nameField>fullNameC</nameField>
          <valueFieldType>string</valueFieldType>
        </optionsSearch>
        <ch>a|People/Profile/Attributes|v,e,a,d|</ch>
      </q>
      <q>
        <i>pspx_allAttributeCatrgories</i>
        <t>a</t>
        <c>Supp: Having All Attribute Categories</c>
        <o>0</o>
        <optionsSearch>
          <url>crm/api/AttributeCategory/GetAttributeCategory?pageSize=10&amp;page=1&amp;searchText=</url>
          <valueField>name</valueField>
          <nameField>name</nameField>
          <valueFieldType>string</valueFieldType>
        </optionsSearch>
        <n>will-be-replaced-by-post-processing</n>
        <p><![CDATA[n|[P].PID IN (SELECT PID FROM dbo.havingTheseAttributeCategorys('<r>comma-separated</r>',1))|field|]]></p>
        <ch>a|People/Profile/Attributes|v,e,a,d|</ch>
      </q>
      <q>
        <i>pspx_action</i>
        <t>s</t>
        <c>Supp: Action</c>
        <n>[SACT].name</n>
        <o>6</o>
        <optionsSearch>
          <url>crm/api/Action/GetAction?categoryId=0&amp;pageSize=10&amp;page=1&amp;searchText=</url>
          <valueField>name</valueField>
          <nameField>fullNameC</nameField>
          <valueFieldType>string</valueFieldType>
        </optionsSearch>
        <ch>a|People/Profile/Actions|v,e,a,d|</ch>
      </q>
      <q>
        <i>pspx_allActionCategories</i>
        <t>a</t>
        <c>Supp: Having All Action Categories</c>
        <o>0</o>
        <optionsSearch>
          <url>crm/api/ActionCategory/GetActionCategory?pageSize=10&amp;page=1&amp;searchText=</url>
          <valueField>name</valueField>
          <nameField>name</nameField>
          <valueFieldType>string</valueFieldType>
        </optionsSearch>
        <n>will-be-replaced-by-post-processing</n>
        <p><![CDATA[n|[P].PID IN (SELECT PID FROM dbo.havingTheseActionCategorys('<r>comma-separated</r>',1))|field|]]></p>
        <ch>a|People/Profile/Actions|v,e,a,d|</ch>
      </q>
      <q>
        <i>pspx_assignfundraiser</i>
        <t>s</t>
        <c>Supp: Assigned Gift Officer</c>
        <n>[SFR].TRACKNO</n>
        <o>6</o>
        <optionsSearch>
          <url>crm/api/PeopleF/GetFundraiserList?pageSize=10&amp;page=1&amp;searchText=</url>
          <valueField>TRACKNO</valueField>
          <nameField>FUNDRAISER</nameField>
          <valueFieldType>string</valueFieldType>
        </optionsSearch>
        <ch>v|Federal,State|C|Supp: Assigned Fundraiser</ch>
      </q>
      <q>
        <i>pspx_suppdod</i>
        <t>b</t>
        <c>Apply DOD Suppression</c>
        <n>
          <![CDATA[[P].DOD is not NULL]]>
        </n>
        <ch>c|show dod|Y|</ch>
      </q>
    </qs>
  </qsfg>
  <aggregation>
    <aggregateColumns>
      <aggregateColumn>
        <column>[P].PID</column>
        <function>COUNT</function>
        <label># Records</label>
        <format>n</format>
        <key>numberOfPeople</key>
        <isOrderingColumn>true</isOrderingColumn>
      </aggregateColumn>
    </aggregateColumns>
    <groupByFields>
      <groupByField>
        <key>peopleType</key>
        <column>[PEOTYPE].PEOTYPE</column>
        <descriptionSelect>[PEOTYPE].DESCRIP</descriptionSelect>
        <label>People Type</label>
        <format>s</format>
      </groupByField>
      <groupByField>
        <key>state</key>
        <column>[A].STATE</column>
        <label>State</label>
        <format>s</format>
      </groupByField>
    </groupByFields>
  </aggregation>

</qDef>
