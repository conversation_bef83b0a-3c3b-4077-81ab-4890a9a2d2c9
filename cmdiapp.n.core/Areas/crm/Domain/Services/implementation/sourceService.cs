﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

using Ninject;
using Ninject.Web.Mvc;

using cmdiapp.n.core.Core.App_Start;
using cmdiapp.n.core.Areas.crm.Domain.Models;
using cmdiapp.n.core.Areas.crm.Domain.ViewModels;
using cmdiapp.n.core.Areas.crm.Domain.Repositories;
using cmdiapp.n.core.Domain.ViewModels;

namespace cmdiapp.n.core.Areas.crm.Domain.Services
{
    public class sourceService : IsourceService
    {
        private I___unitOfWork _unitOfWork;
        private I__DBFactory _dbFactory;
        private IsourceRepository _sourceRepository;

        public sourceService(I___unitOfWork unitOfWork, I__DBFactory dbFactory, IsourceRepository SourceRepository)
        {
            _unitOfWork = unitOfWork;
            _dbFactory = dbFactory;
            _sourceRepository = SourceRepository;
        }

        private string get_SQL(string SQL)
        {
            const string _sql_base_line_chart =
                    @"
						IF OBJECT_ID('tempdb..#SRCEBATCH') IS NOT NULL 
							drop table #SRCEBATCH

						IF OBJECT_ID('tempdb..#actualDONATION') IS NOT NULL 
							drop table #actualDONATION

						-- declare variables to hold the start and end date
						DECLARE @StartDate datetime
						DECLARE @EndDate datetime

						--- assign values to the start date and end date we 
						--- want our reports to cover
						SET @StartDate = '{0}'
						SET @EndDate = '{1}' 

						SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED
						SET NOCOUNT ON

						-- GET SRCE & BATCH DATE INFO FIRST
						SELECT S.SRCEID, M.BATCHDTE 
						INTO #SRCEBATCH
						FROM PACKAGE P, SOURCE S, MONY M
						WHERE 
						M.BATCHDTE >= @StartDate AND M.BATCHDTE < DateAdd(d, 1, @EndDate) 
						AND P.PKGEID = {2}
						AND S.SRCEID = {3}
						AND P.PKGEID = S.PKGEID AND M.SRCEID = S.SRCEID AND M.COUNTER = 1 AND M.SOFTMONEY = 0
						GROUP BY S.SRCEID, M.BATCHDTE

						SELECT YEAR(BATCHDTE)*100 + DATEPART(WW,BATCHDTE) AS _week,
						MAX(ISNULL(BATCHDTE,'1/1/1900')) AS _batchdte,                        
						SUBSTRING(CONVERT(VARCHAR(9), MAX(ISNULL(BATCHDTE,'1/1/1900')), 6),8,2) + SUBSTRING(CONVERT(VARCHAR(9), MAX(ISNULL(BATCHDTE,'1/1/1900')), 6),4,3) + SUBSTRING(CONVERT(VARCHAR(9), MAX(ISNULL(BATCHDTE,'1/1/1900')), 6),1,2) AS _date,
						CONVERT(VARCHAR(10), MAX(ISNULL(BATCHDTE,'1/1/1900')), 101) AS _date1,
						COUNT(*) AS _donationCOUNT,
						SUM(M.AMT) AS _donationDOLLAR
						INTO #actualDONATION
						FROM MONY M
						WHERE M.COUNTER = 1 AND
						M.SOFTMONEY = 0 AND
						M.BATCHDTE >=  @StartDate AND  M.BATCHDTE < DateAdd(d, 1, @EndDate) AND
						M.SRCEID IN (SELECT S.SRCEID FROM #SRCEBATCH S )
						GROUP BY YEAR(BATCHDTE)*100 + DATEPART(WW,BATCHDTE) 
						ORDER BY YEAR(BATCHDTE)*100 + DATEPART(WW,BATCHDTE)

						-- SELECT _week, _batchdte, _date, _date1, ISNULL(_donationCOUNT,0) AS _donationCOUNT, ISNULL(_donationDOLLAR,0.00) AS _donationDOLLAR FROM #actualDONATION
						-- SELECT _week, _batchdte, _date, _date1, ISNULL(_donationCOUNT,0) AS _donationCOUNT, ISNULL(_donationDOLLAR,0.00) AS _donationDOLLAR, CONVERT(varchar(12), _donationCOUNT, 1) AS _donationCOUNT1, '$' + CONVERT(varchar(12), _donationDOLLAR, 1) AS _donationDOLLAR1 FROM #actualDONATION
						SELECT _week, _batchdte, _date, _date1, ISNULL(_donationCOUNT,0) AS _donationCOUNT, ISNULL(_donationDOLLAR,0.00) AS _donationDOLLAR, CONVERT(varchar(12), CONVERT(MONEY,ISNULL(_donationCOUNT,0)), 1) AS _donationCOUNT1, '$' + CONVERT(varchar(12), CONVERT(MONEY,ISNULL(_donationDOLLAR,0.00)), 1) AS _donationDOLLAR1 FROM #actualDONATION
 
					";
            const string _sql_Budget_Text =
             @"
                        SELECT
                        ISNULL(SRCEBUDGETID,0) AS SRCEBUDGETID, 
                        ISNULL(SRCEID,0) AS SRCEID, 
                        ISNULL(BUDGETM1,0.00) AS BUDGETM1, 
                        ISNULL(BUDGETM2,0.00) AS BUDGETM2, 
                        ISNULL(BUDGETM3,0.00) AS BUDGETM3, 
                        ISNULL(BUDGETM4,0.00) AS BUDGETM4, 
                        ISNULL(BUDGETM5,0.00) AS BUDGETM5, 
                        ISNULL(BUDGETM6,0.00) AS BUDGETM6, 
                        ISNULL(BUDGETM7,0.00) AS BUDGETM7, 
                        ISNULL(BUDGETM8,0.00) AS BUDGETM8, 
                        ISNULL(BUDGETM9,0.00) AS BUDGETM9, 
                        ISNULL(BUDGETM10,0.00) AS BUDGETM10, 
                        ISNULL(BUDGETM11,0.00) AS BUDGETM11, 
                        ISNULL(BUDGETM12,0.00) AS BUDGETM12, 
                        ISNULL(BUDGETYR,0.00) AS BUDGETYR, 
                        ISNULL(TYPE,0) AS TYPE,
                        ISNULL(EXPENSEM1,0.00) AS EXPENSEM1, 
                        ISNULL(EXPENSEM2,0.00) AS EXPENSEM2, 
                        ISNULL(EXPENSEM3,0.00) AS EXPENSEM3, 
                        ISNULL(EXPENSEM4,0.00) AS EXPENSEM4, 
                        ISNULL(EXPENSEM5,0.00) AS EXPENSEM5, 
                        ISNULL(EXPENSEM6,0.00) AS EXPENSEM6, 
                        ISNULL(EXPENSEM7,0.00) AS EXPENSEM7, 
                        ISNULL(EXPENSEM8,0.00) AS EXPENSEM8, 
                        ISNULL(EXPENSEM9,0.00) AS EXPENSEM9, 
                        ISNULL(EXPENSEM10,0.00) AS EXPENSEM10, 
                        ISNULL(EXPENSEM11,0.00) AS EXPENSEM11, 
                        ISNULL(EXPENSEM12,0.00) AS EXPENSEM12, 
                        ISNULL(EXPENSEYR,0.00) AS EXPENSEYR, 
                        ISNULL(SRCEYEAR,0) AS SRCEYEAR
                        FROM SRCEBUDGET
                        WHERE SRCEID = {0}
                    ";

            const string sql_allSources = @"SELECT 
                            (SELECT COUNT(DISTINCT SRCEID) FROM SOURCE S, PACKAGE P) AS CNT,
                            ISNULL(S.SRCEID,0) AS SRCEID,
                            ISNULL(S.PKGEID,0) AS PKGEID,
                            LTRIM(RTRIM(ISNULL(K.PKGECODE,''''))) AS PKGECODE,
                            LTRIM(RTRIM(ISNULL(K.PKGEDESC,''''))) AS PKGEDESC,
                            LTRIM(RTRIM(ISNULL(S.SRCECODE,''''))) AS SRCECODE,
                            LTRIM(RTRIM(ISNULL(S.SRCEDESC,''''))) AS SRCEDESC,
                            LTRIM(RTRIM(ISNULL(E.EVNTCODE,''''))) AS EVNTCODE,
                            --remove empty from database
							Case When S.LISTNO = char(32) Then null
                            Else 
							ISNULL(S.LISTNO,null) End AS LISTNO,
                            -- For Mail Date
                            Case When S.MAILDTE = ''1/1/1900'' Then null
                            Else
                            ISNULL(S.MAILDTE,null) End AS MAILDTE,
                            -- For First Cage
                            Case When S.FIRSTCAGE = ''1/1/1900'' Then null
                            Else
                            ISNULL(S.FIRSTCAGE,null) End AS FIRSTCAGE,
                            -- For Last Cage
                            Case When S.LASTCAGE = ''1/1/1900'' Then null
                            Else
                            ISNULL(S.LASTCAGE,null) End AS LASTCAGE,
                            ISNULL(S.COSTPROD1,0.00) AS COSTPROD1,
                            ISNULL(S.COSTPROD2,0.00) AS COSTPROD2,
                            ISNULL(S.COSTPROD3,0.00) AS COSTPROD3,
                            ISNULL(S.COSTPOSTG,0.00) AS COSTPOSTG,
                            ISNULL(S.COSTRESP1,0.00) AS COSTRESP1,
                            ISNULL(S.COSTRESP2,0.00) AS COSTRESP2,
                            ISNULL(S.COSTRESP3,0.00) AS COSTRESP3,
                            ISNULL(S.sQTYMAIL,0) AS sQTYMAIL,
                            ISNULL(S.sMONY,0) AS sMONY,
                            ISNULL(S.sPEOPLE,0) AS sPEOPLE,
                            ISNULL(S.sGROSS,0.00) AS sGROSS,
                            ISNULL(S.sNET,0.00) AS sNET,
                            ISNULL(S.sCOST,0.00) AS sCOST,
                            ISNULL(S.sGROSSPM,0.00) AS sGROSSPM,
                            ISNULL(S.sNETPM,0.00) AS sNETPM,
                            ISNULL(S.sCOSTPM,0.00) AS sCOSTPM,
                            ISNULL(S.sGROSSRSP,0.00) AS sGROSSRSP,
                            ISNULL(S.sNETRSP,0.00) AS sNETRSP,
                            ISNULL(S.sCOSTRSP,0.00) AS sCOSTRSP,
                            ISNULL(S.sRSPPCT,0.00) AS sRSPPCT,
                            ISNULL(S.COMMENT,'''') AS COMMENT,
                            -- For UPDATEDON
                            Case When S.UPDATEDON = ''1/1/1900'' Then null
                            Else
                            ISNULL(S.UPDATEDON,null) End AS UPDATEDON,

                            -- For Last Activity
                            Case When S.LASTACTI = ''1/1/1900'' Then null
                            Else
                            ISNULL(S.LASTACTI,null) End AS LASTACTI,
                            ISNULL(S.SACTIVITY,0) AS SACTIVITY
                            FROM SOURCE S
				            left outer JOIN PACKAGE K ON K.PKGEID = S.PKGEID
                            left outer join pmSPCEVNT E on E.SPCEVNTID=S.SPCEVNTID";

            string _sql_specificSource = @"SELECT	ISNULL(SRCEID,0) AS SRCEID,
		                    ISNULL(PKGEID,0) AS PKGEID, 
		                    LTRIM(RTRIM(ISNULL(SRCECODE,''))) AS SRCECODE,
		                    LTRIM(RTRIM(ISNULL(SRCEDESC,''))) AS SRCEDESC,
                            --remove empty from database
							Case When LISTNOG = char(32) Then null
                            Else 
							ISNULL(LISTNOG,null) End AS LISTNOG,
 
		                    --remove empty from database
							Case When LISTNO = char(32) Then null
                            Else 
							ISNULL(LISTNO,null) End AS LISTNO,
                            -- For Mail Date
                            Case When MAILDTE = '1/1/1900' Then null
                            Else
                            ISNULL(MAILDTE,null) End AS MAILDTE,
                            -- For First Cage
                            Case When FIRSTCAGE = '1/1/1900' Then null
                            Else
                            ISNULL(FIRSTCAGE,null) End AS FIRSTCAGE,
                            -- For Last Cage
                            Case When LASTCAGE = '1/1/1900' Then null
                            Else
                            ISNULL(LASTCAGE,null) End AS LASTCAGE,
		                    
		                    ISNULL(COSTPROD1,0.00) AS COSTPROD1,
		                    ISNULL(COSTPROD2,0.00) AS COSTPROD2,
		                    ISNULL(COSTPROD3,0.00) AS COSTPROD3,
		                    ISNULL(COSTPOSTG,0.00) AS COSTPOSTG,
		                    ISNULL(COSTRESP1,0.00) AS COSTRESP1, 
		                    ISNULL(COSTRESP2,0.00) AS COSTRESP2, 
		                    ISNULL(COSTRESP3,0.00) AS COSTRESP3, 
		                    ISNULL(sQTYMAIL,0) AS sQTYMAIL, 
		                    ISNULL(sMONY,0) AS sMONY,
		                    ISNULL(sPEOPLE,0) AS sPEOPLE,
		                    ISNULL(sGROSS,0.00) AS sGROSS,
		                    ISNULL(sNET,0.00) AS sNET, 
		                    ISNULL(sCOST,0.00) AS sCOST, 
		                    ISNULL(sGROSSPM,0.00) AS sGROSSPM, 
		                    ISNULL(sNETPM,0.00) AS sNETPM, 
		                    ISNULL(sCOSTPM,0.00) AS sCOSTPM, 
		                    ISNULL(sGROSSRSP,0.00) AS sGROSSRSP, 
		                    ISNULL(sNETRSP,0.00) AS sNETRSP, 
		                    ISNULL(sCOSTRSP,0.00) AS sCOSTRSP, 
		                    ISNULL(sRSPPCT,0.00) AS sRSPPCT, 
		                    ISNULL(isLM_SOURCE,0) AS isLM_SOURCE, 
		                    ISNULL(COMMENT,'') AS COMMENT, 

                            Case When UPDATEDON = '1/1/1900' Then null
                            Else
                            ISNULL(UPDATEDON,null) End AS UPDATEDON,
		                    ISNULL(universe,0) AS universe, 
		                    ISNULL(PROGSETID,0) AS PROGSETID, 
		                    ISNULL(SPCEVNTID,0) AS SPCEVNTID, 
		                    ISNULL(COSTACTI,0.00) AS COSTACTI, 
		                    ISNULL(COSTPERC,0.00) AS COSTPERC, 
		                    ISNULL(COSTFLAT,0.00) AS COSTFLAT, 
		                    
		                    Case When FIRSTACTI = '1/1/1900' Then null
                            Else
                            ISNULL(FIRSTACTI,null) End AS FIRSTACTI,

                            Case When LASTACTI = '1/1/1900' Then null
                            Else
                            ISNULL(LASTACTI,null) End AS LASTACTI,

		                    ISNULL(sACTIVITY,0) AS sACTIVITY, 
		                    ISNULL(sPEOPLEi,0) AS sPEOPLEi, 
		                    ISNULL(sRSPPCTi,0.00) AS sRSPPCTi,

                            ISNULL(teleM_completedCalls, 0) AS teleM_completedCalls,  
                            ISNULL(teleM_noPledges, 0) AS teleM_noPledges,  
                            ISNULL(teleM_totalPledges, 0.00) AS teleM_totalPledges,  
                            ISNULL(teleM, null) AS teleM,
                               
                            NonDonorCount
 
                            FROM SOURCE";

            const string _sql_base_donation_totals =
                            @"IF OBJECT_ID('tempdb..#donationTOTALSinitial') IS NOT NULL 
                            drop table #donationTOTALSinitial

                       	    IF OBJECT_ID('tempdb..#donationTOTALSfinal') IS NOT NULL 
                                drop table #donationTOTALSfinal

                            -- initial query for totals
                            SELECT MIN(S.FIRSTCAGE) AS FIRSTCAGE,
                            MAX(S.LASTCAGE) AS LASTCAGE,
                            SUM(ISNULL(S.SPEOPLE,0)) AS DONORS,
                            SUM(ISNULL(S.SMONY,0)) AS DONATIONS, 
                            SUM(ISNULL(S.SGROSS,0)) AS GROSS,
                            SUM((( ISNULL(S.COSTPROD1,0)+ISNULL(S.COSTPROD2,0)+ISNULL(S.COSTPROD3,0)+ISNULL(S.COSTPOSTG,0) )*ISNULL(S.SQTYMAIL,0))+( ISNULL(S.COSTRESP1,0)+ISNULL(S.COSTRESP2,0)+ISNULL(S.COSTRESP3,0) )*ISNULL(S.SMONY,0)) AS COST,
                            SUM(ISNULL(S.SGROSS,0))-SUM((( ISNULL(S.COSTPROD1,0)+ISNULL(S.COSTPROD2,0)+ISNULL(S.COSTPROD3,0)+ISNULL(S.COSTPOSTG,0) )*ISNULL(S.SQTYMAIL,0))+( ISNULL(S.COSTRESP1,0)+ISNULL(S.COSTRESP2,0)+ISNULL(S.COSTRESP3,0) )*S.SMONY) AS NET
                            INTO #donationTOTALSinitial
                            FROM PROGRAM P, PACKAGE K, SOURCE S
                            WHERE P.PROGID = K.PROGID AND K.PKGEID = S.PKGEID AND K.PKGEID={0} AND S.SRCEID={1}
                            GROUP BY P.PROGTYPE, P.DESCRIP, K.PKGECODE, K.PKGEDESC, CAST(K.MAILDTE AS BIGINT)*1000000 + K.PKGEID, K.LASTCAGE, S.SRCECODE, S.SRCEDESC, LISTNO, LISTNOG 
                            ORDER BY FIRSTCAGE

                            -- consolidate totals into one record
                            SELECT MIN(FIRSTCAGE) AS FIRSTCAGE,
                                   MAX(LASTCAGE) AS LASTCAGE,
                                   SUM(donations) AS _totalDonationCount,
                                   SUM(gross) AS _totalDonationDollar,
                                   SUM(cost) AS _totalDonationCost,
                                   SUM(net) AS _totalDonationNet
                            INTO #donationTOTALSfinal
                            FROM #donationTOTALSinitial

                            -- query consolidated totals data

                                select ISNULL(FIRSTCAGE,'1/1/1900') AS FIRSTCAGE,
                                ISNULL(LASTCAGE,'1/1/1900') AS LASTCAGE,
                                ISNULL(_totalDonationCount,0) AS TotalDonationCount,
                                ISNULL(_totalDonationDollar,0.00) AS TotalDonationDollar,
                                ISNULL(_totalDonationCost,0.00) AS TotalDonationCost,
                                ISNULL(_totalDonationNet,0.00) AS TotalDonationNet
                            FROM #donationTOTALSfinal";

            const string _sql_base_activity_totals =
                            @"SELECT
                                    SRCEID,
                                    ISNULL(SUM(sACTIVITY),0) AS sACTIVITYsum,  
                                    ISNULL(SUM(sPEOPLEi),0) AS sPEOPLEisum
                                FROM SOURCE
                                WHERE SRCEID={0}
                                group by SRCEID
                            ";
            const string validate_check_if_used_sql_base =
            @" SELECT count(*) FROM MONY WHERE SRCEID = {0}";

            const string _sql_base_unique_source_code1 =
            @"SELECT TOP 5 * FROM SOURCE WHERE SRCECODE = '{0}'";

            const string _sql_base_unique_source_code2 =
            @"SELECT TOP 5 * FROM SOURCE WHERE SRCECODE = '{0}' AND SRCEID <> {1}";

            if (SQL == "allSources")
                return sql_allSources;

            if (SQL == "specificSource")
                return _sql_specificSource;

            if (SQL == "unique_source_code2")
                return _sql_base_unique_source_code2;

            if (SQL == "unique_source_code1")
                return _sql_base_unique_source_code1;

            if (SQL == "activity_totals")
                return _sql_base_activity_totals;

            if (SQL == "donation_totals")
                return _sql_base_donation_totals;

            if (SQL == "check_if_used")
                return validate_check_if_used_sql_base;

            if (SQL == "SOURCE_BUDGET")
                return _sql_Budget_Text;

            if (SQL == "line_chart")
                return _sql_base_line_chart;

            return String.Empty;
        }

        public List<source_lineCHARTdata> getSourceLineChartData(string sSTARTdt, string sENDdt, int PkgeID, int SrceID)
        {
            string _sql = String.Format(get_SQL("line_chart"),sSTARTdt, sENDdt, PkgeID, SrceID);
            return _dbFactory.getContext().Database.SqlQuery<source_lineCHARTdata>(_sql).ToList();
        }

        public List<_pieCHARTdata> getPieChartData(string _sql)
        {
            return _dbFactory.getContext().Database.SqlQuery<_pieCHARTdata>(_sql).ToList();
        }

        public List<_interactionLineCHARTdata> get_Interaction_LineChartData(string _sql)
        {
            return _dbFactory.getContext().Database.SqlQuery<_interactionLineCHARTdata>(_sql).ToList();
        }

        public string get_initiativeFromSourceID(int sourceID)
        {
            string _sql = String.Format("SELECT TOP 10 [PKGEID],[PROGID] ,[dtPROGID] ,[PKGECODE] ,RTRIM(LTRIM(ISNULL(PKGECODE,''))) + ' - ' + RTRIM(LTRIM(ISNULL(PKGEDESC,''))) as PKGEDESC ,[SENDACKW]  ,[SENDRECV] ,[COMMENT] ,[UPDATEDON] ,[MAILDTE] ,[LASTCAGE] ,[PROGSETID],[channelId],[initiativeTypeId], docFileName,docFileBinaries, docFileBy,docFileDate FROM PACKAGE WHERE PKGEID in (select PKGEID from SOURCE where SRCEID = {0} )", sourceID);

             //docFileName,docFileBinaries, docFileBy,docFileDate


            var q = _dbFactory.getContext().Database.SqlQuery<PACKAGE>(_sql);
            PACKAGE pkg = q.FirstOrDefault();
            return pkg.PKGEDESC;
        }

        public string get_initiativeFromPkgeID(int packageID)
        {
            string _sql = String.Format("SELECT TOP 10 [PKGEID],[PROGID] ,[dtPROGID] ,[PKGECODE] ,RTRIM(LTRIM(ISNULL(PKGECODE,''))) + ' - ' + RTRIM(LTRIM(ISNULL(PKGEDESC,''))) as PKGEDESC ,[SENDACKW]  ,[SENDRECV] ,[COMMENT] ,[UPDATEDON] ,[MAILDTE] ,[LASTCAGE] ,[PROGSETID],[channelId],[initiativeTypeId],docFileName,docFileBinaries, docFileBy,docFileDate FROM PACKAGE WHERE PKGEID ={0}", packageID);
            var q = _dbFactory.getContext().Database.SqlQuery<PACKAGE>(_sql);
            PACKAGE pkg = q.FirstOrDefault();
            return pkg.PKGEDESC;
        }

        public pmSPCEVNT get_evntFromSourceID(int sourceID)
        {
            string sql = String.Format("SELECT TOP 20 ISNULL(SPCEVNTID,0) AS SPCEVNTID, RTRIM(LTRIM(ISNULL(EVNTCODE,''))) as EVNTCODE, EVNTDESC FROM pmSPCEVNT where SPCEVNTID in (select SPCEVNTID from SOURCE where SRCEID = {0})", sourceID);

            var q = _dbFactory.getContext().Database.SqlQuery<pmSPCEVNT>(sql);
            return q.FirstOrDefault();
        }

        public List<pmSPCEVNT> get_all_evntCode()
        {
            string sql = String.Format("SELECT * FROM pmSPCEVNT ORDER BY EVNTCODE");

            var q = _dbFactory.getContext().Database.SqlQuery<pmSPCEVNT>(sql);
            return q.ToList();
        }

        private string ApostropheCheck(string sApostropheCheckThis)
        {
            // take care of apostrophe                                             
            string sApostropheCheck_String = sApostropheCheckThis;
            return sApostropheCheck_String.Replace("'", "''");
        }

        public SOURCE get_source_details(int sourceid)
        {
            string _sql = get_SQL("specificSource");
            string sql = string.Format("{0} WHERE SRCEID ={1}", _sql, sourceid);
            var q = _dbFactory.getContext().Database.SqlQuery<SOURCE>(sql);
            return q.FirstOrDefault();
        }

        public ActivityTotalsFinal get_source_activity_details(int sourceid, int packageid)
        {
            //get activity totals
            string _activitySQL = get_SQL("activity_totals");
            string _activitySQL_final = string.Format(_activitySQL, sourceid);
            return _dbFactory.getContext().Database.SqlQuery<ActivityTotalsFinal>(_activitySQL_final).FirstOrDefault();
        }

        public DonationTotalsFinal get_source_donation_details(int sourceid, int packageid)
        {
            //get donation totals
            string _donationSQL = get_SQL("donation_totals");
            string _donationSQL_final = string.Format(_donationSQL, packageid, sourceid);
            return _dbFactory.getContext().Database.SqlQuery<DonationTotalsFinal>(_donationSQL_final).FirstOrDefault();

        }

        public bool check_unqiueness(SOURCE _SOURCE)
        {
            // take care of apostrophe
            string sApostrophe_check_code = ApostropheCheck(_SOURCE.SRCECODE);

            string sql_code = "";
            if (_SOURCE.SRCEID > 0)   // Existing Record
                sql_code = String.Format(get_SQL("unique_source_code2"), sApostrophe_check_code, _SOURCE.SRCEID);
            else
                sql_code = String.Format(get_SQL("unique_source_code1"), sApostrophe_check_code);

            var code = _dbFactory.getContext().Database.SqlQuery<SOURCE>(sql_code);

            return code.ToList().Count == 0 ? true : false;
        }

        public bool check_record_exists(int sourceid)
        {
            string okToDelete_SQL = get_SQL("check_if_used");
            string okToDelete_SQL_final = string.Format(okToDelete_SQL, sourceid);
            var q = _dbFactory.getContext().Database.SqlQuery<int>(okToDelete_SQL_final);
            if(q.FirstOrDefault()>0)
                return true;
            return false;
        }

        public void Add(SOURCE _SOURCE)
        {
            _sourceRepository.Add(_SOURCE);
            _unitOfWork.commit();
        }

        public void Update(SOURCE _SOURCE)
        {
            _sourceRepository.Update(_SOURCE);
            _unitOfWork.commit();
        }

        public void Delete(SOURCE _SOURCE)
        {
            _sourceRepository.Delete(_SOURCE);
            _unitOfWork.commit();
        }

        public List<SOURCE_ext> get_all_sources(string where, string orderBy, int pageSize, int pageNo)
        {
            string base_sql = get_SQL("allSources");
            string sql = (!string.IsNullOrEmpty(where)) ? string.Format("{0} WHERE {1}", base_sql, where) : string.Format("{0}", base_sql);
            string final_sql = String.Format("EXEC dbo.get_pagedDataSet '{0}', '{1}', {2}, {3}", sql, orderBy, pageSize, pageNo);
            var q = _dbFactory.getContext().Database.SqlQuery<SOURCE_ext>(final_sql);
            var a = q.ToList();
            return q.ToList();
        }

        public void applyCostInPkge(string changes, int packgID)
        {
            string CommandText = "UPDATE SOURCE SET {0} WHERE PKGEID = {1}";
            CommandText = String.Format(CommandText, changes, packgID);
            _dbFactory.getContext().Database.ExecuteSqlCommand(CommandText);
        }
    }
}