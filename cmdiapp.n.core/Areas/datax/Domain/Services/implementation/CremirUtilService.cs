﻿using System;
using System.Collections.Generic;
using System.Configuration;
using System.Linq;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Text;
using System.Web;

namespace cmdiapp.n.core.Areas.datax.Domain.Services.implementation
{
    /// <summary>
    /// Implementation of <see cref="ICremirUtilService"/>
    /// </summary>
    public class CremirUtilService : ICremirUtilService
    {
        private readonly string _apiKey;
        private readonly string _baseUrl;

        public CremirUtilService()
        {
            _apiKey = ConfigurationManager.AppSettings.Get("cremirApiKey");
            if (string.IsNullOrEmpty(_apiKey))
            {
                throw new Exception("Cremir API Key was null or empty.");
            }
            _baseUrl = ConfigurationManager.AppSettings.Get("cremirUrl");
            if (string.IsNullOrEmpty(_baseUrl))
            {
                throw new Exception("Cremir Url was null or empty.");
            }
        }
        /// <summary>
        /// Gets the base URL that locates Cremir
        /// </summary>
        public string BaseUrl => _baseUrl;
        /// <summary>
        /// Adds required authorization header to http request
        /// </summary>
        /// <param name="request">the http request to add authorization to</param>
        public void AddAuthToRequest(HttpRequestMessage request)
        {
            request.Headers.Authorization = new AuthenticationHeaderValue(
                "Basic",
                Convert.ToBase64String(Encoding.UTF8.GetBytes($"user:{_apiKey}")));
        }
    }
}