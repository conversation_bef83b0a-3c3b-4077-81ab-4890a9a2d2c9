﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Data;
using System.Linq;
using System.Web.Mvc;
using System.Data.Linq;
using System.ComponentModel.DataAnnotations.Schema;

using cmdiapp.n.core.Domain.ViewModels;
using cmdiapp.n.core.Areas.crm.Domain.Data;

namespace cmdiapp.n.core.Areas.crm.Domain.Models
{
    public class excepltrExport : _entityBase_crm, iItemType
    {
        public int MID { get; set; }
        public int PID1 { get; set; }
        public string PREFIX1 { get; set; }
        public string FNAME1 { get; set; }
        public string MNAME1 { get; set; }
        public string LNAME1 { get; set; }
        public string SUFFIX1 { get; set; }
        public string FULLNAME1 { get; set; }
        public int PID2 { get; set; }
        public string PREFIX2 { get; set; }
        public string FNAME2 { get; set; }
        public string MNAME2 { get; set; }
        public string LNAME2 { get; set; }
        public string SUFFIX2 { get; set; }
        public string FULLNAME2 { get; set; }
        public string SALUTATION { get; set; }
        public string ADDR1 { get; set; }
        public string ADDR2 { get; set; }
        public string STREET { get; set; }
        public string CITY { get; set; }
        public string STATE { get; set; }
        public string ZIP { get; set; }
        public string PLUS4 { get; set; }
        public string ZIP_PLUS4 { get; set; }
        public string EMAIL { get; set; }
        public string FAX { get; set; }
        public string ACCTCODE { get; set; }


        public int PID { get; set; }
        public string PREFIX { get; set; }
        public string FNAME { get; set; }
        public string MNAME { get; set; }
        public string LNAME { get; set; }
        public string SUFFIX { get; set; }
        public string NAME { get; set; }
        public string SPOUSENAME { get; set; }
        public string FULLSPOUSENAME { get; set; }
        public DateTime? BATCHDTE { get; set; }
        public decimal? AMT { get; set; }
        public string SRCECODE { get; set; }
        public decimal? CTDAMT { get; set; }
        public string SIGNATURE { get; set; }
        public decimal? EXCESS_AMT { get; set; }
    }
    public class excepltr_data
    {
        public string FUNDCODE { get; set; }

        public string EXCEP { get; set; }

        public bool ADDNOTE { get; set; }

        public string NOTE { get; set; }

        public DateTime NOTEDATEc { get; set; }

        public DateTime NOTEDATE { get; set; }

        public string UPGRADEMSG { get; set; }

        public bool canexport { get; set; }
    }

}
