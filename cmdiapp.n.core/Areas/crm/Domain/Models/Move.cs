﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Data;
using System.Linq;
using System.Web.Mvc;
using System.Data.Linq;
using System.ComponentModel.DataAnnotations.Schema;

using cmdiapp.n.core.Domain.ViewModels;
using cmdiapp.n.core.Areas.crm.Domain.Data;

namespace cmdiapp.n.core.Areas.crm.Domain.Models
{
    [Table("MOVE")]
    public class MOVE : _entityBase_crm, iItemType
    {
        [Column]
        [Key]
        public int MOVEID { get; set; }
        [Column]
        public int PID { get; set; }
        [Column]
        public string SUBJECT { get; set; }
        [Column]
        public Int16? ACTTYPEID { get; set; }
        [Column]
        public Int16? PURPOSEID { get; set; }
        [Column]
        public Int16? PIPELINEID { get; set; }
        [Column]
        public DateTime? MOVEDATE { get; set; }
        [Column]
        public int? RELMANAGERID { get; set; }
        [Column]
        public string NOTE { get; set; }
        [Column]
        public DateTime? UPDATEDON { get; set; }
        [Column]
        public decimal? ASKING_AMT_LOW { get; set; }
        [Column]
        public decimal? ASKING_AMT_HIGH { get; set; }
        [Column]
        public string RELMANAGER2 { get; set; }
        [Column]
        public Int16? MOVEPLANID { get; set; }
        [Column]
        public decimal? ACCEPT_AMT { get; set; }
        [Column]
        public Int16? CREATEDBY { get; set; }
        [Column]
        public DateTime? DUEDATE { get; set; }
        [Column]
        public Int16? MOVERESULTID { get; set; }
        [Column]
        public DateTime? DONEON { get; set; }
        [Column]
        public Int16? DONEBY { get; set; }
        [Column]
        public bool? COMPLETED { get; set; }
    }

    public class moveTask
    {
        public int ACTID { get; set; }
        public string SUBJECT { get; set; }
        public string ACTTYPE { get; set; }
        public string ASK { get; set; }
        public DateTime? DUEDATE { get; set; }
    }

    public class moveNote
    {
        public int ACTHISTID { get; set; }
        public string SUBJECT { get; set; }
        public string ASK { get; set; }
        public DateTime? HISTDATE { get; set; }
    }

    public class MOVETAB
    {
        public int pipelineid { get; set; }

        public string label { get; set; }

        public int num { get; set; }

        public List<moveTask> opentasks { get; set; }

        public List<moveTask> donetasks { get; set; }

        public List<moveNote> notes { get; set; }
    }

    [Table("v_people_move")]
    public class v_people_move : _entityBase_crm, iItemType
    {
        [Column]
        [Key]
        public int MOVEID { get; set; }
        [Column]
        public int PID { get; set; }
        [Column]
        public Int16 PEOTYPEID { get; set; }
        [Column]
        public string PEOTYPE { get; set; }
        [Column]
        public string FULLNAME { get; set; }
        [Column]
        public string PREFIX { get; set; }
        [Column]
        public string FNAME { get; set; }
        [Column]
        public string MNAME { get; set; }
        [Column]
        public string LNAME { get; set; }
        [Column]
        public string SUFFIX { get; set; }
        [Column]
        public string SUBJECT { get; set; }
        [Column]
        public DateTime? MOVEDATE { get; set; }
        [Column]
        public DateTime? DUEDATE { get; set; }
        [Column]
        public Int16? PIPELINEID { get; set; }
        [Column]
        public string PIPELINE { get; set; }
        [Column]
        public Int16? MOVEPLANID { get; set; }
        [Column]
        public string MOVEPLAN { get; set; }
        [Column]
        public decimal? ASKING_AMT_LOW { get; set; }
        [Column]
        public decimal? ACCEPT_AMT { get; set; }
        [Column]
        public int? RELMANAGERID { get; set; }
        [Column]
        public string RELMANAGER { get; set; }
        [Column]
        public Int16? CREATEDBY { get; set; }
        [Column]
        public string CREATEUSER { get; set; }
        [Column]
        public string NOTE { get; set; }
        private Int16? _moveresultid;
        [Column]
        public Int16? MOVERESULTID
        {
            get { return this._moveresultid == null ? 0 : this._moveresultid; }
            set { this._moveresultid = value; }
        }
        [Column]
        public string MOVERESULT { get; set; }
        [Column]
        public int OPENTASKS { get; set; }
        [Column]
        public int COMPLETETASKS { get; set; }
        [Column]
        public int NOTES { get; set; }
        [Column]
        public DateTime? NEXTTASKDUE { get; set; }
        [Column]
        public string NEXTTASK { get; set; }
        [Column]
        public decimal? LGIFT { get; set; }
        [Column]
        public DateTime? LGIFTDTE { get; set; }
        [Column]
        public decimal? HPC { get; set; }
        [Column]
        public decimal? CTDAMT { get; set; }
        [Column]
        public decimal? CUMTOT { get; set; }
        [Column]
        public string ADDRESS { get; set; }
        [Column]
        public string STREET { get; set; }
        [Column]
        public string ADDR1 { get; set; }
        [Column]
        public string ADDR2 { get; set; }
        [Column]
        public string CITY { get; set; }
        [Column]
        public string STATE { get; set; }
        [Column]
        public string ZIP { get; set; }
        [Column]
        public string PLUS4 { get; set; }
        [Column]
        public string HMPHONE { get; set; }
        [Column]
        public string BSPHONE { get; set; }
        [Column]
        public string EMAIL { get; set; }
        [Column]
        public int ALLOPENTASKS { get; set; }
        [Column]
        public int ALLCOMPLETETASKS { get; set; }
        [Column]
        public byte[] PICTURE { get; set; }
        [Column]
        public string OVERDUE { get; set; }
    }

    [NotMapped]
    public class v_people_move_ext : v_people_move
    {
        public int count_ { get; set; }
        public Int64 rowNo { get; set; }
    }

    [Table("v_move_summary")]
    public class v_move_summary : _entityBase_crm, iItemType
    {
        [Column]
        [Key]
        public Int16 PIPELINEID { get; set; }
        [Column]
        public string DESCRIP { get; set; }
        [Column]
        public int MOVECNT { get; set; }
        [Column]
        public decimal? ASKTOT { get; set; }
    }

    public class move_dashboard
    {
        public Int16 PIPELINEID { get; set; }
        public string DESCRIP { get; set; }
        public int MOVECNT { get; set; }
        public decimal? ASKTOT { get; set; }
        public int PAGE { get; set; }
        public List<v_people_move_ext> MOVEREC { get; set; }
    }

    [Table("MOVEDOC")]
    public class MOVEDOC : _entityBase_crm, iItemType
    {
        [Column]
        public int MOVEDOCID { get; set; }

        [Column]
        public int? MOVEID { get; set; }

        [Column]
        public string FILENAME { get; set; }

        [Column]
        public byte[] CONTENT { get; set; }

        [Column("_updating_uid")]
        public short? updating_uid { get; set; }
    }

    [Table("jtMOVEACTION")]
    public class jtMOVEACTION : _entityBase_crm, iItemType
    {
        [Column]
        [Key]
        public int jtMOVEACTIONID { get; set; }

        [Column]
        public int? ID { get; set; }

        [Column]
        public int? MOVEID { get; set; }

        [Column]
        public Int16? PIPELINEID { get; set; }

        [Column]
        public DateTime? UPDATEDON { get; set; }

        [Column("_updating_uid")]
        public short? updating_uid { get; set; }
    }

    [Table("jtMOVEEVNT")]
    public class jtMOVEEVNT : _entityBase_crm, iItemType
    {
        [Column]
        [Key]
        public int jtMOVEEVNTID { get; set; }

        [Column]
        public int? jtSPCEVNTID { get; set; }

        [Column]
        public int? MOVEID { get; set; }

        [Column]
        public Int16? PIPELINEID { get; set; }

        [Column]
        public DateTime? UPDATEDON { get; set; }

        [Column("_updating_uid")]
        public short? updating_uid { get; set; }
    }

    [Table("jtMOVEMONY")]
    public class jtMOVEMONY : _entityBase_crm, iItemType
    {
        [Column]
        [Key]
        public int jtMOVEMONYID { get; set; }

        [Column]
        public int? MID { get; set; }

        [Column]
        public int? MOVEID { get; set; }

        [Column]
        public Int16? PIPELINEID { get; set; }

        [Column]
        public DateTime? UPDATEDON { get; set; }

        [Column("_updating_uid")]
        public short? updating_uid { get; set; }
    }

    [Table("jtMOVEPLEG")]
    public class jtMOVEPLEG : _entityBase_crm, iItemType
    {
        [Column]
        [Key]
        public int jtMOVEPLEGID { get; set; }

        [Column]
        public int? PLEDGEID { get; set; }

        [Column]
        public int? MOVEID { get; set; }

        [Column]
        public Int16? PIPELINEID { get; set; }

        [Column]
        public DateTime? UPDATEDON { get; set; }

        [Column("_updating_uid")]
        public short? updating_uid { get; set; }
    }
}