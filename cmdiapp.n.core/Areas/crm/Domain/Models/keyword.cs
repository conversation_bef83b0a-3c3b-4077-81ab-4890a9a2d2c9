﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Data;
using System.Linq;
using System.Web.Mvc;
using System.Data.Linq;
using System.ComponentModel.DataAnnotations.Schema;

using cmdiapp.n.core.Domain.ViewModels;
using cmdiapp.n.core.Areas.crm.Domain.Data;

namespace cmdiapp.n.core.Areas.crm.Domain.Models
{
    [Table("dmKWRD")]
    public class dmKWRD : _entityBase_crm
    {
        [Column]
        [Key]
        public int KWRDID { get; set; }

        [Column]
        [Required(ErrorMessage = "The Keyword is required.")]
        public string KWRD { get; set; }

        [Column]
        public DateTime? LASTUSED { get; set; }

        [Column]
        public DateTime? UPDATEDON { get; set; }

        [Column]
        public string KWRDDESC { get; set; }
    }
        
    [Table("jtKWRD")]
    public class jtKWRD : _entityBase_crm, iItemType
    {
        [Column]
        [Key]
        public int JTKWRDID { get; set; }

        [Column]
        public int PID { get; set; }

        [Column]
        public int KWRDID { get; set; }
        [ForeignKey("KWRDID")]
        public virtual dmKWRD dmKWRD { get; set; }

        [Column]
        public DateTime? UPDATEDON { get; set; }
        public string UPDATEDONs
        {
            get
            {
                return (UPDATEDON == null ? "" : string.Format("{0:MM/dd/yyyy htt}", UPDATEDON));
            }
        }

        [Column("_updating_uid")]
        public short? updating_uid { get; set; }

    }


    [Table("jtChannelSuppressionFlag")]
    public class jtChannelSuppressionFlag : _entityBase_crm, iItemType
    {
        [Column]
        [Key]
        public int jtChannelSuppressionFlagId { get; set; }

        [Column]
        public int FLAGID { get; set; }
        
        [Column]
        public int channelId { get; set; }
        
        [Column]
        public DateTime? createdAt { get; set; }
        public string createdAts
        {
            get
            {
                return (createdAt == null ? "" : string.Format("{0:MM/dd/yyyy htt}", createdAt));
            }
        }

    }


    public class dmKWRD_ext
    {
        public int KWRDID { get; set; }

        public string KWRD { get; set; }

        public DateTime? LASTUSED { get; set; }

        public DateTime? UPDATEDON { get; set; }

        public string KWRDDESC { get; set; }

        public int count_ { get; set; }

        public Int64 rowNo { get; set; }

    }

}