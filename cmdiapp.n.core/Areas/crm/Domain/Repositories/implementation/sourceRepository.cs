﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Data.Linq;
using cmdiapp.n.core.Areas.crm.Domain.Models;

namespace cmdiapp.n.core.Areas.crm.Domain.Repositories
{
    internal class sourceRepository : _baseRepository<SOURCE>, IsourceRepository
    {
        public sourceRepository(I__DBFactory databaseFactory)
            : base(databaseFactory)
        {

        }
    }
}
