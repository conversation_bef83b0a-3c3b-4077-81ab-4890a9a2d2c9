﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Data;
using System.Linq;
using System.Web.Mvc;
using System.Data.Linq;
using System.ComponentModel.DataAnnotations.Schema;

using cmdiapp.n.core.Domain.ViewModels;
using cmdiapp.n.core.Areas.crm.Domain.Data;

namespace cmdiapp.n.core.Areas.crm.Domain.Models
{
    [Table("MONY")]
    public class MONY : _entityBase_crm, iItemType
    {
        [Column]
        [Key]
        public int MID { get; set; }

        [Column]
        public int PID { get; set; }
        [ForeignKey("PID")]
        public virtual people PEOPLE { get; set; }

        [Column]
        public Nullable<short> MONYCODE { get; set; }

        [Column]
        public Int16 FUNDID { get; set; }

        [Column]
        public int SRCEID { get; set; }

        [Column]
        public Nullable<short> SRCTYPEID { get; set; }

        [Column]
        public Nullable<Int16> CENTERID { get; set; }

        [Column]
        public Nullable<Int16> CAMPGNID { get; set; }

        [Column]
        public Int16 MONYTYPEID { get; set; }

        [Column]
        public Nullable<int> BATCHID { get; set; }

        [Column]
        public string BATCHNO { get; set; }

        // Updated by Lydia on 11/5/2013
        [Column]
        public DateTime BATCHDTE { get; set; }

        [Column]
        public Nullable<DateTime> ENTRYDTE { get; set; }

        [Column]
        public Nullable<decimal> AMT { get; set; }

        [Column]
        public string CKNO { get; set; }

        [Column]
        public Nullable<byte> COUNTER { get; set; }

        [Column]
        public Nullable<byte> SOFTMONEY { get; set; }

        [Column]
        public Nullable<byte> ADJTYPEID { get; set; }

        [Column]
        public Nullable<decimal> ADJAMT { get; set; }

        [Column]
        public Nullable<DateTime> ADJDTE { get; set; }

        [Column]
        public Nullable<int> ORIGMID { get; set; }

        [Column]
        public Nullable<Int16> UID { get; set; }

        [Column]
        public string COMMENT { get; set; }

        [Column]
        public Nullable<DateTime> UPDATEDON { get; set; }

        [Column]
        public Nullable<Int16> ACKW { get; set; }

        [Column]
        public Nullable<Int16> EXCEPID { get; set; }

        [Column]
        public Nullable<DateTime> EXCEPDTE { get; set; }

        [Column]
        public Nullable<int> TRACKNO { get; set; }

        [Column]
        public Nullable<int> IMAGEID { get; set; }

        [Column]
        public Nullable<int> dtBATCHID { get; set; }

        [Column]
        public Nullable<int> KEYINGID { get; set; }

        [Column]
        public string EARMARKED { get; set; }

        [Column]
        public string CCEXPMO { get; set; }

        [Column]
        public string CCEXPYR { get; set; }

        [Column]
        public string CCAUTHCODE { get; set; }

        [Column]
        public string CCREFNO { get; set; }

        [Column]
        public Nullable<Boolean> CCPROCESS { get; set; }

        [Column]
        public Nullable<Boolean> CCAPPROVED { get; set; }

        [Column]
        public string ccRESPMSG { get; set; }

        [Column]
        public Nullable<DateTime> RECVDTE { get; set; }

        [Column]
        public Nullable<Boolean> FECXFER { get; set; }

        [Column]
        public Nullable<Int16> MATCHID { get; set; }

        [Column]
        public Nullable<DateTime> MATCHDTE { get; set; }

        [Column]
        public Nullable<decimal> MATCHAMT { get; set; }

        [Column]
        public Nullable<Int16> MATCHEXCEPID { get; set; }

        [Column]
        public Nullable<DateTime> MATCHEXCEPDTE { get; set; }

        [Column]
        public Nullable<Int16> GIFTTYPEID { get; set; }

        [Column]
        public Nullable<decimal> TRACKAMT { get; set; }

        [Column("_UPDATING_UID")]
        public Nullable<Int16> UPDATING_UID { get; set; }

        [Column]
        public Nullable<Int16> CHANNELID { get; set; }

        [Column]
        public Nullable<Boolean> DISCLOSED { get; set; }
    }

    [NotMapped]
    public class MONY_ext : MONY {

        public virtual MONYADDI MONYADDI { get; set; }

        public virtual IEnumerable<MONYTRACK> MONYTRACK { get; set; }

        public virtual IEnumerable<MONYCONDUIT> MONYCONDUIT { get; set; }
    }

    [Table("MONYTRACK")]
    public class MONYTRACK : _entityBase_crm, iItemType
    {
        [Column]
        [Key]
        public int TRACKID { get; set; }

        [Column]
        public int MID { get; set; }

        [Column]
        public Nullable<int> TRACKNO { get; set; }

        [Column]
        public decimal TRACKAMT { get; set; }
    }

    [Table("MONYADDI")]
    public class MONYADDI : _entityBase_crm, iItemType
    {
        [Column]
        [Key]
        public int MONYADDIID { get; set; }

        [Column]
        public int MID { get; set; }

        [Column]
        public string TMCAMPAIGN { get; set; }

        [Column]
        public string TMUNIQNUM { get; set; }

        [Column]
        public Nullable<bool> RECURRED { get; set; }

        [Column]
        public Nullable<DateTime> RECURENDDTE { get; set; }

        [Column]
        public string REFERENCEID { get; set; }

        [Column]
        public bool MULTNAME { get; set; }

        [Column]
        public bool MULTSIGN { get; set; }

        [Column]
        public Nullable<int> WEBGIFTID { get; set; }

        [Column]
        public int? CAGEBATCHDTLID { get; set; }
    }

    [Table("MONYMEM")]
    public class MONYMEM : _entityBase_crm, iItemType
    {
        [Column]
        [Key]
        public int? MONYMEMID { get; set; }

        [Column]
        public int MID { get; set; }

        [Column]
        public int? MEMMID { get; set; }

        [Column]
        public Int16? MEMTYPEID { get; set; }

        [Column]
        public DateTime UPDATEDON { get; set; }

        [Column]
        public string MEMNAME { get; set; }

        [NotMapped]
        public int? PID { get; set; }

        [NotMapped]
        public string HONOREE { get; set; }
    }

    [NotMapped]
    public class MONYMEM_R : _entityBase_crm, iItemType
    {
        public int? MONYMEMID { get; set; }

        public int MID { get; set; }

        public int? MEMMID { get; set; }

        public Int16? MEMTYPEID { get; set; }

        public DateTime UPDATEDON { get; set; }

        public string MEMNAME { get; set; }

        public int? PID { get; set; }

        public string HONOREE { get; set; }
    }

    // Added on 1/11/14 by Lydia
    public class ImportAdjStep2ViewModel
    {
        public int impTypeFieldSel { get; set; }
        public int overwriteSel { get; set; }
        public int origmid { get; set; }
        public string mappingData { get; set; }
        public string uploadedFileName { get; set; }
    }

    public class ImportAdjStep5ViewModel
    {
        public string RECTYPE { get; set; }
        public int? ID { get; set; }
        public string STATUS { get; set; }
        public string PEOTYPE { get; set; }
        public string PREFIX { get; set; }
        public string FNAME { get; set; }
        public string MNAME { get; set; }
        public string LNAME { get; set; }
        public string SUFFIX { get; set; }
        public string EMPLOYER { get; set; }
        public string OCCUPATION { get; set; }
        public string SPOUSENAME { get; set; }
        public string ADDRTYPE { get; set; }
        public string STREET { get; set; }
        public string ADDR1 { get; set; }
        public string ADDR2 { get; set; }
        public string CITY { get; set; }
        public string STATE { get; set; }
        public string ZIP { get; set; }
        public string PLUS4 { get; set; }
        public string HMPHONE { get; set; }
        public string BSPHONE { get; set; }
        public string FAX { get; set; }
        public string CELLPHONE { get; set; }
        public string EMAIL { get; set; }
        public int? MID { get; set; }
        public DateTime? ADJDTE { get; set; }
        public string SRCECODE { get; set; }
        public decimal? AMT { get; set; }
        public int? TRACKNO { get; set; }
        public int? RECNO { get; set; }
        public string CCREFNO { get; set; }
        public string INFSALUT { get; set; }
        public string SALUTATION { get; set; }
        public string EARMARKED { get; set; }
        public string MONYCOMMENT { get; set; }
    }

    public class AdjustmentDetail
    {
        public string ADJUST { get; set; }
        public DateTime? ADJDTE { get; set; }
        public string USER { get; set; }
        public DateTime? TIMESTAMP { get; set; }
        public string NPID { get; set; }
        public string NNAME { get; set; }
        public string NSTREET { get; set; }
        public string NADDR1 { get; set; }
        public string NADDR2 { get; set; }
        public string NCISTZIP { get; set; }
        public int NMID { get; set; }
        public DateTime? NBATCHDTE { get; set; }
        public DateTime? NENTRYDTE { get; set; }
        public string NSOURCE { get; set; }
        public string NFUND { get; set; }
        public string NMONYTYPE { get; set; }
        public decimal NAMOUNT { get; set; }
        public string OPID { get; set; }
        public string ONAME { get; set; }
        public string OSTREET { get; set; }
        public string OADDR1 { get; set; }
        public string OADDR2 { get; set; }
        public string OCISTZIP { get; set; }
        public int OMID { get; set; }
        public DateTime? OBATCHDTE { get; set; }
        public DateTime? OENTRYDTE { get; set; }
        public string OSOURCE { get; set; }
        public string OFUND { get; set; }
        public string OMONYTYPE { get; set; }
        public decimal OAMOUNT { get; set; }
        public decimal ADJUSTAMT { get; set; }
    }

    public class vwMonyAdjustment : iItemType
    {
        public int PID { get; set; }
        public int MID { get; set; }
        public string NAME { get; set; }
        public string ADDR { get; set; }
        public DateTime BATCHDTE { get; set; }
        public string BATCHNO { get; set; }
        public Nullable<decimal> AMT { get; set; }
        public Nullable<decimal> ADJAMT { get; set; }
        public Nullable<DateTime> ADJDTE { get; set; }
        public string MONYTYPE { get; set; }
        public string FUNDCODE { get; set; }
        public string SRCECODE { get; set; }
        public string SRCEDESC { get; set; }
        public string ADJTYPE { get; set; }
        public string ADJDESC { get; set; }
        public Nullable<byte> COUNTER { get; set; }
        public Nullable<byte> SOFTMONEY { get; set; }
        public int ORIGMID { get; set; }
    }

    [NotMapped]
    public class vwMonyAdjustment_ext : vwMonyAdjustment
    {
        public int count_ { get; set; }
        public Int64 rowNo { get; set; }
    }

    public class MailHistList : _entityBase_crm, iItemType
    {
        public int OUTGOID { get; set; }
        public DateTime SELEDTE { get; set; }
        public DateTime? MAILDTE { get; set; }
        public string OUTGOCODE { get; set; }
        public string DESCRIP { get; set; }
        public string PKGECODE { get; set; }
        public string SRCECODE { get; set; }
        public string SRCEDESC { get; set; }
        public string RESPONDED { get; set; }
        public DateTime? BATCHDTE { get; set; }
        public decimal? AMT { get; set; }
    }

    [NotMapped]
    public class MailHistList_ext : MailHistList
    {
        public int count_ { get; set; }

        public Int64 rowNo { get; set; }
    }

    public class corpmatchLink : iItemType
    {
        public int dtMATCHID { get; set; }
        public int MID { get; set; }
        public int PID { get; set; }
        public string NAME { get; set; }
        public string GIFTTYPE { get; set; }
        public DateTime BATCHDTE { get; set; }
        public decimal AMT { get; set; }
        public string ACCTCODE { get; set; }
    }

    [NotMapped]
    public class corpmatchLink_ext : corpmatchLink
    {
        public int count_ { get; set; }

        public Int64 rowNo { get; set; }
    }

    [Table("dtMATCH")]
    public class dtMATCH : _entityBase_crm, iItemType
    {
        [Column]
        [Key]
        public int dtMATCHID { get; set; }

        [Column]
        public int MGMID { get; set; }

        [Column]
        public int MID { get; set; }

        [Column]
        public int CRMID { get; set; }

        [Column]
        public DateTime UPDATEDON { get; set; }
    }

    public class HonMemAckw : iItemType
    {
        public int PID { get; set; }

        public int MID { get; set; }

        public string PREFIX { get; set; }

        public string FNAME { get; set; }

        public string MNAME { get; set; }

        public string LNAME { get; set; }

        public string SUFFIX { get; set; }

        public string SALUTATION { get; set; }

        public string INFSALUT { get; set; }

        public string STREET { get; set; }

        public string ADDR1 { get; set; }

        public string ADDR2 { get; set; }

        public string CITY { get; set; }

        public string STATE { get; set; }

        public string ZIP { get; set; }

        public string PLUS4 { get; set; }

        public DateTime BATCHDTE { get; set; }

        public decimal AMT { get; set; }

    }

    public class bulkRedesignationData: iItemType
    {
        public string Mids { get; set; }
        public int FundId { get; set; }
        public DateTime? AdjDate { get; set; }
        public int UID { get; set; }
    }
}