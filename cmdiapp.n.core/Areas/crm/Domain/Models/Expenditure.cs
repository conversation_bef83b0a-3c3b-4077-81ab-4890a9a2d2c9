﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Data;
using System.Linq;
using System.Web.Mvc;
using System.Data.Linq;
using System.ComponentModel.DataAnnotations.Schema;

using cmdiapp.n.core.Domain.ViewModels;
using cmdiapp.n.core.Areas.crm.Domain.Data;

namespace cmdiapp.n.core.Areas.crm.Domain.Models
{
    [Table("v_receipts_list")]
    public class ReceiptsR : _entityBase_crm, iItemType
    {
        [Column]
        [Key]
        public int TXNID { get; set; }

        [Column]
        public int VENDORID { get; set; }

        [Column]
        public string VENDORTYPE { get; set; }

        [Column]
        public string ORGNAME { get; set; }

        [Column]
        public string PREFIX { get; set; }

        [Column]
        public string FNAME { get; set; }

        [Column]
        public string MNAME { get; set; }

        [Column]
        public string LNAME { get; set; }

        [Column]
        public string SUFFIX { get; set; }

        [Column]
        public string STREET { get; set; }

        [Column]
        public string ADDR1 { get; set; }

        [Column]
        public string CITY { get; set; }

        [Column]
        public string STATE { get; set; }

        [Column]
        public string ZIP { get; set; }

        [Column]
        public DateTime? TXNDTE { get; set; }

        [Column]
        public decimal? AMT { get; set; }

        [Column]
        public string FUNDCODE { get; set; }

        [Column]
        public string TRANSTYPE { get; set; }

        [Column]
        public string DESCRIPTION { get; set; }

        [Column]
        public string BANKACCT { get; set; }

        [Column]
        public bool MEMO { get; set; }

        [Column]
        public string MEMOTXT { get; set; }

        [Column]
        public string CHECKNO { get; set; }

        [Column]
        public string FEC_DESCRIPTION { get; set; }

        [Column]
        public string COMMENT { get; set; }

        [Column]
        public bool? IS1099 { get; set; }

        [Column]
        public string BATCHNO { get; set; }

    }

    [NotMapped]
    public class v_receipts_ext : ReceiptsR
    {
        public int count_ { get; set; }

        public Int64 rowNo { get; set; }
    }

    [Table("Txn")]
    public class Txn : _entityBase_crm, iItemType
    {
        [Column]
        [Key]
        public int TXNID { get; set; }
        [Column]
        public int? ENTITYID { get; set; }
        [Column]
        public short? TXNTYPEID { get; set; }
        [Column]
        public short? FUNDID { get; set; }
        [Column]
        public short? CENTERID { get; set; }
        [Column]
        public string CHKNO { get; set; }
        [Column]
        public short? FECTXNTYPEID { get; set; }
        [Column]
        public DateTime? TXNDTE { get; set; }
        [Column]
        public Decimal? AMT { get; set; }
        [Column]
        public short? TXNCODEID { get; set; }
        [Column]
        public short? TXNCATID { get; set; }
        [Column]
        public string DESCRIP { get; set; }
        [Column]
        public string FECDESCRIP { get; set; }
        [Column]
        public string MEMOTXT { get; set; }
        [Column]
        public bool ISMEMO { get; set; }
        [Column]
        public bool FORCEITEM { get; set; }
        [Column]
        public int? LINKTXNID { get; set; }
        [Column]
        public short? ELECTCDID { get; set; }
        [Column]
        public string ELECTYR { get; set; }
        [Column]
        public string ELECTOTH { get; set; }
        [Column]
        public bool SALT { get; set; }
        [Column]
        public string CDUPREFIX { get; set; }
        [Column]
        public string CDUFNAME { get; set; }
        [Column]
        public string CDUMNAME { get; set; }
        [Column]
        public string CDULNAME { get; set; }
        [Column]
        public string CDUSUFFIX { get; set; }
        [Column]
        public string CDUSTREET1 { get; set; }
        [Column]
        public string CDUSTREET2 { get; set; }
        [Column]
        public string CDUCITY { get; set; }
        [Column]
        public string CDUSTATE { get; set; }
        [Column]
        public string CDUZIP { get; set; }
        [Column]
        public string CDUPLUS4 { get; set; }
        [Column]
        public DateTime? UPDATEDON { get; set; }
        [Column]
        public Decimal? LINKSUM { get; set; }
        [Column]
        public short? UID { get; set; }
        [Column("_updating_uid")]
        public short? updating_uid { get; set; }
        [Column]
        public short? MONYTYPEID { get; set; }
        [Column]
        public short? EVNTTYPEID { get; set; }
        [Column]
        public Nullable<short> ADJTYPEID { get; set; }
        [Column]
        public Nullable<DateTime> ADJDTE { get; set; }
        [Column]
        public Nullable<byte> COUNTER { get; set; }
        [Column]
        public Nullable<int> ORIGTXNID { get; set; }
        [Column]
        public Nullable<DateTime> DISSEMDTE { get; set; }
        [Column]
        public string SOP { get; set; }
        [Column]
        public string SOFECCANID { get; set; }
        [Column]
        public string SOCANPFX { get; set; }
        [Column]
        public string SOCANFN { get; set; }
        [Column]
        public string SOCANMN { get; set; }
        [Column]
        public string SOCANLN { get; set; }
        [Column]
        public string SOCANSFX { get; set; }
        [Column]
        public string SOCANOFFICE { get; set; }
        [Column]
        public string SOCANSTATE { get; set; }
        [Column]
        public string SOCANDIST { get; set; }
        [Column]
        public string SIGNPFX { get; set; }
        [Column]
        public string SIGNFN { get; set; }
        [Column]
        public string SIGNMN { get; set; }
        [Column]
        public string SIGNLN { get; set; }
        [Column]
        public string SIGNSFX { get; set; }
        [Column]
        public bool? IS1099 { get; set; }
        [Column]
        public Nullable<DateTime> SIGNDTE { get; set; }
        [Column]
        public Nullable<DateTime> CLEARDATE { get; set; }
        [Column]
        public string BATCHNO { get; set; }
        [Column]
        public string DESGCMTENAME { get; set; }
        [Column]
        public string DESGCMTEID { get; set; }
        [Column]
        public bool? DESIGNATED { get; set; }
        [Column]
        public string SUBCMTENAME { get; set; }
        [Column]
        public string SUBCMTEID { get; set; }
        [Column]
        public string SUBSTREET1 { get; set; }
        [Column]
        public string SUBSTREET2 { get; set; }
        [Column]
        public string SUBCITY { get; set; }
        [Column]
        public string SUBSTATE { get; set; }
        [Column]
        public string SUBZIP { get; set; }
        [Column]
        public string SUBPLUS4 { get; set; }
        [Column]
        public string EVENTNAME { get; set; }
        [Column]
        public string EVENTTYPE { get; set; }
        [Column]
        public DateTime? CREATEDON { get; set; }
    }
    
    [Table("lkTXNTYPE")]
    public class lkTxnType : _entityBase_crm, iItemType
    {
        [Column]
        [Key]
        public short TXNTYPEID { get; set; }
        [Column]
        public string CODE { get; set; }
        [Column]
        public string DESCRIP { get; set; }
    }

    [Table("lkTXNCAT")]
    public class lkTxnCat : _entityBase_crm, iItemType
    {
        [Column]
        [Key]
        public short TXNCATID { get; set; }
        [Column]
        public string CODE { get; set; }
        [Column]
        public string DESCRIP { get; set; }
        [Column]
        public string FUNDCODE { get; set; }

        public string CODEDESCRIP
        {
            get
            {
                return string.Format("{0} - {1}", CODE, DESCRIP);
            }
        }
    }

    [Table("lkTXNCODE")]
    public class lkTxnCode : _entityBase_crm, iItemType
    {
        [Column]
        [Key]
        public short TXNCODEID { get; set; }
        [Column]
        public string CODE { get; set; }
        [Column]
        public string DESCRIP { get; set; }
        [Column]
        public string FUNDCODE { get; set; }

        public string CODEDESCRIP
        {
            get
            {
                return string.Format("{0} - {1}", CODE, DESCRIP);
            }
        }
    }

    [Table("lkFECTXNTYPE")]
    public class lkFECTxnType : _entityBase_crm, iItemType
    {
        [Column]
        [Key]
        public short FECTXNTYPEID { get; set; }
        [Column]
        public short TXNTYPEID { get; set; }
        [Column]
        public string FORM3 { get; set; }
        [Column]
        public string LINE { get; set; }
        [Column]
        public string DESCRIP { get; set; }
        [Column]
        public bool SHOWCANDINFO { get; set; }

        public string CODEDESCRIP
        {
            get
            {
                return string.Format("{0} - {1}", LINE, DESCRIP);
            }
        }
    }

    [Table("lkCHARTACCT")]
    public class lkChartAcct : _entityBase_crm, iItemType
    {
        [Column]
        [Key]
        public short CHARTACCTID { get; set; }

        [Column]
        public string DESCRIP { get; set; }

        [Column]
        public string ACCTCODE { get; set; }

        [Column]
        public short ACCTTYPEID { get; set; }

        public string CODEDESCRIP
        {
            get
            {
                return string.Format("{0} - {1}", ACCTCODE, DESCRIP);
            }
        }
    }

    [NotMapped]
    public class lkChartAcct_ext:lkChartAcct
    {
        public string TYPEDesc { get; set; }
    }

    [Table("lkFECDESC")]
    public class lkFECDesc : _entityBase_crm, iItemType
    {

        [Column]
        [Key]
        public int ID { get; set; }

        [Column]
        public string DESCRIP { get; set; }
    }

    [Table("lkELECTCD")]
    public class lkElectCD : _entityBase_crm, iItemType
    {
        [Column]
        [Key]
        public short ELECTCDID { get; set; }

        public string CODE { get; set; }

        [Column]
        public string DESCRIP { get; set; }

        public string CODEDESCRIP
        {
            get
            {
                return string.Format("{0} - {1}", CODE, DESCRIP);
            }
        }

    }

    [Table("ACCTXFER")]
    public class AccTxfer : _entityBase_crm, iItemType
    {
        [Column]
        [Key]
        public int ACCTXFERID { get; set; }
        [Column]
        public short CENTERIDFRM { get; set; }
        [Column]
        public short CENTERIDTO { get; set; }
        [Column]
        public DateTime? XFERDTE { get; set; }
        [Column]
        public short UID { get; set; }
    }

    [Table("jtTXNACCT")]
    public class jtTxnAcct : _entityBase_crm, iItemType
    {

        [Column]
        [Key]
        public int TXNACCTID { get; set; }
        [Column]
        public int TXNID { get; set; }
        [Column]
        public short CHARTACCTID { get; set; }
        [Column]
        public Decimal? AMT { get; set; }

    }

    [Table("lkEXPMONYTYPE")]
    public class lkExpMonyType : _entityBase_crm, iItemType
    {

        [Column]
        [Key]
        public short MONYTYPEID { get; set; }
        [Column]
        public string MONYTYPE { get; set; }
        [Column]
        public string DESCRIP { get; set; }
        [Column]
        public byte DEFFLAG { get; set; }
        [Column]
        public byte SYSTEM { get; set; }

        public string CODEDESCRIP
        {
            get
            {
                return string.Format("{0} - {1}", MONYTYPE, DESCRIP);
            }
        }
    }

    [Table("v_entity")]
    public class v_entity : _entityBase_crm, iItemType
    {
        [Column]
        [Key]
        public int ENTITYID { get; set; }
        [Column]
        public short? ENTITYTYPEID { get; set; }
        [Column]
        public string ENTTYPE { get; set; }
        [Column]
        public string ENTDESCRIP { get; set; }
        [Column]
        public string FULLNAME {get; set;}
        [Column]
        public string  FULLADDR { get; set; }
        [Column]
        public string PHONE { get; set; }
        [Column]
        public string FAX { get; set; }
        [Column]
        public string CELL { get; set; }
        [Column]
        public string EMAIL { get; set; }
        [Column]
        public string CONTACT { get; set; }
        [Column]
        public string TAXID { get; set; }
        [Column]
        public bool IS1099 { get; set; }
        [Column]
        public string EMPLOYER { get; set; }
        [Column]
        public string OCCUPATION { get; set; }
        [Column]
        public string FECCMTEID { get; set; }
        [Column]
        public string CANDNAME { get; set; }
        [Column]
        public string CANDFECID { get; set; }
        [Column]
        public string CANDOFFICE { get; set; }
        [Column]
        public string CANDSTATE { get; set; }
        [Column]
        public string CANDDIST { get; set; }
        [Column]
        public DateTime? UPDATEDON { get; set; }
        [Column]
        public string ORGNAME { get; set; }
        [Column]
        public string FNAME { get; set; }
        [Column]
        public string LNAME { get; set; }
        [Column]
        public string LINEADDR { get; set; }
        [Column]
        public string VENDACCTID { get; set; }        

    }

    [Table("v_entity_fullname")]
    public class v_entity_fullname : _entityBase_crm, iItemType
    {
        [Column]
        [Key]
        public int ENTITYID { get; set; }

        [Column]
        public string ENTTYPE { get; set; }

        [Column]
        public string FULLNAME { get; set; }

        [Column]
        public string NAME { get; set; }
    }
    public class v_entity_fullname_v2
    {
        public int ENTITYID { get; set; }
        
        public string FULLNAME_v2 { get; set; }
        
    }

    [Table("v_receipts_TxnAcctChart")]
    public class v_receipts_TxnAcctChart : _entityBase_crm, iItemType
    {
        [Column]
        [Key]
        public int TXNACCTID { get; set; }

        [Column]
        public int? TXNID { get; set; }

        [Column]
        public short CHARTACCTID { get; set; }

        [Column]
        public string DESCRIP { get; set; }

        [Column]
        public Nullable<decimal> AMT { get; set; }
    }

    public class ImportTxnStep2ViewModel
    {
        public int impTypeFieldSel { get; set; }
        public string impTypeFieldSelText { get; set; }

        public string impTxnTypeSel { get; set; }
        public string impTxnTypeSelText { get; set; }

        public string impFundCodeSel { get; set; }
        public string impFundCodeSelText { get; set; }

        public string impBankCodeSel { get; set; }
        public string impBankCodeSelText { get; set; }

        public string impLineNoSel { get; set; }
        public string impLineNoSelText { get; set; }

        public string mappingData { get; set; }
        public string uploadedFileName { get; set; }

        
    }

    public class ImportTxnStep5ViewModel
    {
        public string RECTYPE { get; set; }
        public int ENTITYID { get; set; }
        public string ENTITYTYPE { get; set; }
        public string ORGNAME { get; set; }
        public string PREFIX { get; set; }
        public string LNAME { get; set; }
        public string FNAME { get; set; }
        public string MNAME { get; set; }
        public string SUFFIX { get; set; }
        public string STREET1 { get; set; }
        public string STREET2 { get; set; }
        public string CITY { get; set; }
        public string STATE { get; set; }
        public string ZIP { get; set; }
        public string PLUS4 { get; set; }
        public string PHONE { get; set; }
        public string FAX { get; set; }
        public string CELL { get; set; }
        public string EMAIL { get; set; }
        public string CONTACT { get; set; }
        public string EMPLOYER { get; set; }
        public string OCCUPATION { get; set; }
        public string TAXID { get; set; }
        public string VENDACCTID { get; set; }
        public string FECCMTEID { get; set; }
        public DateTime? TXNDTE { get; set; }
        public decimal? AMT { get; set; }
        public string CHECKNO { get; set; }
        public string TXNTYPE { get; set; }
        public string FUNDCODE { get; set; }
        public string BANKACCT { get; set; }
        public string LINENO { get; set; }
        public string COMMENT { get; set; }
        public string FEC_DESCRIPTION { get; set; }
        public string ISMEMO { get; set; }
        public string MEMOTXT { get; set; }
        public string TRANSCAT { get; set; }
        public string ELECTION { get; set; }
        public string ELECTYR { get; set; }
        public string ELECTOTHER { get; set; }
        public int? LINKTXNID { get; set; }
        public string GLACCT_1 { get; set; }
        public decimal? GLAMT_1 { get; set; }
        public string GLACCT_2 { get; set; }
        public decimal? GLAMT_2 { get; set; }
        public string GLACCT_3 { get; set; }
        public decimal? GLAMT_3 { get; set; }
        public int? TXNNO { get; set; }
        public int? RECNO { get; set; }
        public string STATUS { get; set; }
        public string TXNCODE { get; set; }
        public string PAYTYPE { get; set; }
        public string IS1099 { get; set; }
        public DateTime? DISSEMDTE { get; set; }
        public string SOFECCANID { get; set; }
        public string SOCANPFX { get; set; }
        public string SOCANFN { get; set; }
        public string SOCANMN { get; set; }
        public string SOCANLN { get; set; }
        public string SOCANSFX { get; set; }
        public string SOCANOFFICE { get; set; }
        public string SOCANSTATE { get; set; }
        public string SOCANDIST { get; set; }
        public DateTime? SIGNDTE { get; set; }
        public string SIGNPFX { get; set; }
        public string SIGNFN { get; set; }
        public string SIGNMN { get; set; }
        public string SIGNLN { get; set; }
        public string SIGNSFX { get; set; }
        public string SOP { get; set; }
        /*Default Line No for Vendor Import 9/16/2015 */        
        public string LINENO3_EXP { get; set; }
        public string LINENO3_REC { get; set; }
        public string LINENO3P_EXP { get; set; }
        public string LINENO3P_REC { get; set; }
        public string LINENO3X_EXP { get; set; }
        public string LINENO3X_REC { get; set; }
        public string EVENTNAME { get; set; }
        public string EVENTTYPE { get; set; }
    }

    public class ImportTxnUpdateStep5ViewModel
    {
        public string RECTYPE { get; set; }
        public int? TXNNO { get; set; }
        public int? ENTITYID { get; set; }
        public DateTime? TXNDTE { get; set; }
        public decimal? AMT { get; set; }
        public string FUNDCODE { get; set; }
        public string LINENO { get; set; }
        public string PAYTYPE { get; set; }
        public string FEC_DESCRIPTION { get; set; }
        public string STATUS { get; set; }
    }

    public class TxnDetails : _entityBase_crm, iItemType
    {
        public int TXNID { get; set; }

        public DateTime? TXNDTE { get; set; }

        public string sCHQDTE { get; set; }

        public decimal? AMT { get; set; }

        public string sAMT { get; set; }
        
        public string sAMTinWords { get; set; }

        public string DESCRIP { get; set; }

        public string FECDESCRIP { get; set; }

        public string PAYEE { get; set; }

        public string ORGNAME { get; set; }

        public string PREFIX { get; set; }

        public string FNAME { get; set; }

        public string MNAME { get; set; }

        public string LNAME { get; set; }

        public string SUFFIX { get; set; }

        public string STREET1 { get; set; }

        public string STREET2 { get; set; }

        public string CITY { get; set; }

        public string STATE { get; set; }

        public string ZIP { get; set; }

        public string PLUS4 { get; set; }

        public string sdCHQDTE { get; set; }

    }

    public class TxnPrintDetails : _entityBase_crm, iItemType
    {
        public int TXNID { get; set; }

        public bool CheckOption { get; set; }

        public decimal VERSLDVAL { get; set; }

        public decimal HORSLDVAL { get; set; }
        
    }

    public class genericResponseTxnPrint
    {
        public string Key { get; set; }

        public bool success { get; set; }
        public string message { get; set; }
    }

    public class PrintAdjDetails : _entityBase_crm, iItemType
    {
        public decimal sHoriz { get; set; }

        public decimal sVerti { get; set; }
    }

    public class LinkTxnViewModel
    {
        public int TXNID { get; set; }
        public int LINKTXNID { get; set; }
    }

    public class LineNoForm3
    {
        public short TxnTypeId { get; set; }
        public string Form3 { get; set; }
        
    }

    [Table("lkTXNADJTYPE")]
    public class lkTXNADJTYPE : _entityBase_crm, iItemType
    {
        [Column]
        [Key]
        public Int16 ADJTYPEID { get; set; }

        [Column]
        public string ADJTYPE { get; set; }

        [Column]
        public string DESCRIP { get; set; }

        public string CODEDESCRIP
        {
            get
            {
                return string.Format("{0} - {1}", ADJTYPE, DESCRIP);
            }
        }
    }

    public class TxnBatPrintDetails : _entityBase_crm, iItemType
    {
        public int PAYID { get; set; }

        public bool CheckOption { get; set; }

        public decimal VERSLDVAL { get; set; }

        public decimal HORSLDVAL { get; set; }

    }

    public class iPrintBatCheque
    {
        public string sText { get; set; }
    }

    [Table("TXNDOC")]
    public class TXNDOC : _entityBase_crm, iItemType
    {
        [Column]
        public int TXNDOCID { get; set; }

        [Column]
        public int? TXNID { get; set; }

        [Column]
        public string FILENAME { get; set; }

        [Column]
        public byte[] CONTENT { get; set; }

        [Column("_updating_uid")]
        public short? updating_uid { get; set; }
    }

    public class inkindTxn
    {
        public int TXNID { get; set; }
    }

    [NotMapped]
    public class Txn2 : Txn
    {
        public List<v_receipts_TxnAcctChart> chartaccount { get; set; }  
        public txnUVdata ultimatevendor { get; set; }
    }

    public class txnLookUpData : iItemType
    {
        public List<dmFUND> FUNDCODE;
        public List<dmCENTER> BANKACCCODE;
        public List<lkFECTxnType> LINENO;
        public List<lkChartAcct> CHARTACCT;
        public List<lkExpMonyType> PAYMENTTYPE;
        public List<lkElectCD> ELECTIONCD;
        public List<lkTxnCode> TRANSCODE;
        public List<lkFECDesc> FECDESCRIPTION;
        public List<Vendor> VENDORS;
        public List<lkTXNADJTYPE> ADJTYPES;
        public List<canoffice> CANOFFICE;
        public List<fec_event_type> EVENTTYPEH4;
        public List<fec_event_type> EVENTTYPEH6;
    }

    public class txnUVdata : iItemType
    {
        public List<v_ultimate_vendor> UVDATA;
        public Decimal? LINKSUM;
    }

    public class fec_event_type
    {
        public int id { get; set; }

        public string text { get; set; }
    }
}