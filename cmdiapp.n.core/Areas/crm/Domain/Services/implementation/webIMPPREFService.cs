﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

using System.Data;
using System.Data.SqlClient;

using Ninject;
using Ninject.Web.Mvc;

using cmdiapp.n.core.Core.App_Start;
using cmdiapp.n.core.Areas.crm.Core;
using cmdiapp.n.core.Areas.crm.Domain.Models;
using cmdiapp.n.core.Areas.crm.Domain.ViewModels;
using cmdiapp.n.core.Areas.crm.Domain.Repositories;


namespace cmdiapp.n.core.Areas.crm.Domain.Services
{
    
    public class webIMPPREFService : IwebIMPPREFService
    {
        private I___unitOfWork _unitOfWork;
        private I__DBFactory _dbFactory;
        private IwebIMPPREFRepository _webIMPPREFRepository;

        public webIMPPREFService(I___unitOfWork unitOfWork, I__DBFactory dbFactory, IwebIMPPREFRepository webIMPPREFRepository)
        {
            _unitOfWork = unitOfWork;
            _dbFactory = dbFactory;
            _webIMPPREFRepository = webIMPPREFRepository;
        }
        
        public int DeleteMapping(int UID, int impType, string prefName)
        {
            string sql = string.Format("Delete from webimppref where uid={0} and imptype={1} and prefname='{2}'", UID, impType, prefName);
            int q = _dbFactory.getContext().Database.ExecuteSqlCommand(sql);
            return q;
        }
        /// <summary>
        /// This is used to get all pref names: Used for DDL purpose
        /// </summary>
        /// <param name="UID"></param>
        /// <param name="impTypeSel"></param>
        /// <returns></returns>
        public List<webIMPPREF> read_all_mapping_pref(int UID, int impTypeSel)
        {
            //Note:IMPPREFID is taken as imptype+1 as we are using this dataset to bind the DDL. We need unique combination of imptype and prefname
            string sql = "select distinct imptype, prefname, (imptype+1) as IMPPREFID, UID, '''' as FILECOLUMN, '''' as DBCOLUMN, '''' as MISCCOLUMN  from webimppref where uid =" + UID.ToString();

            var q = _dbFactory.getContext().Database.SqlQuery<webIMPPREF>(String.Format("EXEC dbo.get_allDataSet '{0}'", sql));

            return q.Where(x => x.IMPTYPE == impTypeSel).ToList();
        }

        /// <summary>
        /// This is used to get particular mapping info
        /// </summary>
        /// <param name="UID"></param>
        /// <param name="impTypeSel"></param>
        /// <param name="prefName"></param>
        /// <returns></returns>
        public List<webIMPPREF> get_mapping_for_prefname(int UID, int impTypeSel, string prefName)
        {
            string sql = string.Format("select * from webimppref where uid={0} and imptype={1} and prefname=''{2}''", UID, impTypeSel, prefName);

            var q = _dbFactory.getContext().Database.SqlQuery<webIMPPREF>(String.Format("EXEC dbo.get_allDataSet '{0}'", sql));

            return q.Where(x => x.IMPTYPE == impTypeSel).ToList();
        }


    }
}