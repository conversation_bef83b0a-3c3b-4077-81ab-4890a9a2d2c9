﻿using cmdiapp.n.core.Areas.crm.Domain.Models;
using cmdiapp.n.core.Areas.legislator.Domain.Models;
using cmdiapp.n.core.Domain.ViewModels;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace cmdiapp.n.core.Areas.legislator.Domain.Services
{
    public interface IContactChangeService
    {
        ContactChange Get(int id);

        List<ContactChange> GetByContact(int contactId, bool pendingOnly = false);

        ContactChange GetPreviouslyApprovedChange(int contactId);

        genericResponse Submit(ContactChange change);

        genericResponse UpdateStatus(int id, int statusId);

        List<ContactChangeStatus> GetAccessibleStatuses(int id);

        List<ContactChangeStatus> GetChangeStatuses();

        List<ContactChangeType> GetChangeTypes();

        ContactChangeDisplayPage GetLegislatorDisplay(
            string legislatorId, bool pendingOnly = true, int contactId = 0, int pageNo = 1);

        genericResponse AmendChangeField(int changeId, string fieldName, string value);
    }
}
