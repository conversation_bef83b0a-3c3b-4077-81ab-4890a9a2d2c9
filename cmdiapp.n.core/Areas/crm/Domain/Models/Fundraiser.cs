﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Data;
using System.Linq;
using System.Web.Mvc;
using System.Data.Linq;
using System.ComponentModel.DataAnnotations.Schema;

using cmdiapp.n.core.Domain.ViewModels;
using cmdiapp.n.core.Areas.crm.Domain.Data;

namespace cmdiapp.n.core.Areas.crm.Domain.Models
{
    // (view)v_trackno  -  Used to list all trackno with fundraiser name ]]

    [Table("v_trackno")]
    public class TracknoR : _entityBase_crm, iItemType
    {
        [Column]
        [Key]
        public int PID { get; set; }

        [Column]
        public int TRACKNO { get; set; }

        [Column]
        public string FUNDRAISER { get; set; }
    }

    [NotMapped]
    public class TracknoR_ext : TracknoR
    {
        public int count_ { get; set; }

        public Int64 rowNo { get; set; }
    }

    public class vFundraiser : iItemType
    {
        public string TRACKNO { get; set; }
        public string FUNDRAISER { get; set; }
    }

    [Table("FUNDRAISE")]
    public class FUNDRAISE : _entityBase_crm, iItemType
    {
        [Column]
        [Key]
        public int FUNDRAISEID { get; set; }

        [Column]
        public int? PID { get; set; }

        [Column]
        public int TRACKNO { get; set; }

        [Column]
        public DateTime? DATEFRM { get; set; }

        [Column]
        public DateTime? DATETO { get; set; }

        [Column]
        public bool ACTIVE { get; set; }

        [Column]
        public Decimal? COMMITMENT { get; set; }

        [Column]
        public Decimal? cRAISED { get; set; }

        [Column]
        public Decimal? cREFERRED { get; set; }

        [Column]
        public DateTime? UPDATEDON { get; set; }

        [Column]
        public Decimal? cPLEDGED { get; set; }

        [Column]
        public Decimal? cPLEDGEDREF { get; set; }

        [Column]
        public string TITLE { get; set; }

        [Column]
        public int? RECRUITERNO { get; set; }

        [Column]
        public string STATE { get; set; }

        [Column]
        public Decimal? cSHARED { get; set; }

        [Column]
        public Int16? LOBBYISTID { get; set; }

        [Column]
        public string VERIFIEDBY { get; set; }
        

        [Column]
        public DateTime? VERIFIEDDTE { get; set; }

        [NotMapped]
        public DateTime? VERIFIEDDTEc { get; set; }

        [Column]
        public Decimal? GIVENGOAL { get; set; }

        [Column]
        public int? PREV_RECRUITERNO { get; set; }

        [Column]
        public Decimal? PREV_COMMITMENT { get; set; }

        [Column]
        public Decimal? PREV_cRAISED { get; set; }

        [Column]
        public Decimal? PREV_cREFERRED { get; set; }

        [Column]
        public Decimal? PREV_cSHARED { get; set; }

        [Column]
        public DateTime? CreatedAt { get; set; }

        [Column]
        public Decimal? cEXCLUDE_ROLLUP { get; set; }

        [Column]
        public string IMAGEURL { get; set; }

        [Column]
        public Int16? UID { get; set; }
    }

    [Table("jtFUNDRAISE")]
    public class jtFUNDRAISE : _entityBase_crm, iItemType
    {
        [Column]
        [Key]
        public int jtFUNDRAISEID { get; set; }

        [Column]
        public int PID { get; set; }

        [Column]
        public int TRACKNO { get; set; }

        [Column]
        public DateTime? CREATEDON { get; set; }

        [Column("_updating_uid")]
        public Int16? updating_uid { get; set; }
    }

    public class DOWNLINESREPORTINPUT
    {
        public int TRACKNO { get; set; }

        public string REPORTLBLHEADER { get; set; }
    }

    public class RECDETAILS
    {
        public string RECNAME { get; set; }

        public int PID { get; set; }
    }

    public class ManageFundraiserGroup
    {
        public int FNDRGRPID { get; set; }
         
        public int FUNDRAISEID { get; set; }
    }

    public class TOTRAISE
    {
        public Decimal? TOTRAISED { get; set; }

    }

    public class DownlinesReportData : _entityBase_crm, iItemType
    {
        public int? PID { get; set; }

        public Decimal? cRAISED { get; set; }

        public Decimal? cSHARED { get; set; }

        public Decimal? cROLLUP { get; set; }

        public Decimal? cREFERRED { get; set; }

        public Decimal? PREV_cRAISED { get; set; }

        public Decimal? PREV_cREFERRED { get; set; }

        public Decimal? PREV_cSHARED { get; set; }

        public Decimal? PREV_ROLLUP { get; set; }

        public int? RECRUITERNO { get; set; }

        public int? TRACKNO { get; set; }

        public int? LEVEL { get; set; }

        public Decimal? cTOTAL { get; set; }

        public Decimal? PREV_cTOTAL { get; set; }

        public string FULLNAME { get; set; }

        public string FNAME { get; set; }

        public string MNAME { get; set; }

        public string LNAME { get; set; }

        public string PREFIX { get; set; }

        public string SUFFIX { get; set; }

        public string FULLADDR { get; set; }

        public string STREET { get; set; }

        public string ADDR1 { get; set; }

        public string ADDR2 { get; set; }

        public string CITY { get; set; }

        public string STATE { get; set; }

        public string ZIP { get; set; }

        public string HMPH_mask { get; set; }

        public string BUSPH_mask { get; set; }

        public string CELLPH_mask { get; set; }

        public string EMAIL { get; set; }

        public byte[] PICTURE { get; set; }

        public int? DONORID { get; set; }

        public string DONORNAME { get; set; }

        public string DFNAME { get; set; }

        public string DMNAME { get; set; }

        public string DLNAME { get; set; }

        public string DPREFIX { get; set; }

        public string DSUFFIX { get; set; }

        public string DONORADDR { get; set; }

        public string DSTREET { get; set; }

        public string DADDR1 { get; set; }

        public string DADDR2 { get; set; }

        public string DCITY { get; set; }

        public string DSTATE { get; set; }

        public string DZIP { get; set; }

        public string DHMPH_mask { get; set; }

        public string DBUSPH_mask { get; set; }

        public string DCELLPH_mask { get; set; }

        public string DEMAIL { get; set; }

        public int? MID { get; set; }

        public DateTime? DATE { get; set; }

        public string SOURCE { get; set; }

        public Decimal? AMOUNT  { get; set; }

        public bool  Shared { get; set; }

        public Int16?  Cyclegroup  { get; set; }

        public Decimal? donor_currentCycleTotal  { get; set; }

        public Decimal? donor_prevCycleTotal { get; set; }
    }

    public class FUNDRAISEDONOR
    {
        public string NAME { get; set; }

        public string DISPNAMECURR { get; set; }

        public string DISPNAMEPREV { get; set; }

        public int PID { get; set; }

        public Decimal? cRAISED { get; set; }

        public Decimal? cSHARED { get; set; }

        public Decimal? cROLLUP { get; set; }

        public Decimal? cREFERRED { get; set; }

        public int? RECRUITERNO { get; set; }

        public int TRACKNO { get; set; }

        public Decimal? PREV_COMMITMENT { get; set; }

        public Decimal? PREV_cRAISED { get; set; }
                
        public Decimal? PREV_cREFERRED { get; set; }
                
        public Decimal? PREV_cSHARED { get; set; }

        public Decimal? PREV_ROLLUP { get; set; }

        public int LEVEL { get; set; }

        public byte[] PICTURE { get; set; }

        public int hasChildren { get; set; }

    }

    //public class TREEVIEWRESPONSE
    //{
    //    public List<FUNDRAISEDONOR> FUNDRAISEDONOR { get; set; }

    //    public bool hasChildren { get; set; }
    //}

    [Table("jtFNDRGRP")]
    public class jtFNDRGRP : _entityBase_crm, iItemType
    {
        [Column]
        [Key]
        public int jtFNDRGRPID { get; set; }

        [Column]
        public int FUNDRAISEID { get; set; }

        [Column]
        public Int16 FNDRGRPID { get; set; }

        [Column]
        public Nullable<DateTime> UPDATEDON { get; set; }

    }

    
}
