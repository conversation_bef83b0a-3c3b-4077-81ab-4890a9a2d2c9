﻿using cmdiapp.n.core.Areas.crm.Domain.Data;
using System;
using System.ComponentModel.DataAnnotations.Schema;
using System.Runtime.Serialization;

namespace cmdiapp.n.core.Areas.crm.Domain.Models
{
	[Table("x_va_filerInfo")]
	[DataContract]
    public class VirginiaFiler : _entityBase_crm
    {
        [Column("id")]
		[DataMember(Name = "id")]
		public int Id { get; set; }

		[Column("balanceAtStartOfElectionCycle")]
		[DataMember(Name = "balanceAtStartOfElectionCycle")]
		public decimal BalanceAtStartOfElectionCycle { get; set; }

		[Column("committeeCode")]
		[DataMember(Name = "committeeCode")]
		public string CommitteeCode { get; set; }

		[Column("committeeName")]
		[DataMember(Name = "committeeName")]
		public string CommitteeName { get; set; }

		[Column("addressLine1")]
		[DataMember(Name = "addressLine1")]
		public string AddressLine1 { get; set; }

		[Column("addressLine2")]
		[DataMember(Name = "addressLine2")]
		public string AddressLine2 { get; set; }

		[Column("addressLine3")]
		[DataMember(Name = "addressLine3")]
		public string AddressLine3 { get; set; }

		[Column("city")]
		[DataMember(Name = "city")]
		public string City { get; set; }

		[Column("state")]
		[DataMember(Name = "state")]
		public string State { get; set; }

		[Column("zipCode")]
		[DataMember(Name = "zipCode")]
		public string ZipCode { get; set; }

		[Column("email")]
		[DataMember(Name = "email")]
		public string Email { get; set; }

		[Column("phoneNumber")]
		[DataMember(Name = "phoneNumber")]
		public string PhoneNumber { get; set; }

		[Column("electionCycle")]
		[DataMember(Name = "electionCycle")]
		public string ElectionCycle { get; set; }

		[Column("district")]
		[DataMember(Name = "district")]
		public string District { get; set; }

		[Column("officeSought")]
		[IgnoreDataMember]
		public string OfficeSought { get; set; }

		[Column("dateOfReferendum")]
		[DataMember(Name = "dateOfReferendum")]
		public DateTime? DateOfReferendum { get; set; }

		[Column("treasurer")]
		[DataMember(Name = "treasurer")]
		public string Treasurer { get; set; }

		[Column("createdOn")]
		[DataMember(Name = "createdOn")]
		public DateTime CreatedOn { get; set; }

		[Column("updatedOn")]
		[DataMember(Name = "updatedOn")]
		public DateTime UpdatedOn { get; set; }

		[Column("updatedByUid")]
		[DataMember(Name = "updatedByUid")]
		public int UpdatedByUid { get; set; }

		[Column("itemizationAmount")]
		[DataMember(Name = "itemizationAmount")]
		public decimal ItemizationAmount { get; set; }

		[Column("officeId")]
		[DataMember(Name = "officeId")]
        public int? OfficeId { get; set; }
    }
}