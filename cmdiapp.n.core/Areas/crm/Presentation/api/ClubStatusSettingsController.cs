﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Net;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Web.Http;
using System.Web.Script.Serialization;
using System.Drawing;
using System.IO;
using System.Data.SqlClient;
using System.Collections.ObjectModel;
using System.Xml.Linq;

using Ninject;
using Ninject.Web.Mvc;

using AutoMapper;
using cmdiapp.n.core.Core;
using cmdiapp.n.core.Core.App_Start;
using cmdiapp.n.core.Domain.ViewModels;
using cmdiapp.n.core.Areas.crm.Core;
using cmdiapp.n.core.Areas.crm.Domain.Models;
using cmdiapp.n.core.Areas.crm.Domain.ViewModels;
using cmdiapp.n.core.Areas.crm.Domain.Data;
using cmdiapp.n.core.Areas.crm.Domain.Repositories;
using cmdiapp.n.core.Areas.crm.Domain.Services;
using cmdiapp.n.core.Areas.dataService.Domain.Data;
using cmdiapp.n.core.Areas.dataService.Domain.Models;
using cmdiapp.n.core.Areas.dataService.Domain.Services;
using cmdiapp.n.core.Presentation.Controllers;
using cmdiapp.n.core.Areas.query.Domain.Models;
using cmdiapp.n.core.Domain.Models;
using AutoMapper;
using System.Data;
using cmdiapp.n.core.Library;

namespace cmdiapp.n.core.Areas.crm.Presentation.Controllers.api
{
    [Authorize]
    public class ClubStatusSettingsController : ApiController
    {
        private I_entity_crm _entity_crm;
        
        private readonly IclubStatusService _clubStatusService;
        private readonly IReportDataService _reportService;
        private userSession _userSession;
        
        #region [[ (constructor) SettingsController ]]
        public ClubStatusSettingsController()
        {
            _entity_crm = I_entityManager_crm.getEntity();
            _clubStatusService= NinjectMVC.kernel.Get<IclubStatusService>();
            _reportService = NinjectMVC.kernel.Get<IReportDataService>();
            _userSession = session.userSession;
        }
        #endregion

        #region [[ SQL Query]]
        
        const string _sql_exportClubStatus = @"SELECT ISNULL(CLUBCODE,'''') AS CODE ,ISNULL(DESCRIP,'''') AS DESCRIP from lkCLUBSTAT join pmclub on pmclub.CLUBID = lkCLUBSTAT.CLUBID";
        string clubLabel = crmSession.is_nonProfit_version() ? "Membership" : "Club";
        #endregion

        #region[PEOPLE->SETTINGS->CLUBSTATUS by Tanvir]
        [HttpPost, Route("crm/api/ClubStatusSettings/ExportCStatusKey")]
        [apiAuthorize(AccessElement = "cmdiapp.dms.entitystatus", AccessLevel = "v")]
        public string ExportCStatusKey(string searchText)
        {
            if (string.IsNullOrEmpty(searchText)) searchText = "";

            searchParam param = new searchParam();
            param.searchText = searchText;

            string key_ = System.Guid.NewGuid().ToString();
            HttpRuntime.Cache.Insert(key_, param, null, DateTime.UtcNow.Add(new TimeSpan(1, 0, 1)), System.Web.Caching.Cache.NoSlidingExpiration);

            return key_;

        }

        [HttpGet, Route("crm/api/ClubStatusSettings/ExportCStatus")]
        [apiAuthorize(AccessElement = "cmdiapp.dms.entitystatus", AccessLevel = "v")]
        public string ExportCStatus(string key)
        {
            searchParam _param = (searchParam)HttpRuntime.Cache[key];
            string sToday = String.Format("{0:yyyyMMMdd__hh_mm_ss tt}", DateTime.Now);
            string moduleName = clubLabel+"Status";
            string fileName = moduleName + "_" + System.Guid.NewGuid() + "_" + _userSession.UserName + "_" + sToday;

            DataSet ds = null;

            if (string.IsNullOrEmpty(_param.searchText))
            {
                ds = _reportService.get_dataset_w_sql__single(_sql_exportClubStatus, clubLabel+"Status");
            }
            else
            {
                _param.searchText = Library.util.kill_sqlBlacklistWord(_param.searchText);

                string _where = "";

                if (!string.IsNullOrEmpty(_param.searchText))
                {
                    #region [ Split Search Text > string1, string2 & string3 and create "Where" statement ]
                    char[] delimiterChars = { ' ', ',', '.', ';', ':', '|', '\t' };

                    string[] words = _param.searchText.Split(delimiterChars);

                    if (words.Count() > 0)
                    {
                        string string1 = words[0];
                        string string2 = (words.Count() > 1 ? words[1] : "");
                        string string3 = (words.Count() > 2 ? words[2] : "");

                        #region [ Where Clause ]
                        switch (words.Count())
                        {
                            case 1: // CLUBCODE or DESCRIP
                                _where = string.Format("CLUBCODE LIKE '%{0}%' OR DESCRIP LIKE '%{0}%'", string1);
                                break;

                            case 2: // CLUBCODE and DESCRIP
                                _where = string.Format("CLUBCODE LIKE '%{0}%' OR DESCRIP LIKE '%{1}%'", string1, string2);
                                break;

                            default:
                                break;
                        }
                        #endregion

                    }
                    #endregion
                }
                //We need SQL with Where condition
                string sql = _sql_exportClubStatus + " Where {0}";
                //Compose the SQL
                sql = String.Format(sql, _where);

                ds = _reportService.get_dataset_w_sql__single(sql, clubLabel+"Status");

            }


            if (ds != null)
            {
                //HttpResponse _resp = CreateExcelFile.CreateExcelDocument(_dt.Copy(), fileName, HttpContext.Current.Response);
                string key_ = System.Guid.NewGuid().ToString();
                HttpRuntime.Cache.Insert(key_, ds, null, DateTime.UtcNow.Add(new TimeSpan(1, 0, 1)), System.Web.Caching.Cache.NoSlidingExpiration);
                //return the Key
                return key_;
            }
            else
            {
                return null;
            }
        }

        [HttpPost, Route("crm/api/ClubStatusSettings/ManageClubStatus")]
        [apiAuthorize(AccessElement = "cmdiapp.dms.entitystatus", AccessLevel = "a")]
        public genericResponse ManageClubStatus(vwClubStatus model)
        {
            if (model != null)
            {
                try
                {
                    //Create the object
                    var _lkCLUBSTAT = new lkCLUBSTAT
                    {
                        CLUBSTATID = model.lkCLUBSTAT.CLUBSTATID,
                        CLUBID = model.lkCLUBSTAT.CLUBID,
                        DESCRIP = model.lkCLUBSTAT.DESCRIP,

                    };
                    //check for Uniqueness 
                    if (!_clubStatusService.check_unqiueness(_lkCLUBSTAT))
                    {
                        //return 
                        return new genericResponse{ success = false, message = clubLabel+" Status already exists. Please enter unique Status and try again." };
                    }
                    else
                    {
                        if (_lkCLUBSTAT.CLUBSTATID > 0)
                        {
                            _clubStatusService.Update(_lkCLUBSTAT);
                            return new genericResponse{ success = true, message = clubLabel+" Status updated successfully." };
                        }
                        else
                        {
                            _clubStatusService.Add(_lkCLUBSTAT);
                            return new genericResponse{ success = true, message = clubLabel+" Status added successfully." };
                        }

                    }

                }
                catch
                {
                    //To Do: Log the Exception here
                    return new genericResponse{ success = false, message = "There is a problem processing your request. Please try again later." };
                }
            }
            else
            {
                //Model is not valid - We can not go ahead
                return new genericResponse{ success = false, message = "Missing Required Field(s). Please try again." };
            }

        }

        [HttpPost, Route("crm/api/ClubStatusSettings/DestroyClubStatus")]
        [apiAuthorize(AccessElement = "cmdiapp.dms.entitystatus", AccessLevel = "d")]
        public genericResponse DestroyClubStatus(int clubstatid)
        {
            try
            {
                var viewModel = _clubStatusService.get_clubStatus(clubstatid);
                if (!_clubStatusService.check_record_exists(clubstatid))
                {
                    _clubStatusService.Delete(viewModel);
                    return new genericResponse{ success = true, message = clubLabel+" Status deleted successfully." };
                }
                else
                {
                    //Can not delete as this is being used
                    return new genericResponse{ success = false, message = "The "+ clubLabel + " Status is being referenced so it can not be deleted." };
                }

            }
            catch
            {
                //To do: Log the Exception here
                return new genericResponse{ success = false, message = "There is a problem processing your request. Please try again later." };
            }
        }

        [HttpGet, Route("crm/api/ClubStatusSettings/GetClubStatus")]
        [apiAuthorize(AccessElement = "cmdiapp.dms.entitystatus", AccessLevel = "v")]
        public genericResponse GetClubStatus(string searchText, int? page, int? pageSize)
        {
            #region [ Retrieve Input ]
            string _searchText = Library.util.kill_sqlBlacklistWord((searchText ?? "").Trim().Replace("'", "''''"));

            int _pageSize = (pageSize == null || pageSize.Value == 0 ? 10 : pageSize.Value);
            int _pageNo = (page == null || page.Value == 0 ? 1 : page.Value);

            string sortField = HttpContext.Current.Request.QueryString["sort[0][field]"];
            string sortDir = HttpContext.Current.Request.QueryString["sort[0][dir]"];
            string _sortOptions = "";
            if (!string.IsNullOrEmpty(sortField))
                _sortOptions = sortField;
            if (!string.IsNullOrEmpty(sortField) && !string.IsNullOrEmpty(sortDir))
                _sortOptions = _sortOptions + " " + sortDir;
            #endregion
            
            List<lkCLUBSTAT_ext> _list = new List<lkCLUBSTAT_ext>();
            genericResponse _response;

            if (!string.IsNullOrEmpty(_searchText))
            {
                _searchText = Library.util.kill_sqlBlacklistWord(_searchText);

                int contstatid;

                if (string.IsNullOrEmpty(_sortOptions)) _sortOptions = "CLUBSTATID";

                if (int.TryParse(_searchText, out contstatid))
                    _list = _clubStatusService.get_all_clubstatuses(string.Format("CLUBSTATID={0}", contstatid), _sortOptions, _pageSize, _pageNo);
                else
                {
                    string _where = "";

                    if (!string.IsNullOrEmpty(_searchText))
                    {
                        #region [ Split Search Text > string1, string2 & string3 and create "Where" statement ]
                        char[] delimiterChars = { ' ', ',', '.', ';', ':', '|', '\t' };

                        string[] words = _searchText.Split(delimiterChars);

                        if (words.Count() > 0)
                        {
                            string string1 = words[0];
                            string string2 = (words.Count() > 1 ? words[1] : "");
                            string string3 = (words.Count() > 2 ? words[2] : "");

                            #region [ Where Clause ]
                            switch (words.Count())
                            {
                                case 1: // CLUBCODE or DESCRIP
                                    _where = string.Format("CLUBCODE LIKE ''%{0}%'' OR DESCRIP LIKE ''%{0}%''", string1);
                                    break;

                                case 2: // CLUBCODE and DESCRIP
                                    _where = string.Format("CLUBCODE LIKE ''%{0}%'' OR DESCRIP LIKE ''%{1}%''", string1, string2);
                                    break;

                                default:
                                    break;
                            }
                            #endregion

                            if (string.IsNullOrEmpty(_sortOptions)) _sortOptions = "CLUBSTATID ASC";
                            _list = _clubStatusService.get_all_clubstatuses(_where, _sortOptions, _pageSize, _pageNo);
                        }
                        #endregion
                    }
                }

            }
            else
            {
                //Coming here when SearchText is null
                if (string.IsNullOrEmpty(_sortOptions)) _sortOptions = "CLUBSTATID ASC";
                _list = _clubStatusService.get_all_clubstatuses("", _sortOptions, _pageSize, _pageNo);
            }

            Mapper.CreateMap<lkCLUBSTAT_ext, rlkCLUBSTAT>();
            IEnumerable<iItemType> results = _list.Select(a => Mapper.Map<lkCLUBSTAT_ext, rlkCLUBSTAT>(a)).ToList();
            if (results.Count() > 0)
            {
                _response = new genericResponse() { success = true, __count = _list.FirstOrDefault().count_, results = results.ToList() };
            }
            else
            {
                _response = new genericResponse() { success = true, __count = 0 };
            }
            return _response;
            //return Json(_response, JsonRequestBehavior.AllowGet);
        }
        #endregion

    }
}
