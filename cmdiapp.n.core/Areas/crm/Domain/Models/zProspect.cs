﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Data;
using System.Linq;
using System.Web.Mvc;
using System.Data.Linq;
using System.ComponentModel.DataAnnotations.Schema;
using cmdiapp.n.core.Areas.crm.Domain.Data;

using cmdiapp.n.core.Domain.ViewModels;

namespace cmdiapp.n.core.Areas.crm.Domain.Models
{
    // Updated by Lydia 2013-10-22
    [Table("zProspect")]
    public class zProspect : _entityBase_crm, iItemType
    {
        [Key]
        [Column]
        public Int64 PPID { get; set; }

        [Column]
        public string PREFIX { get; set; }

        [Column]
        public string FNAME { get; set; }

        [Column]
        public string MNAME { get; set; }

        [Column]
        public string LNAME { get; set; }

        [Column]
        public string SUFFIX { get; set; }

        [Column]
        public string STREET { get; set; }

        [Column]
        public string ADDR1 { get; set; }

        [Column]
        public string ADDR2 { get; set; }

        [Column]
        public string CITY { get; set; }

        [Column]
        public string STATE { get; set; }

        [Column]
        public string ZIP { get; set; }

        [Column]
        public string PLUS4 { get; set; }

        [Column]
        public string SOURCE { get; set; }
    }

    public class dmsFinder : _entityBase_crm, iItemType
    {
        [Key]
        [Column]
        public int PID { get; set; }

        [Column]
        public string PREFIX { get; set; }

        [Column]
        public string FNAME { get; set; }

        [Column]
        public string MNAME { get; set; }

        [Column]
        public string LNAME { get; set; }

        [Column]
        public string SUFFIX { get; set; }

        [Column]
        public string SALUTATION { get; set; }

        [Column]
        public string EMPLOYER { get; set; }

        [Column]
        public string OCCUPATION { get; set; }

        [Column]
        public string STREET { get; set; }

        [Column]
        public string ADDR1 { get; set; }

        [Column]
        public string ADDR2 { get; set; }

        [Column]
        public string CITY { get; set; }

        [Column]
        public string STATE { get; set; }

        [Column]
        public string ZIP { get; set; }

        [Column]
        public string PLUS4 { get; set; }

        [Column]
        public string HMPH { get; set; }

        [Column]
        public string BUSPH { get; set; }

        [Column]
        public string FAX { get; set; }

        [Column]
        public string EMAIL { get; set; }

        [Column]
        public bool hasF5 { get; set; }

        [Column]
        public bool hasM { get; set; }

        [Column]
        public string RNCPID { get; set; }
    }
}