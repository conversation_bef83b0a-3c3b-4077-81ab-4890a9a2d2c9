﻿using OfficeOpenXml;
using System;

namespace cmdiapp.n.core.Areas.crm.Core
{
    public class ExcelPackageWrapper : IExcelPackage
    {
        private readonly ExcelPackage _package;

        public ExcelPackageWrapper(ExcelPackage package)
        {
            _package = package ?? throw new ArgumentNullException(nameof(package));
        }

        public IExcelWorksheet AddWorksheet(string name)
        {
            var worksheet = _package.Workbook.Worksheets.Add(name);
            return new ExcelWorksheetWrapper(worksheet);
        }

        public void Dispose()
        {
            _package.Dispose();
        }

        public byte[] GetBytes()
        {
            return _package.GetAsByteArray();
        }
    }
}