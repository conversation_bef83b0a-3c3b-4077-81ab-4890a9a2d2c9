﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Linq.Expressions;
using System.Data.Linq;
using System.Diagnostics;
using System.IO;
using System.Data;
using System.Data.Entity;
using System.Configuration;

namespace cmdiapp.n.core.Areas.dataService.Domain.Data
{
    public class _entity_ds : I_entity_ds
    {
        private DbContext _context;
        private bool _contextDisposed;
        private bool _contextInitialized;
        private readonly string _connectionString;

        #region [[ Constructors ]]
        public _entity_ds()
        {
            _connectionString = ConfigurationManager.ConnectionStrings["dataServiceContext"].ConnectionString;
        }

        #endregion

        public DbContext getContext()
        {
            return _context ?? (_context = new _dsDBContext(_connectionString));
        }

        public void CommitChanges()
        {
            Initialize();
            _context.SaveChanges();
        }

        public void Delete<TSource>(Expression<Func<TSource, bool>> expression) where TSource : _entityBase_ds, new()
        {
            Initialize();
            var query = All<TSource>().Where(expression);
            foreach (TSource item in query.ToList())
                Delete<TSource>(item);
        }

        public void Delete<TSource>(TSource item) where TSource : _entityBase_ds, new()
        {
            Initialize();
            GetTable<TSource>().Remove(item);
        }

        public TSource Single<TSource>(Expression<Func<TSource, bool>> expression) where TSource : _entityBase_ds, new()
        {
            Initialize();
            return GetTable<TSource>().FirstOrDefault<TSource>(expression);
        }

        public IQueryable<TSource> All<TSource>() where TSource : _entityBase_ds, new()
        {
            Initialize();
            return GetTable<TSource>().AsQueryable<TSource>();
        }

        public void Add<TSource>(TSource item) where TSource : _entityBase_ds, new()
        {
            Initialize();
            GetTable<TSource>().Add(item);
        }

        public void Add<TSource>(IEnumerable<TSource> items) where TSource : _entityBase_ds, new()
        {
            Initialize();
            foreach (TSource item in items.ToList())
                Add<TSource>(item);
        }

        public void Update<TSource>(TSource item) where TSource : _entityBase_ds, new()
        {           
            _context.Entry<TSource>(item).State = EntityState.Modified;
        }

        private DbSet<TSource> GetTable<TSource>() where TSource : _entityBase_ds, new()
        {
            return _context.Set<TSource>();
        }

        public void SetLog(TextWriter writer)
        {
            Initialize();

            // .Log is supported in EF6
            //_context.Database.Log = writer;
        }

        public void Dispose()
        {
            if (_context != null)
            {
                _context.Dispose();
                _contextDisposed = true;
            }
        }

        private void Initialize()
        {
            if (!_contextInitialized || _contextDisposed == true)
            {
                _context = new _dsDBContext(_connectionString);
                _context.Configuration.LazyLoadingEnabled = true;
                _contextInitialized = true;
            }
        }

        public void Refresh<TSource>(TSource item) where TSource : _entityBase_ds, new()
        {
            _context.Entry<TSource>(item).Reload();
        }

        class DebugTextWriter : System.IO.TextWriter
        {
            public override void Write(char[] buffer, int index, int count)
            {
                // Debug.Write(new String(buffer, index, count));
            }

            public override void Write(string value)
            {
                Debug.Write(value);
            }

            public override Encoding Encoding
            {
                get { return Encoding.Default; }
            }
        }
    }
}