﻿using cmdiapp.n.core._Domain.Models;
using cmdiapp.n.core.Areas.query.Domain.Services;
using System;
using System.Threading.Tasks;

namespace cmdiapp.n.core.Areas.query.Domain.Models
{
    public class EmailReportSearchQueryCompiler : IEmailReportQueryCompiler
    {
        private readonly ISavedQueryService _savedQueryService;
        private readonly bool _useLogTable;

        public EmailReportSearchQueryCompiler(
            ISavedQueryService savedQueryService,
            bool getInstanceFromLogTable = false)
        {
            _savedQueryService = savedQueryService;
            _useLogTable = getInstanceFromLogTable;
        }

        public async Task<CompiledEmailReportQueries> CompileAsync(int compilationSourceId)
        {
            var savedQuery = await GetQueryInstance(compilationSourceId);
            if (savedQuery == null)
            {
                throw new ArgumentException($"Could not get query instance with id {compilationSourceId}");
            }

            return new CompiledEmailReportQueries()
            {
                ProjectId = savedQuery.ProjectId,
                RecordsQuery = _savedQueryService.GetSql(savedQuery.QueryInstance, QueryResultType.allDatasetExport),
                SummaryQuery = _savedQueryService.GetSql(savedQuery.QueryInstance, QueryResultType.aggregationExport),
                MaxRecordsToExport = _savedQueryService.MaxRecordsToExport
            };
        }

        private Task<SavedQueryInstance> GetQueryInstance(int id)
        {
            if (_useLogTable)
            {
                return _savedQueryService.GetQueryInstanceFromLogAsync(id);
            }
            return _savedQueryService.GetQueryInstanceAsync(id);
        }
    }
}