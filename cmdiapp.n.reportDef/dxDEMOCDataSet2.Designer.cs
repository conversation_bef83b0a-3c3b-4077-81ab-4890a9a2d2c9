﻿//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

#pragma warning disable 1591

namespace cmdiapp.n.reportDef {
    
    
    /// <summary>
    ///Represents a strongly typed in-memory cache of data.
    ///</summary>
    [global::System.Serializable()]
    [global::System.ComponentModel.DesignerCategoryAttribute("code")]
    [global::System.ComponentModel.ToolboxItem(true)]
    [global::System.Xml.Serialization.XmlSchemaProviderAttribute("GetTypedDataSetSchema")]
    [global::System.Xml.Serialization.XmlRootAttribute("dxDEMOCDataSet2")]
    [global::System.ComponentModel.Design.HelpKeywordAttribute("vs.data.DataSet")]
    public partial class dxDEMOCDataSet2 : global::System.Data.DataSet {
        
        private MONYADDIDataTable tableMONYADDI;
        
        private v_people_monyDataTable tablev_people_mony;
        
        private global::System.Data.SchemaSerializationMode _schemaSerializationMode = global::System.Data.SchemaSerializationMode.IncludeSchema;
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
        public dxDEMOCDataSet2() {
            this.BeginInit();
            this.InitClass();
            global::System.ComponentModel.CollectionChangeEventHandler schemaChangedHandler = new global::System.ComponentModel.CollectionChangeEventHandler(this.SchemaChanged);
            base.Tables.CollectionChanged += schemaChangedHandler;
            base.Relations.CollectionChanged += schemaChangedHandler;
            this.EndInit();
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
        protected dxDEMOCDataSet2(global::System.Runtime.Serialization.SerializationInfo info, global::System.Runtime.Serialization.StreamingContext context) : 
                base(info, context, false) {
            if ((this.IsBinarySerialized(info, context) == true)) {
                this.InitVars(false);
                global::System.ComponentModel.CollectionChangeEventHandler schemaChangedHandler1 = new global::System.ComponentModel.CollectionChangeEventHandler(this.SchemaChanged);
                this.Tables.CollectionChanged += schemaChangedHandler1;
                this.Relations.CollectionChanged += schemaChangedHandler1;
                return;
            }
            string strSchema = ((string)(info.GetValue("XmlSchema", typeof(string))));
            if ((this.DetermineSchemaSerializationMode(info, context) == global::System.Data.SchemaSerializationMode.IncludeSchema)) {
                global::System.Data.DataSet ds = new global::System.Data.DataSet();
                ds.ReadXmlSchema(new global::System.Xml.XmlTextReader(new global::System.IO.StringReader(strSchema)));
                if ((ds.Tables["MONYADDI"] != null)) {
                    base.Tables.Add(new MONYADDIDataTable(ds.Tables["MONYADDI"]));
                }
                if ((ds.Tables["v_people_mony"] != null)) {
                    base.Tables.Add(new v_people_monyDataTable(ds.Tables["v_people_mony"]));
                }
                this.DataSetName = ds.DataSetName;
                this.Prefix = ds.Prefix;
                this.Namespace = ds.Namespace;
                this.Locale = ds.Locale;
                this.CaseSensitive = ds.CaseSensitive;
                this.EnforceConstraints = ds.EnforceConstraints;
                this.Merge(ds, false, global::System.Data.MissingSchemaAction.Add);
                this.InitVars();
            }
            else {
                this.ReadXmlSchema(new global::System.Xml.XmlTextReader(new global::System.IO.StringReader(strSchema)));
            }
            this.GetSerializationData(info, context);
            global::System.ComponentModel.CollectionChangeEventHandler schemaChangedHandler = new global::System.ComponentModel.CollectionChangeEventHandler(this.SchemaChanged);
            base.Tables.CollectionChanged += schemaChangedHandler;
            this.Relations.CollectionChanged += schemaChangedHandler;
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
        [global::System.ComponentModel.Browsable(false)]
        [global::System.ComponentModel.DesignerSerializationVisibility(global::System.ComponentModel.DesignerSerializationVisibility.Content)]
        public MONYADDIDataTable MONYADDI {
            get {
                return this.tableMONYADDI;
            }
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
        [global::System.ComponentModel.Browsable(false)]
        [global::System.ComponentModel.DesignerSerializationVisibility(global::System.ComponentModel.DesignerSerializationVisibility.Content)]
        public v_people_monyDataTable v_people_mony {
            get {
                return this.tablev_people_mony;
            }
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
        [global::System.ComponentModel.BrowsableAttribute(true)]
        [global::System.ComponentModel.DesignerSerializationVisibilityAttribute(global::System.ComponentModel.DesignerSerializationVisibility.Visible)]
        public override global::System.Data.SchemaSerializationMode SchemaSerializationMode {
            get {
                return this._schemaSerializationMode;
            }
            set {
                this._schemaSerializationMode = value;
            }
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
        [global::System.ComponentModel.DesignerSerializationVisibilityAttribute(global::System.ComponentModel.DesignerSerializationVisibility.Hidden)]
        public new global::System.Data.DataTableCollection Tables {
            get {
                return base.Tables;
            }
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
        [global::System.ComponentModel.DesignerSerializationVisibilityAttribute(global::System.ComponentModel.DesignerSerializationVisibility.Hidden)]
        public new global::System.Data.DataRelationCollection Relations {
            get {
                return base.Relations;
            }
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
        protected override void InitializeDerivedDataSet() {
            this.BeginInit();
            this.InitClass();
            this.EndInit();
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
        public override global::System.Data.DataSet Clone() {
            dxDEMOCDataSet2 cln = ((dxDEMOCDataSet2)(base.Clone()));
            cln.InitVars();
            cln.SchemaSerializationMode = this.SchemaSerializationMode;
            return cln;
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
        protected override bool ShouldSerializeTables() {
            return false;
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
        protected override bool ShouldSerializeRelations() {
            return false;
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
        protected override void ReadXmlSerializable(global::System.Xml.XmlReader reader) {
            if ((this.DetermineSchemaSerializationMode(reader) == global::System.Data.SchemaSerializationMode.IncludeSchema)) {
                this.Reset();
                global::System.Data.DataSet ds = new global::System.Data.DataSet();
                ds.ReadXml(reader);
                if ((ds.Tables["MONYADDI"] != null)) {
                    base.Tables.Add(new MONYADDIDataTable(ds.Tables["MONYADDI"]));
                }
                if ((ds.Tables["v_people_mony"] != null)) {
                    base.Tables.Add(new v_people_monyDataTable(ds.Tables["v_people_mony"]));
                }
                this.DataSetName = ds.DataSetName;
                this.Prefix = ds.Prefix;
                this.Namespace = ds.Namespace;
                this.Locale = ds.Locale;
                this.CaseSensitive = ds.CaseSensitive;
                this.EnforceConstraints = ds.EnforceConstraints;
                this.Merge(ds, false, global::System.Data.MissingSchemaAction.Add);
                this.InitVars();
            }
            else {
                this.ReadXml(reader);
                this.InitVars();
            }
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
        protected override global::System.Xml.Schema.XmlSchema GetSchemaSerializable() {
            global::System.IO.MemoryStream stream = new global::System.IO.MemoryStream();
            this.WriteXmlSchema(new global::System.Xml.XmlTextWriter(stream, null));
            stream.Position = 0;
            return global::System.Xml.Schema.XmlSchema.Read(new global::System.Xml.XmlTextReader(stream), null);
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
        internal void InitVars() {
            this.InitVars(true);
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
        internal void InitVars(bool initTable) {
            this.tableMONYADDI = ((MONYADDIDataTable)(base.Tables["MONYADDI"]));
            if ((initTable == true)) {
                if ((this.tableMONYADDI != null)) {
                    this.tableMONYADDI.InitVars();
                }
            }
            this.tablev_people_mony = ((v_people_monyDataTable)(base.Tables["v_people_mony"]));
            if ((initTable == true)) {
                if ((this.tablev_people_mony != null)) {
                    this.tablev_people_mony.InitVars();
                }
            }
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
        private void InitClass() {
            this.DataSetName = "dxDEMOCDataSet2";
            this.Prefix = "";
            this.Namespace = "http://tempuri.org/dxDEMOCDataSet2.xsd";
            this.EnforceConstraints = true;
            this.SchemaSerializationMode = global::System.Data.SchemaSerializationMode.IncludeSchema;
            this.tableMONYADDI = new MONYADDIDataTable();
            base.Tables.Add(this.tableMONYADDI);
            this.tablev_people_mony = new v_people_monyDataTable();
            base.Tables.Add(this.tablev_people_mony);
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
        private bool ShouldSerializeMONYADDI() {
            return false;
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
        private bool ShouldSerializev_people_mony() {
            return false;
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
        private void SchemaChanged(object sender, global::System.ComponentModel.CollectionChangeEventArgs e) {
            if ((e.Action == global::System.ComponentModel.CollectionChangeAction.Remove)) {
                this.InitVars();
            }
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
        public static global::System.Xml.Schema.XmlSchemaComplexType GetTypedDataSetSchema(global::System.Xml.Schema.XmlSchemaSet xs) {
            dxDEMOCDataSet2 ds = new dxDEMOCDataSet2();
            global::System.Xml.Schema.XmlSchemaComplexType type = new global::System.Xml.Schema.XmlSchemaComplexType();
            global::System.Xml.Schema.XmlSchemaSequence sequence = new global::System.Xml.Schema.XmlSchemaSequence();
            global::System.Xml.Schema.XmlSchemaAny any = new global::System.Xml.Schema.XmlSchemaAny();
            any.Namespace = ds.Namespace;
            sequence.Items.Add(any);
            type.Particle = sequence;
            global::System.Xml.Schema.XmlSchema dsSchema = ds.GetSchemaSerializable();
            if (xs.Contains(dsSchema.TargetNamespace)) {
                global::System.IO.MemoryStream s1 = new global::System.IO.MemoryStream();
                global::System.IO.MemoryStream s2 = new global::System.IO.MemoryStream();
                try {
                    global::System.Xml.Schema.XmlSchema schema = null;
                    dsSchema.Write(s1);
                    for (global::System.Collections.IEnumerator schemas = xs.Schemas(dsSchema.TargetNamespace).GetEnumerator(); schemas.MoveNext(); ) {
                        schema = ((global::System.Xml.Schema.XmlSchema)(schemas.Current));
                        s2.SetLength(0);
                        schema.Write(s2);
                        if ((s1.Length == s2.Length)) {
                            s1.Position = 0;
                            s2.Position = 0;
                            for (; ((s1.Position != s1.Length) 
                                        && (s1.ReadByte() == s2.ReadByte())); ) {
                                ;
                            }
                            if ((s1.Position == s1.Length)) {
                                return type;
                            }
                        }
                    }
                }
                finally {
                    if ((s1 != null)) {
                        s1.Close();
                    }
                    if ((s2 != null)) {
                        s2.Close();
                    }
                }
            }
            xs.Add(dsSchema);
            return type;
        }
        
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
        public delegate void MONYADDIRowChangeEventHandler(object sender, MONYADDIRowChangeEvent e);
        
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
        public delegate void v_people_monyRowChangeEventHandler(object sender, v_people_monyRowChangeEvent e);
        
        /// <summary>
        ///Represents the strongly named DataTable class.
        ///</summary>
        [global::System.Serializable()]
        [global::System.Xml.Serialization.XmlSchemaProviderAttribute("GetTypedTableSchema")]
        public partial class MONYADDIDataTable : global::System.Data.TypedTableBase<MONYADDIRow> {
            
            private global::System.Data.DataColumn columnMONYADDIID;
            
            private global::System.Data.DataColumn columnMID;
            
            private global::System.Data.DataColumn columnTMCAMPAIGN;
            
            private global::System.Data.DataColumn columnTMUNIQNUM;
            
            private global::System.Data.DataColumn columnRECURRED;
            
            private global::System.Data.DataColumn columnRECURENDDTE;
            
            private global::System.Data.DataColumn columnREFERENCEID;
            
            private global::System.Data.DataColumn columnMULTNAME;
            
            private global::System.Data.DataColumn columnMULTSIGN;
            
            private global::System.Data.DataColumn columnWEBGIFTID;
            
            private global::System.Data.DataColumn columnMISCTEXT;
            
            private global::System.Data.DataColumn columnMONYCCBATID;
            
            private global::System.Data.DataColumn columnMISCJSON;
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
            public MONYADDIDataTable() {
                this.TableName = "MONYADDI";
                this.BeginInit();
                this.InitClass();
                this.EndInit();
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
            internal MONYADDIDataTable(global::System.Data.DataTable table) {
                this.TableName = table.TableName;
                if ((table.CaseSensitive != table.DataSet.CaseSensitive)) {
                    this.CaseSensitive = table.CaseSensitive;
                }
                if ((table.Locale.ToString() != table.DataSet.Locale.ToString())) {
                    this.Locale = table.Locale;
                }
                if ((table.Namespace != table.DataSet.Namespace)) {
                    this.Namespace = table.Namespace;
                }
                this.Prefix = table.Prefix;
                this.MinimumCapacity = table.MinimumCapacity;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
            protected MONYADDIDataTable(global::System.Runtime.Serialization.SerializationInfo info, global::System.Runtime.Serialization.StreamingContext context) : 
                    base(info, context) {
                this.InitVars();
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
            public global::System.Data.DataColumn MONYADDIIDColumn {
                get {
                    return this.columnMONYADDIID;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
            public global::System.Data.DataColumn MIDColumn {
                get {
                    return this.columnMID;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
            public global::System.Data.DataColumn TMCAMPAIGNColumn {
                get {
                    return this.columnTMCAMPAIGN;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
            public global::System.Data.DataColumn TMUNIQNUMColumn {
                get {
                    return this.columnTMUNIQNUM;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
            public global::System.Data.DataColumn RECURREDColumn {
                get {
                    return this.columnRECURRED;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
            public global::System.Data.DataColumn RECURENDDTEColumn {
                get {
                    return this.columnRECURENDDTE;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
            public global::System.Data.DataColumn REFERENCEIDColumn {
                get {
                    return this.columnREFERENCEID;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
            public global::System.Data.DataColumn MULTNAMEColumn {
                get {
                    return this.columnMULTNAME;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
            public global::System.Data.DataColumn MULTSIGNColumn {
                get {
                    return this.columnMULTSIGN;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
            public global::System.Data.DataColumn WEBGIFTIDColumn {
                get {
                    return this.columnWEBGIFTID;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
            public global::System.Data.DataColumn MISCTEXTColumn {
                get {
                    return this.columnMISCTEXT;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
            public global::System.Data.DataColumn MONYCCBATIDColumn {
                get {
                    return this.columnMONYCCBATID;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
            public global::System.Data.DataColumn MISCJSONColumn {
                get {
                    return this.columnMISCJSON;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
            [global::System.ComponentModel.Browsable(false)]
            public int Count {
                get {
                    return this.Rows.Count;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
            public MONYADDIRow this[int index] {
                get {
                    return ((MONYADDIRow)(this.Rows[index]));
                }
            }
            
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
            public event MONYADDIRowChangeEventHandler MONYADDIRowChanging;
            
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
            public event MONYADDIRowChangeEventHandler MONYADDIRowChanged;
            
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
            public event MONYADDIRowChangeEventHandler MONYADDIRowDeleting;
            
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
            public event MONYADDIRowChangeEventHandler MONYADDIRowDeleted;
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
            public void AddMONYADDIRow(MONYADDIRow row) {
                this.Rows.Add(row);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
            public MONYADDIRow AddMONYADDIRow(int MID, string TMCAMPAIGN, string TMUNIQNUM, bool RECURRED, System.DateTime RECURENDDTE, string REFERENCEID, bool MULTNAME, bool MULTSIGN, int WEBGIFTID, string MISCTEXT, int MONYCCBATID, string MISCJSON) {
                MONYADDIRow rowMONYADDIRow = ((MONYADDIRow)(this.NewRow()));
                object[] columnValuesArray = new object[] {
                        null,
                        MID,
                        TMCAMPAIGN,
                        TMUNIQNUM,
                        RECURRED,
                        RECURENDDTE,
                        REFERENCEID,
                        MULTNAME,
                        MULTSIGN,
                        WEBGIFTID,
                        MISCTEXT,
                        MONYCCBATID,
                        MISCJSON};
                rowMONYADDIRow.ItemArray = columnValuesArray;
                this.Rows.Add(rowMONYADDIRow);
                return rowMONYADDIRow;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
            public MONYADDIRow FindByMONYADDIID(int MONYADDIID) {
                return ((MONYADDIRow)(this.Rows.Find(new object[] {
                            MONYADDIID})));
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
            public override global::System.Data.DataTable Clone() {
                MONYADDIDataTable cln = ((MONYADDIDataTable)(base.Clone()));
                cln.InitVars();
                return cln;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
            protected override global::System.Data.DataTable CreateInstance() {
                return new MONYADDIDataTable();
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
            internal void InitVars() {
                this.columnMONYADDIID = base.Columns["MONYADDIID"];
                this.columnMID = base.Columns["MID"];
                this.columnTMCAMPAIGN = base.Columns["TMCAMPAIGN"];
                this.columnTMUNIQNUM = base.Columns["TMUNIQNUM"];
                this.columnRECURRED = base.Columns["RECURRED"];
                this.columnRECURENDDTE = base.Columns["RECURENDDTE"];
                this.columnREFERENCEID = base.Columns["REFERENCEID"];
                this.columnMULTNAME = base.Columns["MULTNAME"];
                this.columnMULTSIGN = base.Columns["MULTSIGN"];
                this.columnWEBGIFTID = base.Columns["WEBGIFTID"];
                this.columnMISCTEXT = base.Columns["MISCTEXT"];
                this.columnMONYCCBATID = base.Columns["MONYCCBATID"];
                this.columnMISCJSON = base.Columns["MISCJSON"];
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
            private void InitClass() {
                this.columnMONYADDIID = new global::System.Data.DataColumn("MONYADDIID", typeof(int), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnMONYADDIID);
                this.columnMID = new global::System.Data.DataColumn("MID", typeof(int), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnMID);
                this.columnTMCAMPAIGN = new global::System.Data.DataColumn("TMCAMPAIGN", typeof(string), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnTMCAMPAIGN);
                this.columnTMUNIQNUM = new global::System.Data.DataColumn("TMUNIQNUM", typeof(string), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnTMUNIQNUM);
                this.columnRECURRED = new global::System.Data.DataColumn("RECURRED", typeof(bool), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnRECURRED);
                this.columnRECURENDDTE = new global::System.Data.DataColumn("RECURENDDTE", typeof(global::System.DateTime), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnRECURENDDTE);
                this.columnREFERENCEID = new global::System.Data.DataColumn("REFERENCEID", typeof(string), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnREFERENCEID);
                this.columnMULTNAME = new global::System.Data.DataColumn("MULTNAME", typeof(bool), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnMULTNAME);
                this.columnMULTSIGN = new global::System.Data.DataColumn("MULTSIGN", typeof(bool), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnMULTSIGN);
                this.columnWEBGIFTID = new global::System.Data.DataColumn("WEBGIFTID", typeof(int), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnWEBGIFTID);
                this.columnMISCTEXT = new global::System.Data.DataColumn("MISCTEXT", typeof(string), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnMISCTEXT);
                this.columnMONYCCBATID = new global::System.Data.DataColumn("MONYCCBATID", typeof(int), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnMONYCCBATID);
                this.columnMISCJSON = new global::System.Data.DataColumn("MISCJSON", typeof(string), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnMISCJSON);
                this.Constraints.Add(new global::System.Data.UniqueConstraint("Constraint1", new global::System.Data.DataColumn[] {
                                this.columnMONYADDIID}, true));
                this.columnMONYADDIID.AutoIncrement = true;
                this.columnMONYADDIID.AutoIncrementSeed = -1;
                this.columnMONYADDIID.AutoIncrementStep = -1;
                this.columnMONYADDIID.AllowDBNull = false;
                this.columnMONYADDIID.ReadOnly = true;
                this.columnMONYADDIID.Unique = true;
                this.columnTMCAMPAIGN.MaxLength = 20;
                this.columnTMUNIQNUM.MaxLength = 20;
                this.columnRECURRED.AllowDBNull = false;
                this.columnREFERENCEID.MaxLength = 50;
                this.columnMULTNAME.AllowDBNull = false;
                this.columnMULTSIGN.AllowDBNull = false;
                this.columnMISCTEXT.MaxLength = 500;
                this.columnMISCJSON.MaxLength = 2147483647;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
            public MONYADDIRow NewMONYADDIRow() {
                return ((MONYADDIRow)(this.NewRow()));
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
            protected override global::System.Data.DataRow NewRowFromBuilder(global::System.Data.DataRowBuilder builder) {
                return new MONYADDIRow(builder);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
            protected override global::System.Type GetRowType() {
                return typeof(MONYADDIRow);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
            protected override void OnRowChanged(global::System.Data.DataRowChangeEventArgs e) {
                base.OnRowChanged(e);
                if ((this.MONYADDIRowChanged != null)) {
                    this.MONYADDIRowChanged(this, new MONYADDIRowChangeEvent(((MONYADDIRow)(e.Row)), e.Action));
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
            protected override void OnRowChanging(global::System.Data.DataRowChangeEventArgs e) {
                base.OnRowChanging(e);
                if ((this.MONYADDIRowChanging != null)) {
                    this.MONYADDIRowChanging(this, new MONYADDIRowChangeEvent(((MONYADDIRow)(e.Row)), e.Action));
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
            protected override void OnRowDeleted(global::System.Data.DataRowChangeEventArgs e) {
                base.OnRowDeleted(e);
                if ((this.MONYADDIRowDeleted != null)) {
                    this.MONYADDIRowDeleted(this, new MONYADDIRowChangeEvent(((MONYADDIRow)(e.Row)), e.Action));
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
            protected override void OnRowDeleting(global::System.Data.DataRowChangeEventArgs e) {
                base.OnRowDeleting(e);
                if ((this.MONYADDIRowDeleting != null)) {
                    this.MONYADDIRowDeleting(this, new MONYADDIRowChangeEvent(((MONYADDIRow)(e.Row)), e.Action));
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
            public void RemoveMONYADDIRow(MONYADDIRow row) {
                this.Rows.Remove(row);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
            public static global::System.Xml.Schema.XmlSchemaComplexType GetTypedTableSchema(global::System.Xml.Schema.XmlSchemaSet xs) {
                global::System.Xml.Schema.XmlSchemaComplexType type = new global::System.Xml.Schema.XmlSchemaComplexType();
                global::System.Xml.Schema.XmlSchemaSequence sequence = new global::System.Xml.Schema.XmlSchemaSequence();
                dxDEMOCDataSet2 ds = new dxDEMOCDataSet2();
                global::System.Xml.Schema.XmlSchemaAny any1 = new global::System.Xml.Schema.XmlSchemaAny();
                any1.Namespace = "http://www.w3.org/2001/XMLSchema";
                any1.MinOccurs = new decimal(0);
                any1.MaxOccurs = decimal.MaxValue;
                any1.ProcessContents = global::System.Xml.Schema.XmlSchemaContentProcessing.Lax;
                sequence.Items.Add(any1);
                global::System.Xml.Schema.XmlSchemaAny any2 = new global::System.Xml.Schema.XmlSchemaAny();
                any2.Namespace = "urn:schemas-microsoft-com:xml-diffgram-v1";
                any2.MinOccurs = new decimal(1);
                any2.ProcessContents = global::System.Xml.Schema.XmlSchemaContentProcessing.Lax;
                sequence.Items.Add(any2);
                global::System.Xml.Schema.XmlSchemaAttribute attribute1 = new global::System.Xml.Schema.XmlSchemaAttribute();
                attribute1.Name = "namespace";
                attribute1.FixedValue = ds.Namespace;
                type.Attributes.Add(attribute1);
                global::System.Xml.Schema.XmlSchemaAttribute attribute2 = new global::System.Xml.Schema.XmlSchemaAttribute();
                attribute2.Name = "tableTypeName";
                attribute2.FixedValue = "MONYADDIDataTable";
                type.Attributes.Add(attribute2);
                type.Particle = sequence;
                global::System.Xml.Schema.XmlSchema dsSchema = ds.GetSchemaSerializable();
                if (xs.Contains(dsSchema.TargetNamespace)) {
                    global::System.IO.MemoryStream s1 = new global::System.IO.MemoryStream();
                    global::System.IO.MemoryStream s2 = new global::System.IO.MemoryStream();
                    try {
                        global::System.Xml.Schema.XmlSchema schema = null;
                        dsSchema.Write(s1);
                        for (global::System.Collections.IEnumerator schemas = xs.Schemas(dsSchema.TargetNamespace).GetEnumerator(); schemas.MoveNext(); ) {
                            schema = ((global::System.Xml.Schema.XmlSchema)(schemas.Current));
                            s2.SetLength(0);
                            schema.Write(s2);
                            if ((s1.Length == s2.Length)) {
                                s1.Position = 0;
                                s2.Position = 0;
                                for (; ((s1.Position != s1.Length) 
                                            && (s1.ReadByte() == s2.ReadByte())); ) {
                                    ;
                                }
                                if ((s1.Position == s1.Length)) {
                                    return type;
                                }
                            }
                        }
                    }
                    finally {
                        if ((s1 != null)) {
                            s1.Close();
                        }
                        if ((s2 != null)) {
                            s2.Close();
                        }
                    }
                }
                xs.Add(dsSchema);
                return type;
            }
        }
        
        /// <summary>
        ///Represents the strongly named DataTable class.
        ///</summary>
        [global::System.Serializable()]
        [global::System.Xml.Serialization.XmlSchemaProviderAttribute("GetTypedTableSchema")]
        public partial class v_people_monyDataTable : global::System.Data.TypedTableBase<v_people_monyRow> {
            
            private global::System.Data.DataColumn columnPID;
            
            private global::System.Data.DataColumn columnMID;
            
            private global::System.Data.DataColumn columnBATCHDTE;
            
            private global::System.Data.DataColumn columnBATCHNO;
            
            private global::System.Data.DataColumn columnAMT;
            
            private global::System.Data.DataColumn columnTRACKNO;
            
            private global::System.Data.DataColumn columnMONYTYPE;
            
            private global::System.Data.DataColumn columnFUNDCODE;
            
            private global::System.Data.DataColumn columnSRCECODE;
            
            private global::System.Data.DataColumn columnSRCEDESC;
            
            private global::System.Data.DataColumn columnPKGECODE;
            
            private global::System.Data.DataColumn columnPKGEDESC;
            
            private global::System.Data.DataColumn columnPROGTYPE;
            
            private global::System.Data.DataColumn columnPROGDESC;
            
            private global::System.Data.DataColumn columnCENTERCODE;
            
            private global::System.Data.DataColumn columnCENTERDESC;
            
            private global::System.Data.DataColumn columnCAMPGNCODE;
            
            private global::System.Data.DataColumn columnCAMPGNDESC;
            
            private global::System.Data.DataColumn columnADJTYPE;
            
            private global::System.Data.DataColumn columnADJDESC;
            
            private global::System.Data.DataColumn columnCOUNTER;
            
            private global::System.Data.DataColumn columnSOFTMONEY;
            
            private global::System.Data.DataColumn columnFUNDORGNAME;
            
            private global::System.Data.DataColumn columnFUNDORGADDR;
            
            private global::System.Data.DataColumn columnEXCEP;
            
            private global::System.Data.DataColumn columnGIFTTYPE;
            
            private global::System.Data.DataColumn columnMONYCODE;
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
            public v_people_monyDataTable() {
                this.TableName = "v_people_mony";
                this.BeginInit();
                this.InitClass();
                this.EndInit();
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
            internal v_people_monyDataTable(global::System.Data.DataTable table) {
                this.TableName = table.TableName;
                if ((table.CaseSensitive != table.DataSet.CaseSensitive)) {
                    this.CaseSensitive = table.CaseSensitive;
                }
                if ((table.Locale.ToString() != table.DataSet.Locale.ToString())) {
                    this.Locale = table.Locale;
                }
                if ((table.Namespace != table.DataSet.Namespace)) {
                    this.Namespace = table.Namespace;
                }
                this.Prefix = table.Prefix;
                this.MinimumCapacity = table.MinimumCapacity;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
            protected v_people_monyDataTable(global::System.Runtime.Serialization.SerializationInfo info, global::System.Runtime.Serialization.StreamingContext context) : 
                    base(info, context) {
                this.InitVars();
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
            public global::System.Data.DataColumn PIDColumn {
                get {
                    return this.columnPID;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
            public global::System.Data.DataColumn MIDColumn {
                get {
                    return this.columnMID;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
            public global::System.Data.DataColumn BATCHDTEColumn {
                get {
                    return this.columnBATCHDTE;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
            public global::System.Data.DataColumn BATCHNOColumn {
                get {
                    return this.columnBATCHNO;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
            public global::System.Data.DataColumn AMTColumn {
                get {
                    return this.columnAMT;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
            public global::System.Data.DataColumn TRACKNOColumn {
                get {
                    return this.columnTRACKNO;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
            public global::System.Data.DataColumn MONYTYPEColumn {
                get {
                    return this.columnMONYTYPE;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
            public global::System.Data.DataColumn FUNDCODEColumn {
                get {
                    return this.columnFUNDCODE;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
            public global::System.Data.DataColumn SRCECODEColumn {
                get {
                    return this.columnSRCECODE;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
            public global::System.Data.DataColumn SRCEDESCColumn {
                get {
                    return this.columnSRCEDESC;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
            public global::System.Data.DataColumn PKGECODEColumn {
                get {
                    return this.columnPKGECODE;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
            public global::System.Data.DataColumn PKGEDESCColumn {
                get {
                    return this.columnPKGEDESC;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
            public global::System.Data.DataColumn PROGTYPEColumn {
                get {
                    return this.columnPROGTYPE;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
            public global::System.Data.DataColumn PROGDESCColumn {
                get {
                    return this.columnPROGDESC;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
            public global::System.Data.DataColumn CENTERCODEColumn {
                get {
                    return this.columnCENTERCODE;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
            public global::System.Data.DataColumn CENTERDESCColumn {
                get {
                    return this.columnCENTERDESC;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
            public global::System.Data.DataColumn CAMPGNCODEColumn {
                get {
                    return this.columnCAMPGNCODE;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
            public global::System.Data.DataColumn CAMPGNDESCColumn {
                get {
                    return this.columnCAMPGNDESC;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
            public global::System.Data.DataColumn ADJTYPEColumn {
                get {
                    return this.columnADJTYPE;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
            public global::System.Data.DataColumn ADJDESCColumn {
                get {
                    return this.columnADJDESC;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
            public global::System.Data.DataColumn COUNTERColumn {
                get {
                    return this.columnCOUNTER;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
            public global::System.Data.DataColumn SOFTMONEYColumn {
                get {
                    return this.columnSOFTMONEY;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
            public global::System.Data.DataColumn FUNDORGNAMEColumn {
                get {
                    return this.columnFUNDORGNAME;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
            public global::System.Data.DataColumn FUNDORGADDRColumn {
                get {
                    return this.columnFUNDORGADDR;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
            public global::System.Data.DataColumn EXCEPColumn {
                get {
                    return this.columnEXCEP;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
            public global::System.Data.DataColumn GIFTTYPEColumn {
                get {
                    return this.columnGIFTTYPE;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
            public global::System.Data.DataColumn MONYCODEColumn {
                get {
                    return this.columnMONYCODE;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
            [global::System.ComponentModel.Browsable(false)]
            public int Count {
                get {
                    return this.Rows.Count;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
            public v_people_monyRow this[int index] {
                get {
                    return ((v_people_monyRow)(this.Rows[index]));
                }
            }
            
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
            public event v_people_monyRowChangeEventHandler v_people_monyRowChanging;
            
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
            public event v_people_monyRowChangeEventHandler v_people_monyRowChanged;
            
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
            public event v_people_monyRowChangeEventHandler v_people_monyRowDeleting;
            
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
            public event v_people_monyRowChangeEventHandler v_people_monyRowDeleted;
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
            public void Addv_people_monyRow(v_people_monyRow row) {
                this.Rows.Add(row);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
            public v_people_monyRow Addv_people_monyRow(
                        int PID, 
                        int MID, 
                        System.DateTime BATCHDTE, 
                        string BATCHNO, 
                        decimal AMT, 
                        int TRACKNO, 
                        string MONYTYPE, 
                        string FUNDCODE, 
                        string SRCECODE, 
                        string SRCEDESC, 
                        string PKGECODE, 
                        string PKGEDESC, 
                        string PROGTYPE, 
                        string PROGDESC, 
                        string CENTERCODE, 
                        string CENTERDESC, 
                        string CAMPGNCODE, 
                        string CAMPGNDESC, 
                        string ADJTYPE, 
                        string ADJDESC, 
                        byte COUNTER, 
                        byte SOFTMONEY, 
                        string FUNDORGNAME, 
                        string FUNDORGADDR, 
                        string EXCEP, 
                        string GIFTTYPE, 
                        short MONYCODE) {
                v_people_monyRow rowv_people_monyRow = ((v_people_monyRow)(this.NewRow()));
                object[] columnValuesArray = new object[] {
                        PID,
                        MID,
                        BATCHDTE,
                        BATCHNO,
                        AMT,
                        TRACKNO,
                        MONYTYPE,
                        FUNDCODE,
                        SRCECODE,
                        SRCEDESC,
                        PKGECODE,
                        PKGEDESC,
                        PROGTYPE,
                        PROGDESC,
                        CENTERCODE,
                        CENTERDESC,
                        CAMPGNCODE,
                        CAMPGNDESC,
                        ADJTYPE,
                        ADJDESC,
                        COUNTER,
                        SOFTMONEY,
                        FUNDORGNAME,
                        FUNDORGADDR,
                        EXCEP,
                        GIFTTYPE,
                        MONYCODE};
                rowv_people_monyRow.ItemArray = columnValuesArray;
                this.Rows.Add(rowv_people_monyRow);
                return rowv_people_monyRow;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
            public v_people_monyRow FindByMID(int MID) {
                return ((v_people_monyRow)(this.Rows.Find(new object[] {
                            MID})));
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
            public override global::System.Data.DataTable Clone() {
                v_people_monyDataTable cln = ((v_people_monyDataTable)(base.Clone()));
                cln.InitVars();
                return cln;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
            protected override global::System.Data.DataTable CreateInstance() {
                return new v_people_monyDataTable();
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
            internal void InitVars() {
                this.columnPID = base.Columns["PID"];
                this.columnMID = base.Columns["MID"];
                this.columnBATCHDTE = base.Columns["BATCHDTE"];
                this.columnBATCHNO = base.Columns["BATCHNO"];
                this.columnAMT = base.Columns["AMT"];
                this.columnTRACKNO = base.Columns["TRACKNO"];
                this.columnMONYTYPE = base.Columns["MONYTYPE"];
                this.columnFUNDCODE = base.Columns["FUNDCODE"];
                this.columnSRCECODE = base.Columns["SRCECODE"];
                this.columnSRCEDESC = base.Columns["SRCEDESC"];
                this.columnPKGECODE = base.Columns["PKGECODE"];
                this.columnPKGEDESC = base.Columns["PKGEDESC"];
                this.columnPROGTYPE = base.Columns["PROGTYPE"];
                this.columnPROGDESC = base.Columns["PROGDESC"];
                this.columnCENTERCODE = base.Columns["CENTERCODE"];
                this.columnCENTERDESC = base.Columns["CENTERDESC"];
                this.columnCAMPGNCODE = base.Columns["CAMPGNCODE"];
                this.columnCAMPGNDESC = base.Columns["CAMPGNDESC"];
                this.columnADJTYPE = base.Columns["ADJTYPE"];
                this.columnADJDESC = base.Columns["ADJDESC"];
                this.columnCOUNTER = base.Columns["COUNTER"];
                this.columnSOFTMONEY = base.Columns["SOFTMONEY"];
                this.columnFUNDORGNAME = base.Columns["FUNDORGNAME"];
                this.columnFUNDORGADDR = base.Columns["FUNDORGADDR"];
                this.columnEXCEP = base.Columns["EXCEP"];
                this.columnGIFTTYPE = base.Columns["GIFTTYPE"];
                this.columnMONYCODE = base.Columns["MONYCODE"];
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
            private void InitClass() {
                this.columnPID = new global::System.Data.DataColumn("PID", typeof(int), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnPID);
                this.columnMID = new global::System.Data.DataColumn("MID", typeof(int), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnMID);
                this.columnBATCHDTE = new global::System.Data.DataColumn("BATCHDTE", typeof(global::System.DateTime), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnBATCHDTE);
                this.columnBATCHNO = new global::System.Data.DataColumn("BATCHNO", typeof(string), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnBATCHNO);
                this.columnAMT = new global::System.Data.DataColumn("AMT", typeof(decimal), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnAMT);
                this.columnTRACKNO = new global::System.Data.DataColumn("TRACKNO", typeof(int), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnTRACKNO);
                this.columnMONYTYPE = new global::System.Data.DataColumn("MONYTYPE", typeof(string), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnMONYTYPE);
                this.columnFUNDCODE = new global::System.Data.DataColumn("FUNDCODE", typeof(string), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnFUNDCODE);
                this.columnSRCECODE = new global::System.Data.DataColumn("SRCECODE", typeof(string), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnSRCECODE);
                this.columnSRCEDESC = new global::System.Data.DataColumn("SRCEDESC", typeof(string), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnSRCEDESC);
                this.columnPKGECODE = new global::System.Data.DataColumn("PKGECODE", typeof(string), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnPKGECODE);
                this.columnPKGEDESC = new global::System.Data.DataColumn("PKGEDESC", typeof(string), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnPKGEDESC);
                this.columnPROGTYPE = new global::System.Data.DataColumn("PROGTYPE", typeof(string), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnPROGTYPE);
                this.columnPROGDESC = new global::System.Data.DataColumn("PROGDESC", typeof(string), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnPROGDESC);
                this.columnCENTERCODE = new global::System.Data.DataColumn("CENTERCODE", typeof(string), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnCENTERCODE);
                this.columnCENTERDESC = new global::System.Data.DataColumn("CENTERDESC", typeof(string), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnCENTERDESC);
                this.columnCAMPGNCODE = new global::System.Data.DataColumn("CAMPGNCODE", typeof(string), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnCAMPGNCODE);
                this.columnCAMPGNDESC = new global::System.Data.DataColumn("CAMPGNDESC", typeof(string), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnCAMPGNDESC);
                this.columnADJTYPE = new global::System.Data.DataColumn("ADJTYPE", typeof(string), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnADJTYPE);
                this.columnADJDESC = new global::System.Data.DataColumn("ADJDESC", typeof(string), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnADJDESC);
                this.columnCOUNTER = new global::System.Data.DataColumn("COUNTER", typeof(byte), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnCOUNTER);
                this.columnSOFTMONEY = new global::System.Data.DataColumn("SOFTMONEY", typeof(byte), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnSOFTMONEY);
                this.columnFUNDORGNAME = new global::System.Data.DataColumn("FUNDORGNAME", typeof(string), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnFUNDORGNAME);
                this.columnFUNDORGADDR = new global::System.Data.DataColumn("FUNDORGADDR", typeof(string), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnFUNDORGADDR);
                this.columnEXCEP = new global::System.Data.DataColumn("EXCEP", typeof(string), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnEXCEP);
                this.columnGIFTTYPE = new global::System.Data.DataColumn("GIFTTYPE", typeof(string), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnGIFTTYPE);
                this.columnMONYCODE = new global::System.Data.DataColumn("MONYCODE", typeof(short), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnMONYCODE);
                this.Constraints.Add(new global::System.Data.UniqueConstraint("Constraint1", new global::System.Data.DataColumn[] {
                                this.columnMID}, true));
                this.columnPID.AllowDBNull = false;
                this.columnMID.AllowDBNull = false;
                this.columnMID.Unique = true;
                this.columnBATCHNO.MaxLength = 9;
                this.columnMONYTYPE.MaxLength = 5;
                this.columnFUNDCODE.AllowDBNull = false;
                this.columnFUNDCODE.MaxLength = 5;
                this.columnSRCECODE.MaxLength = 15;
                this.columnSRCEDESC.MaxLength = 80;
                this.columnPKGECODE.MaxLength = 12;
                this.columnPKGEDESC.MaxLength = 50;
                this.columnPROGTYPE.MaxLength = 5;
                this.columnPROGDESC.MaxLength = 50;
                this.columnCENTERCODE.MaxLength = 10;
                this.columnCENTERDESC.MaxLength = 50;
                this.columnCAMPGNCODE.MaxLength = 7;
                this.columnCAMPGNDESC.MaxLength = 40;
                this.columnADJTYPE.MaxLength = 5;
                this.columnADJDESC.MaxLength = 50;
                this.columnSOFTMONEY.AllowDBNull = false;
                this.columnFUNDORGNAME.ReadOnly = true;
                this.columnFUNDORGNAME.MaxLength = 100;
                this.columnFUNDORGADDR.ReadOnly = true;
                this.columnFUNDORGADDR.MaxLength = 250;
                this.columnEXCEP.MaxLength = 10;
                this.columnGIFTTYPE.MaxLength = 50;
                this.columnMONYCODE.AllowDBNull = false;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
            public v_people_monyRow Newv_people_monyRow() {
                return ((v_people_monyRow)(this.NewRow()));
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
            protected override global::System.Data.DataRow NewRowFromBuilder(global::System.Data.DataRowBuilder builder) {
                return new v_people_monyRow(builder);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
            protected override global::System.Type GetRowType() {
                return typeof(v_people_monyRow);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
            protected override void OnRowChanged(global::System.Data.DataRowChangeEventArgs e) {
                base.OnRowChanged(e);
                if ((this.v_people_monyRowChanged != null)) {
                    this.v_people_monyRowChanged(this, new v_people_monyRowChangeEvent(((v_people_monyRow)(e.Row)), e.Action));
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
            protected override void OnRowChanging(global::System.Data.DataRowChangeEventArgs e) {
                base.OnRowChanging(e);
                if ((this.v_people_monyRowChanging != null)) {
                    this.v_people_monyRowChanging(this, new v_people_monyRowChangeEvent(((v_people_monyRow)(e.Row)), e.Action));
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
            protected override void OnRowDeleted(global::System.Data.DataRowChangeEventArgs e) {
                base.OnRowDeleted(e);
                if ((this.v_people_monyRowDeleted != null)) {
                    this.v_people_monyRowDeleted(this, new v_people_monyRowChangeEvent(((v_people_monyRow)(e.Row)), e.Action));
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
            protected override void OnRowDeleting(global::System.Data.DataRowChangeEventArgs e) {
                base.OnRowDeleting(e);
                if ((this.v_people_monyRowDeleting != null)) {
                    this.v_people_monyRowDeleting(this, new v_people_monyRowChangeEvent(((v_people_monyRow)(e.Row)), e.Action));
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
            public void Removev_people_monyRow(v_people_monyRow row) {
                this.Rows.Remove(row);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
            public static global::System.Xml.Schema.XmlSchemaComplexType GetTypedTableSchema(global::System.Xml.Schema.XmlSchemaSet xs) {
                global::System.Xml.Schema.XmlSchemaComplexType type = new global::System.Xml.Schema.XmlSchemaComplexType();
                global::System.Xml.Schema.XmlSchemaSequence sequence = new global::System.Xml.Schema.XmlSchemaSequence();
                dxDEMOCDataSet2 ds = new dxDEMOCDataSet2();
                global::System.Xml.Schema.XmlSchemaAny any1 = new global::System.Xml.Schema.XmlSchemaAny();
                any1.Namespace = "http://www.w3.org/2001/XMLSchema";
                any1.MinOccurs = new decimal(0);
                any1.MaxOccurs = decimal.MaxValue;
                any1.ProcessContents = global::System.Xml.Schema.XmlSchemaContentProcessing.Lax;
                sequence.Items.Add(any1);
                global::System.Xml.Schema.XmlSchemaAny any2 = new global::System.Xml.Schema.XmlSchemaAny();
                any2.Namespace = "urn:schemas-microsoft-com:xml-diffgram-v1";
                any2.MinOccurs = new decimal(1);
                any2.ProcessContents = global::System.Xml.Schema.XmlSchemaContentProcessing.Lax;
                sequence.Items.Add(any2);
                global::System.Xml.Schema.XmlSchemaAttribute attribute1 = new global::System.Xml.Schema.XmlSchemaAttribute();
                attribute1.Name = "namespace";
                attribute1.FixedValue = ds.Namespace;
                type.Attributes.Add(attribute1);
                global::System.Xml.Schema.XmlSchemaAttribute attribute2 = new global::System.Xml.Schema.XmlSchemaAttribute();
                attribute2.Name = "tableTypeName";
                attribute2.FixedValue = "v_people_monyDataTable";
                type.Attributes.Add(attribute2);
                type.Particle = sequence;
                global::System.Xml.Schema.XmlSchema dsSchema = ds.GetSchemaSerializable();
                if (xs.Contains(dsSchema.TargetNamespace)) {
                    global::System.IO.MemoryStream s1 = new global::System.IO.MemoryStream();
                    global::System.IO.MemoryStream s2 = new global::System.IO.MemoryStream();
                    try {
                        global::System.Xml.Schema.XmlSchema schema = null;
                        dsSchema.Write(s1);
                        for (global::System.Collections.IEnumerator schemas = xs.Schemas(dsSchema.TargetNamespace).GetEnumerator(); schemas.MoveNext(); ) {
                            schema = ((global::System.Xml.Schema.XmlSchema)(schemas.Current));
                            s2.SetLength(0);
                            schema.Write(s2);
                            if ((s1.Length == s2.Length)) {
                                s1.Position = 0;
                                s2.Position = 0;
                                for (; ((s1.Position != s1.Length) 
                                            && (s1.ReadByte() == s2.ReadByte())); ) {
                                    ;
                                }
                                if ((s1.Position == s1.Length)) {
                                    return type;
                                }
                            }
                        }
                    }
                    finally {
                        if ((s1 != null)) {
                            s1.Close();
                        }
                        if ((s2 != null)) {
                            s2.Close();
                        }
                    }
                }
                xs.Add(dsSchema);
                return type;
            }
        }
        
        /// <summary>
        ///Represents strongly named DataRow class.
        ///</summary>
        public partial class MONYADDIRow : global::System.Data.DataRow {
            
            private MONYADDIDataTable tableMONYADDI;
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
            internal MONYADDIRow(global::System.Data.DataRowBuilder rb) : 
                    base(rb) {
                this.tableMONYADDI = ((MONYADDIDataTable)(this.Table));
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
            public int MONYADDIID {
                get {
                    return ((int)(this[this.tableMONYADDI.MONYADDIIDColumn]));
                }
                set {
                    this[this.tableMONYADDI.MONYADDIIDColumn] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
            public int MID {
                get {
                    try {
                        return ((int)(this[this.tableMONYADDI.MIDColumn]));
                    }
                    catch (global::System.InvalidCastException e) {
                        throw new global::System.Data.StrongTypingException("The value for column \'MID\' in table \'MONYADDI\' is DBNull.", e);
                    }
                }
                set {
                    this[this.tableMONYADDI.MIDColumn] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
            public string TMCAMPAIGN {
                get {
                    try {
                        return ((string)(this[this.tableMONYADDI.TMCAMPAIGNColumn]));
                    }
                    catch (global::System.InvalidCastException e) {
                        throw new global::System.Data.StrongTypingException("The value for column \'TMCAMPAIGN\' in table \'MONYADDI\' is DBNull.", e);
                    }
                }
                set {
                    this[this.tableMONYADDI.TMCAMPAIGNColumn] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
            public string TMUNIQNUM {
                get {
                    try {
                        return ((string)(this[this.tableMONYADDI.TMUNIQNUMColumn]));
                    }
                    catch (global::System.InvalidCastException e) {
                        throw new global::System.Data.StrongTypingException("The value for column \'TMUNIQNUM\' in table \'MONYADDI\' is DBNull.", e);
                    }
                }
                set {
                    this[this.tableMONYADDI.TMUNIQNUMColumn] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
            public bool RECURRED {
                get {
                    return ((bool)(this[this.tableMONYADDI.RECURREDColumn]));
                }
                set {
                    this[this.tableMONYADDI.RECURREDColumn] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
            public System.DateTime RECURENDDTE {
                get {
                    try {
                        return ((global::System.DateTime)(this[this.tableMONYADDI.RECURENDDTEColumn]));
                    }
                    catch (global::System.InvalidCastException e) {
                        throw new global::System.Data.StrongTypingException("The value for column \'RECURENDDTE\' in table \'MONYADDI\' is DBNull.", e);
                    }
                }
                set {
                    this[this.tableMONYADDI.RECURENDDTEColumn] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
            public string REFERENCEID {
                get {
                    try {
                        return ((string)(this[this.tableMONYADDI.REFERENCEIDColumn]));
                    }
                    catch (global::System.InvalidCastException e) {
                        throw new global::System.Data.StrongTypingException("The value for column \'REFERENCEID\' in table \'MONYADDI\' is DBNull.", e);
                    }
                }
                set {
                    this[this.tableMONYADDI.REFERENCEIDColumn] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
            public bool MULTNAME {
                get {
                    return ((bool)(this[this.tableMONYADDI.MULTNAMEColumn]));
                }
                set {
                    this[this.tableMONYADDI.MULTNAMEColumn] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
            public bool MULTSIGN {
                get {
                    return ((bool)(this[this.tableMONYADDI.MULTSIGNColumn]));
                }
                set {
                    this[this.tableMONYADDI.MULTSIGNColumn] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
            public int WEBGIFTID {
                get {
                    try {
                        return ((int)(this[this.tableMONYADDI.WEBGIFTIDColumn]));
                    }
                    catch (global::System.InvalidCastException e) {
                        throw new global::System.Data.StrongTypingException("The value for column \'WEBGIFTID\' in table \'MONYADDI\' is DBNull.", e);
                    }
                }
                set {
                    this[this.tableMONYADDI.WEBGIFTIDColumn] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
            public string MISCTEXT {
                get {
                    try {
                        return ((string)(this[this.tableMONYADDI.MISCTEXTColumn]));
                    }
                    catch (global::System.InvalidCastException e) {
                        throw new global::System.Data.StrongTypingException("The value for column \'MISCTEXT\' in table \'MONYADDI\' is DBNull.", e);
                    }
                }
                set {
                    this[this.tableMONYADDI.MISCTEXTColumn] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
            public int MONYCCBATID {
                get {
                    try {
                        return ((int)(this[this.tableMONYADDI.MONYCCBATIDColumn]));
                    }
                    catch (global::System.InvalidCastException e) {
                        throw new global::System.Data.StrongTypingException("The value for column \'MONYCCBATID\' in table \'MONYADDI\' is DBNull.", e);
                    }
                }
                set {
                    this[this.tableMONYADDI.MONYCCBATIDColumn] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
            public string MISCJSON {
                get {
                    try {
                        return ((string)(this[this.tableMONYADDI.MISCJSONColumn]));
                    }
                    catch (global::System.InvalidCastException e) {
                        throw new global::System.Data.StrongTypingException("The value for column \'MISCJSON\' in table \'MONYADDI\' is DBNull.", e);
                    }
                }
                set {
                    this[this.tableMONYADDI.MISCJSONColumn] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
            public bool IsMIDNull() {
                return this.IsNull(this.tableMONYADDI.MIDColumn);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
            public void SetMIDNull() {
                this[this.tableMONYADDI.MIDColumn] = global::System.Convert.DBNull;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
            public bool IsTMCAMPAIGNNull() {
                return this.IsNull(this.tableMONYADDI.TMCAMPAIGNColumn);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
            public void SetTMCAMPAIGNNull() {
                this[this.tableMONYADDI.TMCAMPAIGNColumn] = global::System.Convert.DBNull;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
            public bool IsTMUNIQNUMNull() {
                return this.IsNull(this.tableMONYADDI.TMUNIQNUMColumn);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
            public void SetTMUNIQNUMNull() {
                this[this.tableMONYADDI.TMUNIQNUMColumn] = global::System.Convert.DBNull;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
            public bool IsRECURENDDTENull() {
                return this.IsNull(this.tableMONYADDI.RECURENDDTEColumn);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
            public void SetRECURENDDTENull() {
                this[this.tableMONYADDI.RECURENDDTEColumn] = global::System.Convert.DBNull;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
            public bool IsREFERENCEIDNull() {
                return this.IsNull(this.tableMONYADDI.REFERENCEIDColumn);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
            public void SetREFERENCEIDNull() {
                this[this.tableMONYADDI.REFERENCEIDColumn] = global::System.Convert.DBNull;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
            public bool IsWEBGIFTIDNull() {
                return this.IsNull(this.tableMONYADDI.WEBGIFTIDColumn);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
            public void SetWEBGIFTIDNull() {
                this[this.tableMONYADDI.WEBGIFTIDColumn] = global::System.Convert.DBNull;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
            public bool IsMISCTEXTNull() {
                return this.IsNull(this.tableMONYADDI.MISCTEXTColumn);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
            public void SetMISCTEXTNull() {
                this[this.tableMONYADDI.MISCTEXTColumn] = global::System.Convert.DBNull;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
            public bool IsMONYCCBATIDNull() {
                return this.IsNull(this.tableMONYADDI.MONYCCBATIDColumn);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
            public void SetMONYCCBATIDNull() {
                this[this.tableMONYADDI.MONYCCBATIDColumn] = global::System.Convert.DBNull;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
            public bool IsMISCJSONNull() {
                return this.IsNull(this.tableMONYADDI.MISCJSONColumn);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
            public void SetMISCJSONNull() {
                this[this.tableMONYADDI.MISCJSONColumn] = global::System.Convert.DBNull;
            }
        }
        
        /// <summary>
        ///Represents strongly named DataRow class.
        ///</summary>
        public partial class v_people_monyRow : global::System.Data.DataRow {
            
            private v_people_monyDataTable tablev_people_mony;
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
            internal v_people_monyRow(global::System.Data.DataRowBuilder rb) : 
                    base(rb) {
                this.tablev_people_mony = ((v_people_monyDataTable)(this.Table));
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
            public int PID {
                get {
                    return ((int)(this[this.tablev_people_mony.PIDColumn]));
                }
                set {
                    this[this.tablev_people_mony.PIDColumn] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
            public int MID {
                get {
                    return ((int)(this[this.tablev_people_mony.MIDColumn]));
                }
                set {
                    this[this.tablev_people_mony.MIDColumn] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
            public System.DateTime BATCHDTE {
                get {
                    try {
                        return ((global::System.DateTime)(this[this.tablev_people_mony.BATCHDTEColumn]));
                    }
                    catch (global::System.InvalidCastException e) {
                        throw new global::System.Data.StrongTypingException("The value for column \'BATCHDTE\' in table \'v_people_mony\' is DBNull.", e);
                    }
                }
                set {
                    this[this.tablev_people_mony.BATCHDTEColumn] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
            public string BATCHNO {
                get {
                    try {
                        return ((string)(this[this.tablev_people_mony.BATCHNOColumn]));
                    }
                    catch (global::System.InvalidCastException e) {
                        throw new global::System.Data.StrongTypingException("The value for column \'BATCHNO\' in table \'v_people_mony\' is DBNull.", e);
                    }
                }
                set {
                    this[this.tablev_people_mony.BATCHNOColumn] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
            public decimal AMT {
                get {
                    try {
                        return ((decimal)(this[this.tablev_people_mony.AMTColumn]));
                    }
                    catch (global::System.InvalidCastException e) {
                        throw new global::System.Data.StrongTypingException("The value for column \'AMT\' in table \'v_people_mony\' is DBNull.", e);
                    }
                }
                set {
                    this[this.tablev_people_mony.AMTColumn] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
            public int TRACKNO {
                get {
                    try {
                        return ((int)(this[this.tablev_people_mony.TRACKNOColumn]));
                    }
                    catch (global::System.InvalidCastException e) {
                        throw new global::System.Data.StrongTypingException("The value for column \'TRACKNO\' in table \'v_people_mony\' is DBNull.", e);
                    }
                }
                set {
                    this[this.tablev_people_mony.TRACKNOColumn] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
            public string MONYTYPE {
                get {
                    try {
                        return ((string)(this[this.tablev_people_mony.MONYTYPEColumn]));
                    }
                    catch (global::System.InvalidCastException e) {
                        throw new global::System.Data.StrongTypingException("The value for column \'MONYTYPE\' in table \'v_people_mony\' is DBNull.", e);
                    }
                }
                set {
                    this[this.tablev_people_mony.MONYTYPEColumn] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
            public string FUNDCODE {
                get {
                    return ((string)(this[this.tablev_people_mony.FUNDCODEColumn]));
                }
                set {
                    this[this.tablev_people_mony.FUNDCODEColumn] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
            public string SRCECODE {
                get {
                    try {
                        return ((string)(this[this.tablev_people_mony.SRCECODEColumn]));
                    }
                    catch (global::System.InvalidCastException e) {
                        throw new global::System.Data.StrongTypingException("The value for column \'SRCECODE\' in table \'v_people_mony\' is DBNull.", e);
                    }
                }
                set {
                    this[this.tablev_people_mony.SRCECODEColumn] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
            public string SRCEDESC {
                get {
                    try {
                        return ((string)(this[this.tablev_people_mony.SRCEDESCColumn]));
                    }
                    catch (global::System.InvalidCastException e) {
                        throw new global::System.Data.StrongTypingException("The value for column \'SRCEDESC\' in table \'v_people_mony\' is DBNull.", e);
                    }
                }
                set {
                    this[this.tablev_people_mony.SRCEDESCColumn] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
            public string PKGECODE {
                get {
                    try {
                        return ((string)(this[this.tablev_people_mony.PKGECODEColumn]));
                    }
                    catch (global::System.InvalidCastException e) {
                        throw new global::System.Data.StrongTypingException("The value for column \'PKGECODE\' in table \'v_people_mony\' is DBNull.", e);
                    }
                }
                set {
                    this[this.tablev_people_mony.PKGECODEColumn] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
            public string PKGEDESC {
                get {
                    try {
                        return ((string)(this[this.tablev_people_mony.PKGEDESCColumn]));
                    }
                    catch (global::System.InvalidCastException e) {
                        throw new global::System.Data.StrongTypingException("The value for column \'PKGEDESC\' in table \'v_people_mony\' is DBNull.", e);
                    }
                }
                set {
                    this[this.tablev_people_mony.PKGEDESCColumn] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
            public string PROGTYPE {
                get {
                    try {
                        return ((string)(this[this.tablev_people_mony.PROGTYPEColumn]));
                    }
                    catch (global::System.InvalidCastException e) {
                        throw new global::System.Data.StrongTypingException("The value for column \'PROGTYPE\' in table \'v_people_mony\' is DBNull.", e);
                    }
                }
                set {
                    this[this.tablev_people_mony.PROGTYPEColumn] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
            public string PROGDESC {
                get {
                    try {
                        return ((string)(this[this.tablev_people_mony.PROGDESCColumn]));
                    }
                    catch (global::System.InvalidCastException e) {
                        throw new global::System.Data.StrongTypingException("The value for column \'PROGDESC\' in table \'v_people_mony\' is DBNull.", e);
                    }
                }
                set {
                    this[this.tablev_people_mony.PROGDESCColumn] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
            public string CENTERCODE {
                get {
                    try {
                        return ((string)(this[this.tablev_people_mony.CENTERCODEColumn]));
                    }
                    catch (global::System.InvalidCastException e) {
                        throw new global::System.Data.StrongTypingException("The value for column \'CENTERCODE\' in table \'v_people_mony\' is DBNull.", e);
                    }
                }
                set {
                    this[this.tablev_people_mony.CENTERCODEColumn] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
            public string CENTERDESC {
                get {
                    try {
                        return ((string)(this[this.tablev_people_mony.CENTERDESCColumn]));
                    }
                    catch (global::System.InvalidCastException e) {
                        throw new global::System.Data.StrongTypingException("The value for column \'CENTERDESC\' in table \'v_people_mony\' is DBNull.", e);
                    }
                }
                set {
                    this[this.tablev_people_mony.CENTERDESCColumn] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
            public string CAMPGNCODE {
                get {
                    try {
                        return ((string)(this[this.tablev_people_mony.CAMPGNCODEColumn]));
                    }
                    catch (global::System.InvalidCastException e) {
                        throw new global::System.Data.StrongTypingException("The value for column \'CAMPGNCODE\' in table \'v_people_mony\' is DBNull.", e);
                    }
                }
                set {
                    this[this.tablev_people_mony.CAMPGNCODEColumn] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
            public string CAMPGNDESC {
                get {
                    try {
                        return ((string)(this[this.tablev_people_mony.CAMPGNDESCColumn]));
                    }
                    catch (global::System.InvalidCastException e) {
                        throw new global::System.Data.StrongTypingException("The value for column \'CAMPGNDESC\' in table \'v_people_mony\' is DBNull.", e);
                    }
                }
                set {
                    this[this.tablev_people_mony.CAMPGNDESCColumn] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
            public string ADJTYPE {
                get {
                    try {
                        return ((string)(this[this.tablev_people_mony.ADJTYPEColumn]));
                    }
                    catch (global::System.InvalidCastException e) {
                        throw new global::System.Data.StrongTypingException("The value for column \'ADJTYPE\' in table \'v_people_mony\' is DBNull.", e);
                    }
                }
                set {
                    this[this.tablev_people_mony.ADJTYPEColumn] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
            public string ADJDESC {
                get {
                    try {
                        return ((string)(this[this.tablev_people_mony.ADJDESCColumn]));
                    }
                    catch (global::System.InvalidCastException e) {
                        throw new global::System.Data.StrongTypingException("The value for column \'ADJDESC\' in table \'v_people_mony\' is DBNull.", e);
                    }
                }
                set {
                    this[this.tablev_people_mony.ADJDESCColumn] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
            public byte COUNTER {
                get {
                    try {
                        return ((byte)(this[this.tablev_people_mony.COUNTERColumn]));
                    }
                    catch (global::System.InvalidCastException e) {
                        throw new global::System.Data.StrongTypingException("The value for column \'COUNTER\' in table \'v_people_mony\' is DBNull.", e);
                    }
                }
                set {
                    this[this.tablev_people_mony.COUNTERColumn] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
            public byte SOFTMONEY {
                get {
                    return ((byte)(this[this.tablev_people_mony.SOFTMONEYColumn]));
                }
                set {
                    this[this.tablev_people_mony.SOFTMONEYColumn] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
            public string FUNDORGNAME {
                get {
                    try {
                        return ((string)(this[this.tablev_people_mony.FUNDORGNAMEColumn]));
                    }
                    catch (global::System.InvalidCastException e) {
                        throw new global::System.Data.StrongTypingException("The value for column \'FUNDORGNAME\' in table \'v_people_mony\' is DBNull.", e);
                    }
                }
                set {
                    this[this.tablev_people_mony.FUNDORGNAMEColumn] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
            public string FUNDORGADDR {
                get {
                    try {
                        return ((string)(this[this.tablev_people_mony.FUNDORGADDRColumn]));
                    }
                    catch (global::System.InvalidCastException e) {
                        throw new global::System.Data.StrongTypingException("The value for column \'FUNDORGADDR\' in table \'v_people_mony\' is DBNull.", e);
                    }
                }
                set {
                    this[this.tablev_people_mony.FUNDORGADDRColumn] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
            public string EXCEP {
                get {
                    try {
                        return ((string)(this[this.tablev_people_mony.EXCEPColumn]));
                    }
                    catch (global::System.InvalidCastException e) {
                        throw new global::System.Data.StrongTypingException("The value for column \'EXCEP\' in table \'v_people_mony\' is DBNull.", e);
                    }
                }
                set {
                    this[this.tablev_people_mony.EXCEPColumn] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
            public string GIFTTYPE {
                get {
                    try {
                        return ((string)(this[this.tablev_people_mony.GIFTTYPEColumn]));
                    }
                    catch (global::System.InvalidCastException e) {
                        throw new global::System.Data.StrongTypingException("The value for column \'GIFTTYPE\' in table \'v_people_mony\' is DBNull.", e);
                    }
                }
                set {
                    this[this.tablev_people_mony.GIFTTYPEColumn] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
            public short MONYCODE {
                get {
                    return ((short)(this[this.tablev_people_mony.MONYCODEColumn]));
                }
                set {
                    this[this.tablev_people_mony.MONYCODEColumn] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
            public bool IsBATCHDTENull() {
                return this.IsNull(this.tablev_people_mony.BATCHDTEColumn);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
            public void SetBATCHDTENull() {
                this[this.tablev_people_mony.BATCHDTEColumn] = global::System.Convert.DBNull;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
            public bool IsBATCHNONull() {
                return this.IsNull(this.tablev_people_mony.BATCHNOColumn);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
            public void SetBATCHNONull() {
                this[this.tablev_people_mony.BATCHNOColumn] = global::System.Convert.DBNull;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
            public bool IsAMTNull() {
                return this.IsNull(this.tablev_people_mony.AMTColumn);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
            public void SetAMTNull() {
                this[this.tablev_people_mony.AMTColumn] = global::System.Convert.DBNull;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
            public bool IsTRACKNONull() {
                return this.IsNull(this.tablev_people_mony.TRACKNOColumn);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
            public void SetTRACKNONull() {
                this[this.tablev_people_mony.TRACKNOColumn] = global::System.Convert.DBNull;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
            public bool IsMONYTYPENull() {
                return this.IsNull(this.tablev_people_mony.MONYTYPEColumn);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
            public void SetMONYTYPENull() {
                this[this.tablev_people_mony.MONYTYPEColumn] = global::System.Convert.DBNull;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
            public bool IsSRCECODENull() {
                return this.IsNull(this.tablev_people_mony.SRCECODEColumn);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
            public void SetSRCECODENull() {
                this[this.tablev_people_mony.SRCECODEColumn] = global::System.Convert.DBNull;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
            public bool IsSRCEDESCNull() {
                return this.IsNull(this.tablev_people_mony.SRCEDESCColumn);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
            public void SetSRCEDESCNull() {
                this[this.tablev_people_mony.SRCEDESCColumn] = global::System.Convert.DBNull;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
            public bool IsPKGECODENull() {
                return this.IsNull(this.tablev_people_mony.PKGECODEColumn);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
            public void SetPKGECODENull() {
                this[this.tablev_people_mony.PKGECODEColumn] = global::System.Convert.DBNull;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
            public bool IsPKGEDESCNull() {
                return this.IsNull(this.tablev_people_mony.PKGEDESCColumn);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
            public void SetPKGEDESCNull() {
                this[this.tablev_people_mony.PKGEDESCColumn] = global::System.Convert.DBNull;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
            public bool IsPROGTYPENull() {
                return this.IsNull(this.tablev_people_mony.PROGTYPEColumn);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
            public void SetPROGTYPENull() {
                this[this.tablev_people_mony.PROGTYPEColumn] = global::System.Convert.DBNull;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
            public bool IsPROGDESCNull() {
                return this.IsNull(this.tablev_people_mony.PROGDESCColumn);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
            public void SetPROGDESCNull() {
                this[this.tablev_people_mony.PROGDESCColumn] = global::System.Convert.DBNull;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
            public bool IsCENTERCODENull() {
                return this.IsNull(this.tablev_people_mony.CENTERCODEColumn);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
            public void SetCENTERCODENull() {
                this[this.tablev_people_mony.CENTERCODEColumn] = global::System.Convert.DBNull;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
            public bool IsCENTERDESCNull() {
                return this.IsNull(this.tablev_people_mony.CENTERDESCColumn);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
            public void SetCENTERDESCNull() {
                this[this.tablev_people_mony.CENTERDESCColumn] = global::System.Convert.DBNull;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
            public bool IsCAMPGNCODENull() {
                return this.IsNull(this.tablev_people_mony.CAMPGNCODEColumn);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
            public void SetCAMPGNCODENull() {
                this[this.tablev_people_mony.CAMPGNCODEColumn] = global::System.Convert.DBNull;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
            public bool IsCAMPGNDESCNull() {
                return this.IsNull(this.tablev_people_mony.CAMPGNDESCColumn);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
            public void SetCAMPGNDESCNull() {
                this[this.tablev_people_mony.CAMPGNDESCColumn] = global::System.Convert.DBNull;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
            public bool IsADJTYPENull() {
                return this.IsNull(this.tablev_people_mony.ADJTYPEColumn);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
            public void SetADJTYPENull() {
                this[this.tablev_people_mony.ADJTYPEColumn] = global::System.Convert.DBNull;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
            public bool IsADJDESCNull() {
                return this.IsNull(this.tablev_people_mony.ADJDESCColumn);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
            public void SetADJDESCNull() {
                this[this.tablev_people_mony.ADJDESCColumn] = global::System.Convert.DBNull;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
            public bool IsCOUNTERNull() {
                return this.IsNull(this.tablev_people_mony.COUNTERColumn);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
            public void SetCOUNTERNull() {
                this[this.tablev_people_mony.COUNTERColumn] = global::System.Convert.DBNull;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
            public bool IsFUNDORGNAMENull() {
                return this.IsNull(this.tablev_people_mony.FUNDORGNAMEColumn);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
            public void SetFUNDORGNAMENull() {
                this[this.tablev_people_mony.FUNDORGNAMEColumn] = global::System.Convert.DBNull;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
            public bool IsFUNDORGADDRNull() {
                return this.IsNull(this.tablev_people_mony.FUNDORGADDRColumn);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
            public void SetFUNDORGADDRNull() {
                this[this.tablev_people_mony.FUNDORGADDRColumn] = global::System.Convert.DBNull;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
            public bool IsEXCEPNull() {
                return this.IsNull(this.tablev_people_mony.EXCEPColumn);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
            public void SetEXCEPNull() {
                this[this.tablev_people_mony.EXCEPColumn] = global::System.Convert.DBNull;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
            public bool IsGIFTTYPENull() {
                return this.IsNull(this.tablev_people_mony.GIFTTYPEColumn);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
            public void SetGIFTTYPENull() {
                this[this.tablev_people_mony.GIFTTYPEColumn] = global::System.Convert.DBNull;
            }
        }
        
        /// <summary>
        ///Row event argument class
        ///</summary>
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
        public class MONYADDIRowChangeEvent : global::System.EventArgs {
            
            private MONYADDIRow eventRow;
            
            private global::System.Data.DataRowAction eventAction;
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
            public MONYADDIRowChangeEvent(MONYADDIRow row, global::System.Data.DataRowAction action) {
                this.eventRow = row;
                this.eventAction = action;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
            public MONYADDIRow Row {
                get {
                    return this.eventRow;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
            public global::System.Data.DataRowAction Action {
                get {
                    return this.eventAction;
                }
            }
        }
        
        /// <summary>
        ///Row event argument class
        ///</summary>
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
        public class v_people_monyRowChangeEvent : global::System.EventArgs {
            
            private v_people_monyRow eventRow;
            
            private global::System.Data.DataRowAction eventAction;
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
            public v_people_monyRowChangeEvent(v_people_monyRow row, global::System.Data.DataRowAction action) {
                this.eventRow = row;
                this.eventAction = action;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
            public v_people_monyRow Row {
                get {
                    return this.eventRow;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
            public global::System.Data.DataRowAction Action {
                get {
                    return this.eventAction;
                }
            }
        }
    }
}
namespace cmdiapp.n.reportDef.dxDEMOCDataSet2TableAdapters {
    
    
    /// <summary>
    ///Represents the connection and commands used to retrieve and save data.
    ///</summary>
    [global::System.ComponentModel.DesignerCategoryAttribute("code")]
    [global::System.ComponentModel.ToolboxItem(true)]
    [global::System.ComponentModel.DataObjectAttribute(true)]
    [global::System.ComponentModel.DesignerAttribute("Microsoft.VSDesigner.DataSource.Design.TableAdapterDesigner, Microsoft.VSDesigner" +
        ", Version=10.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a")]
    [global::System.ComponentModel.Design.HelpKeywordAttribute("vs.data.TableAdapter")]
    public partial class MONYADDITableAdapter : global::System.ComponentModel.Component {
        
        private global::System.Data.SqlClient.SqlDataAdapter _adapter;
        
        private global::System.Data.SqlClient.SqlConnection _connection;
        
        private global::System.Data.SqlClient.SqlTransaction _transaction;
        
        private global::System.Data.SqlClient.SqlCommand[] _commandCollection;
        
        private bool _clearBeforeFill;
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
        public MONYADDITableAdapter() {
            this.ClearBeforeFill = true;
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
        protected internal global::System.Data.SqlClient.SqlDataAdapter Adapter {
            get {
                if ((this._adapter == null)) {
                    this.InitAdapter();
                }
                return this._adapter;
            }
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
        internal global::System.Data.SqlClient.SqlConnection Connection {
            get {
                if ((this._connection == null)) {
                    this.InitConnection();
                }
                return this._connection;
            }
            set {
                this._connection = value;
                if ((this.Adapter.InsertCommand != null)) {
                    this.Adapter.InsertCommand.Connection = value;
                }
                if ((this.Adapter.DeleteCommand != null)) {
                    this.Adapter.DeleteCommand.Connection = value;
                }
                if ((this.Adapter.UpdateCommand != null)) {
                    this.Adapter.UpdateCommand.Connection = value;
                }
                for (int i = 0; (i < this.CommandCollection.Length); i = (i + 1)) {
                    if ((this.CommandCollection[i] != null)) {
                        ((global::System.Data.SqlClient.SqlCommand)(this.CommandCollection[i])).Connection = value;
                    }
                }
            }
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
        internal global::System.Data.SqlClient.SqlTransaction Transaction {
            get {
                return this._transaction;
            }
            set {
                this._transaction = value;
                for (int i = 0; (i < this.CommandCollection.Length); i = (i + 1)) {
                    this.CommandCollection[i].Transaction = this._transaction;
                }
                if (((this.Adapter != null) 
                            && (this.Adapter.DeleteCommand != null))) {
                    this.Adapter.DeleteCommand.Transaction = this._transaction;
                }
                if (((this.Adapter != null) 
                            && (this.Adapter.InsertCommand != null))) {
                    this.Adapter.InsertCommand.Transaction = this._transaction;
                }
                if (((this.Adapter != null) 
                            && (this.Adapter.UpdateCommand != null))) {
                    this.Adapter.UpdateCommand.Transaction = this._transaction;
                }
            }
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
        protected global::System.Data.SqlClient.SqlCommand[] CommandCollection {
            get {
                if ((this._commandCollection == null)) {
                    this.InitCommandCollection();
                }
                return this._commandCollection;
            }
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
        public bool ClearBeforeFill {
            get {
                return this._clearBeforeFill;
            }
            set {
                this._clearBeforeFill = value;
            }
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
        private void InitAdapter() {
            this._adapter = new global::System.Data.SqlClient.SqlDataAdapter();
            global::System.Data.Common.DataTableMapping tableMapping = new global::System.Data.Common.DataTableMapping();
            tableMapping.SourceTable = "Table";
            tableMapping.DataSetTable = "MONYADDI";
            tableMapping.ColumnMappings.Add("MONYADDIID", "MONYADDIID");
            tableMapping.ColumnMappings.Add("MID", "MID");
            tableMapping.ColumnMappings.Add("TMCAMPAIGN", "TMCAMPAIGN");
            tableMapping.ColumnMappings.Add("TMUNIQNUM", "TMUNIQNUM");
            tableMapping.ColumnMappings.Add("RECURRED", "RECURRED");
            tableMapping.ColumnMappings.Add("RECURENDDTE", "RECURENDDTE");
            tableMapping.ColumnMappings.Add("REFERENCEID", "REFERENCEID");
            tableMapping.ColumnMappings.Add("MULTNAME", "MULTNAME");
            tableMapping.ColumnMappings.Add("MULTSIGN", "MULTSIGN");
            tableMapping.ColumnMappings.Add("WEBGIFTID", "WEBGIFTID");
            tableMapping.ColumnMappings.Add("MISCTEXT", "MISCTEXT");
            tableMapping.ColumnMappings.Add("MONYCCBATID", "MONYCCBATID");
            tableMapping.ColumnMappings.Add("MISCJSON", "MISCJSON");
            this._adapter.TableMappings.Add(tableMapping);
            this._adapter.DeleteCommand = new global::System.Data.SqlClient.SqlCommand();
            this._adapter.DeleteCommand.Connection = this.Connection;
            this._adapter.DeleteCommand.CommandText = @"DELETE FROM [dbo].[MONYADDI] WHERE (([MONYADDIID] = @Original_MONYADDIID) AND ((@IsNull_MID = 1 AND [MID] IS NULL) OR ([MID] = @Original_MID)) AND ((@IsNull_TMCAMPAIGN = 1 AND [TMCAMPAIGN] IS NULL) OR ([TMCAMPAIGN] = @Original_TMCAMPAIGN)) AND ((@IsNull_TMUNIQNUM = 1 AND [TMUNIQNUM] IS NULL) OR ([TMUNIQNUM] = @Original_TMUNIQNUM)) AND ([RECURRED] = @Original_RECURRED) AND ((@IsNull_RECURENDDTE = 1 AND [RECURENDDTE] IS NULL) OR ([RECURENDDTE] = @Original_RECURENDDTE)) AND ((@IsNull_REFERENCEID = 1 AND [REFERENCEID] IS NULL) OR ([REFERENCEID] = @Original_REFERENCEID)) AND ([MULTNAME] = @Original_MULTNAME) AND ([MULTSIGN] = @Original_MULTSIGN) AND ((@IsNull_WEBGIFTID = 1 AND [WEBGIFTID] IS NULL) OR ([WEBGIFTID] = @Original_WEBGIFTID)) AND ((@IsNull_MISCTEXT = 1 AND [MISCTEXT] IS NULL) OR ([MISCTEXT] = @Original_MISCTEXT)) AND ((@IsNull_MONYCCBATID = 1 AND [MONYCCBATID] IS NULL) OR ([MONYCCBATID] = @Original_MONYCCBATID)))";
            this._adapter.DeleteCommand.CommandType = global::System.Data.CommandType.Text;
            this._adapter.DeleteCommand.Parameters.Add(new global::System.Data.SqlClient.SqlParameter("@Original_MONYADDIID", global::System.Data.SqlDbType.Int, 0, global::System.Data.ParameterDirection.Input, 0, 0, "MONYADDIID", global::System.Data.DataRowVersion.Original, false, null, "", "", ""));
            this._adapter.DeleteCommand.Parameters.Add(new global::System.Data.SqlClient.SqlParameter("@IsNull_MID", global::System.Data.SqlDbType.Int, 0, global::System.Data.ParameterDirection.Input, 0, 0, "MID", global::System.Data.DataRowVersion.Original, true, null, "", "", ""));
            this._adapter.DeleteCommand.Parameters.Add(new global::System.Data.SqlClient.SqlParameter("@Original_MID", global::System.Data.SqlDbType.Int, 0, global::System.Data.ParameterDirection.Input, 0, 0, "MID", global::System.Data.DataRowVersion.Original, false, null, "", "", ""));
            this._adapter.DeleteCommand.Parameters.Add(new global::System.Data.SqlClient.SqlParameter("@IsNull_TMCAMPAIGN", global::System.Data.SqlDbType.Int, 0, global::System.Data.ParameterDirection.Input, 0, 0, "TMCAMPAIGN", global::System.Data.DataRowVersion.Original, true, null, "", "", ""));
            this._adapter.DeleteCommand.Parameters.Add(new global::System.Data.SqlClient.SqlParameter("@Original_TMCAMPAIGN", global::System.Data.SqlDbType.VarChar, 0, global::System.Data.ParameterDirection.Input, 0, 0, "TMCAMPAIGN", global::System.Data.DataRowVersion.Original, false, null, "", "", ""));
            this._adapter.DeleteCommand.Parameters.Add(new global::System.Data.SqlClient.SqlParameter("@IsNull_TMUNIQNUM", global::System.Data.SqlDbType.Int, 0, global::System.Data.ParameterDirection.Input, 0, 0, "TMUNIQNUM", global::System.Data.DataRowVersion.Original, true, null, "", "", ""));
            this._adapter.DeleteCommand.Parameters.Add(new global::System.Data.SqlClient.SqlParameter("@Original_TMUNIQNUM", global::System.Data.SqlDbType.VarChar, 0, global::System.Data.ParameterDirection.Input, 0, 0, "TMUNIQNUM", global::System.Data.DataRowVersion.Original, false, null, "", "", ""));
            this._adapter.DeleteCommand.Parameters.Add(new global::System.Data.SqlClient.SqlParameter("@Original_RECURRED", global::System.Data.SqlDbType.Bit, 0, global::System.Data.ParameterDirection.Input, 0, 0, "RECURRED", global::System.Data.DataRowVersion.Original, false, null, "", "", ""));
            this._adapter.DeleteCommand.Parameters.Add(new global::System.Data.SqlClient.SqlParameter("@IsNull_RECURENDDTE", global::System.Data.SqlDbType.Int, 0, global::System.Data.ParameterDirection.Input, 0, 0, "RECURENDDTE", global::System.Data.DataRowVersion.Original, true, null, "", "", ""));
            this._adapter.DeleteCommand.Parameters.Add(new global::System.Data.SqlClient.SqlParameter("@Original_RECURENDDTE", global::System.Data.SqlDbType.DateTime, 0, global::System.Data.ParameterDirection.Input, 0, 0, "RECURENDDTE", global::System.Data.DataRowVersion.Original, false, null, "", "", ""));
            this._adapter.DeleteCommand.Parameters.Add(new global::System.Data.SqlClient.SqlParameter("@IsNull_REFERENCEID", global::System.Data.SqlDbType.Int, 0, global::System.Data.ParameterDirection.Input, 0, 0, "REFERENCEID", global::System.Data.DataRowVersion.Original, true, null, "", "", ""));
            this._adapter.DeleteCommand.Parameters.Add(new global::System.Data.SqlClient.SqlParameter("@Original_REFERENCEID", global::System.Data.SqlDbType.VarChar, 0, global::System.Data.ParameterDirection.Input, 0, 0, "REFERENCEID", global::System.Data.DataRowVersion.Original, false, null, "", "", ""));
            this._adapter.DeleteCommand.Parameters.Add(new global::System.Data.SqlClient.SqlParameter("@Original_MULTNAME", global::System.Data.SqlDbType.Bit, 0, global::System.Data.ParameterDirection.Input, 0, 0, "MULTNAME", global::System.Data.DataRowVersion.Original, false, null, "", "", ""));
            this._adapter.DeleteCommand.Parameters.Add(new global::System.Data.SqlClient.SqlParameter("@Original_MULTSIGN", global::System.Data.SqlDbType.Bit, 0, global::System.Data.ParameterDirection.Input, 0, 0, "MULTSIGN", global::System.Data.DataRowVersion.Original, false, null, "", "", ""));
            this._adapter.DeleteCommand.Parameters.Add(new global::System.Data.SqlClient.SqlParameter("@IsNull_WEBGIFTID", global::System.Data.SqlDbType.Int, 0, global::System.Data.ParameterDirection.Input, 0, 0, "WEBGIFTID", global::System.Data.DataRowVersion.Original, true, null, "", "", ""));
            this._adapter.DeleteCommand.Parameters.Add(new global::System.Data.SqlClient.SqlParameter("@Original_WEBGIFTID", global::System.Data.SqlDbType.Int, 0, global::System.Data.ParameterDirection.Input, 0, 0, "WEBGIFTID", global::System.Data.DataRowVersion.Original, false, null, "", "", ""));
            this._adapter.DeleteCommand.Parameters.Add(new global::System.Data.SqlClient.SqlParameter("@IsNull_MISCTEXT", global::System.Data.SqlDbType.Int, 0, global::System.Data.ParameterDirection.Input, 0, 0, "MISCTEXT", global::System.Data.DataRowVersion.Original, true, null, "", "", ""));
            this._adapter.DeleteCommand.Parameters.Add(new global::System.Data.SqlClient.SqlParameter("@Original_MISCTEXT", global::System.Data.SqlDbType.VarChar, 0, global::System.Data.ParameterDirection.Input, 0, 0, "MISCTEXT", global::System.Data.DataRowVersion.Original, false, null, "", "", ""));
            this._adapter.DeleteCommand.Parameters.Add(new global::System.Data.SqlClient.SqlParameter("@IsNull_MONYCCBATID", global::System.Data.SqlDbType.Int, 0, global::System.Data.ParameterDirection.Input, 0, 0, "MONYCCBATID", global::System.Data.DataRowVersion.Original, true, null, "", "", ""));
            this._adapter.DeleteCommand.Parameters.Add(new global::System.Data.SqlClient.SqlParameter("@Original_MONYCCBATID", global::System.Data.SqlDbType.Int, 0, global::System.Data.ParameterDirection.Input, 0, 0, "MONYCCBATID", global::System.Data.DataRowVersion.Original, false, null, "", "", ""));
            this._adapter.InsertCommand = new global::System.Data.SqlClient.SqlCommand();
            this._adapter.InsertCommand.Connection = this.Connection;
            this._adapter.InsertCommand.CommandText = @"INSERT INTO [dbo].[MONYADDI] ([MID], [TMCAMPAIGN], [TMUNIQNUM], [RECURRED], [RECURENDDTE], [REFERENCEID], [MULTNAME], [MULTSIGN], [WEBGIFTID], [MISCTEXT], [MONYCCBATID], [MISCJSON]) VALUES (@MID, @TMCAMPAIGN, @TMUNIQNUM, @RECURRED, @RECURENDDTE, @REFERENCEID, @MULTNAME, @MULTSIGN, @WEBGIFTID, @MISCTEXT, @MONYCCBATID, @MISCJSON);
SELECT MONYADDIID, MID, TMCAMPAIGN, TMUNIQNUM, RECURRED, RECURENDDTE, REFERENCEID, MULTNAME, MULTSIGN, WEBGIFTID, MISCTEXT, MONYCCBATID, MISCJSON FROM MONYADDI WHERE (MONYADDIID = SCOPE_IDENTITY())";
            this._adapter.InsertCommand.CommandType = global::System.Data.CommandType.Text;
            this._adapter.InsertCommand.Parameters.Add(new global::System.Data.SqlClient.SqlParameter("@MID", global::System.Data.SqlDbType.Int, 0, global::System.Data.ParameterDirection.Input, 0, 0, "MID", global::System.Data.DataRowVersion.Current, false, null, "", "", ""));
            this._adapter.InsertCommand.Parameters.Add(new global::System.Data.SqlClient.SqlParameter("@TMCAMPAIGN", global::System.Data.SqlDbType.VarChar, 0, global::System.Data.ParameterDirection.Input, 0, 0, "TMCAMPAIGN", global::System.Data.DataRowVersion.Current, false, null, "", "", ""));
            this._adapter.InsertCommand.Parameters.Add(new global::System.Data.SqlClient.SqlParameter("@TMUNIQNUM", global::System.Data.SqlDbType.VarChar, 0, global::System.Data.ParameterDirection.Input, 0, 0, "TMUNIQNUM", global::System.Data.DataRowVersion.Current, false, null, "", "", ""));
            this._adapter.InsertCommand.Parameters.Add(new global::System.Data.SqlClient.SqlParameter("@RECURRED", global::System.Data.SqlDbType.Bit, 0, global::System.Data.ParameterDirection.Input, 0, 0, "RECURRED", global::System.Data.DataRowVersion.Current, false, null, "", "", ""));
            this._adapter.InsertCommand.Parameters.Add(new global::System.Data.SqlClient.SqlParameter("@RECURENDDTE", global::System.Data.SqlDbType.DateTime, 0, global::System.Data.ParameterDirection.Input, 0, 0, "RECURENDDTE", global::System.Data.DataRowVersion.Current, false, null, "", "", ""));
            this._adapter.InsertCommand.Parameters.Add(new global::System.Data.SqlClient.SqlParameter("@REFERENCEID", global::System.Data.SqlDbType.VarChar, 0, global::System.Data.ParameterDirection.Input, 0, 0, "REFERENCEID", global::System.Data.DataRowVersion.Current, false, null, "", "", ""));
            this._adapter.InsertCommand.Parameters.Add(new global::System.Data.SqlClient.SqlParameter("@MULTNAME", global::System.Data.SqlDbType.Bit, 0, global::System.Data.ParameterDirection.Input, 0, 0, "MULTNAME", global::System.Data.DataRowVersion.Current, false, null, "", "", ""));
            this._adapter.InsertCommand.Parameters.Add(new global::System.Data.SqlClient.SqlParameter("@MULTSIGN", global::System.Data.SqlDbType.Bit, 0, global::System.Data.ParameterDirection.Input, 0, 0, "MULTSIGN", global::System.Data.DataRowVersion.Current, false, null, "", "", ""));
            this._adapter.InsertCommand.Parameters.Add(new global::System.Data.SqlClient.SqlParameter("@WEBGIFTID", global::System.Data.SqlDbType.Int, 0, global::System.Data.ParameterDirection.Input, 0, 0, "WEBGIFTID", global::System.Data.DataRowVersion.Current, false, null, "", "", ""));
            this._adapter.InsertCommand.Parameters.Add(new global::System.Data.SqlClient.SqlParameter("@MISCTEXT", global::System.Data.SqlDbType.VarChar, 0, global::System.Data.ParameterDirection.Input, 0, 0, "MISCTEXT", global::System.Data.DataRowVersion.Current, false, null, "", "", ""));
            this._adapter.InsertCommand.Parameters.Add(new global::System.Data.SqlClient.SqlParameter("@MONYCCBATID", global::System.Data.SqlDbType.Int, 0, global::System.Data.ParameterDirection.Input, 0, 0, "MONYCCBATID", global::System.Data.DataRowVersion.Current, false, null, "", "", ""));
            this._adapter.InsertCommand.Parameters.Add(new global::System.Data.SqlClient.SqlParameter("@MISCJSON", global::System.Data.SqlDbType.VarChar, 0, global::System.Data.ParameterDirection.Input, 0, 0, "MISCJSON", global::System.Data.DataRowVersion.Current, false, null, "", "", ""));
            this._adapter.UpdateCommand = new global::System.Data.SqlClient.SqlCommand();
            this._adapter.UpdateCommand.Connection = this.Connection;
            this._adapter.UpdateCommand.CommandText = @"UPDATE [dbo].[MONYADDI] SET [MID] = @MID, [TMCAMPAIGN] = @TMCAMPAIGN, [TMUNIQNUM] = @TMUNIQNUM, [RECURRED] = @RECURRED, [RECURENDDTE] = @RECURENDDTE, [REFERENCEID] = @REFERENCEID, [MULTNAME] = @MULTNAME, [MULTSIGN] = @MULTSIGN, [WEBGIFTID] = @WEBGIFTID, [MISCTEXT] = @MISCTEXT, [MONYCCBATID] = @MONYCCBATID, [MISCJSON] = @MISCJSON WHERE (([MONYADDIID] = @Original_MONYADDIID) AND ((@IsNull_MID = 1 AND [MID] IS NULL) OR ([MID] = @Original_MID)) AND ((@IsNull_TMCAMPAIGN = 1 AND [TMCAMPAIGN] IS NULL) OR ([TMCAMPAIGN] = @Original_TMCAMPAIGN)) AND ((@IsNull_TMUNIQNUM = 1 AND [TMUNIQNUM] IS NULL) OR ([TMUNIQNUM] = @Original_TMUNIQNUM)) AND ([RECURRED] = @Original_RECURRED) AND ((@IsNull_RECURENDDTE = 1 AND [RECURENDDTE] IS NULL) OR ([RECURENDDTE] = @Original_RECURENDDTE)) AND ((@IsNull_REFERENCEID = 1 AND [REFERENCEID] IS NULL) OR ([REFERENCEID] = @Original_REFERENCEID)) AND ([MULTNAME] = @Original_MULTNAME) AND ([MULTSIGN] = @Original_MULTSIGN) AND ((@IsNull_WEBGIFTID = 1 AND [WEBGIFTID] IS NULL) OR ([WEBGIFTID] = @Original_WEBGIFTID)) AND ((@IsNull_MISCTEXT = 1 AND [MISCTEXT] IS NULL) OR ([MISCTEXT] = @Original_MISCTEXT)) AND ((@IsNull_MONYCCBATID = 1 AND [MONYCCBATID] IS NULL) OR ([MONYCCBATID] = @Original_MONYCCBATID)));
SELECT MONYADDIID, MID, TMCAMPAIGN, TMUNIQNUM, RECURRED, RECURENDDTE, REFERENCEID, MULTNAME, MULTSIGN, WEBGIFTID, MISCTEXT, MONYCCBATID, MISCJSON FROM MONYADDI WHERE (MONYADDIID = @MONYADDIID)";
            this._adapter.UpdateCommand.CommandType = global::System.Data.CommandType.Text;
            this._adapter.UpdateCommand.Parameters.Add(new global::System.Data.SqlClient.SqlParameter("@MID", global::System.Data.SqlDbType.Int, 0, global::System.Data.ParameterDirection.Input, 0, 0, "MID", global::System.Data.DataRowVersion.Current, false, null, "", "", ""));
            this._adapter.UpdateCommand.Parameters.Add(new global::System.Data.SqlClient.SqlParameter("@TMCAMPAIGN", global::System.Data.SqlDbType.VarChar, 0, global::System.Data.ParameterDirection.Input, 0, 0, "TMCAMPAIGN", global::System.Data.DataRowVersion.Current, false, null, "", "", ""));
            this._adapter.UpdateCommand.Parameters.Add(new global::System.Data.SqlClient.SqlParameter("@TMUNIQNUM", global::System.Data.SqlDbType.VarChar, 0, global::System.Data.ParameterDirection.Input, 0, 0, "TMUNIQNUM", global::System.Data.DataRowVersion.Current, false, null, "", "", ""));
            this._adapter.UpdateCommand.Parameters.Add(new global::System.Data.SqlClient.SqlParameter("@RECURRED", global::System.Data.SqlDbType.Bit, 0, global::System.Data.ParameterDirection.Input, 0, 0, "RECURRED", global::System.Data.DataRowVersion.Current, false, null, "", "", ""));
            this._adapter.UpdateCommand.Parameters.Add(new global::System.Data.SqlClient.SqlParameter("@RECURENDDTE", global::System.Data.SqlDbType.DateTime, 0, global::System.Data.ParameterDirection.Input, 0, 0, "RECURENDDTE", global::System.Data.DataRowVersion.Current, false, null, "", "", ""));
            this._adapter.UpdateCommand.Parameters.Add(new global::System.Data.SqlClient.SqlParameter("@REFERENCEID", global::System.Data.SqlDbType.VarChar, 0, global::System.Data.ParameterDirection.Input, 0, 0, "REFERENCEID", global::System.Data.DataRowVersion.Current, false, null, "", "", ""));
            this._adapter.UpdateCommand.Parameters.Add(new global::System.Data.SqlClient.SqlParameter("@MULTNAME", global::System.Data.SqlDbType.Bit, 0, global::System.Data.ParameterDirection.Input, 0, 0, "MULTNAME", global::System.Data.DataRowVersion.Current, false, null, "", "", ""));
            this._adapter.UpdateCommand.Parameters.Add(new global::System.Data.SqlClient.SqlParameter("@MULTSIGN", global::System.Data.SqlDbType.Bit, 0, global::System.Data.ParameterDirection.Input, 0, 0, "MULTSIGN", global::System.Data.DataRowVersion.Current, false, null, "", "", ""));
            this._adapter.UpdateCommand.Parameters.Add(new global::System.Data.SqlClient.SqlParameter("@WEBGIFTID", global::System.Data.SqlDbType.Int, 0, global::System.Data.ParameterDirection.Input, 0, 0, "WEBGIFTID", global::System.Data.DataRowVersion.Current, false, null, "", "", ""));
            this._adapter.UpdateCommand.Parameters.Add(new global::System.Data.SqlClient.SqlParameter("@MISCTEXT", global::System.Data.SqlDbType.VarChar, 0, global::System.Data.ParameterDirection.Input, 0, 0, "MISCTEXT", global::System.Data.DataRowVersion.Current, false, null, "", "", ""));
            this._adapter.UpdateCommand.Parameters.Add(new global::System.Data.SqlClient.SqlParameter("@MONYCCBATID", global::System.Data.SqlDbType.Int, 0, global::System.Data.ParameterDirection.Input, 0, 0, "MONYCCBATID", global::System.Data.DataRowVersion.Current, false, null, "", "", ""));
            this._adapter.UpdateCommand.Parameters.Add(new global::System.Data.SqlClient.SqlParameter("@MISCJSON", global::System.Data.SqlDbType.VarChar, 0, global::System.Data.ParameterDirection.Input, 0, 0, "MISCJSON", global::System.Data.DataRowVersion.Current, false, null, "", "", ""));
            this._adapter.UpdateCommand.Parameters.Add(new global::System.Data.SqlClient.SqlParameter("@Original_MONYADDIID", global::System.Data.SqlDbType.Int, 0, global::System.Data.ParameterDirection.Input, 0, 0, "MONYADDIID", global::System.Data.DataRowVersion.Original, false, null, "", "", ""));
            this._adapter.UpdateCommand.Parameters.Add(new global::System.Data.SqlClient.SqlParameter("@IsNull_MID", global::System.Data.SqlDbType.Int, 0, global::System.Data.ParameterDirection.Input, 0, 0, "MID", global::System.Data.DataRowVersion.Original, true, null, "", "", ""));
            this._adapter.UpdateCommand.Parameters.Add(new global::System.Data.SqlClient.SqlParameter("@Original_MID", global::System.Data.SqlDbType.Int, 0, global::System.Data.ParameterDirection.Input, 0, 0, "MID", global::System.Data.DataRowVersion.Original, false, null, "", "", ""));
            this._adapter.UpdateCommand.Parameters.Add(new global::System.Data.SqlClient.SqlParameter("@IsNull_TMCAMPAIGN", global::System.Data.SqlDbType.Int, 0, global::System.Data.ParameterDirection.Input, 0, 0, "TMCAMPAIGN", global::System.Data.DataRowVersion.Original, true, null, "", "", ""));
            this._adapter.UpdateCommand.Parameters.Add(new global::System.Data.SqlClient.SqlParameter("@Original_TMCAMPAIGN", global::System.Data.SqlDbType.VarChar, 0, global::System.Data.ParameterDirection.Input, 0, 0, "TMCAMPAIGN", global::System.Data.DataRowVersion.Original, false, null, "", "", ""));
            this._adapter.UpdateCommand.Parameters.Add(new global::System.Data.SqlClient.SqlParameter("@IsNull_TMUNIQNUM", global::System.Data.SqlDbType.Int, 0, global::System.Data.ParameterDirection.Input, 0, 0, "TMUNIQNUM", global::System.Data.DataRowVersion.Original, true, null, "", "", ""));
            this._adapter.UpdateCommand.Parameters.Add(new global::System.Data.SqlClient.SqlParameter("@Original_TMUNIQNUM", global::System.Data.SqlDbType.VarChar, 0, global::System.Data.ParameterDirection.Input, 0, 0, "TMUNIQNUM", global::System.Data.DataRowVersion.Original, false, null, "", "", ""));
            this._adapter.UpdateCommand.Parameters.Add(new global::System.Data.SqlClient.SqlParameter("@Original_RECURRED", global::System.Data.SqlDbType.Bit, 0, global::System.Data.ParameterDirection.Input, 0, 0, "RECURRED", global::System.Data.DataRowVersion.Original, false, null, "", "", ""));
            this._adapter.UpdateCommand.Parameters.Add(new global::System.Data.SqlClient.SqlParameter("@IsNull_RECURENDDTE", global::System.Data.SqlDbType.Int, 0, global::System.Data.ParameterDirection.Input, 0, 0, "RECURENDDTE", global::System.Data.DataRowVersion.Original, true, null, "", "", ""));
            this._adapter.UpdateCommand.Parameters.Add(new global::System.Data.SqlClient.SqlParameter("@Original_RECURENDDTE", global::System.Data.SqlDbType.DateTime, 0, global::System.Data.ParameterDirection.Input, 0, 0, "RECURENDDTE", global::System.Data.DataRowVersion.Original, false, null, "", "", ""));
            this._adapter.UpdateCommand.Parameters.Add(new global::System.Data.SqlClient.SqlParameter("@IsNull_REFERENCEID", global::System.Data.SqlDbType.Int, 0, global::System.Data.ParameterDirection.Input, 0, 0, "REFERENCEID", global::System.Data.DataRowVersion.Original, true, null, "", "", ""));
            this._adapter.UpdateCommand.Parameters.Add(new global::System.Data.SqlClient.SqlParameter("@Original_REFERENCEID", global::System.Data.SqlDbType.VarChar, 0, global::System.Data.ParameterDirection.Input, 0, 0, "REFERENCEID", global::System.Data.DataRowVersion.Original, false, null, "", "", ""));
            this._adapter.UpdateCommand.Parameters.Add(new global::System.Data.SqlClient.SqlParameter("@Original_MULTNAME", global::System.Data.SqlDbType.Bit, 0, global::System.Data.ParameterDirection.Input, 0, 0, "MULTNAME", global::System.Data.DataRowVersion.Original, false, null, "", "", ""));
            this._adapter.UpdateCommand.Parameters.Add(new global::System.Data.SqlClient.SqlParameter("@Original_MULTSIGN", global::System.Data.SqlDbType.Bit, 0, global::System.Data.ParameterDirection.Input, 0, 0, "MULTSIGN", global::System.Data.DataRowVersion.Original, false, null, "", "", ""));
            this._adapter.UpdateCommand.Parameters.Add(new global::System.Data.SqlClient.SqlParameter("@IsNull_WEBGIFTID", global::System.Data.SqlDbType.Int, 0, global::System.Data.ParameterDirection.Input, 0, 0, "WEBGIFTID", global::System.Data.DataRowVersion.Original, true, null, "", "", ""));
            this._adapter.UpdateCommand.Parameters.Add(new global::System.Data.SqlClient.SqlParameter("@Original_WEBGIFTID", global::System.Data.SqlDbType.Int, 0, global::System.Data.ParameterDirection.Input, 0, 0, "WEBGIFTID", global::System.Data.DataRowVersion.Original, false, null, "", "", ""));
            this._adapter.UpdateCommand.Parameters.Add(new global::System.Data.SqlClient.SqlParameter("@IsNull_MISCTEXT", global::System.Data.SqlDbType.Int, 0, global::System.Data.ParameterDirection.Input, 0, 0, "MISCTEXT", global::System.Data.DataRowVersion.Original, true, null, "", "", ""));
            this._adapter.UpdateCommand.Parameters.Add(new global::System.Data.SqlClient.SqlParameter("@Original_MISCTEXT", global::System.Data.SqlDbType.VarChar, 0, global::System.Data.ParameterDirection.Input, 0, 0, "MISCTEXT", global::System.Data.DataRowVersion.Original, false, null, "", "", ""));
            this._adapter.UpdateCommand.Parameters.Add(new global::System.Data.SqlClient.SqlParameter("@IsNull_MONYCCBATID", global::System.Data.SqlDbType.Int, 0, global::System.Data.ParameterDirection.Input, 0, 0, "MONYCCBATID", global::System.Data.DataRowVersion.Original, true, null, "", "", ""));
            this._adapter.UpdateCommand.Parameters.Add(new global::System.Data.SqlClient.SqlParameter("@Original_MONYCCBATID", global::System.Data.SqlDbType.Int, 0, global::System.Data.ParameterDirection.Input, 0, 0, "MONYCCBATID", global::System.Data.DataRowVersion.Original, false, null, "", "", ""));
            this._adapter.UpdateCommand.Parameters.Add(new global::System.Data.SqlClient.SqlParameter("@MONYADDIID", global::System.Data.SqlDbType.Int, 4, global::System.Data.ParameterDirection.Input, 0, 0, "MONYADDIID", global::System.Data.DataRowVersion.Current, false, null, "", "", ""));
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
        private void InitConnection() {
            this._connection = new global::System.Data.SqlClient.SqlConnection();
            this._connection.ConnectionString = global::cmdiapp.n.reportDef.Properties.Settings.Default.dxDEMOCConnectionString;
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
        private void InitCommandCollection() {
            this._commandCollection = new global::System.Data.SqlClient.SqlCommand[1];
            this._commandCollection[0] = new global::System.Data.SqlClient.SqlCommand();
            this._commandCollection[0].Connection = this.Connection;
            this._commandCollection[0].CommandText = "SELECT MONYADDIID, MID, TMCAMPAIGN, TMUNIQNUM, RECURRED, RECURENDDTE, REFERENCEID" +
                ", MULTNAME, MULTSIGN, WEBGIFTID, MISCTEXT, MONYCCBATID, MISCJSON FROM dbo.MONYAD" +
                "DI";
            this._commandCollection[0].CommandType = global::System.Data.CommandType.Text;
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
        [global::System.ComponentModel.Design.HelpKeywordAttribute("vs.data.TableAdapter")]
        [global::System.ComponentModel.DataObjectMethodAttribute(global::System.ComponentModel.DataObjectMethodType.Fill, true)]
        public virtual int Fill(dxDEMOCDataSet2.MONYADDIDataTable dataTable) {
            this.Adapter.SelectCommand = this.CommandCollection[0];
            if ((this.ClearBeforeFill == true)) {
                dataTable.Clear();
            }
            int returnValue = this.Adapter.Fill(dataTable);
            return returnValue;
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
        [global::System.ComponentModel.Design.HelpKeywordAttribute("vs.data.TableAdapter")]
        [global::System.ComponentModel.DataObjectMethodAttribute(global::System.ComponentModel.DataObjectMethodType.Select, true)]
        public virtual dxDEMOCDataSet2.MONYADDIDataTable GetData() {
            this.Adapter.SelectCommand = this.CommandCollection[0];
            dxDEMOCDataSet2.MONYADDIDataTable dataTable = new dxDEMOCDataSet2.MONYADDIDataTable();
            this.Adapter.Fill(dataTable);
            return dataTable;
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
        [global::System.ComponentModel.Design.HelpKeywordAttribute("vs.data.TableAdapter")]
        public virtual int Update(dxDEMOCDataSet2.MONYADDIDataTable dataTable) {
            return this.Adapter.Update(dataTable);
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
        [global::System.ComponentModel.Design.HelpKeywordAttribute("vs.data.TableAdapter")]
        public virtual int Update(dxDEMOCDataSet2 dataSet) {
            return this.Adapter.Update(dataSet, "MONYADDI");
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
        [global::System.ComponentModel.Design.HelpKeywordAttribute("vs.data.TableAdapter")]
        public virtual int Update(global::System.Data.DataRow dataRow) {
            return this.Adapter.Update(new global::System.Data.DataRow[] {
                        dataRow});
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
        [global::System.ComponentModel.Design.HelpKeywordAttribute("vs.data.TableAdapter")]
        public virtual int Update(global::System.Data.DataRow[] dataRows) {
            return this.Adapter.Update(dataRows);
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
        [global::System.ComponentModel.Design.HelpKeywordAttribute("vs.data.TableAdapter")]
        [global::System.ComponentModel.DataObjectMethodAttribute(global::System.ComponentModel.DataObjectMethodType.Delete, true)]
        public virtual int Delete(int Original_MONYADDIID, global::System.Nullable<int> Original_MID, string Original_TMCAMPAIGN, string Original_TMUNIQNUM, bool Original_RECURRED, global::System.Nullable<global::System.DateTime> Original_RECURENDDTE, string Original_REFERENCEID, bool Original_MULTNAME, bool Original_MULTSIGN, global::System.Nullable<int> Original_WEBGIFTID, string Original_MISCTEXT, global::System.Nullable<int> Original_MONYCCBATID) {
            this.Adapter.DeleteCommand.Parameters[0].Value = ((int)(Original_MONYADDIID));
            if ((Original_MID.HasValue == true)) {
                this.Adapter.DeleteCommand.Parameters[1].Value = ((object)(0));
                this.Adapter.DeleteCommand.Parameters[2].Value = ((int)(Original_MID.Value));
            }
            else {
                this.Adapter.DeleteCommand.Parameters[1].Value = ((object)(1));
                this.Adapter.DeleteCommand.Parameters[2].Value = global::System.DBNull.Value;
            }
            if ((Original_TMCAMPAIGN == null)) {
                this.Adapter.DeleteCommand.Parameters[3].Value = ((object)(1));
                this.Adapter.DeleteCommand.Parameters[4].Value = global::System.DBNull.Value;
            }
            else {
                this.Adapter.DeleteCommand.Parameters[3].Value = ((object)(0));
                this.Adapter.DeleteCommand.Parameters[4].Value = ((string)(Original_TMCAMPAIGN));
            }
            if ((Original_TMUNIQNUM == null)) {
                this.Adapter.DeleteCommand.Parameters[5].Value = ((object)(1));
                this.Adapter.DeleteCommand.Parameters[6].Value = global::System.DBNull.Value;
            }
            else {
                this.Adapter.DeleteCommand.Parameters[5].Value = ((object)(0));
                this.Adapter.DeleteCommand.Parameters[6].Value = ((string)(Original_TMUNIQNUM));
            }
            this.Adapter.DeleteCommand.Parameters[7].Value = ((bool)(Original_RECURRED));
            if ((Original_RECURENDDTE.HasValue == true)) {
                this.Adapter.DeleteCommand.Parameters[8].Value = ((object)(0));
                this.Adapter.DeleteCommand.Parameters[9].Value = ((System.DateTime)(Original_RECURENDDTE.Value));
            }
            else {
                this.Adapter.DeleteCommand.Parameters[8].Value = ((object)(1));
                this.Adapter.DeleteCommand.Parameters[9].Value = global::System.DBNull.Value;
            }
            if ((Original_REFERENCEID == null)) {
                this.Adapter.DeleteCommand.Parameters[10].Value = ((object)(1));
                this.Adapter.DeleteCommand.Parameters[11].Value = global::System.DBNull.Value;
            }
            else {
                this.Adapter.DeleteCommand.Parameters[10].Value = ((object)(0));
                this.Adapter.DeleteCommand.Parameters[11].Value = ((string)(Original_REFERENCEID));
            }
            this.Adapter.DeleteCommand.Parameters[12].Value = ((bool)(Original_MULTNAME));
            this.Adapter.DeleteCommand.Parameters[13].Value = ((bool)(Original_MULTSIGN));
            if ((Original_WEBGIFTID.HasValue == true)) {
                this.Adapter.DeleteCommand.Parameters[14].Value = ((object)(0));
                this.Adapter.DeleteCommand.Parameters[15].Value = ((int)(Original_WEBGIFTID.Value));
            }
            else {
                this.Adapter.DeleteCommand.Parameters[14].Value = ((object)(1));
                this.Adapter.DeleteCommand.Parameters[15].Value = global::System.DBNull.Value;
            }
            if ((Original_MISCTEXT == null)) {
                this.Adapter.DeleteCommand.Parameters[16].Value = ((object)(1));
                this.Adapter.DeleteCommand.Parameters[17].Value = global::System.DBNull.Value;
            }
            else {
                this.Adapter.DeleteCommand.Parameters[16].Value = ((object)(0));
                this.Adapter.DeleteCommand.Parameters[17].Value = ((string)(Original_MISCTEXT));
            }
            if ((Original_MONYCCBATID.HasValue == true)) {
                this.Adapter.DeleteCommand.Parameters[18].Value = ((object)(0));
                this.Adapter.DeleteCommand.Parameters[19].Value = ((int)(Original_MONYCCBATID.Value));
            }
            else {
                this.Adapter.DeleteCommand.Parameters[18].Value = ((object)(1));
                this.Adapter.DeleteCommand.Parameters[19].Value = global::System.DBNull.Value;
            }
            global::System.Data.ConnectionState previousConnectionState = this.Adapter.DeleteCommand.Connection.State;
            if (((this.Adapter.DeleteCommand.Connection.State & global::System.Data.ConnectionState.Open) 
                        != global::System.Data.ConnectionState.Open)) {
                this.Adapter.DeleteCommand.Connection.Open();
            }
            try {
                int returnValue = this.Adapter.DeleteCommand.ExecuteNonQuery();
                return returnValue;
            }
            finally {
                if ((previousConnectionState == global::System.Data.ConnectionState.Closed)) {
                    this.Adapter.DeleteCommand.Connection.Close();
                }
            }
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
        [global::System.ComponentModel.Design.HelpKeywordAttribute("vs.data.TableAdapter")]
        [global::System.ComponentModel.DataObjectMethodAttribute(global::System.ComponentModel.DataObjectMethodType.Insert, true)]
        public virtual int Insert(global::System.Nullable<int> MID, string TMCAMPAIGN, string TMUNIQNUM, bool RECURRED, global::System.Nullable<global::System.DateTime> RECURENDDTE, string REFERENCEID, bool MULTNAME, bool MULTSIGN, global::System.Nullable<int> WEBGIFTID, string MISCTEXT, global::System.Nullable<int> MONYCCBATID, string MISCJSON) {
            if ((MID.HasValue == true)) {
                this.Adapter.InsertCommand.Parameters[0].Value = ((int)(MID.Value));
            }
            else {
                this.Adapter.InsertCommand.Parameters[0].Value = global::System.DBNull.Value;
            }
            if ((TMCAMPAIGN == null)) {
                this.Adapter.InsertCommand.Parameters[1].Value = global::System.DBNull.Value;
            }
            else {
                this.Adapter.InsertCommand.Parameters[1].Value = ((string)(TMCAMPAIGN));
            }
            if ((TMUNIQNUM == null)) {
                this.Adapter.InsertCommand.Parameters[2].Value = global::System.DBNull.Value;
            }
            else {
                this.Adapter.InsertCommand.Parameters[2].Value = ((string)(TMUNIQNUM));
            }
            this.Adapter.InsertCommand.Parameters[3].Value = ((bool)(RECURRED));
            if ((RECURENDDTE.HasValue == true)) {
                this.Adapter.InsertCommand.Parameters[4].Value = ((System.DateTime)(RECURENDDTE.Value));
            }
            else {
                this.Adapter.InsertCommand.Parameters[4].Value = global::System.DBNull.Value;
            }
            if ((REFERENCEID == null)) {
                this.Adapter.InsertCommand.Parameters[5].Value = global::System.DBNull.Value;
            }
            else {
                this.Adapter.InsertCommand.Parameters[5].Value = ((string)(REFERENCEID));
            }
            this.Adapter.InsertCommand.Parameters[6].Value = ((bool)(MULTNAME));
            this.Adapter.InsertCommand.Parameters[7].Value = ((bool)(MULTSIGN));
            if ((WEBGIFTID.HasValue == true)) {
                this.Adapter.InsertCommand.Parameters[8].Value = ((int)(WEBGIFTID.Value));
            }
            else {
                this.Adapter.InsertCommand.Parameters[8].Value = global::System.DBNull.Value;
            }
            if ((MISCTEXT == null)) {
                this.Adapter.InsertCommand.Parameters[9].Value = global::System.DBNull.Value;
            }
            else {
                this.Adapter.InsertCommand.Parameters[9].Value = ((string)(MISCTEXT));
            }
            if ((MONYCCBATID.HasValue == true)) {
                this.Adapter.InsertCommand.Parameters[10].Value = ((int)(MONYCCBATID.Value));
            }
            else {
                this.Adapter.InsertCommand.Parameters[10].Value = global::System.DBNull.Value;
            }
            if ((MISCJSON == null)) {
                this.Adapter.InsertCommand.Parameters[11].Value = global::System.DBNull.Value;
            }
            else {
                this.Adapter.InsertCommand.Parameters[11].Value = ((string)(MISCJSON));
            }
            global::System.Data.ConnectionState previousConnectionState = this.Adapter.InsertCommand.Connection.State;
            if (((this.Adapter.InsertCommand.Connection.State & global::System.Data.ConnectionState.Open) 
                        != global::System.Data.ConnectionState.Open)) {
                this.Adapter.InsertCommand.Connection.Open();
            }
            try {
                int returnValue = this.Adapter.InsertCommand.ExecuteNonQuery();
                return returnValue;
            }
            finally {
                if ((previousConnectionState == global::System.Data.ConnectionState.Closed)) {
                    this.Adapter.InsertCommand.Connection.Close();
                }
            }
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
        [global::System.ComponentModel.Design.HelpKeywordAttribute("vs.data.TableAdapter")]
        [global::System.ComponentModel.DataObjectMethodAttribute(global::System.ComponentModel.DataObjectMethodType.Update, true)]
        public virtual int Update(
                    global::System.Nullable<int> MID, 
                    string TMCAMPAIGN, 
                    string TMUNIQNUM, 
                    bool RECURRED, 
                    global::System.Nullable<global::System.DateTime> RECURENDDTE, 
                    string REFERENCEID, 
                    bool MULTNAME, 
                    bool MULTSIGN, 
                    global::System.Nullable<int> WEBGIFTID, 
                    string MISCTEXT, 
                    global::System.Nullable<int> MONYCCBATID, 
                    string MISCJSON, 
                    int Original_MONYADDIID, 
                    global::System.Nullable<int> Original_MID, 
                    string Original_TMCAMPAIGN, 
                    string Original_TMUNIQNUM, 
                    bool Original_RECURRED, 
                    global::System.Nullable<global::System.DateTime> Original_RECURENDDTE, 
                    string Original_REFERENCEID, 
                    bool Original_MULTNAME, 
                    bool Original_MULTSIGN, 
                    global::System.Nullable<int> Original_WEBGIFTID, 
                    string Original_MISCTEXT, 
                    global::System.Nullable<int> Original_MONYCCBATID, 
                    int MONYADDIID) {
            if ((MID.HasValue == true)) {
                this.Adapter.UpdateCommand.Parameters[0].Value = ((int)(MID.Value));
            }
            else {
                this.Adapter.UpdateCommand.Parameters[0].Value = global::System.DBNull.Value;
            }
            if ((TMCAMPAIGN == null)) {
                this.Adapter.UpdateCommand.Parameters[1].Value = global::System.DBNull.Value;
            }
            else {
                this.Adapter.UpdateCommand.Parameters[1].Value = ((string)(TMCAMPAIGN));
            }
            if ((TMUNIQNUM == null)) {
                this.Adapter.UpdateCommand.Parameters[2].Value = global::System.DBNull.Value;
            }
            else {
                this.Adapter.UpdateCommand.Parameters[2].Value = ((string)(TMUNIQNUM));
            }
            this.Adapter.UpdateCommand.Parameters[3].Value = ((bool)(RECURRED));
            if ((RECURENDDTE.HasValue == true)) {
                this.Adapter.UpdateCommand.Parameters[4].Value = ((System.DateTime)(RECURENDDTE.Value));
            }
            else {
                this.Adapter.UpdateCommand.Parameters[4].Value = global::System.DBNull.Value;
            }
            if ((REFERENCEID == null)) {
                this.Adapter.UpdateCommand.Parameters[5].Value = global::System.DBNull.Value;
            }
            else {
                this.Adapter.UpdateCommand.Parameters[5].Value = ((string)(REFERENCEID));
            }
            this.Adapter.UpdateCommand.Parameters[6].Value = ((bool)(MULTNAME));
            this.Adapter.UpdateCommand.Parameters[7].Value = ((bool)(MULTSIGN));
            if ((WEBGIFTID.HasValue == true)) {
                this.Adapter.UpdateCommand.Parameters[8].Value = ((int)(WEBGIFTID.Value));
            }
            else {
                this.Adapter.UpdateCommand.Parameters[8].Value = global::System.DBNull.Value;
            }
            if ((MISCTEXT == null)) {
                this.Adapter.UpdateCommand.Parameters[9].Value = global::System.DBNull.Value;
            }
            else {
                this.Adapter.UpdateCommand.Parameters[9].Value = ((string)(MISCTEXT));
            }
            if ((MONYCCBATID.HasValue == true)) {
                this.Adapter.UpdateCommand.Parameters[10].Value = ((int)(MONYCCBATID.Value));
            }
            else {
                this.Adapter.UpdateCommand.Parameters[10].Value = global::System.DBNull.Value;
            }
            if ((MISCJSON == null)) {
                this.Adapter.UpdateCommand.Parameters[11].Value = global::System.DBNull.Value;
            }
            else {
                this.Adapter.UpdateCommand.Parameters[11].Value = ((string)(MISCJSON));
            }
            this.Adapter.UpdateCommand.Parameters[12].Value = ((int)(Original_MONYADDIID));
            if ((Original_MID.HasValue == true)) {
                this.Adapter.UpdateCommand.Parameters[13].Value = ((object)(0));
                this.Adapter.UpdateCommand.Parameters[14].Value = ((int)(Original_MID.Value));
            }
            else {
                this.Adapter.UpdateCommand.Parameters[13].Value = ((object)(1));
                this.Adapter.UpdateCommand.Parameters[14].Value = global::System.DBNull.Value;
            }
            if ((Original_TMCAMPAIGN == null)) {
                this.Adapter.UpdateCommand.Parameters[15].Value = ((object)(1));
                this.Adapter.UpdateCommand.Parameters[16].Value = global::System.DBNull.Value;
            }
            else {
                this.Adapter.UpdateCommand.Parameters[15].Value = ((object)(0));
                this.Adapter.UpdateCommand.Parameters[16].Value = ((string)(Original_TMCAMPAIGN));
            }
            if ((Original_TMUNIQNUM == null)) {
                this.Adapter.UpdateCommand.Parameters[17].Value = ((object)(1));
                this.Adapter.UpdateCommand.Parameters[18].Value = global::System.DBNull.Value;
            }
            else {
                this.Adapter.UpdateCommand.Parameters[17].Value = ((object)(0));
                this.Adapter.UpdateCommand.Parameters[18].Value = ((string)(Original_TMUNIQNUM));
            }
            this.Adapter.UpdateCommand.Parameters[19].Value = ((bool)(Original_RECURRED));
            if ((Original_RECURENDDTE.HasValue == true)) {
                this.Adapter.UpdateCommand.Parameters[20].Value = ((object)(0));
                this.Adapter.UpdateCommand.Parameters[21].Value = ((System.DateTime)(Original_RECURENDDTE.Value));
            }
            else {
                this.Adapter.UpdateCommand.Parameters[20].Value = ((object)(1));
                this.Adapter.UpdateCommand.Parameters[21].Value = global::System.DBNull.Value;
            }
            if ((Original_REFERENCEID == null)) {
                this.Adapter.UpdateCommand.Parameters[22].Value = ((object)(1));
                this.Adapter.UpdateCommand.Parameters[23].Value = global::System.DBNull.Value;
            }
            else {
                this.Adapter.UpdateCommand.Parameters[22].Value = ((object)(0));
                this.Adapter.UpdateCommand.Parameters[23].Value = ((string)(Original_REFERENCEID));
            }
            this.Adapter.UpdateCommand.Parameters[24].Value = ((bool)(Original_MULTNAME));
            this.Adapter.UpdateCommand.Parameters[25].Value = ((bool)(Original_MULTSIGN));
            if ((Original_WEBGIFTID.HasValue == true)) {
                this.Adapter.UpdateCommand.Parameters[26].Value = ((object)(0));
                this.Adapter.UpdateCommand.Parameters[27].Value = ((int)(Original_WEBGIFTID.Value));
            }
            else {
                this.Adapter.UpdateCommand.Parameters[26].Value = ((object)(1));
                this.Adapter.UpdateCommand.Parameters[27].Value = global::System.DBNull.Value;
            }
            if ((Original_MISCTEXT == null)) {
                this.Adapter.UpdateCommand.Parameters[28].Value = ((object)(1));
                this.Adapter.UpdateCommand.Parameters[29].Value = global::System.DBNull.Value;
            }
            else {
                this.Adapter.UpdateCommand.Parameters[28].Value = ((object)(0));
                this.Adapter.UpdateCommand.Parameters[29].Value = ((string)(Original_MISCTEXT));
            }
            if ((Original_MONYCCBATID.HasValue == true)) {
                this.Adapter.UpdateCommand.Parameters[30].Value = ((object)(0));
                this.Adapter.UpdateCommand.Parameters[31].Value = ((int)(Original_MONYCCBATID.Value));
            }
            else {
                this.Adapter.UpdateCommand.Parameters[30].Value = ((object)(1));
                this.Adapter.UpdateCommand.Parameters[31].Value = global::System.DBNull.Value;
            }
            this.Adapter.UpdateCommand.Parameters[32].Value = ((int)(MONYADDIID));
            global::System.Data.ConnectionState previousConnectionState = this.Adapter.UpdateCommand.Connection.State;
            if (((this.Adapter.UpdateCommand.Connection.State & global::System.Data.ConnectionState.Open) 
                        != global::System.Data.ConnectionState.Open)) {
                this.Adapter.UpdateCommand.Connection.Open();
            }
            try {
                int returnValue = this.Adapter.UpdateCommand.ExecuteNonQuery();
                return returnValue;
            }
            finally {
                if ((previousConnectionState == global::System.Data.ConnectionState.Closed)) {
                    this.Adapter.UpdateCommand.Connection.Close();
                }
            }
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
        [global::System.ComponentModel.Design.HelpKeywordAttribute("vs.data.TableAdapter")]
        [global::System.ComponentModel.DataObjectMethodAttribute(global::System.ComponentModel.DataObjectMethodType.Update, true)]
        public virtual int Update(
                    global::System.Nullable<int> MID, 
                    string TMCAMPAIGN, 
                    string TMUNIQNUM, 
                    bool RECURRED, 
                    global::System.Nullable<global::System.DateTime> RECURENDDTE, 
                    string REFERENCEID, 
                    bool MULTNAME, 
                    bool MULTSIGN, 
                    global::System.Nullable<int> WEBGIFTID, 
                    string MISCTEXT, 
                    global::System.Nullable<int> MONYCCBATID, 
                    string MISCJSON, 
                    int Original_MONYADDIID, 
                    global::System.Nullable<int> Original_MID, 
                    string Original_TMCAMPAIGN, 
                    string Original_TMUNIQNUM, 
                    bool Original_RECURRED, 
                    global::System.Nullable<global::System.DateTime> Original_RECURENDDTE, 
                    string Original_REFERENCEID, 
                    bool Original_MULTNAME, 
                    bool Original_MULTSIGN, 
                    global::System.Nullable<int> Original_WEBGIFTID, 
                    string Original_MISCTEXT, 
                    global::System.Nullable<int> Original_MONYCCBATID) {
            return this.Update(MID, TMCAMPAIGN, TMUNIQNUM, RECURRED, RECURENDDTE, REFERENCEID, MULTNAME, MULTSIGN, WEBGIFTID, MISCTEXT, MONYCCBATID, MISCJSON, Original_MONYADDIID, Original_MID, Original_TMCAMPAIGN, Original_TMUNIQNUM, Original_RECURRED, Original_RECURENDDTE, Original_REFERENCEID, Original_MULTNAME, Original_MULTSIGN, Original_WEBGIFTID, Original_MISCTEXT, Original_MONYCCBATID, Original_MONYADDIID);
        }
    }
    
    /// <summary>
    ///Represents the connection and commands used to retrieve and save data.
    ///</summary>
    [global::System.ComponentModel.DesignerCategoryAttribute("code")]
    [global::System.ComponentModel.ToolboxItem(true)]
    [global::System.ComponentModel.DataObjectAttribute(true)]
    [global::System.ComponentModel.DesignerAttribute("Microsoft.VSDesigner.DataSource.Design.TableAdapterDesigner, Microsoft.VSDesigner" +
        ", Version=10.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a")]
    [global::System.ComponentModel.Design.HelpKeywordAttribute("vs.data.TableAdapter")]
    public partial class v_people_monyTableAdapter : global::System.ComponentModel.Component {
        
        private global::System.Data.SqlClient.SqlDataAdapter _adapter;
        
        private global::System.Data.SqlClient.SqlConnection _connection;
        
        private global::System.Data.SqlClient.SqlTransaction _transaction;
        
        private global::System.Data.SqlClient.SqlCommand[] _commandCollection;
        
        private bool _clearBeforeFill;
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
        public v_people_monyTableAdapter() {
            this.ClearBeforeFill = true;
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
        protected internal global::System.Data.SqlClient.SqlDataAdapter Adapter {
            get {
                if ((this._adapter == null)) {
                    this.InitAdapter();
                }
                return this._adapter;
            }
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
        internal global::System.Data.SqlClient.SqlConnection Connection {
            get {
                if ((this._connection == null)) {
                    this.InitConnection();
                }
                return this._connection;
            }
            set {
                this._connection = value;
                if ((this.Adapter.InsertCommand != null)) {
                    this.Adapter.InsertCommand.Connection = value;
                }
                if ((this.Adapter.DeleteCommand != null)) {
                    this.Adapter.DeleteCommand.Connection = value;
                }
                if ((this.Adapter.UpdateCommand != null)) {
                    this.Adapter.UpdateCommand.Connection = value;
                }
                for (int i = 0; (i < this.CommandCollection.Length); i = (i + 1)) {
                    if ((this.CommandCollection[i] != null)) {
                        ((global::System.Data.SqlClient.SqlCommand)(this.CommandCollection[i])).Connection = value;
                    }
                }
            }
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
        internal global::System.Data.SqlClient.SqlTransaction Transaction {
            get {
                return this._transaction;
            }
            set {
                this._transaction = value;
                for (int i = 0; (i < this.CommandCollection.Length); i = (i + 1)) {
                    this.CommandCollection[i].Transaction = this._transaction;
                }
                if (((this.Adapter != null) 
                            && (this.Adapter.DeleteCommand != null))) {
                    this.Adapter.DeleteCommand.Transaction = this._transaction;
                }
                if (((this.Adapter != null) 
                            && (this.Adapter.InsertCommand != null))) {
                    this.Adapter.InsertCommand.Transaction = this._transaction;
                }
                if (((this.Adapter != null) 
                            && (this.Adapter.UpdateCommand != null))) {
                    this.Adapter.UpdateCommand.Transaction = this._transaction;
                }
            }
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
        protected global::System.Data.SqlClient.SqlCommand[] CommandCollection {
            get {
                if ((this._commandCollection == null)) {
                    this.InitCommandCollection();
                }
                return this._commandCollection;
            }
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
        public bool ClearBeforeFill {
            get {
                return this._clearBeforeFill;
            }
            set {
                this._clearBeforeFill = value;
            }
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
        private void InitAdapter() {
            this._adapter = new global::System.Data.SqlClient.SqlDataAdapter();
            global::System.Data.Common.DataTableMapping tableMapping = new global::System.Data.Common.DataTableMapping();
            tableMapping.SourceTable = "Table";
            tableMapping.DataSetTable = "v_people_mony";
            tableMapping.ColumnMappings.Add("PID", "PID");
            tableMapping.ColumnMappings.Add("MID", "MID");
            tableMapping.ColumnMappings.Add("BATCHDTE", "BATCHDTE");
            tableMapping.ColumnMappings.Add("BATCHNO", "BATCHNO");
            tableMapping.ColumnMappings.Add("AMT", "AMT");
            tableMapping.ColumnMappings.Add("TRACKNO", "TRACKNO");
            tableMapping.ColumnMappings.Add("MONYTYPE", "MONYTYPE");
            tableMapping.ColumnMappings.Add("FUNDCODE", "FUNDCODE");
            tableMapping.ColumnMappings.Add("SRCECODE", "SRCECODE");
            tableMapping.ColumnMappings.Add("SRCEDESC", "SRCEDESC");
            tableMapping.ColumnMappings.Add("PKGECODE", "PKGECODE");
            tableMapping.ColumnMappings.Add("PKGEDESC", "PKGEDESC");
            tableMapping.ColumnMappings.Add("PROGTYPE", "PROGTYPE");
            tableMapping.ColumnMappings.Add("PROGDESC", "PROGDESC");
            tableMapping.ColumnMappings.Add("CENTERCODE", "CENTERCODE");
            tableMapping.ColumnMappings.Add("CENTERDESC", "CENTERDESC");
            tableMapping.ColumnMappings.Add("CAMPGNCODE", "CAMPGNCODE");
            tableMapping.ColumnMappings.Add("CAMPGNDESC", "CAMPGNDESC");
            tableMapping.ColumnMappings.Add("ADJTYPE", "ADJTYPE");
            tableMapping.ColumnMappings.Add("ADJDESC", "ADJDESC");
            tableMapping.ColumnMappings.Add("COUNTER", "COUNTER");
            tableMapping.ColumnMappings.Add("SOFTMONEY", "SOFTMONEY");
            tableMapping.ColumnMappings.Add("FUNDORGNAME", "FUNDORGNAME");
            tableMapping.ColumnMappings.Add("FUNDORGADDR", "FUNDORGADDR");
            tableMapping.ColumnMappings.Add("EXCEP", "EXCEP");
            tableMapping.ColumnMappings.Add("GIFTTYPE", "GIFTTYPE");
            tableMapping.ColumnMappings.Add("MONYCODE", "MONYCODE");
            this._adapter.TableMappings.Add(tableMapping);
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
        private void InitConnection() {
            this._connection = new global::System.Data.SqlClient.SqlConnection();
            this._connection.ConnectionString = global::cmdiapp.n.reportDef.Properties.Settings.Default.dxDEMOCConnectionString;
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
        private void InitCommandCollection() {
            this._commandCollection = new global::System.Data.SqlClient.SqlCommand[1];
            this._commandCollection[0] = new global::System.Data.SqlClient.SqlCommand();
            this._commandCollection[0].Connection = this.Connection;
            this._commandCollection[0].CommandText = @"SELECT PID, MID, BATCHDTE, BATCHNO, AMT, TRACKNO, MONYTYPE, FUNDCODE, SRCECODE, SRCEDESC, PKGECODE, PKGEDESC, PROGTYPE, PROGDESC, CENTERCODE, CENTERDESC, CAMPGNCODE, CAMPGNDESC, ADJTYPE, ADJDESC, COUNTER, SOFTMONEY, FUNDORGNAME, FUNDORGADDR, EXCEP, GIFTTYPE, MONYCODE FROM dbo.v_people_mony";
            this._commandCollection[0].CommandType = global::System.Data.CommandType.Text;
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
        [global::System.ComponentModel.Design.HelpKeywordAttribute("vs.data.TableAdapter")]
        [global::System.ComponentModel.DataObjectMethodAttribute(global::System.ComponentModel.DataObjectMethodType.Fill, true)]
        public virtual int Fill(dxDEMOCDataSet2.v_people_monyDataTable dataTable) {
            this.Adapter.SelectCommand = this.CommandCollection[0];
            if ((this.ClearBeforeFill == true)) {
                dataTable.Clear();
            }
            int returnValue = this.Adapter.Fill(dataTable);
            return returnValue;
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
        [global::System.ComponentModel.Design.HelpKeywordAttribute("vs.data.TableAdapter")]
        [global::System.ComponentModel.DataObjectMethodAttribute(global::System.ComponentModel.DataObjectMethodType.Select, true)]
        public virtual dxDEMOCDataSet2.v_people_monyDataTable GetData() {
            this.Adapter.SelectCommand = this.CommandCollection[0];
            dxDEMOCDataSet2.v_people_monyDataTable dataTable = new dxDEMOCDataSet2.v_people_monyDataTable();
            this.Adapter.Fill(dataTable);
            return dataTable;
        }
    }
    
    /// <summary>
    ///TableAdapterManager is used to coordinate TableAdapters in the dataset to enable Hierarchical Update scenarios
    ///</summary>
    [global::System.ComponentModel.DesignerCategoryAttribute("code")]
    [global::System.ComponentModel.ToolboxItem(true)]
    [global::System.ComponentModel.DesignerAttribute("Microsoft.VSDesigner.DataSource.Design.TableAdapterManagerDesigner, Microsoft.VSD" +
        "esigner, Version=10.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a")]
    [global::System.ComponentModel.Design.HelpKeywordAttribute("vs.data.TableAdapterManager")]
    public partial class TableAdapterManager : global::System.ComponentModel.Component {
        
        private UpdateOrderOption _updateOrder;
        
        private MONYADDITableAdapter _mONYADDITableAdapter;
        
        private bool _backupDataSetBeforeUpdate;
        
        private global::System.Data.IDbConnection _connection;
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
        public UpdateOrderOption UpdateOrder {
            get {
                return this._updateOrder;
            }
            set {
                this._updateOrder = value;
            }
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
        [global::System.ComponentModel.EditorAttribute("Microsoft.VSDesigner.DataSource.Design.TableAdapterManagerPropertyEditor, Microso" +
            "ft.VSDesigner, Version=10.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3" +
            "a", "System.Drawing.Design.UITypeEditor")]
        public MONYADDITableAdapter MONYADDITableAdapter {
            get {
                return this._mONYADDITableAdapter;
            }
            set {
                this._mONYADDITableAdapter = value;
            }
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
        public bool BackupDataSetBeforeUpdate {
            get {
                return this._backupDataSetBeforeUpdate;
            }
            set {
                this._backupDataSetBeforeUpdate = value;
            }
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
        [global::System.ComponentModel.Browsable(false)]
        public global::System.Data.IDbConnection Connection {
            get {
                if ((this._connection != null)) {
                    return this._connection;
                }
                if (((this._mONYADDITableAdapter != null) 
                            && (this._mONYADDITableAdapter.Connection != null))) {
                    return this._mONYADDITableAdapter.Connection;
                }
                return null;
            }
            set {
                this._connection = value;
            }
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
        [global::System.ComponentModel.Browsable(false)]
        public int TableAdapterInstanceCount {
            get {
                int count = 0;
                if ((this._mONYADDITableAdapter != null)) {
                    count = (count + 1);
                }
                return count;
            }
        }
        
        /// <summary>
        ///Update rows in top-down order.
        ///</summary>
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
        private int UpdateUpdatedRows(dxDEMOCDataSet2 dataSet, global::System.Collections.Generic.List<global::System.Data.DataRow> allChangedRows, global::System.Collections.Generic.List<global::System.Data.DataRow> allAddedRows) {
            int result = 0;
            if ((this._mONYADDITableAdapter != null)) {
                global::System.Data.DataRow[] updatedRows = dataSet.MONYADDI.Select(null, null, global::System.Data.DataViewRowState.ModifiedCurrent);
                updatedRows = this.GetRealUpdatedRows(updatedRows, allAddedRows);
                if (((updatedRows != null) 
                            && (0 < updatedRows.Length))) {
                    result = (result + this._mONYADDITableAdapter.Update(updatedRows));
                    allChangedRows.AddRange(updatedRows);
                }
            }
            return result;
        }
        
        /// <summary>
        ///Insert rows in top-down order.
        ///</summary>
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
        private int UpdateInsertedRows(dxDEMOCDataSet2 dataSet, global::System.Collections.Generic.List<global::System.Data.DataRow> allAddedRows) {
            int result = 0;
            if ((this._mONYADDITableAdapter != null)) {
                global::System.Data.DataRow[] addedRows = dataSet.MONYADDI.Select(null, null, global::System.Data.DataViewRowState.Added);
                if (((addedRows != null) 
                            && (0 < addedRows.Length))) {
                    result = (result + this._mONYADDITableAdapter.Update(addedRows));
                    allAddedRows.AddRange(addedRows);
                }
            }
            return result;
        }
        
        /// <summary>
        ///Delete rows in bottom-up order.
        ///</summary>
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
        private int UpdateDeletedRows(dxDEMOCDataSet2 dataSet, global::System.Collections.Generic.List<global::System.Data.DataRow> allChangedRows) {
            int result = 0;
            if ((this._mONYADDITableAdapter != null)) {
                global::System.Data.DataRow[] deletedRows = dataSet.MONYADDI.Select(null, null, global::System.Data.DataViewRowState.Deleted);
                if (((deletedRows != null) 
                            && (0 < deletedRows.Length))) {
                    result = (result + this._mONYADDITableAdapter.Update(deletedRows));
                    allChangedRows.AddRange(deletedRows);
                }
            }
            return result;
        }
        
        /// <summary>
        ///Remove inserted rows that become updated rows after calling TableAdapter.Update(inserted rows) first
        ///</summary>
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
        private global::System.Data.DataRow[] GetRealUpdatedRows(global::System.Data.DataRow[] updatedRows, global::System.Collections.Generic.List<global::System.Data.DataRow> allAddedRows) {
            if (((updatedRows == null) 
                        || (updatedRows.Length < 1))) {
                return updatedRows;
            }
            if (((allAddedRows == null) 
                        || (allAddedRows.Count < 1))) {
                return updatedRows;
            }
            global::System.Collections.Generic.List<global::System.Data.DataRow> realUpdatedRows = new global::System.Collections.Generic.List<global::System.Data.DataRow>();
            for (int i = 0; (i < updatedRows.Length); i = (i + 1)) {
                global::System.Data.DataRow row = updatedRows[i];
                if ((allAddedRows.Contains(row) == false)) {
                    realUpdatedRows.Add(row);
                }
            }
            return realUpdatedRows.ToArray();
        }
        
        /// <summary>
        ///Update all changes to the dataset.
        ///</summary>
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
        public virtual int UpdateAll(dxDEMOCDataSet2 dataSet) {
            if ((dataSet == null)) {
                throw new global::System.ArgumentNullException("dataSet");
            }
            if ((dataSet.HasChanges() == false)) {
                return 0;
            }
            if (((this._mONYADDITableAdapter != null) 
                        && (this.MatchTableAdapterConnection(this._mONYADDITableAdapter.Connection) == false))) {
                throw new global::System.ArgumentException("All TableAdapters managed by a TableAdapterManager must use the same connection s" +
                        "tring.");
            }
            global::System.Data.IDbConnection workConnection = this.Connection;
            if ((workConnection == null)) {
                throw new global::System.ApplicationException("TableAdapterManager contains no connection information. Set each TableAdapterMana" +
                        "ger TableAdapter property to a valid TableAdapter instance.");
            }
            bool workConnOpened = false;
            if (((workConnection.State & global::System.Data.ConnectionState.Broken) 
                        == global::System.Data.ConnectionState.Broken)) {
                workConnection.Close();
            }
            if ((workConnection.State == global::System.Data.ConnectionState.Closed)) {
                workConnection.Open();
                workConnOpened = true;
            }
            global::System.Data.IDbTransaction workTransaction = workConnection.BeginTransaction();
            if ((workTransaction == null)) {
                throw new global::System.ApplicationException("The transaction cannot begin. The current data connection does not support transa" +
                        "ctions or the current state is not allowing the transaction to begin.");
            }
            global::System.Collections.Generic.List<global::System.Data.DataRow> allChangedRows = new global::System.Collections.Generic.List<global::System.Data.DataRow>();
            global::System.Collections.Generic.List<global::System.Data.DataRow> allAddedRows = new global::System.Collections.Generic.List<global::System.Data.DataRow>();
            global::System.Collections.Generic.List<global::System.Data.Common.DataAdapter> adaptersWithAcceptChangesDuringUpdate = new global::System.Collections.Generic.List<global::System.Data.Common.DataAdapter>();
            global::System.Collections.Generic.Dictionary<object, global::System.Data.IDbConnection> revertConnections = new global::System.Collections.Generic.Dictionary<object, global::System.Data.IDbConnection>();
            int result = 0;
            global::System.Data.DataSet backupDataSet = null;
            if (this.BackupDataSetBeforeUpdate) {
                backupDataSet = new global::System.Data.DataSet();
                backupDataSet.Merge(dataSet);
            }
            try {
                // ---- Prepare for update -----------
                //
                if ((this._mONYADDITableAdapter != null)) {
                    revertConnections.Add(this._mONYADDITableAdapter, this._mONYADDITableAdapter.Connection);
                    this._mONYADDITableAdapter.Connection = ((global::System.Data.SqlClient.SqlConnection)(workConnection));
                    this._mONYADDITableAdapter.Transaction = ((global::System.Data.SqlClient.SqlTransaction)(workTransaction));
                    if (this._mONYADDITableAdapter.Adapter.AcceptChangesDuringUpdate) {
                        this._mONYADDITableAdapter.Adapter.AcceptChangesDuringUpdate = false;
                        adaptersWithAcceptChangesDuringUpdate.Add(this._mONYADDITableAdapter.Adapter);
                    }
                }
                // 
                //---- Perform updates -----------
                //
                if ((this.UpdateOrder == UpdateOrderOption.UpdateInsertDelete)) {
                    result = (result + this.UpdateUpdatedRows(dataSet, allChangedRows, allAddedRows));
                    result = (result + this.UpdateInsertedRows(dataSet, allAddedRows));
                }
                else {
                    result = (result + this.UpdateInsertedRows(dataSet, allAddedRows));
                    result = (result + this.UpdateUpdatedRows(dataSet, allChangedRows, allAddedRows));
                }
                result = (result + this.UpdateDeletedRows(dataSet, allChangedRows));
                // 
                //---- Commit updates -----------
                //
                workTransaction.Commit();
                if ((0 < allAddedRows.Count)) {
                    global::System.Data.DataRow[] rows = new System.Data.DataRow[allAddedRows.Count];
                    allAddedRows.CopyTo(rows);
                    for (int i = 0; (i < rows.Length); i = (i + 1)) {
                        global::System.Data.DataRow row = rows[i];
                        row.AcceptChanges();
                    }
                }
                if ((0 < allChangedRows.Count)) {
                    global::System.Data.DataRow[] rows = new System.Data.DataRow[allChangedRows.Count];
                    allChangedRows.CopyTo(rows);
                    for (int i = 0; (i < rows.Length); i = (i + 1)) {
                        global::System.Data.DataRow row = rows[i];
                        row.AcceptChanges();
                    }
                }
            }
            catch (global::System.Exception ex) {
                workTransaction.Rollback();
                // ---- Restore the dataset -----------
                if (this.BackupDataSetBeforeUpdate) {
                    global::System.Diagnostics.Debug.Assert((backupDataSet != null));
                    dataSet.Clear();
                    dataSet.Merge(backupDataSet);
                }
                else {
                    if ((0 < allAddedRows.Count)) {
                        global::System.Data.DataRow[] rows = new System.Data.DataRow[allAddedRows.Count];
                        allAddedRows.CopyTo(rows);
                        for (int i = 0; (i < rows.Length); i = (i + 1)) {
                            global::System.Data.DataRow row = rows[i];
                            row.AcceptChanges();
                            row.SetAdded();
                        }
                    }
                }
                throw ex;
            }
            finally {
                if (workConnOpened) {
                    workConnection.Close();
                }
                if ((this._mONYADDITableAdapter != null)) {
                    this._mONYADDITableAdapter.Connection = ((global::System.Data.SqlClient.SqlConnection)(revertConnections[this._mONYADDITableAdapter]));
                    this._mONYADDITableAdapter.Transaction = null;
                }
                if ((0 < adaptersWithAcceptChangesDuringUpdate.Count)) {
                    global::System.Data.Common.DataAdapter[] adapters = new System.Data.Common.DataAdapter[adaptersWithAcceptChangesDuringUpdate.Count];
                    adaptersWithAcceptChangesDuringUpdate.CopyTo(adapters);
                    for (int i = 0; (i < adapters.Length); i = (i + 1)) {
                        global::System.Data.Common.DataAdapter adapter = adapters[i];
                        adapter.AcceptChangesDuringUpdate = true;
                    }
                }
            }
            return result;
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
        protected virtual void SortSelfReferenceRows(global::System.Data.DataRow[] rows, global::System.Data.DataRelation relation, bool childFirst) {
            global::System.Array.Sort<global::System.Data.DataRow>(rows, new SelfReferenceComparer(relation, childFirst));
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
        protected virtual bool MatchTableAdapterConnection(global::System.Data.IDbConnection inputConnection) {
            if ((this._connection != null)) {
                return true;
            }
            if (((this.Connection == null) 
                        || (inputConnection == null))) {
                return true;
            }
            if (string.Equals(this.Connection.ConnectionString, inputConnection.ConnectionString, global::System.StringComparison.Ordinal)) {
                return true;
            }
            return false;
        }
        
        /// <summary>
        ///Update Order Option
        ///</summary>
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
        public enum UpdateOrderOption {
            
            InsertUpdateDelete = 0,
            
            UpdateInsertDelete = 1,
        }
        
        /// <summary>
        ///Used to sort self-referenced table's rows
        ///</summary>
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
        private class SelfReferenceComparer : object, global::System.Collections.Generic.IComparer<global::System.Data.DataRow> {
            
            private global::System.Data.DataRelation _relation;
            
            private int _childFirst;
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
            internal SelfReferenceComparer(global::System.Data.DataRelation relation, bool childFirst) {
                this._relation = relation;
                if (childFirst) {
                    this._childFirst = -1;
                }
                else {
                    this._childFirst = 1;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
            private global::System.Data.DataRow GetRoot(global::System.Data.DataRow row, out int distance) {
                global::System.Diagnostics.Debug.Assert((row != null));
                global::System.Data.DataRow root = row;
                distance = 0;

                global::System.Collections.Generic.IDictionary<global::System.Data.DataRow, global::System.Data.DataRow> traversedRows = new global::System.Collections.Generic.Dictionary<global::System.Data.DataRow, global::System.Data.DataRow>();
                traversedRows[row] = row;

                global::System.Data.DataRow parent = row.GetParentRow(this._relation, global::System.Data.DataRowVersion.Default);
                for (
                ; ((parent != null) 
                            && (traversedRows.ContainsKey(parent) == false)); 
                ) {
                    distance = (distance + 1);
                    root = parent;
                    traversedRows[parent] = parent;
                    parent = parent.GetParentRow(this._relation, global::System.Data.DataRowVersion.Default);
                }

                if ((distance == 0)) {
                    traversedRows.Clear();
                    traversedRows[row] = row;
                    parent = row.GetParentRow(this._relation, global::System.Data.DataRowVersion.Original);
                    for (
                    ; ((parent != null) 
                                && (traversedRows.ContainsKey(parent) == false)); 
                    ) {
                        distance = (distance + 1);
                        root = parent;
                        traversedRows[parent] = parent;
                        parent = parent.GetParentRow(this._relation, global::System.Data.DataRowVersion.Original);
                    }
                }

                return root;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
            public int Compare(global::System.Data.DataRow row1, global::System.Data.DataRow row2) {
                if (object.ReferenceEquals(row1, row2)) {
                    return 0;
                }
                if ((row1 == null)) {
                    return -1;
                }
                if ((row2 == null)) {
                    return 1;
                }

                int distance1 = 0;
                global::System.Data.DataRow root1 = this.GetRoot(row1, out distance1);

                int distance2 = 0;
                global::System.Data.DataRow root2 = this.GetRoot(row2, out distance2);

                if (object.ReferenceEquals(root1, root2)) {
                    return (this._childFirst * distance1.CompareTo(distance2));
                }
                else {
                    global::System.Diagnostics.Debug.Assert(((root1.Table != null) 
                                    && (root2.Table != null)));
                    if ((root1.Table.Rows.IndexOf(root1) < root2.Table.Rows.IndexOf(root2))) {
                        return -1;
                    }
                    else {
                        return 1;
                    }
                }
            }
        }
    }
}

#pragma warning restore 1591