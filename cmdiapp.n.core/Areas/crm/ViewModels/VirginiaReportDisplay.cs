﻿using System;
using System.Runtime.Serialization;

namespace cmdiapp.n.core.Areas.crm.ViewModels
{
    [DataContract]
    public class VirginiaReportDisplay
    {
        [DataMember(Name = "id")]
        public int Id { get; set; }

        [DataMember(Name = "filerId")]
        public int FilerId { get; set; }

        [DataMember(Name = "electionCycle")]
        public string ElectionCycle { get; set; }

        [DataMember(Name = "filingDate")]
        public DateTime FilingDate { get; set; }

        [DataMember(Name = "reportPeriodStart")]
        public DateTime ReportPeriodStart { get; set; }

        [DataMember(Name = "reportPeriodEnd")]
        public DateTime ReportPeriodEnd { get; set; }

        [DataMember(Name = "receiptsThisPeriod")]
        public decimal? ReceiptsThisPeriod { get; set; }

        [DataMember(Name = "disbursementsThisPeriod")]
        public decimal? DisbursementsThisPeriod { get; set; }

        [DataMember(Name = "totalReceiptsThisElectionCycle")]
        public decimal TotalReceiptsThisElectionCycle { get; set; }

        [DataMember(Name = "totalDisbursementsThisElectionCycle")]
        public decimal TotalDisbursementsThisElectionCycle { get; set; }

        [DataMember(Name = "loanBalance")]
        public decimal LoanBalance { get; set; }

        [DataMember(Name = "endingBalance")]
        public decimal EndingBalance { get; set; }

        [DataMember(Name = "isAmendment")]
        public bool IsAmendment { get; set; }

        [DataMember(Name = "amendmentNumber")]
        public int AmendmentNumber { get; set; }

        [DataMember(Name = "createdOn")]
        public DateTime CreatedOn { get; set; }

        [DataMember(Name = "reportType")]
        public string ReportType { get; set; }

        [DataMember(Name = "reportTypeCode")]
        public string ReportTypeCode { get; set; }
    }
}