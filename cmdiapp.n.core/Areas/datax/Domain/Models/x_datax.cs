﻿using cmdiapp.n.core.Areas.crm.Domain.Data;
using cmdiapp.n.core.Domain.ViewModels;
using Newtonsoft.Json;
using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.IO;
using System.Runtime.Serialization;

namespace cmdiapp.n.core.Areas.datax.Domain.Models
{
    [Table("x_datax")]
    public class x_datax : _entityBase_crm, iItemType
    {
        [Column, Key]
        public int Id { get; set; }

        [Column]
        public string caller { get; set; }
        [Column]
        public string type { get; set; }
        [Column]
        public string transUid { get; set; }
        [Column]
        public string data { get; set; }
        [Column]
        public int? pid { get; set; }
        [Column]
        public int? dataId { get; set; }
        [Column]
        public string result { get; set; }
        [Column]
        public DateTime? createdAt { get; set; }
        [Column]
        public DateTime? handledAt { get; set; }

    }
}