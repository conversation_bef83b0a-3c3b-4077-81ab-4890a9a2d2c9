using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Data;
using System.Linq;
using System.Web.Mvc;
using System.Data.Linq;
using System.ComponentModel.DataAnnotations.Schema;

using cmdiapp.n.core.Domain.ViewModels;
using cmdiapp.n.core.Areas.crm.Domain.Data;

namespace cmdiapp.n.core.Areas.crm.Domain.Models
{
    [Table("address")]
    public class Address : _entityBase_crm, iItemType
    {
        [Column]
        [Key]
        public int ADDRESSID { get; set; }

        [Column]
        public int PID { get; set; }

        [Column]
        public short ADDRTYPEID { get; set; }

        [Column]
        public Nullable<short> COUNTRYID { get; set; }

        [Column]
        public string STREET { get; set; }

        [Column]
        public string ADDR1 { get; set; }

        [Column]
        public string ADDR2 { get; set; }

        [Column]
        public string CITY { get; set; }

        //[ForeignKey("County"), 
        [Column(Order = 0)]
        public string STATE { get; set; }

        [Column]
        public string ZIP { get; set; }

        [Column]
        public string PLUS4 { get; set; }

        //[ForeignKey("County")]
        [Column(Order = 1)]
        public string COUNTY { get; set; }

        [Column]
        public string CRRT { get; set; }

        [Column]
        public string CDCODE { get; set; }

        [Column]
        public string SDCODE { get; set; }

        [Column]
        public string LDCODE { get; set; }

        [Column]
        public string COMMENT { get; set; }

        [Column]
        public Byte PRIME { get; set; }

        [Column]
        public Nullable<short> STRTMO { get; set; }

        [Column]
        public Nullable<short> ENDMO { get; set; }

        [Column]
        public string POSTNET { get; set; }

        [Column]
        public string ADDRCODE { get; set; }

        [Column]
        public decimal? LATITUDE { get; set; }

        [Column]
        public decimal? LONGITUDE { get; set; }

        [Column]
        public Nullable<int> REFADDRID { get; set; }

        [Column]
        public Nullable<System.DateTime> UPDATEDON { get; set; }

        [Column]
        public bool ADDRLOCK { get; set; }

        [Column("_updating_uid")]
        public Nullable<short> updating_uid { get; set; }

        //public virtual County County { get; set; }

        [Column]
        public bool BAD { get; set; }
    }

    [Table("lkCOUNTRY")]
    public class lkCOUNTRY : _entityBase_crm
    {
        [Column]
        [Key]
        public short COUNTRYID { get; set; }
        [Column]
        public string COUNTRY { get; set; }
        [Column]
        public string DESCRIP { get; set; }
        [Column]
        public byte DEFFLAG { get; set; }
        [Column]
        public byte SYSTEM { get; set; }
    }
}
