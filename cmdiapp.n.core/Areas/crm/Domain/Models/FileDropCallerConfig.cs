﻿using DataFileEnblobulator.Core;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.Serialization;
using System.Web;

namespace cmdiapp.n.core.Areas.crm.Domain.Models
{
    /// <summary>
    /// Class that represents configuration values
    /// for file drop functions for data integrators, i.e., <see cref="datax.Domain.Models.Caller"/>s
    /// </summary>
    [DataContract]
    public class FileDropCallerConfig
    {
        /// <summary>
        /// Gets or sets <see cref="datax.Domain.Models.Caller.Id"/>
        /// </summary>
        [DataMember(Name = "CallerId")]
        public int CallerId { get; set; }
        /// <summary>
        /// Gets or sets <see cref="datax.Domain.Models.Caller.callerKeyName"/>
        /// </summary>
        [DataMember(Name = "CallerKeyName")]
        public string CallerKeyName { get; set; }
        /// <summary>
        /// Gets or sets that maximum number of records that can be uploaded in
        /// a single file
        /// </summary>
        [DataMember(Name = "MaxRecords")]
        public int MaxRecords { get; set; }
        /// <summary>
        /// Gets or sets the Azure blob container name to send data to
        /// </summary>
        [DataMember(Name = "ContainerName")]
        public string ContainerName { get; set; }
        /// <summary>
        /// Gets or sets configuration for processing data file and producing
        /// correct output to be uploaded to blob
        /// </summary>
        [DataMember(Name = "IOConfiguration")]
        public BlobClientIOConfiguration IOConfiguration { get; set; }
    }
}