﻿using cmdiapp.n.core.Core;

namespace cmdiapp.n.core.Areas.crm.Core
{
    public class UserSessionWrapper : IUserSession
    {
        public string GetConfigVal(string key)
        {
            return session.userSession.GetConfigValue(key);
        }

        public void UpsertConfigVal(string key, string value)
        {
            session.upsert_ConfigVal(key, value, true);
        }

        public string GetCurrentUserId()
        {
            return session.userSession?.UserId;
        }

        public string GetCurrentUserName()
        {
            return session.userSession?.UserName;
        }

        public int GetCurrentProjectId()
        {
            return session.currentDomain_projectId;
        }

        public int? GetAccessSessionId()
        {
            return session.userSession?.access_sessionsId;
        }
    }
}