﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using cmdiapp.n.core.Areas.crm.Domain.Models;

namespace cmdiapp.n.core.Domain.ViewModels
{
    public class vwDataEntry
    {
        public dtBATCH dtBATCH { get; set; }
        public dtBatchExtended dtBatchExtended { get; set; }
        public IEnumerable<dmFUNDForDataEntry> dmFUND { get; set; } // Fund Code
        public IEnumerable<SOURCE> SOURCE { get; set; } // Source Code
        public IEnumerable<lkMONYTYPEForDataEntry> lkMONYTYPE { get; set; } // $ Type
        public IEnumerable<lkPEOTYPEForDataEntry> lkPEOTYPE { get; set; } //Donor Type
        public IEnumerable<lkPEOCODEForDataEntry> lkPEOCODE { get; set; } // People Code
        public IEnumerable<lkCCMonthOptions> MonthOptions { get; set; } // CC Month Options
        public IEnumerable<lkCCYearOptions> YearOptions { get; set; } // CC Year Options
        //For People Donation Summary Info
        public IEnumerable<PeopleSummary> PeopleSummaryDetails { get; set; } // People record Donation History Info
        public string IsRecdOnDate { get; set; }
        public string IsRecurringOption { get; set; }
        public DateTime? RecurringDate { get; set; }
        public string AccessElement { get; set; }
   }
}