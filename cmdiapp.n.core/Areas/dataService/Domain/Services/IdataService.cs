﻿using System.Collections.Generic;
using System.Web.Mvc;

using cmdiapp.n.core.Areas.dataService.Domain.Models;

namespace cmdiapp.n.core.Areas.dataService.Domain.Services
{
    public interface IdataService
    {
        string same_nickNames(string firstName);
        cassAddress verifyAddress(cassAddress addr);
        rectangularArea get_rectangularArea(decimal loc_latitude, decimal loc_longitude, int miles);
    }
}
