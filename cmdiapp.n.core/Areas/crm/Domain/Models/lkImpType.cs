﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Data;
using System.Linq;
using System.Web.Mvc;
using System.Data.Linq;
using System.ComponentModel.DataAnnotations.Schema;
using cmdiapp.n.core.Domain.ViewModels;
using cmdiapp.n.core.Areas.crm.Domain.Data;

namespace cmdiapp.n.core.Areas.crm.Domain.Models
{
    [Table("lkIMPTYPE")] 
    public class lkIMPTYPE : _entityBase_crm, iItemType
    {
        [Column]
        [Key]
        public int IMPID { get; set; }

        [Column]
        public string IMPTYPE { get; set; }

        [Column]
        public bool? IsActive { get; set; }

        [Column]
        public int IMPTYPEID { get; set; }

    }
}