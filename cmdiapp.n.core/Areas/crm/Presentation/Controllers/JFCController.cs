﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.Mvc;

using System.Data;

using AutoMapper;

using cmdiapp.n.core.Core;
using cmdiapp.n.core.Core.App_Start;
using cmdiapp.n.core.Domain.Models;
using cmdiapp.n.core.Domain.ViewModels;
using cmdiapp.n.core.Areas.crm.Core;
using cmdiapp.n.core.Areas.crm.Domain.Data;
using cmdiapp.n.core.Areas.crm.Domain.Models;
using cmdiapp.n.core.Areas.crm.Domain.ViewModels;
using cmdiapp.n.core.Areas.crm.Domain.Repositories;
using cmdiapp.n.core.Areas.crm.Domain.Services;
using cmdiapp.n.core.Library;
using cmdiapp.n.core.Presentation.Controllers;
using cmdiapp.n.core.Areas.dataService.Domain.Data;
using cmdiapp.n.core.Areas.dataService.Domain.Services;
using cmdiapp.n.core.Domain.Repositories;
using cmdiapp.n.core.Domain.Services;

using Ninject;
using Ninject.Web.Mvc;
using System.Collections.ObjectModel;
using System.Xml.Linq;



namespace cmdiapp.n.core.Areas.crm.Presentation.Controllers
{
    public class JFCController : Controller
    {
        private readonly I_entity_crm _entity;
        private readonly IjfcService _jfcService;
        private IdataService _dataService;

        private cmdiapp.n.core.Areas.crm.Domain.Repositories.I__DBFactory _dbFactory;

        public JFCController(cmdiapp.n.core.Areas.crm.Domain.Repositories.I__DBFactory dbFactory)
        {
            _entity = I_entityManager_crm.getEntity();
            _dataService = I_entityManager_ds.getService();
            _jfcService = NinjectMVC.kernel.Get<IjfcService>();

            _dbFactory = dbFactory;
        }       

        [aElementAuthorize(AccessElement = "/crm/JFC/ManPage", AccessLevel = "v")]
        public ActionResult Index()
        {
            return View();
        }

        [aElementAuthorize(AccessElement = "cmdiapp.dms.distributions", AccessLevel = "v")]
        public ActionResult Distributions(string udlo)
        {
            return View(@"DistributionQuickSearch");
        }


        //Main Import Page for JFC Vett
        public ActionResult Vetting(string title)
        {
            pageInfo _pageInfo = new pageInfo() { title = title };
            _pageInfo.LayoutPage = crmConstants.DEFAULT_LAYOUT_w_HEADER_FOOTER;
            //return the partial view
            return PartialView(@"Vetting", _pageInfo);
        }


        [aElementAuthorize(AccessElement = "cmdiapp.dms.distributions", AccessLevel = "e")]
        public ActionResult DistributionOpen(string udlo, int? id)
        {
            pageInfo _pageInfo = new pageInfo();
            if (udlo == "y")
                _pageInfo.LayoutPage = crmConstants.DEFAULT_LAYOUT_BLANK;
            else
                _pageInfo.LayoutPage = "";

            _pageInfo.pageEntityz_uniqueId = id.ToString();
            return View(@"Distribution", _pageInfo);
        }

        [aElementAuthorize(AccessElement = "cmdiapp.dms.distributions", AccessLevel = "e")]
        public ActionResult DistributionEdit(string udlo, int? id)
        {
            pageInfo _pageInfo = new pageInfo();
            if (udlo == "y")
                _pageInfo.LayoutPage = crmConstants.DEFAULT_LAYOUT_BLANK;
            else
                _pageInfo.LayoutPage = "";

            _pageInfo.title = "Edit a Distribution Record";
            _pageInfo.title_short = "Edit";
            _pageInfo.pageEntityz_uniqueId = id.ToString();
            return View(@"DistributionEdit", _pageInfo);
        }
        
        [aElementAuthorize(AccessElement = "cmdiapp.dms.distributions", AccessLevel = "v")]
        public ActionResult ContributionDetails(string udlo, int? id)
        {
            pageInfo _pageInfo = new pageInfo();
            if (udlo == "y")
                _pageInfo.LayoutPage = crmConstants.DEFAULT_LAYOUT_BLANK;
            else
                _pageInfo.LayoutPage = "";

            _pageInfo.title = "Contribution Details";
            _pageInfo.title_short = "Edit";
            _pageInfo.pageEntityz_uniqueId = id.ToString();
            return View(@"ContributionDetails", _pageInfo);
        }
       
    }
}
