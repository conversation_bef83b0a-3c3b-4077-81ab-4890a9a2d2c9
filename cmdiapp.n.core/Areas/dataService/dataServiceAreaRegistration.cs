﻿using System.Web.Mvc;
using System.Web.Http;

namespace cmdiapp.n.core.Areas.dataService
{
    public class dataServiceAreaRegistration : AreaRegistration
    {
        public override string AreaName
        {
            get
            {
                return "dataService";
            }
        }

        public override void RegisterArea(AreaRegistrationContext context)
        {
            context.Routes.MapMvcAttributeRoutes();
        }
    }
}
