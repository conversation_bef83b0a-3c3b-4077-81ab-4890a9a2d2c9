﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Xml.Linq;
using System.Data;
using System.Data.SqlClient;
using System.Xml.Serialization;

using AutoMapper;
using System.Configuration;

using cmdiapp.n.core.Domain.Data;
using cmdiapp.n.core.Domain.Models;
using cmdiapp.n.core.Core;
using cmdiapp.n.core.Library;
using cmdiapp.n.core.Areas.query.Domain.Models;
using cmdiapp.n.core.Areas.dataService.Domain.Services;

namespace cmdiapp.n.core.Areas.query
{
    public static class q_library
    {

        #region [ func (List<qOperator>)get_lookupOperators ]
        public static List<qOperator> get_lookupOperators(IEnumerable<XElement> qXMLs, string fieldCaption)
        {
            try
            {
                var q = from a in qXMLs
                        select new qOperator()
                        {
                            symbol = (a.Element("s") != null ? Convert.ToString(a.Element("s").Value) : ""),
                            label = (a.Element("r") != null ? Convert.ToString(a.Element("r").Value) : "")
                        };

                List<qOperator> _qOperators = q.ToList();
                if (_qOperators != null && _qOperators.Count>0)
                    _qOperators.Insert(0, new qOperator() { symbol = "", label = string.IsNullOrEmpty(fieldCaption) ? "(Choose)" : "Choose a " + fieldCaption });

                return _qOperators;
            }
            catch
            {
                return null;
            }
        }
        #endregion 
        
        #region [ func (List<q_whereItem_S>)get_q_whereItems ]
        public static List<q_whereItem_S> get_q_whereItems(IEnumerable<XElement> qXMLs, int appId, int projectId)
        {

            try
            {
                var q = from a in qXMLs
                        // [Allow/Disallow Filterings based on CRM Configs]
                        where (
                               (a.Elements("cq").Count() == 0 && a.Element("c").Attribute("type") == null)
                            || (a.Element("cq") != null
                                &&  a.Element("cq").Attribute("type") != null && a.Element("cq").Attribute("type").Value == "config"
                                && (a.Element("cq").Attribute("negate") == null || a.Element("cq").Attribute("negate").Value != "Y")
                                && (applica.getConfigVal(a.Element("cq").Attribute("key").Value, appId, projectId) == a.Element("cq").Attribute("value").Value))

                            || (a.Element("cq") != null
                                && a.Element("cq").Attribute("type") != null && a.Element("cq").Attribute("type").Value == "config"
                                && a.Element("cq").Attribute("negate") != null && a.Element("cq").Attribute("negate").Value == "Y"
                                && (applica.getConfigVal(a.Element("cq").Attribute("key").Value) == "" || applica.getConfigVal(a.Element("cq").Attribute("key").Value) != a.Element("cq").Attribute("value").Value))
                            || (a.Element("c").Attribute("type") != null 
                                && a.Element("c").Attribute("type").Value == "config"
                                && !string.IsNullOrEmpty(applica.getConfigVal(a.Element("c").Attribute("key").Value)))
                        )
                        select new q_whereItem_S()
                        {
                            id = (a.Element("i") != null ? Convert.ToString(a.Element("i").Value) : ""),
                            groupbyHaving = (a.Attribute("groupbyHaving") != null && Convert.ToString(a.Attribute("groupbyHaving").Value) == "Y" ? true : false),
                            fieldType = (a.Element("t") != null ? Convert.ToString(a.Element("t").Value) : ""),
                            fieldCaption = (a.Element("c").Attribute("type") != null && a.Element("c").Attribute("type").Value == "config" && !string.IsNullOrEmpty(applica.getConfigVal(a.Element("c").Attribute("key").Value)) ? applica.getConfigVal(a.Element("c").Attribute("key").Value) : (a.Element("c") != null ? Convert.ToString(a.Element("c").Value) : "")),
                            fieldCaptionDetails = (a.Element("d") != null ? Convert.ToString(a.Element("d").Value) : ""),
                            fieldName = (a.Element("n") != null ? Convert.ToString(a.Element("n").Value) : ""),
                            default_fieldValue = (a.Element("v") != null ? Convert.ToString(a.Element("v").Value) : ""),
                            default_operatorIdx = (a.Element("o") != null ? Convert.ToInt32(a.Element("o").Value) : 0),
                            lOperators = get_lookupOperators(a.Descendants("l").Descendants("i"), (a.Element("c").Attribute("type") != null && a.Element("c").Attribute("type").Value == "config" && !string.IsNullOrEmpty(applica.getConfigVal(a.Element("c").Attribute("key").Value)) ? applica.getConfigVal(a.Element("c").Attribute("key").Value) : (a.Element("c") != null ? Convert.ToString(a.Element("c").Value) : "")))
                        };


                return q.ToList();
            }
            catch (System.Exception e)
            {
                return null;
            }
        }
        #endregion 

        #region [ func (List<q_whereItem_panel_S)get_q_whereItem_panels ]
        public static List<q_whereItem_panel_S> get_q_whereItem_panel(IEnumerable<XElement> qXMLs, int appId, int projectId, int userGroupId)
        {
            try
            {
                var q = from a in qXMLs
                        where (a.Attribute("disabled") == null)
                        select new q_whereItem_panel_S()
                        {
                            name = (a.Attribute("name") != null ? Convert.ToString(a.Attribute("name").Value) : ""),
                            caption = (a.Attribute("caption") != null ? Convert.ToString(a.Attribute("caption").Value) : ""),
                            groupBy = (a.Attribute("groupBy") != null && Convert.ToString(a.Attribute("groupBy").Value) == "Y" ? true : false),
                            sq_uniqId = (a.Element("sq_uniqId") != null ? Convert.ToString(a.Element("sq_uniqId").Value) : ""),
                            sq_from = (a.Element("sq_from") != null ? Convert.ToString(a.Element("sq_from").Value) : ""),
                            sq_groupBy = (a.Element("sq_groupBy") != null ? Convert.ToString(a.Element("sq_groupBy").Value) : ""),

                            qItems = get_q_whereItems(a.Descendants("q"), appId, projectId),

                            subquery = (a.Attribute("subquery") != null && Convert.ToString(a.Attribute("subquery").Value)=="Y" ? true : false),
                            showEXCL = (a.Attribute("showEXCL") != null && Convert.ToString(a.Attribute("showEXCL").Value) == "Y" ? true : false),
                            showOR = (a.Attribute("showOR") != null && Convert.ToString(a.Attribute("showOR").Value) == "Y" ? true : false),
                            forceSuppress = (a.Attribute("forceSuppress") != null && Convert.ToString(a.Attribute("forceSuppress").Value) == "Y" ? true : false),
                            andSuppress = (a.Attribute("andSuppress") != null && Convert.ToString(a.Attribute("andSuppress").Value) == "Y" ? true : false),
                        };

                List<q_whereItem_panel_S> qPanels = q.ToList();
                foreach (q_whereItem_panel_S item in qPanels)
                    item.qItems = item.qItems.Select(a => 
                        { 
                            a.parentPanelName = item.name; 
                            a.parentPanel = item;

                            // Query Restriction
                            a.locked = (applica.qRestrictions.Count(b => b.userGroupId==userGroupId && b.fieldName == a.fieldName) > 0);
                            a.default_operatorIdx = (applica.qRestrictions.Count(b => b.userGroupId == userGroupId && b.fieldName == a.fieldName) > 0 ? applica.qRestrictions.FirstOrDefault(b => b.userGroupId == userGroupId && b.fieldName == a.fieldName).operatorIdx : a.default_operatorIdx);
                            a.default_fieldValue = (applica.qRestrictions.Count(b => b.userGroupId == userGroupId && b.fieldName == a.fieldName) > 0 ? applica.qRestrictions.FirstOrDefault(b => b.userGroupId == userGroupId && b.fieldName == a.fieldName).value : a.default_fieldValue);

                            return a; 
                    
                        }).ToList();

                return qPanels;
            }
            catch
            {
                return null;
            }
        }
        #endregion

        #region [ func (List<q_sortOption_S)get_q_sortOptions_S ]
        public static List<q_sortOption_S> get_q_sortOptions_S(IEnumerable<XElement> qXMLs, int appId, int projectId, int userGroupId)
        {
            try
            {
                #region [ Check and use if there is a valid custom sort option for the client ]
                XElement xelement_ = qXMLs.Descendants("cq").FirstOrDefault();
                if (xelement_ != null)
                {
                    string configKey_ = xelement_.Attribute("key").Value;
                    if (!string.IsNullOrEmpty(configKey_))
                    {
                        string configVal_ = applica.getConfigVal(configKey_, appId, projectId);
                        if (!string.IsNullOrEmpty(configVal_))
                        {
                            try
                            {
                                XElement sortOptionsXml = XElement.Load(System.Xml.XmlReader.Create(new System.IO.StringReader(configVal_)));
                                var qc = from a in sortOptionsXml.Descendants("qsoi")
                                        select new q_sortOption_S()
                                        {
                                            id = (a.Element("i") != null ? Convert.ToString(a.Element("i").Value) : ""),
                                            caption = (a.Element("c") != null ? Convert.ToString(a.Element("c").Value) : ""),
                                            name = (a.Element("n") != null ? Convert.ToString(a.Element("n").Value) : ""),
                                            order = (a.Element("o") != null ? Convert.ToString(a.Element("o").Value) : ""),
                                        };
                                return qc.ToList();
                            }
                            catch
                            {
                                // Continue
                            }
                        }
                    }

                }
                #endregion

                // Default
                var q = from a in qXMLs.Descendants("qsoi")
                    select new q_sortOption_S()
                    {
                        id = (a.Element("i") != null ? Convert.ToString(a.Element("i").Value) : ""),
                        caption = (a.Element("c") != null ? Convert.ToString(a.Element("c").Value) : ""),
                        name = (a.Element("n") != null ? Convert.ToString(a.Element("n").Value) : ""),
                        order = (a.Element("o") != null ? Convert.ToString(a.Element("o").Value) : ""),
                    };

                return q.ToList();
            }
            catch
            {
                return null;
            }
        }
        #endregion

        private static string get_lookupData(string qDefXml_S, string dbConnString)
        {
            #region [ to be backward compatible w/ Crimson 1]
            qDefXml_S = qDefXml_S.Replace("<<<lookup","<lookup");
            qDefXml_S = qDefXml_S.Replace("</lookup>>>","</lookup>");
            #endregion

            while (qDefXml_S.IndexOf("<lookup") >= 0 && qDefXml_S.IndexOf("</lookup>") >= 0)
            {
                string _lookupXML = qDefXml_S.Substring(qDefXml_S.IndexOf("<lookup"), qDefXml_S.IndexOf("</lookup>") - qDefXml_S.IndexOf("<lookup") + 9);
                XElement _lookupXML_e = XElement.Parse(_lookupXML);

                string _lookupXML_sql = (string)(_lookupXML_e.Value);

                #region [[ Only if Gateway database call is needed ]]
                
                if (_lookupXML_sql.Contains("<<<GATEWAY>>>"))
                {
                    String s = _lookupXML_sql;
                    s = s.Replace("<<<GATEWAY>>>", " ").Trim();
                    //For parameters....
                    s = s + " " + session.currentDomain_projectId.ToString() + "," + (session.userSession.is_sysAdmin ? "1" : "0");
                    //assign back
                    _lookupXML_sql = s;
                    //Also change the connection string..
                    dbConnString = ConfigurationManager.ConnectionStrings["kernelContext"].ConnectionString;
                }

                if (_lookupXML_sql.Contains("<<<GATEWAYNOPARA>>>"))
                {
                    String s = _lookupXML_sql;
                    s = s.Replace("<<<GATEWAYNOPARA>>>", " ").Trim();
                    //assign back
                    _lookupXML_sql = s;
                    //Also change the connection string..
                    dbConnString = ConfigurationManager.ConnectionStrings["kernelContext"].ConnectionString;
                }

                #endregion

                string _lookupXML_name = (string)(_lookupXML_e.Attribute("id"));

                string _lookupResult = "";

                System.Data.DataSet sqlSelect = get_dataset_w_sql__single(dbConnString, _lookupXML_sql, "result", 0, 0);
                if (sqlSelect != null && sqlSelect.Tables.Count > 0 && sqlSelect.Tables[0].Rows.Count > 0)
                {
                    // If the xml string data is bigger than 2,033 bytes, it returns in multiple rows and need to concatenate  
                    // Old code - _lookupResult = sqlSelect.Tables[0].Rows[0][0].ToString();
                    for (int rowcount = 0; rowcount < sqlSelect.Tables[0].Rows.Count; rowcount++)
                    {
                        _lookupResult = _lookupResult + sqlSelect.Tables[0].Rows[rowcount][0].ToString();
                    }

                }

                qDefXml_S = qDefXml_S.Replace(_lookupXML, _lookupResult);
            }
            return qDefXml_S;
        }

        #region [ func (q_def_S) get_q_def ]
        public class qDefXmlParam
        {
            public string qDefXml_path { get; set; }
            public string dbConnString { get; set; }
            public int appId { get; set; }
            public int projectId { get; set; }
            public int userGroupId { get; set; }
        }

        public static q_def_S get_q_def(object _qDefXmlParam)
        {
            qDefXmlParam _param = (qDefXmlParam)_qDefXmlParam;
            string qDefXml_S = System.IO.File.ReadAllText(_param.qDefXml_path.ToString());

            qDefXml_S = get_lookupData(qDefXml_S, _param.dbConnString);

            XDocument qDefXml = XDocument.Parse(qDefXml_S);

            var q = from a in qDefXml.Descendants("qDef")
                    select new q_def_S()
                    {
                        name = (a.Attribute("name") != null ? Convert.ToString(a.Attribute("name").Value) : ""),                        
                        caption = (a.Attribute("caption") != null ? Convert.ToString(a.Attribute("caption").Value) : ""),
                        aElementIdentifier = (a.Element("aElementIdentifier") != null ? Convert.ToString(a.Element("aElementIdentifier").Value) : ""),

                        showExportBtn = 
                                    ( a.Element("showExportBtn") != null 
                                      && Convert.ToString(a.Element("showExportBtn").Value) == "Y"
                                      && (a.Element("aElementIdentifier") == null || Convert.ToString(a.Element("aElementIdentifier").Value) == "" || applica.can_i_do(_param.userGroupId, Convert.ToString(a.Element("aElementIdentifier").Value), "e"))  // Only if "be able to Edit" on "the query module"
                                        ? true
                                        : false                        
                                    ),
                        searchBtnCaption =
                                    (a.Element("searchBtnCaption") != null
                                        ? Convert.ToString(a.Element("searchBtnCaption").Value)
                                        : "Search"
                                    ),
                        customBtnTop =
                                    (a.Element("customBtnTop") != null
                                      && (a.Element("aElementIdentifier") == null || Convert.ToString(a.Element("aElementIdentifier").Value) == "" || applica.can_i_do(_param.userGroupId, Convert.ToString(a.Element("aElementIdentifier").Value), "e"))
                                        ? Convert.ToString(a.Element("customBtnTop").Value)
                                        : ""
                                    ),
                        customBtnBottom =
                                    (a.Element("customBtnBottom") != null
                                      && (a.Element("aElementIdentifier") == null || Convert.ToString(a.Element("aElementIdentifier").Value) == "" || applica.can_i_do(_param.userGroupId, Convert.ToString(a.Element("aElementIdentifier").Value), "e"))
                                        ? Convert.ToString(a.Element("customBtnBottom").Value)
                                        : ""                        
                                    ),
                        customBtnBottom2 =
                                    (a.Element("customBtnBottom2") != null
                                      && (a.Element("aElementIdentifier") == null || Convert.ToString(a.Element("aElementIdentifier").Value) == "" || applica.can_i_do(_param.userGroupId, Convert.ToString(a.Element("aElementIdentifier").Value), "e"))
                                        ? Convert.ToString(a.Element("customBtnBottom2").Value)
                                        : ""
                                    ),
                        customBtnBottom1 =
                                    (a.Element("customBtnBottom1") != null
                                      && (a.Element("aElementIdentifier") == null || Convert.ToString(a.Element("aElementIdentifier").Value) == "" || applica.can_i_do(_param.userGroupId, Convert.ToString(a.Element("aElementIdentifier").Value), "e"))
                                        ? Convert.ToString(a.Element("customBtnBottom1").Value)
                                        : ""
                                    ),
                        showQuickTextSearch =
                                    (a.Element("showQuickTextSearch") != null
                                      && Convert.ToString(a.Element("showQuickTextSearch").Value) == "Y"
                                        ? true
                                        : false                        
                                    ),

                        sq_uniqId = (a.Element("sq_uniqId") != null ? Convert.ToString(a.Element("sq_uniqId").Value) : ""),
                        sq_fields = (a.Element("sq_fields") != null ? Convert.ToString(a.Element("sq_fields").Value) : ""),
                        sq_fieldsV = (a.Element("sq_fieldsV") != null ? Convert.ToString(a.Element("sq_fieldsV").Value) : ""),
                        sq_from = (a.Element("sq_from") != null ? Convert.ToString(a.Element("sq_from").Value) : ""),
                        sq_defaultWhere = (a.Element("sq_defaultWhere") != null ? Convert.ToString(a.Element("sq_defaultWhere").Value) : ""),

                        qPanels = get_q_whereItem_panel(a.Descendants("qs"), _param.appId, _param.projectId, _param.userGroupId),
                        qSortOptions = get_q_sortOptions_S(a.Descendants("qso"), _param.appId, _param.projectId, _param.userGroupId),

                        /* 
                                            mid = (a.Element("i") != null ? Convert.ToInt32(a.Element("i").Value) : (int?)null),
                                            giftDate = (a.Element("t") != null ? Convert.ToDateTime(a.Element("t").Value) : (DateTime?)null),
                                            giftAmount = (a.Element("a") != null ? Convert.ToDecimal(a.Element("a").Value) : (decimal?)null),
                        */
                    };

            return q.FirstOrDefault();
        }
        #endregion

        #region [ func (q_def_C) clone_q_def_S ]
        public static q_def_C clone_q_def_S(object o_def)
        {
            q_def_S _def = o_def as q_def_S;

            q_def_C _def_C = new q_def_C() { name = _def.name, caption = _def.caption, 
                        showExportBtn = _def.showExportBtn, 
                        searchBtnCaption = _def.searchBtnCaption,
                        customBtnTop = _def.customBtnTop,
                        customBtnBottom = _def.customBtnBottom,
                        customBtnBottom2 = _def.customBtnBottom2,
                        customBtnBottom1 = _def.customBtnBottom1,
                        showQuickTextSearch = _def.showQuickTextSearch, 
                        qPanels = new List<q_whereItem_panel_C>(),
                        qSortOptions = new List<q_sortOption>(),
                        accessRight = session.what_can_i_do(_def.aElementIdentifier)
            };

            Mapper.CreateMap<q_whereItem_panel_S, q_whereItem_panel>();
            Mapper.CreateMap<q_whereItem_panel, q_whereItem_panel_C>();

            Mapper.CreateMap<q_whereItem_S, q_whereItem>();

            foreach (q_whereItem_panel_S panel in _def.qPanels)
            {
                q_whereItem_panel _panel = new q_whereItem_panel();
                q_whereItem_panel_C _panel_C = new q_whereItem_panel_C();
                Mapper.Map<q_whereItem_panel_S, q_whereItem_panel>(panel, _panel);
                Mapper.Map<q_whereItem_panel, q_whereItem_panel_C>(_panel, _panel_C);

                _panel_C.qItems = new List<q_whereItem>();

                foreach (q_whereItem_S item in panel.qItems)
                {
                    q_whereItem _item = new q_whereItem();
                    Mapper.Map<q_whereItem_S, q_whereItem>(item, _item);

                    _item.lOperators = new List<qOperator>();

                    foreach (qOperator loperator in item.lOperators)
                    {
                        _item.lOperators.Add(new qOperator(){symbol=loperator.symbol, label=loperator.label});    
                    }

                    _panel_C.qItems.Add(_item);
                }

                _def_C.qPanels.Add(_panel_C);
            }

            if (_def.qSortOptions != null)
            {
                Mapper.CreateMap<q_sortOption_S, q_sortOption>();

                foreach (q_sortOption_S sortOption in _def.qSortOptions)
                {
                    q_sortOption _item = new q_sortOption();
                    Mapper.Map<q_sortOption_S, q_sortOption>(sortOption, _item);
                    _def_C.qSortOptions.Add(_item);
                }
            }
            return _def_C;
        }
        #endregion

        #region [ func (q_def_S) get_q_def_fr_cache ]
        public static q_def_S get_q_def_fr_cache(string defName)
        {
            string name = defName.ToString().Replace('_', '/');
            string qDef_filePath = Library.util.fullPath(String.Format(@"/Areas/query/resource/{0}.xml", name));

            if (System.IO.File.Exists(qDef_filePath))
            {
                q_def_S _def = session.get_a_cache(session.get_cacheKey_for_appId_projectId_userGroupId("qDef-S-" + name),
                                                    session.appCacheDuration,
                                                    null,
                                                    q_library.get_q_def,
                                                    new qDefXmlParam() { qDefXml_path = qDef_filePath, dbConnString = session.currentDomain_project._connectionString(), appId=session.currentDomain_project.appId, projectId=session.currentDomain_projectId, userGroupId=session.currentDomainAuthorizedProject_userGroupId }) as q_def_S;
               
                return _def;
            }
            else
                return null;

        }
        #endregion

        #region [ func (q_def_S) get_q_def_fr_cache for a specific project]
        public static q_def_S get_q_def_fr_cache(string defName, string dbConnString, int appId, int projectId, int userGroupId)
        {
            string name = defName.ToString().Replace('_', '/');
            string qDef_filePath = Library.util.fullPath(String.Format(@"/Areas/query/resource/{0}.xml", name));

            
            if (System.IO.File.Exists(qDef_filePath))
            {
                q_def_S _def = session.get_a_cache(session.get_cacheKey_for_appId_projectId_userGroupId("qDef-S-" + name, appId, projectId, userGroupId),
                                                    session.appCacheDuration,
                                                    null,
                                                    q_library.get_q_def,
                                                    new qDefXmlParam() { qDefXml_path = qDef_filePath, dbConnString = dbConnString, appId=appId, projectId=projectId, userGroupId=userGroupId }) as q_def_S;

                return _def;
            }
            else
                return null;

        }
        #endregion

        #region [ (private.func) get_qItems_fr_report ]
        private static q_whereItem_panel_S get_qItems_fr_report(ref string p_sql)
        {
            q_whereItem_panel_S _qPanel = new q_whereItem_panel_S()
            {
                name = "Run Report",
                caption = "Report Filters",
                qItems = new List<q_whereItem_S>()
            };

            while (p_sql.IndexOf("<<") >= 0 && p_sql.IndexOf(">>") >= 0)
            {
                string _con = p_sql.Substring(p_sql.IndexOf("<<") + 2, p_sql.IndexOf(">>") - p_sql.IndexOf("<<") - 1);

                XDocument _xmlDoc = XDocument.Parse(_con);
                var q = from a in _xmlDoc.Elements("q")
                        select new q_whereItem_S()
                        {
                            id = (a.Element("c") != null ? "<+-"+Convert.ToString(a.Element("c").Value)+"-+>" : ""),
                            fieldType = (a.Element("t") != null ? Convert.ToString(a.Element("t").Value) : ""),
                            fieldCaption = (a.Element("c") != null ? Convert.ToString(a.Element("c").Value) : ""),
                            fieldCaptionDetails = (a.Element("d") != null ? Convert.ToString(a.Element("d").Value) : ""),
                            fieldName = (a.Element("n") != null ? Convert.ToString(a.Element("n").Value) : ""),
                            default_fieldValue = (a.Element("v") != null ? Convert.ToString(a.Element("v").Value) : ""),
                            default_operatorIdx = (a.Element("o") != null ? Convert.ToInt32(a.Element("o").Value) : 0),
                            lOperators = q_library.get_lookupOperators(a.Descendants("l").Descendants("i"), (a.Element("c") != null ? Convert.ToString(a.Element("c").Value) : ""))
                        };

                q_whereItem_S _qItem = q.FirstOrDefault();

                _qItem.locked = (session.myQRestrictions.Count(b => b.fieldName == _qItem.fieldName) > 0);
                _qItem.default_operatorIdx = (session.myQRestrictions.Count(b => b.fieldName == _qItem.fieldName) > 0 ? session.myQRestrictions.FirstOrDefault(b => b.fieldName == _qItem.fieldName).operatorIdx : _qItem.default_operatorIdx);
                _qItem.default_fieldValue = (session.myQRestrictions.Count(b => b.fieldName == _qItem.fieldName) > 0 ? session.myQRestrictions.FirstOrDefault(b => b.fieldName == _qItem.fieldName).value : _qItem.default_fieldValue);

                _qPanel.qItems.Add(_qItem);

                p_sql = p_sql.Replace("<<" + _con + ">>", "<+-" + _qItem.fieldCaption + "-+>");
            }
            return _qPanel;
        }
        #endregion

        #region [ (private.func) get_qItems_fr_report_oldVersion ]
        private static q_whereItem_panel_S get_qItems_fr_report_oldVersion(ref string p_sql)
        {
            q_whereItem_panel_S _qPanel = new q_whereItem_panel_S()
            {
                name = "Run Report",
                caption = "Report Filters",
                qItems = new List<q_whereItem_S>()
            };

            while (p_sql.IndexOf("<<") >= 0 && p_sql.IndexOf(">>") >= 0)
            {
                string _con = p_sql.Substring(p_sql.IndexOf("<<") + 2, p_sql.IndexOf(">>") - p_sql.IndexOf("<<") - 2);

                string _fieldType = _con.Substring(_con.IndexOf("^") + 1, _con.IndexOf("@") - _con.IndexOf("^") - 1).ToLower();
                _fieldType = (_fieldType == "c" ? "s" : _fieldType);

                q_whereItem_S _qItem = new q_whereItem_S()
                {
                    id = "<+-" + _con.Substring(_con.IndexOf(">") + 1, _con.IndexOf("^") - _con.IndexOf(">") - 1) + "-+>",
                    fieldType = _fieldType,
                    fieldCaption = _con.Substring(_con.IndexOf(">") + 1, _con.IndexOf("^") - _con.IndexOf(">") - 1),
                    fieldName = _con.Substring(_con.IndexOf("[") + 1, _con.IndexOf(">") - _con.IndexOf("[") - 1),
                    default_fieldValue = "",
                    default_operatorIdx = 0,
                    lOperators = new List<qOperator>()
                };

                _qItem.locked = (session.myQRestrictions.Count(b => b.fieldName == _qItem.fieldName) > 0);
                _qItem.default_operatorIdx = (session.myQRestrictions.Count(b => b.fieldName == _qItem.fieldName) > 0 ? session.myQRestrictions.FirstOrDefault(b => b.fieldName == _qItem.fieldName).operatorIdx : _qItem.default_operatorIdx);
                _qItem.default_fieldValue = (session.myQRestrictions.Count(b => b.fieldName == _qItem.fieldName) > 0 ? session.myQRestrictions.FirstOrDefault(b => b.fieldName == _qItem.fieldName).value : _qItem.default_fieldValue);

                _qPanel.qItems.Add(_qItem);

                p_sql = p_sql.Replace("<<" + _con + ">>", "<+-" + _qItem.fieldCaption + "-+>");
            }
            return _qPanel;
        }
        #endregion

        #region [ func (q_def_S) get_q_def_report ]
        public static q_def_S get_q_def_report(object reportId_o)
        {
            int reportId = (int)reportId_o;

            I_entity _entity = I_entityManager.getEntity();
            report _report = _entity.Single<report>(a => a.reportId == reportId);
            if (_report == null) return null;

            q_def_S _def = new q_def_S()
            {
                name = "report_search",
                caption = "Run Report",
                aElementIdentifier = "cmdiapp.dms.reports",
                showExportBtn = session.can_i_export(reportId),
                //searchBtnCaption = (_report.iconCode=="r" ? "Print" : "Run"),
                searchBtnCaption = "Run", // Update by Bhavesh : 7/21/2014 : https://www.pivotaltracker.com/s/projects/753361/stories/69262076
                customBtnTop = "",
                customBtnBottom = "",
                customBtnBottom2 = "",
                customBtnBottom1 = "",
                qPanels = new List<q_whereItem_panel_S>()
            };

            string _sql = _report.script;

            _sql = get_lookupData(_sql, session.currentDomain_project._connectionString());

            // Collect Query Filter Definition
            if (_sql.IndexOf("<<<") >= 0 && _sql.IndexOf(">>>") >= 0)
                _def.qPanels.Add(get_qItems_fr_report(ref _sql));

            // Need to collect Query Conditions (Old: eg. <<[DF.FLAG>Flag^C@10*!>cccccccccc;0; ]>> )
            else if (_sql.IndexOf("<<[") >= 0 && _sql.IndexOf("]>>") >= 0)
                _def.qPanels.Add(get_qItems_fr_report_oldVersion(ref _sql));

            foreach (q_whereItem_panel_S item in _def.qPanels)
                item.qItems = item.qItems.Select(a =>
                {
                    a.parentPanelName = item.name;
                    a.parentPanel = item;

                    // Query Restriction
                    a.locked = (session.myQRestrictions.Count(b => b.fieldName == a.fieldName) > 0);
                    a.default_operatorIdx = (session.myQRestrictions.Count(b => b.fieldName == a.fieldName) > 0 ? session.myQRestrictions.FirstOrDefault(b => b.fieldName == a.fieldName).operatorIdx : a.default_operatorIdx);
                    a.default_fieldValue = (session.myQRestrictions.Count(b => b.fieldName == a.fieldName) > 0 ? session.myQRestrictions.FirstOrDefault(b => b.fieldName == a.fieldName).value : a.default_fieldValue);

                    return a;

                }).ToList();

            _def.reportScript = _sql;
            _def.reportId = _report.reportId;
            _def.reportNo = _report.no;
            _def.reportName = _report.name;

            return _def;
        }
        #endregion

        #region [ func (q_def_S) get_q_def_fr_cache_report ]
        public static q_def_S get_q_def_fr_cache_report(int reportId)
        {

            q_def_S _def = session.get_a_cache(session.get_cacheKey_for_appId_projectId_userGroupId("qDef-S-report-" + reportId.ToString()),
                                                session.appCacheDuration,
                                                null,
                                                q_library.get_q_def_report,
                                                reportId) as q_def_S;

            return _def;
        }
        #endregion


        #region [[ func.string.get_dataset_w_sql__single - Single Set ]]
        public static DataSet get_dataset_w_sql__single(string dbConnString, string p_sql, string p_datasetName, int p_startIndex, int p_maxRow)
        {
            try
            {
                // Get the DB connection
                if (dbConnString == "")
                    return null;

                using (var _dbConn = new SqlConnection(dbConnString))
                {
                    DataSet _dataset = new System.Data.DataSet();
                    SqlDataAdapter _adapter;

                    _adapter = new SqlDataAdapter(p_sql, _dbConn);
                    _adapter.SelectCommand.CommandTimeout = 1800000;  // 30 min.

                    if (p_startIndex == 0 && p_maxRow == 0)
                        _adapter.Fill(_dataset, p_datasetName);
                    else
                        _adapter.Fill(_dataset, p_startIndex, p_maxRow, p_datasetName);

                    _adapter.Dispose();

                    return _dataset;
                }
            }
            catch (System.Exception e)
            {
                // This will need to be rewritten to return more detail message
                return null;
            }
        }
        #endregion

        #region [[ func.replace_dynamicSQL #1]]
        public static string replace_dynamicSQL(string sql, string dbConnString)
        {
            while (sql.IndexOf("|<replace>") >= 0 && sql.IndexOf("</replace>|") >= 0)
            {
                string replStr = util.grab_substring(sql, "|<replace>", "</replace>|", false);
                string replStrSQL = util.grab_substring(sql, "|<replace>", "</replace>|", true);

                string newStr = "";
                try
                {
                    if (!string.IsNullOrEmpty(replStrSQL))
                    {

                        System.Data.DataSet sqlSelect = get_dataset_w_sql__single(dbConnString, replStrSQL, "result", 0, 0);
                        if (sqlSelect != null && sqlSelect.Tables.Count > 0 && sqlSelect.Tables[0].Rows.Count > 0)
                        {
                            newStr = sqlSelect.Tables[0].Rows[0][0].ToString();
                        }
                    }
                }
                catch (Exception e)
                {
                    // Ignore
                }

                sql = sql.Replace(replStr, newStr);
            }

            return sql;
        }
        #endregion

        #region [ get_normFNAMEwhere ]
        public static string get_normFNAMEwhere(string firstName_fieldName, string firstName_filter, IdataService _dataService)
        {
            string _sameNickNames = _dataService.same_nickNames(firstName_filter); // ,Joe,Joseph,

            _sameNickNames = _sameNickNames.Substring(1, _sameNickNames.Length - 2);
            string _FNAME_where = "";
            if (_sameNickNames.IndexOf(",") > 0 && _sameNickNames.ToLower() != firstName_filter.ToLower())
            {
                string[] _FNAMEs = _sameNickNames.Split(',');
                foreach (string item in _FNAMEs)
                    _FNAME_where = _FNAME_where + (!string.IsNullOrEmpty(_FNAME_where) ? "," : "") + string.Format("'{0}'", item);
            }

            if (!string.IsNullOrEmpty(_FNAME_where))
                return string.Format("({0} LIKE '{1}%' OR {0} IN ({2}))", firstName_fieldName, firstName_filter, _FNAME_where);
            else
                return string.Format("{0} LIKE '{1}%'", firstName_fieldName, firstName_filter);
        }
        #endregion
        
        #region [[ func.form_comma_separated ]] Receiving a comma-separated list, return a quoted string
        public static string form_comma_separated(string p_input, string p_quote_character)   // p_quote_character(') - eg. single quote 'abc'
        {            
            string[] _items;

            // Lookup Selected should be separated by ' | '
            if (p_input.IndexOf(" | ")>=0)
                _items = p_input.Split(new string[] { " | " }, StringSplitOptions.None);

            // Split by comma
            else
                _items = p_input.Split(',');

            // Clean each item and the reserved, quote character
            string _result = "";
            for (int i = 0; i < _items.Count(); i++)
            {
                // Remove the quote character.
                string _item = _items[i].Trim();
                if (!String.IsNullOrEmpty(p_quote_character))
                    _item = _item.Replace(p_quote_character, "");

                // Put it back
                if (!String.IsNullOrEmpty(_item))
                    _result = _result + (!string.IsNullOrEmpty(_result) ? "," : "")
                                      + String.Format("{0}{1}{2}", p_quote_character, _item, p_quote_character);
            }
            return _result;
        }
        #endregion

        #region [ func (string)getSQL ### OLD VERSION ### ]

            #region [ func (string)getSQLwhere - No Sub-Query yet]
            private static string getSQLwhere(List<searchItem_S> _filters)
            {
                string _where = "";
                foreach (searchItem_S item in _filters)
                {
                    string valuef = Library.util.kill_sqlBlacklistWord(item.valuef);
                    if (!item.qItem.parentPanel.subquery && !string.IsNullOrEmpty(valuef))
                        _where = _where + (!string.IsNullOrEmpty(_where) ? " AND " : "") + item.whereS;
                }
                return (string.IsNullOrEmpty(_where) ? "" : "WHERE "+_where);
            }
            #endregion

        public static string getSQL1(q_def_S _def, List<searchItem> _filters, string dbConnString,
            Func<string, string, string> fn_get_sqlFROM = null,
            Func<string, string, string> fn_get_sqlFIELDs = null,
            bool forView = false)
        {
            List<q_whereItem_S> qItems = _def.qItems_;

            var f = from a in qItems
                    join b in _filters on a.id equals b.id
                    select new searchItem_S()
                    {
                        id = b.id,
                        fieldType = a.fieldType,
                        fieldName = a.fieldName,
                        opValue = b.opValue,
                        value1 = b.value1,
                        value2 = b.value2,
                        valuef = b.valuef,

                        qItem = a
                       
                    };

            string _sqlWhere = getSQLwhere(f.ToList());
            string _sqlFrom = (fn_get_sqlFROM != null ? fn_get_sqlFROM(_sqlWhere, _def.sq_from) : _def.sq_from);
            string _sqlFields = (fn_get_sqlFIELDs != null ? fn_get_sqlFIELDs(_sqlWhere, (forView ? _def.sq_fieldsV : _def.sq_fields)) : (forView ? _def.sq_fieldsV : _def.sq_fields));

            string sql = string.Format(@"
                SELECT DISTINCT
                {0}
                FROM
                {1}
                {2}
            ",
             replace_dynamicSQL(_sqlFields, dbConnString),
             _sqlFrom,
             _sqlWhere);

            return sql;
        }
        #endregion

        #region [ func (List<q_whereItem_S>) merge_whereItems_n_Filters ]
        public static List<q_whereItem_S> merge_whereItems_n_Filters(q_def_S _def, List<searchItem> _filters)
        {
            var q_selected_qItems =
                    from a in _def.qItems_
                    join b in _filters on a.id equals b.id
                    select new q_whereItem_S()
                    {
                        id = a.id,
                        fieldType = a.fieldType,
                        fieldName = a.fieldName,
                        parentPanelName = a.parentPanelName,
                        groupbyHaving = a.groupbyHaving,

                        filter = new searchItem_S()
                        {
                            id = a.id,
                            fieldName = a.fieldName,
                            fieldType = a.fieldType,
                            applyOR = b.applyOR,
                            applyEXCL = b.applyEXCL,
                            opValue = b.opValue,
                            value1 = b.value1,
                            value2 = b.value2,
                            valuef = b.valuef
                        }
                    };

            return q_selected_qItems.ToList();
        }
        #endregion

        #region [ func (List<q_whereItem_S> create_whereItems_to_return_emptySet()
        public static List<q_whereItem_S> create_whereItems_to_return_emptySet(q_def_S _def)
        {
            #region [ Filters to return an empty dataset ]
            List<searchItem> _filters = new List<searchItem>();
            foreach (q_whereItem_S item in _def.qItems_)
            {                
                searchItem searchItem = new searchItem();

                searchItem.id = item.id;
                searchItem.fieldType = item.fieldType;
                searchItem.fieldName = item.fieldName;
                if (item.fieldType == "s" && _def.reportId != 368 && _def.reportId != 369)
                {
                    searchItem.opValue = "[[ eLIKE ]]";
                    searchItem.value1 = "80418e1a-1180-4a47-9e97-9f679fec3ba4";
                    searchItem.valuef = "80418e1a-1180-4a47-9e97-9f679fec3ba4";
                }
                else if (item.fieldType == "n")
                {
                    searchItem.opValue = "[[: ]]";
                    searchItem.value1 = "90817263";
                    searchItem.valuef = "90817263";
                }
                else if (item.fieldType == "c")
                {
                    searchItem.opValue = "[[:$ ]]";
                    searchItem.value1 = "90817263.54";
                    searchItem.valuef = "90817263.54";
                }
                else if (item.fieldType == "d")
                {
                    searchItem.opValue = "[[ On ]]";
                    searchItem.value1 = "11/29/9999";
                    searchItem.valuef = "11/29/9999";
                }
                else if (item.fieldType == "b")
                {
                    searchItem.opValue = "";
                    searchItem.value1 = "";
                    searchItem.fieldName = "1=0";
                    searchItem.valuef = "true";
                }
                if (item.fieldType == "s" && (_def.reportId == 368 || _def.reportId == 369))
                {
                    searchItem.opValue = "[[: ]]";
                    searchItem.value1 = "90817263";
                    searchItem.valuef = "90817263";
                }
                _filters.Add(searchItem);
            }
            #endregion

            return merge_whereItems_n_Filters(_def, _filters);
        }
        #endregion

        //Fix for : https://www.pivotaltracker.com/n/projects/753361/stories/101794230
        public static string takeCareofSpecialCase(string inp)
        {
            //case like O'Brien --> we need to convert it to O''Brien
            if(!string.IsNullOrEmpty(inp) && inp.Contains("'"))
                inp = inp.Trim().Replace("'", "''");
            return inp;
        }
        
        #region [ func (string)getSQL]
        public static string getSQL(q_def_S _def, List<searchItem> _filters, string dbConnString,
            Func<string, string, string> fn_get_sqlFROM = null,
            Func<string, string, string> fn_get_sqlFIELDs = null,
            bool forView = false)
        {
            List<q_whereItem_panel_S> qPanels = new List<q_whereItem_panel_S>();

            //Apply filters only if there is a specified filter(s)
            if(_filters != null)
            {
                var q_selected_qItems =
                    from a in _def.qItems_
                    join b in _filters on a.id equals b.id
                    select new q_whereItem_S()
                    {
                        id = a.id,
                        fieldType = a.fieldType,
                        fieldName = a.fieldName,
                        parentPanelName = a.parentPanelName,
                        groupbyHaving = a.groupbyHaving,

                        filter = new searchItem_S()
                        {
                            id = a.id,
                            fieldName = a.fieldName,
                            fieldType = a.fieldType,
                            applyOR = b.applyOR,
                            applyEXCL = b.applyEXCL,
                            opValue = b.opValue,
                            value1 = takeCareofSpecialCase(b.value1),
                            value2 = b.value2,
                            valuef = b.valuef
                        }
                    };

                    var q_selected_qPanels =
                    from a in _def.qPanels.Where(a => a.qItems.Count(b => q_selected_qItems.Any(i => i.id == b.id)) > 0)
                    select new q_whereItem_panel_S()
                    {
                        name = a.name,
                        caption = a.caption,
                        subquery = a.subquery,
                        groupBy = a.groupBy,
                        sq_uniqId = a.sq_uniqId,
                        sq_from = a.sq_from,
                        sq_groupBy = a.sq_groupBy,
                        forceSuppress = a.forceSuppress,
                        andSuppress = a.andSuppress,
                        showEXCL = a.showEXCL,
                        showOR = a.showOR,
                        qItems = q_selected_qItems.Where(c => c.parentPanelName == a.name).ToList()
                    };

                    qPanels = q_selected_qPanels.ToList();

            }
            
            string where_ = "";
            string groupbyHaving_ = "";
            foreach (q_whereItem_panel_S panel in qPanels)
            {
                #region [ Main ]
                if (!panel.subquery)
                {
                    foreach (q_whereItem_S item in panel.qItems)
                    {
                        string valuef = Library.util.kill_sqlBlacklistWord(item.filter.valuef);
                        if (!string.IsNullOrEmpty(valuef))
                        {
                            if (item.groupbyHaving)
                            {
                                groupbyHaving_ = groupbyHaving_ + (!string.IsNullOrEmpty(groupbyHaving_) ? " AND " : "") + item.filter.whereS;
                            }
                            else
                            {
                                if (_def.name == "people_search" && item.parentPanelName == "people_gifts" && item.id == "pspm_trk#")
                                {
                                    where_ = where_ + (!string.IsNullOrEmpty(where_) ? " AND " : "") + item.filter.whereS + ")";
                                }
                                else
                                {
                                    where_ = where_ + (!string.IsNullOrEmpty(where_) ? " AND " : "") + item.filter.whereS;
                                }
                                
                            }
                                                        
                        }
                    }

                }
                #endregion

                #region [ Sub-query ]
                else
                {
                    string where_sub_ = "";
                    string groupbyHaving_sub_ = "";
                    foreach (q_whereItem_S item in panel.qItems)
                    {
                        string valuef = Library.util.kill_sqlBlacklistWord(item.filter.valuef);
                        if (!string.IsNullOrEmpty(valuef))
                        {
                            if (item.groupbyHaving)
                                groupbyHaving_sub_ = groupbyHaving_sub_ + (!string.IsNullOrEmpty(groupbyHaving_sub_) ? (item.filter.applyOR ? " OR " : " AND ") : "") + item.filter.whereS;
                            else
                                where_sub_ = where_sub_ + (!string.IsNullOrEmpty(where_sub_) ? (item.filter.applyOR ? " OR " : " AND ") : "") + item.filter.whereS;
                        }
                    }

                    if (!string.IsNullOrEmpty(where_sub_) || !string.IsNullOrEmpty(groupbyHaving_sub_))
                    {
                        where_sub_ = (!string.IsNullOrEmpty(where_sub_) ? " WHERE " + where_sub_ : "");
                        groupbyHaving_sub_ = (!string.IsNullOrEmpty(groupbyHaving_sub_) ? " HAVING " + groupbyHaving_sub_ : "");

                        where_sub_ = 
                            string.Format(@"
                                SELECT
                                {0}
                                FROM
                                {1}
                                {2}
                                {3}
                                {4}
                            ",
                             panel.sq_uniqId,
                             panel.sq_from,
                             where_sub_,
                             (panel.groupBy ? "GROUP BY " + panel.sq_groupBy : ""),
                             groupbyHaving_sub_);

                        where_ = where_ + ( !string.IsNullOrEmpty(where_) ? " AND " : "") + string.Format(@"
                            {0} {1} IN ({2})
                            ",
                            _def.sq_uniqId,
                            (panel.forceSuppress || panel.andSuppress || panel.qItems[0].filter.applyEXCL ? "NOT" : ""),
                            where_sub_);
                    }
                }
                #endregion
            }

            string _sqlWhere = (string.IsNullOrEmpty(where_) && string.IsNullOrEmpty(_def.sq_defaultWhere) 
                                ? "" 
                                : "WHERE "
                                  + _def.sq_defaultWhere 
                                  + (!string.IsNullOrEmpty(_def.sq_defaultWhere) && !string.IsNullOrEmpty(where_) ? " AND " : "") 
                                  + where_                                
                                );
            
            //select PID from Mony where MID in (SELECT [MID] FROM [dsNRSC].[dbo].[vwMONYTRACK] where TRACKNO = 52016)
            //AND P.PID IN (SELECT PID FROM MONY WHERE MID IN (SELECT [MID] FROM vwMONYTRACK where TRACKNO = 52016)

            string _sqlGroupbyHaving = (string.IsNullOrEmpty(groupbyHaving_) ? "" : "HAVING " + groupbyHaving_); 
            string _sqlFrom = (fn_get_sqlFROM != null ? fn_get_sqlFROM(_sqlWhere, _def.sq_from) : _def.sq_from);
            string _sqlFields = (fn_get_sqlFIELDs != null ? fn_get_sqlFIELDs(_sqlWhere, (forView ? _def.sq_fieldsV : _def.sq_fields)) : (forView ? _def.sq_fieldsV : _def.sq_fields));

            string _sqlGroupBy = (!string.IsNullOrEmpty(_def.sq_groupBy) ? "GROUP BY " + _def.sq_groupBy + (!string.IsNullOrEmpty(_sqlGroupbyHaving) ? _sqlGroupbyHaving : "") : "");

            string sql = string.Format(@"
                SELECT DISTINCT
                {0}
                FROM
                {1}
                {2}
                {3}
            ",
             replace_dynamicSQL(_sqlFields, dbConnString),
             _sqlFrom,
             _sqlWhere,
             _sqlGroupBy);

            return sql;
        }
        #endregion
        
    }
}