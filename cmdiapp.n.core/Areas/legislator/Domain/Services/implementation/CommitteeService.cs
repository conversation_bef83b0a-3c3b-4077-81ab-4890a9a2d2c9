﻿using cmdiapp.n.core.Areas.legislator.Domain.Data;
using cmdiapp.n.core.Areas.legislator.Domain.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace cmdiapp.n.core.Areas.legislator.Domain.Services.implementation
{
    public class CommitteeService : ICommitteeService
    {
        private I_entity_legislator _entity;

        public CommitteeService(I_entity_legislator entity)
        {
            _entity = entity;
        }

        public Committee GetCommittee(string committeeId)
        {
            return _entity.Single<Committee>(c => c.Id == committeeId && c.active);
        }

        public List<CommitteeMemberView> GetCommitteeMembers(string committeeId)
        {
            var q = from commMember in _entity.All<CommitteeMember>()
                    join legislator in _entity.All<Legislator>() on commMember.legislatorId equals legislator.Id                    
                    where commMember.committeeId == committeeId 
                        && commMember.active 
                        && legislator.active
                    orderby commMember.rank ascending, commMember.party ascending // 'majority' < 'minority'
                    select new CommitteeMemberView
                    {
                        legislatorParty = legislator.termParty,
                        legislatorDistrict = legislator.termDistrict,
                        legislatorState = legislator.termState,
                        committeeMember = commMember
                    };
            return q.ToList();
        }

        public List<Committee> GetSubcommittees(string parentCommitteeId)
        {
            return _entity.All<Committee>()
                .Where(c => c.parentId == parentCommitteeId 
                    && c.isSubCommittee 
                    && c.active)
                .ToList();
        }
    }
}