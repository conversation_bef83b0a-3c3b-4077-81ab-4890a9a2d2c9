﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Net;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Web.Http;
using System.Web.Script.Serialization;
using System.Drawing;
using System.IO;
using System.Runtime.Serialization.Json;

using Ninject;
using Ninject.Web.Mvc;

using AutoMapper;

using cmdiapp.n.core.Core;
using cmdiapp.n.core.Core.App_Start;
using cmdiapp.n.core.Library;
using cmdiapp.n.core.Domain.ViewModels;
using cmdiapp.n.core.Presentation.Controllers;
using cmdiapp.n.core.Areas.crm.Core;
using cmdiapp.n.core.Areas.crm.Domain.Models;
using cmdiapp.n.core.Areas.crm.Domain.Data;
using cmdiapp.n.core.Areas.crm.Domain.Services;
using cmdiapp.n.core.Areas.query.Domain.Models;
using cmdiapp.n.core.Areas.query;
using cmdiapp.n.core.Areas.dataService.Domain.Data;
using cmdiapp.n.core.Areas.dataService.Domain.Models;
using cmdiapp.n.core.Areas.dataService.Domain.Services;

using cmdiapp.n.core.Domain.Models;
using cmdiapp.n.core.Domain.Repositories;


namespace cmdiapp.n.core.Areas.crm.Presentation.Controllers.api
{
    [Authorize]
    [apiAuthorize(AccessElement = "cmdiapp.dms.voter_qq", AccessLevel = "v")]
    public class VoterController : ApiController
    {
        //<vm><apps><app>VA</app><app>MD</app></apps><clientId></clientId></vm>
                
        #region [[ Declaration ]]
        private I_entity_crm _entity_crm;
        private IdataService _dataService;
        string statelist = "";
        string congDatalist = "";
        string stateSenateData = "";
        string stateHouseAssyData = "";
        string stateCountyData = "";
        string statePrecinctData = "";
        string cityData = "";
        string zipData = "";
        string listData = "";
        string suplistData = "";
        string firstNameData = "";
        string lastNameData = "";
        string genderData = "";
        string marstatusData = "";
        string occupationData = "";
        string estIncomeData = "";
        string partyData = "";
        string hhpartyData = "";
        string agegroupsData = "";
        string voterIdData = "";
        int hhCountData;
        bool voterwphoneData;
        bool subCountAvl;
        int voteFreqData;
        string electionData = "";
        string electionYearData = "";
        //string elecStartDate = "";
        //string elecEndDate = "";
        string regStartDate = "";
        string regEndDate = "";
        bool IsAndNeeded = false;
        System.Data.DataSet _ds;
        string sql = "";
        string customORCondition = "";
        #endregion

        #region [[ (constructor) VoterQController ]]
        public VoterController()
        {
            _entity_crm = I_entityManager_crm.getEntity();
            _dataService = I_entityManager_ds.getService();
        }
        #endregion

        #region[[Voter Main]]
        [HttpGet, Route("crm/api/Voter/getVm_Voter/{voterSeq}")]
        public vm_Voter getVm_Voter(string voterSeq)
        {
            string _sql = String.Format("select * from x_vm_voter where voterSeq={0}", voterSeq);

            return _entity_crm.getContext().Database.SqlQuery<vm_Voter>(_sql).FirstOrDefault();
        }

        [HttpGet, Route("crm/api/Voter/getVm_DemographicInfo/{voterSeq}")]
        public vm_Demographic getVm_DemographicInfo(string voterSeq)
        {

            try
            {
                string _sql = String.Format("select * from x_vm_demographic where voterSeq={0}", voterSeq);
                return _entity_crm.getContext().Database.SqlQuery<vm_Demographic>(_sql).FirstOrDefault();
            }
            catch(Exception ex)
            {
                return null;
            }
            
        }

        [HttpGet, Route("crm/api/Voter/GetVoterInfo/{voterSeq}")]
        public VoterSearch GetVoterInfo(int voterSeq)
        {
            try
            {
                VoterSearch _voter = _entity_crm.Single<VoterSearch>(a => a.voterSeq == voterSeq);
                return _voter;
            }
            catch(Exception ex)
            {
                return null;
            }
            
        }
        #endregion
        
        #region [[ Lookup Data based on UI changes ]]

        [HttpGet, Route("crm/api/Voter/getVMListInfo")]
        [apiAuthorize(AccessElement = "cmdiapp.dms.voter_qq", AccessLevel = "v")]
        public List<vm_ListInfo> getVMListInfo()
        {
            string _sql = String.Format("SELECT [listId],[recCnt],[fileRecvDate],[statusDate],[status],[metaData],[LandL_listId],[LandL_uuId],[custName],[listName],[userName],[recCount_shouldBe],[recPurchased],[amountDue] FROM [x_vm_listInfo] order by fileRecvDate desc");
            List<vm_ListInfo> _result = new List<vm_ListInfo>();
            _result = _entity_crm.getContext().Database.SqlQuery<vm_ListInfo>(_sql).ToList();

            if (_result != null && _result.Count > 0)
                _result.Insert(0, new vm_ListInfo() { listId = 0, listName = "Choose a List" });

            if (_result != null && _result.Count == 0)
                _result.Insert(0, new vm_ListInfo() { listId = 0, listName = "No Data found" });

            return _result;

        }

        [HttpGet, Route("crm/api/Voter/getCongData/{selstates}")]
        [apiAuthorize(AccessElement = "cmdiapp.dms.voter_qq", AccessLevel = "v")]
        public List<CongDistData> getCongData(string selstates)
        {
            if (selstates != "0")
            {
                string[] statesinArray = selstates.Split(',');
                statelist = GetListFromArray(statesinArray);
                string _sql = String.Format("SELECT [STATE], [CD2011] FROM x_vm_voter V INNER JOIN x_vm_demographic D ON V.voterSeq=D.voterSeq WHERE [STATE]<>'' AND [CD2011]<>'' AND [STATE] in ({0}) GROUP BY [STATE], [CD2011] ORDER BY [STATE], [CD2011]", statelist);
                List<CongDistData> _result = new List<CongDistData>();
                _result = _entity_crm.getContext().Database.SqlQuery<CongDistData>(_sql).ToList();

                if (_result != null && _result.Count > 0)
                    _result.Insert(0, new CongDistData() { CD2011 = "Choose a Congressional District", STATE = null });

                if (_result != null && _result.Count == 0)
                    _result.Insert(0, new CongDistData() { CD2011 = "No Data found", STATE = null });

                return _result;
            }
            else
            {
                List<CongDistData> _result = new List<CongDistData>();
                _result.Insert(0, new CongDistData() { CD2011 = "Choose a Congressional District", STATE = null });
                return _result;
            }

        }

        [HttpGet, Route("crm/api/Voter/getStateSenateData/{selstates}")]
        [apiAuthorize(AccessElement = "cmdiapp.dms.voter_qq", AccessLevel = "v")]
        public List<StateDistData> getStateSenateData(string selstates)
        {
            if (selstates != "0")
            {
                string[] statesinArray = selstates.Split(',');
                statelist = GetListFromArray(statesinArray);
                string _sql = String.Format("SELECT [STATE], [SD2011] FROM x_vm_voter V INNER JOIN x_vm_demographic D ON V.voterSeq=D.voterSeq WHERE [STATE]<>'' AND [SD2011]<>'' AND [STATE] in ({0}) GROUP BY [STATE], [SD2011] ORDER BY [STATE], [SD2011]", statelist);
                List<StateDistData> _result = new List<StateDistData>();
                _result = _entity_crm.getContext().Database.SqlQuery<StateDistData>(_sql).ToList();

                if (_result != null && _result.Count > 0)
                    _result.Insert(0, new StateDistData() { SD2011 = "Choose a State Senate District", STATE = null });

                if (_result != null && _result.Count == 0)
                    _result.Insert(0, new StateDistData() { SD2011 = "No Data found", STATE = null });

                return _result;
            }
            else
            {
                List<StateDistData> _result = new List<StateDistData>();
                _result.Insert(0, new StateDistData() { SD2011 = "Choose a State Senate District", STATE = null });
                return _result;
            }

        }

        [HttpGet, Route("crm/api/Voter/getStateHouseAssyData/{selstates}")]
        [apiAuthorize(AccessElement = "cmdiapp.dms.voter_qq", AccessLevel = "v")]
        public List<StateHouseAsslyDistData> getStateHouseAssyData(string selstates)
        {
            if (selstates != "0")
            {
                string[] statesinArray = selstates.Split(',');
                statelist = GetListFromArray(statesinArray);
                string _sql = String.Format("SELECT [STATE], [LS2011], COUNT(*) FROM x_vm_voter V INNER JOIN x_vm_demographic D ON V.voterSeq=D.voterSeq WHERE [STATE]<>'' AND [LS2011]<>'' AND [STATE] in ({0}) GROUP BY [STATE], [LS2011] ORDER BY [STATE], [LS2011]", statelist);
                List<StateHouseAsslyDistData> _result = new List<StateHouseAsslyDistData>();
                _result = _entity_crm.getContext().Database.SqlQuery<StateHouseAsslyDistData>(_sql).ToList();

                if (_result != null && _result.Count > 0)
                    _result.Insert(0, new StateHouseAsslyDistData() { LS2011 = "Choose a State House/Assy District", STATE = null });

                if (_result != null && _result.Count == 0)
                    _result.Insert(0, new StateHouseAsslyDistData() { LS2011 = "No Data found", STATE = null });

                return _result;
            }
            else
            {
                List<StateHouseAsslyDistData> _result = new List<StateHouseAsslyDistData>();
                _result.Insert(0, new StateHouseAsslyDistData() { LS2011 = "Choose a State House/Assy District", STATE = null });
                return _result;
            }

        }

        [HttpGet, Route("crm/api/Voter/getStateCountyData/{selstates}")]
        [apiAuthorize(AccessElement = "cmdiapp.dms.voter_qq", AccessLevel = "v")]
        public List<StateCountyData> getStateCountyData(string selstates)
        {
            if (selstates != "0")
            {
                string[] statesinArray = selstates.Split(',');
                statelist = GetListFromArray(statesinArray);
                string _sql = String.Format("SELECT [STATE], [County] FROM x_vm_voter V INNER JOIN x_vm_demographic D ON V.voterSeq=D.voterSeq WHERE [STATE]<>'' AND [County]<>'' AND [STATE] in ({0}) GROUP BY [STATE], [County] ORDER BY [STATE], [County]", statelist);
                List<StateCountyData> _result = new List<StateCountyData>();
                _result = _entity_crm.getContext().Database.SqlQuery<StateCountyData>(_sql).ToList();

                if (_result != null && _result.Count > 0)
                    _result.Insert(0, new StateCountyData() { County = "Choose a County", STATE = null });

                if (_result != null && _result.Count == 0)
                    _result.Insert(0, new StateCountyData() { County = "No Data found", STATE = null });

                return _result;
            }
            else
            {
                List<StateCountyData> _result = new List<StateCountyData>();
                _result.Insert(0, new StateCountyData() { County = "Choose a County", STATE = null });
                return _result;
            }

        }

        //[HttpGet, Route("crm/api/Voter/getStatePrecinctData")]
        //[apiAuthorize(AccessElement = "cmdiapp.dms.voter_qq", AccessLevel = "v")]
        //[Queryable(PageSize = 10)]
        //public IQueryable<StatePrecinctData> getStatePrecinctData(string selstates, string creiteria)
        //{
        //    string _sql;
        //    if (selstates != "0")
        //    {
        //        string statelist = "";
        //        string[] statesinArray = selstates.Split(',');

        //        for (int i = 0; i <= statesinArray.Count() - 1; i++)
        //        {
        //            if (string.IsNullOrEmpty(statelist))
        //            {
        //                statelist = "'" + statesinArray[i].ToString() + "'";
        //            }
        //            else
        //            {
        //                statelist = statelist + "," + "'" + statesinArray[i].ToString() + "'";
        //            }
        //        }
        //        _sql = String.Format("SELECT [STATE], [Precinct] FROM x_vm_voter V INNER JOIN x_vm_demographic D ON V.voterSeq=D.voterSeq WHERE [STATE]<>'' AND [Precinct]<>'' AND [STATE] in ({0}) AND [Precinct] LIKE '%{1}%' GROUP BY [STATE], [Precinct] ORDER BY [STATE], [Precinct]", statelist, creiteria);
        //    }
        //    else
        //    {
        //        _sql = String.Format("SELECT [STATE], [Precinct] FROM x_vm_voter V INNER JOIN x_vm_demographic D ON V.voterSeq=D.voterSeq WHERE [STATE]<>'' AND [Precinct]<>'' AND [Precinct] LIKE '%{0}%' GROUP BY [STATE], [Precinct] ORDER BY [STATE], [Precinct]", creiteria);
        //    }
        //    List<StatePrecinctData> _result = new List<StatePrecinctData>();
        //    _result = _entity_crm.getContext().Database.SqlQuery<StatePrecinctData>(_sql).ToList();
        //    return _result.AsQueryable<StatePrecinctData>();
        //}

        [HttpPost, Route("crm/api/Voter/getStatePrecinctData")]
        [apiAuthorize(AccessElement = "cmdiapp.dms.voter_qq", AccessLevel = "v")]
        public List<StatePrecinctData> getStatePrecinctData(CongDistWithCntyData _congDistWithCntyData)
        {
            statelist = GetListFromArray(_congDistWithCntyData.SELSTATES);
            congDatalist = GetListFromArray(_congDistWithCntyData.SELCONGDISTS);
            stateCountyData = GetListFromArray(_congDistWithCntyData.SELSTATECNTYS);
            //Result List
            List<StatePrecinctData> _result = new List<StatePrecinctData>();
            string _sql = "";
            //Create SQL and Get Data
            if (!string.IsNullOrEmpty(statelist) && !string.IsNullOrEmpty(congDatalist) && !string.IsNullOrEmpty(stateCountyData))
            {
                _sql = String.Format("SELECT [STATE], [Precinct] FROM x_vm_voter V INNER JOIN x_vm_demographic D ON V.voterSeq=D.voterSeq WHERE [STATE]<>'' AND [Precinct]<>'' AND [STATE] in ({0}) AND [CD2011] in ({1}) AND [COUNTY] in ({2})  GROUP BY [STATE], [Precinct] ORDER BY [STATE], [Precinct]", statelist, congDatalist, stateCountyData);
            }
            else if (!string.IsNullOrEmpty(statelist) && !string.IsNullOrEmpty(congDatalist) && string.IsNullOrEmpty(stateCountyData))
            {
                _sql = String.Format("SELECT [STATE], [Precinct] FROM x_vm_voter V INNER JOIN x_vm_demographic D ON V.voterSeq=D.voterSeq WHERE [STATE]<>'' AND [Precinct]<>'' AND [STATE] in ({0}) AND [CD2011] in ({1}) GROUP BY [STATE], [Precinct] ORDER BY [STATE], [Precinct]", statelist, congDatalist);
            }
            else if (!string.IsNullOrEmpty(statelist) && string.IsNullOrEmpty(congDatalist) && !string.IsNullOrEmpty(stateCountyData))
            {
                _sql = String.Format("SELECT [STATE], [Precinct] FROM x_vm_voter V INNER JOIN x_vm_demographic D ON V.voterSeq=D.voterSeq WHERE [STATE]<>'' AND [Precinct]<>'' AND [STATE] in ({0}) AND [COUNTY] in ({1})  GROUP BY [STATE], [Precinct] ORDER BY [STATE], [Precinct]", statelist, stateCountyData);
            }
            //get result
            _result = _entity_crm.getContext().Database.SqlQuery<StatePrecinctData>(_sql).ToList();
            if (_result != null && _result.Count > 0)
                _result.Insert(0, new StatePrecinctData() { Precinct = "Choose a Precinct", STATE = null });

            if (_result != null && _result.Count == 0)
                _result.Insert(0, new StatePrecinctData() { Precinct = "No Data found", STATE = null });

            //Go Back            
            return _result;
        }
        
        [HttpGet, Route("crm/api/Voter/getCityData")]
        [apiAuthorize(AccessElement = "cmdiapp.dms.voter_qq", AccessLevel = "v")]
        [Queryable(PageSize = 10)]
        public IQueryable<CityData> getCityData(string selstates, string creiteria)
        {
            string _sql;
            if (selstates != "0")
            {
                string statelist = "";
                string[] statesinArray = selstates.Split(',');

                for (int i = 0; i <= statesinArray.Count() - 1; i++)
                {
                    if (string.IsNullOrEmpty(statelist))
                    {
                        statelist = "'" + statesinArray[i].ToString() + "'";
                    }
                    else
                    {
                        statelist = statelist + "," + "'" + statesinArray[i].ToString() + "'";
                    }
                }
                _sql = String.Format("SELECT [STATE], [City] FROM x_vm_voter WHERE [STATE]<>'' AND [City]<>'' AND [STATE] in ({0}) AND [City] LIKE '%{1}%' GROUP BY [STATE], [city] ORDER BY [STATE], [City]", statelist, creiteria);
            }
            else
            {
                _sql = String.Format("SELECT [STATE], [City] FROM x_vm_voter WHERE [STATE]<>'' AND [City]<>'' AND [City] LIKE '%{0}%' GROUP BY [STATE], [city] ORDER BY [STATE], [City]", creiteria);
            }
            List<CityData> _result = new List<CityData>();
            _result = _entity_crm.getContext().Database.SqlQuery<CityData>(_sql).ToList();
            return _result.AsQueryable<CityData>();
        }

        [HttpGet, Route("crm/api/Voter/getZipData")]
        [apiAuthorize(AccessElement = "cmdiapp.dms.voter_qq", AccessLevel = "v")]
        [Queryable(PageSize = 10)]
        public IQueryable<ZipData> getZipData(string selstates, string creiteria)
        {
            string _sql;
            if (selstates != "0")
            {
                string statelist = "";
                string[] statesinArray = selstates.Split(',');

                for (int i = 0; i <= statesinArray.Count() - 1; i++)
                {
                    if (string.IsNullOrEmpty(statelist))
                    {
                        statelist = "'" + statesinArray[i].ToString() + "'";
                    }
                    else
                    {
                        statelist = statelist + "," + "'" + statesinArray[i].ToString() + "'";
                    }
                }
                _sql = String.Format("SELECT [STATE], [Zip] FROM x_vm_voter WHERE [STATE]<>'' AND [Zip]<>'' AND [STATE] in ({0}) AND [Zip] LIKE '{1}%' GROUP BY [STATE], [Zip] ORDER BY [STATE], [Zip]", statelist, creiteria);
            }
            else
            {
                _sql = String.Format("SELECT [STATE], [Zip] FROM x_vm_voter WHERE [STATE]<>'' AND [Zip]<>'' AND [Zip] LIKE '{0}%' GROUP BY [STATE], [Zip] ORDER BY [STATE], [Zip]", creiteria);
            }
            List<ZipData> _result = new List<ZipData>();
            _result = _entity_crm.getContext().Database.SqlQuery<ZipData>(_sql).ToList();
            return _result.AsQueryable<ZipData>();
        }

        [HttpGet, Route("crm/api/Voter/ArrayItemWithListName/{sessiondataKey}/{tempdataKey}")]
        [apiAuthorize(AccessElement = "cmdiapp.dms.voter_qq", AccessLevel = "v")]
        public List<ItemWithListName> ArrayItemWithListName(string sessiondataKey, string tempdataKey)
        {
            //Let us have ItemList Object
            List<ItemWithListName> _itemsList = new List<ItemWithListName>();
            //Get the search Criteria from Session
            sessiondataKey = sessiondataKey.Replace("\"", "").Trim();
            //Get Search Criteria Back
            SearchVoterData _searchVoterData = (SearchVoterData)HttpRuntime.Cache[sessiondataKey];
            //Now get the Diff Object Back
            tempdataKey = tempdataKey.Replace("\"", "").Trim();
            //Get Search Criteria Back
            TempStorageData _tempStorageData = (TempStorageData)HttpRuntime.Cache[tempdataKey];
            //Let us loop through each removed item to find out who is the owner
            for (int i = 0; i <= _tempStorageData.TEMPSTORAGE.Count() - 1; i++)
            {
                string arylstitem = "";
                bool IsgoalAchieved = false;
                string _tempStorageItem = _tempStorageData.TEMPSTORAGE[i].ToString();
                //Now get the Individual item
                string[] _tempStorageArray = _tempStorageItem.Split(' ');
                ItemWithListName _vtrItem = new ItemWithListName();

                #region [[ Get Item Based on count ]]
                if (_tempStorageArray.Count() == 2)
                {
                    arylstitem = _tempStorageArray[0].ToString();
                }

                if (_tempStorageArray.Count() == 3)
                {
                    arylstitem = _tempStorageArray[0].ToString() + " " + _tempStorageArray[1].ToString();
                }

                if (_tempStorageArray.Count() >= 4)
                {
                    //Lopp throgh to get the Item
                    for (int p = 0; p <= _tempStorageArray.Count() - 2; p++)
                    {
                        if (p == 0)
                        {
                            arylstitem = _tempStorageArray[0].ToString();
                        }
                        if (p == 1)
                        {
                            arylstitem = arylstitem + " " + _tempStorageArray[1].ToString();
                        }
                        if (p > 1)
                        {
                            arylstitem = arylstitem + " " + _tempStorageArray[p].ToString();
                        }
                    }
                }
                #endregion
                                                                
                #region [[ Processing Part ]]
                    
                    //State or States Possible
                    if (arylstitem == "States" || arylstitem == "State")
                    {
                        IsgoalAchieved = true;
                        //User wants to remove all 
                        _vtrItem.ItemList = "States";
                        _vtrItem.Item = "ALL";
                        _itemsList.Add(_vtrItem);
                    }
                    //Senate or Senates Possible
                    if (!IsgoalAchieved)
                    {
                        if (arylstitem == "Senates" || arylstitem == "Senate")
                        {
                            IsgoalAchieved = true;
                            //User wants to remove all 
                            _vtrItem.ItemList = "Senates";
                            _vtrItem.Item = "ALL";
                            _itemsList.Add(_vtrItem);
                        }
                    }
                    //Assembly or Assemblies Possible
                    if (!IsgoalAchieved)
                    {
                        if (arylstitem == "Assemblies" || arylstitem == "Assembly")
                        {
                            IsgoalAchieved = true;
                            //User wants to remove all 
                            _vtrItem.ItemList = "Assemblies";
                            _vtrItem.Item = "ALL";
                            _itemsList.Add(_vtrItem);
                        }
                    }
                    //City or Cities Possible
                    if (!IsgoalAchieved)
                    {
                        if (arylstitem == "Cities" || arylstitem == "City")
                        {
                            IsgoalAchieved = true;
                            //User wants to remove all 
                            _vtrItem.ItemList = "Cities";
                            _vtrItem.Item = "ALL";
                            _itemsList.Add(_vtrItem);
                        }
                    }
                    
                    //Precinct or Precincts Possible
                    if (!IsgoalAchieved)
                    {
                        if (arylstitem == "Precincts" || arylstitem == "Precinct")
                        {
                            IsgoalAchieved = true;
                            //User wants to remove all 
                            _vtrItem.ItemList = "Precincts";
                            _vtrItem.Item = "ALL";
                            _itemsList.Add(_vtrItem);
                        }
                    }
                    //County or Countys Possible
                    if (!IsgoalAchieved)
                    {
                        if (arylstitem == "Countys" || arylstitem == "County")
                        {
                            IsgoalAchieved = true;
                            //User wants to remove all 
                            _vtrItem.ItemList = "Countys";
                            _vtrItem.Item = "ALL";
                            _itemsList.Add(_vtrItem);
                        }
                    }
                    //Zip or Zips Possible
                    if (!IsgoalAchieved)
                    {
                        if (arylstitem == "Zips" || arylstitem == "Zip")
                        {
                            IsgoalAchieved = true;
                            //User wants to remove all 
                            _vtrItem.ItemList = "Zips";
                            _vtrItem.Item = "ALL";
                            _itemsList.Add(_vtrItem);
                        }
                    }
                    //FirstName or FirstNames Possible
                    if (!IsgoalAchieved)
                    {
                        if (arylstitem == "FirstNames" || arylstitem == "FirstName")
                        {
                            IsgoalAchieved = true;
                            //User wants to remove all 
                            _vtrItem.ItemList = "FirstNames";
                            _vtrItem.Item = "ALL";
                            _itemsList.Add(_vtrItem);
                        }
                    }

                    if (!IsgoalAchieved)
                    {
                        //LastName or LastNames Possible
                        if (arylstitem == "LastNames" || arylstitem == "LastName")
                        {
                            IsgoalAchieved = true;
                            //User wants to remove all 
                            _vtrItem.ItemList = "LastNames";
                            _vtrItem.Item = "ALL";
                            _itemsList.Add(_vtrItem);
                        }
                    }

                    if (!IsgoalAchieved)
                    {
                        //Gender or Genders Possible
                        if (arylstitem == "Genders" || arylstitem == "Gender")
                        {
                            IsgoalAchieved = true;
                            //User wants to remove all 
                            _vtrItem.ItemList = "Genders";
                            _vtrItem.Item = "ALL";
                            _itemsList.Add(_vtrItem);
                        }
                    }
                    
                    //MaritalStatus or MaritalStatuses Possible
                    if (!IsgoalAchieved)
                    {
                        if (arylstitem == "MaritalStatuses" || arylstitem == "MaritalStatus")
                        {
                            IsgoalAchieved = true;
                            //User wants to remove all 
                            _vtrItem.ItemList = "MaritalStatuses";
                            _vtrItem.Item = "ALL";
                            _itemsList.Add(_vtrItem);
                        }
                    }
                    
                    //AgeGroup or AgeGroups Possible
                    if (!IsgoalAchieved)
                    {
                        if (arylstitem == "AgeGroups" || arylstitem == "AgeGroup")
                        {
                            IsgoalAchieved = true;
                            //User wants to remove all 
                            _vtrItem.ItemList = "AgeGroups";
                            _vtrItem.Item = "ALL";
                            _itemsList.Add(_vtrItem);
                        }
                    }
                
                    
                    //Occupation or Occupations Possible
                    if (!IsgoalAchieved)
                    {
                        if (arylstitem == "Occupations" || arylstitem == "Occupation")
                        {
                            IsgoalAchieved = true;
                            //User wants to remove all 
                            _vtrItem.ItemList = "Occupations";
                            _vtrItem.Item = "ALL";
                            _itemsList.Add(_vtrItem);
                        }
                    }
                    
                    //Income or Incomes Possible
                    if (!IsgoalAchieved)
                    {
                        if (arylstitem == "Incomes" || arylstitem == "Income")
                        {
                            IsgoalAchieved = true;
                            //User wants to remove all 
                            _vtrItem.ItemList = "Incomes";
                            _vtrItem.Item = "ALL";
                            _itemsList.Add(_vtrItem);
                        }
                    }
                    
                    //Party or Parties Possible
                    if (!IsgoalAchieved)
                    {
                        if (arylstitem == "Parties" || arylstitem == "Party")
                        {
                            IsgoalAchieved = true;
                            //User wants to remove all 
                            _vtrItem.ItemList = "Parties";
                            _vtrItem.Item = "ALL";
                            _itemsList.Add(_vtrItem);
                        }
                    }
                    
                    //HHParty or HHParties Possible
                    if (!IsgoalAchieved)
                    {
                        if (arylstitem == "HHParties" || arylstitem == "HHParty")
                        {
                            IsgoalAchieved = true;
                            //User wants to remove all 
                            _vtrItem.ItemList = "HHParties";
                            _vtrItem.Item = "ALL";
                            _itemsList.Add(_vtrItem);
                        }
                    }
                    
                    //Election or Elections Possible
                    if (!IsgoalAchieved)
                    {
                        if (arylstitem == "Elections" || arylstitem == "Election")
                        {
                            IsgoalAchieved = true;
                            //User wants to remove all 
                            _vtrItem.ItemList = "Elections";
                            _vtrItem.Item = "ALL";
                            _itemsList.Add(_vtrItem);
                        }
                    }

                    if (!IsgoalAchieved)
                    {
                        //ElectionYear or ElectionYears Possible
                        if (arylstitem == "ElectionYears" || arylstitem == "ElectionYear")
                        {
                            IsgoalAchieved = true;
                            //User wants to remove all 
                            _vtrItem.ItemList = "ElectionYears";
                            _vtrItem.Item = "ALL";
                            _itemsList.Add(_vtrItem);
                        }
                    }

                    if (!IsgoalAchieved)
                    {
                        //List or Lists Possible
                        if (arylstitem == "Lists" || arylstitem == "List")
                        {
                            IsgoalAchieved = true;
                            //User wants to remove all 
                            _vtrItem.ItemList = "Lists";
                            _vtrItem.Item = "ALL";
                            _itemsList.Add(_vtrItem);
                        }
                    }
                    
                    //SuppList or SuppLists Possible
                    if (!IsgoalAchieved)
                    {
                        if (arylstitem == "SuppLists" || arylstitem == "SuppList")
                        {
                            IsgoalAchieved = true;
                            //User wants to remove all 
                            _vtrItem.ItemList = "SuppLists";
                            _vtrItem.Item = "ALL";
                            _itemsList.Add(_vtrItem);
                        }
                    }
                    
                    //Continue if we still need to find
                    if (!IsgoalAchieved)
                    {
                        //Now let us check next array
                        IsgoalAchieved = IsElementExists(_searchVoterData.SELSTATES, arylstitem);
                        //If it is true
                        if (IsgoalAchieved)
                        {
                            //User wants to remove all States
                            _vtrItem.ItemList = "State";
                            _vtrItem.Item = arylstitem;
                            _itemsList.Add(_vtrItem);
                        }
                    }
                    if (!IsgoalAchieved)
                    {
                        //Now let us check next array
                        IsgoalAchieved = IsElementExists(_searchVoterData.SELFIRSTNAMES, arylstitem);
                        //If it is true
                        if (IsgoalAchieved)
                        {
                            //User wants to remove all
                            _vtrItem.ItemList = "FirstName";
                            _vtrItem.Item = arylstitem;
                            _itemsList.Add(_vtrItem);
                        }
                    }
                    if (!IsgoalAchieved)
                    {
                        //Now let us check next array
                        IsgoalAchieved = IsElementExists(_searchVoterData.SELLASTNAMES, arylstitem);
                        //If it is true
                        if (IsgoalAchieved)
                        {
                            //User wants to remove all
                            _vtrItem.ItemList = "LastName";
                            _vtrItem.Item = arylstitem;
                            _itemsList.Add(_vtrItem);
                        }
                    }
                    if (!IsgoalAchieved)
                    {
                        //Now let us check next array
                        IsgoalAchieved = IsElementExists(_searchVoterData.SELGENDERS, arylstitem);
                        //If it is true
                        if (IsgoalAchieved)
                        {
                            //User wants to remove all
                            _vtrItem.ItemList = "Gender";
                            _vtrItem.Item = arylstitem;
                            _itemsList.Add(_vtrItem);
                        }
                    }
                    if (!IsgoalAchieved)
                    {
                        //Now let us check next array
                        IsgoalAchieved = IsElementExists(_searchVoterData.SELMARSTATUSS, arylstitem);
                        //If it is true
                        if (IsgoalAchieved)
                        {
                            //User wants to remove all
                            _vtrItem.ItemList = "MaritalStatus";
                            _vtrItem.Item = arylstitem;
                            _itemsList.Add(_vtrItem);
                        }
                    }
                    if (!IsgoalAchieved)
                    {
                        //Now let us check next array
                        IsgoalAchieved = IsElementExists(_searchVoterData.SELOCCUPATIONS, arylstitem);
                        //If it is true
                        if (IsgoalAchieved)
                        {
                            //User wants to remove all
                            _vtrItem.ItemList = "Occupation";
                            _vtrItem.Item = arylstitem;
                            _itemsList.Add(_vtrItem);
                        }
                    }
                    if (!IsgoalAchieved)
                    {
                        //Now let us check next array
                        IsgoalAchieved = IsElementExists(_searchVoterData.SELESTINCOMES, arylstitem);
                        //If it is true
                        if (IsgoalAchieved)
                        {
                            //User wants to remove all
                            _vtrItem.ItemList = "Income";
                            _vtrItem.Item = arylstitem;
                            _itemsList.Add(_vtrItem);
                        }
                    }
                    if (!IsgoalAchieved)
                    {
                        //Now let us check next array
                        IsgoalAchieved = IsElementExists(_searchVoterData.SELPARTYS, arylstitem);
                        //If it is true
                        if (IsgoalAchieved)
                        {
                            //User wants to remove all
                            _vtrItem.ItemList = "Party";
                            _vtrItem.Item = arylstitem;
                            _itemsList.Add(_vtrItem);
                        }
                    }
                    if (!IsgoalAchieved)
                    {
                        //Now let us check next array
                        IsgoalAchieved = IsElementExists(_searchVoterData.SELHHPARTYS, arylstitem);
                        //If it is true
                        if (IsgoalAchieved)
                        {
                            //User wants to remove all
                            _vtrItem.ItemList = "HHParty";
                            _vtrItem.Item = arylstitem;
                            _itemsList.Add(_vtrItem);
                        }
                    }
                                   
                    if (!IsgoalAchieved)
                    {
                        //State or States Possible
                        if (arylstitem == "Cong Dists" || arylstitem == "Cong Dist")
                        {
                            IsgoalAchieved = true;
                            //User wants to remove all Dists
                            _vtrItem.ItemList = "Dists";
                            _vtrItem.Item = "ALL";
                            _itemsList.Add(_vtrItem);
                        }
                    }
                                    
                    
                    if (!IsgoalAchieved)
                    {
                        //Now let us check next array
                        IsgoalAchieved = IsElementExists(_searchVoterData.SELAGEGROUPS, arylstitem);
                        //If it is true
                        if (IsgoalAchieved)
                        {
                            //User wants to remove all
                            _vtrItem.ItemList = "AgeGroup";
                            _vtrItem.Item = arylstitem;
                            _itemsList.Add(_vtrItem);
                        }
                    }
                    
                    
                    if (!IsgoalAchieved)
                    {
                        //Now let us check next array
                        IsgoalAchieved = IsElementExists(_searchVoterData.SELCONGDISTS, arylstitem);
                        if (IsgoalAchieved)
                        {
                            //User wants to remove 
                            _vtrItem.ItemList = "Dist";
                            _vtrItem.Item = arylstitem;
                            _itemsList.Add(_vtrItem);
                        }
                    }
                                    
                    
                    //Continue if we still need to find
                    if (!IsgoalAchieved)
                    {
                        IsgoalAchieved = IsElementExists(_searchVoterData.SELSTATESENATES, arylstitem);
                        if (IsgoalAchieved)
                        {
                            //User wants to remove 
                            _vtrItem.ItemList = "Senate";
                            _vtrItem.Item = arylstitem;
                            _itemsList.Add(_vtrItem);
                        }
                    }
                    //Continue if we still need to find
                    if (!IsgoalAchieved)
                    {
                        IsgoalAchieved = IsElementExists(_searchVoterData.SELSTATEHASSYS, arylstitem);
                        if (IsgoalAchieved)
                        {
                            //User wants to remove 
                            _vtrItem.ItemList = "Assembly";
                            _vtrItem.Item = arylstitem;
                            _itemsList.Add(_vtrItem);
                        }
                    }
                    //Continue if we still need to find
                    if (!IsgoalAchieved)
                    {
                        IsgoalAchieved = IsElementExists(_searchVoterData.SELCITYS, arylstitem);
                        if (IsgoalAchieved)
                        {
                            //User wants to remove 
                            _vtrItem.ItemList = "City";
                            _vtrItem.Item = arylstitem;
                            _itemsList.Add(_vtrItem);
                        }
                    }
                    //Continue if we still need to find
                    if (!IsgoalAchieved)
                    {
                        IsgoalAchieved = IsElementExists(_searchVoterData.SELSTATEPRECS, arylstitem);
                        if (IsgoalAchieved)
                        {
                            //User wants to remove 
                            _vtrItem.ItemList = "Precinct";
                            _vtrItem.Item = arylstitem;
                            _itemsList.Add(_vtrItem);
                        }
                    }
                    //Continue if we still need to find
                    if (!IsgoalAchieved)
                    {
                        IsgoalAchieved = IsElementExists(_searchVoterData.SELSTATECNTYS, arylstitem);
                        if (IsgoalAchieved)
                        {
                            //User wants to remove 
                            _vtrItem.ItemList = "County";
                            _vtrItem.Item = arylstitem;
                            _itemsList.Add(_vtrItem);
                        }
                    }
                    //Continue if we still need to find
                    if (!IsgoalAchieved)
                    {
                        IsgoalAchieved = IsElementExists(_searchVoterData.SELZIPS, arylstitem);
                        if (IsgoalAchieved)
                        {
                            //User wants to remove 
                            _vtrItem.ItemList = "Zip";
                            _vtrItem.Item = arylstitem;
                            _itemsList.Add(_vtrItem);
                        }
                    }
                    //Continue if we still need to find
                    if (!IsgoalAchieved)
                    {
                        IsgoalAchieved = IsElementExists(_searchVoterData.SELELECTIONS, arylstitem);
                        if (IsgoalAchieved)
                        {
                            //User wants to remove 
                            _vtrItem.ItemList = "Election";
                            _vtrItem.Item = arylstitem;
                            _itemsList.Add(_vtrItem);
                        }
                    }
                    //Continue if we still need to find
                    if (!IsgoalAchieved)
                    {
                        IsgoalAchieved = IsElementExists(_searchVoterData.SELELECTIONYEARS, arylstitem);
                        if (IsgoalAchieved)
                        {
                            //User wants to remove 
                            _vtrItem.ItemList = "ElectionYear";
                            _vtrItem.Item = arylstitem;
                            _itemsList.Add(_vtrItem);
                        }
                    }
                    //Continue if we still need to find
                    if (!IsgoalAchieved)
                    {
                        IsgoalAchieved = IsElementExists(_searchVoterData.SELVMLISTS, arylstitem);
                        if (IsgoalAchieved)
                        {
                            //User wants to remove 
                            _vtrItem.ItemList = "List";
                            _vtrItem.Item = arylstitem;
                            _itemsList.Add(_vtrItem);
                        }
                    }
                    //Continue if we still need to find
                    if (!IsgoalAchieved)
                    {
                        IsgoalAchieved = IsElementExists(_searchVoterData.SELSUPVMLISTS, arylstitem);
                        if (IsgoalAchieved)
                        {
                            //User wants to remove 
                            _vtrItem.ItemList = "SuppList";
                            _vtrItem.Item = arylstitem;
                            _itemsList.Add(_vtrItem);
                        }
                    }

                #endregion
                
            }
            return _itemsList.ToList();
        }

        [HttpPost, Route("crm/api/Voter/DiffStorageKey")]
        [apiAuthorize(AccessElement = "cmdiapp.dms.voter_qq", AccessLevel = "e")]
        public string DiffStorageKey(TempStorageData _tempStorageData)
        {
            string key_ = System.Guid.NewGuid().ToString();
            HttpRuntime.Cache.Insert(key_, _tempStorageData, null, DateTime.UtcNow.Add(new TimeSpan(1, 0, 1)), System.Web.Caching.Cache.NoSlidingExpiration);

            return key_;
        }
               
        [HttpPost, Route("crm/api/Voter/FieldChecker")]
        [apiAuthorize(AccessElement = "cmdiapp.dms.voter_qq", AccessLevel = "v")]
        public genericResponse FieldChecker(SearchVoterData _searchVoterData)
        {
            //Now check if Registration date is selected : Start and End both should be there + End Date should be greate than the Start Date
            if (_searchVoterData.REGSDTE != null && _searchVoterData.REGEDTE == null)
            {
                genericResponse _response = new genericResponse() { success = false, message = "Registration End Date is not selected. Please select Registration End Date to proceed." };
                return _response;
            }
            if (_searchVoterData.REGSDTE != null && _searchVoterData.REGEDTE != null && _searchVoterData.REGEDTE < _searchVoterData.REGSDTE)
            {
                genericResponse _response = new genericResponse() { success = false, message = "Registration End Date should be greate than Registration Start Date." };
                return _response;
            }

            if (string.IsNullOrEmpty(_searchVoterData.searchText) &&
                _searchVoterData.SELSTATES.Count() == 0 &&
                _searchVoterData.SELCITYS.Count() == 0 &&
                _searchVoterData.SELSTATEPRECS.Count() == 0 &&
                _searchVoterData.SELZIPS.Count() == 0 &&
                _searchVoterData.SELFIRSTNAMES.Count() == 0 &&
                _searchVoterData.SELMARSTATUSS.Count() == 0 &&
                _searchVoterData.SELLASTNAMES.Count() == 0 &&
                _searchVoterData.SELGENDERS.Count() == 0 &&
                _searchVoterData.SELAGEGROUPS.Count() == 0 &&
                _searchVoterData.SELOCCUPATIONS.Count() == 0 &&
                _searchVoterData.SELESTINCOMES.Count() == 0 &&
                _searchVoterData.SELPARTYS.Count() == 0 &&
                _searchVoterData.SELHHPARTYS.Count() == 0 &&
                _searchVoterData.SELVMLISTS.Count() == 0 &&
                _searchVoterData.SELSUPVMLISTS.Count() == 0 &&
                _searchVoterData.SELELECTIONS.Count() == 0 &&
                _searchVoterData.SELELECTIONYEARS.Count() == 0 &&
                string.IsNullOrEmpty(_searchVoterData.VOTERID) &&
                string.IsNullOrEmpty(_searchVoterData.HHCOUNT) &&
                string.IsNullOrEmpty(_searchVoterData.VOTEFREQ) &&
                _searchVoterData.REGEDTE == null &&
                _searchVoterData.VOTERWPHONE == false &&
                _searchVoterData.REGSDTE == null)
            {
                genericResponse _response = new genericResponse() { success = false, message = "Search criteria is not selected. Please select at least one criteria to perform search." };
                return _response;
            }
            //Everything is passed 
            genericResponse _responseOk = new genericResponse() { success = true };
            return _responseOk;
        }

        [HttpPost, Route("crm/api/Voter/SearchVoterData")]
        [apiAuthorize(AccessElement = "cmdiapp.dms.voter_qq", AccessLevel = "v")]
        public genericResponse SearchVoterData(SearchVoterData _searchVoterData)
        {

            #region [ Retrieve Input ]
            string _searchText = _searchVoterData.searchText_;
            int _pageSize = _searchVoterData.pageSize_;
            int _pageNo = _searchVoterData.page_;
            string _sortOptions = _searchVoterData.sortOption1_;
            #endregion

            List<VoterR_ext1> _list;

            #region [ 1. Quick Text Search ]
            if (!string.IsNullOrEmpty(_searchText))
            {
                _list = quickTextSearch(_searchText, _pageSize, _pageNo, _sortOptions);
            }
            #endregion

            #region [ 2. Search Data : Advanced Search Here ]
            else
            {
                //Now gather the Information to create SQL statement
                string _sql = GetSQLForAdvancedSearchExport(_searchVoterData);
                //Now Perform the Search 
                _list = sqlSearch(_sql, _pageSize, _pageNo, _sortOptions);
            }
            #endregion

            if (_list != null && _list.Count() > 0)
            {
                Mapper.CreateMap<VoterR_ext1, VoterR>();
                IEnumerable<iItemType> results = _list.Select(a => Mapper.Map<VoterR_ext1, VoterR>(a)).ToList();
                return new genericResponse() { success = true, __count = _list.FirstOrDefault().count_, results = results.ToList() };
            }

            return new genericResponse() { success = true, __count = 0 };

        }

        [HttpPost, Route("crm/api/Voter/GetCounterInformation")]
        [apiAuthorize(AccessElement = "cmdiapp.dms.voter_qq", AccessLevel = "v")]
        public genericResponseV GetCounterInformation(SearchVoterData _searchVoterData)
        {
            VoterCounterBank voterBank = new VoterCounterBank();
            //Get the counter Collection
            voterBank = GetSQLForCounters(_searchVoterData);
            if (voterBank.mainCollection.Count > 0)
            {
                //get List
                IEnumerable<iItemType> mainresults = voterBank.mainCollection.OrderBy(a => a.CounterSeq).ToList();
                IEnumerable<iItemType> allresults = voterBank.allCollection.OrderBy(a => a.CounterSeq).ToList();
                //return
                return new genericResponseV() { success = true, mainresults = mainresults.ToList(), allresults = allresults.ToList(), subCountAvl = subCountAvl };
            }
            else
            {
                //return
                return new genericResponseV() { success = false };
            }
        }

        #region [[SQL Statements to pull Data]]


        #region [[ SQL Statements Bank ]]
        const string _sql_BasicFields = @"SELECT DISTINCT {0} {1} {2}";
        const string _sql_BasicFieldsNoDistinct = @"SELECT {0} {1} {2}"; //Used for Election and Election year Data as one type of Election may have happened multiple times 

        const string _sql_FromClauseWithDemographic = @" FROM x_vm_voter V LEFT OUTER JOIN [dbo].[x_vm_demographic] D ON V.voterSeq = D.voterSeq {0} {1}";
        const string _sql_AddListInfoClause = @" LEFT OUTER JOIN [dbo].[x_vm_jt_list_voter] JL ON V.voterSeq = JL.voterSeq LEFT OUTER JOIN [dbo].[x_vm_listInfo] L ON JL.listId = L.listId";
        const string _sql_AddVoteHistoryClause = @" LEFT OUTER JOIN [dbo].[x_vm_voteHistory] VH ON V.voterSeq = VH.voterSeq";

        const string _sql_StateRecordsClause = @" V.[STATE] in ({0})";
        const string _sql_VoterStateIdClause = @" V.[voterStateId] in ('{0}')";
        const string _sql_CongDataRecordsClause = @" D.CD2011 in ({0})";
        const string _sql_StateSenateRecordsClause = @" D.SD2011 in ({0})";
        const string _sql_StateHouseAssyRecordsClause = @" D.LS2011 in ({0})";
        const string _sql_CountyRecordsClause = @" D.County in ({0})";
        const string _sql_StatePrecinctRecordsClause = @" D.Precinct in ({0})";
        const string _sql_AgeGroupRecordsClause = @" D.ageGroup in ({0})";
        const string _sql_CityRecordsClause = @" V.[City] in ({0})";
        const string _sql_ZipRecordsClause = @" V.[Zip] in ({0})";
        const string _sql_FirstNameRecordsClause = @" V.[FNAME] in ({0})";
        const string _sql_LastNameRecordsClause = @" V.[LNAME] in ({0})";
        const string _sql_PartyRecordsClause = @" D.Party in ({0})";
        const string _sql_GenderRecordsClause = @" D.Gender in ({0})";
        const string _sql_MaritalStatusRecordsClause = @" D.MaritalStatus in ({0})";
        const string _sql_HHPartyRecordsClause = @" D.HHParties in ({0})";
        const string _sql_EstIncomeRecordsClause = @" D.EstimatedIncome in ({0})";
        const string _sql_OccupationRecordsClause = @" D.Occupation in ({0})";
        const string _sql_VoterIdRecordsClause = @" upper(V.voterId) = '{0}'";
        const string _sql_ListRecordsClause = @" L.listName in ({0})";
        const string _sql_SupListRecordsClause = @" L.listName NOT in ({0})";
        const string _sql_ElectionClause = @" VH.ElectionType in ({0})";
        const string _sql_ElectionYearClause = @" YEAR(VH.ElectionDate) in ({0})";
        const string _sql_HHCountClause = @" D.HHCount = {0}";
        const string _sql_VoteFrequencyClause = @" D.VoteFrequency = {0}";
        const string _sql_RegistrationDateRecordsClause = @" D.RegistrationDate >= '{0} 00:00:00' AND D.RegistrationDate <= '{1} 23:59:59'";
        const string _sql_NoPhoneRecordsClause = @" V.voterSeq IN (SELECT voterSeq FROM x_vm_demographic WHERE ISNULL(TELEPHONE,'')<>'')";
        //For Counters
        const string _sql_CounterBase = @"SELECT COUNT(*) As Number ";
        const string _sql_CounterBasic = @" FROM x_vm_voter V ";
        const string _sql_CounterWithDemographic = @" LEFT OUTER JOIN [dbo].[x_vm_demographic] D ON V.voterSeq = D.voterSeq ";
        const string _sql_CounterWithListInfo = @" LEFT OUTER JOIN [dbo].[x_vm_jt_list_voter] JL ON V.voterSeq = JL.voterSeq LEFT OUTER JOIN [dbo].[x_vm_listInfo] L ON JL.listId = L.listId";
        const string _sql_CounterWithVoteHistory = @" LEFT OUTER JOIN [dbo].[x_vm_voteHistory] VH ON V.voterSeq = VH.voterSeq";
        const string _sql_MasterWithAllJoins = _sql_CounterBase + _sql_CounterBasic + _sql_CounterWithDemographic + _sql_CounterWithListInfo + _sql_CounterWithVoteHistory;

        #endregion

        #endregion

        #region [[ Support Methods ]]

        public bool IsElementExists(string[] array, string element)
        {
            if (array.Count() > 0)
            {
                for (int i = 0; i <= array.Count() - 1; i++)
                {
                    if (array[i].ToString().Trim() == element.Trim())
                    {
                        return true;
                    }
                }
            }
            return false;
        }
       
        public string GetItemWithoutStateInfo(string listItem)
        {
            string[] listItemArray = listItem.Trim().Split(':');

            if (listItemArray.Count() > 1)//For Example DC : 08 - Congressional District
                return listItemArray[1].Trim();

            return listItemArray[0].Trim(); //For Example DC - Only State
        }

        public string GetListFromArray(string[] array)
        {
            string list = "";
            if (array != null && array.Count() > 0)
            {
                for (int i = 0; i <= array.Count() - 1; i++)
                {
                    if (string.IsNullOrEmpty(list))
                    {
                        list = "'" + GetItemWithoutStateInfo(array[i].ToString()) + "'";
                    }
                    else
                    {
                        list = list + "," + "'" + GetItemWithoutStateInfo(array[i].ToString()) + "'";
                    }
                }
            }
            return list;
        }

        public string AddAddlnAndCondition(string sql, string whereclause, string addlniteminstring)
        {
            if (!string.IsNullOrEmpty(addlniteminstring))
            {
                sql = sql + " AND " + String.Format(whereclause, addlniteminstring);
            }
            return sql;
        }

        public VoterCounterBank GetSQLForCounters(SearchVoterData _searchVoterData)
        {
            VoterCounterBank vtrBank = new VoterCounterBank();
            List<VoterCounterData> ctrCollection = new List<VoterCounterData>();
            List<VoterCounterData> ctrAllCollection = new List<VoterCounterData>();
            subCountAvl = false; //Set False in the Beginning
            //WHERE Clause
            string WhereClause = " Where";
            //bool addnMain = false;
            string count = "";
            int CounterSeq = 1;
            VoterCounterData _vtr = new VoterCounterData();
            //Now gather the Information to create SQL statement
            #region [[Information Processing Here ]]
            //For Arrays
            statelist = GetListFromArray(_searchVoterData.SELSTATES);
            congDatalist = GetListFromArray(_searchVoterData.SELCONGDISTS);
            stateSenateData = GetListFromArray(_searchVoterData.SELSTATESENATES);
            stateHouseAssyData = GetListFromArray(_searchVoterData.SELSTATEHASSYS);
            stateCountyData = GetListFromArray(_searchVoterData.SELSTATECNTYS);
            statePrecinctData = GetListFromArray(_searchVoterData.SELSTATEPRECS);
            cityData = GetListFromArray(_searchVoterData.SELCITYS);
            zipData = GetListFromArray(_searchVoterData.SELZIPS);
            listData = GetListFromArray(_searchVoterData.SELVMLISTS);
            suplistData = GetListFromArray(_searchVoterData.SELSUPVMLISTS);
            firstNameData = GetListFromArray(_searchVoterData.SELFIRSTNAMES);
            lastNameData = GetListFromArray(_searchVoterData.SELLASTNAMES);
            genderData = GetListFromArray(_searchVoterData.SELGENDERS);
            marstatusData = GetListFromArray(_searchVoterData.SELMARSTATUSS);
            occupationData = GetListFromArray(_searchVoterData.SELOCCUPATIONS);
            estIncomeData = GetListFromArray(_searchVoterData.SELESTINCOMES);
            partyData = GetListFromArray(_searchVoterData.SELPARTYS);
            hhpartyData = GetListFromArray(_searchVoterData.SELHHPARTYS);
            agegroupsData = GetListFromArray(_searchVoterData.SELAGEGROUPS);
            electionData = GetListFromArray(_searchVoterData.SELELECTIONS);
            electionYearData = GetListFromArray(_searchVoterData.SELELECTIONYEARS);
            //For other fields like Boolean and Integer
            voterIdData = string.IsNullOrEmpty(_searchVoterData.VOTERID) ? "" : _searchVoterData.VOTERID.Trim();
            hhCountData = string.IsNullOrEmpty(_searchVoterData.HHCOUNT) ? 0 : Convert.ToInt32(_searchVoterData.HHCOUNT.Trim());
            voterwphoneData = _searchVoterData.VOTERWPHONE ? true : false;
            voteFreqData = string.IsNullOrEmpty(_searchVoterData.VOTEFREQ) ? 0 : Convert.ToInt32(_searchVoterData.VOTEFREQ.Trim());
            regStartDate = _searchVoterData.REGSDTE != null ? Convert.ToDateTime(_searchVoterData.REGSDTE).Date.ToString("d") : "";
            regEndDate = _searchVoterData.REGEDTE != null ? Convert.ToDateTime(_searchVoterData.REGEDTE).Date.ToString("d") : "";
            #endregion
                                  
            #region [[ CODE for Counter Calucation ]]

            #region [[ Area Selection Section ]]
            
            #region [[ For State : Base Item ]]
            //for State
            if (!string.IsNullOrEmpty(statelist))
            {
                //Let us have counter for State selection
                sql = _sql_MasterWithAllJoins + WhereClause + String.Format(_sql_StateRecordsClause, statelist);
                //get count
                count = GetCountForSelection(sql);
                //add in the collection
                ctrCollection = CreateCounterCollection(ctrCollection, (_searchVoterData.SELSTATES.Count() > 1 ? "States" : "State") + " (" + count + ")", "m", CounterSeq);
                //Also store in All Collection
                ctrAllCollection = CreateCounterCollection(ctrAllCollection, (_searchVoterData.SELSTATES.Count() > 1 ? "States" : "State") + " (" + count + ")", "m", CounterSeq);
            }
            //Information for Individual States if more than one state is selected
            if (_searchVoterData.SELSTATES.Count() > 0)
            {
                subCountAvl = true; // We are marking something here so we know that subcount is available for this search
                for (int i = 0; i <= _searchVoterData.SELSTATES.Count() - 1; i++)
                {
                    CounterSeq = CounterSeq + 1;
                    string _st = "'" + _searchVoterData.SELSTATES[i].Trim() + "'";
                    //for this state
                    sql = _sql_MasterWithAllJoins + WhereClause + String.Format(_sql_StateRecordsClause, _st);
                    count = GetCountForSelection(sql);
                    ctrAllCollection = CreateCounterCollection(ctrAllCollection, _st.Replace("'", "").Trim() + " (" + count + ")", "s", CounterSeq);
                }
            }
            #endregion
            
            #region [[ For Cong Dists : 1 - Only State in where ]]
            //for Cong Dist 
            if (!string.IsNullOrEmpty(congDatalist))
            {
                CounterSeq = CounterSeq + 1;
                //Let us have counter for Cong Dist  selection : We will have State Info
                sql = _sql_MasterWithAllJoins + WhereClause + String.Format(_sql_StateRecordsClause, statelist) + " AND " + String.Format(_sql_CongDataRecordsClause, congDatalist);
                //Now Get Count
                count = GetCountForSelection(sql);
                ctrCollection = CreateCounterCollection(ctrCollection, (_searchVoterData.SELCONGDISTS.Count() > 1 ? "Cong Dists" : "Cong Dist") + " (" + count + ")", "m", CounterSeq);
                //Also store in All Collection
                ctrAllCollection = CreateCounterCollection(ctrAllCollection, (_searchVoterData.SELCONGDISTS.Count() > 1 ? "Cong Dists" : "Cong Dist") + " (" + count + ")", "m", CounterSeq);
            }
            //For Cong Dist : User can not select Cong Dist Direcly : He has to select STATE first so we need to include state criteria while showing the count
            if (_searchVoterData.SELCONGDISTS.Count() > 0)
            {
                subCountAvl = true; // We are marking something here so we know that subcount is available for this search
                for (int i = 0; i <= _searchVoterData.SELCONGDISTS.Count() - 1; i++)
                {
                    CounterSeq = CounterSeq + 1;
                    string _cdt = _searchVoterData.SELCONGDISTS[i].Trim();
                    //Now only find the Congressional Dist : Here we have state information also like VA:01
                    string[] _cdtinArray = _cdt.Split(':');
                    //for this Cong Dist with State so we get correct count
                    sql = _sql_MasterWithAllJoins + WhereClause + String.Format(_sql_StateRecordsClause, "'" + _cdtinArray[0].Trim() + "'") + " AND " + String.Format(_sql_CongDataRecordsClause, "'" + _cdtinArray[1].Trim() + "'");
                    count = GetCountForSelection(sql);
                    //Store in the collection
                    ctrAllCollection = CreateCounterCollection(ctrAllCollection, _cdt.Replace("'", "").Trim() + " (" + count + ")", "s", CounterSeq);
                }
            }
            #endregion
            
            #region [[ State Senate : 2 : State + CongDist in Where ]]
            //For State Senate
            if (!string.IsNullOrEmpty(stateSenateData))
            {
                CounterSeq = CounterSeq + 1;
                sql = _sql_MasterWithAllJoins + WhereClause + String.Format(_sql_StateRecordsClause, statelist) + " AND " + String.Format(_sql_StateSenateRecordsClause, stateSenateData);
                //Add if CongDist is selected
                sql = AddAddlnAndCondition(sql, _sql_CongDataRecordsClause, congDatalist);
                //Now get count
                count = GetCountForSelection(sql);
                //Now add in the collection
                ctrCollection = CreateCounterCollection(ctrCollection, (_searchVoterData.SELSTATESENATES.Count() > 1 ? "Senates" : "Senate") + " (" + count + ")", "m", CounterSeq);
                //Also store in All Collection
                ctrAllCollection = CreateCounterCollection(ctrAllCollection, (_searchVoterData.SELSTATESENATES.Count() > 1 ? "Senates" : "Senate") + " (" + count + ")", "m", CounterSeq);
            }
            //For more than one State Senate Selection
            if (_searchVoterData.SELSTATESENATES.Count() > 0)
            {
                subCountAvl = true; // We are marking something here so we know that subcount is available for this search
                for (int i = 0; i <= _searchVoterData.SELSTATESENATES.Count() - 1; i++)
                {
                    CounterSeq = CounterSeq + 1;
                    string _stsenate = _searchVoterData.SELSTATESENATES[i].Trim();
                    //Now only find the State Senate 
                    string[] _stsenateinArray = _stsenate.Split(':');
                    //for this State Senate : We need to check if we need to include Cong Dist in the condition or not
                    sql = _sql_MasterWithAllJoins + WhereClause + String.Format(_sql_StateRecordsClause, "'" + _stsenateinArray[0].Trim() + "'") + " AND " + String.Format(_sql_StateSenateRecordsClause, "'" + _stsenateinArray[1].Trim() + "'");
                    //Add if CongDist is selected
                    sql = AddAddlnAndCondition(sql, _sql_CongDataRecordsClause, congDatalist);
                    count = GetCountForSelection(sql);
                    //ctrAllCollection = CreateCounterCollection(ctrAllCollection, "Senate : " + _stsenate.Replace("'", "").Trim() + " : [" + count + "]", "s", CounterSeq);
                    ctrAllCollection = CreateCounterCollection(ctrAllCollection, _stsenate.Replace("'", "").Trim() + " (" + count + ")", "s", CounterSeq);
                }
            }
            #endregion
            
            #region [[ State House Assy : 3 : State + CongDist + State Senate ]]
            //For State House Assy
            if (!string.IsNullOrEmpty(stateHouseAssyData))
            {
                CounterSeq = CounterSeq + 1;
                sql = _sql_MasterWithAllJoins + WhereClause + String.Format(_sql_StateRecordsClause, statelist) + " AND " + String.Format(_sql_StateHouseAssyRecordsClause, stateHouseAssyData);
                //Add if CongDist is selected
                sql = AddAddlnAndCondition(sql, _sql_CongDataRecordsClause, congDatalist);
                //Add if StateSenate is also selected
                sql = AddAddlnAndCondition(sql, _sql_StateSenateRecordsClause, stateSenateData);
                //Now get count
                count = GetCountForSelection(sql);
                //Now add in the collection
                ctrCollection = CreateCounterCollection(ctrCollection, (_searchVoterData.SELSTATEHASSYS.Count() > 1 ? "Assemblies" : "Assembly") + " (" + count + ")", "m", CounterSeq);
                //Also store in All Collection
                ctrAllCollection = CreateCounterCollection(ctrAllCollection, (_searchVoterData.SELSTATEHASSYS.Count() > 1 ? "Assemblies" : "Assembly") + " (" + count + ")", "m", CounterSeq);
            }
            //For more than one State House Assy Selection
            if (_searchVoterData.SELSTATEHASSYS.Count() > 0)
            {
                subCountAvl = true; // We are marking something here so we know that subcount is available for this search
                for (int i = 0; i <= _searchVoterData.SELSTATEHASSYS.Count() - 1; i++)
                {
                    CounterSeq = CounterSeq + 1;
                    string _sthassy = _searchVoterData.SELSTATEHASSYS[i].Trim();
                    //Now only find the State House Assy
                    string[] _sthassyinArray = _sthassy.Split(':');
                    //for this State House Assy : We will have state selection but we are not sure about Cong Dist and State Senate Selection : We need to add if we have
                    sql = _sql_MasterWithAllJoins + WhereClause + String.Format(_sql_StateRecordsClause, "'" + _sthassyinArray[0].Trim() + "'") + " AND " + String.Format(_sql_StateHouseAssyRecordsClause, "'" + _sthassyinArray[1].Trim() + "'");
                    //Add if CongDist is selected
                    sql = AddAddlnAndCondition(sql, _sql_CongDataRecordsClause, congDatalist);
                    //Add if StateSenate is also selected
                    sql = AddAddlnAndCondition(sql, _sql_StateSenateRecordsClause, stateSenateData);
                    //Now get count Info
                    count = GetCountForSelection(sql);
                    ctrAllCollection = CreateCounterCollection(ctrAllCollection, _sthassy.Replace("'", "").Trim() + " (" + count + ")", "s", CounterSeq);
                }
            }
            #endregion
            
            #region [[ City Data : 4 : State + CongDist + State Senate + State House Assy : Can be without selecting State ]]
            //City Data
            if (!string.IsNullOrEmpty(cityData))
            {
                //Check if States are selected by the user or not
                if (!string.IsNullOrEmpty(statelist))
                {
                    CounterSeq = CounterSeq + 1;
                    //Let us have counter for City selection + We also need State Info so that we calculate count correctly
                    sql = _sql_MasterWithAllJoins + WhereClause + String.Format(_sql_StateRecordsClause, statelist) + " AND " + String.Format(_sql_CityRecordsClause, cityData);
                    //Add if CongDist is selected
                    sql = AddAddlnAndCondition(sql, _sql_CongDataRecordsClause, congDatalist);
                    //Add if StateSenate is also selected
                    sql = AddAddlnAndCondition(sql, _sql_StateSenateRecordsClause, stateSenateData);
                    //Add if State House Assy is also selected
                    sql = AddAddlnAndCondition(sql, _sql_StateHouseAssyRecordsClause, stateHouseAssyData);
                    count = GetCountForSelection(sql);
                    //Now add in the collection
                    ctrCollection = CreateCounterCollection(ctrCollection, (_searchVoterData.SELCITYS.Count() > 1 ? "Cities" : "City") + " (" + count + ")", "m", CounterSeq);
                    //Also store in All Collection
                    ctrAllCollection = CreateCounterCollection(ctrAllCollection, (_searchVoterData.SELCITYS.Count() > 1 ? "Cities" : "City") + " (" + count + ")", "m", CounterSeq);
                }
                else
                {
                    CounterSeq = CounterSeq + 1;
                    //City is selected without selecting State so we need changes here
                    for (int i = 0; i <= _searchVoterData.SELCITYS.Count() - 1; i++)
                    {
                        string[] citystateInfo = _searchVoterData.SELCITYS[i].Trim().Split(':');
                        //Create SQL statement
                        if (string.IsNullOrEmpty(customORCondition))
                        {
                            customORCondition = "(" + String.Format(_sql_StateRecordsClause, "'" + citystateInfo[0].Trim() + "'") + " AND " + String.Format(_sql_CityRecordsClause, "'" + citystateInfo[1].Trim() + "'") + ")";
                        }
                        else
                        {
                            customORCondition = "(" + customORCondition + " OR (" + String.Format(_sql_StateRecordsClause, "'" + citystateInfo[0].Trim() + "'") + " AND " + String.Format(_sql_CityRecordsClause, "'" + citystateInfo[1].Trim() + "'") + "))";
                        }
                    }
                    //Let us have counter for City selection + We also need State Info so that we calculate count correctly
                    sql = _sql_MasterWithAllJoins + WhereClause + " " + customORCondition;
                    count = GetCountForSelection(sql);
                    //Now add in the collection
                    ctrCollection = CreateCounterCollection(ctrCollection, (_searchVoterData.SELCITYS.Count() > 1 ? "Cities" : "City") + " (" + count + ")", "m", CounterSeq);
                    //Also store in All Collection
                    ctrAllCollection = CreateCounterCollection(ctrAllCollection, (_searchVoterData.SELCITYS.Count() > 1 ? "Cities" : "City") + " (" + count + ")", "m", CounterSeq);
                    customORCondition = "";
                }
            }
            //For more than one City Selection
            if (_searchVoterData.SELCITYS.Count() > 0)
            {
                subCountAvl = true; // We are marking something here so we know that subcount is available for this search
                for (int i = 0; i <= _searchVoterData.SELCITYS.Count() - 1; i++)
                {
                    CounterSeq = CounterSeq + 1;
                    string _stcty = _searchVoterData.SELCITYS[i].Trim();
                    //Now only find the City
                    string[] _stctyinArray = _stcty.Split(':');
                    //for this City 
                    sql = _sql_MasterWithAllJoins + WhereClause + String.Format(_sql_StateRecordsClause, "'" + _stctyinArray[0].Trim() + "'") + " AND " + String.Format(_sql_CityRecordsClause, "'" + _stctyinArray[1].Trim() + "'");
                    //Add if CongDist is selected
                    sql = AddAddlnAndCondition(sql, _sql_CongDataRecordsClause, congDatalist);
                    //Add if StateSenate is also selected
                    sql = AddAddlnAndCondition(sql, _sql_StateSenateRecordsClause, stateSenateData);
                    //Add if State House Assy is also selected
                    sql = AddAddlnAndCondition(sql, _sql_StateHouseAssyRecordsClause, stateHouseAssyData);
                    //Now get cont
                    count = GetCountForSelection(sql);
                    //Add in Collection
                    ctrAllCollection = CreateCounterCollection(ctrAllCollection, _stcty.Replace("'", "").Trim() + " (" + count + ")", "s", CounterSeq);
                }
            }
            #endregion

            #region [[ State County : 5 : State + CongDist + State Senate + State House Assy + City ]]
            //State County
            if (!string.IsNullOrEmpty(stateCountyData))
            {
                CounterSeq = CounterSeq + 1;
                //State list will be available for County selection
                sql = _sql_MasterWithAllJoins + WhereClause + String.Format(_sql_StateRecordsClause, statelist) + " AND " + String.Format(_sql_CountyRecordsClause, stateCountyData);
                //Add if CongDist is selected
                sql = AddAddlnAndCondition(sql, _sql_CongDataRecordsClause, congDatalist);
                //Add if StateSenate is also selected
                sql = AddAddlnAndCondition(sql, _sql_StateSenateRecordsClause, stateSenateData);
                //Add if State House Assy is also selected
                sql = AddAddlnAndCondition(sql, _sql_StateHouseAssyRecordsClause, stateHouseAssyData);
                //Add if State County Data is also selected
                sql = AddAddlnAndCondition(sql, _sql_CityRecordsClause, cityData);
                //Now get count
                count = GetCountForSelection(sql);
                //Now add in the collection
                ctrCollection = CreateCounterCollection(ctrCollection, (_searchVoterData.SELSTATECNTYS.Count() > 1 ? "Countys" : "County") + " (" + count + ")", "m", CounterSeq);
                //Also store in All Collection
                ctrAllCollection = CreateCounterCollection(ctrAllCollection, (_searchVoterData.SELSTATECNTYS.Count() > 1 ? "Countys" : "County") + " (" + count + ")", "m", CounterSeq);
            }
            //For more than one State county
            if (_searchVoterData.SELSTATECNTYS.Count() > 0)
            {
                subCountAvl = true; // We are marking something here so we know that subcount is available for this search
                for (int i = 0; i <= _searchVoterData.SELSTATECNTYS.Count() - 1; i++)
                {
                    CounterSeq = CounterSeq + 1;
                    string _stcnty = _searchVoterData.SELSTATECNTYS[i].Trim();
                    //Now only find the State County
                    string[] _stcntyinArray = _stcnty.Split(':');
                    //for this State county : Let us check if have any Cong Dist, State Senate or State House Assy selected....
                    sql = _sql_MasterWithAllJoins + WhereClause + String.Format(_sql_StateRecordsClause, "'" + _stcntyinArray[0].Trim() + "'") + " AND " + String.Format(_sql_CountyRecordsClause, "'" + _stcntyinArray[1].Trim() + "'");
                    //Add if CongDist is selected
                    sql = AddAddlnAndCondition(sql, _sql_CongDataRecordsClause, congDatalist);
                    //Add if StateSenate is also selected
                    sql = AddAddlnAndCondition(sql, _sql_StateSenateRecordsClause, stateSenateData);
                    //Add if State House Assy is also selected
                    sql = AddAddlnAndCondition(sql, _sql_StateHouseAssyRecordsClause, stateHouseAssyData);
                    //Add if State County Data is also selected
                    sql = AddAddlnAndCondition(sql, _sql_CityRecordsClause, cityData);
                    //Now get count
                    count = GetCountForSelection(sql);
                    //Add in the collection
                    ctrAllCollection = CreateCounterCollection(ctrAllCollection, _stcnty.Replace("'", "").Trim() + " (" + count + ")", "s", CounterSeq);
                }
            }
            #endregion
                        
            #region [[ State Precinct : 6 : State + CongDist + State Senate + State House Assy + City + Cnty ]]
            //Precinct Data
            if (!string.IsNullOrEmpty(statePrecinctData))
            {
                //Check if States are selected by the user or not
                if (!string.IsNullOrEmpty(statelist))
                {
                    CounterSeq = CounterSeq + 1;
                    //Let us have counter for City selection + We also need State Info so that we calculate count correctly
                    sql = _sql_MasterWithAllJoins + WhereClause + String.Format(_sql_StateRecordsClause, statelist) + " AND " + String.Format(_sql_StatePrecinctRecordsClause, statePrecinctData);
                    //Add if CongDist is selected
                    sql = AddAddlnAndCondition(sql, _sql_CongDataRecordsClause, congDatalist);
                    //Add if StateSenate is also selected
                    sql = AddAddlnAndCondition(sql, _sql_StateSenateRecordsClause, stateSenateData);
                    //Add if State House Assy is also selected
                    sql = AddAddlnAndCondition(sql, _sql_StateHouseAssyRecordsClause, stateHouseAssyData);
                    //Add if State County Data is also selected
                    sql = AddAddlnAndCondition(sql, _sql_CityRecordsClause, cityData);
                    //Add if cnty is also selected
                    sql = AddAddlnAndCondition(sql, _sql_CountyRecordsClause, stateCountyData);
                    //Now get count
                    count = GetCountForSelection(sql);
                    //Now add in the collection
                    ctrCollection = CreateCounterCollection(ctrCollection, (_searchVoterData.SELSTATEPRECS.Count() > 1 ? "Precincts" : "Precinct") + " (" + count + ")", "m", CounterSeq);
                    //Also store in All Collection
                    ctrAllCollection = CreateCounterCollection(ctrAllCollection, (_searchVoterData.SELSTATEPRECS.Count() > 1 ? "Precincts" : "Precinct") + " (" + count + ")", "m", CounterSeq);
                }
                else
                {
                    CounterSeq = CounterSeq + 1;
                    //City is selected without selecting State so we need changes here
                    for (int i = 0; i <= _searchVoterData.SELSTATEPRECS.Count() - 1; i++)
                    {
                        string[] precintInfo = _searchVoterData.SELSTATEPRECS[i].Trim().Split(':');
                        //Create SQL statement
                        if (string.IsNullOrEmpty(customORCondition))
                        {
                            customORCondition = "(" + String.Format(_sql_StateRecordsClause, "'" + precintInfo[0].Trim() + "'") + " AND " + String.Format(_sql_StatePrecinctRecordsClause, "'" + precintInfo[1].Trim() + "'") + ")";
                        }
                        else
                        {
                            customORCondition = "(" + customORCondition + " OR (" + String.Format(_sql_StateRecordsClause, "'" + precintInfo[0].Trim() + "'") + " AND " + String.Format(_sql_StatePrecinctRecordsClause, "'" + precintInfo[1].Trim() + "'") + "))";
                        }
                    }
                    //Let us have counter for City selection + We also need State Info so that we calculate count correctly
                    sql = _sql_MasterWithAllJoins + WhereClause + " " + customORCondition;
                    count = GetCountForSelection(sql);
                    //Now add in the collection
                    ctrCollection = CreateCounterCollection(ctrCollection, (_searchVoterData.SELSTATEPRECS.Count() > 1 ? "Precincts" : "Precinct") + " (" + count + ")", "m", CounterSeq);
                    //Also store in All Collection
                    ctrAllCollection = CreateCounterCollection(ctrAllCollection, (_searchVoterData.SELSTATEPRECS.Count() > 1 ? "Precincts" : "Precinct") + " (" + count + ")", "m", CounterSeq);
                    customORCondition = "";
                }
            }
            //For more than one State Precinct selection
            if (_searchVoterData.SELSTATEPRECS.Count() > 0)
            {
                subCountAvl = true; // We are marking something here so we know that subcount is available for this search
                for (int i = 0; i <= _searchVoterData.SELSTATEPRECS.Count() - 1; i++)
                {
                    CounterSeq = CounterSeq + 1;
                    string _stprec = _searchVoterData.SELSTATEPRECS[i].Trim();
                    //Now only find the State Precinct
                    string[] _stprecinArray = _stprec.Split(':');
                    //for this State Precinct
                    sql = _sql_MasterWithAllJoins + WhereClause + String.Format(_sql_StateRecordsClause, "'" + _stprecinArray[0].Trim() + "'") + " AND " + String.Format(_sql_StatePrecinctRecordsClause, "'" + _stprecinArray[1].Trim() + "'");
                    //Add if CongDist is selected
                    sql = AddAddlnAndCondition(sql, _sql_CongDataRecordsClause, congDatalist);
                    //Add if StateSenate is also selected
                    sql = AddAddlnAndCondition(sql, _sql_StateSenateRecordsClause, stateSenateData);
                    //Add if State House Assy is also selected
                    sql = AddAddlnAndCondition(sql, _sql_StateHouseAssyRecordsClause, stateHouseAssyData);
                    //Add if State County Data is also selected
                    sql = AddAddlnAndCondition(sql, _sql_CityRecordsClause, cityData);
                    //Add if cnty is also selected
                    sql = AddAddlnAndCondition(sql, _sql_CountyRecordsClause, stateCountyData);
                    //Now get count
                    count = GetCountForSelection(sql);
                    //Add in the collection
                    ctrAllCollection = CreateCounterCollection(ctrAllCollection, _stprec.Replace("'", "").Trim() + " (" + count + ")", "s", CounterSeq);
                }
            }
            #endregion
                      
            #region [[ Zip Data : 7 : State + CongDist + State Senate + State House Assy + City + Precinct + State County ]]
            //Zip Data
            if (!string.IsNullOrEmpty(zipData))
            {
                CounterSeq = CounterSeq + 1;
                //Zip can be selected without selecting State so we need changes here
                for (int i = 0; i <= _searchVoterData.SELZIPS.Count() - 1; i++)
                {
                    string[] zipstateInfo = _searchVoterData.SELZIPS[i].Trim().Split(':');
                    //Create SQL statement
                    if (string.IsNullOrEmpty(customORCondition))
                    {
                        customORCondition = "(" + String.Format(_sql_StateRecordsClause, "'" + zipstateInfo[0].Trim() + "'") + " AND " + String.Format(_sql_ZipRecordsClause, "'" + zipstateInfo[1].Trim() + "'") + ")";
                    }
                    else
                    {
                        customORCondition = "(" + customORCondition + " OR (" + String.Format(_sql_StateRecordsClause, "'" + zipstateInfo[0].Trim() + "'") + " AND " + String.Format(_sql_ZipRecordsClause, "'" + zipstateInfo[1].Trim() + "'") + "))";
                    }
                }
                //Let us have counter for City selection
                sql = _sql_MasterWithAllJoins + WhereClause + customORCondition;
                //Add if CongDist is selected
                sql = AddAddlnAndCondition(sql, _sql_CongDataRecordsClause, congDatalist);
                //Add if StateSenate is also selected
                sql = AddAddlnAndCondition(sql, _sql_StateSenateRecordsClause, stateSenateData);
                //Add if State House Assy is also selected
                sql = AddAddlnAndCondition(sql, _sql_StateHouseAssyRecordsClause, stateHouseAssyData);
                //Add if State County Data is also selected
                sql = AddAddlnAndCondition(sql, _sql_CountyRecordsClause, stateCountyData);
                //Add if City Data is also selected
                sql = AddAddlnAndCondition(sql, _sql_CityRecordsClause, cityData);
                //Add if State Precint Data is also selected
                sql = AddAddlnAndCondition(sql, _sql_StatePrecinctRecordsClause, statePrecinctData);
                //Add if State Precint Data is also selected
                sql = AddAddlnAndCondition(sql, _sql_CountyRecordsClause, stateCountyData);
                //Now get count
                count = GetCountForSelection(sql);
                //Now add in the collection
                ctrCollection = CreateCounterCollection(ctrCollection, (_searchVoterData.SELZIPS.Count() > 1 ? "Zips" : "Zip") + " (" + count + ")", "m", CounterSeq);
                //Also store in All Collection
                ctrAllCollection = CreateCounterCollection(ctrAllCollection, (_searchVoterData.SELZIPS.Count() > 1 ? "Zips" : "Zip") + " (" + count + ")", "m", CounterSeq);
            }
            //For more than one Zip 
            if (_searchVoterData.SELZIPS.Count() > 0)
            {
                subCountAvl = true; // We are marking something here so we know that subcount is available for this search
                for (int i = 0; i <= _searchVoterData.SELZIPS.Count() - 1; i++)
                {
                    CounterSeq = CounterSeq + 1;
                    string _stzip = _searchVoterData.SELZIPS[i].Trim();
                    string[] _stzipinArray = _stzip.Split(':');
                    //for this Zip 
                    sql = _sql_MasterWithAllJoins + WhereClause + String.Format(_sql_StateRecordsClause, "'" + _stzipinArray[0].Trim() + "'") + " AND " + String.Format(_sql_ZipRecordsClause, "'" + _stzipinArray[1].Trim() + "'");
                    //Add if CongDist is selected
                    sql = AddAddlnAndCondition(sql, _sql_CongDataRecordsClause, congDatalist);
                    //Add if StateSenate is also selected
                    sql = AddAddlnAndCondition(sql, _sql_StateSenateRecordsClause, stateSenateData);
                    //Add if State House Assy is also selected
                    sql = AddAddlnAndCondition(sql, _sql_StateHouseAssyRecordsClause, stateHouseAssyData);
                    //Add if State County Data is also selected
                    sql = AddAddlnAndCondition(sql, _sql_CountyRecordsClause, stateCountyData);
                    //Add if City Data is also selected
                    sql = AddAddlnAndCondition(sql, _sql_CityRecordsClause, cityData);
                    //Add if State Precint Data is also selected
                    sql = AddAddlnAndCondition(sql, _sql_StatePrecinctRecordsClause, statePrecinctData);
                    //Add if State Precint Data is also selected
                    sql = AddAddlnAndCondition(sql, _sql_CountyRecordsClause, stateCountyData);
                    //Now get count
                    count = GetCountForSelection(sql);
                    //Add in collection
                    ctrAllCollection = CreateCounterCollection(ctrAllCollection, _stzip.Replace("'", "").Trim() + " (" + count + ")", "s", CounterSeq);
                }
            }
            #endregion

            #endregion

            #region [[ Contact Section ]]

            #region [[ First Name : 8 + All members of Area Selection Section ]]
            //For Firstname
            if (!string.IsNullOrEmpty(firstNameData))
            {
                CounterSeq = CounterSeq + 1;
                //Let us have counter for Firstname selection
                sql = _sql_MasterWithAllJoins + WhereClause + String.Format(_sql_FirstNameRecordsClause, firstNameData);
                //Add if State is selected
                sql = AddAddlnAndCondition(sql, _sql_StateRecordsClause, statelist);
                //Add if CongDist is selected
                sql = AddAddlnAndCondition(sql, _sql_CongDataRecordsClause, congDatalist);
                //Add if StateSenate is also selected
                sql = AddAddlnAndCondition(sql, _sql_StateSenateRecordsClause, stateSenateData);
                //Add if State House Assy is also selected
                sql = AddAddlnAndCondition(sql, _sql_StateHouseAssyRecordsClause, stateHouseAssyData);
                //Add if State County Data is also selected
                sql = AddAddlnAndCondition(sql, _sql_CountyRecordsClause, stateCountyData);
                //Add if City Data is also selected
                sql = AddAddlnAndCondition(sql, _sql_CityRecordsClause, cityData);
                //Add if State Precint Data is also selected
                sql = AddAddlnAndCondition(sql, _sql_StatePrecinctRecordsClause, statePrecinctData);
                //Add if Zip Data is also selected
                sql = AddAddlnAndCondition(sql, _sql_ZipRecordsClause, zipData);
                //Now get count
                count = GetCountForSelection(sql);
                //Now add in the collection
                ctrCollection = CreateCounterCollection(ctrCollection, (_searchVoterData.SELFIRSTNAMES.Count() > 1 ? "FirstNames" : "FirstName") + " (" + count + ")", "m", CounterSeq);
                //Also store in All Collection
                ctrAllCollection = CreateCounterCollection(ctrAllCollection, (_searchVoterData.SELFIRSTNAMES.Count() > 1 ? "FirstNames" : "FirstName") + " (" + count + ")", "m", CounterSeq);
            }
            //for more than one First Name selection
            if (_searchVoterData.SELFIRSTNAMES.Count() > 0)
            {
                subCountAvl = true; // We are marking something here so we know that subcount is available for this search
                for (int i = 0; i <= _searchVoterData.SELFIRSTNAMES.Count() - 1; i++)
                {
                    CounterSeq = CounterSeq + 1;
                    string _fname = "'" + _searchVoterData.SELFIRSTNAMES[i].Trim() + "'";
                    //for this First Name
                    sql = _sql_MasterWithAllJoins + WhereClause + String.Format(_sql_FirstNameRecordsClause, _fname);
                    //Add if State is selected
                    sql = AddAddlnAndCondition(sql, _sql_StateRecordsClause, statelist);
                    //Add if CongDist is selected
                    sql = AddAddlnAndCondition(sql, _sql_CongDataRecordsClause, congDatalist);
                    //Add if StateSenate is also selected
                    sql = AddAddlnAndCondition(sql, _sql_StateSenateRecordsClause, stateSenateData);
                    //Add if State House Assy is also selected
                    sql = AddAddlnAndCondition(sql, _sql_StateHouseAssyRecordsClause, stateHouseAssyData);
                    //Add if State County Data is also selected
                    sql = AddAddlnAndCondition(sql, _sql_CountyRecordsClause, stateCountyData);
                    //Add if City Data is also selected
                    sql = AddAddlnAndCondition(sql, _sql_CityRecordsClause, cityData);
                    //Add if State Precint Data is also selected
                    sql = AddAddlnAndCondition(sql, _sql_StatePrecinctRecordsClause, statePrecinctData);
                    //Add if Zip Data is also selected
                    sql = AddAddlnAndCondition(sql, _sql_ZipRecordsClause, zipData);
                    //Now get count
                    count = GetCountForSelection(sql);
                    //Add in the collection
                    ctrAllCollection = CreateCounterCollection(ctrAllCollection, _fname.Replace("'", "").Trim() + " (" + count + ")", "s", CounterSeq);
                }
            }
            #endregion

            #region [[ Last Name : 9 + All members of Area Selection Section + First Name ]]
            //For Last Names 
            if (!string.IsNullOrEmpty(lastNameData))
            {
                CounterSeq = CounterSeq + 1;
                //Let us have counter for Firstname selection
                sql = _sql_MasterWithAllJoins + WhereClause + String.Format(_sql_LastNameRecordsClause, lastNameData);
                //Add if State is selected
                sql = AddAddlnAndCondition(sql, _sql_StateRecordsClause, statelist);
                //Add if CongDist is selected
                sql = AddAddlnAndCondition(sql, _sql_CongDataRecordsClause, congDatalist);
                //Add if StateSenate is also selected
                sql = AddAddlnAndCondition(sql, _sql_StateSenateRecordsClause, stateSenateData);
                //Add if State House Assy is also selected
                sql = AddAddlnAndCondition(sql, _sql_StateHouseAssyRecordsClause, stateHouseAssyData);
                //Add if State County Data is also selected
                sql = AddAddlnAndCondition(sql, _sql_CountyRecordsClause, stateCountyData);
                //Add if City Data is also selected
                sql = AddAddlnAndCondition(sql, _sql_CityRecordsClause, cityData);
                //Add if State Precint Data is also selected
                sql = AddAddlnAndCondition(sql, _sql_StatePrecinctRecordsClause, statePrecinctData);
                //Add if Zip Data is also selected
                sql = AddAddlnAndCondition(sql, _sql_ZipRecordsClause, zipData);
                //Add if First Name Data is also selected
                sql = AddAddlnAndCondition(sql, _sql_FirstNameRecordsClause, firstNameData);
                //Now get count
                count = GetCountForSelection(sql);
                //Now add in the collection
                ctrCollection = CreateCounterCollection(ctrCollection, (_searchVoterData.SELLASTNAMES.Count() > 1 ? "LastNames" : "LastName") + " (" + count + ")", "m", CounterSeq);
                //Also store in All Collection
                ctrAllCollection = CreateCounterCollection(ctrAllCollection, (_searchVoterData.SELLASTNAMES.Count() > 1 ? "LastNames" : "LastName") + " (" + count + ")", "m", CounterSeq);
            }
            //for more than one First Name selection
            if (_searchVoterData.SELLASTNAMES.Count() > 0)
            {
                subCountAvl = true; // We are marking something here so we know that subcount is available for this search
                for (int i = 0; i <= _searchVoterData.SELLASTNAMES.Count() - 1; i++)
                {
                    CounterSeq = CounterSeq + 1;
                    string _lname = "'" + _searchVoterData.SELLASTNAMES[i].Trim() + "'";
                    //for this Last Name
                    sql = _sql_MasterWithAllJoins + WhereClause + String.Format(_sql_LastNameRecordsClause, _lname);
                    //Add if State is selected
                    sql = AddAddlnAndCondition(sql, _sql_StateRecordsClause, statelist);
                    //Add if CongDist is selected
                    sql = AddAddlnAndCondition(sql, _sql_CongDataRecordsClause, congDatalist);
                    //Add if StateSenate is also selected
                    sql = AddAddlnAndCondition(sql, _sql_StateSenateRecordsClause, stateSenateData);
                    //Add if State House Assy is also selected
                    sql = AddAddlnAndCondition(sql, _sql_StateHouseAssyRecordsClause, stateHouseAssyData);
                    //Add if State County Data is also selected
                    sql = AddAddlnAndCondition(sql, _sql_CountyRecordsClause, stateCountyData);
                    //Add if City Data is also selected
                    sql = AddAddlnAndCondition(sql, _sql_CityRecordsClause, cityData);
                    //Add if State Precint Data is also selected
                    sql = AddAddlnAndCondition(sql, _sql_StatePrecinctRecordsClause, statePrecinctData);
                    //Add if Zip Data is also selected
                    sql = AddAddlnAndCondition(sql, _sql_ZipRecordsClause, zipData);
                    //Add if First Name Data is also selected
                    sql = AddAddlnAndCondition(sql, _sql_FirstNameRecordsClause, firstNameData);
                    //Now get count                 
                    count = GetCountForSelection(sql);
                    //Add in collection
                    ctrAllCollection = CreateCounterCollection(ctrAllCollection, _lname.Replace("'", "").Trim() + " (" + count + ")", "s", CounterSeq);
                }
            }
            #endregion
            
            #region [[ Commented code for VoterId and Voter with Phone records ]]

            //#region [[ For Registration Date : 19 ]]
            //if (!string.IsNullOrEmpty(regStartDate) && !string.IsNullOrEmpty(regEndDate))
            //{
            //    sql = _sql_CounterBase + _sql_CounterBasic + _sql_CounterWithDemographic + WhereClause + String.Format(_sql_RegistrationDateRecordsClause, regStartDate, regEndDate);
            //    //Add if State is selected
            //    sql = AddAddlnAndCondition(sql, _sql_StateRecordsClause, statelist);
            //    //Add if CongDist is selected
            //    sql = AddAddlnAndCondition(sql, _sql_CongDataRecordsClause, congDatalist);
            //    //Add if StateSenate is also selected
            //    sql = AddAddlnAndCondition(sql, _sql_StateSenateRecordsClause, stateSenateData);
            //    //Add if State House Assy is also selected
            //    sql = AddAddlnAndCondition(sql, _sql_StateHouseAssyRecordsClause, stateHouseAssyData);
            //    //Add if State County Data is also selected
            //    sql = AddAddlnAndCondition(sql, _sql_CountyRecordsClause, stateCountyData);
            //    //Add if City Data is also selected
            //    sql = AddAddlnAndCondition(sql, _sql_CityRecordsClause, cityData);
            //    //Add if State Precint Data is also selected
            //    sql = AddAddlnAndCondition(sql, _sql_StatePrecinctRecordsClause, statePrecinctData);
            //    //Add if Zip Data is also selected
            //    sql = AddAddlnAndCondition(sql, _sql_ZipRecordsClause, zipData);
            //    //Add if First Name Data is also selected
            //    sql = AddAddlnAndCondition(sql, _sql_FirstNameRecordsClause, firstNameData);
            //    //Add if Last Name Data is also selected
            //    sql = AddAddlnAndCondition(sql, _sql_LastNameRecordsClause, lastNameData);
            //    //Add if Voter Id is specified by the user
            //    sql = string.IsNullOrEmpty(_searchVoterData.VOTERID) ? sql : sql + " AND " + String.Format(_sql_VoterStateIdClause, _searchVoterData.VOTERID);
            //    //Add if voters with Phone record is selected by the user
            //    sql = _searchVoterData.VOTERWPHONE == false ? sql : sql + " AND " + _sql_NoPhoneRecordsClause;
            //    //Add if Gender Data is also selected
            //    sql = AddAddlnAndCondition(sql, _sql_GenderRecordsClause, genderData);
            //    //Add if Gender Data is also selected
            //    sql = AddAddlnAndCondition(sql, _sql_MaritalStatusRecordsClause, marstatusData);
            //    //Add if Age Group Data is also selected
            //    sql = AddAddlnAndCondition(sql, _sql_AgeGroupRecordsClause, agegroupsData);
            //    //Add if Occupation Data is also selected
            //    sql = AddAddlnAndCondition(sql, _sql_OccupationRecordsClause, occupationData);
            //    //Add if House Hold data is available
            //    sql = hhCountData == 0 ? sql : sql + " AND " + String.Format(_sql_HHCountClause, hhCountData);
            //    //Add if Estimated Income Data is also selected
            //    sql = AddAddlnAndCondition(sql, _sql_EstIncomeRecordsClause, estIncomeData);
            //    //Add if House Hold Party Data is also selected
            //    sql = AddAddlnAndCondition(sql, _sql_PartyRecordsClause, partyData);
            //    //Add if House Hold Party Data is also selected
            //    sql = AddAddlnAndCondition(sql, _sql_HHPartyRecordsClause, hhpartyData);
            //    //Now get Count
            //    count = GetCountForSelection(sql);
            //    ctrCollection = CreateCounterCollection(ctrCollection, "Reg Date : " + regStartDate + " - " + regEndDate + " : [" + count + "]", "m", CounterSeq);
            //}
            //#endregion

            //#region [[ For Voter Frequency Data : 20 ]]
            //if (voteFreqData > 0)
            //{
            //    sql = _sql_CounterBase + _sql_CounterBasic + _sql_CounterWithDemographic + WhereClause + String.Format(_sql_VoteFrequencyClause, voteFreqData);
            //    //Add if State is selected
            //    sql = AddAddlnAndCondition(sql, _sql_StateRecordsClause, statelist);
            //    //Add if CongDist is selected
            //    sql = AddAddlnAndCondition(sql, _sql_CongDataRecordsClause, congDatalist);
            //    //Add if StateSenate is also selected
            //    sql = AddAddlnAndCondition(sql, _sql_StateSenateRecordsClause, stateSenateData);
            //    //Add if State House Assy is also selected
            //    sql = AddAddlnAndCondition(sql, _sql_StateHouseAssyRecordsClause, stateHouseAssyData);
            //    //Add if State County Data is also selected
            //    sql = AddAddlnAndCondition(sql, _sql_CountyRecordsClause, stateCountyData);
            //    //Add if City Data is also selected
            //    sql = AddAddlnAndCondition(sql, _sql_CityRecordsClause, cityData);
            //    //Add if State Precint Data is also selected
            //    sql = AddAddlnAndCondition(sql, _sql_StatePrecinctRecordsClause, statePrecinctData);
            //    //Add if Zip Data is also selected
            //    sql = AddAddlnAndCondition(sql, _sql_ZipRecordsClause, zipData);
            //    //Add if First Name Data is also selected
            //    sql = AddAddlnAndCondition(sql, _sql_FirstNameRecordsClause, firstNameData);
            //    //Add if Last Name Data is also selected
            //    sql = AddAddlnAndCondition(sql, _sql_LastNameRecordsClause, lastNameData);
            //    //Add if Voter Id is specified by the user
            //    sql = string.IsNullOrEmpty(_searchVoterData.VOTERID) ? sql : sql + " AND " + String.Format(_sql_VoterStateIdClause, _searchVoterData.VOTERID);
            //    //Add if voters with Phone record is selected by the user
            //    sql = _searchVoterData.VOTERWPHONE == false ? sql : sql + " AND " + _sql_NoPhoneRecordsClause;
            //    //Add if Gender Data is also selected
            //    sql = AddAddlnAndCondition(sql, _sql_GenderRecordsClause, genderData);
            //    //Add if Gender Data is also selected
            //    sql = AddAddlnAndCondition(sql, _sql_MaritalStatusRecordsClause, marstatusData);
            //    //Add if Age Group Data is also selected
            //    sql = AddAddlnAndCondition(sql, _sql_AgeGroupRecordsClause, agegroupsData);
            //    //Add if Occupation Data is also selected
            //    sql = AddAddlnAndCondition(sql, _sql_OccupationRecordsClause, occupationData);
            //    //Add if House Hold data is available
            //    sql = hhCountData == 0 ? sql : sql + " AND " + String.Format(_sql_HHCountClause, hhCountData);
            //    //Add if Estimated Income Data is also selected
            //    sql = AddAddlnAndCondition(sql, _sql_EstIncomeRecordsClause, estIncomeData);
            //    //Add if House Hold Party Data is also selected
            //    sql = AddAddlnAndCondition(sql, _sql_PartyRecordsClause, partyData);
            //    //Add if House Hold Party Data is also selected
            //    sql = AddAddlnAndCondition(sql, _sql_HHPartyRecordsClause, hhpartyData);
            //    //Check if Reg Start and End Date are available
            //    if (!string.IsNullOrEmpty(regStartDate) && !string.IsNullOrEmpty(regEndDate))
            //    {
            //        sql = sql + " AND " + String.Format(_sql_RegistrationDateRecordsClause, regStartDate, regEndDate);
            //    }
            //    //Now get count
            //    count = GetCountForSelection(sql);
            //    ctrCollection = CreateCounterCollection(ctrCollection, "Voter Freq : " + voteFreqData.ToString() + " : [" + count + "]", "m", CounterSeq);
            //}
            //#endregion

            
            
            //#region [[ Voter Id : 10 + All members of Area Selection Section + First Name + Last Name ]]
            //if (!string.IsNullOrEmpty(_searchVoterData.VOTERID))
            //{
            //    sql = _sql_CounterBase + _sql_CounterBasic + _sql_CounterWithDemographic + WhereClause + String.Format(_sql_VoterStateIdClause, _searchVoterData.VOTERID);
            //    //Add if State is selected
            //    sql = AddAddlnAndCondition(sql, _sql_StateRecordsClause, statelist);
            //    //Add if CongDist is selected
            //    sql = AddAddlnAndCondition(sql, _sql_CongDataRecordsClause, congDatalist);
            //    //Add if StateSenate is also selected
            //    sql = AddAddlnAndCondition(sql, _sql_StateSenateRecordsClause, stateSenateData);
            //    //Add if State House Assy is also selected
            //    sql = AddAddlnAndCondition(sql, _sql_StateHouseAssyRecordsClause, stateHouseAssyData);
            //    //Add if State County Data is also selected
            //    sql = AddAddlnAndCondition(sql, _sql_CountyRecordsClause, stateCountyData);
            //    //Add if City Data is also selected
            //    sql = AddAddlnAndCondition(sql, _sql_CityRecordsClause, cityData);
            //    //Add if State Precint Data is also selected
            //    sql = AddAddlnAndCondition(sql, _sql_StatePrecinctRecordsClause, statePrecinctData);
            //    //Add if Zip Data is also selected
            //    sql = AddAddlnAndCondition(sql, _sql_ZipRecordsClause, zipData);
            //    //Add if First Name Data is also selected
            //    sql = AddAddlnAndCondition(sql, _sql_FirstNameRecordsClause, firstNameData);
            //    //Add if Last Name Data is also selected
            //    sql = AddAddlnAndCondition(sql, _sql_LastNameRecordsClause, lastNameData);
            //    //Now get count
            //    count = GetCountForSelection(sql);
            //    ctrCollection = CreateCounterCollection(ctrCollection, _searchVoterData.VOTERID.ToString() + " (" + count + ")", "m", CounterSeq);
            //}
            //#endregion

            //#region [[ Voter with Phone Record : 10 ]]

            //if (_searchVoterData.VOTERWPHONE)
            //{
            //    sql = _sql_CounterBase + _sql_CounterBasic + _sql_CounterWithDemographic + WhereClause + _sql_NoPhoneRecordsClause;
            //    //Add if State is selected
            //    sql = AddAddlnAndCondition(sql, _sql_StateRecordsClause, statelist);
            //    //Add if CongDist is selected
            //    sql = AddAddlnAndCondition(sql, _sql_CongDataRecordsClause, congDatalist);
            //    //Add if StateSenate is also selected
            //    sql = AddAddlnAndCondition(sql, _sql_StateSenateRecordsClause, stateSenateData);
            //    //Add if State House Assy is also selected
            //    sql = AddAddlnAndCondition(sql, _sql_StateHouseAssyRecordsClause, stateHouseAssyData);
            //    //Add if State County Data is also selected
            //    sql = AddAddlnAndCondition(sql, _sql_CountyRecordsClause, stateCountyData);
            //    //Add if City Data is also selected
            //    sql = AddAddlnAndCondition(sql, _sql_CityRecordsClause, cityData);
            //    //Add if State Precint Data is also selected
            //    sql = AddAddlnAndCondition(sql, _sql_StatePrecinctRecordsClause, statePrecinctData);
            //    //Add if Zip Data is also selected
            //    sql = AddAddlnAndCondition(sql, _sql_ZipRecordsClause, zipData);
            //    //Add if First Name Data is also selected
            //    sql = AddAddlnAndCondition(sql, _sql_FirstNameRecordsClause, firstNameData);
            //    //Add if Last Name Data is also selected
            //    sql = AddAddlnAndCondition(sql, _sql_LastNameRecordsClause, lastNameData);
            //    //Add if Voter Id is specified by the user
            //    sql = string.IsNullOrEmpty(_searchVoterData.VOTERID) ? sql : sql + " AND " + String.Format(_sql_VoterStateIdClause, _searchVoterData.VOTERID);
            //    //Now get count
            //    count = GetCountForSelection(sql);
            //    ctrCollection = CreateCounterCollection(ctrCollection, "Voter with Phone : " + "Yes" + " : [" + count + "]", "m", CounterSeq);
            //}
            //#endregion

            //#region [[ For HH Count Data : 14 ]]
            //if (hhCountData > 0)
            //{
            //    sql = _sql_CounterBase + _sql_CounterBasic + _sql_CounterWithDemographic + WhereClause + String.Format(_sql_HHCountClause, hhCountData);
            //    //Add if State is selected
            //    sql = AddAddlnAndCondition(sql, _sql_StateRecordsClause, statelist);
            //    //Add if CongDist is selected
            //    sql = AddAddlnAndCondition(sql, _sql_CongDataRecordsClause, congDatalist);
            //    //Add if StateSenate is also selected
            //    sql = AddAddlnAndCondition(sql, _sql_StateSenateRecordsClause, stateSenateData);
            //    //Add if State House Assy is also selected
            //    sql = AddAddlnAndCondition(sql, _sql_StateHouseAssyRecordsClause, stateHouseAssyData);
            //    //Add if State County Data is also selected
            //    sql = AddAddlnAndCondition(sql, _sql_CountyRecordsClause, stateCountyData);
            //    //Add if City Data is also selected
            //    sql = AddAddlnAndCondition(sql, _sql_CityRecordsClause, cityData);
            //    //Add if State Precint Data is also selected
            //    sql = AddAddlnAndCondition(sql, _sql_StatePrecinctRecordsClause, statePrecinctData);
            //    //Add if Zip Data is also selected
            //    sql = AddAddlnAndCondition(sql, _sql_ZipRecordsClause, zipData);
            //    //Add if First Name Data is also selected
            //    sql = AddAddlnAndCondition(sql, _sql_FirstNameRecordsClause, firstNameData);
            //    //Add if Last Name Data is also selected
            //    sql = AddAddlnAndCondition(sql, _sql_LastNameRecordsClause, lastNameData);
            //    //Add if Last Name Data is also selected
            //    sql = AddAddlnAndCondition(sql, _sql_LastNameRecordsClause, lastNameData);
            //    //Add if Gender Data is also selected
            //    sql = AddAddlnAndCondition(sql, _sql_GenderRecordsClause, genderData);
            //    //Add if Gender Data is also selected
            //    sql = AddAddlnAndCondition(sql, _sql_MaritalStatusRecordsClause, marstatusData);
            //    //Add if Age Group Data is also selected
            //    sql = AddAddlnAndCondition(sql, _sql_AgeGroupRecordsClause, agegroupsData);
            //    count = GetCountForSelection(sql);
            //    ctrCollection = CreateCounterCollection(ctrCollection, "HH Count : " + hhCountData.ToString() + " : [" + count + "]", "m", CounterSeq);
            //}
            //#endregion



            #endregion

            #endregion

            #region [[ Demographics Section ]]

            #region [[ For Gender : 10 : Area and contact selection section + VoterId + Voter with Phone records ]]
            //For Gender
            if (!string.IsNullOrEmpty(genderData))
            {
                CounterSeq = CounterSeq + 1;
                sql = _sql_MasterWithAllJoins + WhereClause + String.Format(_sql_GenderRecordsClause, genderData);
                //Add if State is selected
                sql = AddAddlnAndCondition(sql, _sql_StateRecordsClause, statelist);
                //Add if CongDist is selected
                sql = AddAddlnAndCondition(sql, _sql_CongDataRecordsClause, congDatalist);
                //Add if StateSenate is also selected
                sql = AddAddlnAndCondition(sql, _sql_StateSenateRecordsClause, stateSenateData);
                //Add if State House Assy is also selected
                sql = AddAddlnAndCondition(sql, _sql_StateHouseAssyRecordsClause, stateHouseAssyData);
                //Add if State County Data is also selected
                sql = AddAddlnAndCondition(sql, _sql_CountyRecordsClause, stateCountyData);
                //Add if City Data is also selected
                sql = AddAddlnAndCondition(sql, _sql_CityRecordsClause, cityData);
                //Add if State Precint Data is also selected
                sql = AddAddlnAndCondition(sql, _sql_StatePrecinctRecordsClause, statePrecinctData);
                //Add if Zip Data is also selected
                sql = AddAddlnAndCondition(sql, _sql_ZipRecordsClause, zipData);
                //Add if First Name Data is also selected
                sql = AddAddlnAndCondition(sql, _sql_FirstNameRecordsClause, firstNameData);
                //Add if Last Name Data is also selected
                sql = AddAddlnAndCondition(sql, _sql_LastNameRecordsClause, lastNameData);
                //Add if Voter Id is specified by the user
                sql = string.IsNullOrEmpty(_searchVoterData.VOTERID) ? sql : sql + " AND " + String.Format(_sql_VoterStateIdClause, _searchVoterData.VOTERID);
                //Add if voters with Phone record is selected by the user
                sql = _searchVoterData.VOTERWPHONE == false ? sql : sql + " AND " + _sql_NoPhoneRecordsClause;
                //Now get count
                count = GetCountForSelection(sql);
                //Now add in the collection
                ctrCollection = CreateCounterCollection(ctrCollection, (_searchVoterData.SELGENDERS.Count() > 1 ? "Genders" : "Gender") + " (" + count + ")", "m", CounterSeq);
                //Also store in All Collection
                ctrAllCollection = CreateCounterCollection(ctrAllCollection, (_searchVoterData.SELGENDERS.Count() > 1 ? "Genders" : "Gender") + " (" + count + ")", "m", CounterSeq);
            }
            //For more than one Gender Selection
            if (_searchVoterData.SELGENDERS.Count() > 0)
            {
                subCountAvl = true; // We are marking something here so we know that subcount is available for this search
                for (int i = 0; i <= _searchVoterData.SELGENDERS.Count() - 1; i++)
                {
                    CounterSeq = CounterSeq + 1;
                    string _gndr = "'" + _searchVoterData.SELGENDERS[i].Trim() + "'";
                    //for this Gender Selection
                    sql = _sql_MasterWithAllJoins + WhereClause + String.Format(_sql_GenderRecordsClause, _gndr);
                    //Add if State is selected
                    sql = AddAddlnAndCondition(sql, _sql_StateRecordsClause, statelist);
                    //Add if CongDist is selected
                    sql = AddAddlnAndCondition(sql, _sql_CongDataRecordsClause, congDatalist);
                    //Add if StateSenate is also selected
                    sql = AddAddlnAndCondition(sql, _sql_StateSenateRecordsClause, stateSenateData);
                    //Add if State House Assy is also selected
                    sql = AddAddlnAndCondition(sql, _sql_StateHouseAssyRecordsClause, stateHouseAssyData);
                    //Add if State County Data is also selected
                    sql = AddAddlnAndCondition(sql, _sql_CountyRecordsClause, stateCountyData);
                    //Add if City Data is also selected
                    sql = AddAddlnAndCondition(sql, _sql_CityRecordsClause, cityData);
                    //Add if State Precint Data is also selected
                    sql = AddAddlnAndCondition(sql, _sql_StatePrecinctRecordsClause, statePrecinctData);
                    //Add if Zip Data is also selected
                    sql = AddAddlnAndCondition(sql, _sql_ZipRecordsClause, zipData);
                    //Add if First Name Data is also selected
                    sql = AddAddlnAndCondition(sql, _sql_FirstNameRecordsClause, firstNameData);
                    //Add if Last Name Data is also selected
                    sql = AddAddlnAndCondition(sql, _sql_LastNameRecordsClause, lastNameData);
                    //Add if Voter Id is specified by the user
                    sql = string.IsNullOrEmpty(_searchVoterData.VOTERID) ? sql : sql + " AND " + String.Format(_sql_VoterStateIdClause, _searchVoterData.VOTERID);
                    //Add if voters with Phone record is selected by the user
                    sql = _searchVoterData.VOTERWPHONE == false ? sql : sql + " AND " + _sql_NoPhoneRecordsClause;
                    //Now get count
                    count = GetCountForSelection(sql);
                    //Add in the collection
                    ctrAllCollection = CreateCounterCollection(ctrAllCollection, _gndr.Replace("'", "").Trim() + " (" + count + ")", "s", CounterSeq);
                }
            }
            #endregion
            
            #region [[ For Marital Status : 11 : Area and contact selection section + VoterId + Voter with Phone records + Gender Data ]]
            //Marital Status
            if (!string.IsNullOrEmpty(marstatusData))
            {
                CounterSeq = CounterSeq + 1;
                sql = _sql_MasterWithAllJoins + WhereClause + String.Format(_sql_MaritalStatusRecordsClause, marstatusData);
                //Add if State is selected
                sql = AddAddlnAndCondition(sql, _sql_StateRecordsClause, statelist);
                //Add if CongDist is selected
                sql = AddAddlnAndCondition(sql, _sql_CongDataRecordsClause, congDatalist);
                //Add if StateSenate is also selected
                sql = AddAddlnAndCondition(sql, _sql_StateSenateRecordsClause, stateSenateData);
                //Add if State House Assy is also selected
                sql = AddAddlnAndCondition(sql, _sql_StateHouseAssyRecordsClause, stateHouseAssyData);
                //Add if State County Data is also selected
                sql = AddAddlnAndCondition(sql, _sql_CountyRecordsClause, stateCountyData);
                //Add if City Data is also selected
                sql = AddAddlnAndCondition(sql, _sql_CityRecordsClause, cityData);
                //Add if State Precint Data is also selected
                sql = AddAddlnAndCondition(sql, _sql_StatePrecinctRecordsClause, statePrecinctData);
                //Add if Zip Data is also selected
                sql = AddAddlnAndCondition(sql, _sql_ZipRecordsClause, zipData);
                //Add if First Name Data is also selected
                sql = AddAddlnAndCondition(sql, _sql_FirstNameRecordsClause, firstNameData);
                //Add if Last Name Data is also selected
                sql = AddAddlnAndCondition(sql, _sql_LastNameRecordsClause, lastNameData);
                //Add if Voter Id is specified by the user
                sql = string.IsNullOrEmpty(_searchVoterData.VOTERID) ? sql : sql + " AND " + String.Format(_sql_VoterStateIdClause, _searchVoterData.VOTERID);
                //Add if voters with Phone record is selected by the user
                sql = _searchVoterData.VOTERWPHONE == false ? sql : sql + " AND " + _sql_NoPhoneRecordsClause;
                //Add if Gender Data is also selected
                sql = AddAddlnAndCondition(sql, _sql_GenderRecordsClause, genderData);
                //Now get count
                count = GetCountForSelection(sql);
                //Now add in the collection
                ctrCollection = CreateCounterCollection(ctrCollection, (_searchVoterData.SELMARSTATUSS.Count() > 1 ? "MaritalStatuses" : "MaritalStatus") + " (" + count + ")", "m", CounterSeq);
                //Also store in All Collection
                ctrAllCollection = CreateCounterCollection(ctrAllCollection, (_searchVoterData.SELMARSTATUSS.Count() > 1 ? "MaritalStatuses" : "MaritalStatus") + " (" + count + ")", "m", CounterSeq);
            }
            //For more than one Marital Status Selection
            if (_searchVoterData.SELMARSTATUSS.Count() > 0)
            {
                subCountAvl = true; // We are marking something here so we know that subcount is available for this search
                for (int i = 0; i <= _searchVoterData.SELMARSTATUSS.Count() - 1; i++)
                {
                    CounterSeq = CounterSeq + 1;
                    string _mstatus = "'" + _searchVoterData.SELMARSTATUSS[i].Trim() + "'";
                    //for this Marital Status Selection
                    sql = _sql_MasterWithAllJoins + WhereClause + String.Format(_sql_MaritalStatusRecordsClause, _mstatus);
                    //Add if State is selected
                    sql = AddAddlnAndCondition(sql, _sql_StateRecordsClause, statelist);
                    //Add if CongDist is selected
                    sql = AddAddlnAndCondition(sql, _sql_CongDataRecordsClause, congDatalist);
                    //Add if StateSenate is also selected
                    sql = AddAddlnAndCondition(sql, _sql_StateSenateRecordsClause, stateSenateData);
                    //Add if State House Assy is also selected
                    sql = AddAddlnAndCondition(sql, _sql_StateHouseAssyRecordsClause, stateHouseAssyData);
                    //Add if State County Data is also selected
                    sql = AddAddlnAndCondition(sql, _sql_CountyRecordsClause, stateCountyData);
                    //Add if City Data is also selected
                    sql = AddAddlnAndCondition(sql, _sql_CityRecordsClause, cityData);
                    //Add if State Precint Data is also selected
                    sql = AddAddlnAndCondition(sql, _sql_StatePrecinctRecordsClause, statePrecinctData);
                    //Add if Zip Data is also selected
                    sql = AddAddlnAndCondition(sql, _sql_ZipRecordsClause, zipData);
                    //Add if First Name Data is also selected
                    sql = AddAddlnAndCondition(sql, _sql_FirstNameRecordsClause, firstNameData);
                    //Add if Last Name Data is also selected
                    sql = AddAddlnAndCondition(sql, _sql_LastNameRecordsClause, lastNameData);
                    //Add if Voter Id is specified by the user
                    sql = string.IsNullOrEmpty(_searchVoterData.VOTERID) ? sql : sql + " AND " + String.Format(_sql_VoterStateIdClause, _searchVoterData.VOTERID);
                    //Add if voters with Phone record is selected by the user
                    sql = _searchVoterData.VOTERWPHONE == false ? sql : sql + " AND " + _sql_NoPhoneRecordsClause;
                    //Add if Gender Data is also selected
                    sql = AddAddlnAndCondition(sql, _sql_GenderRecordsClause, genderData);
                    //Now get count
                    count = GetCountForSelection(sql);
                    //In collection
                    ctrAllCollection = CreateCounterCollection(ctrAllCollection, _mstatus.Replace("'", "").Trim() + " (" + count + ")", "s", CounterSeq);
                }
            }
            #endregion

            #region [[ For Age Group : 13 : Area and contact selection section + VoterId + Voter with Phone records + Gender Data + Marital Status ]]

            //Age Group 
            if (!string.IsNullOrEmpty(agegroupsData))
            {
                CounterSeq = CounterSeq + 1;
                sql = _sql_MasterWithAllJoins + WhereClause + String.Format(_sql_AgeGroupRecordsClause, agegroupsData);
                //Add if State is selected
                sql = AddAddlnAndCondition(sql, _sql_StateRecordsClause, statelist);
                //Add if CongDist is selected
                sql = AddAddlnAndCondition(sql, _sql_CongDataRecordsClause, congDatalist);
                //Add if StateSenate is also selected
                sql = AddAddlnAndCondition(sql, _sql_StateSenateRecordsClause, stateSenateData);
                //Add if State House Assy is also selected
                sql = AddAddlnAndCondition(sql, _sql_StateHouseAssyRecordsClause, stateHouseAssyData);
                //Add if State County Data is also selected
                sql = AddAddlnAndCondition(sql, _sql_CountyRecordsClause, stateCountyData);
                //Add if City Data is also selected
                sql = AddAddlnAndCondition(sql, _sql_CityRecordsClause, cityData);
                //Add if State Precint Data is also selected
                sql = AddAddlnAndCondition(sql, _sql_StatePrecinctRecordsClause, statePrecinctData);
                //Add if Zip Data is also selected
                sql = AddAddlnAndCondition(sql, _sql_ZipRecordsClause, zipData);
                //Add if First Name Data is also selected
                sql = AddAddlnAndCondition(sql, _sql_FirstNameRecordsClause, firstNameData);
                //Add if Last Name Data is also selected
                sql = AddAddlnAndCondition(sql, _sql_LastNameRecordsClause, lastNameData);
                //Add if Voter Id is specified by the user
                sql = string.IsNullOrEmpty(_searchVoterData.VOTERID) ? sql : sql + " AND " + String.Format(_sql_VoterStateIdClause, _searchVoterData.VOTERID);
                //Add if voters with Phone record is selected by the user
                sql = _searchVoterData.VOTERWPHONE == false ? sql : sql + " AND " + _sql_NoPhoneRecordsClause;
                //Add if Gender Data is also selected
                sql = AddAddlnAndCondition(sql, _sql_GenderRecordsClause, genderData);
                //Add if Gender Data is also selected
                sql = AddAddlnAndCondition(sql, _sql_MaritalStatusRecordsClause, marstatusData);
                //Now get count
                count = GetCountForSelection(sql);
                //Now add in the collection
                ctrCollection = CreateCounterCollection(ctrCollection, (_searchVoterData.SELAGEGROUPS.Count() > 1 ? "AgeGroups" : "AgeGroup") + " (" + count + ")", "m", CounterSeq);
                //Also store in All Collection
                ctrAllCollection = CreateCounterCollection(ctrAllCollection, (_searchVoterData.SELAGEGROUPS.Count() > 1 ? "AgeGroups" : "AgeGroup") + " (" + count + ")", "m", CounterSeq);
            }
            //For more than one Age Group Selection
            if (_searchVoterData.SELAGEGROUPS.Count() > 0)
            {
                subCountAvl = true; // We are marking something here so we know that subcount is available for this search
                for (int i = 0; i <= _searchVoterData.SELAGEGROUPS.Count() - 1; i++)
                {
                    CounterSeq = CounterSeq + 1;
                    string _agegrp = "'" + _searchVoterData.SELAGEGROUPS[i].Trim() + "'";
                    //for this Age Group
                    sql = _sql_MasterWithAllJoins + WhereClause + String.Format(_sql_AgeGroupRecordsClause, _agegrp);
                    //Add if State is selected
                    sql = AddAddlnAndCondition(sql, _sql_StateRecordsClause, statelist);
                    //Add if CongDist is selected
                    sql = AddAddlnAndCondition(sql, _sql_CongDataRecordsClause, congDatalist);
                    //Add if StateSenate is also selected
                    sql = AddAddlnAndCondition(sql, _sql_StateSenateRecordsClause, stateSenateData);
                    //Add if State House Assy is also selected
                    sql = AddAddlnAndCondition(sql, _sql_StateHouseAssyRecordsClause, stateHouseAssyData);
                    //Add if State County Data is also selected
                    sql = AddAddlnAndCondition(sql, _sql_CountyRecordsClause, stateCountyData);
                    //Add if City Data is also selected
                    sql = AddAddlnAndCondition(sql, _sql_CityRecordsClause, cityData);
                    //Add if State Precint Data is also selected
                    sql = AddAddlnAndCondition(sql, _sql_StatePrecinctRecordsClause, statePrecinctData);
                    //Add if Zip Data is also selected
                    sql = AddAddlnAndCondition(sql, _sql_ZipRecordsClause, zipData);
                    //Add if First Name Data is also selected
                    sql = AddAddlnAndCondition(sql, _sql_FirstNameRecordsClause, firstNameData);
                    //Add if Last Name Data is also selected
                    sql = AddAddlnAndCondition(sql, _sql_LastNameRecordsClause, lastNameData);
                    //Add if Voter Id is specified by the user
                    sql = string.IsNullOrEmpty(_searchVoterData.VOTERID) ? sql : sql + " AND " + String.Format(_sql_VoterStateIdClause, _searchVoterData.VOTERID);
                    //Add if voters with Phone record is selected by the user
                    sql = _searchVoterData.VOTERWPHONE == false ? sql : sql + " AND " + _sql_NoPhoneRecordsClause;
                    //Add if Gender Data is also selected
                    sql = AddAddlnAndCondition(sql, _sql_GenderRecordsClause, genderData);
                    //Add if Gender Data is also selected
                    sql = AddAddlnAndCondition(sql, _sql_MaritalStatusRecordsClause, marstatusData);
                    //Now get count
                    count = GetCountForSelection(sql);
                    //Add in collection
                    ctrAllCollection = CreateCounterCollection(ctrAllCollection, _agegrp.Replace("'", "").Trim() + " (" + count + ")", "s", CounterSeq);
                }
            }
            #endregion

            #region [[ For Occupation Data : 14 : Area and contact selection section + VoterId + Voter with Phone records + Gender Data + Marital Status + Age group + HH count]]
            //Occupation Data
            if (!string.IsNullOrEmpty(occupationData))
            {
                CounterSeq = CounterSeq + 1;
                sql = _sql_MasterWithAllJoins + WhereClause + String.Format(_sql_OccupationRecordsClause, occupationData);
                //Add if State is selected
                sql = AddAddlnAndCondition(sql, _sql_StateRecordsClause, statelist);
                //Add if CongDist is selected
                sql = AddAddlnAndCondition(sql, _sql_CongDataRecordsClause, congDatalist);
                //Add if StateSenate is also selected
                sql = AddAddlnAndCondition(sql, _sql_StateSenateRecordsClause, stateSenateData);
                //Add if State House Assy is also selected
                sql = AddAddlnAndCondition(sql, _sql_StateHouseAssyRecordsClause, stateHouseAssyData);
                //Add if State County Data is also selected
                sql = AddAddlnAndCondition(sql, _sql_CountyRecordsClause, stateCountyData);
                //Add if City Data is also selected
                sql = AddAddlnAndCondition(sql, _sql_CityRecordsClause, cityData);
                //Add if State Precint Data is also selected
                sql = AddAddlnAndCondition(sql, _sql_StatePrecinctRecordsClause, statePrecinctData);
                //Add if Zip Data is also selected
                sql = AddAddlnAndCondition(sql, _sql_ZipRecordsClause, zipData);
                //Add if First Name Data is also selected
                sql = AddAddlnAndCondition(sql, _sql_FirstNameRecordsClause, firstNameData);
                //Add if Last Name Data is also selected
                sql = AddAddlnAndCondition(sql, _sql_LastNameRecordsClause, lastNameData);
                //Add if Voter Id is specified by the user
                sql = string.IsNullOrEmpty(_searchVoterData.VOTERID) ? sql : sql + " AND " + String.Format(_sql_VoterStateIdClause, _searchVoterData.VOTERID);
                //Add if voters with Phone record is selected by the user
                sql = _searchVoterData.VOTERWPHONE == false ? sql : sql + " AND " + _sql_NoPhoneRecordsClause;
                //Add if Gender Data is also selected
                sql = AddAddlnAndCondition(sql, _sql_GenderRecordsClause, genderData);
                //Add if Gender Data is also selected
                sql = AddAddlnAndCondition(sql, _sql_MaritalStatusRecordsClause, marstatusData);
                //Add if Age Group Data is also selected
                sql = AddAddlnAndCondition(sql, _sql_AgeGroupRecordsClause, agegroupsData);
                //Add if House Hold data is available
                sql = hhCountData == 0 ? sql : sql + " AND " + String.Format(_sql_HHCountClause, hhCountData);
                count = GetCountForSelection(sql);
                //Now add in the collection
                ctrCollection = CreateCounterCollection(ctrCollection, (_searchVoterData.SELOCCUPATIONS.Count() > 1 ? "Occupations" : "Occupation") + " (" + count + ")", "m", CounterSeq);
                //Also store in All Collection
                ctrAllCollection = CreateCounterCollection(ctrAllCollection, (_searchVoterData.SELOCCUPATIONS.Count() > 1 ? "Occupations" : "Occupation") + " (" + count + ")", "m", CounterSeq);
            }
            //For more than one Occupation Data Selection
            if (_searchVoterData.SELOCCUPATIONS.Count() > 0)
            {
                subCountAvl = true; // We are marking something here so we know that subcount is available for this search
                for (int i = 0; i <= _searchVoterData.SELOCCUPATIONS.Count() - 1; i++)
                {
                    CounterSeq = CounterSeq + 1;
                    string _odata = "'" + _searchVoterData.SELOCCUPATIONS[i].Trim() + "'";
                    //for this Occupation 
                    sql = _sql_MasterWithAllJoins + WhereClause + String.Format(_sql_OccupationRecordsClause, _odata);
                    //Add if State is selected
                    sql = AddAddlnAndCondition(sql, _sql_StateRecordsClause, statelist);
                    //Add if CongDist is selected
                    sql = AddAddlnAndCondition(sql, _sql_CongDataRecordsClause, congDatalist);
                    //Add if StateSenate is also selected
                    sql = AddAddlnAndCondition(sql, _sql_StateSenateRecordsClause, stateSenateData);
                    //Add if State House Assy is also selected
                    sql = AddAddlnAndCondition(sql, _sql_StateHouseAssyRecordsClause, stateHouseAssyData);
                    //Add if State County Data is also selected
                    sql = AddAddlnAndCondition(sql, _sql_CountyRecordsClause, stateCountyData);
                    //Add if City Data is also selected
                    sql = AddAddlnAndCondition(sql, _sql_CityRecordsClause, cityData);
                    //Add if State Precint Data is also selected
                    sql = AddAddlnAndCondition(sql, _sql_StatePrecinctRecordsClause, statePrecinctData);
                    //Add if Zip Data is also selected
                    sql = AddAddlnAndCondition(sql, _sql_ZipRecordsClause, zipData);
                    //Add if First Name Data is also selected
                    sql = AddAddlnAndCondition(sql, _sql_FirstNameRecordsClause, firstNameData);
                    //Add if Last Name Data is also selected
                    sql = AddAddlnAndCondition(sql, _sql_LastNameRecordsClause, lastNameData);
                    //Add if Voter Id is specified by the user
                    sql = string.IsNullOrEmpty(_searchVoterData.VOTERID) ? sql : sql + " AND " + String.Format(_sql_VoterStateIdClause, _searchVoterData.VOTERID);
                    //Add if voters with Phone record is selected by the user
                    sql = _searchVoterData.VOTERWPHONE == false ? sql : sql + " AND " + _sql_NoPhoneRecordsClause;
                    //Add if Gender Data is also selected
                    sql = AddAddlnAndCondition(sql, _sql_GenderRecordsClause, genderData);
                    //Add if Gender Data is also selected
                    sql = AddAddlnAndCondition(sql, _sql_MaritalStatusRecordsClause, marstatusData);
                    //Add if Age Group Data is also selected
                    sql = AddAddlnAndCondition(sql, _sql_AgeGroupRecordsClause, agegroupsData);
                    //Add if House Hold data is available
                    sql = hhCountData == 0 ? sql : sql + " AND " + String.Format(_sql_HHCountClause, hhCountData);
                    //Now get count
                    count = GetCountForSelection(sql);
                    //Add in the collection
                    ctrAllCollection = CreateCounterCollection(ctrAllCollection, _odata.Replace("'", "").Trim() + " (" + count + ")", "s", CounterSeq);
                }
            }

            #endregion

            #region [[ Estimated Income Data : 15 : Above + Occupation data ]]
            //Estimated Income
            if (!string.IsNullOrEmpty(estIncomeData))
            {
                CounterSeq = CounterSeq + 1;
                sql = _sql_MasterWithAllJoins + WhereClause + String.Format(_sql_EstIncomeRecordsClause, estIncomeData);
                //Add if State is selected
                sql = AddAddlnAndCondition(sql, _sql_StateRecordsClause, statelist);
                //Add if CongDist is selected
                sql = AddAddlnAndCondition(sql, _sql_CongDataRecordsClause, congDatalist);
                //Add if StateSenate is also selected
                sql = AddAddlnAndCondition(sql, _sql_StateSenateRecordsClause, stateSenateData);
                //Add if State House Assy is also selected
                sql = AddAddlnAndCondition(sql, _sql_StateHouseAssyRecordsClause, stateHouseAssyData);
                //Add if State County Data is also selected
                sql = AddAddlnAndCondition(sql, _sql_CountyRecordsClause, stateCountyData);
                //Add if City Data is also selected
                sql = AddAddlnAndCondition(sql, _sql_CityRecordsClause, cityData);
                //Add if State Precint Data is also selected
                sql = AddAddlnAndCondition(sql, _sql_StatePrecinctRecordsClause, statePrecinctData);
                //Add if Zip Data is also selected
                sql = AddAddlnAndCondition(sql, _sql_ZipRecordsClause, zipData);
                //Add if First Name Data is also selected
                sql = AddAddlnAndCondition(sql, _sql_FirstNameRecordsClause, firstNameData);
                //Add if Last Name Data is also selected
                sql = AddAddlnAndCondition(sql, _sql_LastNameRecordsClause, lastNameData);
                //Add if Voter Id is specified by the user
                sql = string.IsNullOrEmpty(_searchVoterData.VOTERID) ? sql : sql + " AND " + String.Format(_sql_VoterStateIdClause, _searchVoterData.VOTERID);
                //Add if voters with Phone record is selected by the user
                sql = _searchVoterData.VOTERWPHONE == false ? sql : sql + " AND " + _sql_NoPhoneRecordsClause;
                //Add if Gender Data is also selected
                sql = AddAddlnAndCondition(sql, _sql_GenderRecordsClause, genderData);
                //Add if Gender Data is also selected
                sql = AddAddlnAndCondition(sql, _sql_MaritalStatusRecordsClause, marstatusData);
                //Add if Age Group Data is also selected
                sql = AddAddlnAndCondition(sql, _sql_AgeGroupRecordsClause, agegroupsData);
                //Add if Occupation Data is also selected
                sql = AddAddlnAndCondition(sql, _sql_OccupationRecordsClause, occupationData);
                //Add if House Hold data is available
                sql = hhCountData == 0 ? sql : sql + " AND " + String.Format(_sql_HHCountClause, hhCountData);
                //Get count
                count = GetCountForSelection(sql);
                //Now add in the collection
                ctrCollection = CreateCounterCollection(ctrCollection, (_searchVoterData.SELESTINCOMES.Count() > 1 ? "Incomes" : "Income") + " (" + count + ")", "m", CounterSeq);
                //Also store in All Collection
                ctrAllCollection = CreateCounterCollection(ctrAllCollection, (_searchVoterData.SELESTINCOMES.Count() > 1 ? "Incomes" : "Income") + " (" + count + ")", "m", CounterSeq);
            }
            //For more than one Estimated Income Selection
            if (_searchVoterData.SELESTINCOMES.Count() > 0)
            {
                subCountAvl = true; // We are marking something here so we know that subcount is available for this search
                for (int i = 0; i <= _searchVoterData.SELESTINCOMES.Count() - 1; i++)
                {
                    CounterSeq = CounterSeq + 1;
                    string _estIncome = "'" + _searchVoterData.SELESTINCOMES[i].Trim() + "'";
                    //for this Estimated Income Selection
                    sql = _sql_MasterWithAllJoins + WhereClause + String.Format(_sql_EstIncomeRecordsClause, _estIncome);
                    //Add if State is selected
                    sql = AddAddlnAndCondition(sql, _sql_StateRecordsClause, statelist);
                    //Add if CongDist is selected
                    sql = AddAddlnAndCondition(sql, _sql_CongDataRecordsClause, congDatalist);
                    //Add if StateSenate is also selected
                    sql = AddAddlnAndCondition(sql, _sql_StateSenateRecordsClause, stateSenateData);
                    //Add if State House Assy is also selected
                    sql = AddAddlnAndCondition(sql, _sql_StateHouseAssyRecordsClause, stateHouseAssyData);
                    //Add if State County Data is also selected
                    sql = AddAddlnAndCondition(sql, _sql_CountyRecordsClause, stateCountyData);
                    //Add if City Data is also selected
                    sql = AddAddlnAndCondition(sql, _sql_CityRecordsClause, cityData);
                    //Add if State Precint Data is also selected
                    sql = AddAddlnAndCondition(sql, _sql_StatePrecinctRecordsClause, statePrecinctData);
                    //Add if Zip Data is also selected
                    sql = AddAddlnAndCondition(sql, _sql_ZipRecordsClause, zipData);
                    //Add if First Name Data is also selected
                    sql = AddAddlnAndCondition(sql, _sql_FirstNameRecordsClause, firstNameData);
                    //Add if Last Name Data is also selected
                    sql = AddAddlnAndCondition(sql, _sql_LastNameRecordsClause, lastNameData);
                    //Add if Voter Id is specified by the user
                    sql = string.IsNullOrEmpty(_searchVoterData.VOTERID) ? sql : sql + " AND " + String.Format(_sql_VoterStateIdClause, _searchVoterData.VOTERID);
                    //Add if voters with Phone record is selected by the user
                    sql = _searchVoterData.VOTERWPHONE == false ? sql : sql + " AND " + _sql_NoPhoneRecordsClause;
                    //Add if Gender Data is also selected
                    sql = AddAddlnAndCondition(sql, _sql_GenderRecordsClause, genderData);
                    //Add if Gender Data is also selected
                    sql = AddAddlnAndCondition(sql, _sql_MaritalStatusRecordsClause, marstatusData);
                    //Add if Age Group Data is also selected
                    sql = AddAddlnAndCondition(sql, _sql_AgeGroupRecordsClause, agegroupsData);
                    //Add if Occupation Data is also selected
                    sql = AddAddlnAndCondition(sql, _sql_OccupationRecordsClause, occupationData);
                    //Add if House Hold data is available
                    sql = hhCountData == 0 ? sql : sql + " AND " + String.Format(_sql_HHCountClause, hhCountData);
                    //Now get count
                    count = GetCountForSelection(sql);
                    ctrAllCollection = CreateCounterCollection(ctrAllCollection, _estIncome.Replace("'", "").Trim() + " (" + count + ")", "s", CounterSeq);
                }
            }
            #endregion
            
            #endregion

            #region [[ Political Section ]]

            #region [[ For Party Selection : 17 : All above ]]
            //For Party 
            if (!string.IsNullOrEmpty(partyData))
            {
                CounterSeq = CounterSeq + 1;
                sql = _sql_MasterWithAllJoins + WhereClause + String.Format(_sql_PartyRecordsClause, partyData);
                //Add if State is selected
                sql = AddAddlnAndCondition(sql, _sql_StateRecordsClause, statelist);
                //Add if CongDist is selected
                sql = AddAddlnAndCondition(sql, _sql_CongDataRecordsClause, congDatalist);
                //Add if StateSenate is also selected
                sql = AddAddlnAndCondition(sql, _sql_StateSenateRecordsClause, stateSenateData);
                //Add if State House Assy is also selected
                sql = AddAddlnAndCondition(sql, _sql_StateHouseAssyRecordsClause, stateHouseAssyData);
                //Add if State County Data is also selected
                sql = AddAddlnAndCondition(sql, _sql_CountyRecordsClause, stateCountyData);
                //Add if City Data is also selected
                sql = AddAddlnAndCondition(sql, _sql_CityRecordsClause, cityData);
                //Add if State Precint Data is also selected
                sql = AddAddlnAndCondition(sql, _sql_StatePrecinctRecordsClause, statePrecinctData);
                //Add if Zip Data is also selected
                sql = AddAddlnAndCondition(sql, _sql_ZipRecordsClause, zipData);
                //Add if First Name Data is also selected
                sql = AddAddlnAndCondition(sql, _sql_FirstNameRecordsClause, firstNameData);
                //Add if Last Name Data is also selected
                sql = AddAddlnAndCondition(sql, _sql_LastNameRecordsClause, lastNameData);
                //Add if Voter Id is specified by the user
                sql = string.IsNullOrEmpty(_searchVoterData.VOTERID) ? sql : sql + " AND " + String.Format(_sql_VoterStateIdClause, _searchVoterData.VOTERID);
                //Add if voters with Phone record is selected by the user
                sql = _searchVoterData.VOTERWPHONE == false ? sql : sql + " AND " + _sql_NoPhoneRecordsClause;
                //Add if Gender Data is also selected
                sql = AddAddlnAndCondition(sql, _sql_GenderRecordsClause, genderData);
                //Add if Gender Data is also selected
                sql = AddAddlnAndCondition(sql, _sql_MaritalStatusRecordsClause, marstatusData);
                //Add if Age Group Data is also selected
                sql = AddAddlnAndCondition(sql, _sql_AgeGroupRecordsClause, agegroupsData);
                //Add if Occupation Data is also selected
                sql = AddAddlnAndCondition(sql, _sql_OccupationRecordsClause, occupationData);
                //Add if House Hold data is available
                sql = hhCountData == 0 ? sql : sql + " AND " + String.Format(_sql_HHCountClause, hhCountData);
                //Add if Estimated Income Data is also selected
                sql = AddAddlnAndCondition(sql, _sql_EstIncomeRecordsClause, estIncomeData);
                //Now get count
                count = GetCountForSelection(sql);
                //Now add in the collection
                ctrCollection = CreateCounterCollection(ctrCollection, (_searchVoterData.SELPARTYS.Count() > 1 ? "Parties" : "Party") + " (" + count + ")", "m", CounterSeq);
                //Also store in All Collection
                ctrAllCollection = CreateCounterCollection(ctrAllCollection, (_searchVoterData.SELPARTYS.Count() > 1 ? "Parties" : "Party") + " (" + count + ")", "m", CounterSeq);
            }
            
            //For more than one Party Group Selection
            if (_searchVoterData.SELPARTYS.Count() > 0)
            {
                subCountAvl = true; // We are marking something here so we know that subcount is available for this search
                for (int i = 0; i <= _searchVoterData.SELPARTYS.Count() - 1; i++)
                {
                    CounterSeq = CounterSeq + 1;
                    string _partygrp = "'" + _searchVoterData.SELPARTYS[i].Trim() + "'";
                    //for this Party 
                    sql = _sql_MasterWithAllJoins + WhereClause + String.Format(_sql_PartyRecordsClause, _partygrp);
                    //Add if State is selected
                    sql = AddAddlnAndCondition(sql, _sql_StateRecordsClause, statelist);
                    //Add if CongDist is selected
                    sql = AddAddlnAndCondition(sql, _sql_CongDataRecordsClause, congDatalist);
                    //Add if StateSenate is also selected
                    sql = AddAddlnAndCondition(sql, _sql_StateSenateRecordsClause, stateSenateData);
                    //Add if State House Assy is also selected
                    sql = AddAddlnAndCondition(sql, _sql_StateHouseAssyRecordsClause, stateHouseAssyData);
                    //Add if State County Data is also selected
                    sql = AddAddlnAndCondition(sql, _sql_CountyRecordsClause, stateCountyData);
                    //Add if City Data is also selected
                    sql = AddAddlnAndCondition(sql, _sql_CityRecordsClause, cityData);
                    //Add if State Precint Data is also selected
                    sql = AddAddlnAndCondition(sql, _sql_StatePrecinctRecordsClause, statePrecinctData);
                    //Add if Zip Data is also selected
                    sql = AddAddlnAndCondition(sql, _sql_ZipRecordsClause, zipData);
                    //Add if First Name Data is also selected
                    sql = AddAddlnAndCondition(sql, _sql_FirstNameRecordsClause, firstNameData);
                    //Add if Last Name Data is also selected
                    sql = AddAddlnAndCondition(sql, _sql_LastNameRecordsClause, lastNameData);
                    //Add if Voter Id is specified by the user
                    sql = string.IsNullOrEmpty(_searchVoterData.VOTERID) ? sql : sql + " AND " + String.Format(_sql_VoterStateIdClause, _searchVoterData.VOTERID);
                    //Add if voters with Phone record is selected by the user
                    sql = _searchVoterData.VOTERWPHONE == false ? sql : sql + " AND " + _sql_NoPhoneRecordsClause;
                    //Add if Gender Data is also selected
                    sql = AddAddlnAndCondition(sql, _sql_GenderRecordsClause, genderData);
                    //Add if Gender Data is also selected
                    sql = AddAddlnAndCondition(sql, _sql_MaritalStatusRecordsClause, marstatusData);
                    //Add if Age Group Data is also selected
                    sql = AddAddlnAndCondition(sql, _sql_AgeGroupRecordsClause, agegroupsData);
                    //Add if Occupation Data is also selected
                    sql = AddAddlnAndCondition(sql, _sql_OccupationRecordsClause, occupationData);
                    //Add if House Hold data is available
                    sql = hhCountData == 0 ? sql : sql + " AND " + String.Format(_sql_HHCountClause, hhCountData);
                    //Add if Estimated Income Data is also selected
                    sql = AddAddlnAndCondition(sql, _sql_EstIncomeRecordsClause, estIncomeData);
                    //Now get count
                    count = GetCountForSelection(sql);
                    //Add in the collection
                    ctrAllCollection = CreateCounterCollection(ctrAllCollection, _partygrp.Replace("'", "").Trim() + " (" + count + ")", "s", CounterSeq);
                }
            }
            #endregion
            
            #region [[ For HH Party Selection : 18 ]]
            //Party Group Selection

            //For House Party 
            if (!string.IsNullOrEmpty(hhpartyData))
            {
                CounterSeq = CounterSeq + 1;
                sql = _sql_MasterWithAllJoins + WhereClause + String.Format(_sql_HHPartyRecordsClause, hhpartyData);
                //Add if State is selected
                sql = AddAddlnAndCondition(sql, _sql_StateRecordsClause, statelist);
                //Add if CongDist is selected
                sql = AddAddlnAndCondition(sql, _sql_CongDataRecordsClause, congDatalist);
                //Add if StateSenate is also selected
                sql = AddAddlnAndCondition(sql, _sql_StateSenateRecordsClause, stateSenateData);
                //Add if State House Assy is also selected
                sql = AddAddlnAndCondition(sql, _sql_StateHouseAssyRecordsClause, stateHouseAssyData);
                //Add if State County Data is also selected
                sql = AddAddlnAndCondition(sql, _sql_CountyRecordsClause, stateCountyData);
                //Add if City Data is also selected
                sql = AddAddlnAndCondition(sql, _sql_CityRecordsClause, cityData);
                //Add if State Precint Data is also selected
                sql = AddAddlnAndCondition(sql, _sql_StatePrecinctRecordsClause, statePrecinctData);
                //Add if Zip Data is also selected
                sql = AddAddlnAndCondition(sql, _sql_ZipRecordsClause, zipData);
                //Add if First Name Data is also selected
                sql = AddAddlnAndCondition(sql, _sql_FirstNameRecordsClause, firstNameData);
                //Add if Last Name Data is also selected
                sql = AddAddlnAndCondition(sql, _sql_LastNameRecordsClause, lastNameData);
                //Add if Voter Id is specified by the user
                sql = string.IsNullOrEmpty(_searchVoterData.VOTERID) ? sql : sql + " AND " + String.Format(_sql_VoterStateIdClause, _searchVoterData.VOTERID);
                //Add if voters with Phone record is selected by the user
                sql = _searchVoterData.VOTERWPHONE == false ? sql : sql + " AND " + _sql_NoPhoneRecordsClause;
                //Add if Gender Data is also selected
                sql = AddAddlnAndCondition(sql, _sql_GenderRecordsClause, genderData);
                //Add if Gender Data is also selected
                sql = AddAddlnAndCondition(sql, _sql_MaritalStatusRecordsClause, marstatusData);
                //Add if Age Group Data is also selected
                sql = AddAddlnAndCondition(sql, _sql_AgeGroupRecordsClause, agegroupsData);
                //Add if Occupation Data is also selected
                sql = AddAddlnAndCondition(sql, _sql_OccupationRecordsClause, occupationData);
                //Add if House Hold data is available
                sql = hhCountData == 0 ? sql : sql + " AND " + String.Format(_sql_HHCountClause, hhCountData);
                //Add if Estimated Income Data is also selected
                sql = AddAddlnAndCondition(sql, _sql_EstIncomeRecordsClause, estIncomeData);
                //Add if House Hold Party Data is also selected
                sql = AddAddlnAndCondition(sql, _sql_PartyRecordsClause, partyData);
                count = GetCountForSelection(sql);
                //Now add in the collection
                ctrCollection = CreateCounterCollection(ctrCollection, (_searchVoterData.SELHHPARTYS.Count() > 1 ? "HHParties" : "HHParty") + " (" + count + ")", "m", CounterSeq);
                //Also store in All Collection
                ctrAllCollection = CreateCounterCollection(ctrAllCollection, (_searchVoterData.SELHHPARTYS.Count() > 1 ? "HHParties" : "HHParty") + " (" + count + ")", "m", CounterSeq);
            }
            
            //For more than one Party Group Selection
            if (_searchVoterData.SELHHPARTYS.Count() > 0)
            {
                subCountAvl = true; // We are marking something here so we know that subcount is available for this search
                for (int i = 0; i <= _searchVoterData.SELHHPARTYS.Count() - 1; i++)
                {
                    CounterSeq = CounterSeq + 1;
                    string _hhpartygrp = "'" + _searchVoterData.SELHHPARTYS[i].Trim() + "'";
                    //for this House hold Party 
                    sql = _sql_MasterWithAllJoins + WhereClause + String.Format(_sql_HHPartyRecordsClause, _hhpartygrp);
                    //Add if State is selected
                    sql = AddAddlnAndCondition(sql, _sql_StateRecordsClause, statelist);
                    //Add if CongDist is selected
                    sql = AddAddlnAndCondition(sql, _sql_CongDataRecordsClause, congDatalist);
                    //Add if StateSenate is also selected
                    sql = AddAddlnAndCondition(sql, _sql_StateSenateRecordsClause, stateSenateData);
                    //Add if State House Assy is also selected
                    sql = AddAddlnAndCondition(sql, _sql_StateHouseAssyRecordsClause, stateHouseAssyData);
                    //Add if State County Data is also selected
                    sql = AddAddlnAndCondition(sql, _sql_CountyRecordsClause, stateCountyData);
                    //Add if City Data is also selected
                    sql = AddAddlnAndCondition(sql, _sql_CityRecordsClause, cityData);
                    //Add if State Precint Data is also selected
                    sql = AddAddlnAndCondition(sql, _sql_StatePrecinctRecordsClause, statePrecinctData);
                    //Add if Zip Data is also selected
                    sql = AddAddlnAndCondition(sql, _sql_ZipRecordsClause, zipData);
                    //Add if First Name Data is also selected
                    sql = AddAddlnAndCondition(sql, _sql_FirstNameRecordsClause, firstNameData);
                    //Add if Last Name Data is also selected
                    sql = AddAddlnAndCondition(sql, _sql_LastNameRecordsClause, lastNameData);
                    //Add if Voter Id is specified by the user
                    sql = string.IsNullOrEmpty(_searchVoterData.VOTERID) ? sql : sql + " AND " + String.Format(_sql_VoterStateIdClause, _searchVoterData.VOTERID);
                    //Add if voters with Phone record is selected by the user
                    sql = _searchVoterData.VOTERWPHONE == false ? sql : sql + " AND " + _sql_NoPhoneRecordsClause;
                    //Add if Gender Data is also selected
                    sql = AddAddlnAndCondition(sql, _sql_GenderRecordsClause, genderData);
                    //Add if Gender Data is also selected
                    sql = AddAddlnAndCondition(sql, _sql_MaritalStatusRecordsClause, marstatusData);
                    //Add if Age Group Data is also selected
                    sql = AddAddlnAndCondition(sql, _sql_AgeGroupRecordsClause, agegroupsData);
                    //Add if Occupation Data is also selected
                    sql = AddAddlnAndCondition(sql, _sql_OccupationRecordsClause, occupationData);
                    //Add if House Hold data is available
                    sql = hhCountData == 0 ? sql : sql + " AND " + String.Format(_sql_HHCountClause, hhCountData);
                    //Add if Estimated Income Data is also selected
                    sql = AddAddlnAndCondition(sql, _sql_EstIncomeRecordsClause, estIncomeData);
                    //Add if House Hold Party Data is also selected
                    sql = AddAddlnAndCondition(sql, _sql_PartyRecordsClause, partyData);
                    //Now get count
                    count = GetCountForSelection(sql);
                    //Add in collection
                    ctrAllCollection = CreateCounterCollection(ctrAllCollection, _hhpartygrp.Replace("'", "").Trim() + " (" + count + ")", "s", CounterSeq);
                }
            }
            #endregion

            #region [[ For Election : 21 ]]
            //For Election
            if (!string.IsNullOrEmpty(electionData))
            {
                CounterSeq = CounterSeq + 1;
                sql = _sql_MasterWithAllJoins +  WhereClause + String.Format(_sql_ElectionClause, electionData);
                //Add if State is selected
                sql = AddAddlnAndCondition(sql, _sql_StateRecordsClause, statelist);
                //Add if CongDist is selected
                sql = AddAddlnAndCondition(sql, _sql_CongDataRecordsClause, congDatalist);
                //Add if StateSenate is also selected
                sql = AddAddlnAndCondition(sql, _sql_StateSenateRecordsClause, stateSenateData);
                //Add if State House Assy is also selected
                sql = AddAddlnAndCondition(sql, _sql_StateHouseAssyRecordsClause, stateHouseAssyData);
                //Add if State County Data is also selected
                sql = AddAddlnAndCondition(sql, _sql_CountyRecordsClause, stateCountyData);
                //Add if City Data is also selected
                sql = AddAddlnAndCondition(sql, _sql_CityRecordsClause, cityData);
                //Add if State Precint Data is also selected
                sql = AddAddlnAndCondition(sql, _sql_StatePrecinctRecordsClause, statePrecinctData);
                //Add if Zip Data is also selected
                sql = AddAddlnAndCondition(sql, _sql_ZipRecordsClause, zipData);
                //Add if First Name Data is also selected
                sql = AddAddlnAndCondition(sql, _sql_FirstNameRecordsClause, firstNameData);
                //Add if Last Name Data is also selected
                sql = AddAddlnAndCondition(sql, _sql_LastNameRecordsClause, lastNameData);
                //Add if Voter Id is specified by the user
                sql = string.IsNullOrEmpty(_searchVoterData.VOTERID) ? sql : sql + " AND " + String.Format(_sql_VoterStateIdClause, _searchVoterData.VOTERID);
                //Add if voters with Phone record is selected by the user
                sql = _searchVoterData.VOTERWPHONE == false ? sql : sql + " AND " + _sql_NoPhoneRecordsClause;
                //Add if Gender Data is also selected
                sql = AddAddlnAndCondition(sql, _sql_GenderRecordsClause, genderData);
                //Add if Gender Data is also selected
                sql = AddAddlnAndCondition(sql, _sql_MaritalStatusRecordsClause, marstatusData);
                //Add if Age Group Data is also selected
                sql = AddAddlnAndCondition(sql, _sql_AgeGroupRecordsClause, agegroupsData);
                //Add if Occupation Data is also selected
                sql = AddAddlnAndCondition(sql, _sql_OccupationRecordsClause, occupationData);
                //Add if House Hold data is available
                sql = hhCountData == 0 ? sql : sql + " AND " + String.Format(_sql_HHCountClause, hhCountData);
                //Add if Estimated Income Data is also selected
                sql = AddAddlnAndCondition(sql, _sql_EstIncomeRecordsClause, estIncomeData);
                //Add if House Hold Party Data is also selected
                sql = AddAddlnAndCondition(sql, _sql_PartyRecordsClause, partyData);
                //Add if House Hold Party Data is also selected
                sql = AddAddlnAndCondition(sql, _sql_HHPartyRecordsClause, hhpartyData);
                //Check if Reg Start and End Date are available
                if (!string.IsNullOrEmpty(regStartDate) && !string.IsNullOrEmpty(regEndDate))
                {
                    sql = sql + " AND " + String.Format(_sql_RegistrationDateRecordsClause, regStartDate, regEndDate);
                }
                //Add if Voter Freq data is available
                sql = voteFreqData == 0 ? sql : sql + " AND " + String.Format(_sql_VoteFrequencyClause, voteFreqData);
                count = GetCountForSelection(sql);
                //Now add in the collection
                ctrCollection = CreateCounterCollection(ctrCollection, (_searchVoterData.SELELECTIONS.Count() > 1 ? "Elections" : "Election") + " (" + count + ")", "m", CounterSeq);
                //Also store in All Collection
                ctrAllCollection = CreateCounterCollection(ctrAllCollection, (_searchVoterData.SELELECTIONS.Count() > 1 ? "Elections" : "Election") + " (" + count + ")", "m", CounterSeq);
            }
            //For more than one Election Selection
            if (_searchVoterData.SELELECTIONS.Count() > 0)
            {
                subCountAvl = true; // We are marking something here so we know that subcount is available for this search
                for (int i = 0; i <= _searchVoterData.SELELECTIONS.Count() - 1; i++)
                {
                    CounterSeq = CounterSeq + 1;
                    string _election = "'" + _searchVoterData.SELELECTIONS[i].Trim() + "'";
                    //for this Election 
                    sql = _sql_MasterWithAllJoins + WhereClause + String.Format(_sql_ElectionClause, _election);
                    //Add if State is selected
                    sql = AddAddlnAndCondition(sql, _sql_StateRecordsClause, statelist);
                    //Add if CongDist is selected
                    sql = AddAddlnAndCondition(sql, _sql_CongDataRecordsClause, congDatalist);
                    //Add if StateSenate is also selected
                    sql = AddAddlnAndCondition(sql, _sql_StateSenateRecordsClause, stateSenateData);
                    //Add if State House Assy is also selected
                    sql = AddAddlnAndCondition(sql, _sql_StateHouseAssyRecordsClause, stateHouseAssyData);
                    //Add if State County Data is also selected
                    sql = AddAddlnAndCondition(sql, _sql_CountyRecordsClause, stateCountyData);
                    //Add if City Data is also selected
                    sql = AddAddlnAndCondition(sql, _sql_CityRecordsClause, cityData);
                    //Add if State Precint Data is also selected
                    sql = AddAddlnAndCondition(sql, _sql_StatePrecinctRecordsClause, statePrecinctData);
                    //Add if Zip Data is also selected
                    sql = AddAddlnAndCondition(sql, _sql_ZipRecordsClause, zipData);
                    //Add if First Name Data is also selected
                    sql = AddAddlnAndCondition(sql, _sql_FirstNameRecordsClause, firstNameData);
                    //Add if Last Name Data is also selected
                    sql = AddAddlnAndCondition(sql, _sql_LastNameRecordsClause, lastNameData);
                    //Add if Voter Id is specified by the user
                    sql = string.IsNullOrEmpty(_searchVoterData.VOTERID) ? sql : sql + " AND " + String.Format(_sql_VoterStateIdClause, _searchVoterData.VOTERID);
                    //Add if voters with Phone record is selected by the user
                    sql = _searchVoterData.VOTERWPHONE == false ? sql : sql + " AND " + _sql_NoPhoneRecordsClause;
                    //Add if Gender Data is also selected
                    sql = AddAddlnAndCondition(sql, _sql_GenderRecordsClause, genderData);
                    //Add if Gender Data is also selected
                    sql = AddAddlnAndCondition(sql, _sql_MaritalStatusRecordsClause, marstatusData);
                    //Add if Age Group Data is also selected
                    sql = AddAddlnAndCondition(sql, _sql_AgeGroupRecordsClause, agegroupsData);
                    //Add if Occupation Data is also selected
                    sql = AddAddlnAndCondition(sql, _sql_OccupationRecordsClause, occupationData);
                    //Add if House Hold data is available
                    sql = hhCountData == 0 ? sql : sql + " AND " + String.Format(_sql_HHCountClause, hhCountData);
                    //Add if Estimated Income Data is also selected
                    sql = AddAddlnAndCondition(sql, _sql_EstIncomeRecordsClause, estIncomeData);
                    //Add if House Hold Party Data is also selected
                    sql = AddAddlnAndCondition(sql, _sql_PartyRecordsClause, partyData);
                    //Add if House Hold Party Data is also selected
                    sql = AddAddlnAndCondition(sql, _sql_HHPartyRecordsClause, hhpartyData);
                    //Check if Reg Start and End Date are available
                    if (!string.IsNullOrEmpty(regStartDate) && !string.IsNullOrEmpty(regEndDate))
                    {
                        sql = sql + " AND " + String.Format(_sql_RegistrationDateRecordsClause, regStartDate, regEndDate);
                    }
                    //Add if Voter Freq data is available
                    sql = voteFreqData == 0 ? sql : sql + " AND " + String.Format(_sql_VoteFrequencyClause, voteFreqData);
                    //Now get count
                    count = GetCountForSelection(sql);
                    //Add in the collection
                    ctrAllCollection = CreateCounterCollection(ctrAllCollection, _election.Replace("'", "").Trim() + " (" + count + ")", "s", CounterSeq);
                }
            }
            #endregion

            #region [[ For Election Year : 22 ]]
            //For Election Year
            if (!string.IsNullOrEmpty(electionYearData))
            {
                CounterSeq = CounterSeq + 1;
                sql = _sql_MasterWithAllJoins +  WhereClause + String.Format(_sql_ElectionYearClause, electionYearData);
                //Add if State is selected
                sql = AddAddlnAndCondition(sql, _sql_StateRecordsClause, statelist);
                //Add if CongDist is selected
                sql = AddAddlnAndCondition(sql, _sql_CongDataRecordsClause, congDatalist);
                //Add if StateSenate is also selected
                sql = AddAddlnAndCondition(sql, _sql_StateSenateRecordsClause, stateSenateData);
                //Add if State House Assy is also selected
                sql = AddAddlnAndCondition(sql, _sql_StateHouseAssyRecordsClause, stateHouseAssyData);
                //Add if State County Data is also selected
                sql = AddAddlnAndCondition(sql, _sql_CountyRecordsClause, stateCountyData);
                //Add if City Data is also selected
                sql = AddAddlnAndCondition(sql, _sql_CityRecordsClause, cityData);
                //Add if State Precint Data is also selected
                sql = AddAddlnAndCondition(sql, _sql_StatePrecinctRecordsClause, statePrecinctData);
                //Add if Zip Data is also selected
                sql = AddAddlnAndCondition(sql, _sql_ZipRecordsClause, zipData);
                //Add if First Name Data is also selected
                sql = AddAddlnAndCondition(sql, _sql_FirstNameRecordsClause, firstNameData);
                //Add if Last Name Data is also selected
                sql = AddAddlnAndCondition(sql, _sql_LastNameRecordsClause, lastNameData);
                //Add if Voter Id is specified by the user
                sql = string.IsNullOrEmpty(_searchVoterData.VOTERID) ? sql : sql + " AND " + String.Format(_sql_VoterStateIdClause, _searchVoterData.VOTERID);
                //Add if voters with Phone record is selected by the user
                sql = _searchVoterData.VOTERWPHONE == false ? sql : sql + " AND " + _sql_NoPhoneRecordsClause;
                //Add if Gender Data is also selected
                sql = AddAddlnAndCondition(sql, _sql_GenderRecordsClause, genderData);
                //Add if Gender Data is also selected
                sql = AddAddlnAndCondition(sql, _sql_MaritalStatusRecordsClause, marstatusData);
                //Add if Age Group Data is also selected
                sql = AddAddlnAndCondition(sql, _sql_AgeGroupRecordsClause, agegroupsData);
                //Add if Occupation Data is also selected
                sql = AddAddlnAndCondition(sql, _sql_OccupationRecordsClause, occupationData);
                //Add if House Hold data is available
                sql = hhCountData == 0 ? sql : sql + " AND " + String.Format(_sql_HHCountClause, hhCountData);
                //Add if Estimated Income Data is also selected
                sql = AddAddlnAndCondition(sql, _sql_EstIncomeRecordsClause, estIncomeData);
                //Add if House Hold Party Data is also selected
                sql = AddAddlnAndCondition(sql, _sql_PartyRecordsClause, partyData);
                //Add if House Hold Party Data is also selected
                sql = AddAddlnAndCondition(sql, _sql_HHPartyRecordsClause, hhpartyData);
                //Check if Reg Start and End Date are available
                if (!string.IsNullOrEmpty(regStartDate) && !string.IsNullOrEmpty(regEndDate))
                {
                    sql = sql + " AND " + String.Format(_sql_RegistrationDateRecordsClause, regStartDate, regEndDate);
                }
                //Add if Voter Freq data is available
                sql = voteFreqData == 0 ? sql : sql + " AND " + String.Format(_sql_VoteFrequencyClause, voteFreqData);
                //Add if Election data is available
                sql = AddAddlnAndCondition(sql, _sql_ElectionClause, electionData);
                //Now get count
                count = GetCountForSelection(sql);
                //Now add in the collection
                ctrCollection = CreateCounterCollection(ctrCollection, (_searchVoterData.SELELECTIONYEARS.Count() > 1 ? "ElectionYears" : "ElectionYear") + " (" + count + ")", "m", CounterSeq);
                //Also store in All Collection
                ctrAllCollection = CreateCounterCollection(ctrAllCollection, (_searchVoterData.SELELECTIONYEARS.Count() > 1 ? "ElectionYears" : "ElectionYear") + " (" + count + ")", "m", CounterSeq);
            }
            //For more than one Election Year Selection
            if (_searchVoterData.SELELECTIONYEARS.Count() > 0)
            {
                subCountAvl = true; // We are marking something here so we know that subcount is available for this search
                for (int i = 0; i <= _searchVoterData.SELELECTIONYEARS.Count() - 1; i++)
                {
                    CounterSeq = CounterSeq + 1;
                    string _electionYear = "'" + _searchVoterData.SELELECTIONYEARS[i].Trim() + "'";
                    //for this Election Year
                    sql = _sql_MasterWithAllJoins + WhereClause + String.Format(_sql_ElectionYearClause, _electionYear);
                    //Add if State is selected
                    sql = AddAddlnAndCondition(sql, _sql_StateRecordsClause, statelist);
                    //Add if CongDist is selected
                    sql = AddAddlnAndCondition(sql, _sql_CongDataRecordsClause, congDatalist);
                    //Add if StateSenate is also selected
                    sql = AddAddlnAndCondition(sql, _sql_StateSenateRecordsClause, stateSenateData);
                    //Add if State House Assy is also selected
                    sql = AddAddlnAndCondition(sql, _sql_StateHouseAssyRecordsClause, stateHouseAssyData);
                    //Add if State County Data is also selected
                    sql = AddAddlnAndCondition(sql, _sql_CountyRecordsClause, stateCountyData);
                    //Add if City Data is also selected
                    sql = AddAddlnAndCondition(sql, _sql_CityRecordsClause, cityData);
                    //Add if State Precint Data is also selected
                    sql = AddAddlnAndCondition(sql, _sql_StatePrecinctRecordsClause, statePrecinctData);
                    //Add if Zip Data is also selected
                    sql = AddAddlnAndCondition(sql, _sql_ZipRecordsClause, zipData);
                    //Add if First Name Data is also selected
                    sql = AddAddlnAndCondition(sql, _sql_FirstNameRecordsClause, firstNameData);
                    //Add if Last Name Data is also selected
                    sql = AddAddlnAndCondition(sql, _sql_LastNameRecordsClause, lastNameData);
                    //Add if Voter Id is specified by the user
                    sql = string.IsNullOrEmpty(_searchVoterData.VOTERID) ? sql : sql + " AND " + String.Format(_sql_VoterStateIdClause, _searchVoterData.VOTERID);
                    //Add if voters with Phone record is selected by the user
                    sql = _searchVoterData.VOTERWPHONE == false ? sql : sql + " AND " + _sql_NoPhoneRecordsClause;
                    //Add if Gender Data is also selected
                    sql = AddAddlnAndCondition(sql, _sql_GenderRecordsClause, genderData);
                    //Add if Gender Data is also selected
                    sql = AddAddlnAndCondition(sql, _sql_MaritalStatusRecordsClause, marstatusData);
                    //Add if Age Group Data is also selected
                    sql = AddAddlnAndCondition(sql, _sql_AgeGroupRecordsClause, agegroupsData);
                    //Add if Occupation Data is also selected
                    sql = AddAddlnAndCondition(sql, _sql_OccupationRecordsClause, occupationData);
                    //Add if House Hold data is available
                    sql = hhCountData == 0 ? sql : sql + " AND " + String.Format(_sql_HHCountClause, hhCountData);
                    //Add if Estimated Income Data is also selected
                    sql = AddAddlnAndCondition(sql, _sql_EstIncomeRecordsClause, estIncomeData);
                    //Add if House Hold Party Data is also selected
                    sql = AddAddlnAndCondition(sql, _sql_PartyRecordsClause, partyData);
                    //Add if House Hold Party Data is also selected
                    sql = AddAddlnAndCondition(sql, _sql_HHPartyRecordsClause, hhpartyData);
                    //Check if Reg Start and End Date are available
                    if (!string.IsNullOrEmpty(regStartDate) && !string.IsNullOrEmpty(regEndDate))
                    {
                        sql = sql + " AND " + String.Format(_sql_RegistrationDateRecordsClause, regStartDate, regEndDate);
                    }
                    //Add if Voter Freq data is available
                    sql = voteFreqData == 0 ? sql : sql + " AND " + String.Format(_sql_VoteFrequencyClause, voteFreqData);
                    //Add if Election data is available
                    sql = AddAddlnAndCondition(sql, _sql_ElectionClause, electionData);
                    //Now get count
                    count = GetCountForSelection(sql);
                    //Add to the collection
                    ctrAllCollection = CreateCounterCollection(ctrAllCollection, _electionYear.Replace("'", "").Trim() + " (" + count + ")", "s", CounterSeq);
                }
            }
            #endregion

            #endregion

            #region [[ List Section ]]

            #region [[ For List Data : 23 ]]
            //For List Data
            if (!string.IsNullOrEmpty(listData))
            {
                CounterSeq = CounterSeq + 1;
                //Let us have counter for City selection
                sql = _sql_MasterWithAllJoins + WhereClause + String.Format(_sql_ListRecordsClause, listData);
                //Add if State is selected
                sql = AddAddlnAndCondition(sql, _sql_StateRecordsClause, statelist);
                //Add if CongDist is selected
                sql = AddAddlnAndCondition(sql, _sql_CongDataRecordsClause, congDatalist);
                //Add if StateSenate is also selected
                sql = AddAddlnAndCondition(sql, _sql_StateSenateRecordsClause, stateSenateData);
                //Add if State House Assy is also selected
                sql = AddAddlnAndCondition(sql, _sql_StateHouseAssyRecordsClause, stateHouseAssyData);
                //Add if State County Data is also selected
                sql = AddAddlnAndCondition(sql, _sql_CountyRecordsClause, stateCountyData);
                //Add if City Data is also selected
                sql = AddAddlnAndCondition(sql, _sql_CityRecordsClause, cityData);
                //Add if State Precint Data is also selected
                sql = AddAddlnAndCondition(sql, _sql_StatePrecinctRecordsClause, statePrecinctData);
                //Add if Zip Data is also selected
                sql = AddAddlnAndCondition(sql, _sql_ZipRecordsClause, zipData);
                //Add if First Name Data is also selected
                sql = AddAddlnAndCondition(sql, _sql_FirstNameRecordsClause, firstNameData);
                //Add if Last Name Data is also selected
                sql = AddAddlnAndCondition(sql, _sql_LastNameRecordsClause, lastNameData);
                //Add if Voter Id is specified by the user
                sql = string.IsNullOrEmpty(_searchVoterData.VOTERID) ? sql : sql + " AND " + String.Format(_sql_VoterStateIdClause, _searchVoterData.VOTERID);
                //Add if voters with Phone record is selected by the user
                sql = _searchVoterData.VOTERWPHONE == false ? sql : sql + " AND " + _sql_NoPhoneRecordsClause;
                //Add if Gender Data is also selected
                sql = AddAddlnAndCondition(sql, _sql_GenderRecordsClause, genderData);
                //Add if Gender Data is also selected
                sql = AddAddlnAndCondition(sql, _sql_MaritalStatusRecordsClause, marstatusData);
                //Add if Age Group Data is also selected
                sql = AddAddlnAndCondition(sql, _sql_AgeGroupRecordsClause, agegroupsData);
                //Add if Occupation Data is also selected
                sql = AddAddlnAndCondition(sql, _sql_OccupationRecordsClause, occupationData);
                //Add if House Hold data is available
                sql = hhCountData == 0 ? sql : sql + " AND " + String.Format(_sql_HHCountClause, hhCountData);
                //Add if Estimated Income Data is also selected
                sql = AddAddlnAndCondition(sql, _sql_EstIncomeRecordsClause, estIncomeData);
                //Add if House Hold Party Data is also selected
                sql = AddAddlnAndCondition(sql, _sql_PartyRecordsClause, partyData);
                //Add if House Hold Party Data is also selected
                sql = AddAddlnAndCondition(sql, _sql_HHPartyRecordsClause, hhpartyData);
                //Check if Reg Start and End Date are available
                if (!string.IsNullOrEmpty(regStartDate) && !string.IsNullOrEmpty(regEndDate))
                {
                    sql = sql + " AND " + String.Format(_sql_RegistrationDateRecordsClause, regStartDate, regEndDate);
                }
                //Add if Voter Freq data is available
                sql = voteFreqData == 0 ? sql : sql + " AND " + String.Format(_sql_VoteFrequencyClause, voteFreqData);
                //Add if Election data is available
                sql = AddAddlnAndCondition(sql, _sql_ElectionClause, electionData);
                //Add if Election Year is available
                sql = AddAddlnAndCondition(sql, _sql_ElectionYearClause, electionYearData);
                //Now get count
                count = GetCountForSelection(sql);
                //Now add in the collection
                ctrCollection = CreateCounterCollection(ctrCollection, (_searchVoterData.SELVMLISTS.Count() > 1 ? "Lists" : "List") + " (" + count + ")", "m", CounterSeq);
                //Also store in All Collection
                ctrAllCollection = CreateCounterCollection(ctrAllCollection, (_searchVoterData.SELVMLISTS.Count() > 1 ? "Lists" : "List") + " (" + count + ")", "m", CounterSeq);
            }
            //For more than one List 
            if (_searchVoterData.SELVMLISTS.Count() > 0)
            {
                subCountAvl = true; // We are marking something here so we know that subcount is available for this search
                for (int i = 0; i <= _searchVoterData.SELVMLISTS.Count() - 1; i++)
                {
                    CounterSeq = CounterSeq + 1;
                    string _list = "'" + _searchVoterData.SELVMLISTS[i].Trim() + "'";
                    //for this List 
                    sql = _sql_MasterWithAllJoins + WhereClause + String.Format(_sql_ListRecordsClause, _list);
                    //Add if State is selected
                    sql = AddAddlnAndCondition(sql, _sql_StateRecordsClause, statelist);
                    //Add if CongDist is selected
                    sql = AddAddlnAndCondition(sql, _sql_CongDataRecordsClause, congDatalist);
                    //Add if StateSenate is also selected
                    sql = AddAddlnAndCondition(sql, _sql_StateSenateRecordsClause, stateSenateData);
                    //Add if State House Assy is also selected
                    sql = AddAddlnAndCondition(sql, _sql_StateHouseAssyRecordsClause, stateHouseAssyData);
                    //Add if State County Data is also selected
                    sql = AddAddlnAndCondition(sql, _sql_CountyRecordsClause, stateCountyData);
                    //Add if City Data is also selected
                    sql = AddAddlnAndCondition(sql, _sql_CityRecordsClause, cityData);
                    //Add if State Precint Data is also selected
                    sql = AddAddlnAndCondition(sql, _sql_StatePrecinctRecordsClause, statePrecinctData);
                    //Add if Zip Data is also selected
                    sql = AddAddlnAndCondition(sql, _sql_ZipRecordsClause, zipData);
                    //Add if First Name Data is also selected
                    sql = AddAddlnAndCondition(sql, _sql_FirstNameRecordsClause, firstNameData);
                    //Add if Last Name Data is also selected
                    sql = AddAddlnAndCondition(sql, _sql_LastNameRecordsClause, lastNameData);
                    //Add if Voter Id is specified by the user
                    sql = string.IsNullOrEmpty(_searchVoterData.VOTERID) ? sql : sql + " AND " + String.Format(_sql_VoterStateIdClause, _searchVoterData.VOTERID);
                    //Add if voters with Phone record is selected by the user
                    sql = _searchVoterData.VOTERWPHONE == false ? sql : sql + " AND " + _sql_NoPhoneRecordsClause;
                    //Add if Gender Data is also selected
                    sql = AddAddlnAndCondition(sql, _sql_GenderRecordsClause, genderData);
                    //Add if Gender Data is also selected
                    sql = AddAddlnAndCondition(sql, _sql_MaritalStatusRecordsClause, marstatusData);
                    //Add if Age Group Data is also selected
                    sql = AddAddlnAndCondition(sql, _sql_AgeGroupRecordsClause, agegroupsData);
                    //Add if Occupation Data is also selected
                    sql = AddAddlnAndCondition(sql, _sql_OccupationRecordsClause, occupationData);
                    //Add if House Hold data is available
                    sql = hhCountData == 0 ? sql : sql + " AND " + String.Format(_sql_HHCountClause, hhCountData);
                    //Add if Estimated Income Data is also selected
                    sql = AddAddlnAndCondition(sql, _sql_EstIncomeRecordsClause, estIncomeData);
                    //Add if House Hold Party Data is also selected
                    sql = AddAddlnAndCondition(sql, _sql_PartyRecordsClause, partyData);
                    //Add if House Hold Party Data is also selected
                    sql = AddAddlnAndCondition(sql, _sql_HHPartyRecordsClause, hhpartyData);
                    //Check if Reg Start and End Date are available
                    if (!string.IsNullOrEmpty(regStartDate) && !string.IsNullOrEmpty(regEndDate))
                    {
                        sql = sql + " AND " + String.Format(_sql_RegistrationDateRecordsClause, regStartDate, regEndDate);
                    }
                    //Add if Voter Freq data is available
                    sql = voteFreqData == 0 ? sql : sql + " AND " + String.Format(_sql_VoteFrequencyClause, voteFreqData);
                    //Add if Election data is available
                    sql = AddAddlnAndCondition(sql, _sql_ElectionClause, electionData);
                    //Add if Election Year is available
                    sql = AddAddlnAndCondition(sql, _sql_ElectionYearClause, electionYearData);
                    //Now get count
                    count = GetCountForSelection(sql);
                    //Now add in the collection
                    ctrAllCollection = CreateCounterCollection(ctrAllCollection, _list.Replace("'", "").Trim() + " (" + count + ")", "s", CounterSeq);
                }
            }
            #endregion

            #region [[ For Supress List : 24 ]]
            //Supress List
            if (!string.IsNullOrEmpty(suplistData))
            {
                CounterSeq = CounterSeq + 1;
                //Let us have counter for City selection
                sql = _sql_MasterWithAllJoins +  WhereClause + String.Format(_sql_SupListRecordsClause, suplistData);
                //Add if State is selected
                sql = AddAddlnAndCondition(sql, _sql_StateRecordsClause, statelist);
                //Add if CongDist is selected
                sql = AddAddlnAndCondition(sql, _sql_CongDataRecordsClause, congDatalist);
                //Add if StateSenate is also selected
                sql = AddAddlnAndCondition(sql, _sql_StateSenateRecordsClause, stateSenateData);
                //Add if State House Assy is also selected
                sql = AddAddlnAndCondition(sql, _sql_StateHouseAssyRecordsClause, stateHouseAssyData);
                //Add if State County Data is also selected
                sql = AddAddlnAndCondition(sql, _sql_CountyRecordsClause, stateCountyData);
                //Add if City Data is also selected
                sql = AddAddlnAndCondition(sql, _sql_CityRecordsClause, cityData);
                //Add if State Precint Data is also selected
                sql = AddAddlnAndCondition(sql, _sql_StatePrecinctRecordsClause, statePrecinctData);
                //Add if Zip Data is also selected
                sql = AddAddlnAndCondition(sql, _sql_ZipRecordsClause, zipData);
                //Add if First Name Data is also selected
                sql = AddAddlnAndCondition(sql, _sql_FirstNameRecordsClause, firstNameData);
                //Add if Last Name Data is also selected
                sql = AddAddlnAndCondition(sql, _sql_LastNameRecordsClause, lastNameData);
                //Add if Voter Id is specified by the user
                sql = string.IsNullOrEmpty(_searchVoterData.VOTERID) ? sql : sql + " AND " + String.Format(_sql_VoterStateIdClause, _searchVoterData.VOTERID);
                //Add if voters with Phone record is selected by the user
                sql = _searchVoterData.VOTERWPHONE == false ? sql : sql + " AND " + _sql_NoPhoneRecordsClause;
                //Add if Gender Data is also selected
                sql = AddAddlnAndCondition(sql, _sql_GenderRecordsClause, genderData);
                //Add if Gender Data is also selected
                sql = AddAddlnAndCondition(sql, _sql_MaritalStatusRecordsClause, marstatusData);
                //Add if Age Group Data is also selected
                sql = AddAddlnAndCondition(sql, _sql_AgeGroupRecordsClause, agegroupsData);
                //Add if Occupation Data is also selected
                sql = AddAddlnAndCondition(sql, _sql_OccupationRecordsClause, occupationData);
                //Add if House Hold data is available
                sql = hhCountData == 0 ? sql : sql + " AND " + String.Format(_sql_HHCountClause, hhCountData);
                //Add if Estimated Income Data is also selected
                sql = AddAddlnAndCondition(sql, _sql_EstIncomeRecordsClause, estIncomeData);
                //Add if House Hold Party Data is also selected
                sql = AddAddlnAndCondition(sql, _sql_PartyRecordsClause, partyData);
                //Add if House Hold Party Data is also selected
                sql = AddAddlnAndCondition(sql, _sql_HHPartyRecordsClause, hhpartyData);
                //Check if Reg Start and End Date are available
                if (!string.IsNullOrEmpty(regStartDate) && !string.IsNullOrEmpty(regEndDate))
                {
                    sql = sql + " AND " + String.Format(_sql_RegistrationDateRecordsClause, regStartDate, regEndDate);
                }
                //Add if Voter Freq data is available
                sql = voteFreqData == 0 ? sql : sql + " AND " + String.Format(_sql_VoteFrequencyClause, voteFreqData);
                //Add if Election data is available
                sql = AddAddlnAndCondition(sql, _sql_ElectionClause, electionData);
                //Add if Election Year is available
                sql = AddAddlnAndCondition(sql, _sql_ElectionYearClause, electionYearData);
                //Add if List data is available
                sql = AddAddlnAndCondition(sql, _sql_ListRecordsClause, listData);
                count = GetCountForSelection(sql);
                //Now add in the collection
                ctrCollection = CreateCounterCollection(ctrCollection, (_searchVoterData.SELSUPVMLISTS.Count() > 1 ? "SuppLists" : "SuppList") + " (" + count + ")", "m", CounterSeq);
                //Also store in All Collection
                ctrAllCollection = CreateCounterCollection(ctrAllCollection, (_searchVoterData.SELSUPVMLISTS.Count() > 1 ? "SuppLists" : "SuppList") + " (" + count + ")", "m", CounterSeq);
            }
            //For more than one Supress List 
            if (_searchVoterData.SELSUPVMLISTS.Count() > 0)
            {
                subCountAvl = true; // We are marking something here so we know that subcount is available for this search
                for (int i = 0; i <= _searchVoterData.SELSUPVMLISTS.Count() - 1; i++)
                {
                    CounterSeq = CounterSeq + 1;
                    string _suplist = "'" + _searchVoterData.SELSUPVMLISTS[i].Trim() + "'";
                    //for this Sup List 
                    sql = _sql_MasterWithAllJoins + WhereClause + String.Format(_sql_SupListRecordsClause, _suplist);
                    //Add if State is selected
                    sql = AddAddlnAndCondition(sql, _sql_StateRecordsClause, statelist);
                    //Add if CongDist is selected
                    sql = AddAddlnAndCondition(sql, _sql_CongDataRecordsClause, congDatalist);
                    //Add if StateSenate is also selected
                    sql = AddAddlnAndCondition(sql, _sql_StateSenateRecordsClause, stateSenateData);
                    //Add if State House Assy is also selected
                    sql = AddAddlnAndCondition(sql, _sql_StateHouseAssyRecordsClause, stateHouseAssyData);
                    //Add if State County Data is also selected
                    sql = AddAddlnAndCondition(sql, _sql_CountyRecordsClause, stateCountyData);
                    //Add if City Data is also selected
                    sql = AddAddlnAndCondition(sql, _sql_CityRecordsClause, cityData);
                    //Add if State Precint Data is also selected
                    sql = AddAddlnAndCondition(sql, _sql_StatePrecinctRecordsClause, statePrecinctData);
                    //Add if Zip Data is also selected
                    sql = AddAddlnAndCondition(sql, _sql_ZipRecordsClause, zipData);
                    //Add if First Name Data is also selected
                    sql = AddAddlnAndCondition(sql, _sql_FirstNameRecordsClause, firstNameData);
                    //Add if Last Name Data is also selected
                    sql = AddAddlnAndCondition(sql, _sql_LastNameRecordsClause, lastNameData);
                    //Add if Voter Id is specified by the user
                    sql = string.IsNullOrEmpty(_searchVoterData.VOTERID) ? sql : sql + " AND " + String.Format(_sql_VoterStateIdClause, _searchVoterData.VOTERID);
                    //Add if voters with Phone record is selected by the user
                    sql = _searchVoterData.VOTERWPHONE == false ? sql : sql + " AND " + _sql_NoPhoneRecordsClause;
                    //Add if Gender Data is also selected
                    sql = AddAddlnAndCondition(sql, _sql_GenderRecordsClause, genderData);
                    //Add if Gender Data is also selected
                    sql = AddAddlnAndCondition(sql, _sql_MaritalStatusRecordsClause, marstatusData);
                    //Add if Age Group Data is also selected
                    sql = AddAddlnAndCondition(sql, _sql_AgeGroupRecordsClause, agegroupsData);
                    //Add if Occupation Data is also selected
                    sql = AddAddlnAndCondition(sql, _sql_OccupationRecordsClause, occupationData);
                    //Add if House Hold data is available
                    sql = hhCountData == 0 ? sql : sql + " AND " + String.Format(_sql_HHCountClause, hhCountData);
                    //Add if Estimated Income Data is also selected
                    sql = AddAddlnAndCondition(sql, _sql_EstIncomeRecordsClause, estIncomeData);
                    //Add if House Hold Party Data is also selected
                    sql = AddAddlnAndCondition(sql, _sql_PartyRecordsClause, partyData);
                    //Add if House Hold Party Data is also selected
                    sql = AddAddlnAndCondition(sql, _sql_HHPartyRecordsClause, hhpartyData);
                    //Check if Reg Start and End Date are available
                    if (!string.IsNullOrEmpty(regStartDate) && !string.IsNullOrEmpty(regEndDate))
                    {
                        sql = sql + " AND " + String.Format(_sql_RegistrationDateRecordsClause, regStartDate, regEndDate);
                    }
                    //Add if Voter Freq data is available
                    sql = voteFreqData == 0 ? sql : sql + " AND " + String.Format(_sql_VoteFrequencyClause, voteFreqData);
                    //Add if Election data is available
                    sql = AddAddlnAndCondition(sql, _sql_ElectionClause, electionData);
                    //Add if Election Year is available
                    sql = AddAddlnAndCondition(sql, _sql_ElectionYearClause, electionYearData);
                    //Add if List data is available
                    sql = AddAddlnAndCondition(sql, _sql_ListRecordsClause, listData);
                    //Now get count
                    count = GetCountForSelection(sql);
                    ctrAllCollection = CreateCounterCollection(ctrAllCollection, _suplist.Replace("'", "").Trim() + " (" + count + ")", "s", CounterSeq);
                }
            }
            #endregion
            
            #endregion
            
            #endregion

            //Create the Voter Bank
            vtrBank.mainCollection = ctrCollection;
            vtrBank.allCollection = ctrAllCollection;
            //return the collection
            return vtrBank;
        }

        public List<VoterCounterData> CreateCounterCollection(List<VoterCounterData> counterCollection, string cntrItem, string Category, int CounterSeq)
        {
            //Create New Item and then add            
            VoterCounterData _vtrItem = new VoterCounterData();
            _vtrItem.CounterInfo = cntrItem;
            _vtrItem.Category = Category;
            _vtrItem.CounterSeq = CounterSeq;
            counterCollection.Add(_vtrItem);
            //return  the collection
            return counterCollection;
        }

        public string GetCountForSelection(string sql)
        {
            _ds = q_library.get_dataset_w_sql__single(session.currentDomain_project._connectionString(), sql, "result", 0, 0);
            //get the count
            int _countNumber = Convert.ToInt32(_ds.Tables[0].Rows[0]["Number"].ToString());
            //return the count
            return _countNumber < 10 ? _countNumber.ToString() : String.Format("{0:0,0}", Convert.ToDouble(_ds.Tables[0].Rows[0]["Number"]));
        }

        public string GetSQLForAdvancedSearchExport(SearchVoterData _searchVoterData)
        {
            //Now gather the Information to create SQL statement
            #region [[Information Processing Here ]]
            //For Arrays
            statelist = GetListFromArray(_searchVoterData.SELSTATES);
            congDatalist = GetListFromArray(_searchVoterData.SELCONGDISTS);
            stateSenateData = GetListFromArray(_searchVoterData.SELSTATESENATES);
            stateHouseAssyData = GetListFromArray(_searchVoterData.SELSTATEHASSYS);
            stateCountyData = GetListFromArray(_searchVoterData.SELSTATECNTYS);
            statePrecinctData = GetListFromArray(_searchVoterData.SELSTATEPRECS);
            cityData = GetListFromArray(_searchVoterData.SELCITYS);
            zipData = GetListFromArray(_searchVoterData.SELZIPS);
            listData = GetListFromArray(_searchVoterData.SELVMLISTS);
            suplistData = GetListFromArray(_searchVoterData.SELSUPVMLISTS);
            firstNameData = GetListFromArray(_searchVoterData.SELFIRSTNAMES);
            lastNameData = GetListFromArray(_searchVoterData.SELLASTNAMES);
            genderData = GetListFromArray(_searchVoterData.SELGENDERS);
            marstatusData = GetListFromArray(_searchVoterData.SELMARSTATUSS);
            occupationData = GetListFromArray(_searchVoterData.SELOCCUPATIONS);
            estIncomeData = GetListFromArray(_searchVoterData.SELESTINCOMES);
            partyData = GetListFromArray(_searchVoterData.SELPARTYS);
            hhpartyData = GetListFromArray(_searchVoterData.SELHHPARTYS);
            agegroupsData = GetListFromArray(_searchVoterData.SELAGEGROUPS);
            electionData = GetListFromArray(_searchVoterData.SELELECTIONS);
            electionYearData = GetListFromArray(_searchVoterData.SELELECTIONYEARS);
            //For other fields like Boolean and Integer
            voterIdData = string.IsNullOrEmpty(_searchVoterData.VOTERID) ? "" : _searchVoterData.VOTERID.Trim();
            hhCountData = string.IsNullOrEmpty(_searchVoterData.HHCOUNT) ? 0 : Convert.ToInt32(_searchVoterData.HHCOUNT.Trim());
            voterwphoneData = _searchVoterData.VOTERWPHONE ? true : false;
            voteFreqData = string.IsNullOrEmpty(_searchVoterData.VOTEFREQ) ? 0 : Convert.ToInt32(_searchVoterData.VOTEFREQ.Trim());
            regStartDate = _searchVoterData.REGSDTE != null ? Convert.ToDateTime(_searchVoterData.REGSDTE).Date.ToString("d") : "";
            regEndDate = _searchVoterData.REGEDTE != null ? Convert.ToDateTime(_searchVoterData.REGEDTE).Date.ToString("d") : "";
            #endregion

            #region [[ Create SQL Statement : FROM and WHERE Clauses ]]

            //FROM Clause
            string ListClause = (!string.IsNullOrEmpty(listData) || !string.IsNullOrEmpty(suplistData)) ? _sql_AddListInfoClause : "";
            string VoterHistoryClause = (!string.IsNullOrEmpty(electionData) || !string.IsNullOrEmpty(electionYearData)) ? _sql_AddVoteHistoryClause : "";
            string FromClause = String.Format(_sql_FromClauseWithDemographic, ListClause, VoterHistoryClause);
            //WHERE Clause
            string WhereClause = " Where";
           
            //For State selection - IsAndNeeded
            WhereClause = string.IsNullOrEmpty(statelist) ? WhereClause : WhereClause + String.Format(_sql_StateRecordsClause, statelist);
            IsAndNeeded = string.IsNullOrEmpty(statelist) ? false : true;
            //for CongDataList
            WhereClause = string.IsNullOrEmpty(congDatalist) ? WhereClause : WhereClause + (IsAndNeeded == true ? " AND " : " ") + String.Format(_sql_CongDataRecordsClause, congDatalist);
            IsAndNeeded = RecalculateIsAndNeeded(congDatalist, IsAndNeeded);
            //for stateSenateData
            WhereClause = string.IsNullOrEmpty(stateSenateData) ? WhereClause : WhereClause + (IsAndNeeded == true ? " AND " : " ") + String.Format(_sql_StateSenateRecordsClause, stateSenateData);
            IsAndNeeded = RecalculateIsAndNeeded(stateSenateData, IsAndNeeded);
            //for stateHouseAssyData
            WhereClause = string.IsNullOrEmpty(stateHouseAssyData) ? WhereClause : WhereClause + (IsAndNeeded == true ? " AND " : " ") + String.Format(_sql_StateHouseAssyRecordsClause, stateHouseAssyData);
            IsAndNeeded = RecalculateIsAndNeeded(stateHouseAssyData, IsAndNeeded);
            //for stateCountyData
            WhereClause = string.IsNullOrEmpty(stateCountyData) ? WhereClause : WhereClause + (IsAndNeeded == true ? " AND " : " ") + String.Format(_sql_CountyRecordsClause, stateCountyData);
            IsAndNeeded = RecalculateIsAndNeeded(stateCountyData, IsAndNeeded);

            #region [[ Special case for State Precint : User can select without entering State ]]
            //statePrecinctData
            if (!string.IsNullOrEmpty(statelist))
            {
                //State condition already added so we just need to add Precint Condition if selected
                WhereClause = string.IsNullOrEmpty(statePrecinctData) ? WhereClause : WhereClause + (IsAndNeeded == true ? " AND " : " ") + String.Format(_sql_StatePrecinctRecordsClause, statePrecinctData);
                IsAndNeeded = RecalculateIsAndNeeded(statePrecinctData, IsAndNeeded);
            }
            else
            {
                //State condition is not added so we need to add if city is selected.
                if (!string.IsNullOrEmpty(statePrecinctData))
                {
                    //City is selected without selecting State so we need changes here
                    for (int i = 0; i <= _searchVoterData.SELSTATEPRECS.Count() - 1; i++)
                    {
                        string[] precintInfo = _searchVoterData.SELSTATEPRECS[i].Trim().Split(':');
                        //Create SQL statement
                        if (string.IsNullOrEmpty(customORCondition))
                        {
                            customORCondition = "(" + String.Format(_sql_StateRecordsClause, "'" + precintInfo[0].Trim() + "'") + " AND " + String.Format(_sql_StatePrecinctRecordsClause, "'" + precintInfo[1].Trim() + "'") + ")";
                        }
                        else
                        {
                            customORCondition = "(" + customORCondition + " OR (" + String.Format(_sql_StateRecordsClause, "'" + precintInfo[0].Trim() + "'") + " AND " + String.Format(_sql_StatePrecinctRecordsClause, "'" + precintInfo[1].Trim() + "'") + "))";
                        }
                    }
                    WhereClause = WhereClause + " " + customORCondition;
                    IsAndNeeded = RecalculateIsAndNeeded(statePrecinctData, IsAndNeeded);
                    customORCondition = "";
                }
                else
                {
                    //Precint is not selected so go ahead
                    IsAndNeeded = RecalculateIsAndNeeded(statePrecinctData, IsAndNeeded);
                }
            }
            #endregion

            #region [[ Special Case for City Data : User can select without entering State ]]
            //cityData 
            if (!string.IsNullOrEmpty(statelist))
            {
                //State condition already added so we just need to add City condition if selected
                WhereClause = string.IsNullOrEmpty(cityData) ? WhereClause : WhereClause + (IsAndNeeded == true ? " AND " : " ") + String.Format(_sql_CityRecordsClause, cityData);
                IsAndNeeded = RecalculateIsAndNeeded(cityData, IsAndNeeded);
            }
            else
            {
                //State condition is not added so we need to add if city is selected.
                if (!string.IsNullOrEmpty(cityData))
                {
                    //City is selected without selecting State so we need changes here
                    for (int i = 0; i <= _searchVoterData.SELCITYS.Count() - 1; i++)
                    {
                        string[] citystateInfo = _searchVoterData.SELCITYS[i].Trim().Split(':');
                        //Create SQL statement
                        if (string.IsNullOrEmpty(customORCondition))
                        {
                            customORCondition = "(" + String.Format(_sql_StateRecordsClause, "'" + citystateInfo[0].Trim() + "'") + " AND " + String.Format(_sql_CityRecordsClause, "'" + citystateInfo[1].Trim() + "'") + ")";
                        }
                        else
                        {
                            customORCondition = "(" + customORCondition + " OR (" + String.Format(_sql_StateRecordsClause, "'" + citystateInfo[0].Trim() + "'") + " AND " + String.Format(_sql_CityRecordsClause, "'" + citystateInfo[1].Trim() + "'") + "))";
                        }
                    }
                    WhereClause = WhereClause + " " + customORCondition;
                    IsAndNeeded = RecalculateIsAndNeeded(cityData, IsAndNeeded);
                    customORCondition = "";
                }
                else
                {
                    //City is not selected so go ahead
                    IsAndNeeded = RecalculateIsAndNeeded(cityData, IsAndNeeded);
                }
            }
            #endregion

            #region [[ Special Case for Zip : User can select without entering State ]]

            if (!string.IsNullOrEmpty(statelist))
            {
                //State condition already added so we just need to add Zip condition if selected
                WhereClause = string.IsNullOrEmpty(zipData) ? WhereClause : WhereClause + (IsAndNeeded == true ? " AND " : " ") + String.Format(_sql_ZipRecordsClause, zipData);
                IsAndNeeded = RecalculateIsAndNeeded(zipData, IsAndNeeded);
            }
            else
            {
                //State condition is not added so we need to add if city is selected.
                if (!string.IsNullOrEmpty(zipData))
                {
                    //City is selected without selecting State so we need changes here
                    for (int i = 0; i <= _searchVoterData.SELZIPS.Count() - 1; i++)
                    {
                        string[] zipstateInfo = _searchVoterData.SELZIPS[i].Trim().Split(':');
                        //Create SQL statement
                        if (string.IsNullOrEmpty(customORCondition))
                        {
                            customORCondition = "(" + String.Format(_sql_StateRecordsClause, "'" + zipstateInfo[0].Trim() + "'") + " AND " + String.Format(_sql_ZipRecordsClause, "'" + zipstateInfo[1].Trim() + "'") + ")";
                        }
                        else
                        {
                            customORCondition = "(" + customORCondition + " OR (" + String.Format(_sql_StateRecordsClause, "'" + zipstateInfo[0].Trim() + "'") + " AND " + String.Format(_sql_ZipRecordsClause, "'" + zipstateInfo[1].Trim() + "'") + "))";
                        }
                    }
                    WhereClause = WhereClause + " " + customORCondition;
                    IsAndNeeded = RecalculateIsAndNeeded(zipData, IsAndNeeded);
                    customORCondition = "";
                }
                else
                {
                    //City is not selected so go ahead
                    IsAndNeeded = RecalculateIsAndNeeded(zipData, IsAndNeeded);
                }
            }

            #endregion

            //listData
            WhereClause = string.IsNullOrEmpty(listData) ? WhereClause : WhereClause + (IsAndNeeded == true ? " AND " : " ") + String.Format(_sql_ListRecordsClause, listData);
            IsAndNeeded = RecalculateIsAndNeeded(listData, IsAndNeeded);
            //suplistData
            WhereClause = string.IsNullOrEmpty(suplistData) ? WhereClause : WhereClause + (IsAndNeeded == true ? " AND " : " ") + String.Format(_sql_SupListRecordsClause, suplistData);
            IsAndNeeded = string.IsNullOrEmpty(suplistData) && IsAndNeeded == false ? false : true;
            //firstNameData
            WhereClause = string.IsNullOrEmpty(firstNameData) ? WhereClause : WhereClause + (IsAndNeeded == true ? " AND " : " ") + String.Format(_sql_FirstNameRecordsClause, firstNameData);
            IsAndNeeded = RecalculateIsAndNeeded(firstNameData, IsAndNeeded);
            //lastNameData
            WhereClause = string.IsNullOrEmpty(lastNameData) ? WhereClause : WhereClause + (IsAndNeeded == true ? " AND " : " ") + String.Format(_sql_LastNameRecordsClause, lastNameData);
            IsAndNeeded = RecalculateIsAndNeeded(lastNameData, IsAndNeeded);
            //genderData
            WhereClause = string.IsNullOrEmpty(genderData) ? WhereClause : WhereClause + (IsAndNeeded == true ? " AND " : " ") + String.Format(_sql_GenderRecordsClause, genderData);
            IsAndNeeded = RecalculateIsAndNeeded(genderData, IsAndNeeded);
            //marstatusData
            WhereClause = string.IsNullOrEmpty(marstatusData) ? WhereClause : WhereClause + (IsAndNeeded == true ? " AND " : " ") + String.Format(_sql_MaritalStatusRecordsClause, marstatusData);
            IsAndNeeded = RecalculateIsAndNeeded(marstatusData, IsAndNeeded);
            //occupationData
            WhereClause = string.IsNullOrEmpty(occupationData) ? WhereClause : WhereClause + (IsAndNeeded == true ? " AND " : " ") + String.Format(_sql_OccupationRecordsClause, occupationData);
            IsAndNeeded = RecalculateIsAndNeeded(occupationData, IsAndNeeded);
            //estIncomeData
            WhereClause = string.IsNullOrEmpty(estIncomeData) ? WhereClause : WhereClause + (IsAndNeeded == true ? " AND " : " ") + String.Format(_sql_EstIncomeRecordsClause, estIncomeData);
            IsAndNeeded = RecalculateIsAndNeeded(estIncomeData, IsAndNeeded);
            //House Hold Count Data
            WhereClause = hhCountData == 0 ? WhereClause : WhereClause + (IsAndNeeded == true ? " AND " : " ") + String.Format(_sql_HHCountClause, hhCountData);
            IsAndNeeded = hhCountData == 0 && IsAndNeeded == false ? false : true;
            //partyData
            WhereClause = string.IsNullOrEmpty(partyData) ? WhereClause : WhereClause + (IsAndNeeded == true ? " AND " : " ") + String.Format(_sql_PartyRecordsClause, partyData);
            IsAndNeeded = RecalculateIsAndNeeded(partyData, IsAndNeeded);
            //hhpartyData
            WhereClause = string.IsNullOrEmpty(hhpartyData) ? WhereClause : WhereClause + (IsAndNeeded == true ? " AND " : " ") + String.Format(_sql_HHPartyRecordsClause, hhpartyData);
            IsAndNeeded = RecalculateIsAndNeeded(hhpartyData, IsAndNeeded);
            //Election Data
            WhereClause = string.IsNullOrEmpty(electionData) ? WhereClause : WhereClause + (IsAndNeeded == true ? " AND " : " ") + String.Format(_sql_ElectionClause, electionData);
            IsAndNeeded = RecalculateIsAndNeeded(electionData, IsAndNeeded);
            //Election Year Data
            WhereClause = string.IsNullOrEmpty(electionYearData) ? WhereClause : WhereClause + (IsAndNeeded == true ? " AND " : " ") + String.Format(_sql_ElectionYearClause, electionYearData);
            IsAndNeeded = RecalculateIsAndNeeded(electionYearData, IsAndNeeded);
            //agegroupsData
            WhereClause = string.IsNullOrEmpty(agegroupsData) ? WhereClause : WhereClause + (IsAndNeeded == true ? " AND " : " ") + String.Format(_sql_AgeGroupRecordsClause, agegroupsData);
            IsAndNeeded = RecalculateIsAndNeeded(hhpartyData, IsAndNeeded);
            //For Phone records
            WhereClause = _searchVoterData.VOTERWPHONE == false ? WhereClause : WhereClause + (IsAndNeeded == true ? " AND " : " ") + _sql_NoPhoneRecordsClause;
            IsAndNeeded = _searchVoterData.VOTERWPHONE == false && IsAndNeeded == false ? false : true;
            //For Registration Date clause
            if (!string.IsNullOrEmpty(regStartDate) && !string.IsNullOrEmpty(regEndDate))
            {
                WhereClause = WhereClause + (IsAndNeeded == true ? " AND " : " ") + String.Format(_sql_RegistrationDateRecordsClause, regStartDate, regEndDate);
                IsAndNeeded = true;
            }
            //For VoterState Id selection
            WhereClause = string.IsNullOrEmpty(_searchVoterData.VOTERID) ? WhereClause : WhereClause + (IsAndNeeded == true ? " AND " : " ") + String.Format(_sql_VoterStateIdClause, _searchVoterData.VOTERID);
            IsAndNeeded = string.IsNullOrEmpty(_searchVoterData.VOTERID) && IsAndNeeded == false ? false : true;
            //Vote Frequency 
            WhereClause = voteFreqData == 0 ? WhereClause : WhereClause + (IsAndNeeded == true ? " AND " : " ") + String.Format(_sql_VoteFrequencyClause, voteFreqData);
            IsAndNeeded = voteFreqData == 0 && IsAndNeeded == false ? false : true;
            //SQL Definition
            q_def_S _def = q_library.get_q_def_fr_cache("crm_voterSearch");
            //Now let us return the SQL statement : Note - we will use DISTINCT Columns except Election/ElectionYear data is available
            return (string.IsNullOrEmpty(electionData) && string.IsNullOrEmpty(electionYearData)) ? String.Format(_sql_BasicFields, _def.sq_fieldsV, FromClause, WhereClause) : String.Format(_sql_BasicFieldsNoDistinct, _def.sq_fieldsV, FromClause, WhereClause);

            #endregion

        }

        #region [ (private-List<VoterR_ext1>) Quick Text Search ]
        private string quickTextSearchSQLExport(string searchText, string qDefName = "crm_voterSearch")
        {

            string _where = "";

            #region [ Split Search Text > string1, string2 & string3 and create "Where" statement ]
            char[] delimiterChars = { ' ', ',', '.', ';', ':', '|', '\t' };

            string[] words = searchText.Split(delimiterChars);

            if (words.Count() > 0)
            {
                string string1 = words[0];
                string string2 = (words.Count() > 1 ? words[1] : "");
                string string3 = (words.Count() > 2 ? words[2] : "");

                string _FNAME_where = "";

                #region [ Where Clause ]

                //_FNAME_where = q_library.get_normFNAMEwhere("FNAME", string1, _dataService).Replace("'", "''");
                _FNAME_where = q_library.get_normFNAMEwhere("FNAME", string1, _dataService);

                switch (words.Count())
                {
                    case 1: // Voter ID or First or Last Name
                        _where = string.Format(_FNAME_where + " OR LNAME LIKE '{0}%' OR VoterStateID LIKE '{0}%'", string1);
                        break;

                    case 2: // First and Last Name  OR First OR Last OR Voter ID
                        _where = string.Format("(" + _FNAME_where + " AND LNAME LIKE '{0}%') OR FNAME LIKE '{1}%' OR LNAME LIKE '{1}%' OR VoterStateID LIKE '{1}%'", string2, searchText);
                        break;

                    case 3: // First, Middle and Last Name  OR Last OR Voter ID
                        _where = string.Format("(" + _FNAME_where + " AND MNAME LIKE '{0}%' AND LNAME LIKE '{1}%') OR FNAME LIKE '{2}%' OR LNAME LIKE '{2}%' OR VoterStateID LIKE '{2}%'", string2, string3, searchText);
                        break;
                    default:
                        break;
                }
                #endregion

                // SQL Definition
                q_def_S _def = q_library.get_q_def_fr_cache(qDefName);

                // Compose SQL
                string sql = string.Format(@"
                        SELECT DISTINCT TOP 100000 
                        {0}
                        FROM {1}
                        WHERE {2}
                    ", _def.sq_fieldsV, _def.sq_from, _where);



                return sql;
            }
            else
                return null;
            #endregion

        }

        private List<VoterR_ext1> quickTextSearch(string searchText, int pageSize, int pageNo, string sortOptions, string qDefName = "crm_voterSearch")
        {

            string _where = "";

            #region [ Split Search Text > string1, string2 & string3 and create "Where" statement ]
            char[] delimiterChars = { ' ', ',', '.', ';', ':', '|', '\t' };

            string[] words = searchText.Split(delimiterChars);

            if (words.Count() > 0)
            {
                string string1 = words[0];
                string string2 = (words.Count() > 1 ? words[1] : "");
                string string3 = (words.Count() > 2 ? words[2] : "");

                string _FNAME_where = "";

                #region [ Where Clause ]

                _FNAME_where = q_library.get_normFNAMEwhere("FNAME", string1, _dataService).Replace("'", "''");

                switch (words.Count())
                {
                    case 1: // Voter ID or First or Last Name
                        _where = string.Format(_FNAME_where + " OR LNAME LIKE ''{0}%'' OR VoterStateID LIKE ''{0}%''", string1);
                        break;

                    case 2: // First and Last Name  OR First OR Last OR Voter ID
                        _where = string.Format("(" + _FNAME_where + " AND LNAME LIKE ''{0}%'') OR FNAME LIKE ''{1}%'' OR LNAME LIKE ''{1}%'' OR VoterStateID LIKE ''{1}%''", string2, searchText);
                        break;

                    case 3: // First, Middle and Last Name  OR Last OR Voter ID
                        _where = string.Format("(" + _FNAME_where + " AND MNAME LIKE ''{0}%'' AND LNAME LIKE ''{1}%'') OR FNAME LIKE ''{2}%'' OR LNAME LIKE ''{2}%'' OR VoterStateID LIKE ''{2}%''", string2, string3, searchText);
                        break;
                    default:
                        break;
                }
                #endregion

                if (string.IsNullOrEmpty(sortOptions)) sortOptions = "LNAME, FNAME";

                // SQL Definition
                q_def_S _def = q_library.get_q_def_fr_cache(qDefName);

                // Compose SQL
                string sql = string.Format(@"
                        SELECT DISTINCT 
                        {0}
                        FROM {1}
                        WHERE {2}
                    ", _def.sq_fieldsV, _def.sq_from, _where);

                //string sqlParam = sql.Trim().Replace("'", "''");

                System.Diagnostics.Stopwatch stopWatch = new System.Diagnostics.Stopwatch();
                stopWatch.Start();
                var q = _entity_crm.getContext().Database.SqlQuery<VoterR_ext1>(String.Format("EXEC dbo.get_pagedDataSet '{0}', '{1}', {2}, {3}", sql, sortOptions, pageSize, pageNo));

                List<VoterR_ext1> result = q.ToList();
                stopWatch.Stop();

                cl_query.create_a_sqlLog(
                            sql.Trim(),
                            "crm/api/Voter/SearchVoterData(Quick)",
                            util.serialize_toXmlString(_where),
                            crmSession.UID().Value,
                            (result != null && result.Count() > 0 ? result.FirstOrDefault().count_ : 0),
                            "Voter Search - View Columns",
                            "",
                            _entity_crm,
             Convert.ToSingle(stopWatch.ElapsedMilliseconds / 1000.0));

                return result;
            }
            else
                return new List<VoterR_ext1>();
            #endregion

        }
        #endregion

        #region [ (private-List<VoterR_ext1>) Advanced Search ]
        private List<VoterR_ext1> sqlSearch(string sql, int pageSize, int pageNo, string sortOptions)
        {
            //if (string.IsNullOrEmpty(sortOptions)) sortOptions = "voterSeq";
            if (string.IsNullOrEmpty(sortOptions)) sortOptions = "LNAME,FNAME,MNAME";

            string sqlParam = sql.Trim().Replace("'", "''");

            System.Diagnostics.Stopwatch stopWatch = new System.Diagnostics.Stopwatch();
            stopWatch.Start();

            List<VoterR_ext1> result = null;

            var q = _entity_crm.getContext().Database.SqlQuery<VoterR_ext1>(String.Format("EXEC dbo.get_pagedDataSet '{0}', '{1}', {2}, {3}", sqlParam, sortOptions, pageSize, pageNo));
            result = q.ToList();
            stopWatch.Stop();
            
            cl_query.create_a_sqlLog(
                        sql.Trim(),
                        "crm/api/Voter/SearchVoterData(Adv)",
                        util.serialize_toXmlString(sql),
                        crmSession.UID().Value,
                        (result != null && result.Count() > 0 ? result.FirstOrDefault().count_ : 0),
                        "Voter Search - View Columns",
                        "",
                        _entity_crm,
             Convert.ToSingle(stopWatch.ElapsedMilliseconds / 1000.0));

            return result;
        }
        #endregion

        public bool RecalculateIsAndNeeded(string uiValue, bool isNeeded)
        {
            return string.IsNullOrEmpty(uiValue) && isNeeded == false ? false : true;
        }

        #endregion

        #region [[ For Export Feature ]]

        [HttpPost, Route("crm/api/Voter/ExportKey")]
        [apiAuthorize(AccessElement = "cmdiapp.dms.voter_qq", AccessLevel = "e")]
        public string ExportKey(SearchVoterData _searchVoterData)
        {
            string key_ = System.Guid.NewGuid().ToString();
            HttpRuntime.Cache.Insert(key_, _searchVoterData, null, DateTime.UtcNow.Add(new TimeSpan(1, 0, 1)), System.Web.Caching.Cache.NoSlidingExpiration);

            return key_;
        }
                        
        [HttpGet, Route("crm/api/Voter/Export")]
        [apiAuthorize(AccessElement = "cmdiapp.dms.voter_qq", AccessLevel = "e")]
        public string Export(string key)
        {
            key = key.Replace("\"", "").Trim();

            string exportSql = "";

            SearchVoterData _searchVoterData = (SearchVoterData)HttpRuntime.Cache[key];

            #region [ 1. Quick Text Search ]
            if (!string.IsNullOrEmpty(_searchVoterData.searchText))
            {
                exportSql = quickTextSearchSQLExport(_searchVoterData.searchText, "crm_voterSearch");
            }
            #endregion

            #region [ 2. Search Data : Advanced Search Here ]
            else
            {
                if (_searchVoterData.SELELECTIONS.Count() > 0 || _searchVoterData.SELELECTIONYEARS.Count() > 0)
                {
                    exportSql = GetSQLForAdvancedSearchExport(_searchVoterData).Replace("SELECT", "SELECT TOP 100000"); // ############ LIMIT TO 100K FOR NOW)
                }
                else
                {
                    exportSql = GetSQLForAdvancedSearchExport(_searchVoterData).Replace("SELECT DISTINCT", "SELECT DISTINCT TOP 100000"); // ############ LIMIT TO 100K FOR NOW)
                }

            }
            #endregion

            System.Data.DataSet _ds = q_library.get_dataset_w_sql__single(session.currentDomain_project._connectionString(), exportSql, "result", 0, 0);

            if (_ds != null)
            {
                string key_ = System.Guid.NewGuid().ToString();
                HttpRuntime.Cache.Insert(key_, _ds, null, DateTime.UtcNow.Add(new TimeSpan(1, 0, 1)), System.Web.Caching.Cache.NoSlidingExpiration);
                return key_;
            }
            else
            {
                return null;
            }

        }
                
        #endregion

        #endregion
    }
}

       