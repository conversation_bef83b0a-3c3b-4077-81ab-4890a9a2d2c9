﻿using System;
using System.Data.Entity;

namespace cmdiapp.n.core.Areas.legislator.Domain.Data
{
    public class LegislatorDBInitializer : IDatabaseInitializer<_legislatorDBContext>
    {
        public void InitializeDatabase(_legislatorDBContext context)
        {
            if (!context.Database.Exists())
            {
                throw new NotSupportedException("The legislator database does not exist.");
            }
        }
    }
}