﻿using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Web;
using System.Web.Mvc;

using System.IO;
using System.Data;
using System.Dynamic;
using System.Web.Helpers;

using AutoMapper;
using System.Runtime.Serialization;
using System.Runtime.Serialization.Json;
using System.Text;
using System.Xml.Linq;
using System.Xml;

using cmdiapp.n.core.Core;
using cmdiapp.n.core.Core.App_Start;
using cmdiapp.n.core.Domain.Models;
using cmdiapp.n.core.Domain.ViewModels;
using cmdiapp.n.core.Domain.Repositories;
using cmdiapp.n.core.Domain.Services;

using cmdiapp.n.core.Areas.crm.Core;
using cmdiapp.n.core.Areas.crm.Domain.Models;
using cmdiapp.n.core.Areas.crm.Domain.ViewModels;
using cmdiapp.n.core.Areas.crm.Domain.Repositories;
using cmdiapp.n.core.Areas.crm.Domain.Services;
using cmdiapp.n.core.Presentation.Controllers;

using cmdiapp.n.core.Library;
using cmdiapp.n.core.Library.Report;
using cmdiapp.n.core._Presentation.Attributes;

namespace cmdiapp.n.core.Areas.crm.Presentation.Controllers
{
    [Authorize]    
    public class ReportController : Controller
    {
        private readonly IReportDataService _reportDataService;
        private const string RespAnalysis_cREPORT_NUMBER = "3";
        private const string RespAnalysis_cREPORT_NUMBER_TM = "33";
        private const string RespAnalysis_cREPORT_NUMBER_SUB = "333";
        private const string RespAnalysis_cREPORT_NUMBER_WITHNONDONORS = "3333";

        //Get User Session Info
        userSession _userSession = session.userSession;
        private string CrLf = "\r\n";
        private string _sql_script1 = "";
        private System.Xml.XmlNode _node;
        System.Collections.Generic.List<string> ids = new System.Collections.Generic.List<string>();
        System.Collections.Generic.List<string> sqls = new System.Collections.Generic.List<string>();
        System.Collections.Generic.List<int> zeros = new System.Collections.Generic.List<int>();
        System.Xml.XmlNodeList xlist;
        System.Data.DataSet dSet;
        supportMethods _supportMethods = new supportMethods();
        //ReportInfo reportInfo;
        
        public ReportController(IReportDataService ReportDataService)
        {
            _reportDataService = ReportDataService;
        }

        #region [[ Response Analysis Report ]]
        public ActionResult ResponseAnalysis(string udlo)
        {
            pageInfo _pageInfo = new pageInfo();
            if (udlo == "y")
                _pageInfo.LayoutPage = crmConstants.DEFAULT_LAYOUT_BLANK;
            else
                _pageInfo.LayoutPage = "";

            _pageInfo.pageData1 = "";
            _pageInfo.pageData2 = "";

            if (!string.IsNullOrEmpty(session.userSession.getConfigVal(crmConstants.RAbySub)) && session.userSession.getConfigVal(crmConstants.RAbySub).ToUpper() == "Y")
            {
                _pageInfo.pageData1 = "Y";
            }

            if (!string.IsNullOrEmpty(session.userSession.getConfigVal(crmConstants.ra_defaultByList)) && session.userSession.getConfigVal(crmConstants.ra_defaultByList).ToUpper() == "Y")
            {
                _pageInfo.pageData2 = "Y";
            }
            //ra_defaultByList

            return View(@"_ResponseAnalysis", _pageInfo);
        }

        [HttpPost]
        public ActionResult ProcessReport(ResponseAnalysisCollection _responseAnalysisCollection)
        {
            try
            {
                if (_responseAnalysisCollection != null)
                {
                    
                    //Create Unique GUID Here
                    string p_key = System.Guid.NewGuid().ToString();

                    #region [[ Process the Session Here ]]
                    System.Collections.Specialized.NameValueCollection reportParameter = new System.Collections.Specialized.NameValueCollection();

                    reportParameter.Add("cc", "ccc");
                    //The report number is based on which report user wants to see - Regular or the Telemarketing Version..
                    reportParameter.Add("rn", _responseAnalysisCollection.ISTELEMVERSION ? RespAnalysis_cREPORT_NUMBER_TM : (_responseAnalysisCollection.SHOWNONDONORS ? RespAnalysis_cREPORT_NUMBER_WITHNONDONORS : RespAnalysis_cREPORT_NUMBER));
                    reportParameter.Add("Program", _responseAnalysisCollection.PROGRAM);

                    //Get the PackageIds
                    string packageIds = "";
                    if ((_responseAnalysisCollection.SELECTEDPKGITEMS?.Length ?? 0) > 0)
                    {
                        packageIds = string.Join(",",
                            _responseAnalysisCollection.SELECTEDPKGITEMS
                                    .Select(x => x.ToString()).ToArray());                            
                    }
                    else
                    {
                        // select all
                        packageIds = "-1";
                    }

                    reportParameter.Add("Package", packageIds);

                    reportParameter.Add("apply_maildate", _responseAnalysisCollection.MAILDTE ? "1" : "0");

                    string _maildate_from = null;
                    if (_responseAnalysisCollection.MAILDTE && _responseAnalysisCollection.STARTDTE != null)
                    {
                        try
                        {
                            _maildate_from = Convert.ToDateTime(_responseAnalysisCollection.STARTDTE).ToString("yyyy-MM-dd");
                        }
                        catch { }
                    }

                    reportParameter.Add("Maildate_From", _maildate_from);

                    string _maildate_to = null;
                    if (_responseAnalysisCollection.MAILDTE && _responseAnalysisCollection.ENDDTE != null)
                    {
                        try
                        {
                            _maildate_to = Convert.ToDateTime(_responseAnalysisCollection.ENDDTE).ToString("yyyy-MM-dd");
                        }
                        catch { }
                    }

                    reportParameter.Add("Maildate_To", _maildate_to);

                    reportParameter.Add("show_details", _responseAnalysisCollection.SHOWDETAILS ? "True" : "False");
                    reportParameter.Add("show_subtotal_LISTNOG", _responseAnalysisCollection.SHOWSUBTOTBYGRP ? "True" : "False");
                    reportParameter.Add("show_subtotal_LISTNO", _responseAnalysisCollection.SHOWSUBTOTBYLIST ? "True" : "False");
                    reportParameter.Add("show_subtotal_PKGE", _responseAnalysisCollection.SHOWSUBTOTBYPCKG ? "True" : "False");
                    //Save the Report Parameter collection in the Session
                    System.Web.HttpContext.Current.Session.Add(p_key + "_c", reportParameter);

                    #endregion

                    #region [[ Get Data based on Session ]]
                    string _sp = _responseAnalysisCollection.SHOWNONDONORS && !_responseAnalysisCollection.ISTELEMVERSION ? "iGetResponseAnalysisDataWithNonDonors" : "iGetResponseAnalysisData2";
                    DataSet _respAnalysisDataset =
                        _reportDataService.get_dataset_w_sql__single(String.Format("EXEC dbo.{9} {0},'{1}',{2},{3},{4},{5},{6},'{7}','{8}'", 
                        Convert.ToInt32(_responseAnalysisCollection.PROGRAM), //0
                        packageIds,//1
                        (_responseAnalysisCollection.MAILDTE ? 1 : 0),//2
                        (_responseAnalysisCollection.SHOWDETAILS ? 1 : 0),//3
                        (_responseAnalysisCollection.SHOWSUBTOTBYGRP ? 1 : 0),//4
                        (_responseAnalysisCollection.SHOWSUBTOTBYLIST ? 1 : 0),//5
                        (_responseAnalysisCollection.SHOWSUBTOTBYPCKG ? 1 : 0),//6
                        _maildate_from,//7
                        _maildate_to,//8
                        _sp),//9
                        "respAnaData");
                    
                    //First Data Set 
                    System.Web.HttpContext.Current.Session.Add(p_key + "_fd", _respAnalysisDataset);
                    #endregion

                    #region [[ For SystemInfo ]]
                    DataSet _systemDataset = _reportDataService.get_dataset_w_sql__single("SELECT ClientCode, ClientName,rs_url,rs_auth_path,rs_folder,rs_report FROM ssSystem", "systemData");
                    //Second Data Set 
                    System.Web.HttpContext.Current.Session.Add(p_key + "_sd", _systemDataset);
                    #endregion

                    //For Report Name
                    System.Web.HttpContext.Current.Session.Add(p_key + "_rName", "ResponseAnalysis");

                    if (_respAnalysisDataset.Tables[0].Rows.Count > 0 && _systemDataset.Tables[0].Rows.Count > 0)
                    {
                        return Json(new { success = true, key = p_key });
                    }
                    else
                    {
                        return Json(new { success = false, message = "There are no data to display on the report. Please change your selection." });
                    }
                                        
                }
                else
                {
                    return Json(new { success = false, message = "There is some issue in processing report request. Please try again later." });
                }
            }
            catch(Exception ex)
            {
                return Json(new { success = false, message = "There is some issue in processing report request. Please try again later." });
            }
            
        }

        [HttpPost]
        public ActionResult ProcessReportSub(ResponseAnalysisCollectionSub _responseAnalysisCollection)
        {
            try
            {
                if (_responseAnalysisCollection != null)
                {

                    //Create Unique GUID Here
                    string p_key = System.Guid.NewGuid().ToString();

                    #region [[ Process the Session Here ]]
                    System.Collections.Specialized.NameValueCollection reportParameter = new System.Collections.Specialized.NameValueCollection();

                    reportParameter.Add("cc", "ccc");
                    //The report number is based on which report user wants to see - Regular or the Telemarketing Version..
                    reportParameter.Add("rn", RespAnalysis_cREPORT_NUMBER_SUB);
                    reportParameter.Add("Program", _responseAnalysisCollection.SUBPROGRAM);

                    //Get the PackageIds
                    string packageIds = "";

                    if ((_responseAnalysisCollection.SUBSELECTEDPKGITEMS?.Length ?? 0) > 0)
                    {
                        packageIds = string.Join(",",
                            _responseAnalysisCollection.SUBSELECTEDPKGITEMS
                                    .Select(x => x.ToString()).ToArray());
                    }
                    else
                    {
                        // use all
                        packageIds = "-1";
                    }

                    reportParameter.Add("Package", packageIds);

                    reportParameter.Add("apply_maildate", _responseAnalysisCollection.SUBMAILDTE ? "1" : "0");

                    string _maildate_from = null;
                    if (_responseAnalysisCollection.SUBMAILDTE && _responseAnalysisCollection.SUBSTARTDTE != null)
                    {
                        try
                        {
                            _maildate_from = Convert.ToDateTime(_responseAnalysisCollection.SUBSTARTDTE).ToString("yyyy-MM-dd");
                        }
                        catch { }
                    }

                    reportParameter.Add("Maildate_From", _maildate_from);

                    string _maildate_to = null;
                    if (_responseAnalysisCollection.SUBMAILDTE && _responseAnalysisCollection.SUBENDDTE != null)
                    {
                        try
                        {
                            _maildate_to = Convert.ToDateTime(_responseAnalysisCollection.SUBENDDTE).ToString("yyyy-MM-dd");
                        }
                        catch { }
                    }

                    reportParameter.Add("Maildate_To", _maildate_to);

                    reportParameter.Add("show_details", _responseAnalysisCollection.SUBSHOWDETAILS ? "True" : "False");
                    reportParameter.Add("show_subtotal_LISTNOG", _responseAnalysisCollection.SUBSHOWSUBTOTBYGRP ? "True" : "False");
                    reportParameter.Add("show_subtotal_LISTNO", _responseAnalysisCollection.SUBSHOWSUBTOTBYLIST ? "True" : "False");
                    reportParameter.Add("show_subtotal_PKGE", _responseAnalysisCollection.SUBSHOWSUBTOTBYPCKG ? "True" : "False");
                    //Save the Report Parameter collection in the Session
                    System.Web.HttpContext.Current.Session.Add(p_key + "_c", reportParameter);

                    #endregion

                    #region [[ Get Data based on Session ]]
                    DataSet _respAnalysisDataset =
                        _reportDataService.get_dataset_w_sql__single(String.Format("EXEC dbo.iGetResponseAnalysisDataSubPrgm {0},'{1}',{2},{3},{4},{5},{6},'{7}','{8}'",
                        Convert.ToInt32(_responseAnalysisCollection.SUBPROGRAM), //0
                        packageIds,//1
                        (_responseAnalysisCollection.SUBMAILDTE ? 1 : 0),//2
                        (_responseAnalysisCollection.SUBSHOWDETAILS ? 1 : 0),//3
                        (_responseAnalysisCollection.SUBSHOWSUBTOTBYGRP ? 1 : 0),//4
                        (_responseAnalysisCollection.SUBSHOWSUBTOTBYLIST ? 1 : 0),//5
                        (_responseAnalysisCollection.SUBSHOWSUBTOTBYPCKG ? 1 : 0),//6
                        _maildate_from,//7
                        _maildate_to),//8
                        "respAnaData");

                    //First Data Set 
                    System.Web.HttpContext.Current.Session.Add(p_key + "_fd", _respAnalysisDataset);
                    #endregion

                    #region [[ For SystemInfo ]]
                    DataSet _systemDataset = _reportDataService.get_dataset_w_sql__single("SELECT ClientCode, ClientName,rs_url,rs_auth_path,rs_folder,rs_report FROM ssSystem", "systemData");
                    //Second Data Set 
                    System.Web.HttpContext.Current.Session.Add(p_key + "_sd", _systemDataset);
                    #endregion

                    //For Report Name
                    System.Web.HttpContext.Current.Session.Add(p_key + "_rName", "ResponseAnalysis");

                    if (_respAnalysisDataset.Tables[0].Rows.Count > 0 && _systemDataset.Tables[0].Rows.Count > 0)
                    {
                        return Json(new { success = true, key = p_key });
                    }
                    else
                    {
                        return Json(new { success = false, message = "There are no data to display on the report. Please change your selection." });
                    }

                }
                else
                {
                    return Json(new { success = false, message = "There is some issue in processing report request. Please try again later." });
                }
            }
            catch (Exception ex)
            {
                return Json(new { success = false, message = "There is some issue in processing report request. Please try again later." });
            }

        }
              

        public ActionResult DiplayReport(string pkgId, string prgId, string srcCount, string teleCount, string rndNo)
        {
            try
            {
                //Create Unique GUID Here
                string p_key = System.Guid.NewGuid().ToString();
                bool IsTeleSource = false;
                
                //if (Convert.ToInt32(srcCount) == Convert.ToInt32(teleCount))
                //    IsTeleSource = true;

                //At least on source marked as TeleMarketing then display telmarketing version of BlueBook
                if (Convert.ToInt32(teleCount) > 0)
                    IsTeleSource = true;

                #region [[ Process the Session Here ]]
                System.Collections.Specialized.NameValueCollection reportParameter = new System.Collections.Specialized.NameValueCollection();

                reportParameter.Add("cc", "ccc");
                //The report number is based on which report user wants to see - Regular or the Telemarketing Version..
                reportParameter.Add("rn", IsTeleSource == true ? RespAnalysis_cREPORT_NUMBER_TM : RespAnalysis_cREPORT_NUMBER);
                reportParameter.Add("Program", prgId);

                //Get the PackageIds
                reportParameter.Add("Package", pkgId);
                reportParameter.Add("apply_maildate", "0");

                string _maildate_from = null;
                reportParameter.Add("Maildate_From", _maildate_from);

                string _maildate_to = null;
                reportParameter.Add("Maildate_To", _maildate_to);

                bool IsdefaultByListConfig = false;
                if (!string.IsNullOrEmpty(session.userSession.getConfigVal(crmConstants.ra_defaultByList)) && session.userSession.getConfigVal(crmConstants.ra_defaultByList).ToUpper() == "Y")
                {
                    IsdefaultByListConfig = true;
                }

                reportParameter.Add("show_details", "True");
                reportParameter.Add("show_subtotal_LISTNOG", "False");

                if(IsdefaultByListConfig)
                    reportParameter.Add("show_subtotal_LISTNO", "True");
                else
                    reportParameter.Add("show_subtotal_LISTNO", "False");

                reportParameter.Add("show_subtotal_PKGE",  "True");
                //Save the Report Parameter collection in the Session
                System.Web.HttpContext.Current.Session.Add(p_key + "_c", reportParameter);

                #endregion

                #region [[ Get Data based on Session ]]

                

                DataSet _respAnalysisDataset =
                    _reportDataService.get_dataset_w_sql__single(String.Format("EXEC dbo.iGetResponseAnalysisData2 {0},'{1}',{2},{3},{4},{5},{6},'{7}','{8}'",
                    Convert.ToInt32(prgId), //0
                    pkgId,//1
                    (0),//2 -- Mail Date
                    (1),//3 - Show Details
                    (0),//4 - Group
                    IsdefaultByListConfig ? (1) : (0),//5 - List Option
                    (1),//6
                    _maildate_from,//7
                    _maildate_to),//8
                    "respAnaData");

                //First Data Set 
                System.Web.HttpContext.Current.Session.Add(p_key + "_fd", _respAnalysisDataset);
                #endregion

                #region [[ For SystemInfo ]]
                DataSet _systemDataset = _reportDataService.get_dataset_w_sql__single("SELECT ClientCode, ClientName,rs_url,rs_auth_path,rs_folder,rs_report FROM ssSystem", "systemData");
                //Second Data Set 
                System.Web.HttpContext.Current.Session.Add(p_key + "_sd", _systemDataset);
                #endregion

                //For Report Name
                System.Web.HttpContext.Current.Session.Add(p_key + "_rName", "ResponseAnalysis");

                if (_respAnalysisDataset.Tables[0].Rows.Count > 0 && _systemDataset.Tables[0].Rows.Count > 0)
                {
                    //Let us go to the Response Analysis Report Page - Note only one .. is required
                    return Redirect("../../Report/ReportPage.aspx?p_key=" + p_key);
                }
                else
                {
                    return Json(new { success = false, message = "There are no data to display on the report." });
                }

            }
            catch
            {
                return Json(new { success = false, JsonRequestBehavior.AllowGet });
            }
        }

        [NotLaunchpadAuthorize]
        public ActionResult ShowReport(string p_key)
        {
            try
            {
                if (Session[p_key + "_c"] != null && Session[p_key + "_fd"] != null && Session[p_key + "_sd"] != null)
                {
                    //Let us go to the Response Analysis Report Page - Note only one .. is required
                    return Redirect("../../Report/ReportPage.aspx?p_key=" + p_key);
                }
                else
                {
                    return Json(new { success = false, JsonRequestBehavior.AllowGet });
                }
            }
            catch
            {
                return Json(new { success = false, JsonRequestBehavior.AllowGet });
            }

        }
        #endregion

        #region [[ For Downlines Report ]]
        
        [HttpPost]
        public ActionResult ProcessDownlinesReport(DOWNLINESREPORTINPUT _downlinesreportInput)
        {
            try
            {
                if (_downlinesreportInput != null)
                {
                    #region [[ Get Data ]]
                    
                    string _xml = get_report_data_sourceDef_in_xml(_downlinesreportInput.TRACKNO, _downlinesreportInput.REPORTLBLHEADER);

                    //Create collection
                    System.Xml.XmlDocument doc = new System.Xml.XmlDocument();
                    doc.LoadXml(_xml);
                    _node = doc.DocumentElement.SelectSingleNode("/ReportParameters");

                    System.Collections.Specialized.NameValueCollection _collection = new System.Collections.Specialized.NameValueCollection();
                    _collection.Add("cc", _node.Attributes["clientcode"].Value);
                    _collection.Add("rn", _node.Attributes["reportcode"].Value);
                    _collection.Add("XML", _node.OuterXml);
                    
                    //Get Queries Dataset
                    xlist = _node.SelectNodes("Queries/Query");

                    foreach (System.Xml.XmlNode node in xlist)
                    {
                        ids.Add(node.Attributes["id"].Value);
                        sqls.Add(node.InnerText);
                        zeros.Add(0);
                    }
                    dSet = _reportDataService.get_dataset_w_sql__multi(sqls, ids, zeros, zeros);
                    //Let us have unique GUID which will be used to retrieve the sessions for the Report

                    string p_key = System.Guid.NewGuid().ToString();
                    //First Dataset
                    System.Web.HttpContext.Current.Session.Add(p_key+ "_fd", dSet);
                    //Save the Report Parameter collection in the Session
                    System.Web.HttpContext.Current.Session.Add(p_key + "_c", _collection);
                    //Also Add Report Name in the Session
                    System.Web.HttpContext.Current.Session.Add(p_key + "_rName", "DownlinesReport");
                    
                    #endregion

                    if (dSet.Tables[0].Rows.Count > 0)
                    {
                        return Json(new { success = true, key = p_key });
                    }
                    else
                    {
                        return Json(new { success = false, message = "There are no data to display on the report. Please try again later." });
                    }
                }
                else
                {
                    return Json(new { success = false, message = "There is some issue in processing report request. Please try again later." });
                }
            }
            catch
            {
                return Json(new { success = false, message = "There is some issue in processing report request. Please try again later." });
            }
        }

        public ActionResult ShowDownlinesReport(string p_key)
        {
            try
            {
                if (Session[p_key + "_c"] != null && Session[p_key + "_fd"] != null && Session[p_key + "_rName"] != null)
                {
                    //Let us go to the Downlines Report Landing Page
                    return Redirect("../../Report/ReportPage.aspx?p_key=" + p_key);
                }
                else
                {
                    return Json(new { success = false, JsonRequestBehavior.AllowGet });
                }
            }
            catch
            {
                return Json(new { success = false, JsonRequestBehavior.AllowGet });
            }
        }
        

        #region func.string.get_report_data_sourceDef_in_xml
        private string get_report_data_sourceDef_in_xml(int trackNo, string reportName)
        {
            #region Sample XML
            /*
                 <ReportParameters clientcode="ALL" reportcode="111">
                    <Parameters>
                                    <Parameter id="PID">123456</Parameter>
                                    <Parameter id="PID">456789</Parameter>
                                    <Parameter id="PID">357689</Parameter>
                                    ...
                    </Parameters>

                    <Queries>
                                    <Query id="recordsetName1">select * from PEOPLE where CREATE_DT &gt; '2009-02-03'</Query>
                                    <Query id="recordsetName2">select * from MONY where BATCHDTE &gt; '2009-02-03'</Query>
                                    <SubQuery id="iPhone_detail" reportname="SubReport1">select * from PHN where PID = 123456</Query>
                                    <SubQuery id="iEmail_detail" reportname="SubReport2">select * from PHN where PID = 123456</Query>
                                    ...
                    </Queries>
                </ReportParameters>
             */
            #endregion

            XElement _x_root = new XElement("ReportParameters");
            XElement _x_parameters = new XElement("Parameters");
            XElement _x_queries = new XElement("Queries");

            string downlines_report_name = crmSession.fundraiser_downlines_reportName();

            string downlines_report_storedProc;
            if (!string.IsNullOrEmpty(downlines_report_name))
                downlines_report_storedProc = "i_get_downlines_w_details_w_pledges";
            else
                downlines_report_storedProc = "i_get_downlines_w_details";

            String _ClientCode = "ALL";
            String _ReportName = (String.IsNullOrEmpty(downlines_report_name) ? "Downlines" : downlines_report_name);

            // Create a HeaderNode
            _x_root.Add(new XAttribute("clientcode", _ClientCode),
                        new XAttribute("reportcode", _ReportName));

            // Parameters
            _x_parameters.Add(new XElement("Parameter", new XAttribute("id", "default_collapse_donations"), "True"));
            _x_parameters.Add(new XElement("Parameter", new XAttribute("id", "report_title"), reportName));
            _x_root.Add(_x_parameters);

            // Queries
            _x_root.Add(_x_queries);
            string _sql_script = String.Format("EXEC dbo.{0} {1}, 1", downlines_report_storedProc, trackNo);

            _x_queries.Add(new XElement("Query",
                                          new XAttribute("id", "Dexter"),
                                          _sql_script));

            return _x_root.ToString();
        }
        #endregion

        #endregion
        
        #region [[ Support Methods ]]
        public string GetEitherCodeOrDescription(string _codeWithDescp, int CodeOrDescp)
        {
            string[] _actualData = _codeWithDescp.Split(':');
            if (_actualData.Length >= 1)
            {
                if(CodeOrDescp == 0) return _actualData[0].ToString().Trim();
                if (CodeOrDescp == 1) return _actualData[1].ToString().Trim();
                return "";
            }
            else
            {
                return "";
            }
        }
        
        #region [ Get Fund Code Aggregate Data for Profile Call Sheet ]
        private void buildSCRIPT1()
        {
            _sql_script1 = "DECLARE @FUNDID1 INT " + CrLf;
            _sql_script1 += "DECLARE @FUNDCODE1 VARCHAR(5) " + CrLf;
            _sql_script1 += "DECLARE @FUNDDESC1 VARCHAR(70) " + CrLf;
            _sql_script1 += "DECLARE @GIFT_AMT1 MONEY " + CrLf;
            _sql_script1 += "DECLARE @FUNDID2 INT " + CrLf;
            _sql_script1 += "DECLARE @FUNDCODE2 VARCHAR(5) " + CrLf;
            _sql_script1 += "DECLARE @FUNDDESC2 VARCHAR(70) " + CrLf;
            _sql_script1 += "DECLARE @GIFT_AMT2 MONEY " + CrLf;
            _sql_script1 += "DECLARE @SEQ INT " + CrLf;
            _sql_script1 += "DECLARE @PID1 INT " + CrLf;
            _sql_script1 += "DECLARE @PID2 INT " + CrLf;
            _sql_script1 += "DECLARE @PIDS_ROW_COUNT INT " + CrLf + CrLf;
            _sql_script1 += "SET ANSI_WARNINGS OFF " + CrLf + CrLf;

            _sql_script1 += "CREATE TABLE #TEMP_FUNDSCHED " + CrLf;
            _sql_script1 += "( SEQ       int " + CrLf;
            _sql_script1 += ",FUNDCODE1 char(5) " + CrLf;
            _sql_script1 += ",FUNDDESC1 char(70) " + CrLf;
            _sql_script1 += ",GIFT_AMT1 money " + CrLf;
            _sql_script1 += ",FUNDCODE2 char(5) " + CrLf;
            _sql_script1 += ",FUNDDESC2 char(70) " + CrLf;
            _sql_script1 += ",GIFT_AMT2 money  " + CrLf;
            _sql_script1 += ",PID       int) " + CrLf + CrLf;

            _sql_script1 += "%%#TEMP_PIDS%% " + CrLf + CrLf;        //     --  (put here pSql with ... P.PID 'INTO #TEMP_PIDS' FROM ...

            _sql_script1 += "SELECT CAST(NULL AS VARCHAR) AS FLAG , ISNULL(D.FUNDID,0) AS FUNDID " + CrLf;
            _sql_script1 += ", ISNULL(D.FUNDCODE,'') AS FUNDCODE , ISNULL(D.FUNDDESC ,'') AS FUNDDESC " + CrLf;
            _sql_script1 += ", T.PID AS PID , ISNULL(SUM(M.AMT),0.00) AS GIFT_AMT " + CrLf;
            _sql_script1 += "INTO #TEMP_FUND " + CrLf;
            _sql_script1 += "FROM #TEMP_PIDS T " + CrLf;
            _sql_script1 += "INNER JOIN MONY M ON T.PID=M.PID AND M.COUNTER=1 AND M.SOFTMONEY=0 " + CrLf;
            _sql_script1 += "INNER JOIN dmFUND D ON M.FUNDID = D.FUNDID " + CrLf;
            _sql_script1 += "GROUP BY D.FUNDID, D.FUNDCODE, D.FUNDDESC, T.PID " + CrLf;
            _sql_script1 += "ORDER BY PID, GIFT_AMT DESC " + CrLf + CrLf;

            _sql_script1 += "SET @PIDS_ROW_COUNT=@@ROWCOUNT" + CrLf;
            _sql_script1 += "SELECT @SEQ = 0" + CrLf;
            _sql_script1 += "DECLARE @runTheLoop INT" + CrLf;
            _sql_script1 += "SET @runTheLoop=1" + CrLf + CrLf;

            _sql_script1 += "WHILE @runTheLoop=1" + CrLf;
            _sql_script1 += "BEGIN" + CrLf;
            _sql_script1 += "SET @SEQ = @SEQ + 1" + CrLf;
            _sql_script1 += "SELECT TOP 1 @FUNDID1=FUNDID, @FUNDCODE1=FUNDCODE, @FUNDDESC1=FUNDDESC, @GIFT_AMT1=GIFT_AMT, @PID1=PID, @PID2=0  FROM #TEMP_FUND WHERE FLAG IS NULL" + CrLf;
            _sql_script1 += "IF( @@ROWCOUNT = 0 ) BREAK" + CrLf;
            _sql_script1 += "ELSE" + CrLf;
            _sql_script1 += "UPDATE #TEMP_FUND SET FLAG='Y' WHERE FUNDID=@FUNDID1 AND PID=@PID1" + CrLf;
            _sql_script1 += "INSERT INTO #TEMP_FUNDSCHED ( SEQ, FUNDCODE1,FUNDDESC1,GIFT_AMT1,FUNDCODE2,FUNDDESC2,GIFT_AMT2,PID ) " + CrLf;
            _sql_script1 += "VALUES( @SEQ, @FUNDCODE1,@FUNDDESC1,@GIFT_AMT1,NULL,NULL,NULL, @PID1 )" + CrLf;
            _sql_script1 += "SELECT TOP 1 @FUNDID2=FUNDID, @FUNDCODE2=FUNDCODE, @FUNDDESC2=FUNDDESC, @GIFT_AMT2=GIFT_AMT, @PID2=PID FROM #TEMP_FUND WHERE FLAG IS NULL" + CrLf;
            _sql_script1 += "IF( @@ROWCOUNT = 0 ) BREAK" + CrLf;
            _sql_script1 += "ELSE" + CrLf;
            _sql_script1 += "IF(@PID1=@PID2)" + CrLf;
            _sql_script1 += "BEGIN" + CrLf;
            _sql_script1 += "UPDATE #TEMP_FUNDSCHED SET FUNDCODE2=@FUNDCODE2, FUNDDESC2=@FUNDDESC2, GIFT_AMT2=@GIFT_AMT2, PID=@PID1 WHERE SEQ=@SEQ" + CrLf;
            _sql_script1 += "UPDATE #TEMP_FUND SET FLAG='Y' WHERE FUNDID=@FUNDID2 AND PID=@PID1" + CrLf;
            _sql_script1 += "END" + CrLf;
            _sql_script1 += "END" + CrLf + CrLf;

            _sql_script1 += "   SELECT * FROM #TEMP_FUNDSCHED " + CrLf + CrLf;

            _sql_script1 += "IF OBJECT_ID('tempdb..#TEMP_FUND') IS NOT NULL" + CrLf;
            _sql_script1 += "BEGIN  drop table #TEMP_FUND END" + CrLf;
            _sql_script1 += "IF OBJECT_ID('tempdb..#TEMP_FUNDSCHED') IS NOT NULL" + CrLf;
            _sql_script1 += "BEGIN  drop table #TEMP_FUNDSCHED END" + CrLf;
            _sql_script1 += "IF OBJECT_ID('tempdb..#TEMP_PIDS') IS NOT NULL " + CrLf;
            _sql_script1 += "BEGIN  drop table #TEMP_PIDS END" + CrLf;
        }
        #endregion

        private string get_report_data_sourceDef_in_xml_Extended(string gPID)
        {
            XElement _x_root = new XElement("ReportParameters");
            XElement _x_parameters = new XElement("Parameters");
            XElement _x_queries = new XElement("Queries");

            String _ClientCode = "ALL";

            //custom configured Report for future use
            String _ReportName = "Profile_extended";

            if (_userSession.configItems.Where(a => a.Key == crmConstants.profile_extended).Count() > 0 && _userSession.configItems.Where(a => a.Key == crmConstants.profile_extended).FirstOrDefault().Val != null)
            {
                _ReportName = _userSession.configItems.Where(a => a.Key == crmConstants.profile_extended).FirstOrDefault().Val;
            }

            // Create a HeaderNode
            _x_root.Add(new XAttribute("clientcode", _ClientCode),
                        new XAttribute("reportcode", _ReportName));
            _x_root.Add(_x_queries);

            //Let us know if we are using this field...
            string lblForSTR1 = crmSession.people_str1_field();
            string lblForSTR2 = crmSession.people_str2_field();
            string _sql_script = "Exec z_get_profileDataset_full " + gPID.ToString();

            _x_queries.Add(new XElement("Query",
                                            new XAttribute("id", "iGetProfile"),
                                            _sql_script));

            _sql_script = "select jtpeopledge.PID, PLEDGE.PLEDGEDTE, PLEDGE.PLEDGEAMT, PLEDGE.SRCECODE  from jtpeopledge with (nolock) inner join PLEDGE with (nolock) on jtpeopledge.PLEDGEID = PLEDGE.PLEDGEID  where jtpeopledge.PID = " + gPID.ToString();
            _x_queries.Add(new XElement("SubQuery",
                                          new XAttribute("id", "iGetProfilePledge"),
                                          new XAttribute("reportname", "Profile_Pledge"),
                                          _sql_script));

            String tempTblPIDs = "SELECT PID INTO #TEMP_PIDS FROM People WHERE PID=" + gPID.ToString();
            buildSCRIPT1();

            _sql_script1 = _sql_script1.Replace("%%#TEMP_PIDS%%", tempTblPIDs);

            _x_queries.Add(new XElement("SubQuery",
                                          new XAttribute("id", "iGetDollarByFundCode"),
                                          new XAttribute("reportname", "Profile_DollarByFundCode"),
                                          _sql_script1));

            _sql_script = "SELECT PID, b.EVNTCODE,a.ATTENDDTE, b.EVNTDESC, c.DESCRIP as STATUS FROM jtSPCEVNT a with (nolock) inner join pmSPCEVNT b with (nolock) on a.SPCEVNTID = b.SPCEVNTID  left outer join lkEVNTSTATUS c with (nolock) on a.STATUS = c.STATUS  WHERE PID = " + gPID.ToString() + " ORDER BY 1 DESC";
            _x_queries.Add(new XElement("SubQuery",
                                          new XAttribute("id", "iGetProfileEvnt"),
                                          new XAttribute("reportname", "Profile_Event"),
                                          _sql_script));
            //CLUB
            _sql_script = @"select P.PID, [DC].CLUBCODE AS Club,
            [LKC].DESCRIP AS[Status],
            [JC].SOLICITOR AS Solicitor,
            [JC].CANDIDATE AS CandidateName,
            [JC].RNEWDTE AS RenewalDate,
            CASE WHEN[JC].PRIME = 1 THEN 'Yes' ELSE 'No' END AS PrimaryClub
           from PEOPLE P
INNER JOIN jtCLUB[JC] ON P.PID=JC.PID AND ISNULL(JC.CLOSED,0)=0 
          INNER JOIN lkCLUBSTAT[LKC] ON JC.cSTATUS=LKC.CLUBSTATID
         INNER JOIN pmCLUB[DC] ON JC.CLUBID=DC.CLUBID
        WHERE P.PID IN (" + gPID.ToString() + ") ORDER BY 1 DESC";
            _x_queries.Add(new XElement("SubQuery",
                                          new XAttribute("id", "iGetProfileClub"),
                                          new XAttribute("reportname", "Profile_Club"),
                                          _sql_script));

            _sql_script = string.Format("SELECT * FROM v_people_mony WHERE PID = {0} order by BATCHDTE desc",gPID.ToString());
            _x_queries.Add(new XElement("SubQuery",
                                          new XAttribute("id", "iGetProfileMoneyList"),
                                          new XAttribute("reportname", "Profile_MoneyList"),
                                          _sql_script));

            _sql_script = "SELECT a.PID, a.ACTDATE, REPLACE(ISNULL(b.FNAME,'') + ' ' + ISNULL(b.MNAME,'') + ISNULL(b.LNAME,''), '  ', ' ') as SCHEDFOR,  REPLACE(ISNULL(c.FNAME,'') + ' ' + ISNULL(c.MNAME,'') + ISNULL(c.LNAME,''), '  ', ' ') as SCHEDBY,   a.SUBJECT, a.ASKMEMO, a.NOTE, lkACTTYPE.DESCRIP as 'TYPE', CASE WHEN a.DONE = 1 THEN 'COMPLETED' ELSE 'OPEN' END AS ACTIVITY_STATUS  ";
            _sql_script += " FROM ACTIVITY a with (nolock) left outer join ssUSER b with (nolock) on a.SCHEDFOR = b.UID  left outer join ssUSER c with (nolock) on a.SCHEDBY = c.UID   left outer join lkACTTYPE with (nolock) on a.ACTTYPEID = lkACTTYPE.ACTTYPEID  WHERE  a.DONE = 0 AND a.PID = " + gPID.ToString() + " ORDER BY 1 DESC";
            _x_queries.Add(new XElement("SubQuery",
                                          new XAttribute("id", "iGetProfileTask"),
                                          new XAttribute("reportname", "Profile_Task"),
                                          _sql_script));

            // _sql_script = "select top 10 * from v_people_note where pid = " + gPID.ToString() + " order by histdate desc";                                   // trifs 1 June 2012 2:03 pm
            // _sql_script = "select top 10 * from v_people_note_for_CallSheet where pid = " + gPID.ToString() + " order by histdate desc";                     // trifs 1 June 2012 2:03 pm
            _sql_script = "SELECT TOP 10 PID, ACTHISTID, HISTDATE, SUBJECT, NOTE FROM ACTHIST WHERE PID = " + gPID.ToString() + " ORDER BY HISTDATE DESC";      // trifs 6 June 2012 7:47 am
            _x_queries.Add(new XElement("SubQuery",
                                          new XAttribute("id", "iGetProfileNotes"),
                                          new XAttribute("reportname", "Profile_Notes"),
                                          _sql_script));
            _sql_script = string.IsNullOrEmpty(lblForSTR1) && string.IsNullOrEmpty(lblForSTR2) ? "SELECT TOP 1 NULL AS STR1LBL, NULL AS STR1,NULL AS STR2LBL, NULL AS STR2, 0 as PID FROM PEOPLE" : "SELECT TOP 1 '" + lblForSTR1 + "' AS STR1LBL, STR1, '" + lblForSTR2 + "' AS STR2LBL, STR2," + gPID.ToString() + " AS PID FROM PEOPLE WHERE PID='"+ gPID.ToString() +"'";
            _x_queries.Add(new XElement("Query",
                                          new XAttribute("id", "DataSet7"),
                                          _sql_script));
            _sql_script = @"SELECT AP.PID, AC.[name] AS ActionCategory,A.[name] as [Action],CONVERT(varchar, AP.actionDate, 101) AS [Date],AP.note AS [Note],
dbo.action_getCustomFieldsNameOrValueForAll(AP.customizedData,1,1) AS clName1,dbo.action_getCustomFieldsNameOrValueForAll(AP.customizedData,1,0)  AS clValue1,
dbo.action_getCustomFieldsNameOrValueForAll(AP.customizedData,2,1) AS clName2,dbo.action_getCustomFieldsNameOrValueForAll(AP.customizedData,2,0) AS clValue2,
dbo.action_getCustomFieldsNameOrValueForAll(AP.customizedData,3,1) AS clName3,dbo.action_getCustomFieldsNameOrValueForAll(AP.customizedData,3,0) AS clValue3
FROM ActionPeople AP Inner join [Action] A ON A.id =AP.actionId INNER JOIN ActionCategory AC ON AC.id =A.categoryId
WHERE AP.PID = " + gPID.ToString();
            _x_queries.Add(new XElement("SubQuery",
                                          new XAttribute("id", "iGetProfileAction"),
                                          new XAttribute("reportname", "Profile_Action"),
                                          _sql_script));
            if (_ReportName == "Profile_extended_JasonMiyares")
            {
                _sql_script = @"SELECT R.JTRELATEID,R.PID, RT.DESCRIP AS Relationship, P.FULLNAME_ AS [Name] FROM jtRELATE R INNER JOIN v_people_1 P ON R.RELATETO = P.PID 
INNER JOIN lkRELATETYPE RT ON RT.RELATETYPEID = R.RELATETYPEID
WHERE R.PID = " + gPID.ToString() + @" 
UNION 
SELECT R.JTRELATEID,R.RELATETO AS PID, RT.RECIPROCAL AS Relationship, P.FULLNAME_ AS [Name] FROM jtRELATE R INNER JOIN v_people_1 P ON R.PID = P.PID 
INNER JOIN lkRELATETYPE RT ON RT.RELATETYPEID = R.RELATETYPEID
WHERE R.RELATETO = " + gPID.ToString() + "  ";
                _x_queries.Add(new XElement("SubQuery",
                                              new XAttribute("id", "iGetProfileRelationships"),
                                              new XAttribute("reportname", "Profile_Relationships"),
                                              _sql_script));
            }

            //Back
            return _x_root.ToString();

        }

        private string get_report_data_sourceDef_in_xml_Full(string gPID)
        {
            #region Sample XML
            /*
                 <ReportParameters clientcode="ALL" reportcode="111">
                    <Parameters>
                                    <Parameter id="PID">123456</Parameter>
                                    <Parameter id="PID">456789</Parameter>
                                    <Parameter id="PID">357689</Parameter>
                                    ...
                    </Parameters>

                    <Queries>
                                    <Query id="recordsetName1">select * from PEOPLE where CREATE_DT &gt; '2009-02-03'</Query>
                                    <Query id="recordsetName2">select * from MONY where BATCHDTE &gt; '2009-02-03'</Query>
                                    <SubQuery id="iPhone_detail" reportname="SubReport1">select * from PHN where PID = 123456</Query>
                                    <SubQuery id="iEmail_detail" reportname="SubReport2">select * from PHN where PID = 123456</Query>
                                    ...
                    </Queries>
                </ReportParameters>
             */
            #endregion

            XElement _x_root = new XElement("ReportParameters");
            XElement _x_parameters = new XElement("Parameters");
            XElement _x_queries = new XElement("Queries");

            String _ClientCode = "ALL";

            //custom configured Report
            String _ReportName = "Profile_full";

            if (_userSession.configItems.Where(a => a.Key == crmConstants.profile_full).Count() > 0 && _userSession.configItems.Where(a => a.Key == crmConstants.profile_full).FirstOrDefault().Val != null)
            {
                _ReportName = _userSession.configItems.Where(a => a.Key == crmConstants.profile_full).FirstOrDefault().Val;
            }

            // Create a HeaderNode
            _x_root.Add(new XAttribute("clientcode", _ClientCode),
                        new XAttribute("reportcode", _ReportName));
            _x_root.Add(_x_queries);

            #region [[ For Boehner ]]
            if (_ReportName == "Profile_full_Boehner")
            {
                // trifs 2 Feb 2013 6:48pm
                string _sql_script = "Exec z_get_profileDataset_full_Boehner " + gPID.ToString();

                _x_queries.Add(new XElement("Query",
                                                new XAttribute("id", "iGetProfile"),
                                                _sql_script));

                _sql_script = "SELECT * FROM v_people_mony WHERE PID = " + gPID.ToString() + " ORDER BY BATCHDTE DESC";      // trifs 2 Feb 2013 7:04pm
                _x_queries.Add(new XElement("SubQuery",
                                              new XAttribute("id", "iGetProfile_Boehner_GivingHistory"),
                                              new XAttribute("reportname", "Profile_Boehner_GivingHistory"),
                                              _sql_script));

                _sql_script = "SELECT TOP 10 PID, ACTHISTID, HISTDATE, SUBJECT, NOTE FROM ACTHIST WHERE PID = " + gPID.ToString() + " ORDER BY HISTDATE DESC";      // trifs 2 Feb 2013 7:04pm
                _x_queries.Add(new XElement("SubQuery",
                                              new XAttribute("id", "iGetProfile_Boehner_Notes"),
                                              new XAttribute("reportname", "Profile_Boehner_Notes"),
                                              _sql_script));
            }
            #endregion

            #region [[ For NRCC : Last Updated by Bhavesh ]]
            else if (_ReportName == "Profile_full_NRCC2")
            {
                //Let us know if we are using this field...
                string lblForSTR1 = crmSession.people_str1_field();

                string _sql_script = "";
                //_sql_script = "SELECT ACTIVITY.SUBJECT, ACTIVITY.NOTE,P.PID,P.PID as ACTPID, P.CHKDGT, K.DESCRIP AS PEOTYPE, C.DESCRIP AS PEOCODE, ISNULL(P.PREFIX,'') as PREFIX, ISNULL(P.FNAME,'') as FNAME, ISNULL(P.STR1,'') as STR1, ISNULL(P.MNAME,'') as MNAME, ISNULL(P.LNAME,'') as LNAME, ISNULL(P.SUFFIX,'') as SUFFIX,";
                _sql_script = "SELECT PC.DESCRIP AS INDUSTRY, P.PID,P.PID as ACTPID, P.CHKDGT, K.DESCRIP AS PEOTYPE, C.DESCRIP AS PEOCODE, ISNULL(P.PREFIX,'') as PREFIX, ISNULL(P.FNAME,'') as FNAME, ISNULL(P.STR1,'') as STR1, ISNULL(P.MNAME,'') as MNAME, ISNULL(P.LNAME,'') as LNAME, ISNULL(P.SUFFIX,'') as SUFFIX,";
                _sql_script += " dbo.oFULLNAME(0, P.PREFIX, P.FNAME, P.MNAME, P.LNAME, P.SUFFIX) as NAME, P.OCCUPATION, P.EMPLOYER, P.SALUTATION, P.INFSALUT, P.SPOUSENAME, dbo.oPHONEwMASK2(H.PHNNO) AS HMPH,dbo.oPHONEwMASK2(B.PHNNO) AS BUSPH,H.UPDATEDON AS HMPHUPDON, B.UPDATEDON AS BUSPHUPDON, F.UPDATEDON AS FAXPHUPDON, E.UPDATEDON AS EMAILUPDON, M.UPDATEDON AS CELLPHUPDON";
                _sql_script += " ,dbo.oPHONEwMASK2(F.PHNNO) AS FAXPH, E.PHNNO AS EMAIL, dbo.oPHONEwMASK2(M.PHNNO) AS CELLPH, dbo.oPHONEwMASK2(ASTP.PHNNO) AS ASTPHN, ASTP.UPDATEDON AS ASTPHNUPDON, ASTE.PHNNO AS ASTEMAIL, ASTE.UPDATEDON AS ASTEMAILUPDON,dbo.oPHONEwMASK2(DIRP.PHNNO) AS DIRPHN, DIRP.UPDATEDON AS DIRPHNPUPDON, A.ADDR1, A.ADDR2, A.STREET, ISNULL(A.CITY,'') as CITY, ISNULL(A.STATE,'') as STATE, ISNULL(A.ZIP,'') as ZIP, ISNULL(A.PLUS4,'') as PLUS4,";
                _sql_script += " dbo.oGetFLAGDESCSTR(P.PID) AS FLAGLIST,dbo.oGetKWRDSTR(P.PID) AS KWRDLIST,ISNULL(P.BIO,'') AS BIO, P.PICTURE, P.TITLE, P.ASSISTANT, P.TrackNo, A.CDCODE, lkCOUNTY.COUNTY, ssSystem.ClientName,isnull(SUMMARYD.CTDAMT,0) as CTDAMT,";
                 //Additional clumns for Dataset2 to 6...
                _sql_script += " ACTHIST.SUBJECT AS ASKSUBJECT, ACTHIST.NOTE AS ASKNOTE, SUMMARYDINFO.PREV1YRAMT,SUMMARYDINFO.PREV2YRAMT,SUMMARYDINFO.PREV3YRAMT, SUMMARYDINFO.YTDAMT, CLUBINFO.CLUBCODE, CLUBINFO.CLUBSTAT, CLUBINFO.RNEWDTE, ACTHISTINFO.SUBJECT AS CALLSUBJECT, ACTHISTINFO.NOTE AS CALLNOTE, PLEDGEINFO.PLEDGEAMT, PLEDGEINFO.PLEDGEDTE";

                _sql_script += " FROM PEOPLE P with (nolock) left outer join ADDRESS A with (nolock) on P.PID = A.PID AND A.PRIME=1";
                _sql_script += " left outer join lkCOUNTY with (nolock) on lkCOUNTY.STATE = A.STATE AND lkCOUNTY.FIPS = A.COUNTY";
                _sql_script += " left outer join lkPEOTYPE K with (nolock) on P.PEOTYPEID = K.PEOTYPEID";
                _sql_script += " left outer join lkPEOCODE C with (nolock) on P.PEOCODEID = C.PEOCODEID";
                _sql_script += " LEFT OUTER JOIN LKPEOCLASS PC with (nolock) on P.PEOCLASSID = PC.PEOCLASSID";
                _sql_script += " left outer join PHONE H with (nolock) on P.PID = H.PID AND H.PHNTYPEID=1 AND H.PRIME=1";
                _sql_script += " left outer join PHONE B with (nolock) on P.PID = B.PID AND B.PHNTYPEID=2 AND B.PRIME=1";
                _sql_script += " left outer join PHONE F with (nolock) on P.PID = F.PID AND F.PHNTYPEID=5 AND F.PRIME=1";
                _sql_script += " left outer join PHONE E with (nolock) on P.PID = E.PID AND E.PHNTYPEID=4 AND E.PRIME=1";
                _sql_script += " left outer join PHONE M with (nolock) on P.PID = M.PID AND M.PHNTYPEID=3 AND M.PRIME=1";

                _sql_script += " LEFT OUTER JOIN PHONE ASTP ON P.PID=ASTP.PID AND ASTP.PHNTYPEID=11 AND ASTP.PRIME=1 ";
                _sql_script += " LEFT OUTER JOIN PHONE ASTE ON P.PID=ASTE.PID AND ASTE.PHNTYPEID=12 AND ASTE.PRIME=1 ";
                _sql_script += " LEFT OUTER JOIN PHONE DIRP ON P.PID=DIRP.PID AND DIRP.PHNTYPEID=13 AND DIRP.PRIME=1 ";
                
                _sql_script += " left outer join SUMMARYD with (nolock) on P.PID = SUMMARYD.PID";
                //_sql_script += " left outer join (SELECT TOP 1 ACTIVITY.PID, ACTIVITY.SUBJECT, ACTIVITY.NOTE from ACTIVITY with (nolock) inner join lkacttype with (nolock) on ACTIVITY.ACTTYPEID = lkacttype.ACTTYPEID where ACTIVITY.DONE = 0 and lkacttype.ACTTYPE = 'CL' and ACTIVITY.PID = " + gPID.ToString() + " order by ACTDATE desc) ACTIVITY on P.PID = ACTIVITY.PID";
                //For Dataset2
                _sql_script += " LEFT OUTER JOIN (SELECT TOP 1 ACTHIST.PID, ACTHIST.ACTHISTID, ACTHIST.HISTDATE, ACTHIST.SUBJECT, ACTHIST.NOTE FROM ACTHIST WHERE UPPER(SUBJECT) = 'ASK' AND PID = " + gPID.ToString() + " ORDER BY HISTDATE DESC) ACTHIST ON ACTHIST.PID = P.PID";
                //For Dataset3
                _sql_script += " LEFT OUTER JOIN (SELECT SUMMARYD.PID, ISNULL(SUMMARYD.YTDAMT,0) AS YTDAMT, ISNULL(SUMMARYD.PREV1YRAMT,0) AS PREV1YRAMT,ISNULL(SUMMARYD.PREV2YRAMT,0) AS PREV2YRAMT,ISNULL(SUMMARYD.PREV3YRAMT,0) AS PREV3YRAMT FROM SUMMARYD WHERE PID = " + gPID.ToString() + ") SUMMARYDINFO ON SUMMARYDINFO.PID = P.PID";
                //For Dataset4
                _sql_script += " LEFT OUTER JOIN (SELECT PID, ISNULL(PMC.CLUBCODE,'') AS CLUBCODE, ISNULL(cbs.CLUBSTAT,'') AS CLUBSTAT, jtc.RNEWDTE AS RNEWDTE FROM JTCLUB jtc inner join pmclub pmc on pmc.CLUBID = jtc.CLUBID inner join lkCLUBSTAT cbs on cbs.CLUBSTATID = jtc.Cstatus WHERE jtc.prime =1 and jtc.pid = " + gPID.ToString() + ") CLUBINFO ON CLUBINFO.PID = P.PID";
                //For Dataset5
                _sql_script += " LEFT OUTER JOIN (SELECT TOP 1 ACTHIST.PID, ACTHIST.ACTHISTID, ACTHIST.HISTDATE, ACTHIST.SUBJECT, ACTHIST.NOTE FROM ACTHIST WHERE UPPER(SUBJECT) = 'CALL NOTES' AND PID = " + gPID.ToString() + " ORDER BY HISTDATE DESC) ACTHISTINFO ON ACTHISTINFO.PID = P.PID";
                //FOR DATASET6
                _sql_script += " LEFT OUTER JOIN (SELECT TOP 1 PID, PLEDGEDTE, ISNULL(PLEDGEAMT,0) AS PLEDGEAMT from PLEDGE  PG INNER JOIN PEOPLE P ON P.TRACKNO = PG.TRACKNO AND P.PID = " + gPID.ToString() + ") PLEDGEINFO ON PLEDGEINFO.PID = P.PID";
                _sql_script += " left outer join (select top 1 ClientName from ssSystem order by DateImplem) ssSystem on 1=1";
                _sql_script += " WHERE P.PID = " + gPID.ToString();
                _x_queries.Add(new XElement("Query",
                                              new XAttribute("id", "iGetProfile"),
                                              _sql_script));

                #region [[ Old code ]]
                //_sql_script = "SELECT TOP 1 PID, ACTHISTID, HISTDATE, SUBJECT, NOTE FROM ACTHIST WHERE UPPER(SUBJECT) = 'ASK' AND PID = " + gPID.ToString() + " ORDER BY HISTDATE DESC";
                //_x_queries.Add(new XElement("Query",
                //                              new XAttribute("id", "DataSet2"),
                //                              _sql_script));

                //_sql_script = "SELECT PID, ISNULL(YTDAMT,0) AS YTDAMT, ISNULL(PREV1YRAMT,0) AS PREV1YRAMT,ISNULL(PREV2YRAMT,0) AS PREV2YRAMT,ISNULL(PREV3YRAMT,0) AS PREV3YRAMT from SUMMARYD WHERE PID = " + gPID.ToString();
                //_x_queries.Add(new XElement("Query",
                //                              new XAttribute("id", "DataSet3"),
                //                              _sql_script));

                //_sql_script = "SELECT PID, ISNULL(PMC.CLUBCODE,'') AS CLUBCODE, ISNULL(cbs.CLUBSTAT,'') AS CLUBSTAT, jtc.RNEWDTE AS RNEWDTE FROM JTCLUB jtc inner join pmclub pmc on pmc.CLUBID = jtc.CLUBID inner join lkCLUBSTAT cbs on cbs.CLUBSTATID = jtc.Cstatus WHERE jtc.prime =1 and jtc.pid = " + gPID.ToString();
                //_x_queries.Add(new XElement("Query",
                //                              new XAttribute("id", "DataSet4"),
                //                              _sql_script));

                //_sql_script = "SELECT TOP 1 PID, ACTHISTID, HISTDATE, SUBJECT, NOTE FROM ACTHIST WHERE UPPER(SUBJECT) = 'CALL NOTES' AND PID = " + gPID.ToString() + " ORDER BY HISTDATE DESC";
                //_x_queries.Add(new XElement("Query",
                //                              new XAttribute("id", "DataSet5"),
                //                              _sql_script));

                //_sql_script = "SELECT TOP 1 PID, PLEDGEDTE, ISNULL(PLEDGEAMT,0) AS PLEDGEAMT from PLEDGE  PG INNER JOIN PEOPLE P ON P.TRACKNO = PG.TRACKNO AND P.PID = " + gPID.ToString();
                //_x_queries.Add(new XElement("Query",
                //                              new XAttribute("id", "DataSet6"),
                //                              _sql_script));
                #endregion
                
                _sql_script = string.IsNullOrEmpty(lblForSTR1) ? "SELECT TOP 1 NULL AS STR1LBL, 0 as PID FROM ACTHIST" : "SELECT TOP 1 '" + lblForSTR1 + "' AS STR1LBL, " + gPID.ToString() + " AS PID FROM ACTHIST";
                _x_queries.Add(new XElement("Query",
                                              new XAttribute("id", "DataSet7"),
                                              _sql_script));

                return _x_root.ToString();
            }
            #endregion

            #region [[ For rickscott ]]
            else if (_ReportName == "Profile_full_rickscott")
            {
                string _sql_script = "Exec z_get_profileDataset_full " + gPID.ToString();

                _x_queries.Add(new XElement("Query",
                                                new XAttribute("id", "iGetProfile"),
                                                _sql_script));

                _sql_script = "select jtpeopledge.PID, PLEDGE.PLEDGEDTE, PLEDGE.PLEDGEAMT, PLEDGE.SRCECODE  from jtpeopledge with (nolock) inner join PLEDGE with (nolock) on jtpeopledge.PLEDGEID = PLEDGE.PLEDGEID  where jtpeopledge.PID = " + gPID.ToString();
                _x_queries.Add(new XElement("SubQuery",
                                              new XAttribute("id", "iGetProfilePledge"),
                                              new XAttribute("reportname", "Profile_Pledge"),
                                              _sql_script));

                String tempTblPIDs = "SELECT PID INTO #TEMP_PIDS FROM People WHERE PID=" + gPID.ToString();
                buildSCRIPT1();

                _sql_script1 = _sql_script1.Replace("%%#TEMP_PIDS%%", tempTblPIDs);

                _x_queries.Add(new XElement("SubQuery",
                                              new XAttribute("id", "iGetDollarByFundCode"),
                                              new XAttribute("reportname", "Profile_DollarByFundCode"),
                                              _sql_script1));

                _sql_script = "SELECT PID, b.EVNTCODE,a.ATTENDDTE, b.EVNTDESC, c.DESCRIP as STATUS FROM jtSPCEVNT a with (nolock) inner join pmSPCEVNT b with (nolock) on a.SPCEVNTID = b.SPCEVNTID  left outer join lkEVNTSTATUS c with (nolock) on a.STATUS = c.STATUS  WHERE PID = " + gPID.ToString() + " ORDER BY 1 DESC";
                _x_queries.Add(new XElement("SubQuery",
                                              new XAttribute("id", "iGetProfileEvnt"),
                                              new XAttribute("reportname", "Profile_Event"),
                                              _sql_script));

                _sql_script = "SELECT TOP 3 a.PID, a.ACTDATE, REPLACE(ISNULL(b.FNAME,'') + ' ' + ISNULL(b.MNAME,'') + ISNULL(b.LNAME,''), '  ', ' ') as SCHEDFOR,  REPLACE(ISNULL(c.FNAME,'') + ' ' + ISNULL(c.MNAME,'') + ISNULL(c.LNAME,''), '  ', ' ') as SCHEDBY,   a.SUBJECT, a.ASKMEMO, a.NOTE, lkACTTYPE.DESCRIP as 'TYPE', CASE WHEN a.DONE = 1 THEN 'COMPLETED' ELSE 'OPEN' END AS ACTIVITY_STATUS  ";
                _sql_script += " FROM ACTIVITY a with (nolock) left outer join ssUSER b with (nolock) on a.SCHEDFOR = b.UID  left outer join ssUSER c with (nolock) on a.SCHEDBY = c.UID   left outer join lkACTTYPE with (nolock) on a.ACTTYPEID = lkACTTYPE.ACTTYPEID  WHERE  a.DONE = 0 AND a.PID = " + gPID.ToString() + " ORDER BY a.ACTDATE DESC";
                _x_queries.Add(new XElement("SubQuery",
                                              new XAttribute("id", "iGetProfileTask"),
                                              new XAttribute("reportname", "Profile_Task"),
                                              _sql_script));

                // _sql_script = "select top 10 * from v_people_note where pid = " + gPID.ToString() + " order by histdate desc";                                   // trifs 1 June 2012 2:03 pm
                // _sql_script = "select top 10 * from v_people_note_for_CallSheet where pid = " + gPID.ToString() + " order by histdate desc";                     // trifs 1 June 2012 2:03 pm
                _sql_script = "SELECT TOP 2 PID, ACTHISTID, HISTDATE, SUBJECT, NOTE FROM ACTHIST WHERE PID = " + gPID.ToString() + " ORDER BY HISTDATE DESC";      // trifs 6 June 2012 7:47 am
                _x_queries.Add(new XElement("SubQuery",
                                              new XAttribute("id", "iGetProfileNotes"),
                                              new XAttribute("reportname", "Profile_Notes"),
                                              _sql_script));
            }
            #endregion

            #region [[ For All Other Clients ]]
            else
            {
                // trifs 12 June 2012 9:11am
                string _sql_script = "Exec z_get_profileDataset_full " + gPID.ToString();

                _x_queries.Add(new XElement("Query",
                                                new XAttribute("id", "iGetProfile"),
                                                _sql_script));

                _sql_script = "select jtpeopledge.PID, PLEDGE.PLEDGEDTE, PLEDGE.PLEDGEAMT, PLEDGE.SRCECODE  from jtpeopledge with (nolock) inner join PLEDGE with (nolock) on jtpeopledge.PLEDGEID = PLEDGE.PLEDGEID  where jtpeopledge.PID = " + gPID.ToString();
                _x_queries.Add(new XElement("SubQuery",
                                              new XAttribute("id", "iGetProfilePledge"),
                                              new XAttribute("reportname", "Profile_Pledge"),
                                              _sql_script));

                String tempTblPIDs = "SELECT PID INTO #TEMP_PIDS FROM People WHERE PID=" + gPID.ToString();
                buildSCRIPT1();
                
                _sql_script1 = _sql_script1.Replace("%%#TEMP_PIDS%%", tempTblPIDs);

                _x_queries.Add(new XElement("SubQuery",
                                              new XAttribute("id", "iGetDollarByFundCode"),
                                              new XAttribute("reportname", "Profile_DollarByFundCode"),
                                              _sql_script1));

                _sql_script = "SELECT PID, b.EVNTCODE,a.ATTENDDTE, b.EVNTDESC, c.DESCRIP as STATUS FROM jtSPCEVNT a with (nolock) inner join pmSPCEVNT b with (nolock) on a.SPCEVNTID = b.SPCEVNTID  left outer join lkEVNTSTATUS c with (nolock) on a.STATUS = c.STATUS  WHERE PID = " + gPID.ToString() + " ORDER BY 1 DESC";
                _x_queries.Add(new XElement("SubQuery",
                                              new XAttribute("id", "iGetProfileEvnt"),
                                              new XAttribute("reportname", "Profile_Event"),
                                              _sql_script));

                _sql_script = "SELECT a.PID, a.ACTDATE, REPLACE(ISNULL(b.FNAME,'') + ' ' + ISNULL(b.MNAME,'') + ISNULL(b.LNAME,''), '  ', ' ') as SCHEDFOR,  REPLACE(ISNULL(c.FNAME,'') + ' ' + ISNULL(c.MNAME,'') + ISNULL(c.LNAME,''), '  ', ' ') as SCHEDBY,   a.SUBJECT, a.ASKMEMO, a.NOTE, lkACTTYPE.DESCRIP as 'TYPE', CASE WHEN a.DONE = 1 THEN 'COMPLETED' ELSE 'OPEN' END AS ACTIVITY_STATUS  ";
                _sql_script += " FROM ACTIVITY a with (nolock) left outer join ssUSER b with (nolock) on a.SCHEDFOR = b.UID  left outer join ssUSER c with (nolock) on a.SCHEDBY = c.UID   left outer join lkACTTYPE with (nolock) on a.ACTTYPEID = lkACTTYPE.ACTTYPEID  WHERE  a.DONE = 0 AND a.PID = " + gPID.ToString() + " ORDER BY 1 DESC";
                _x_queries.Add(new XElement("SubQuery",
                                              new XAttribute("id", "iGetProfileTask"),
                                              new XAttribute("reportname", "Profile_Task"),
                                              _sql_script));

                // _sql_script = "select top 10 * from v_people_note where pid = " + gPID.ToString() + " order by histdate desc";                                   // trifs 1 June 2012 2:03 pm
                // _sql_script = "select top 10 * from v_people_note_for_CallSheet where pid = " + gPID.ToString() + " order by histdate desc";                     // trifs 1 June 2012 2:03 pm
                _sql_script = "SELECT TOP 10 PID, ACTHISTID, HISTDATE, SUBJECT, NOTE FROM ACTHIST WHERE PID = " + gPID.ToString() + " ORDER BY HISTDATE DESC";      // trifs 6 June 2012 7:47 am
                _x_queries.Add(new XElement("SubQuery",
                                              new XAttribute("id", "iGetProfileNotes"),
                                              new XAttribute("reportname", "Profile_Notes"),
                                              _sql_script));
                _sql_script = @"SELECT AP.PID, AC.[name] AS ActionCategory,A.[name] as [Action],CONVERT(varchar, AP.actionDate, 101) AS [Date],AP.note AS [Note],
dbo.action_getCustomFieldsNameOrValueForAll(AP.customizedData,1,1) AS clName1,dbo.action_getCustomFieldsNameOrValueForAll(AP.customizedData,1,0)  AS clValue1,
dbo.action_getCustomFieldsNameOrValueForAll(AP.customizedData,2,1) AS clName2,dbo.action_getCustomFieldsNameOrValueForAll(AP.customizedData,2,0) AS clValue2,
dbo.action_getCustomFieldsNameOrValueForAll(AP.customizedData,3,1) AS clName3,dbo.action_getCustomFieldsNameOrValueForAll(AP.customizedData,3,0) AS clValue3
FROM ActionPeople AP Inner join [Action] A ON A.id =AP.actionId INNER JOIN ActionCategory AC ON AC.id =A.categoryId
WHERE AP.PID = " + gPID.ToString();
                _x_queries.Add(new XElement("SubQuery",
                                              new XAttribute("id", "iGetProfileAction"),
                                              new XAttribute("reportname", "Profile_Action"),
                                              _sql_script));
            }
            #endregion

            //Back
            return _x_root.ToString();
                       
        }

        private string get_report_data_sourceDef_in_xml_Brief(string gPID)
        {
            XElement _x_root = new XElement("ReportParameters");
            XElement _x_parameters = new XElement("Parameters");
            XElement _x_queries = new XElement("Queries");

            String _ClientCode = "ALL";

            //custom configured Report
            String _ReportName = "Profile_brief";                          // as per Rakesh ... 9 Aug 2011 3:59pm

            if (_userSession.configItems.Where(a => a.Key == crmConstants.profile_brief).Count() > 0 && _userSession.configItems.Where(a => a.Key == crmConstants.profile_brief).FirstOrDefault().Val != null)
            {
                _ReportName = _userSession.configItems.Where(a => a.Key == crmConstants.profile_brief).FirstOrDefault().Val;
            }

            // Create a HeaderNode
            _x_root.Add(new XAttribute("clientcode", _ClientCode),
                        new XAttribute("reportcode", _ReportName));
            _x_root.Add(_x_queries);

            string _sql_script = "";

            if (_ReportName == "TNGPeopleCallSheet")
            {
                return _supportMethods.get_report_data_sourceDef_in_xml_TNGPeopleCallSheet(gPID, null);
            }
            if(_ReportName == "Profile_brief_Emmer")
            {
                return _supportMethods.get_report_data_sourceDef_in_xml_Profile_brief_Emmer(gPID, null);
            }
            if (_ReportName == "Profile_brief_NRSC")
            {
                return _supportMethods.get_report_data_sourceDef_in_xml_Profile_brief_NRSC(gPID, null);
            }
            if (_ReportName == "Profile_brief_NRCC")
            {
                supportMethods _supportMethods = new supportMethods();
                _sql_script = string.Format(_supportMethods.get_sql_for_Profile_brief_NRCC(), gPID.ToString());
            }
            else
            {
                _sql_script = "SELECT ACTIVITY.SUBJECT,ACTIVITY.ASKMEMO, ACTIVITY.ACTDATE,ACTIVITY.NOTE,P.PID, P.CHKDGT, K.DESCRIP AS PEOTYPE, Q.DESCRIP AS PEOCLASS, C.DESCRIP AS PEOCODE, ISNULL(P.PREFIX,'') as PREFIX, ISNULL(P.FNAME,'') as FNAME, ISNULL(P.MNAME,'') as MNAME, ISNULL(P.LNAME,'') as LNAME, ISNULL(P.SUFFIX,'') as SUFFIX, ";
                _sql_script += " dbo.oFULLNAME(0, P.PREFIX, P.FNAME, P.MNAME, P.LNAME, P.SUFFIX) as NAME, P.OCCUPATION, P.EMPLOYER, P.SALUTATION, P.INFSALUT, P.SPOUSENAME, dbo.oPHONEwMASK2(H.PHNNO) AS HMPH,dbo.oPHONEwMASK2(B.PHNNO) AS BUSPH";
                _sql_script += " ,dbo.oPHONEwMASK2(F.PHNNO) AS FAXPH, E.PHNNO AS EMAIL, dbo.oPHONEwMASK2(M.PHNNO) AS CELLPH, dbo.oPHONEwMASK2(ASTP.PHNNO) AS ASTPHN, ASTE.PHNNO AS ASTEMAIL,  dbo.oPHONEwMASK2(DIRP.PHNNO) AS DIRPHN, A.ADDR1, A.ADDR2, A.STREET, ISNULL(A.CITY,'') as CITY, ISNULL(A.STATE,'') as STATE, ISNULL(A.ZIP,'') as ZIP, ISNULL(A.PLUS4,'') as PLUS4,";
                _sql_script += " dbo.oGetFLAGDESCSTR(P.PID) AS FLAGLIST,dbo.oGetKWRDSTR(P.PID) AS KWRDLIST,P.BIO, P.PICTURE, P.TITLE, P.ASSISTANT, P.TrackNo, A.CDCODE, lkCOUNTY.COUNTY, ssSystem.ClientName";
                _sql_script += " ,ISNULL(S.LGIFT,0) as LGIFT,S.LGIFTDTE,ISNULL(S.NOGIFTS,0) AS NOGIFTS,ISNULL(S.CTDAMT,0.00) as CTDAMT,ISNULL(S.CUMTOT,0.00) AS CUMTOT ";
                _sql_script += " ,ISNULL(S.YTDAMT,0.00) AS YTDAMT, dbo.cacheVal('appversion') AS appVersion, dbo.oGetFLAGDESCSTR(P.PID) as FLAGLIST, dbo.oGetKWRDSTR(P.PID) as KWRDLIST ";
                _sql_script += " FROM PEOPLE P with (nolock) left outer join ADDRESS A with (nolock) on P.PID = A.PID AND A.PRIME=1";
                _sql_script += " left outer join lkCOUNTY with (nolock) on lkCOUNTY.STATE = A.STATE AND lkCOUNTY.FIPS = A.COUNTY";
                _sql_script += " left outer join lkPEOTYPE K with (nolock) on P.PEOTYPEID = K.PEOTYPEID";
                _sql_script += " left outer join lkPEOCLASS Q with (nolock) on P.PEOCLASSID = Q.PEOCLASSID";
                _sql_script += " left outer join lkPEOCODE C with (nolock) on P.PEOCODEID = C.PEOCODEID";
                _sql_script += " left outer join PHONE H with (nolock) on P.PID = H.PID AND H.PHNTYPEID=1 AND H.PRIME=1";
                _sql_script += " left outer join PHONE B with (nolock) on P.PID = B.PID AND B.PHNTYPEID=2 AND B.PRIME=1";
                _sql_script += " left outer join PHONE F with (nolock) on P.PID = F.PID AND F.PHNTYPEID=5 AND F.PRIME=1";
                _sql_script += " left outer join PHONE E with (nolock) on P.PID = E.PID AND E.PHNTYPEID=4 AND E.PRIME=1";
                _sql_script += " left outer join PHONE M with (nolock) on P.PID = M.PID AND M.PHNTYPEID=3 AND M.PRIME=1";

                _sql_script += " LEFT OUTER JOIN PHONE ASTP with (nolock) ON P.PID=ASTP.PID AND ASTP.PHNTYPEID=11 AND ASTP.PRIME=1";
                _sql_script += " LEFT OUTER JOIN PHONE ASTE with (nolock) ON P.PID=ASTE.PID AND ASTE.PHNTYPEID=12 AND ASTE.PRIME=1";
                _sql_script += " LEFT OUTER JOIN PHONE DIRP with (nolock) ON P.PID=DIRP.PID AND DIRP.PHNTYPEID=13 AND DIRP.PRIME=1";

                _sql_script += " left outer join SUMMARYD S with (nolock) on P.PID = S.PID";
                //_sql_script += " left outer join (select top 1 ACTIVITY.PID, ACTIVITY.SUBJECT,ACTIVITY.ASKMEMO, ACTIVITY.ACTDATE, ACTIVITY.NOTE from ACTIVITY with (nolock) inner join lkacttype with (nolock) on ACTIVITY.ACTTYPEID = lkacttype.ACTTYPEID where ACTIVITY.DONE = 0 and ( lkacttype.ACTTYPE = 'CL' OR lkacttype.ACTTYPE = 'SC' ) and ACTIVITY.PID = " + gPID.ToString() + " order by ACTDATE desc) ACTIVITY on P.PID = ACTIVITY.PID";
                _sql_script += " left outer join (select top 1 ACTIVITY.PID, ACTIVITY.SUBJECT,ACTIVITY.ASKMEMO, ACTIVITY.ACTDATE, ACTIVITY.NOTE from ACTIVITY with (nolock) inner join lkacttype with (nolock) on ACTIVITY.ACTTYPEID = lkacttype.ACTTYPEID where ACTIVITY.DONE = 0 and ACTIVITY.PID = " + gPID.ToString() + " order by ACTDATE desc) ACTIVITY on P.PID = ACTIVITY.PID";
                _sql_script += " left outer join (select top 1 ClientName from ssSystem order by DateImplem) ssSystem on 1=1";
                _sql_script += " WHERE P.PID = " + gPID.ToString();
            }
            
            _x_queries.Add(new XElement("Query",
                                            new XAttribute("id", "iGetProfile"),
                                            _sql_script));

            String tempTblPIDs = "SELECT PID INTO #TEMP_PIDS FROM People WHERE PID=" + gPID.ToString();
            buildSCRIPT1();
            _sql_script1 = _sql_script1.Replace("%%#TEMP_PIDS%%", tempTblPIDs);
            _x_queries.Add(new XElement("SubQuery",
                                          new XAttribute("id", "iGetDollarByFundCode"),
                                          new XAttribute("reportname", "Profile_DollarByFundCode"),
                                          _sql_script1));

            _sql_script = "select jtpeopledge.PID, PLEDGE.PLEDGEDTE, PLEDGE.PLEDGEAMT, PLEDGE.SRCECODE  from jtpeopledge with (nolock) inner join PLEDGE with (nolock) on jtpeopledge.PLEDGEID = PLEDGE.PLEDGEID  where jtpeopledge.PID = " + gPID.ToString();
            _x_queries.Add(new XElement("SubQuery",
                                          new XAttribute("id", "iGetProfilePledge"),
                                          new XAttribute("reportname", "Profile_Pledge"),
                                          _sql_script));

            return _x_root.ToString();
        }

        private string get_report_data_sourceDef_in_xml_PAC(string gPID)
        {
            XElement _x_root = new XElement("ReportParameters");
            XElement _x_parameters = new XElement("Parameters");
            XElement _x_queries = new XElement("Queries");

            String _ClientCode = "ALL";

            //custom configured Report
            String _ReportName = "Profile_PAC";
            if (_userSession.configItems.Where(a => a.Key == crmConstants.profile_PAC).Count() > 0 && _userSession.configItems.Where(a => a.Key == crmConstants.profile_PAC).FirstOrDefault().Val != null)
            {
                _ReportName = _userSession.configItems.Where(a => a.Key == crmConstants.profile_PAC).FirstOrDefault().Val;
            }
            if (_ReportName == "TNGPACCallSheet")
            {
                return _supportMethods.get_report_data_sourceDef_in_xml_TNGPACCallSheet(gPID);
            }
            // Create a HeaderNode
            _x_root.Add(new XAttribute("clientcode", _ClientCode),
                        new XAttribute("reportcode", _ReportName));
            _x_root.Add(_x_queries);

            string _sql_script = "";
            _sql_script = "SELECT ACTIVITY.SUBJECT, ACTIVITY.ASKMEMO, ACTIVITY.ACTDATE, ACTIVITY.NOTE,P.PID, P.CHKDGT, K.DESCRIP AS PEOTYPE, C.DESCRIP AS PEOCODE, ISNULL(P.PREFIX,'') as PREFIX, ISNULL(P.FNAME,'') as FNAME, ISNULL(P.MNAME,'') as MNAME, ISNULL(P.LNAME,'') as LNAME, ISNULL(P.SUFFIX,'') as SUFFIX, ";
            _sql_script += " dbo.oFULLNAME(0, P.PREFIX, P.FNAME, P.MNAME, P.LNAME, P.SUFFIX) as NAME, P.OCCUPATION, P.EMPLOYER, P.SALUTATION, P.INFSALUT, P.SPOUSENAME, dbo.oPHONEwMASK2(H.PHNNO) AS HMPH,dbo.oPHONEwMASK2(B.PHNNO) AS BUSPH";
            _sql_script += " ,dbo.oPHONEwMASK2(F.PHNNO) AS FAXPH, E.PHNNO AS EMAIL, dbo.oPHONEwMASK2(M.PHNNO) AS CELLPH, dbo.oPHONEwMASK2(ASTP.PHNNO) AS ASTPHN, ASTE.PHNNO AS ASTEMAIL,  dbo.oPHONEwMASK2(DIRP.PHNNO) AS DIRPHN, A.ADDR1, A.ADDR2, A.STREET, ISNULL(A.CITY,'') as CITY, ISNULL(A.STATE,'') as STATE, ISNULL(A.ZIP,'') as ZIP, ISNULL(A.PLUS4,'') as PLUS4,";
            _sql_script += " dbo.oGetFLAGDESCSTR(P.PID) AS FLAGLIST,dbo.oGetKWRDSTR(P.PID) AS KWRDLIST,P.BIO, P.PICTURE, P.TITLE, P.ASSISTANT, P.TrackNo, A.CDCODE, lkCOUNTY.COUNTY, ssSystem.ClientName";
            _sql_script += " ,ISNULL(S.LGIFT,0) as LGIFT,S.LGIFTDTE,ISNULL(S.NOGIFTS,0) AS NOGIFTS,ISNULL(S.CTDAMT,0.00) as CTDAMT,ISNULL(S.CUMTOT,0.00) AS CUMTOT ";
            _sql_script += " ,ISNULL(S.YTDAMT,0.00) AS YTDAMT, dbo.cacheVal('appversion') AS appVersion, dbo.oGetFLAGDESCSTR(P.PID) as FLAGLIST, dbo.oGetKWRDSTR(P.PID) as KWRDLIST ";
            _sql_script += " FROM PEOPLE P with (nolock) left outer join ADDRESS A with (nolock) on P.PID = A.PID AND A.PRIME=1";
            _sql_script += " left outer join lkCOUNTY with (nolock) on lkCOUNTY.STATE = A.STATE AND lkCOUNTY.FIPS = A.COUNTY";
            _sql_script += " left outer join lkPEOTYPE K with (nolock) on P.PEOTYPEID = K.PEOTYPEID";
            _sql_script += " left outer join lkPEOCODE C with (nolock) on P.PEOCODEID = C.PEOCODEID";
            _sql_script += " left outer join PHONE H with (nolock) on P.PID = H.PID AND H.PHNTYPEID=1 AND H.PRIME=1";
            _sql_script += " left outer join PHONE B with (nolock) on P.PID = B.PID AND B.PHNTYPEID=2 AND B.PRIME=1";
            _sql_script += " left outer join PHONE F with (nolock) on P.PID = F.PID AND F.PHNTYPEID=5 AND F.PRIME=1";
            _sql_script += " left outer join PHONE E with (nolock) on P.PID = E.PID AND E.PHNTYPEID=4 AND E.PRIME=1";
            _sql_script += " left outer join PHONE M with (nolock) on P.PID = M.PID AND M.PHNTYPEID=3 AND M.PRIME=1";
            _sql_script += " LEFT OUTER JOIN PHONE ASTP with (nolock) ON P.PID=ASTP.PID AND ASTP.PHNTYPEID=11 AND ASTP.PRIME=1";
            _sql_script += " LEFT OUTER JOIN PHONE ASTE with (nolock) ON P.PID=ASTE.PID AND ASTE.PHNTYPEID=12 AND ASTE.PRIME=1";
            _sql_script += " LEFT OUTER JOIN PHONE DIRP with (nolock) ON P.PID=DIRP.PID AND DIRP.PHNTYPEID=13 AND DIRP.PRIME=1";
            _sql_script += " left outer join SUMMARYD S with (nolock) on P.PID = S.PID";
            //_sql_script += " left outer join (select top 1 ACTIVITY.PID, ACTIVITY.SUBJECT, ACTIVITY.ASKMEMO, ACTIVITY.ACTDATE, ACTIVITY.NOTE from ACTIVITY with (nolock) inner join lkacttype with (nolock) on ACTIVITY.ACTTYPEID = lkacttype.ACTTYPEID where ACTIVITY.DONE = 0 and lkacttype.ACTTYPE = 'CL' and ACTIVITY.PID = " + gPID.ToString() + " order by ACTDATE desc) ACTIVITY on P.PID = ACTIVITY.PID";
            _sql_script += " left outer join (select top 1 ACTIVITY.PID, ACTIVITY.SUBJECT, ACTIVITY.ASKMEMO, ACTIVITY.ACTDATE, ACTIVITY.NOTE from ACTIVITY with (nolock) inner join lkacttype with (nolock) on ACTIVITY.ACTTYPEID = lkacttype.ACTTYPEID where ACTIVITY.DONE = 0 and ACTIVITY.PID = " + gPID.ToString() + " order by ACTDATE desc) ACTIVITY on P.PID = ACTIVITY.PID";
            _sql_script += " left outer join (select top 1 ClientName from ssSystem order by DateImplem) ssSystem on 1=1";
            _sql_script += " WHERE P.PID = " + gPID.ToString();
            _x_queries.Add(new XElement("Query",
                                            new XAttribute("id", "iGetProfile"),
                                            _sql_script));

            String tempTblPIDs = "SELECT PID INTO #TEMP_PIDS FROM People WHERE PID=" + gPID.ToString();
            buildSCRIPT1();
            _sql_script1 = _sql_script1.Replace("%%#TEMP_PIDS%%", tempTblPIDs);
            _x_queries.Add(new XElement("SubQuery",
                                          new XAttribute("id", "iGetDollarByFundCode"),
                                          new XAttribute("reportname", "Profile_DollarByFundCode"),
                                          _sql_script1));

            _sql_script = "select jtpeopledge.PID, PLEDGE.PLEDGEDTE, PLEDGE.PLEDGEAMT, PLEDGE.SRCECODE  from jtpeopledge with (nolock) inner join PLEDGE with (nolock) on jtpeopledge.PLEDGEID = PLEDGE.PLEDGEID  where jtpeopledge.PID = " + gPID.ToString();
            _x_queries.Add(new XElement("SubQuery",
                                          new XAttribute("id", "iGetProfilePledge"),
                                          new XAttribute("reportname", "Profile_Pledge"),
                                          _sql_script));

            string _sql_script2 = String.Format("EXEC dbo.iGetProfileContactsByPID {0}", gPID.ToString());
            _x_queries.Add(new XElement("SubQuery",
                                          new XAttribute("id", "iGetProfileContactsByPID"),
                                          new XAttribute("reportname", "Profile_ContactsByPID"),
                                          _sql_script2));


            return _x_root.ToString();
        }

        private string get_report_data_sourceDef_in_xml_PAC_ext(string gPID)
        {
            XElement _x_root = new XElement("ReportParameters");
            XElement _x_parameters = new XElement("Parameters");
            XElement _x_queries = new XElement("Queries");

            String _ClientCode = "ALL";

            //custom configured Report
            String _ReportName = "Profile_PAC_ext";
            if (_userSession.configItems.Where(a => a.Key == crmConstants.profile_PAC).Count() > 0 && _userSession.configItems.Where(a => a.Key == crmConstants.profile_PAC).FirstOrDefault().Val != null)
            {
                _ReportName = _userSession.configItems.Where(a => a.Key == crmConstants.profile_PAC).FirstOrDefault().Val;
            }
            // Create a HeaderNode
            _x_root.Add(new XAttribute("clientcode", _ClientCode),
                        new XAttribute("reportcode", _ReportName));
            _x_root.Add(_x_queries);

            string _sql_script = "";
            _sql_script = "SELECT ACTIVITY.SUBJECT, ACTIVITY.ASKMEMO, ACTIVITY.ACTDATE, ACTIVITY.NOTE,P.PID, P.CHKDGT, K.DESCRIP AS PEOTYPE, C.DESCRIP AS PEOCODE, ISNULL(P.PREFIX,'') as PREFIX, ISNULL(P.FNAME,'') as FNAME, ISNULL(P.MNAME,'') as MNAME, ISNULL(P.LNAME,'') as LNAME, ISNULL(P.SUFFIX,'') as SUFFIX, ";
            _sql_script += " dbo.oFULLNAME(0, P.PREFIX, P.FNAME, P.MNAME, P.LNAME, P.SUFFIX) as NAME, P.OCCUPATION, P.EMPLOYER, P.SALUTATION, P.INFSALUT, P.SPOUSENAME, dbo.oPHONEwMASK2(H.PHNNO) AS HMPH,dbo.oPHONEwMASK2(B.PHNNO) AS BUSPH";
            _sql_script += " ,dbo.oPHONEwMASK2(F.PHNNO) AS FAXPH, E.PHNNO AS EMAIL, dbo.oPHONEwMASK2(M.PHNNO) AS CELLPH, dbo.oPHONEwMASK2(ASTP.PHNNO) AS ASTPHN, ASTE.PHNNO AS ASTEMAIL,  dbo.oPHONEwMASK2(DIRP.PHNNO) AS DIRPHN, A.ADDR1, A.ADDR2, A.STREET, ISNULL(A.CITY,'') as CITY, ISNULL(A.STATE,'') as STATE, ISNULL(A.ZIP,'') as ZIP, ISNULL(A.PLUS4,'') as PLUS4,";
            _sql_script += " dbo.oGetFLAGDESCSTR(P.PID) AS FLAGLIST,dbo.oGetKWRDSTR(P.PID) AS KWRDLIST,P.BIO, P.PICTURE, P.TITLE, P.ASSISTANT, P.TrackNo, A.CDCODE, lkCOUNTY.COUNTY, ssSystem.ClientName";
            _sql_script += " ,ISNULL(S.LGIFT,0) as LGIFT,S.LGIFTDTE,ISNULL(S.NOGIFTS,0) AS NOGIFTS,ISNULL(S.CTDAMT,0.00) as CTDAMT,ISNULL(S.CUMTOT,0.00) AS CUMTOT ";
            _sql_script += " ,ISNULL(S.YTDAMT,0.00) AS YTDAMT, dbo.cacheVal('appversion') AS appVersion, dbo.oGetFLAGDESCSTR(P.PID) as FLAGLIST, dbo.oGetKWRDSTR(P.PID) as KWRDLIST ";
            _sql_script += " FROM PEOPLE P with (nolock) left outer join ADDRESS A with (nolock) on P.PID = A.PID AND A.PRIME=1";
            _sql_script += " left outer join lkCOUNTY with (nolock) on lkCOUNTY.STATE = A.STATE AND lkCOUNTY.FIPS = A.COUNTY";
            _sql_script += " left outer join lkPEOTYPE K with (nolock) on P.PEOTYPEID = K.PEOTYPEID";
            _sql_script += " left outer join lkPEOCODE C with (nolock) on P.PEOCODEID = C.PEOCODEID";
            _sql_script += " left outer join PHONE H with (nolock) on P.PID = H.PID AND H.PHNTYPEID=1 AND H.PRIME=1";
            _sql_script += " left outer join PHONE B with (nolock) on P.PID = B.PID AND B.PHNTYPEID=2 AND B.PRIME=1";
            _sql_script += " left outer join PHONE F with (nolock) on P.PID = F.PID AND F.PHNTYPEID=5 AND F.PRIME=1";
            _sql_script += " left outer join PHONE E with (nolock) on P.PID = E.PID AND E.PHNTYPEID=4 AND E.PRIME=1";
            _sql_script += " left outer join PHONE M with (nolock) on P.PID = M.PID AND M.PHNTYPEID=3 AND M.PRIME=1";
            _sql_script += " LEFT OUTER JOIN PHONE ASTP with (nolock) ON P.PID=ASTP.PID AND ASTP.PHNTYPEID=11 AND ASTP.PRIME=1";
            _sql_script += " LEFT OUTER JOIN PHONE ASTE with (nolock) ON P.PID=ASTE.PID AND ASTE.PHNTYPEID=12 AND ASTE.PRIME=1";
            _sql_script += " LEFT OUTER JOIN PHONE DIRP with (nolock) ON P.PID=DIRP.PID AND DIRP.PHNTYPEID=13 AND DIRP.PRIME=1";
            _sql_script += " left outer join SUMMARYD S with (nolock) on P.PID = S.PID";
            //_sql_script += " left outer join (select top 1 ACTIVITY.PID, ACTIVITY.SUBJECT, ACTIVITY.ASKMEMO, ACTIVITY.ACTDATE, ACTIVITY.NOTE from ACTIVITY with (nolock) inner join lkacttype with (nolock) on ACTIVITY.ACTTYPEID = lkacttype.ACTTYPEID where ACTIVITY.DONE = 0 and lkacttype.ACTTYPE = 'CL' and ACTIVITY.PID = " + gPID.ToString() + " order by ACTDATE desc) ACTIVITY on P.PID = ACTIVITY.PID";
            _sql_script += " left outer join (select top 1 ACTIVITY.PID, ACTIVITY.SUBJECT, ACTIVITY.ASKMEMO, ACTIVITY.ACTDATE, ACTIVITY.NOTE from ACTIVITY with (nolock) inner join lkacttype with (nolock) on ACTIVITY.ACTTYPEID = lkacttype.ACTTYPEID where ACTIVITY.DONE = 0 and ACTIVITY.PID = " + gPID.ToString() + " order by ACTDATE desc) ACTIVITY on P.PID = ACTIVITY.PID";
            _sql_script += " left outer join (select top 1 ClientName from ssSystem order by DateImplem) ssSystem on 1=1";
            _sql_script += " WHERE P.PID = " + gPID.ToString();
            _x_queries.Add(new XElement("Query",
                                            new XAttribute("id", "iGetProfile"),
                                            _sql_script));

            String tempTblPIDs = "SELECT PID INTO #TEMP_PIDS FROM People WHERE PID=" + gPID.ToString();
            buildSCRIPT1();
            _sql_script1 = _sql_script1.Replace("%%#TEMP_PIDS%%", tempTblPIDs);
            _x_queries.Add(new XElement("SubQuery",
                                          new XAttribute("id", "iGetDollarByFundCode"),
                                          new XAttribute("reportname", "Profile_DollarByFundCode"),
                                          _sql_script1));

            _sql_script = "select jtpeopledge.PID, PLEDGE.PLEDGEDTE, PLEDGE.PLEDGEAMT, PLEDGE.SRCECODE  from jtpeopledge with (nolock) inner join PLEDGE with (nolock) on jtpeopledge.PLEDGEID = PLEDGE.PLEDGEID  where jtpeopledge.PID = " + gPID.ToString();
            _x_queries.Add(new XElement("SubQuery",
                                          new XAttribute("id", "iGetProfilePledge"),
                                          new XAttribute("reportname", "Profile_Pledge"),
                                          _sql_script));
            _sql_script = string.Format("SELECT * FROM v_people_mony WHERE PID = {0} order by BATCHDTE desc", gPID.ToString());
            _x_queries.Add(new XElement("SubQuery",
                                          new XAttribute("id", "iGetProfileMoneyList"),
                                          new XAttribute("reportname", "Profile_MoneyList"),
                                          _sql_script));
            string _sql_script2 = String.Format("EXEC dbo.iGetProfileContactsByPID {0}", gPID.ToString());
            _x_queries.Add(new XElement("SubQuery",
                                          new XAttribute("id", "iGetProfileContactsByPID"),
                                          new XAttribute("reportname", "Profile_ContactsByPID"),
                                          _sql_script2));


            return _x_root.ToString();
        }

        private string get_report_data_sourceDef_in_xml_Pocket(string gPID)
        {
            XElement _x_root = new XElement("ReportParameters");
            XElement _x_parameters = new XElement("Parameters");
            XElement _x_queries = new XElement("Queries");

            String _ClientCode = "ALL";

            //custom configured Report
            String _ReportName = "Profile_pocket";                          // as per Rakesh ... 9 Aug 2011 3:59pm
            if (_userSession.configItems.Where(a => a.Key == crmConstants.profile_pocket).Count() > 0 && _userSession.configItems.Where(a => a.Key == crmConstants.profile_pocket).FirstOrDefault().Val != null)
            {
                _ReportName = _userSession.configItems.Where(a => a.Key == crmConstants.profile_pocket).FirstOrDefault().Val;
            }

            // Create a HeaderNode
            _x_root.Add(new XAttribute("clientcode", _ClientCode),
                        new XAttribute("reportcode", _ReportName));
            _x_root.Add(_x_queries);

            string _sql_script = "";
            _sql_script = "SELECT ACTIVITY.SUBJECT, ACTIVITY.ASKMEMO, ACTIVITY.ACTDATE, ACTIVITY.NOTE,P.PID, P.CHKDGT, K.DESCRIP AS PEOTYPE, C.DESCRIP AS PEOCODE, ISNULL(P.PREFIX,'') as PREFIX, ISNULL(P.FNAME,'') as FNAME, ISNULL(P.MNAME,'') as MNAME, ISNULL(P.LNAME,'') as LNAME, ISNULL(P.SUFFIX,'') as SUFFIX,";
            _sql_script += " dbo.oFULLNAME(0, P.PREFIX, P.FNAME, P.MNAME, P.LNAME, P.SUFFIX) as NAME, P.OCCUPATION, P.EMPLOYER, P.SALUTATION, P.INFSALUT, P.SPOUSENAME, dbo.oPHONEwMASK2(H.PHNNO) AS HMPH,dbo.oPHONEwMASK2(B.PHNNO) AS BUSPH";
            _sql_script += " ,dbo.oPHONEwMASK2(F.PHNNO) AS FAXPH, E.PHNNO AS EMAIL, dbo.oPHONEwMASK2(M.PHNNO) AS CELLPH, dbo.oPHONEwMASK2(ASTP.PHNNO) AS ASTPHN, ASTE.PHNNO AS ASTEMAIL,  dbo.oPHONEwMASK2(DIRP.PHNNO) AS DIRPHN, A.ADDR1, A.ADDR2, A.STREET, ISNULL(A.CITY,'') as CITY, ISNULL(A.STATE,'') as STATE, ISNULL(A.ZIP,'') as ZIP, ISNULL(A.PLUS4,'') as PLUS4,";
            _sql_script += " dbo.oGetFLAGDESCSTR(P.PID) AS FLAGLIST,dbo.oGetKWRDSTR(P.PID) AS KWRDLIST,P.BIO, P.PICTURE, P.TITLE, P.ASSISTANT, P.TrackNo, A.CDCODE, lkCOUNTY.COUNTY, ssSystem.ClientName,isnull(SUMMARYD.CTDAMT,0) as CTDAMT, dbo.cacheVal('appversion') AS appVersion ";
            _sql_script += " FROM PEOPLE P with (nolock) left outer join ADDRESS A with (nolock) on P.PID = A.PID AND A.PRIME=1";
            _sql_script += " left outer join lkCOUNTY with (nolock) on lkCOUNTY.STATE = A.STATE AND lkCOUNTY.FIPS = A.COUNTY";
            _sql_script += " left outer join lkPEOTYPE K with (nolock) on P.PEOTYPEID = K.PEOTYPEID";
            _sql_script += " left outer join lkPEOCODE C with (nolock) on P.PEOCODEID = C.PEOCODEID";
            _sql_script += " left outer join PHONE H with (nolock) on P.PID = H.PID AND H.PHNTYPEID=1 AND H.PRIME=1";
            _sql_script += " left outer join PHONE B with (nolock) on P.PID = B.PID AND B.PHNTYPEID=2 AND B.PRIME=1";
            _sql_script += " left outer join PHONE F with (nolock) on P.PID = F.PID AND F.PHNTYPEID=5 AND F.PRIME=1";
            _sql_script += " left outer join PHONE E with (nolock) on P.PID = E.PID AND E.PHNTYPEID=4 AND E.PRIME=1";
            _sql_script += " left outer join PHONE M with (nolock) on P.PID = M.PID AND M.PHNTYPEID=3 AND M.PRIME=1";
            _sql_script += " LEFT OUTER JOIN PHONE ASTP with (nolock) ON P.PID=ASTP.PID AND ASTP.PHNTYPEID=11 AND ASTP.PRIME=1";
            _sql_script += " LEFT OUTER JOIN PHONE ASTE with (nolock) ON P.PID=ASTE.PID AND ASTE.PHNTYPEID=12 AND ASTE.PRIME=1";
            _sql_script += " LEFT OUTER JOIN PHONE DIRP with (nolock) ON P.PID=DIRP.PID AND DIRP.PHNTYPEID=13 AND DIRP.PRIME=1";
            _sql_script += " left outer join SUMMARYD with (nolock) on P.PID = SUMMARYD.PID";
            //_sql_script += " left outer join (select top 1 ACTIVITY.PID, ACTIVITY.SUBJECT, ACTIVITY.ASKMEMO, ACTIVITY.ACTDATE, ACTIVITY.NOTE from ACTIVITY with (nolock) inner join lkacttype with (nolock) on ACTIVITY.ACTTYPEID = lkacttype.ACTTYPEID where ACTIVITY.DONE = 0 and lkacttype.ACTTYPE = 'CL' and ACTIVITY.PID = " + gPID.ToString() + " order by ACTDATE desc) ACTIVITY on P.PID = ACTIVITY.PID";
            _sql_script += " left outer join (select top 1 ACTIVITY.PID, ACTIVITY.SUBJECT, ACTIVITY.ASKMEMO, ACTIVITY.ACTDATE, ACTIVITY.NOTE from ACTIVITY with (nolock) inner join lkacttype with (nolock) on ACTIVITY.ACTTYPEID = lkacttype.ACTTYPEID where ACTIVITY.DONE = 0 and ACTIVITY.PID = " + gPID.ToString() + " order by ACTDATE desc) ACTIVITY on P.PID = ACTIVITY.PID";
            _sql_script += " left outer join (select top 1 ClientName from ssSystem order by DateImplem) ssSystem on 1=1";
            _sql_script += " WHERE P.PID = " + gPID.ToString();
            _x_queries.Add(new XElement("Query",
                                          new XAttribute("id", "iGetProfile"),
                                          _sql_script));

            return _x_root.ToString();
        }

        


        #endregion

        #region [[ Print Call sheet Reports + Email Full Profile ]]

        public string  PrepareSessionsForReport(string pVersion, string pType, string gPID)
        {
            string p_key = System.Guid.NewGuid().ToString();
                        
            #region [[ Create XML first ]]
                    string _xml = "";
                    if (pVersion == "Profile_full")
                        _xml = get_report_data_sourceDef_in_xml_Full(gPID);
                    else if (pVersion == "Profile_brief")
                        _xml = get_report_data_sourceDef_in_xml_Brief(gPID);
                    else if (pVersion == "Profile_pocket")
                        _xml = get_report_data_sourceDef_in_xml_Pocket(gPID);
                    else if (pVersion == "Profile_PAC")
                        _xml = get_report_data_sourceDef_in_xml_PAC(gPID);
                    else if (pVersion == "Profile_extended")
                        _xml = get_report_data_sourceDef_in_xml_Extended(gPID);
                    else if (pVersion == "Profile_PAC_ext")
                        _xml = get_report_data_sourceDef_in_xml_PAC_ext(gPID);

            #endregion

            #region [[ For Collection ]]
            //Create collection
            System.Xml.XmlDocument doc = new System.Xml.XmlDocument();
                    doc.LoadXml(_xml);
                    _node = doc.DocumentElement.SelectSingleNode("/ReportParameters");

                    System.Collections.Specialized.NameValueCollection _collection = new System.Collections.Specialized.NameValueCollection();
                    _collection.Add("cc", _node.Attributes["clientcode"].Value);
                    _collection.Add("rn", _node.Attributes["reportcode"].Value);
                    _collection.Add("XML", _node.OuterXml);
                    //Save the Report Parameter collection in the Session
                    System.Web.HttpContext.Current.Session.Add(p_key + "_c", _collection);
                    #endregion

            #region [[ For Queries DataSet - First Dataset ]]
                    //Get Queries Dataset
                    xlist = _node.SelectNodes("Queries/Query"); 

                    foreach (System.Xml.XmlNode node in xlist)
                    {
                       ids.Add(node.Attributes["id"].Value);
                       sqls.Add(node.InnerText);
                       zeros.Add(0);
                    }
                    dSet = _reportDataService.get_dataset_w_sql__multi(sqls, ids, zeros, zeros);
                    //For Resizing the Picture...
                    dSet = _supportMethods.ReSizePictureinDataSet(dSet);
                    //First Dataset
                    System.Web.HttpContext.Current.Session.Add(p_key + "_fd", dSet);
                    #endregion
                    
            #region [[ For SubQueries Dataset ]]
                    //First set all for null and then Get SubQueries DataSet
                    xlist = null;
                    dSet = null;
                    xlist = _node.SelectNodes("Queries/SubQuery");
                    ids.Clear();
                    sqls.Clear();
                    zeros.Clear();
                    foreach (System.Xml.XmlNode node in xlist)
                    {
                       ids.Add(node.Attributes["id"].Value);
                       sqls.Add(node.InnerText);
                       zeros.Add(0);
                    }
                    dSet = _reportDataService.get_dataset_w_sql__multi(sqls, ids, zeros, zeros);
                    //Second Dataset
                    System.Web.HttpContext.Current.Session.Add(p_key + "_sd", dSet);
                    #endregion

            //Also Add Report Name in the Session
            System.Web.HttpContext.Current.Session.Add(p_key + "_rName", "Profile"); 
            //Return the GUID
            return p_key;

        }

        [aElementAuthorize(AccessElement = "People/Print Profile", AccessLevel = "v")]
        [NotLaunchpadAuthorize]
        public ActionResult Run_Server_Side_Report(string pVersion, string pType, string gPID)
        {
            try
            {
                if (!string.IsNullOrEmpty(pVersion) && !string.IsNullOrEmpty(gPID) && pType == "Print")
                {
                    string p_key = PrepareSessionsForReport(pVersion, pType, gPID);
                    //Let us go to the Report Page
                    return Redirect("../../Report/ReportPage.aspx?p_key=" + p_key);
                }
                else
                {
                    return Json(new { success = false, JsonRequestBehavior.AllowGet });
                }
            }
            catch(Exception ex)
            {
                return Json(new { success = false, JsonRequestBehavior.AllowGet });
            }
        }
        
        [HttpPost]
        [aElementAuthorize(AccessElement = "People/Email Profile", AccessLevel = "v")]
        public ActionResult EmailProfile(ProfileEmail _profileEmail)
        {
            try
            {
                cl_email _email = new cl_email();
                //Now create the xml string from the responsed we received from user
                _email.FROM = _profileEmail.USREMAIL;
                _email.TO = _profileEmail.MAILTO;
                _email.CC = _profileEmail.MAILCC;
                _email.SUBJECT = _profileEmail.SUBJECT;
                _email.BODY = _profileEmail.MESSAGE;
                _email.REPORT_NAME = _profileEmail.gPID + "-" + _profileEmail.FULLNAME;
                _email.REPORT = "Yes";
                //Create string from the XML
                string _xml = _email.ToString();
                string fr = SendEmail(_xml, _profileEmail.gPID.ToString());
                if (fr == "Sent")
                {
                    return Json(new { success = true });
                }
                else
                {
                    return Json(new { success = false, message = "There is some problem creating report to send via email. Please try again later." });
                    
                }
                
            }
            catch
            {
                return Json(new { success = false, message = "There is some problem creating report to send via email. Please try again later." });
            }
            
        }

        public string SendEmail(string email_xml, string gPID)
        {
            string xml_concanated = email_xml;
            try
            {
                // read XML
                System.Xml.XmlDocument oDoc = new System.Xml.XmlDocument();
                oDoc.LoadXml(xml_concanated);
                System.Xml.XmlNode oNode = oDoc.SelectSingleNode("/EMAIL");

                System.Net.Mail.MailMessage oMsg = new System.Net.Mail.MailMessage();

                //From
                oMsg.From = oNode.SelectSingleNode("FROM") != null ? new System.Net.Mail.MailAddress(oNode.SelectSingleNode("FROM").InnerText) : null;

                //To
                if (oNode.SelectSingleNode("TO") != null && oNode.SelectSingleNode("TO").InnerText != string.Empty)
                {
                    string[] toList = oNode.SelectSingleNode("TO").InnerText.Split(new char[] { ',', ';' });
                    foreach (string str in toList)
                    {
                        oMsg.To.Add(str);
                    }
                }
                //CC
                if (oNode.SelectSingleNode("CC") != null && oNode.SelectSingleNode("CC").InnerText != string.Empty)
                {
                    string[] ccList = oNode.SelectSingleNode("CC").InnerText.Split(new char[] { ',', ';' });
                    foreach (string str in ccList)
                    {
                        oMsg.CC.Add(str);
                    }
                }
                //BCC
                if (oNode.SelectSingleNode("BCC") != null && oNode.SelectSingleNode("BCC").InnerText != string.Empty)
                {
                    string[] bccList = oNode.SelectSingleNode("BCC").InnerText.Split(new char[] { ',', ';' });
                    foreach (string str in bccList)
                    {
                        oMsg.Bcc.Add(str);
                    }
                }
                //Subject
                oMsg.Subject = oNode.SelectSingleNode("SUBJECT") != null ? oNode.SelectSingleNode("SUBJECT").InnerText : string.Empty;
                //Attachments
                if (oNode.SelectSingleNode("ATTACHMENTS") != null)
                {
                    //string attachment_path = HttpContext.Current.Server.MapPath("~");
                    string attachment_path = System.Web.Hosting.HostingEnvironment.MapPath("~");
                    attachment_path = (attachment_path.Substring(attachment_path.Length - 1, 1) == "\\" ? attachment_path : attachment_path + "\\") + "tmp\\";
                    foreach (System.Xml.XmlNode node in oNode.SelectSingleNode("ATTACHMENTS").SelectNodes("ATTACHMENT"))
                    {
                        oMsg.Attachments.Add(new System.Net.Mail.Attachment(attachment_path + node.InnerText));
                    }
                }

                // body
                if (oNode.SelectSingleNode("BODY") != null && oNode.SelectSingleNode("BODY").InnerText != string.Empty)
                {
                    //oMsg.Body = HttpContext.Current.Server.HtmlEncode(oNode.SelectSingleNode("BODY").InnerText).Replace("\r\n", "<br/>").Replace(" ", "&nbsp;");
                    oMsg.Body = HttpUtility.HtmlEncode(oNode.SelectSingleNode("BODY").InnerText).Replace("\r\n", "<br/>").Replace(" ", "&nbsp;");
                }
                //add report into body
                if (oNode.SelectSingleNode("REPORT") != null && oNode.SelectSingleNode("REPORT").InnerText != string.Empty)
                {
                    string p_key = PrepareSessionsForReport("Profile_full", "Email", gPID);

                    System.Collections.Specialized.NameValueCollection collection = new System.Collections.Specialized.NameValueCollection();
                    collection = (System.Collections.Specialized.NameValueCollection)Session[p_key + "_c"];

                    //Create report and get the file
                    createReportFile _createRptFile = new createReportFile();
                    string reportFile = _createRptFile.GetFile(collection, oNode.SelectSingleNode("REPORT").Attributes["filename"].Value.Replace(".", string.Empty), "EmailOther", (DataSet)Session[p_key + "_fd"], (DataSet)Session[p_key + "_sd"]);
                                        
                    //attach HTML report
                    if (System.IO.File.Exists(reportFile))
                    {
                        oMsg.Attachments.Add(new System.Net.Mail.Attachment(reportFile));
                    }

                    //attach PDF report
                    if (System.IO.File.Exists(reportFile + ".pdf"))
                    {
                        oMsg.Attachments.Add(new System.Net.Mail.Attachment(reportFile + ".pdf"));
                    }
                }

                oMsg.IsBodyHtml = true;
                System.Net.Mail.SmtpClient smtp = new System.Net.Mail.SmtpClient();// ("CMDI60");
                smtp.Timeout = 30000000;

                try
                {
                    smtp.Send(oMsg);
                }
                catch (Exception) { }
                return "Sent";
            }
            catch (Exception ex)
            {
                return ex.Message;
                //return "<error msg=\"error occurred during sending email\"/>";
            }
        }

        #endregion
        
        #region [[ Reports : My, Group, Custom, Income, Event, Bundler, Treasury, Compliance, DataMaintenance, Dashboards, All ]]

        [sysAdminAuthorizeAttribute]
        public ActionResult ManageGroup(string reportId)
        {
            pageInfo _pageInfo = new pageInfo();
            _pageInfo.pageData1 = reportId;

            //Icon or List View
            return View(@"ManageGroup", _pageInfo);
        }
        
        //public ActionResult Reports(string viewtype, string udlo, string reportType, string scope)
        public ActionResult Reports(string viewtype, string scope)
        {
            pageInfo _pageInfo = new pageInfo();
            //if (udlo == "y")
            //    _pageInfo.LayoutPage = crmConstants.DEFAULT_LAYOUT_BLANK;
            //else
                _pageInfo.LayoutPage = "";

            //_pageInfo.pageData1 = reportType;
            _pageInfo.pageData2 = scope;
            //Icon or List View
            return View((viewtype == "I") ? @"_ReportsI" : @"_ReportsL", _pageInfo);
        }

        public ActionResult MyReports(string udlo)
        {
            pageInfo _pageInfo = new pageInfo();
            if (udlo == "y")
                _pageInfo.LayoutPage = crmConstants.DEFAULT_LAYOUT_BLANK;
            else
                _pageInfo.LayoutPage = "";

            _pageInfo.pageData1 = "My Reports";
            _pageInfo.pageData2 = "user";
            return View(@"_Reports", _pageInfo);
        }

        public ActionResult GroupReports(string udlo)
        {
            pageInfo _pageInfo = new pageInfo();
            if (udlo == "y")
                _pageInfo.LayoutPage = crmConstants.DEFAULT_LAYOUT_BLANK;
            else
                _pageInfo.LayoutPage = "";

            _pageInfo.pageData1 = "Group Reports";
            _pageInfo.pageData2 = "userGroup";
            return View(@"_Reports", _pageInfo);
        }

        public ActionResult CustomReports(string udlo)
        {
            pageInfo _pageInfo = new pageInfo();
            if (udlo == "y")
                _pageInfo.LayoutPage = crmConstants.DEFAULT_LAYOUT_BLANK;
            else
                _pageInfo.LayoutPage = "";

            _pageInfo.pageData1 = "Custom Reports";
            _pageInfo.pageData2 = "project";
            return View(@"_Reports", _pageInfo);
        }

        public ActionResult IncomeReports(string udlo)
        {
            pageInfo _pageInfo = new pageInfo();
            if (udlo == "y")
                _pageInfo.LayoutPage = crmConstants.DEFAULT_LAYOUT_BLANK;
            else
                _pageInfo.LayoutPage = "";

            _pageInfo.pageData1 = "Income Reports";
            _pageInfo.pageData2 = "IncomeSection";
            return View(@"_Reports", _pageInfo);
        }

        public ActionResult EventReports(string udlo)
        {
            pageInfo _pageInfo = new pageInfo();
            if (udlo == "y")
                _pageInfo.LayoutPage = crmConstants.DEFAULT_LAYOUT_BLANK;
            else
                _pageInfo.LayoutPage = "";

            _pageInfo.pageData1 = "Event Reports";
            _pageInfo.pageData2 = "EventSection";
            return View(@"_Reports", _pageInfo);
        }

        public ActionResult BundlerReports(string udlo)
        {
            pageInfo _pageInfo = new pageInfo();
            if (udlo == "y")
                _pageInfo.LayoutPage = crmConstants.DEFAULT_LAYOUT_BLANK;
            else
                _pageInfo.LayoutPage = "";

            _pageInfo.pageData1 = "Bundler Reports";
            _pageInfo.pageData2 = "BundlerSection";
            return View(@"_Reports", _pageInfo);
        }

        public ActionResult GiftOfficerReports(string udlo)
        {
            pageInfo _pageInfo = new pageInfo();
            if (udlo == "y")
                _pageInfo.LayoutPage = crmConstants.DEFAULT_LAYOUT_BLANK;
            else
                _pageInfo.LayoutPage = "";

            _pageInfo.pageData1 = "Gift Officer Reports";
            _pageInfo.pageData2 = "GiftOfficerSection";
            return View(@"_Reports", _pageInfo);
        }

        public ActionResult TreasuryReports(string udlo)
        {
            pageInfo _pageInfo = new pageInfo();
            if (udlo == "y")
                _pageInfo.LayoutPage = crmConstants.DEFAULT_LAYOUT_BLANK;
            else
                _pageInfo.LayoutPage = "";

            _pageInfo.pageData1 = "Treasury Reports";
            _pageInfo.pageData2 = "TreasurySection";
            return View(@"_Reports", _pageInfo);
        }

        public ActionResult ComplianceReports(string udlo)
        {
            pageInfo _pageInfo = new pageInfo();
            if (udlo == "y")
                _pageInfo.LayoutPage = crmConstants.DEFAULT_LAYOUT_BLANK;
            else
                _pageInfo.LayoutPage = "";

            _pageInfo.pageData1 = "Compliance Reports";
            _pageInfo.pageData2 = "ComplianceSection";
            return View(@"_Reports", _pageInfo);
        }

        public ActionResult DataMaintenanceReports(string udlo)
        {
            pageInfo _pageInfo = new pageInfo();
            if (udlo == "y")
                _pageInfo.LayoutPage = crmConstants.DEFAULT_LAYOUT_BLANK;
            else
                _pageInfo.LayoutPage = "";

            _pageInfo.pageData1 = "Data Maintenance Reports";
            _pageInfo.pageData2 = "DataMaintSection";
            return View(@"_Reports", _pageInfo);
        }

        public ActionResult DashboardsReports(string udlo)
        {
            pageInfo _pageInfo = new pageInfo();
            if (udlo == "y")
                _pageInfo.LayoutPage = crmConstants.DEFAULT_LAYOUT_BLANK;
            else
                _pageInfo.LayoutPage = "";

            _pageInfo.pageData1 = "Dashboards Reports";
            _pageInfo.pageData2 = "DashboardSection";
            return View(@"_Reports", _pageInfo);
        }

        public ActionResult AllReports(string udlo)
        {
            pageInfo _pageInfo = new pageInfo();
            if (udlo == "y")
                _pageInfo.LayoutPage = crmConstants.DEFAULT_LAYOUT_BLANK;
            else
                _pageInfo.LayoutPage = "";

            _pageInfo.pageData1 = "All Reports";
            _pageInfo.pageData2 = "All";
            return View(@"_Reports", _pageInfo);
        }
        #endregion

        #region [[ We need to Download Print Money Details - Receipt ]]

        private string get_report_data_sourceDef_in_xml_MoneyReciept(string gMID)
        {
            XElement _x_root = new XElement("ReportParameters");
            XElement _x_parameters = new XElement("Parameters");
            XElement _x_queries = new XElement("Queries");

            String _ClientCode = "ALL";

            //Report Name..
            String _ReportName = "MoneyReciept";

            // Create a HeaderNode
            _x_root.Add(new XAttribute("clientcode", _ClientCode),
                        new XAttribute("reportcode", _ReportName));
            _x_root.Add(_x_queries);

            string _sql_script = String.Format("EXEC dbo.iGetMoneyReceiptByMID {0}", gMID.ToString());
            _x_queries.Add(new XElement("Query",
                                            new XAttribute("id", "DataSet1"),
                                            _sql_script));

            return _x_root.ToString();
        }


        [aElementAuthorize(AccessElement = "cmdiapp.dms.People_Money", AccessLevel = "v")]
        public ActionResult DownloadReceipt(int Id)
        {
            try
            {
                
                #region [[ Create XML and FileName based on MID ]]
                string dwnloadFileName = "MoneyReceipt-" + Id.ToString() + ".pdf";
                string _xml = get_report_data_sourceDef_in_xml_MoneyReciept(Id.ToString());
                #endregion

                #region [[ For Collection ]]
                //Create collection
                System.Xml.XmlDocument doc = new System.Xml.XmlDocument();
                doc.LoadXml(_xml);
                _node = doc.DocumentElement.SelectSingleNode("/ReportParameters");

                System.Collections.Specialized.NameValueCollection _collection = new System.Collections.Specialized.NameValueCollection();
                _collection.Add("cc", _node.Attributes["clientcode"].Value);
                _collection.Add("rn", _node.Attributes["reportcode"].Value);
                _collection.Add("XML", _node.OuterXml);
                #endregion

                #region [[ For Queries DataSet - First Dataset ]]
                //Get Queries Dataset
                xlist = _node.SelectNodes("Queries/Query"); 

                foreach (System.Xml.XmlNode node in xlist)
                {
                    ids.Add(node.Attributes["id"].Value);
                    sqls.Add(node.InnerText);
                    zeros.Add(0);
                }
                dSet = _reportDataService.get_dataset_w_sql__multi(sqls, ids, zeros, zeros);
                #endregion

                #region [[ Create Receipt pdf filestream ]]
                //Create report and get the filestream so that we can flush...
                createReportFile _createRptFile = new createReportFile();
                byte[] reportFileData = _createRptFile.GetFileStream(_collection, "MoneyReceipt", "MoneyReceipt", dSet, null);
                #endregion

                #region [[ Now flush the file to the Browser ]]
                //Add header and Flush the file 
                Response.AppendHeader("Content-Disposition", "attachment; filename=" + dwnloadFileName);
                #endregion

                return File(reportFileData, dwnloadFileName);
            }
            catch (FileNotFoundException)
            {
                throw new HttpException(404, "The Receipt for this Donation can not be processed at this time. Please try again later.");
            }
            catch (Exception ex)
            {
                throw new HttpException();
            }

        }

        #endregion



    }
}
