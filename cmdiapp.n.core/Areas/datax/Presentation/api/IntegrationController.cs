﻿using cmdiapp.n.core.Areas.datax.Domain.Models;
using cmdiapp.n.core.Areas.datax.Domain.Models.ViewModels;
using cmdiapp.n.core.Areas.datax.Domain.Services;
using cmdiapp.n.core.Core.App_Start;
using cmdiapp.n.core.Presentation.Controllers;
using Ninject;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Threading.Tasks;
using System.Web.Http;

namespace cmdiapp.n.core.Areas.datax.Presentation.api
{
    [RoutePrefix("datax/api/Integration")]
    public class IntegrationController : ApiController
    {
        private IIntegrationService _service;

        public class ccProcessParam
        {
            public int fundid { get; set; } 
            public string batchno { get; set; }
            public DateTime batchdte { get; set; }
        }

        public class ccProcessOneParam
        {
            public int fundid { get; set; }
            public string batchno { get; set; }
            public DateTime batchdte { get; set; }
            public int pid { get; set; }
            public string refno { get; set; }
            public string srcecode { get; set; }
            public decimal amt { get; set; }
            public string chapcode { get; set; }
            public string campgncode { get; set; }
            public int? trackno { get; set; }
            public string comment { get; set; }
        }

        public IntegrationController()
        {
            _service = NinjectMVC.kernel.Get<IIntegrationService>();
        }

        [HttpPost, Route("Accounts/{callerId}")]
        [apiAuthorize]
        public Task<List<CallerAccountDisplay>> CallerAccounts(CallerCredentials credentials, int callerId)
        {
            return _service.GetCallerAccountsAsync(credentials, callerId);
        }

        [HttpPost, Route("AnedotccProcessUrl")]
        [apiAuthorize]
        public Task<string> AnedotccProcessUrl(ccProcessParam _param)
        {
            return _service.AnedotccProcessUrl(_param.fundid, _param.batchno, _param.batchdte);
        }

        [HttpPost, Route("AnedotccProcessOneUrl")]
        [apiAuthorize]
        public Task<string> AnedotccProcessOneUrl(ccProcessOneParam _param)
        {
            return _service.AnedotccProcessOneUrl(_param.fundid, _param.batchno, _param.batchdte, _param.pid, _param.refno, _param.srcecode, _param.amt,
                                                    _param.chapcode, _param.campgncode, _param.trackno, _param.comment);
        }

        [HttpGet, Route("setupAnedot/{fundid}")]
        [apiAuthorize]
        public bool setupAnedot(int fundid)
        {
            return _service.setupAnedot(fundid);
        }
    }
}
