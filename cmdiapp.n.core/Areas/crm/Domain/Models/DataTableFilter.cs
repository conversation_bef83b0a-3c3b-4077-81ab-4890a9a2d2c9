﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace cmdiapp.n.core.Areas.crm.Domain.Models
{
    public class DataTableFilter
    {
        public string label { get; set; }
        public DataTableFilterOption[] options { get; set; }
        public string paramName { get; set; }
        public bool? multiValue { get; set; }
        public string type { get; set; }
        public dynamic value { get; set; }
    }

    public class DataTableFilterOption
    {
        public string label { get; set; }
        public string value { get; set; }
    }
}