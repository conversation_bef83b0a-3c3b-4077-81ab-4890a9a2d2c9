﻿using cmdiapp.n.core.Areas.legislator.Domain.Data;
using cmdiapp.n.core.Domain.ViewModels;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Web;

namespace cmdiapp.n.core.Areas.legislator.Domain.Models
{
    [Table("lkContactChangeType")]
    public class ContactChangeType : _entityBase_legislator, iItemType
    {
        [Key, Column]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        public int Id { get; set; }

        [Column, MaxLength(30)]
        public string type { get; set; }

        [Column, MaxLength(2)]
        public string code { get; set; }
    }
}