﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Data.Linq;
using System.Web;
using System.Data.Entity;
using System.Data.Entity.Infrastructure;
 
using cmdiapp.n.core.Areas.crm.Domain.Models;
using cmdiapp.n.core.Areas.datax.Domain.Models;

namespace cmdiapp.n.core.Areas.crm.Domain.Repositories
{
    public class _crmDBContext : DbContext
    {
        public _crmDBContext(string _appDBContext_connString_itself_or_name): base(_appDBContext_connString_itself_or_name)
        {
            // Get the ObjectContext and set the CommandTimeout
            var objectContext = (this as IObjectContextAdapter).ObjectContext;
            objectContext.CommandTimeout = 600;
           
            Database.SetInitializer<_crmDBContext>(new DBInitializer());
        }
        
        #region [[ PEOPLE ]]
        public DbSet<people> people { get; set; }
        public DbSet<PeopleF> v_people_rec { get; set; }
        public DbSet<PeopleR> v_people_1 { get; set; }
        public DbSet<Address> address { get; set; }
        public DbSet<Phone> phone { get; set; }
        #endregion 

        #region [[[ Lookup Data ]]]
        public DbSet<PeopleType> lkPEOTYPE { get; set; }
        public DbSet<PeopleCode> lkPEOCODE { get; set; }
        public DbSet<lkPEOCLASS> lkPEOCLASS { get; set; }
        public DbSet<AddressType> lkADDRTYPE { get; set; }
        public DbSet<County> lkCOUNTY { get; set; }
        public DbSet<v_lkEXCEP> v_lkEXCEP { get; set; }
        public DbSet<v_SOURCE> v_SOURCE { get; set; }
        public DbSet<PhoneType> lkPHNTYPE { get; set; }
        public DbSet<v_eventcode> v_eventcode { get; set; }
        public DbSet<lkEVNTSTATUS> lkEVNTSTATUS { get; set; }
        public DbSet<lkUPDTYPE> lkupdtype { get; set; }
        public DbSet<lkMONYTYPE> lkMONYTYPE { get; set; }
        public DbSet<lkADJTYPE> lkADJTYPE { get; set; }
        public DbSet<lkEXCEP> lkEXCEP { get; set; }
        public DbSet<lkMATCH> lkMATCH { get; set; }
        public DbSet<lkACKW> lkACKW { get; set; }
        public DbSet<v_Package> v_Package { get; set; }
        public DbSet<lkRELATETYPE> lkRELATETYPE { get; set; }
        public DbSet<jtRELATE> jtRELATE { get; set; }
        public DbSet<lkPeopleSTR1> lkPeopleSTR1 { get; set; }
        public DbSet<lkINVITEETYPE> lkINVITEETYPE { get; set; }
        public DbSet<lkCHAPCODE> lkCHAPCODE { get; set; }
        public DbSet<lkMONYCODE> lkMONYCODE { get; set; }
        public DbSet<lkGIFTTYPE> lkGIFTTYPE { get; set; }
        public DbSet<lkMEMTYPE> lkMEMTYPE { get; set; }
        public DbSet<lkBATTYPE> lkBATTYPE { get; set; }
        public DbSet<lkCOUNTRY> lkCOUNTRY { get; set; }
        public DbSet<lkFUNDTYPE> lkFUNDTYPE { get; set; }
        #endregion

        #region [[[ Fundraising ]]]
        //public DbSet<SummaryD> SummaryD { get; set; }
        //public DbSet<SummaryF> SummaryD { get; set; }
        public DbSet<MonyR> v_people_mony { get; set; }
        public DbSet<PledgeR> v_people_pledge { get; set; }
        public DbSet<PledgeView> PledgeView { get; set; }
        public DbSet<ClubR> v_people_club { get; set; }
        public DbSet<MonyStatement> iGenStatement { get; set; }
        public DbSet<SourceR> v_srce_pkge_prog { get; set; }
        public DbSet<TracknoR> v_trackno { get; set; }
        public DbSet<MONY> MONY { get; set; }
        public DbSet<MONYTRACK> MONYTRACK { get; set; }
        public DbSet<MONYADDI> MONYADDI { get; set; }
        public DbSet<FUNDRAISE> FUNDRAISE { get; set; }
        public DbSet<lkLOBBYIST> lkLOBBYIST { get; set; }
        public DbSet<lkFNDRGRP> lkFNDRGRP { get; set; }
        public DbSet<lkFUNDRAISETITLE> lkFUNDRAISETITLE { get; set; }
        public DbSet<jtFNDRGRP> jtFNDRGRP { get; set; }
        public DbSet<MONYMEM> MONYMEM { get; set; }
        public DbSet<dtMATCH> dtMATCH { get; set; }
        public DbSet<jtFUNDRAISE> jtFUNDRAISE { get; set; }

        #endregion

        #region [[ Pledge ]]
        public DbSet<PLEDGE> PLEDGE { get; set; }
        public DbSet<jtPEOPLEDGE> jtPEOPLEDGE { get; set; }
        public DbSet<jtMONYPLEDGE> jtMONYPLEDGE { get; set; }
        public DbSet<PLEDGETRACK> PLEDGETRACK { get; set; }
        public DbSet<jtPLEG> jtPLEG { get; set; }
        public DbSet<PEOPLEDGEDOC> PEOPLEDGEDOC { get; set; }
        #endregion

        #region [[ System ]]
        public DbSet<ssSystem> ssSystems { get; set; }
        public DbSet<ssUSER> ssUSER { get; set; }
        public DbSet<v_gateway_users> v_gateway_users { get; set; }
        public DbSet<ssSQLLOG> ssSQLLOG { get; set; }
        public DbSet<ssCONFIG> ssCONFIG { get; set; }
        public DbSet<dm_Cycle> dm_Cycle { get; set; }
        #endregion

        public DbSet<fileAttachment> fileAttachment { get; set; }
        public DbSet<dmTEMPLATE> dmTEMPLATE { get; set; }
        public DbSet<x_ms_org> x_ms_org { get; set; }
        public DbSet<x_ms_template> x_ms_template { get; set; }
        public DbSet<x_ms_job> x_ms_job { get; set; }
        public DbSet<dmFLAG> dmFLAG { get; set; }
        public DbSet<jtFLAG> jtFLAG { get; set; }
        //Added for Contact Flag
        public DbSet<lkCONTFLAG> lkCONTFLAG { get; set; }
        public DbSet<jtCONTFLAG> jtCONTFLAG { get; set; }
        //Added for Club
        public DbSet<pmCLUB> pmCLUB { get; set; }
        public DbSet<jtCLUB> jtCLUB { get; set; }
        public DbSet<lkCLUBSTAT> lkCLUBSTAT { get; set; }
        //Added for KWRD
        public DbSet<dmKWRD> dmKWRD { get; set; }
        public DbSet<jtKWRD> jtKWRD { get; set; }
        public DbSet<jtChannelSuppressionFlag> jtChannelSuppressionFlag { get; set; }

        //For dmFund
        public DbSet<dmFUND> dmFUND { get; set; }
        public DbSet<lkFUNDFORMTYPE> lkFUNDFORMTYPE { get; set; }
        //For LkChannel
        public DbSet<lkChannel> lkChannel { get; set; }
        //For Target Channels
        public DbSet<lkTargetChannel> lkTargetChannel { get; set; }
        //For webIMPPREF
        public DbSet<webIMPPREF> webIMPPREF { get; set; }
        //Added for Source
        public DbSet<SOURCE> source { get; set; }
        //Added for SRCEBUDGET
        public DbSet<SRCEBUDGET> srcebudget { get; set; }
        //Added for Program
        public DbSet<PROGRAM> program { get; set; }
        public DbSet<dtPROG> dtprogram { get; set; }
        public DbSet<PROGRAM_GROUP> PROGRAM_GROUP { get; set; }

        //Package
        public DbSet<PACKAGE> package { get; set; }
        public DbSet<lkInitiativeType> lkInitiativeType { get; set; }
        //For Batch
        public DbSet<BATCH> batch { get; set; }
        //For dtBatch
        public DbSet<dtBATCH> dtBATCH { get; set; }
        //For dtBatchAddi
        public DbSet<dtBATCHADDI> dtBATCHADDI { get; set; }
        //For dmCENTER
        public DbSet<dmCENTER> dmCENTER { get; set; }
        //For dmCAMPGN
        public DbSet<dmCAMPGN> dmCAMPGN { get; set; }

        public DbSet<Contact> contact { get; set; }
        public DbSet<v_people_task> v_people_task { get; set; }
        public DbSet<ACTHIST> acthist { get; set; }
        public DbSet<ACTHISTDOC> acthistdoc { get; set; }
        public DbSet<v_people_note> note { get; set; }

        public DbSet<lkACTTYPE> lkACTTYPE { get; set; }

        public DbSet<lkPURPOSE> lkPURPOSE { get; set; }

        public DbSet<lkPRIORITY> lkPRIORITY { get; set; }

        public DbSet<v_user> v_user { get; set; }

        public DbSet<ACTIVITY> ACTIVITY { get; set; }
        public DbSet<ACTIVITYDOC> ACTIVITYDOC { get; set; }
        public DbSet<EventF> EventF { get; set; }
        public DbSet<EventQuestion> EventQuestion { get; set; }
        public DbSet<EventDoc> EventDoc { get; set; }
        public DbSet<webGIFTLOG> webGIFTLOG { get; set; }
        public DbSet<SPCEVNTTICKET> SPCEVNTTICKET { get; set; }

        // For Outside Gift 
        public DbSet<OUTSIDEGIFT> OUTSIDEGIFT { get; set; }
        public DbSet<DECLINED> DECLINED { get; set; }

        //BudgetAlloc
        public DbSet<BudgetAlloc> BudgetAlloc { get; set; }

        //Personal contribution
        public DbSet<PersonalContrib> PersonalContrib { get; set; }
        public DbSet<lkPersonalContribType> lkPersonalContribType { get; set; }
        //lkPartyAffil
        public DbSet<lkPartyAffil> lkPartyAffil { get; set; }

        #region [[ Conduit ]]
        public DbSet<CONDUIT> CONDUIT { get; set; }
        public DbSet<MONYCONDUIT> MONYCONDUIT { get; set; }
        public DbSet<ConduitnoR> v_conduitno { get; set; }
        public DbSet<CONDUITDIST> CONDUITDIST { get; set; }
        public DbSet<dtCONDUITDIST> dtCONDUITDIST { get; set; }
        public DbSet<dtBATCHCONDUIT> dtBATCHCONDUIT { get; set; }
        #endregion

        #region [[ PEOPLE BROWSER-EVENT]]
        public DbSet<v_people_event> PeopleEvent { get; set; }
        public DbSet<v_event_payments> PeopleEventPayment { get; set; }
        public DbSet<dtSPCEVNTGUEST> PeopleEventGuest { get; set; }
        public DbSet<jtSPCEVNT> jtSPCEVNT { get; set; }
        public DbSet<dtSPCEVNT> dtSPCEVNT { get; set; }
        public DbSet<dtSPCEVNTMONY> dtSPCEVNTMONY { get; set; }
        public DbSet<dtSPCEVNTGUESTFLD> dtSPCEVNTGUESTFLD { get; set; }
        public DbSet<jtINVITEETICKET> jtINVITEETICKET { get; set; }
        public DbSet<SPCEVNTBRIEF> SPCEVNTBRIEF { get; set; }
        public DbSet<jtSPCEVNTPEOPLE> jtSPCEVNTPEOPLE { get; set; }
        public DbSet<SPCEVNTTIMELINE> SPCEVNTTIMELINE { get; set; }
        public DbSet<SPCEVNTBRIEFCONTENT> SPCEVNTBRIEFCONTENT { get; set; }
        #endregion

        #region [[ Political ]]
        public DbSet<Voter> v_x_vm_voter { get; set; }
        #endregion

        #region [[ Voter->Browser ]]
        public DbSet<VoterSearch> v_x_vm_VoterSearch { get; set; }
        #endregion

        #region [[ Expenditure ]]
        public DbSet<lkENTITYTYPE> lkENTITYTYPE { get; set; }
        public DbSet<Vendor> ENTITY { get; set; }
        public DbSet<v_entity_txn> v_entity_txn { get; set; }
        public DbSet<Txn> Txn { get; set; }
        public DbSet<TXNDOC> txndoc { get; set; } 
        public DbSet<lkTxnType> lkTxnType { get; set; }
        public DbSet<lkTxnCat> lkTxnCat { get; set; }
        public DbSet<lkTxnCode> lkTxnCode { get; set; }
        public DbSet<lkFECTxnType> lkFECTxnType { get; set; }
        public DbSet<lkChartAcct> lkChartAcct { get; set; }
        public DbSet<lkCHARTACCTTYPE> lkCHARTACCTTYPE { get; set; }
        public DbSet<lkFECDesc> lkFECDesc { get; set; }
        public DbSet<lkElectCD> lkElectCD { get; set; }
        public DbSet<AccTxfer> AccTxfer { get; set; }
        public DbSet<jtTxnAcct> jtTxnAcct { get; set; }
        public DbSet<lkExpMonyType> lkExpMonyType { get; set; }
        public DbSet<v_entity> v_entity { get; set; }
        public DbSet<v_receipts_TxnAcctChart> v_receipts_TxnAcctChart { get; set; }
        public DbSet<v_entity_fullname> v_entity_fullname { get; set; }
        public DbSet<v_ultimate_vendor> v_ultimate_vendor { get; set; }
        public DbSet<VENDHIST> VENDHIST { get; set; }
        public DbSet<lkTXNADJTYPE> lkTXNADJTYPE { get; set; }
        public DbSet<INVOICE> INVOICE { get; set; }
        public DbSet<INVOICEDOC> invoicedoc { get; set; }
        public DbSet<lkINVSTATUS> lkINVSTATUS { get; set; }
        public DbSet<jtINVACCT> jtINVACCT { get; set; }
        public DbSet<v_entity_invoice> v_entity_invoice { get; set; }
        public DbSet<INVOICEPAY> INVOICEPAY { get; set; }
        public DbSet<INVOICESEL> INVOICESEL { get; set; }
        public DbSet<INVOICECHK> INVOICECHK { get; set; }
        public DbSet<v_reconcile_txn> v_reconcile_txn { get; set; }
        public DbSet<jtTXNINV> jtTXNINV { get; set; }
        public DbSet<PAYREQ> PAYREQ { get; set; }
        public DbSet<lkPAYREQTYPE> lkPAYREQTYPE { get; set; }
        public DbSet<lkPAYREQSTATUS> lkPAYREQSTATUS { get; set; }
        public DbSet<PAYREQDOC> PAYREQDOC { get; set; }
        public DbSet<EntityContact> EntityContact { get; set; }
        public DbSet<jtENTITYCONTFLAG> EntityContactFlag { get; set; }
        #endregion

        #region [[ JFC ]]
        public DbSet<MONYALLOC> MONYALLOC { get; set; }
        public DbSet<jtJFCDISTRIB> jtJFCDISTRIB { get; set; }
        public DbSet<dtJFCDISTRIB> dtJFCDISTRIB { get; set; }
        public DbSet<v_JFC_batch> v_JFC_batch { get; set; }
        public DbSet<JFCDISTRIB> JFCDISTRIB { get; set; }
        public DbSet<lkDISTRIBSTATUS> lkDISTRIBSTATUS { get; set; }
        public DbSet<jtJFCDISTRIBMID> jtJFCDISTRIBMID { get; set; }
        public DbSet<lkJFCCMTE> lkJFCCMTE { get; set; }
        public DbSet<dmJFCCMTE> dmJFCCMTE { get; set; }
        #endregion

        #region [[ Import ]]
        public DbSet<lkIMPTYPE> lkIMPTYPE { get; set; }
        public DbSet<lkImpField> lkImpField { get; set; }
        #endregion

        #region [[ EventBrite ]]
        public DbSet<ebEvent> x_eb_event { get; set; }
        public DbSet<queue_eventAttendee> queue_eventAttendee { get; set; }
        #endregion

        #region [[ Check Images ]]
        public DbSet<MonyImage> MonyImage { get; set; }
        #endregion

        #region [[ Data Entry Prospect ]]
        public DbSet<zProspect> zProspect { get; set; }
        #endregion

        #region [[ Queue Job ]]
        public DbSet<JOB> JOB { get; set; }
        public DbSet<jtJOB> jtJOB { get; set; }
        #endregion

        #region [[ Move Management ]]
        public DbSet<MOVE> MOVE { get; set; }
        public DbSet<lkPIPELINE> lkPIPELINE { get; set; }
        public DbSet<lkMOVEPLAN> lkMOVEPLAN { get; set; }
        public DbSet<lkMOVERESULT> lkMOVERESULT { get; set; }
        public DbSet<v_people_move> v_people_move { get; set; }
        public DbSet<v_move_summary> v_move_summary { get; set; }
        public DbSet<MOVEDOC> MOVEDOC { get; set; }
        public DbSet<jtMOVEACTION> jtMOVEACTION { get; set; }
        public DbSet<jtMOVEEVNT> jtMOVEEVNT { get; set; }
        public DbSet<jtMOVEMONY> jtMOVEMONY { get; set; }
        public DbSet<jtMOVEPLEG> jtMOVEPLEG { get; set; }
        #endregion

        #region [[ Caging Scan ]]
        public DbSet<v_cagebatch_batch> v_cagebatch_batch { get; set; }
        public DbSet<v_cagebatch_image> v_cagebatch_image { get; set; }
        public DbSet<v_cagebatch_checkdocimage> v_cagebatch_checkdocimage { get; set; }
        #endregion

        public DbSet<zz_rpm_weekly_email_to_fundraisers> zz_rpm_weekly_email_to_fundraisers { get; set; }

        public DbSet<crmIMPORTQUE> crmIMPORTQUE { get; set; }
        public DbSet<LegislatorInfo> LEGISLATORINFO { get; set; }
        public DbSet<x_datax> x_datax { get; set; }

        public DbSet<Attribute_> Attribute_ { get; set; }
        public DbSet<AttributeCategory> AttributeCategory { get; set; }
        public DbSet<AttributePeople> AttributePeople { get; set; }

        public DbSet<Action_> Action_ { get; set; }
        public DbSet<ActionCategory> ActionCategory { get; set; }
        public DbSet<ActionPeople> ActionPeople { get; set; }
        public DbSet<ACTIONPEOPLEDOC> ACTIONPEOPLEDOC { get; set; }

        public DbSet<x_gs_org> x_gs_org { get; set; }
        public DbSet<zLoadLog> zLoadLog { get; set; }

        public DbSet<EmailListCustomField> EmailListCustomFields { get; set; }

        public DbSet<VirginiaFiler> VirginiaFilers { get; set; }
        public DbSet<VirginiaReport> VirginiaReports { get; set; }
        public DbSet<VirginiaReportType> VirginiaReportTypes { get; set; }
        public DbSet<VirginiaOffice> VirginiaOffices { get; set; }

        public DbSet<IllinoisFiler> IllinoisFilers { get; set; }
        public DbSet<IllinoisReport> IllinoisReports { get; set; }
        public DbSet<IllinoisReportType> IllinoisReportTypes { get; set; }

        public DbSet<user_montly_calendar> v_monthly_calendar { get; set; }

        protected override void OnModelCreating(DbModelBuilder modelBuilder)
        {
            modelBuilder.Conventions.Remove<System.Data.Entity.ModelConfiguration.Conventions.PluralizingTableNameConvention>();

            /*
            modelBuilder.Entity<user>().ToTable("ssSystems");
             */
        }
    }

    public class DBInitializer : IDatabaseInitializer<_crmDBContext>
    {
        public void InitializeDatabase(_crmDBContext context)
        {
            if (!context.Database.Exists())
            {
                throw new NotSupportedException("The database does not exist.");
            }
        }
    }

}