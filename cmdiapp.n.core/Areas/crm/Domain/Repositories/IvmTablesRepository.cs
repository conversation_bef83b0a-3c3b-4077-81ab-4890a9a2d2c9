﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using cmdiapp.n.core.Areas.crm.Domain.Models;

namespace cmdiapp.n.core.Areas.crm.Domain.Repositories
{
    public interface IvmDemographicRepository : I_baseRepository<vm_Demographic>
    {

    }

    public interface IvmVoterRepository : I_baseRepository<vm_Voter>
    {

    }


    public interface IvmListInfoRepository : I_baseRepository<vm_ListInfo>
    {

    }


    public interface IvmVoteHistoryRepository : I_baseRepository<vm_VoteHistory>
    {

    }

    public interface IvmjtListvoterRepository : I_baseRepository<vm_jt_Listvoter>
    {

    }
}