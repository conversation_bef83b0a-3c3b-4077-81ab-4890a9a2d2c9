﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Data.Linq;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

using cmdiapp.n.core.Domain.ViewModels;
using cmdiapp.n.core.Areas.crm.Domain.Data;

namespace cmdiapp.n.core.Areas.crm.Domain.Models
{
    // (view)v_people_rec  -  Used for a single PEOPLE Add & Update ]]

    [Table("v_people_rec")]
    public class PeopleF : _entityBase_crm, iItemType
    {
        [Column]
        [Key]
        public int PID { get; set; }

        [Column]
        public DateTime? DOB { get; set; } 

        [Column]
        public string PEOTYPE { get; set; }

        [Column]
        public string PEOCODE { get; set; }

        [Column]
        public string ACCTNO { get; set; }

        [Column]
        public Nullable<int> TRACKNO { get; set; }

        [Column]
        public string PREFIX { get; set; }

        [Column]
        public string FNAME { get; set; }

        [Column]
        public string MNAME { get; set; }

        [Column]
        public string LNAME { get; set; }

        [Column]
        public string SUFFIX { get; set; }

        [Column]
        public string FULLNAME { get; set; }

        [Column]
        public string STR1 { get; set; }

        [Column]
        public string STR2 { get; set; }

        [Column]
        public int? NUM1 { get; set; }

        [Column]
        public string INDUSTRY { get; set; }

        [Column]
        public string EMPLOYER { get; set; }

        [Column]
        public string OCCUPATION { get; set; }

        [Column]
        public string SALUTATION { get; set; }

        [Column]
        public string INFSALUT { get; set; }

        [Column]
        public string PRESSALUT { get; set; }

        [Column]
        public string MAILNAME { get; set; }

        [Column]
        public string SPOUSENAME { get; set; }

        [Column]
        public string ASSISTANT { get; set; }

        [Column]
        public string TITLE { get; set; }

        [Column]
        public string FECCMTEID { get; set; }

        [Column]
        public Nullable<bool> PRIMEMAIL { get; set; }

        [Column]
        public string ADDRTYPE { get; set; }

        [Column]
        public string STREET { get; set; }

        [Column]
        public string ADDR1 { get; set; }

        [Column]
        public string ADDR2 { get; set; }

        [Column]
        public string CITY { get; set; }

        [Column]
        public string STATE { get; set; }

        [Column]
        public string ZIP { get; set; }

        [Column]
        public string PLUS4 { get; set; }

        [Column]
        public string FULLADDR { get; set; }

        [Column]
        public string STATECOUNTY { get; set; }

        [Column]
        public string HMPH { get; set; }

        [Column]
        public string BUSPH { get; set; }

        [Column]
        public string CELLPH { get; set; }

        [Column]
        public string EMAIL { get; set; }

        [Column]
        public string URL { get; set; }

        [Column]
        public string FAX { get; set; }

        [Column("_updating_uid")]
        public Nullable<short> updating_uid { get; set; }

        [Column]
        public string FACEBOOKID { get; set; }

        [Column]
        public string TWITTERID { get; set; }

        [Column("LINKEDID")]
        public string LINKEDINID { get; set; }
        
        [Column]
        public string BIO { get; set; }

        [Column]
        public string ASSTPHONE { get; set; }

        [Column]
        public string ASSTEMAIL { get; set; }

        [Column]
        public string DIRECTPHONE { get; set; }

        [Column]
        public Int16? CHAPCODEID { get; set; }

        [Column]
        public bool ACTIVE { get; set; }

        [Column]
        public DateTime? DOD { get; set; }

        [Column]
        public Int16? COUNTRYID { get; set; }

    }

    [Table("jtRELATE")]
    public class jtRELATE : _entityBase_crm, iItemType
    {
        [Column]
        [Key]
        public int JTRELATEID { get; set; }

        [Column]
        public int PID { get; set; }

        [Column]
        public int RELATETO { get; set; }

        [Column]
        public Int16 RELATETYPEID { get; set; }

        [Column]
        public string COMMENT { get; set; }

        [Column]
        public DateTime? UPDATEDON { get; set; }

        [Column]
        public DateTime? RELATEDTE { get; set; }

        [Column("_updating_uid")]
        public Int16? updating_uid { get; set; }
    }

    public class RelatePlusNames
    {
        public jtRELATE jtRelate { get; set; }
        public string SourceName { get; set; }
        public string TargetName { get; set; }
    }

    public class RelationInfo
    {
        public int PID { get; set; }
        public string FirstName { get; set; }
        public string FullName { get; set; }
    }

    public class RelationNode
    {
        public jtRELATE Relate { get; set; }
        public string Name { get; set; }
        public string RelationDescrip { get; set; }
        public int ChildPID { get; set; }
    }
}