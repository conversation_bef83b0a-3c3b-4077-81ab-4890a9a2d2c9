﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

using Ninject;
using Ninject.Web.Mvc;

using cmdiapp.n.core.Core;
using cmdiapp.n.core.Domain.Models;
using cmdiapp.n.core.Core.App_Start;
using cmdiapp.n.core.Areas.crm.Core;
using cmdiapp.n.core.Areas.crm.Domain.Models;
using cmdiapp.n.core.Areas.crm.Domain.ViewModels;
using cmdiapp.n.core.Areas.crm.Domain.Repositories;
using cmdiapp.n.core.Areas.crm.Domain.Data;

using System.Reflection.Emit;
using System.Reflection;
using cmdiapp.n.core.Domain.ViewModels;

namespace cmdiapp.n.core.Areas.crm.Domain.Services
{
    public class jfcService : IjfcService
    {
        private I___unitOfWork _unitOfWork;
        private I__DBFactory _dbFactory;
        private I_entity_crm _entity_crm;

        public jfcService(I___unitOfWork unitOfWork, I__DBFactory dbFactory, I_entity_crm entity_crm)
        {
            _unitOfWork = unitOfWork;
            _dbFactory = dbFactory;
            _entity_crm = entity_crm;
        }

        public List<v_JFC_batch_ext> get_v_distributions(string where, string orderBy, int pageSize, int pageNo)
        {
            string sql = "";

            if (String.IsNullOrEmpty(where))
            {
                sql = string.Format("SELECT * FROM v_JFC_batch");
            }
            else
            {
                sql = string.Format("SELECT * FROM v_JFC_batch WHERE {0}", where);
            }

            var q = _dbFactory.getContext().Database.SqlQuery<v_JFC_batch_ext>(String.Format("EXEC dbo.get_pagedDataSet '{0}', '{1}', {2}, {3}", sql, orderBy, pageSize, pageNo));

            return q.ToList();
        }

        public int deleteDistribution(string _sql)
        {
            var q = _dbFactory.getContext().Database.SqlQuery<Int32>(_sql);
            return q.FirstOrDefault();
        }

        public int addDistribution(JFCDISTRIB _jfcdistrib)
        {
            _entity_crm.Add(_jfcdistrib);
            _entity_crm.CommitChanges();
            return _jfcdistrib.DISTRIBID;
        }

        public int saveDistribution(JFCDISTRIB _jfcdistrib)
        {
            _entity_crm.Update(_jfcdistrib);
            _entity_crm.CommitChanges();
            return _jfcdistrib.DISTRIBID;
        }

        public List<v_JFC_batch_ext> get_jfcmisslink(string fundcode, string orderBy, int pageSize, int pageNo)
        {
            string sql = "";

            if (String.IsNullOrEmpty(fundcode))
            {
                sql = string.Format("SELECT * FROM fn_JfcMissLink(''{0}'')", '%');
            }
            else
            {
                sql = string.Format("SELECT * FROM fn_JfcMissLink(''{0}'')", fundcode);
            }

            var q = _dbFactory.getContext().Database.SqlQuery<v_JFC_batch_ext>(String.Format("EXEC dbo.get_pagedDataSet '{0}', '{1}', {2}, {3}", sql, orderBy, pageSize, pageNo));

            return q.ToList();
        }

        public List<jfc_Distrib_ext> get_jfcDistrib(string fundcode, string participant, string orderBy, int pageSize, int pageNo)
        {
            string sql = string.Format("select * from [dbo].[fn_dsb_jfcDistrib](''{0}'',''{1}'')", fundcode, participant);

            var q = _dbFactory.getContext().Database.SqlQuery<jfc_Distrib_ext>(String.Format("EXEC dbo.get_pagedDataSet '{0}', '{1}', {2}, {3}", sql, orderBy, pageSize, pageNo));

            return q.ToList();
        }

        public List<jfc_AwaitDistrib_ext> get_jfcAwaitDistrib(string fundcode, string participant, string orderBy, int pageSize, int pageNo)
        {
            string sql = string.Format("select * from [dbo].[fn_dsb_jfcAwaitDistrib](''{0}'',''{1}'')", fundcode, participant);

            var q = _dbFactory.getContext().Database.SqlQuery<jfc_AwaitDistrib_ext>(String.Format("EXEC dbo.get_pagedDataSet '{0}', '{1}', {2}, {3}", sql, orderBy, pageSize, pageNo));

            return q.ToList();
        }

        public List<jfc_Alloc_ext> get_jfcAlloc(string fundcode, string participant, string orderBy, int pageSize, int pageNo)
        {
            string sql = string.Format("select * from [dbo].[fn_dsb_jfcAlloc](''{0}'',''{1}'')", fundcode, participant);

            var q = _dbFactory.getContext().Database.SqlQuery<jfc_Alloc_ext>(String.Format("EXEC dbo.get_pagedDataSet '{0}', '{1}', {2}, {3}", sql, orderBy, pageSize, pageNo));

            return q.ToList();
        }
    }
}