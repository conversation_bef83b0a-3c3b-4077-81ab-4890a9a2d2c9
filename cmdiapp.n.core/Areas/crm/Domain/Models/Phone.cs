using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Data;
using System.Linq;
using System.Web.Mvc;
using System.Data.Linq;
using System.ComponentModel.DataAnnotations.Schema;

using cmdiapp.n.core.Domain.ViewModels;
using cmdiapp.n.core.Areas.crm.Domain.Data;

namespace cmdiapp.n.core.Areas.crm.Domain.Models
{
    [Table("phone")]
    public class Phone : _entityBase_crm, iItemType
    {
        [Column]
        [Key]
        public int PHONEID { get; set; }

        [Column]
        public int PID { get; set; }

        [Column]
        public short PHNTYPEID { get; set; }

        [Column]
        public string PHNNO { get; set; }

        [Column]
        public string COMMENT { get; set; }

        [Column]
        public byte PRIME { get; set; }

        [Column("_updating_uid")]
        public Nullable<short> updating_uid { get; set; }

        [Column]
        public DateTime? VERIFIEDAT { get; set; }

        [Column]
        public bool BAD { get; set; }

    }
}
