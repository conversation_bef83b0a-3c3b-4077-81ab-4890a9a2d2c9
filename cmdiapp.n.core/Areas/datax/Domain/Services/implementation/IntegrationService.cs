﻿using System;
using System.Collections.Generic;
using System.Configuration;
using System.IO;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Threading.Tasks;
using System.Web;
using cmdiapp.n.core.Areas.datax.Domain.Models;
using cmdiapp.n.core.Areas.datax.Domain.Models.ViewModels;
using cmdiapp.n.core.Library;
using cmdiapp.n.core.Core;
using cmdiapp.n.core.Domain.Data;

namespace cmdiapp.n.core.Areas.datax.Domain.Services.implementation
{
    /// <summary>
    /// An implementation of <see cref="IIntegrationService"/>
    /// </summary>
    public class IntegrationService : IIntegrationService
    {
        private Dictionary<int, CallerRequestConfig> CallerIdToRequestBuildersMap;
        private static HttpClient Client = new HttpClient();
        private IAccountService _accountservice;
        private I_entity _entity;
        public IntegrationService(IAccountService accountservice)
        {
            _entity = I_entityManager.getEntity();
            _accountservice = accountservice;

            CallerIdToRequestBuildersMap = new Dictionary<int, CallerRequestConfig>();
            // set configurations
            CallerIdToRequestBuildersMap.Add(1,
                new CallerRequestConfig()
                {
                    RequestBuilder = AnedotRequestBuilder,
                    ResponseReader = AnedotResponseReader
                });
            CallerIdToRequestBuildersMap.Add(9,
                new CallerRequestConfig()
                {
                    RequestBuilder = WealthEngineRequestBuilder
                });
        }

        public async Task<List<CallerAccountDisplay>> GetCallerAccountsAsync(CallerCredentials credentials, int callerId)
        {
            List<CallerAccountDisplay> accounts = new List<CallerAccountDisplay>();
            // get configuration according to callerId
            if (!CallerIdToRequestBuildersMap.TryGetValue(callerId, out CallerRequestConfig requestConfig))
            {
                // if fails, return empty list
                return accounts;
            }
            // get request from RequestBuilder
            HttpRequestMessage request = requestConfig.RequestBuilder(credentials);
            if (request == null)
            {
                return accounts;
            }
            try
            {                
                // required to establish SSL/TLS connection
                ServicePointManager.Expect100Continue = true;
                ServicePointManager.SecurityProtocol = SecurityProtocolType.Tls12;
                // send request to partner
                HttpResponseMessage response = await Client.SendAsync(request);
                //wealthEngine
                if (callerId == 9)
                {
                    if((response?.IsSuccessStatusCode ?? false) && response?.Content != null)
                    {
                        string html =await response.Content.ReadAsStringAsync();
                        if (!html.ToUpper().Contains("ERROR") && response.Content.Headers.ContentLength > 200)
                        {
                            //account validated
                            accounts.Add(new CallerAccountDisplay() { Id = credentials.UserName, Name = credentials.UserName });
                        }
                    }
                    return accounts;
                }
                if ((response?.IsSuccessStatusCode ?? false) && response?.Content != null)
                {
                    // if successful, read response body into List<CallerAccountDisplay>
                    accounts = await requestConfig.ResponseReader(response.Content);
                }
            }
            catch (Exception ex)
            {
                util.recordInErrorLog($"{ex.Source} {ex.GetType().Name}: {ex.Message}");
            }
            return accounts;
        }

        private delegate HttpRequestMessage CallerRequestBuilder(CallerCredentials credentials);

        private delegate Task<List<CallerAccountDisplay>> CallerResponseReader(HttpContent content);

        private class CallerRequestConfig
        {
            public CallerRequestBuilder RequestBuilder { get; set; }
            public CallerResponseReader ResponseReader { get; set; }
        }

        private static HttpRequestMessage AnedotRequestBuilder(CallerCredentials credentials)
        {
            string authKey = credentials.Base64EncodedCredentials;
            if (string.IsNullOrEmpty(authKey))
            {
                return null;
            }
            string url = ConfigurationManager.AppSettings.Get("anedotAccountsUrl");
            HttpRequestMessage request = new HttpRequestMessage(HttpMethod.Get, url);
            request.Headers.Authorization = new AuthenticationHeaderValue("Basic", authKey + "=");
            return request;
        }
        private static Task<List<CallerAccountDisplay>> AnedotResponseReader(HttpContent content)
        {
            return content.ReadAsAsync<List<AnedotAccount>>()
                .ContinueWith((task) =>
                    task.Result?.Select(anedotAccount => new CallerAccountDisplay()
                    {
                        Name = anedotAccount.DisplayName,
                        Id = anedotAccount.Id
                    }).ToList() ?? new List<CallerAccountDisplay>());
        }
        private static HttpRequestMessage WealthEngineRequestBuilder(CallerCredentials credentials)
        {
            if (string.IsNullOrEmpty(credentials.UserName) || string.IsNullOrEmpty(credentials.PassKey))
            {
                return null;
            }
            string _unAndPasswd = string.Format("uid={0}&passwd={1}", credentials.UserName, credentials.PassKey);
            string url = string.Format("https://fw8.wealthengine.com/findwealth/faces/advSrch?loginPath=A&{0}",
                                    _unAndPasswd
                                );
            HttpRequestMessage request = new HttpRequestMessage(HttpMethod.Get, url);
            //request.Headers.Authorization = new AuthenticationHeaderValue("Basic", authKey + "=");
            return request;
        }


        public async Task<string> AnedotccProcessUrl(int fundid, string batchno, DateTime batchdte)
        {
            try
            {
                // get account uid
                List<Account> accounts =  _accountservice.GetAccountsForCurrentProject(1);
                Account account = accounts.SingleOrDefault<Account>(a => a.loadInfo.fundId == fundid);
                if (account == null)
                {
                    return null;
                }
                else
                {
                    // get crimson token
                    int projectId = session.currentDomain_projectId;
                    string apiKey = ConfigurationManager.AppSettings.Get("crimsonAPIKey");
                    string apiUrl = ConfigurationManager.AppSettings.Get("crimsonAPIUrl") + projectId.ToString();
                    using (HttpClient client = new HttpClient())
                    {
                        ServicePointManager.Expect100Continue = true;
                        ServicePointManager.SecurityProtocol = SecurityProtocolType.Tls12;

                        client.BaseAddress = new Uri(apiUrl);
                        client.DefaultRequestHeaders.Add("x-functions-key", apiKey);
                        HttpResponseMessage response = await client.GetAsync(apiUrl);
                        if (response.IsSuccessStatusCode)
                        {
                            string token = await response.Content.ReadAsStringAsync();
                            // anedot ccProcess Url
                            return string.Format("{0}?token={1}&account_uid={2}&batchno={3}&batchdate={4}",
                                projectId == 1 || projectId == 71 ? ConfigurationManager.AppSettings.Get("anedotccProcessSandboxUrl") : ConfigurationManager.AppSettings.Get("anedotccProcessUrl"),
                                token, account.clientCallerAccountId,
                                batchno, batchdte.ToString("yyyy-MM-dd"));
                        }
                        else
                        {
                            return null;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                return null;
            }
        }

        public async Task<string> AnedotccProcessOneUrl(int fundid, string batchno, DateTime batchdte, int pid, string refno, string srcecode, decimal amt,
                                        string chapcode, string campgncode, int? trackno, string comment)
        {
            try
            {
                // get account uid
                List<Account> accounts = _accountservice.GetAccountsForCurrentProject(1);
                Account account = accounts.SingleOrDefault<Account>(a => a.loadInfo.fundId == fundid);
                if (account == null)
                {
                    return null;
                }
                else
                {
                    // get crimson token
                    int projectId = session.currentDomain_projectId;
                    string apiKey = ConfigurationManager.AppSettings.Get("crimsonAPIKey");
                    string apiUrl = ConfigurationManager.AppSettings.Get("crimsonAPIUrl") + projectId.ToString();
                    using (HttpClient client = new HttpClient())
                    {
                        ServicePointManager.Expect100Continue = true;
                        ServicePointManager.SecurityProtocol = SecurityProtocolType.Tls12;

                        client.BaseAddress = new Uri(apiUrl);
                        client.DefaultRequestHeaders.Add("x-functions-key", apiKey);
                        HttpResponseMessage response = await client.GetAsync(apiUrl);
                        if (response.IsSuccessStatusCode)
                        {
                            string token = await response.Content.ReadAsStringAsync();
                            // anedot ccProcess Url
                            string url = string.Format("{0}?token={1}&account_uid={2}&batchno={3}&batchdate={4}&pid={5}&source_code={6}&amount={7}",
                                projectId == 1 || projectId == 71 ? ConfigurationManager.AppSettings.Get("anedotccProcessSandboxUrl") : ConfigurationManager.AppSettings.Get("anedotccProcessUrl"),
                                token, account.clientCallerAccountId,
                                batchno, batchdte.ToString("yyyy-MM-dd"), pid, srcecode, amt);
                            url += refno == null || refno == ""? "" : "&refno=" + refno;
                            url += chapcode == null || chapcode == "" ? "" : "&chapcode=" + chapcode;
                            url += campgncode == null || campgncode == "" ? "" : "&campgncode=" + campgncode;
                            url += trackno == null || trackno == 0 ? "" : "&trackno=" + trackno;
                            url += comment == null || comment == ""? "" : "&comment=" + comment;
                            return url;
                        }
                        else
                        {
                            return null;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                return null;
            }
        }

        public bool setupAnedot(int fundid)
        {
            // use web.config to phase in anedot clients
            //string anedotProjectids = ConfigurationManager.AppSettings.Get("anedotProjectIds");
            //string[] projectIds = anedotProjectids.Split(',');
            //if (projectIds.Where(x => x == session.currentDomain_projectId.ToString()).Count() > 0) {
                // for clients with 'datax-anedot' configuration
                List<Account> accounts = _accountservice.GetAccountsForCurrentProject(1);
                if (accounts.Count > 0)
                {
                    if (fundid > 0) /* check fund */
                    {
                        Account account = accounts.SingleOrDefault<Account>(a => a.loadInfo.fundId == fundid);
                        return account == null ? false : true;
                    }
                    else /* just check if any account is set up for Donate Now */
                        return true;
                }
                else
                {
                    return false;
                }
            //}
            //else
            //{
            //    return false;
            //}
        }
    }
}