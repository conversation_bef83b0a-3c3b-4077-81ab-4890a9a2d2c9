﻿using cmdiapp.n.core.Areas.datax.Domain.Data;
using cmdiapp.n.core.Domain.ViewModels;
using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Runtime.Serialization;

namespace cmdiapp.n.core.Areas.datax.Domain.Models
{
    /// <summary>
    /// Represents a Cremir sending email records job
    /// </summary>
    [Table("v_es_SendJobInfo")]
    [DataContract]
    public class CremirSendJobInfo : _entityBase_datax, iItemType
    {
        /// <summary>
        /// The Id of the job
        /// </summary>
        [Column("jobId")]
        [Key]
        [DataMember(Name = "jobId")]
        public int JobId { get; set; }
        /// <summary>
        /// The <see cref="Account.Id"/>
        /// </summary>
        [Column("accountId")]
        [DataMember(Name = "accountId")]
        public int AccountId { get; set; }
        /// <summary>
        /// The email list id
        /// </summary>
        [Column("listId")]
        [DataMember(Name = "listId")]
        public string ListId { get; set; }
        /// <summary>
        /// The type of Job--whether it's a one-time or periodic send
        /// </summary>
        [Column("jobType")]
        [DataMember(Name = "jobType")]
        public string JobType { get; set; }
        /// <summary>
        /// The code for the job type (will be SO for one-time or SP for periodic)
        /// </summary>
        [Column("jobTypeCode")]
        [DataMember(Name = "jobTypeCode")]
        public string JobTypeCode { get; set; }
        /// <summary>
        /// The job status: Unstarted, Started, Succeeded, Failed, Archived
        /// </summary>
        [Column("jobStatus")]
        [DataMember(Name = "jobStatus")]
        public string JobStatus { get; set; }
        /// <summary>
        /// Code for the job status: U: Unstarted, S: Started, O: Succeeded, X: Failed, A: Archived
        /// </summary>
        [Column("jobStatusCode")]
        [DataMember(Name = "jobStatusCode")]
        public string JobStatusCode { get; set; }
        /// <summary>
        /// Timestamp for when the job was created
        /// </summary>
        [Column("createdOn")]
        [DataMember(Name = "createdOn")]
        public DateTime CreatedOn { get; set; }
        /// <summary>
        /// Timestamp for when the job was started
        /// </summary>
        [Column("startedOn")]
        [DataMember(Name = "startedOn")]
        public DateTime? StartedOn { get; set; }
        /// <summary>
        /// Timestamp for when the job was completed
        /// </summary>
        [Column("completedOn")]
        [DataMember(Name = "completedOn")]
        public DateTime? CompletedOn { get; set; }
        /// <summary>
        /// The number of records Cremir attempted to send for this job
        /// </summary>
        [Column("totalRecordCount")]
        [DataMember(Name = "totalRecordCount")]
        public int? TotalRecordCount { get; set; }
        /// <summary>
        /// The last log message for this job
        /// </summary>
        [Column("lastLogMessage")]
        [DataMember(Name = "lastLogMessage")]
        public string LastLogMessage { get; set; }
    }
}