﻿using cmdiapp.n.core.Areas.legislator.Domain.Models;
using cmdiapp.n.core.Areas.legislator.Domain.Services;
using cmdiapp.n.core.Core.App_Start;
using cmdiapp.n.core.Domain.ViewModels;
using Ninject;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Web.Http;

namespace cmdiapp.n.core.Areas.legislator.Presentation.api
{
    [RoutePrefix("legislator/api/ContactChange")]
    [Authorize]
    public class ContactChangeController : ApiController
    {
        private IContactChangeService _service;

        public ContactChangeController()
        {
            _service = NinjectMVC.kernel.Get<IContactChangeService>();
        }

        [HttpGet, Route("{id}")]
        public ContactChange Get(int id)
        {
            return _service.Get(id);
        }

        [HttpPost, Route("Submit")]
        public genericResponse Submit(ContactChange change)
        {
            return _service.Submit(change);
        }

        [HttpGet, Route("{id}/GetAccessibleStatuses")]
        public List<ContactChangeStatus> GetAccessibleStatuses(int id)
        {
            return _service.GetAccessibleStatuses(id);
        }

        [HttpPost, Route("{id}/UpdateStatus/{statusId}")]
        public genericResponse UpdateStatus(int id, int statusId)
        {
            return _service.UpdateStatus(id, statusId);
        }

        [HttpGet, Route("Types")]
        public List<ContactChangeType> GetChangeTypes()
        {
            return _service.GetChangeTypes();
        }

        [HttpGet, Route("Statuses")]
        public List<ContactChangeStatus> GetChangeStatuses()
        {
            return _service.GetChangeStatuses();
        }

        [HttpGet, Route("LegislatorDisplay/{legislatorId}")]
        public ContactChangeDisplayPage GetLegislatorDisplay(string legislatorId, bool pendingOnly = true, int contactId = 0, int pageNo = 1)
        {
            return _service.GetLegislatorDisplay(legislatorId, pendingOnly, contactId, pageNo);
        }

        [HttpGet, Route("{id}/PreviouslyApproved")]
        public ContactChange GetPreviouslyApprovedChange(int id)
        {
            return _service.GetPreviouslyApprovedChange(id);
        }

        [HttpPost, Route("{id}/Amend/{fieldName}")]
        public genericResponse AmendChangeField([FromBody] string value, int id, string fieldName)
        {
            return _service.AmendChangeField(id, fieldName, value);
        }
    }
}