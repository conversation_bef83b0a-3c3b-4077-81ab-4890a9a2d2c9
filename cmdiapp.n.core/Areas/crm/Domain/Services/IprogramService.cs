﻿using System.Collections.Generic;
using System.Web.Mvc;

using cmdiapp.n.core.Areas.crm.Domain.Models;
using cmdiapp.n.core.Areas.crm.Domain.ViewModels;
using cmdiapp.n.core.Areas.crm.Domain.Repositories;

namespace cmdiapp.n.core.Areas.crm.Domain.Services
{
    public interface IprogramService
    {

        bool check_unqiueness(PROGRAM _program);
        bool check_record_exists(int progID);

        void Add(PROGRAM _program);
        void Update(PROGRAM _program);
        void Delete(PROGRAM _program);

        List<PROGRAM> read_all_programs();
        PROGRAM get_program(int progID);

        List<PROGRAM_ext> get_all_programs(string where, string orderBy, int pageSize, int pageNo);

    }
}