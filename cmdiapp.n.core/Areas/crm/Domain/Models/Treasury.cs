﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Data;
using System.Linq;
using System.Web.Mvc;
using System.Data.Linq;
using System.ComponentModel.DataAnnotations.Schema;

using cmdiapp.n.core.Domain.ViewModels;
using cmdiapp.n.core.Areas.crm.Domain.Data;

namespace cmdiapp.n.core.Areas.crm.Domain.Models
{
    public class AccountRegister : _entityBase_crm, iItemType
    {
        public int ROWID { get; set; }
        public int TXNID { get; set; }
        public string TRANSACTIONTYPE { get; set; }
        public string PAYMENTTYPE { get; set; }
        public string CHKNO { get; set; }
        public DateTime? TXNDTE { get; set; }
        public string VENDORNAME { get; set; }
        public decimal? RECEIPT { get; set; }
        public decimal? DISBURSMENT { get; set; }
        public decimal? BALANCE { get; set; }
        public string MEMOCD { get; set; }
        public short TXNTYPEID { get; set; }
    }

    public class AccountRegisterSearch : _entityBase_crm, iItemType
    {
        public int? CENTERID { get; set; }
        public short? TXNTYPEID { get; set; }
        public DateTime? TXNDTEFROM { get; set; }
        public DateTime? TXNDTETO { get; set; }
        public string VENDOR { get; set; }
    }
    public class AccountRegisterSearchData : _entityBase_crm, iItemType
    {
        public AccountRegisterSearch searchData { get; set; }
    }

    public class AccountCashSummary : _entityBase_crm, iItemType
    {
        public Int16 CENTERID { get; set; }
        public string DESCRIP { get; set; }
        public decimal? ENDINGBALANCE { get; set; }
    }

    public class InvoiceSummary : _entityBase_crm, iItemType
    {
        public string FUNDCODE { get; set; }
        public string DESCRIP { get; set; }
        public decimal? FUNDBALANCE { get; set; }
    }

    public class InvoiceTotals : _entityBase_crm, iItemType
    {
        public int? ENTITYID { get; set; }
        public string VENDOR { get; set; }
        public decimal? BALANCETOTAL { get; set; }
        public decimal? BALANCE0TO30 { get; set; }
        public decimal? BALANCE31TO60 { get; set; }
        public decimal? BALANCEGT60 { get; set; }
    }
}