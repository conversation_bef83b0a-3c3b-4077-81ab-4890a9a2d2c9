﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Net;
using System.Web.Http;

using Ninject;
using Ninject.Web.Mvc;

using cmdiapp.n.core.Areas.docService.Domain.Models;
using cmdiapp.n.core.Areas.docService.Domain.Data;
using cmdiapp.n.core.Areas.docService.Domain.Services;
using cmdiapp.n.core.Core;
using cmdiapp.n.core.Domain.Models;

namespace cmdiapp.n.core.Areas.docService.Presentation.Controllers.api
{

    [Authorize]
    public class documentController : ApiController
    {
        private IdocService _service;
        //private userSession _userSession;

        public documentController()
        {
            _service = I_entityManager_docService.getService();
            //_userSession = session.userSession;
        }

        //[HttpGet, Route("docService/api/etc/getAllphotos/{rand}")]
        //public List<emailMsgData> getAllphotos( int rand)
        //{
        //    string email = _userSession.Email;
        //    return _service.get_emailMsgData(email);
        //}

        //[HttpGet, Route("dataService/api/etc/nickNames/{FirstName}")]
        //public List<string> nickNames(string FirstName)
        //{
        //    string nickNames = _service.same_nickNames(FirstName);
        //    return nickNames.Split(',').ToList<string>().Where(a=>!string.IsNullOrWhiteSpace(a)).ToList();
        //}

    }
}
