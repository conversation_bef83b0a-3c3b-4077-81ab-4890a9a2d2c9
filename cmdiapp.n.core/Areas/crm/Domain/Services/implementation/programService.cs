﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

using Ninject;
using Ninject.Web.Mvc;

using cmdiapp.n.core.Core.App_Start;
using cmdiapp.n.core.Areas.crm.Domain.Models;
using cmdiapp.n.core.Areas.crm.Domain.ViewModels;
using cmdiapp.n.core.Areas.crm.Domain.Repositories;
namespace cmdiapp.n.core.Areas.crm.Domain.Services
{
    public class programService : IprogramService
    {
        private I___unitOfWork _unitOfWork;
        private I__DBFactory _dbFactory;
        private IprogramRepository _programRepository;

        public programService(I___unitOfWork unitOfWork, I__DBFactory dbFactory, IprogramRepository programRepository)
        {
            _unitOfWork = unitOfWork;
            _dbFactory = dbFactory;
            _programRepository = programRepository;
        }

        public PROGRAM get_program(int progId)
        {
            string sql = string.Format("SELECT * FROM PROGRAM WHERE PROGID={0}", progId);
            var q = _dbFactory.getContext().Database.SqlQuery<PROGRAM>(sql);
            return q.FirstOrDefault();
        }

        public bool check_unqiueness(PROGRAM _program)
        {
            // take care of apostrophe
            string v = _program.PROGTYPE.Replace("'", "''");
            string sql = "";

            if (_program.PROGID > 0)   // Existing Record
                sql = String.Format("SELECT * FROM PROGRAM WHERE PROGTYPE = '{0}' AND PROGID <> {1}", v, _program.PROGID);
            else
                sql = String.Format("SELECT * FROM PROGRAM WHERE PROGTYPE = '{0}'", v);

            var q = _dbFactory.getContext().Database.SqlQuery<PROGRAM>(sql);
            return q.ToList().Count == 0 ? true : false;
        }

        public bool check_record_exists(int progId)
        {
            string sql = string.Format("SELECT * FROM PACKAGE WHERE PROGID='{0}'", progId);
            var q = _dbFactory.getContext().Database.SqlQuery<PACKAGE>(sql);
            return q.ToList().Count > 0 ? true : false;
        }

        public void Add(PROGRAM _program)
        {
            _programRepository.Add(_program);
            _unitOfWork.commit();
        }

        public void Update(PROGRAM _program)
        {
            _programRepository.Update(_program);
            _unitOfWork.commit();
        }

        public void Delete(PROGRAM _program)
        {
            _programRepository.Delete(_program);
            _unitOfWork.commit();
        }

        //Read all programs
        public List<PROGRAM> read_all_programs()
        {
            return _programRepository.All().ToList();
        }

        public List<PROGRAM_ext> get_all_programs(string where, string orderBy, int pageSize, int pageNo)
        {
            string sql = (!string.IsNullOrEmpty(where) ? string.Format("SELECT * FROM PROGRAM WHERE {0}", where) : "SELECT * FROM PROGRAM");

            var q = _dbFactory.getContext().Database.SqlQuery<PROGRAM_ext>(String.Format("EXEC dbo.get_pagedDataSet '{0}', '{1}', {2}, {3}", sql, orderBy, pageSize, pageNo));
            
            return q.ToList();
        }
    }
}