﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Data;
using System.Linq;
using System.Web.Mvc;
using System.Data.Linq;
using System.ComponentModel.DataAnnotations.Schema;

using cmdiapp.n.core.Domain.ViewModels;
using cmdiapp.n.core.Areas.crm.Domain.Data;

namespace cmdiapp.n.core.Areas.crm.Domain.Models
{
    [Table("lkChannel")]
    public class lkChannel : _entityBase_crm, iItemType
    {
        [Column]
        [Key]
        public Int16 channelId { get; set; }

        [Column]
        public string channel { get; set; }

        [Column]
        public string descrip { get; set; }
        

    }
    public class lkChannel_ext : _entityBase_crm, iItemType
    {
        public Int16 channelId { get; set; }

        public string channel { get; set; }

        public string descrip { get; set; }
        
        public bool? isDefault { get; set; }
        
        public bool? isSystem { get; set; }
        public int count_ { get; set; }
        public Int64 rowNo { get; set; }

    }

    [Table("lkTargetChannel")]
    public class lkTargetChannel : _entityBase_crm, iItemType
    {
        [Column]
        [Key]
        public Int32 channelId { get; set; }

        [Column]
        public string name { get; set; }

        [Column]
        public Int32? order { get; set; }

        [Column]
        public bool show { get; set; }

    }



    [Table("lkPEOCLASS")]
    public class lkPEOCLASS : _entityBase_crm
    {
        [Column]
        [Key]
        public Int16 PEOCLASSID { get; set; }

        [Column]
        public string PEOCLASS { get; set; }

        [Column]
        public string DESCRIP { get; set; }

    }

    public class MessageBoard
    {
        public string message { get; set; }

        public bool status { get; set; }

        public string xmlresponse { get; set; }

        public string tempTableName { get; set; }

        public int impTypeSelection { get; set; }

        public int recordCount { get; set; }

        public int prevDtBatchId { get; set; }

        public int? BatchId { get; set; }

        public Decimal? Amount { get; set; }

        public string AdjType { get; set; }
        public string dataKey { get; set; }
    }

    public class UniquenessCheck
    {
        public int recordCount { get; set; }
    }

    public class OtherDataEntryFields
    {
        public string selectedflagsId { get; set; }

        public string selectedkeywordsId { get; set; }

        public bool redesignationFChkBoxSel { get; set; }

        public bool redesignationPChkBoxSel { get; set; }

        public bool splitChkBoxSel { get; set; }

        public bool reattributeChkboxSel { get; set; }

        public bool AutoCaseChkBoxSel { get; set; }

        public bool CCProcessChkBoxSel { get; set; }

        public bool cbMonthlySel { get; set; }

        public string AddressId { get; set; }

        public string cRENEW { get; set; }

        public string OperationTypeDE { get; set; }

        public string OdtBATCHID { get; set; }

        public string IMAGEID { get; set; }

        public string ADJTYPEID { get; set; }

        public string CCRESPMSG { get; set; }

        public string CCREFNO { get; set; }

        public string ADJAMT { get; set; }

        public string INPUTNO { get; set; }

        public bool PrimMailChkBoxSel { get; set; }

        public string pADJ_ADJDTE { get; set; }

        public string pADJ_ADJTYPEID { get; set; }

        public string pADJ_ADJAMT { get; set; }

        public string pADJ_UID { get; set; }

        public string pADJ_PID { get; set; }

        public string pADJ_FUNDID { get; set; }

        public string CHKDGT { get; set; }

        public string ACCTNO { get; set; }

        public string Attributed { get; set; }

        public string cNEWREC { get; set; }

        public string cNEWRECP { get; set; }

        public string cRENEWCHG { get; set; }

        public string Loaded { get; set; }

    }

    [Table("lkEVNTSTATUS")]
    public class lkEVNTSTATUS : _entityBase_crm, iItemType
    {
        [Column]
        [Key]
        public short STATUS { get; set; }

        [Column]
        public string DESCRIP { get; set; }
    }

    public class lkEVNTSTATUS_ext
    {
        public short STATUS { get; set; }

        public string DESCRIP { get; set; }
                
        public int count_ { get; set; }

        public Int64 rowNo { get; set; }
    }


    [Table("lkADJTYPE")]
    public class lkADJTYPE : _entityBase_crm, iItemType
    {
        [Column]
        [Key]
        public Int16 ADJTYPEID { get; set; }

        [Column]
        public string ADJTYPE { get; set; }

        [Column]
        public string DESCRIP { get; set; }

        public string CODEDESCRIP
        {
            get
            {
                return string.Format("{0} - {1}", ADJTYPE, DESCRIP);
            }
        }

        public Nullable<byte> DEFFLAG { get; set; }

        public Nullable<byte> SYSTEM { get; set; }
        [Column]
        public string appVersion { get; set; }
    }

    [Table("lkEXCEP")]
    public class lkEXCEP : _entityBase_crm, iItemType
    {
        [Column]
        [Key]
        public Int16 EXCEPID { get; set; }

        [Column]
        public string EXCEP { get; set; }

        [Column]
        public string DESCRIP { get; set; }

        public string CODEDESCRIP
        {
            get
            {
                return string.Format("{0} - {1}", EXCEP, DESCRIP);
            }
        }

        public string LETTER { get; set; }

        public string FECMEMO { get; set; }

    }

    [Table("lkMATCH")]
    public class lkMATCH : _entityBase_crm, iItemType
    {
        [Column]
        [Key]
        public Int16 MATCHID { get; set; }

        [Column]
        public string CODE { get; set; }

        [Column]
        public string DESCRIP { get; set; }

        public string CODEDESCRIP
        {
            get
            {
                return string.Format("{0} - {1}", CODE, DESCRIP);
            }
        }

    }

    [Table("lkACKW")]
    public class lkACKW : _entityBase_crm, iItemType
    {
        [Column]
        [Key]
        public Int16 ACKWID { get; set; }

        [Column]
        public string ACKW { get; set; }

        [Column]
        public string DESCRIP { get; set; }

        public string CODEDESCRIP
        {
            get
            {
                return string.Format("{0} - {1}", ACKW, DESCRIP);
            }
        }

    }

    [Table("lkCHARTACCTTYPE")]
    public class lkCHARTACCTTYPE : _entityBase_crm, iItemType
    {
        [Column]
        [Key]
        public Int16 ACCTTYPEID { get; set; }

        [Column]
        public string DESCRIP { get; set; }

    }

    [Table("lkLOBBYIST")]
    public class lkLOBBYIST : _entityBase_crm, iItemType
    {
        [Column]
        [Key]
        public Int16 LOBBYISTID { get; set; }

        [Column]
        public string LOBBYIST { get; set; }

    }

    [Table("lkFNDRGRP")]
    public class lkFNDRGRP : _entityBase_crm, iItemType
    {
        [Column]
        [Key]
        public Int16 FNDRGRPID { get; set; }

        [Column]
        public string FNDRGRP { get; set; }

        [Column]
        public string DESCRIP { get; set; }

    }

    [Table("lkFUNDRAISETITLE")]
    public class lkFUNDRAISETITLE : _entityBase_crm, iItemType
    {
        [Column]
        [Key]
        public int TITLEID { get; set; }

        [Column]
        public string TITLE { get; set; }

    }

    [Table("lkPeopleSTR1")]
    public class lkPeopleSTR1 : _entityBase_crm, iItemType
    {
        [Column]
        [Key]
        public string stringValue { get; set; }

    }

    [Table("lkPartyAffil")]
    public class lkPartyAffil : _entityBase_crm, iItemType
    {
        [Column]
        [Key]
        public string partyAffil { get; set; }
        [Column]
        public string descrip { get; set; }

    }

    [Table("lkCHAPCODE")]
    public class lkCHAPCODE : _entityBase_crm, iItemType
    {
        [Column]
        [Key]
        public Int16 CHAPCODEID { get; set; }

        [Column]
        public string CHAPCODE { get; set; }

        [Column]
        public string DESCRIP { get; set; }

        [Column]
        public Nullable<byte> DEFFLAG { get; set; }

        [Column]
        public Nullable<byte> SYSTEM { get; set; }

        public string CODEDESCRIP
        {
            get
            {
                return string.Format("{0} - {1}", CHAPCODE, DESCRIP);
            }
        }

    }

    [Table("lkMONYCODE")]
    public class lkMONYCODE : _entityBase_crm, iItemType
    {
        [Column]
        [Key]
        public Int16 MONYCODEID { get; set; }

        [Column]
        public Int16 MONYCODE { get; set; }

        [Column]
        public string DESCRIP { get; set; }

        [Column]
        public Nullable<byte> DEFFLAG { get; set; }

        [Column]
        public Nullable<byte> SYSTEM { get; set; }

        public string CODEDESCRIP
        {
            get
            {
                return string.Format("{0} - {1}", MONYCODE, DESCRIP);
            }
        }

    }

    [Table("lkGIFTTYPE")]
    public class lkGIFTTYPE : _entityBase_crm, iItemType
    {
        [Column]
        [Key]
        public Int16 GIFTTYPEID { get; set; }

        [Column]
        public string GIFTTYPE { get; set; }

        [Column]
        public string DESCRIP { get; set; }

        [Column]
        public Nullable<byte> DEFFLAG { get; set; }

        [Column]
        public Nullable<byte> SYSTEM { get; set; }

        public string CODEDESCRIP
        {
            get
            {
                return string.Format("{0} - {1}", GIFTTYPE, DESCRIP);
            }
        } 
    }

    [Table("lkMEMTYPE")]
    public class lkMEMTYPE : _entityBase_crm, iItemType
    {
        [Column]
        [Key]
        public Int16 MEMTYPEID { get; set; }

        [Column]
        public string MEMTYPE { get; set; }

        [Column]
        public string DESCRIP { get; set; }

        [Column]
        public Nullable<byte> DEFFLAG { get; set; }

        [Column]
        public Nullable<byte> SYSTEM { get; set; }

        public string CODEDESCRIP
        {
            get
            {
                return string.Format("{0} - {1}", MEMTYPE, DESCRIP);
            }
        }
    }

    [Table("lkBATTYPE")]
    public class lkBATTYPE : _entityBase_crm, iItemType
    {
        [Column]
        [Key]
        public Int16 BATTYPEID { get; set; }

        [Column]
        public string BATTYPE { get; set; }

        [Column]
        public string DESCRIP { get; set; }

        [Column]
        public Nullable<byte> DEFFLAG { get; set; }

        [Column]
        public Nullable<byte> SYSTEM { get; set; }

        public string CODEDESCRIP
        {
            get
            {
                return string.Format("{0} - {1}", BATTYPE, DESCRIP);
            }
        }
    }

    [Table("lkPIPELINE")]
    public class lkPIPELINE : _entityBase_crm, iItemType
    {
        [Column]
        [Key]
        public Int16 PIPELINEID { get; set; }

        [Column]
        public string PIPELINE { get; set; }

        [Column]
        public Int16 STAGE { get; set; }

        [Column]
        public string DESCRIP { get; set; }

        [Column]
        public Nullable<byte> DEFFLAG { get; set; }

        [Column]
        public Nullable<byte> SYSTEM { get; set; }

        public string CODEDESCRIP
        {
            get
            {
                return string.Format("{0} - {1}", PIPELINE, DESCRIP);
            }
        }
    }

    [Table("lkMOVEPLAN")]
    public class lkMOVEPLAN : _entityBase_crm, iItemType
    {
        [Column]
        [Key]
        public Int16 MOVEPLANID { get; set; }

        [Column]
        public string MOVEPLAN { get; set; }

        [Column]
        public string DESCRIP { get; set; }

        [Column]
        public Nullable<byte> DEFFLAG { get; set; }

        [Column]
        public Nullable<byte> SYSTEM { get; set; }

        public string CODEDESCRIP
        {
            get
            {
                return string.Format("{0} - {1}", MOVEPLAN, DESCRIP);
            }
        }
    }

    [Table("lkMOVERESULT")]
    public class lkMOVERESULT : _entityBase_crm, iItemType
    {
        [Column]
        [Key]
        public Int16 MOVERESULTID { get; set; }

        [Column]
        public string MOVERESULT { get; set; }

        [Column]
        public string DESCRIP { get; set; }

        [Column]
        public Nullable<byte> DEFFLAG { get; set; }

        [Column]
        public Nullable<byte> SYSTEM { get; set; }

        public string CODEDESCRIP
        {
            get
            {
                return string.Format("{0} - {1}", MOVERESULT, DESCRIP);
            }
        }
    }

    [Table("lkFUNDTYPE")]
    public class lkFUNDTYPE : _entityBase_crm, iItemType
    {
        [Column]
        [Key]
        public short FUNDTYPEID { get; set; }
        [Column]
        public string FUNDTYPE { get; set; }
        [Column]
        public string DESCRIP { get; set; }
        [Column]
        public Nullable<byte> DEFFLAG { get; set; }
        [Column]
        public Nullable<byte> SYSTEM { get; set; }
    }

    public class FundTypes
    {
        public string FUNDTYPE { get; set; }
        public string DESCRIP { get; set; }
    }
}