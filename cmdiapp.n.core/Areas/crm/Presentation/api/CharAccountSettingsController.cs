﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Net;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Web.Http;
using System.Web.Script.Serialization;
using System.Drawing;
using System.IO;
using System.Data.SqlClient;
using System.Collections.ObjectModel;
using System.Xml.Linq;

using Ninject;
using Ninject.Web.Mvc;

using AutoMapper;
using cmdiapp.n.core.Core;
using cmdiapp.n.core.Core.App_Start;
using cmdiapp.n.core.Domain.ViewModels;
using cmdiapp.n.core.Areas.crm.Core;
using cmdiapp.n.core.Areas.crm.Domain.Models;
using cmdiapp.n.core.Areas.crm.Domain.ViewModels;
using cmdiapp.n.core.Areas.crm.Domain.Data;
using cmdiapp.n.core.Areas.crm.Domain.Repositories;
using cmdiapp.n.core.Areas.crm.Domain.Services;
using cmdiapp.n.core.Areas.dataService.Domain.Data;
using cmdiapp.n.core.Areas.dataService.Domain.Models;
using cmdiapp.n.core.Areas.dataService.Domain.Services;
using cmdiapp.n.core.Presentation.Controllers;
using cmdiapp.n.core.Areas.query.Domain.Models;
using cmdiapp.n.core.Domain.Models;
using AutoMapper;
using System.Data;
using cmdiapp.n.core.Library;

namespace cmdiapp.n.core.Areas.crm.Presentation.Controllers.api
{
    [Authorize]
    public class ChartAccountSettingsController : ApiController
    {
        private I_entity_crm _entity_crm;
        private userSession _userSession;
        private readonly IReportDataService _reportService;

        #region [[ (constructor) SettingsController ]]
        public ChartAccountSettingsController()
        {
            _entity_crm = I_entityManager_crm.getEntity();
            
            _reportService = NinjectMVC.kernel.Get<IReportDataService>();
            
            _userSession = session.userSession;
        }
        #endregion

        #region [[ SQL Query]]
        const string _sql_exportChartAccounts =
           @"SELECT 
            [ACCTCODE] as [No], 
            [lkCHARTACCT].[DESCRIP] AS [Account], 
            [lkCHARTACCTTYPE].[DESCRIP] AS [Type]
            FROM  [dbo].[lkCHARTACCT]
            LEFT OUTER JOIN [dbo].[lkCHARTACCTTYPE] ON [lkCHARTACCT].[ACCTTYPEID] = [lkCHARTACCTTYPE].[ACCTTYPEID]";
        #endregion

        #region[COMPLIANCE->SETTINGS->ChartAccounts by Tanvir]

        [HttpPost, Route("crm/api/ChartAccountSettings/ExportChartAccountsKey")]
        [apiAuthorize(AccessElement = @"Compliance\Settings", AccessLevel = "v")]
        public string ExportChartAccountsKey(string searchText)
        {
            if (string.IsNullOrEmpty(searchText)) searchText = "";

            searchParam param = new searchParam();
            param.searchText = searchText;

            string key_ = System.Guid.NewGuid().ToString();
            HttpRuntime.Cache.Insert(key_, param, null, DateTime.UtcNow.Add(new TimeSpan(1, 0, 1)), System.Web.Caching.Cache.NoSlidingExpiration);

            return key_;
        }

        [HttpGet, Route("crm/api/ChartAccountSettings/ExportChartAccounts")]
        [apiAuthorize(AccessElement = @"Compliance\Settings", AccessLevel = "v")]
        public string ExportChartAccounts(string key)
        {
            searchParam _param = (searchParam)HttpRuntime.Cache[key];
            string sToday = String.Format("{0:yyyyMMMdd__hh_mm_ss tt}", DateTime.Now);
            string moduleName = "ChartAccounts";
            string fileName = moduleName + "_" + System.Guid.NewGuid() + "_" + _userSession.UserName + "_" + sToday;

            DataSet ds = null;

            if (string.IsNullOrEmpty(_param.searchText))
            {
                ds = _reportService.get_dataset_w_sql__single(_sql_exportChartAccounts + "order by [lkCHARTACCT].[DESCRIP] asc", "ChartAccounts");
            }
            else
            {
                _param.searchText = Library.util.kill_sqlBlacklistWord(_param.searchText);

                string _where = "";

                if (!string.IsNullOrEmpty(_param.searchText))
                {
                    #region [ Split Search Text > string1, string2 & string3 and create "Where" statement ]
                    char[] delimiterChars = { ' ', ',', '.', ';', ':', '|', '\t' };

                    string[] words = _param.searchText.Split(delimiterChars);

                    if (words.Count() > 0)
                    {
                        _where = string.Format("[lkCHARTACCT].[DESCRIP] LIKE '%{0}%' OR ACCTCODE LIKE '%{0}%' OR [lkCHARTACCTTYPE].[DESCRIP] LIKE '%{0}%'", words[0]);
                        for (int i = 1; i < words.Length; i++)
                        {
                            _where = _where + string.Format("OR [lkCHARTACCT].[DESCRIP] LIKE '%{0}%' OR ACCTCODE LIKE '%{0}%' OR [lkCHARTACCTTYPE].[DESCRIP] LIKE '%{0}%'", words[i]);
                        }
                    }
                    #endregion
                }
                //We need SQL with Where condition
                string sql = _sql_exportChartAccounts + " Where {0}";
                //Compose the SQL
                sql = String.Format(sql, _where) + "order by [lkCHARTACCT].[DESCRIP] asc";

                ds = _reportService.get_dataset_w_sql__single(sql, "ChartAccounts");

            }
            if (ds != null)
            {
                //HttpResponse _resp = CreateExcelFile.CreateExcelDocument(_dt.Copy(), fileName, HttpContext.Current.Response);
                string key_ = System.Guid.NewGuid().ToString();
                HttpRuntime.Cache.Insert(key_, ds, null, DateTime.UtcNow.Add(new TimeSpan(1, 0, 1)), System.Web.Caching.Cache.NoSlidingExpiration);
                //return the Key
                return key_;
            }
            else
            {
                return null;
            }
        }

        [HttpGet, Route("crm/api/ChartAccountSettings/GetChartAccounts")]
        [apiAuthorize(AccessElement = @"Compliance\Settings", AccessLevel = "v")]
        public genericResponse GetChartAccounts(string searchText)
        {
            string _sql = @"SELECT 
	                       [CHARTACCTID], 
	                       [lkCHARTACCT].[DESCRIP] AS [DESCRIP], 
	                       [ACCTCODE], 
	                       [lkCHARTACCT].[ACCTTYPEID], 
	                       [lkCHARTACCTTYPE].[DESCRIP] AS [TYPEDesc]
	                       FROM  [dbo].[lkCHARTACCT]
	                       LEFT OUTER JOIN [dbo].[lkCHARTACCTTYPE] ON [lkCHARTACCT].[ACCTTYPEID] = [lkCHARTACCTTYPE].[ACCTTYPEID]";

            //order by
            string _sortOption = "[lkCHARTACCT].[DESCRIP] ASC";

            //where
            string _where = "";
            if (!string.IsNullOrEmpty(searchText))
            {
                searchText = Library.util.kill_sqlBlacklistWord(searchText);


                if (!string.IsNullOrEmpty(searchText))
                {
                    #region [ Split Search Text > string1, string2 & string3 and create "Where" statement ]
                    char[] delimiterChars = { ' ', ',', '.', ';', ':', '|', '\t' };

                    string[] words = searchText.Split(delimiterChars);

                    if (words.Count() > 0)
                    {
                        _where = string.Format("[lkCHARTACCT].[DESCRIP] LIKE '%{0}%' OR ACCTCODE LIKE '%{0}%' OR [lkCHARTACCTTYPE].[DESCRIP] LIKE '%{0}%'", words[0]);
                        for (int i = 1; i < words.Length; i++)
                        {
                            _where = _where + string.Format("OR [lkCHARTACCT].[DESCRIP] LIKE '%{0}%' OR ACCTCODE LIKE '%{0}%' OR [lkCHARTACCTTYPE].[DESCRIP] LIKE '%{0}%'", words[i]);
                        }
                    }
                    #endregion
                }
                _sql = _sql + " where " + _where;
            }
            _sql = _sql + " order by " + _sortOption;

            try
            {
                List<lkChartAcct_ext> _list = _entity_crm.getContext().Database.SqlQuery<lkChartAcct_ext>(_sql).ToList();

                return new genericResponse() { success = true, __count = _list.Count, results = _list.ToList<iItemType>() };
            }
            catch
            {
                return new genericResponse() { success = false, __count = 0 };
            }
        }

        [HttpGet, Route("crm/api/ChartAccountSettings/CATypes")]
        [apiAuthorize(AccessElement = @"Compliance\Settings", AccessLevel = "v")]
        public List<lkCHARTACCTTYPE> CATypes()
        {
            List<lkCHARTACCTTYPE> all = _entity_crm.All<lkCHARTACCTTYPE>().Where(lkc => lkc.ACCTTYPEID > 0).OrderBy(lkc => lkc.DESCRIP).ToList();

            lkCHARTACCTTYPE _firstItem = new lkCHARTACCTTYPE();
            _firstItem.ACCTTYPEID = -1;
            _firstItem.DESCRIP = "-- Please Select --";
            all.Insert(0, _firstItem);
            return all;
        }

        [HttpGet, Route("crm/api/ChartAccountSettings/getChartAccount/{_CHARTACCTID}")]
        [apiAuthorize(AccessElement = @"Compliance\Settings", AccessLevel = "v")]
        public lkChartAcct getChartAccount(Int16 _CHARTACCTID)
        {
            if (_CHARTACCTID <= 0)//meaning new exception code
                return new lkChartAcct { ACCTTYPEID = -1, CHARTACCTID = -1 };

            return _entity_crm.All<lkChartAcct>().Where(lke => lke.CHARTACCTID == _CHARTACCTID).FirstOrDefault();
        }

        [HttpPost, Route("crm/api/ChartAccountSettings/saveChartAccount")]
        [apiAuthorize(AccessElement = @"Compliance\Settings", AccessLevel = "a")]
        public genericResponse saveChartAccount(lkChartAcct lkc)
        {
            Int16 _CHARTACCTID = lkc.CHARTACCTID;
            lkChartAcct _record = null;
            //check CODE uniqueness
            _record = _entity_crm.Single<lkChartAcct>(a => a.ACCTCODE == lkc.ACCTCODE);

            if (_record != null && _record.CHARTACCTID != _CHARTACCTID)
                return new genericResponse()
                {
                    success = false,
                    message = "CODE: " + lkc.ACCTCODE.Trim() + " already exists. Please enter unique CODE and try again."
                };
            //check NAME uniqueness
            _record = _entity_crm.Single<lkChartAcct>(a => a.DESCRIP.Trim() == lkc.DESCRIP.Trim());

            if (_record != null && _record.CHARTACCTID != _CHARTACCTID)
                return new genericResponse()
                {
                    success = false,
                    message = "Name: " + lkc.DESCRIP.Trim() + " already exists. Please enter unique NAME and try again."
                };

            //check if update or add code
            _record = _entity_crm.Single<lkChartAcct>(a => a.CHARTACCTID == lkc.CHARTACCTID);
            try
            {
                // existing record
                if (_record != null && _CHARTACCTID >= 0)
                {
                    _record.DESCRIP = lkc.DESCRIP;
                    _record.ACCTCODE = lkc.ACCTCODE;
                    _record.ACCTTYPEID = lkc.ACCTTYPEID;

                    _entity_crm.Update(_record);
                }
                else// new record
                {
                    _record = new lkChartAcct();
                    _record.DESCRIP = lkc.DESCRIP;
                    _record.ACCTCODE = lkc.ACCTCODE;
                    _record.ACCTTYPEID = lkc.ACCTTYPEID;

                    _entity_crm.Add(_record);
                }

                _entity_crm.CommitChanges();
                return new genericResponse() { success = true, message = "Chart Account was saved successfully saved" };
            }
            catch (Exception ex)
            {
                return new genericResponse() { success = false, message = "Chart Account was NOT saved. Try again later" };
            }

        }

        [HttpPost, Route("crm/api/ChartAccountSettings/DeleteChartAccount/{_CHARTACCTID}")]
        [apiAuthorize(AccessElement = @"Compliance\Settings", AccessLevel = "d")]
        public genericResponse DeleteChartAccount(int _CHARTACCTID)
        {
            try
            {
                _entity_crm.Delete<lkChartAcct>(a => a.CHARTACCTID == _CHARTACCTID);
                _entity_crm.CommitChanges();

                genericResponse _response = new genericResponse() { success = true, message = "Exception Code deleted successfully" };
                return _response;
            }
            catch
            {
                genericResponse _response = new genericResponse() { success = false, message = "Error occurred while deleting code. Please try again later." };
                return _response;
            }
        }
        #endregion
    }
}
