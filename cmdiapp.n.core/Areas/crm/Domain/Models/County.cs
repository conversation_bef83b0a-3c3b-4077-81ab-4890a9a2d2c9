using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Data;
using System.Linq;
using System.Web.Mvc;
using System.Data.Linq;
using System.ComponentModel.DataAnnotations.Schema;

using cmdiapp.n.core.Domain.ViewModels;
using cmdiapp.n.core.Areas.crm.Domain.Data;

namespace cmdiapp.n.core.Areas.crm.Domain.Models
{
    [Table("lkCOUNTY")]
    public class County : _entityBase_crm, iItemType
    {
        [Column]
        //[Key]
        public int COUNTYID { get; set; }

        [Key, Column(Order=0)]
        public string STATE { get; set; }

        [Key, Column(Order=1)]
        public string FIPS { get; set; }

        [Column]
        public string COUNTY { get; set; }

        [Column]
        public Nullable<int> CSACode { get; set; }

        //public virtual ICollection<Address> Addresses { get; set; }
    }
}
