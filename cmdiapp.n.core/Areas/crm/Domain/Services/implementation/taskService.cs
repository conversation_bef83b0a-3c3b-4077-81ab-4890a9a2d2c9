﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

using Ninject;
using Ninject.Web.Mvc;

using cmdiapp.n.core.Core;
using cmdiapp.n.core.Domain.Models;
using cmdiapp.n.core.Core.App_Start;
using cmdiapp.n.core.Areas.crm.Core;
using cmdiapp.n.core.Areas.crm.Domain.Models;
using cmdiapp.n.core.Areas.crm.Domain.ViewModels;
using cmdiapp.n.core.Areas.crm.Domain.Repositories;

using System.Reflection.Emit;
using System.Reflection;
using cmdiapp.n.core.Areas.crm.Domain.Data;

namespace cmdiapp.n.core.Areas.crm.Domain.Services
{
    public class taskService : ItaskService
    {
        private I___unitOfWork _unitOfWork;
        private I__DBFactory _dbFactory;
        private I_entity_crm _entity_crm;

        public taskService(I___unitOfWork unitOfWork, I__DBFactory dbFactory, I_entity_crm entity_crm)
        {
            _unitOfWork = unitOfWork;
            _dbFactory = dbFactory;
            _entity_crm = entity_crm;
        }
               
        public List<TaskSearchResult_ext1> get_allTasks(string where, string orderBy, int pageSize, int pageNo)
        {
            var q = _dbFactory.getContext().Database.SqlQuery<TaskSearchResult_ext1>(String.Format("EXEC dbo.get_pagedDataSet '{0}', '{1}', {2}, {3}", where, orderBy, pageSize, pageNo));
            return q.ToList();
        }

        public List<UserTasks_ext1> get_MyTasks(string where, string orderBy, int pageSize, int pageNo)
        {
            var q = _dbFactory.getContext().Database.SqlQuery<UserTasks_ext1>(String.Format("EXEC dbo.get_pagedDataSet '{0}', '{1}', {2}, {3}", where, orderBy, pageSize, pageNo));
            return q.ToList();
        }

        public List<UserTasks> get_allMyTasks(string where)
        {
            var q = _dbFactory.getContext().Database.SqlQuery<UserTasks>(String.Format("EXEC dbo.get_allDataSet '{0}'", where));

            return q.ToList();
        }

        public int reassign_TaskItems(string _sql)
        {
            var q = _dbFactory.getContext().Database.SqlQuery<Int32>(_sql);
            return q.FirstOrDefault();
        }
        
        public int completeMassTasks(string _sql)
        {
            var q = _dbFactory.getContext().Database.SqlQuery<Int32>(_sql);
            return q.FirstOrDefault();
        }
        
        public string get_GroupDescription(short? userAppUID)
        {
            //For Group Description
            string sql = string.Format("SELECT ISNULL(UGROUPDESC ,'') AS GROUPDESC FROM V_User WHERE UID = {0}", userAppUID);
            var q = _dbFactory.getContext().Database.SqlQuery<UserGroupDescription>(sql);
            return q.FirstOrDefault().GROUPDESC;
        }

    }
}