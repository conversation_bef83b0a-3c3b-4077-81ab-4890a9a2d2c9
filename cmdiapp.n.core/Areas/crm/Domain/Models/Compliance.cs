﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Data;
using System.Linq;
using System.Web.Mvc;
using System.Data.Linq;
using cmdiapp.n.core.Domain.ViewModels;
using cmdiapp.n.core.Areas.crm.Domain.Data;
using System.ComponentModel.DataAnnotations.Schema;

namespace cmdiapp.n.core.Areas.crm.Domain.Models
{
    public class compliancebesteffortdata : iItemType
    {
        public string type { get; set; }
        public int cnt { get; set; }
    }

    public class complianceoverlimitdata : iItemType
    {
        public int pid { get; set; }
        public string name { get; set; }
        public decimal cumtot { get; set; }
    }

    public class pacswithnofecid : iItemType
    {
        public int PID { get; set; }
        public string NAME { get; set; }
        public string STREET { get; set; }
        public string CITY { get; set; }
        public string STATE { get; set; }
        public string RECENTGIFTDATE { get; set; }
        public decimal? CTDAMT { get; set; }
        public decimal? YTDAMT { get; set; }
    }

    public class irregularname : iItemType
    {
        public int PID { get; set; }
        public string PREFIX { get; set; }
        public string FNAME { get; set; }
        public string MNAME { get; set; }
        public string LNAME { get; set; }
        public string REASON { get; set; }
        public string BAD_FIELD { get; set; }
    }

    public class duplicateindividual : iItemType
    {
        public string MATCHTYPE { get; set; }

        public int PID1 { get; set; }
        public string PREFIX1 { get; set; }
        public string FNAME1 { get; set; }
        public string MNAME1 { get; set; }
        public string LNAME1 { get; set; }
        public string SUFFIX1 { get; set; }
        public string EMPLOYER1 { get; set; }
        public string OCCUPATION1 { get; set; }
        public string SPOUSE1 { get; set; }
        public string STREET1 { get; set; }
        public string ADDR1 { get; set; }
        public string CITY1 { get; set; }
        public string STATE1 { get; set; }
        public string ZIP1 { get; set; }
        public string HOMEPHONE1 { get; set; }
        public string EMAIL1 { get; set; }
        public string TITLE1 { get; set; }
        public decimal? CTDAMT1 { get; set; }
        public DateTime? MRCDATE1 { get; set; }

        public int PID2 { get; set; }
        public string PREFIX2 { get; set; }
        public string FNAME2 { get; set; }
        public string MNAME2 { get; set; }
        public string LNAME2 { get; set; }
        public string SUFFIX2 { get; set; }
        public string EMPLOYER2 { get; set; }
        public string OCCUPATION2 { get; set; }
        public string SPOUSE2 { get; set; }
        public string STREET2 { get; set; }
        public string ADDR2 { get; set; }
        public string CITY2 { get; set; }
        public string STATE2 { get; set; }
        public string ZIP2 { get; set; }
        public string HOMEPHONE2 { get; set; }
        public string EMAIL2 { get; set; }
        public string TITLE2 { get; set; }
        public decimal? CTDAMT2 { get; set; }
        public DateTime? MRCDATE2 { get; set; }
    }

    public class duplicatenonindividual : iItemType
    {
        public int ROWNUMBER { get; set; }
        public string METHOD { get; set; }
        
        public string SEARCHTEXT1 { get; set; }
        public int PID1 { get; set; }
        public string FECCMTEID1 { get; set; }
        public string PREFIX1 { get; set; }
        public string FNAME1 { get; set; }
        public string MNAME1 { get; set; }
        public string LNAME1 { get; set; }
        public string cPREFIX1 { get; set; }
        public string cFNAME1 { get; set; }
        public string cMNAME1 { get; set; }
        public string cLNAME1 { get; set; }
        public string cTITLE1 { get; set; }
        public string STREET1 { get; set; }
        public string ADDR1 { get; set; }
        public string CITY1 { get; set; }
        public string STATE1 { get; set; }
        public string ZIP1 { get; set; }
        public string PLUS41 { get; set; }
        
        public string SEARCHTEXT2 { get; set; }
        public int PID2 { get; set; }
        public string FECCMTEID2 { get; set; }
        public string PREFIX2 { get; set; }
        public string FNAME2 { get; set; }
        public string MNAME2 { get; set; }
        public string LNAME2 { get; set; }
        public string cPREFIX2 { get; set; }
        public string cFNAME2 { get; set; }
        public string cMNAME2 { get; set; }
        public string cLNAME2 { get; set; }
        public string cTITLE2 { get; set; }
        public string STREET2 { get; set; }
        public string ADDR2 { get; set; }
        public string CITY2 { get; set; }
        public string STAT2 { get; set; }
        public string ZIP2 { get; set; }
        public string PLUS42 { get; set; }
    }

    public class llcnoattribution : iItemType
    {
        public int PID { get; set; }
        public string NAME { get; set; }
        public string STREET { get; set; }
        public string ADDR1 { get; set; }
        public string ADDR2 { get; set; }
        public string CITY { get; set; }
        public string STATE { get; set; }
        public string ZIP { get; set; }
        public string FECID { get; set; }
        public string BSPHN { get; set; }
        public string EMAIL { get; set; }
        public string RECENTGIFTDATE { get; set; }
        public decimal? CTDAMT { get; set; }
        public decimal? YTDAMT { get; set; }
        public string CON_FNAME { get; set; }
        public string CON_LNAME { get; set; }
        public string CON_EMAIL { get; set; }
    }
    
    public class NoNeedAttention : iItemType
    {
        public string TYPE { get; set; }
        public int ID { get; set; }
        public DateTime? CREATEDON { get; set; }
    }

    public class z_compl_EmpOccu : iItemType
    {
        public int PID { get; set; }
        public string  Name { get; set; }
        public string Employer { get; set; }
        public string Occupation { get; set; }
        public int sProjectId { get; set; }
        public string sDatabaseName { get; set; }
        public int sPID { get; set; }
        public string sName { get; set; }
        public string sCityState { get; set; }
        public string sEmployer { get; set; }
        public string sOccupation { get; set; }
        public DateTime sLGiftDte { get; set; }
        public DateTime createdAt { get; set; }
        public bool selected { get; set; }
        public int totalCount { get; set; }
    }
}