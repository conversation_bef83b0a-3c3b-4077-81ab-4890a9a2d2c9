﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

using Ninject;
using Ninject.Web.Mvc;

using cmdiapp.n.core.Core;
using cmdiapp.n.core.Domain.Models;
using cmdiapp.n.core.Core.App_Start;
using cmdiapp.n.core.Areas.crm.Core;
using cmdiapp.n.core.Areas.crm.Domain.Models;
using cmdiapp.n.core.Areas.crm.Domain.ViewModels;
using cmdiapp.n.core.Areas.crm.Domain.Repositories;
using cmdiapp.n.core.Areas.query.Domain.Models;

using System.Reflection.Emit;
using System.Reflection;
using cmdiapp.n.core.Areas.crm.Domain.Data;
using System.Text;
using System.Threading.Tasks;
using System.Data.SqlClient;
using System.Data;
using System.Data.Entity.SqlServer;
using System.Data.Entity.Infrastructure;

namespace cmdiapp.n.core.Areas.crm.Domain.Services
{
    public class eventService : IeventService
    {
        private I___unitOfWork _unitOfWork;
        private I__DBFactory _dbFactory;
        private I_entity_crm _entity_crm;

        public eventService(I___unitOfWork unitOfWork, I__DBFactory dbFactory, I_entity_crm entity_crm)
        {
            _unitOfWork = unitOfWork;
            _dbFactory = dbFactory;
            _entity_crm = entity_crm;
        }

        #region [[ For Events ]]

        public List<Event_ext1> get_events(string where, string orderBy, int pageSize, int pageNo)
        {
            //string sql = (!string.IsNullOrEmpty(where) ? string.Format("SELECT P.SPCEVNTID,P.EVNTCODE,P.EVNTDESC,P.STARTDTE,P.ENDDTE, (SELECT COUNT(*) FROM SOURCE S WHERE S.SPCEVNTID = P.SPCEVNTID) as EventCount FROM pmSPCEVNT P WHERE {0}", where) : "SELECT P.SPCEVNTID,P.EVNTCODE,P.EVNTDESC,P.STARTDTE,P.ENDDTE, (SELECT COUNT(*) FROM SOURCE S WHERE S.SPCEVNTID = P.SPCEVNTID) as EventCount FROM pmSPCEVNT P");
            string sql = (!string.IsNullOrEmpty(where) ? string.Format("SELECT P.SPCEVNTID,P.EVNTCODE,P.EVNTDESC,P.STARTDTE,P.ENDDTE, (SELECT COUNT(*) FROM SOURCE S WHERE UPPER(S.SRCECODE) = UPPER(P.EVNTCODE)) as EventCount FROM pmSPCEVNT P WHERE {0}", where) : "SELECT P.SPCEVNTID,P.EVNTCODE,P.EVNTDESC,P.STARTDTE,P.ENDDTE, (SELECT COUNT(*) FROM SOURCE S WHERE S.SPCEVNTID = P.SPCEVNTID) as EventCount FROM pmSPCEVNT P");
            
            var q = _dbFactory.getContext().Database.SqlQuery<Event_ext1>(String.Format("EXEC dbo.get_pagedDataSet '{0}', '{1}', {2}, {3}", sql, orderBy, pageSize, pageNo));

            return q.ToList();

        }

        public List<Event> get_allevents(string where)
        {

            where = !string.IsNullOrEmpty(where) ? "STARTDTE >= GetDate() AND STARTDTE IS NOT NULL ORDER BY SPCEVNTID DESC" : where;
            string sql = (!string.IsNullOrEmpty(where) ? string.Format("SELECT SPCEVNTID,(EVNTCODE + '' - '' + EVNTDESC) AS EVNTFULL, EVNTCODE,EVNTDESC,CONVERT(DATETIME,CONVERT(varchar(10),STARTDTE,101)+'' ''+ISNULL(convert(varchar(5),EVNTTIME,108),''00:00'')) AS STARTDTE, Case when ENDDTE IS not null then CONVERT(DATETIME,CONVERT(varchar(10),ENDDTE,101)+'' '' +ISNULL(convert(varchar(5),ENDTIME,108),''00:00'')) else CONVERT(DATETIME,CONVERT(varchar(10),STARTDTE,101)+'' '' +ISNULL(convert(varchar(5),ENDTIME,108),''00:00'')) End  ENDDTE  FROM pmSPCEVNT WHERE {0}", where) : "SELECT SPCEVNTID,(EVNTCODE + '' - '' + EVNTDESC) AS EVNTFULL,EVNTCODE,EVNTDESC,CONVERT(DATETIME,CONVERT(varchar(10),STARTDTE,101)+'' ''+ISNULL(convert(varchar(5),EVNTTIME,108),''00:00'')) AS STARTDTE, Case when ENDDTE IS not null then CONVERT(DATETIME,CONVERT(varchar(10),ENDDTE,101)+'' '' +ISNULL(convert(varchar(5),ENDTIME,108),''00:00'')) else CONVERT(DATETIME,CONVERT(varchar(10),STARTDTE,101)+'' '' +ISNULL(convert(varchar(5),ENDTIME,108),''00:00'')) End  ENDDTE  FROM pmSPCEVNT WHERE STARTDTE IS NOT NULL ORDER BY SPCEVNTID DESC");
            
            //string sql = "SELECT SPCEVNTID,EVNTCODE,EVNTDESC,CONVERT(DATETIME,CONVERT(varchar(10),STARTDTE,101)+'' ''+ISNULL(convert(varchar(5),EVNTTIME,108),''00:00'')) AS STARTDTE, Case when ENDDTE IS not null then CONVERT(DATETIME,CONVERT(varchar(10),ENDDTE,101)+'' '' +ISNULL(convert(varchar(5),ENDTIME,108),''00:00'')) else CONVERT(DATETIME,CONVERT(varchar(10),STARTDTE,101)+'' '' +ISNULL(convert(varchar(5),ENDTIME,108),''00:00'')) End  ENDDTE  FROM pmSPCEVNT WHERE EVNTTIME IS NOT NULL AND STARTDTE IS NOT NULL ORDER BY SPCEVNTID DESC";
            
            var q = _dbFactory.getContext().Database.SqlQuery<Event>(String.Format("EXEC dbo.get_allDataSet '{0}'", sql));

            return q.ToList();
        }

        public lkEVNTSTATUS get_EventStatus(int status)
        {
            string sql = string.Format("SELECT * FROM lkEVNTSTATUS WHERE STATUS={0}", status);
            var q = _dbFactory.getContext().Database.SqlQuery<lkEVNTSTATUS>(sql);
            return q.FirstOrDefault();
        }

        public bool check_record_exists(int status)
        {
            //Check in jtSPCEVNT Table
            string sql = string.Format("SELECT Count(*) as recordCount FROM jtSPCEVNT WHERE STATUS={0}", status);
            var q = _dbFactory.getContext().Database.SqlQuery<UniquenessCheck>(sql);
            int isZero = q.FirstOrDefault().recordCount;
            return (isZero > 0) ? true : false;

        }

        public bool IsFundCodeRequired (int evntId)
        {
            //Check in jtSPCEVNT Table
            string sql = string.Format("SELECT Count(*) as recordCount FROM pmSPCEVNT E WHERE E.SPCEVNTID = {0} AND E.SPCEVNTID IN (SELECT SPCEVNTID FROM x_eb_event) AND ISNULL(E.FUNDID,0) = 0", evntId);
            var q = _dbFactory.getContext().Database.SqlQuery<UniquenessCheck>(sql);
            int isZero = q.FirstOrDefault().recordCount;
            return (isZero > 0) ? true : false;
        }

        public bool IsSourceMsgRequired(int evntId)
        {
            //Check in jtSPCEVNT Table
            string sql = string.Format("SELECT Count(*) as recordCount FROM pmSPCEVNT E WHERE E.SPCEVNTID = {0} AND E.SPCEVNTID IN (SELECT SPCEVNTID FROM x_eb_event) AND ((SELECT COUNT(*) FROM SOURCE WHERE SRCECODE=E.EVNTCODE OR ISNULL(SPCEVNTID,0)=E.SPCEVNTID) = 0)", evntId);
            var q = _dbFactory.getContext().Database.SqlQuery<UniquenessCheck>(sql);
            int isZero = q.FirstOrDefault().recordCount;
            return (isZero > 0) ? true : false;
        }
        
        public bool IsLinkExists(int evntId)
        {
            //Check in jtSPCEVNT Table
            string sql = string.Format("SELECT Count(*) as recordCount FROM pmSPCEVNT E WHERE E.SPCEVNTID = {0} AND E.SPCEVNTID IN (SELECT SPCEVNTID FROM x_eb_event)", evntId);
            var q = _dbFactory.getContext().Database.SqlQuery<UniquenessCheck>(sql);
            int isZero = q.FirstOrDefault().recordCount;
            return (isZero > 0) ? true : false;
        }
        
        public int AddEventStatus(lkEVNTSTATUS _lkEVNTSTATUS)
        {
            _entity_crm.Add(_lkEVNTSTATUS);
            _entity_crm.CommitChanges();
            return _lkEVNTSTATUS.STATUS;
        }

        public int UpdateEventStatus(lkEVNTSTATUS _lkEVNTSTATUS)
        {
            _entity_crm.Update(_lkEVNTSTATUS);
            _entity_crm.CommitChanges();
            return _lkEVNTSTATUS.STATUS;
        }

        public int DeleteEventStatus(string _sql)
        {
            var q = _dbFactory.getContext().Database.SqlQuery<Int32>(_sql);
            return q.FirstOrDefault();
        }
                
        //public void DeleteEventStatus(lkEVNTSTATUS _lkEVNTSTATUS)
        //{
        //     _entity_crm.Delete(_lkEVNTSTATUS);
        //    _entity_crm.CommitChanges();

        //}
        
        public bool check_unqiueness(lkEVNTSTATUS _lkEVNTSTATUS)
        {
            // take care of apostrophe
            string v = _lkEVNTSTATUS.DESCRIP.Replace("'", "''");
            string sql = "";

            if (_lkEVNTSTATUS.STATUS > 0)   // Existing Record
                sql = String.Format("SELECT Count(*) as recordCount FROM lkEVNTSTATUS WHERE DESCRIP = '{0}' AND STATUS <> {1}", v, _lkEVNTSTATUS.STATUS);
            else
                sql = String.Format("SELECT Count(*) as recordCount FROM lkEVNTSTATUS WHERE DESCRIP = '{0}'", v);

            int isZero = _dbFactory.getContext().Database.SqlQuery<int>(sql).FirstOrDefault();
            return (isZero == 0) ? true : false;
        }
        
        public List<lkEVNTSTATUS_ext> get_all_EventStatus(string where, string orderBy, int pageSize, int pageNo)
        {
            string sql = (!string.IsNullOrEmpty(where) ? string.Format("SELECT STATUS,DESCRIP FROM lkEVNTSTATUS WHERE {0}", where) : "SELECT STATUS,DESCRIP FROM lkEVNTSTATUS");

            var q = _dbFactory.getContext().Database.SqlQuery<lkEVNTSTATUS_ext>(String.Format("EXEC dbo.get_pagedDataSet '{0}', '{1}', {2}, {3}", sql, orderBy, pageSize, pageNo));

            return q.ToList();

        }
        
        public EventDoc getEventdoc(int eventDocId)
        {
            string sql = string.Format("SELECT SPCEVNTDOCID ,SPCEVNTID ,FILENAME ,CONTENT FROM pmSPCEVNTDOC WHERE SPCEVNTDOCID = {0}", eventDocId);
            var q = _dbFactory.getContext().Database.SqlQuery<EventDoc>(sql);
            return q.FirstOrDefault();
        }

        public List<v_people_1_ext1> get_all_PeopleMatchingRecords(string sql, string orderBy, int pageSize, int pageNo)
        {
            string final_sql = String.Format("EXEC dbo.get_pagedDataSet '{0}', '{1}', {2}, {3}", sql, orderBy, pageSize, pageNo);
            var q = _dbFactory.getContext().Database.SqlQuery<v_people_1_ext1>(final_sql);

            return q.ToList();
        }

        public EventInviteeGuestDisplay GetInviteeGuestDisplay(int jtSpcevntId)
        {
            // get event id
            int eventId = _entity_crm.Single<jtSPCEVNT>(i => i.jtSPCEVNTID == jtSpcevntId)?.SPCEVNTID ?? 0;
            if (eventId == 0)
            {
                return null;
            }
            return GetInviteesAndGuestsRawQuery(eventId)
                .Where(invitee => invitee.jtSPCEVNTID == jtSpcevntId)
                .FirstOrDefault();
        }

        public object InviteeExport(int eventId,
            string searchText,
            string status = "",
            string type = "",
            string checkedIn = "",
            int CTD = 0)
        {
            DataTable table = InviteeExportTable(eventId, searchText, status, type, checkedIn, CTD);
            if (table != null && table.Rows.Count > 0)
            {
                Tuple<string, string> filenames = new QExportExcel().ToFile(table, $"Event{eventId}_Invitees");
                return new
                {
                    success = true,
                    serverFileName = filenames.Item1,
                    clientFileName = filenames.Item2
                };
            }
            else
            {
                return new
                {
                    success = false,
                    message = "The query returned no results."
                };
            }
        }
        public DataTable InviteeExportTable(int eventId,
            string searchText,
            string status = "",
            string type = "",
            string checkedIn = "",
            int CTD = 0)
        {
            // get invitees
            Tuple<int, List<EventInviteeGuestDisplay>> invTuple = GetInviteesAndGuests(
                eventId, searchText, 1, 100000, null, "", status, type, checkedIn, CTD);

            // join invitees with tickets
            var q = from invitee in invTuple.Item2
                    join ticket in _entity_crm.All<jtINVITEETICKET>()
                        .Join(_entity_crm.All<SPCEVNTTICKET>(),
                            i => i.SPCEVNTTICKETID,
                            e => e.SPCEVNTTICKETID,
                            (inv, evt) => new { inv, evt }
                        )
                        on invitee.jtSPCEVNTID equals ticket.inv.jtSPCEVNTID into tickGrp
                    select new
                    {
                        invitee.PID,
                        invitee.GUESTID,
                        invitee.CHECKEDIN,
                        RSVP_ON = invitee.RSVP,
                        invitee.STATUS,
                        invitee.TYPE,
                        invitee.PREFIX,
                        invitee.FNAME,
                        invitee.MNAME,
                        invitee.LNAME,
                        invitee.SUFFIX,
                        invitee.SALUTATION,
                        invitee.MAILNAME,
                        MAILSALUTATION = invitee.PRESSALUT,
                        invitee.STREET,
                        invitee.ADDR1,
                        invitee.ADDR2,
                        invitee.CITY,
                        invitee.STATE,
                        invitee.ZIP,
                        invitee.PLUS4,
                        invitee.HMPHN,
                        invitee.BSPHN,
                        invitee.CELL,
                        invitee.EMAIL,
                        invitee.EMPLOYER,
                        invitee.OCCUPATION,
                        invitee.ASSISTANT,
                        invitee.ASSTPHONE,
                        invitee.ASSTEMAIL,
                        invitee.DONATED,
                        invitee.PLEDGED,
                        invitee.OUTSTANDINGPLEDGE,
                        NUM_OF_TICKETS = tickGrp.Sum(t => t.inv.QUANTITY),
                        TICKETS = string.Join(", ",
                            tickGrp.Select(grp =>
                                $"{grp.inv.QUANTITY} {grp.evt.DESCRIP} @ {grp.evt.PARTICLEVEL:C} each")),
                        invitee.CTD,
                        FTD = invitee.CTD                        
                    };

            DataTable table = q.ToList().ToDataTable();
            if (table != null && table.Rows.Count > 0)
            {
                //get app version
                bool nonProfit = session.currentDomain_project.projectType.appVersion.ToLower() == "nonprofit";
                if (nonProfit)
                {
                    table.Columns.Remove("CTD");
                }
                else
                {
                    table.Columns.Remove("FTD");
                }
                return table;
            }
            else
            {
                return null;
            }
        }

        private DbRawSqlQuery<EventInviteeGuestDisplay> GetInviteesAndGuestsRawQuery(int eventId)
        {
            string sql = $@"SELECT
                            E.jtSPCEVNTID,
                            E.PID,
                            E.GUESTID,
                            E.RSVP,
                            E.UPDATEDON,
                            E.[STATUS],
                            E.[TYPE],
                            E.PREFIX,
                            E.FNAME,
                            E.MNAME,
                            E.LNAME,
                            E.SUFFIX,
		                    E.SALUTATION,
		                    E.MAILNAME,
                            E.STREET,
		                    E.ADDR1,
		                    E.ADDR2,
		                    E.CITY,
		                    E.[STATE],
		                    E.ZIP,
		                    E.PLUS4,
                            E.HMPHN,
                            E.BSPHN,
                            E.CELL,
                            E.EMAIL,
                            E.EMPLOYER,
                            E.OCCUPATION,
                            E.DONATED,
                            E.PLEDGED,
                            E.CTD,
                            E.OUTSTANDINGPLEDGE,
                            E.CHECKEDIN,
                            P.ASSISTANT,P.ASSTPHONE,P.ASSTEMAIL,P.PRESSALUT
                            FROM dbo.fn_eventInviteesAndGuests({QzLib.sanitize(eventId.ToString())}) E
                            LEFT OUTER JOIN v_people_rec P ON E.PID=P.PID
";
            return _entity_crm.getContext().Database.SqlQuery<EventInviteeGuestDisplay>(sql);
        }

        // returns <totalCount, list>
        public Tuple<int, List<EventInviteeGuestDisplay>> GetInviteesAndGuests(
            int eventId, 
            string searchText, 
            int page, 
            int pageSize, 
            QueryRuntimeSort sort,
            string searchOn="",
            string status="", 
            string type="", 
            string checkedIn="",
            int CTD=0)
        {
             
            var q = GetInviteesAndGuestsRawQuery(eventId)
                .Where(inv => InviteesAndGuestSearchCondition(inv, searchText, searchOn)
                            && InviteesAndGuestFilterCondition(inv, status, type, checkedIn, CTD));
                        
            int count = q.Count();
            string sortDir = sort?.dir ?? "asc";            
            string sortField = sort?.field ?? "LNAME";
            // verify that sortfield is valid property
            if (new EventInviteeGuestDisplay().GetType().GetProperty(sortField) == null)
            {
                sortField = "LNAME";
            }
            List<EventInviteeGuestDisplay> list;
            var propertyInfo = typeof(EventInviteeGuestDisplay).GetProperty(sortField);
            if (sortDir == "asc")
            {
                list = q.OrderBy(inv => propertyInfo.GetValue(inv, null))
                            .Skip((page - 1) * pageSize)
                            .Take(pageSize)
                            .ToList();
            }
            else
            {
                list = q.OrderByDescending(inv => propertyInfo.GetValue(inv, null))
                            .Skip((page - 1) * pageSize)
                            .Take(pageSize)
                            .ToList();
            }

            return new Tuple<int, List<EventInviteeGuestDisplay>>(count, list);            
        }

        private bool InviteesAndGuestSearchCondition(EventInviteeGuestDisplay invitee, string searchText, string searchOn)
        {
            if (string.IsNullOrEmpty(searchText))
            {
                return true;
            }

            string loweredSearchText = searchText?.ToLower() ?? null;            
            string[] splitSearchText = loweredSearchText?.Split(' ') ?? null;

            if (string.IsNullOrEmpty(searchOn) || searchOn.Contains("NAME"))
            {
                string[] names = new string[5] { invitee.PREFIX, invitee.FNAME, invitee.MNAME, invitee.LNAME, invitee.SUFFIX };
                if(splitSearchText.All(str => names.Any(n => n?.ToLower().Contains(str) ?? false)))
                {
                    return true;
                }
            }

            if (string.IsNullOrEmpty(searchOn) || searchOn.Contains("STATUS"))
            {
                if (invitee.STATUS.ToLower().Contains(loweredSearchText))
                {
                    return true;
                }
            }

            if (string.IsNullOrEmpty(searchOn) || searchOn.Contains("TYPE"))
            {
                if (invitee.TYPE.ToLower().Contains(loweredSearchText))
                {
                    return true;
                }
            }

            if (string.IsNullOrEmpty(searchOn) || searchOn.Contains("ADDR"))
            {
                if (splitSearchText.All(str => 
                    invitee.STREET.ToLower().Contains(str)
                    || invitee.ADDR1.ToLower().Contains(str)
                    || invitee.ADDR2.ToLower().Contains(str)
                    || invitee.CITY.ToLower().Contains(str)
                    || invitee.STATE.ToLower().Contains(str)
                    || invitee.ZIP.ToLower().Contains(str)
                    || invitee.PLUS4.ToLower().Contains(str)))
                {
                    return true;
                }
            }

            if (string.IsNullOrEmpty(searchOn) || searchOn.Contains("PID"))
            {
                if (invitee.PID.ToString().Contains(loweredSearchText))
                {
                    return true;
                }
            }

            return false;
        }

        private bool InviteesAndGuestFilterCondition(
            EventInviteeGuestDisplay invitee, 
            string status = "",
            string type = "",
            string checkedIn = "",
            int CTD=0)
        {
            bool checkedInBool = checkedIn == "1";
            // split multi-value parameters
            IEnumerable<string> statusSplit = status?.Split('|')?.Select(s => s.Trim()) ?? new List<string>();
            IEnumerable<string> typeSplit = type?.Split('|')?.Select(s => s.Trim()) ?? new List<string>();
            return (string.IsNullOrEmpty(status) || statusSplit.Contains(invitee.STATUS))
                && (string.IsNullOrEmpty(type) || typeSplit.Contains(invitee.TYPE))
                && (string.IsNullOrEmpty(checkedIn) || invitee.CHECKEDIN == checkedInBool)
                && (invitee.CTD >= CTD);
        }

        public string ChangeInviteeCheckIn(int jtSPCEVNTID, bool checkedIn)
        {
            jtSPCEVNT invitee = _entity_crm.Single<jtSPCEVNT>(inv => inv.jtSPCEVNTID == jtSPCEVNTID);
            if (invitee != null)
            {
                try
                {
                    invitee.CHECKEDIN = checkedIn;
                    _entity_crm.Update<jtSPCEVNT>(invitee);
                    _entity_crm.CommitChanges();
                    return "";
                }
                catch (Exception ex)
                {
                    return "An error occurred.  Unable to update invitee record.";                    
                }
            }
            else
            {
                return "Unable to find invitee record.";
            }
        }

        public string ChangeInviteeEventStatus(int jtSPCEVNTID, int statusId)
        {
            // check if this is valid status id
            bool validStatus = _entity_crm.All<lkEVNTSTATUS>().Any(status => status.STATUS == statusId);
            if (!validStatus)
            {
                return "Not a valid Event Status.";
            }
            jtSPCEVNT invitee = _entity_crm.Single<jtSPCEVNT>(inv => inv.jtSPCEVNTID == jtSPCEVNTID);
            if (invitee != null)
            {
                try
                {
                    invitee.STATUS = (short?)statusId;
                    _entity_crm.Update<jtSPCEVNT>(invitee);
                    _entity_crm.CommitChanges();
                    return "";
                }
                catch (Exception ex)
                {
                    return "An error occurred.  Unable to update invitee record.";
                }
            }
            else
            {
                return "Unable to find invitee record.";
            }
        }

        public int GetInviteeMassUpdateCount(InviteeMassUpdateParams updateParams)
        {
            if (updateParams.runSearch)
            {
                InviteeSearchParams searchParams = updateParams.searchParams;
                // run search to get ids
                return GetInviteesAndGuests(
                    searchParams.eventId,
                    searchParams.searchText,
                    1,
                    int.MaxValue,
                    null,
                    searchParams.status,
                    searchParams.type,
                    searchParams.checkedIn).Item2
                        // remove exceptions
                        .Where(inv => !updateParams.exceptionIds.Contains(inv.jtSPCEVNTID))
                        .Select(inv => inv.jtSPCEVNTID)
                        .Count();

            }
            else
            {
                return updateParams.fewIncludeIds.Count();
            }
        }

        public string MassUpdateInviteesCheckInOrStatus(InviteeMassUpdateParams updateParams)
        {
            IEnumerable<int> ids;
            if (updateParams.runSearch)
            {
                InviteeSearchParams searchParams = updateParams.searchParams;
                // run search to get ids
                ids = GetInviteesAndGuests(
                    searchParams.eventId,
                    searchParams.searchText,
                    1,
                    int.MaxValue,
                    null,
                    searchParams.status,
                    searchParams.type,
                    searchParams.checkedIn).Item2
                    // remove exceptions
                        .Where(inv => !updateParams.exceptionIds.Contains(inv.jtSPCEVNTID))
                        .Select(inv => inv.jtSPCEVNTID);

            }
            else
            {
                ids = updateParams.fewIncludeIds;
            }
            // get id string
            string idString = GetJtspcevntidString(ids);
            // run stored procedure
            int result = RunInviteesUpdateProc(idString, updateParams.checkedIn, updateParams.statusId);
            return result != 0 ? "" : "An error occurred. Unable to update invitee records.";            
        }

        private string GetJtspcevntidString(IEnumerable<int> ids)
        {
            // create a comma delimited string of jtSPCEVNTID 
            // to pass to SP z_batch_update_jtspcevnt_checkedin_status
            StringBuilder sb = new StringBuilder();
            foreach (var id in ids)
            {
                // we don't have to worry about final trailing ','
                // stored procedure will still work properly
                sb.Append(id).Append(',');
            }
            return sb.ToString();
        }

        private int RunInviteesUpdateProc(string commaDelimIdString, bool? checkedIn=null, int? statusId = null)
        {
            int _checkedIn = checkedIn == null ? -1 : ((bool)checkedIn ? 1 : 0);
            int _statusId = statusId ?? -1;
            if (_statusId > -1)
            {
                // confirm that event status id actually exists, else we'll violate foreign key constraint
                bool statusExists = _entity_crm.All<lkEVNTSTATUS>().Any(s => s.STATUS == _statusId);
                _statusId = statusExists ? _statusId : -1;
            }
            try
            {
                int result = _dbFactory.getContext().Database
                    .ExecuteSqlCommand(
                        "EXEC z_batch_update_jtspcevnt_checkedin_status @idString, @checkedIn, @statusId",
                        new SqlParameter("@idString", commaDelimIdString),
                        new SqlParameter("@checkedIn", _checkedIn),
                        new SqlParameter("@statusId", _statusId));
                // should return -1
                return result;

            }
            catch (Exception ex)
            {

                return 0;
            }

        }

        


        
        //public List<EventDoc_ext1> get_eventDocs(string where, string orderBy, int pageSize, int pageNo)
        //{
        //    string sql = string.Format("SELECT SPCEVNTDOCID ,SPCEVNTID ,FILENAME ,CONTENT FROM pmSPCEVNTDOC WHERE {0}", where);

        //    var q = _dbFactory.getContext().Database.SqlQuery<EventDoc_ext1>(String.Format("EXEC dbo.get_pagedDataSet '{0}', '{1}', {2}, {3}", sql, orderBy, pageSize, pageNo));

        //    return q.ToList();

        //}

        public List<EventDocSplExt> get_eventDocs(string where, string orderBy, int pageSize, int pageNo)
        {
            string sql = string.Format("SELECT SPCEVNTDOCID ,SPCEVNTID ,FILENAME FROM pmSPCEVNTDOC WHERE {0}", where);

            var q = _dbFactory.getContext().Database.SqlQuery<EventDocSplExt>(String.Format("EXEC dbo.get_pagedDataSet '{0}', '{1}', {2}, {3}", sql, orderBy, pageSize, pageNo));

            return q.ToList();

        }

        
        public bool check_dupEvent(string evtcode, int evtid)
        {
            string sql = (evtid == 0 ? string.Format("SELECT Count(*) as recordCount FROM pmSPCEVNT WHERE Upper(EVNTCODE) ='{0}'", evtcode.ToUpper()) :
                                       string.Format("SELECT Count(*) as recordCount FROM pmSPCEVNT WHERE Upper(EVNTCODE) ='{0}' and SPCEVNTID != {1}", evtcode.ToUpper(), evtid));

            var q = _dbFactory.getContext().Database.SqlQuery<UniquenessCheck>(sql);
            int isZero = q.FirstOrDefault().recordCount;
            return !(isZero == 0);
        }

        public bool check_DonorsSignedUp(int evtid)
        {
            string sql = string.Format("SELECT Count(*) as recordCount FROM People P INNER JOIN jtSPCEVNT F ON P.PID = F.PID WHERE F.SPCEVNTID = {0}", evtid);

            var q = _dbFactory.getContext().Database.SqlQuery<UniquenessCheck>(sql);
            int isZero = q.FirstOrDefault().recordCount;
            return !(isZero == 0);
        }

        public bool check_SourceExistsForEvent(int evtid)
        {
            string sql = string.Format("select Count(*) as recordCount FROM SOURCE Where SPCEVNTID = {0}", evtid);

            var q = _dbFactory.getContext().Database.SqlQuery<UniquenessCheck>(sql);
            int isZero = q.FirstOrDefault().recordCount;
            return !(isZero == 0);
        }


        public bool check_guestRespExist(int evtFldId)
        {
            string sql = string.Format("SELECT Count(*) as recordCount FROM dtSPCEVNTGUESTFLD WHERE spcevntfldid ={0}", evtFldId);
            
            var q = _dbFactory.getContext().Database.SqlQuery<UniquenessCheck>(sql);
            int isZero = q.FirstOrDefault().recordCount;
            return !(isZero == 0);

        }

        public int delete_EvtItems(string _sql)
        {
            var q = _dbFactory.getContext().Database.SqlQuery<Int32>(_sql);
            return q.FirstOrDefault();
        }
        
        public short get_MaxFieldSeqNumber(int evetnId)
        {
            string sql = string.Format("SELECT ISNULL(MAX(FIELDSEQ),0) as FS FROM pmSPCEVNTFLD where spcevntid ={0}", evetnId);

            var q = _dbFactory.getContext().Database.SqlQuery<short>(sql);
            return q.FirstOrDefault();
        }
        
        public int Add(EventF _event)
        {
            _entity_crm.Add(_event);
            _entity_crm.CommitChanges();
            return _event.SPCEVNTID;
        }

        public int Update(EventF _event)
        {
            _entity_crm.Update(_event);
            _entity_crm.CommitChanges();
            return _event.SPCEVNTID;
        }

        public void Delete(EventF _event)
        {
            _entity_crm.Delete(_event);
            _entity_crm.CommitChanges();

        }

        public int DeleteEvent(string _sql)
        {
            var q = _dbFactory.getContext().Database.SqlQuery<Int32>(_sql);
            return q.FirstOrDefault();
        }

        public decimal getPledgeBalance(int eventId)
        {
            string sql = @"
                    SELECT ISNULL(SUM(c.PLEDGEBAL),0) as PLEDGEBAL
                        FROM (SELECT b.PLEDGEAMT - ISNULL((SELECT SUM(m.AMT) FROM MONY m, jtMONYPLEDGE j WHERE m.MID = j.MID AND j.PLEDGEID = b.PLEDGEID AND m.COUNTER = 1 AND m.SOFTMONEY = 0),0) as PLEDGEBAL
                        FROM (SELECT s.SRCECODE FROM SOURCE s WHERE s.SPCEVNTID = " + eventId.ToString() +
                        @" UNION
                        SELECT e.EVNTCODE FROM pmSPCEVNT e WHERE e.SPCEVNTID = " + eventId.ToString() + @") a,PLEDGE b
                        WHERE a.SRCECODE = b.SRCECODE) c ";

            var q = _dbFactory.getContext().Database.SqlQuery<decimal>(sql);

            decimal pledgeBalance = q.FirstOrDefault();
            
            return pledgeBalance;
            
        }

        public costData getCostData(int eventId)
        {
            string sql = @"WITH 
                           cteEVNTRecords (AMT, BATCHDTE, SPCEVNTID)
                    AS
                           (select M.AMT, M.BATCHDTE, j.SPCEVNTID
                           FROM MONY m, dtSPCEVNTMONY d, jtSPCEVNT j 
                           WHERE m.COUNTER = 1 AND m.SOFTMONEY = 0 AND m.MID = d.MID AND 
                           d.jtSPCEVNTID = j.jtSPCEVNTID AND j.SPCEVNTID = " + eventId.ToString() + ")" +
                    "select raised = ISNULL(SUM(AMT),0) , FGIFTDTE = MIN(BATCHDTE), FGIFT = (select top 1 ISNULL(AMT,0) from cteEVNTRecords order by BATCHDTE ASC, AMT DESC ), LGIFTDTE = MAX(BATCHDTE), LGIFT = (select top 1 ISNULL(AMT,0) from cteEVNTRecords order by BATCHDTE DESC, AMT DESC) from cteEVNTRecords";
            
            var q = _dbFactory.getContext().Database.SqlQuery<costData>(sql);

            return q.FirstOrDefault();

        }


        public raisedValueData getraisedValueData(int eventId)
        {
            try
            {
                string sql = @"SELECT ISNULL(SUM(AMT),0) as raisedValue FROM MONY WHERE SRCEID IN (SELECT SRCEID FROM SOURCE where SPCEVNTID = " + eventId.ToString() + ") AND COUNTER = 1 AND SOFTMONEY = 0";

                var q = _dbFactory.getContext().Database.SqlQuery<raisedValueData>(sql);

                return q.FirstOrDefault();
            }
            catch
            {
                return null;
            }
            
        }

        public bool CheckEventQuestionExists(EventQuestion _eventQue)
        {
            try
            {
                EventQuestion q = _entity_crm.Single<EventQuestion>(a => a.FIELDNAME == _eventQue.FIELDNAME && a.SPCEVNTID == _eventQue.SPCEVNTID);
                if (q != null) { return true; }
                else return false;
            }
            catch
            {
                return false;
            }
        }

        public int AddEventQuestion(EventQuestion _eventQue)
        {
            _entity_crm.Add(_eventQue);
            _entity_crm.CommitChanges();
            return _eventQue.SPCEVNTFLDID;
        }

        public int UpdateEventQuestion(EventQuestion _eventQue)
        {
            _entity_crm.Update(_eventQue);
            _entity_crm.CommitChanges();
            return _eventQue.SPCEVNTFLDID;
        }

        public int AddEventDoc(EventDoc _eventDoc)
        {
            _entity_crm.Add(_eventDoc);
            _entity_crm.CommitChanges();
            return _eventDoc.SPCEVNTDOCID;
        }

        public void DeleteEventDoc(EventDoc _eventDoc)
        {
            _entity_crm.Delete(_eventDoc);
            _entity_crm.CommitChanges();

        }

        #endregion


        #region [[ People Borwser - Events ]]

        public int AddEventQuestionAnswer(PeopleEventQuesAns _PeopleQuesAns)
        {
            _entity_crm.Add(_PeopleQuesAns);
            _entity_crm.CommitChanges();
            return _PeopleQuesAns.SPCEVNTFLDID;
        }

        public int Deletejtevnt(string _sql)
        {
            var q = _dbFactory.getContext().Database.SqlQuery<Int32>(_sql);
            return q.FirstOrDefault();

        }

        #endregion

        public IQueryable<pmSPCEVNT> GetDisplayEventQueryable()
        {
            return _entity_crm.All<EventF>().Select(e => new pmSPCEVNT()
            {
                SPCEVNTID = e.SPCEVNTID,
                EVNTCODE = e.EVNTCODE,
                EVNTDESC = e.EVNTCODE + " - " + e.EVNTDESC
            });
        }
    }
}