﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace cmdiapp.n.core.Areas.crm.Core
{
    public class TxtFormatParams
    {
        public string RowDelimiter { get; set; }
        public string ColDelimiter { get; set; }
        public bool QuoteFieldsWithColDelim { get; set; }
        public char QuoteChar { get; set; }
        public bool RemoveColDelim { get; set; }

        public TxtFormatParams()
        {
            // defaults
            RowDelimiter = Environment.NewLine;
            ColDelimiter = ",";
            QuoteFieldsWithColDelim = false;
            RemoveColDelim = false;
        }

        public TxtFormatParams(
            string rowDelimiter, 
            string colDelimiter, 
            bool quoteFieldsWithColDelim,
            char quoteChar,
            bool removeColDelim)
        {
            RowDelimiter = rowDelimiter;
            ColDelimiter = colDelimiter;
            QuoteFieldsWithColDelim = quoteFieldsWithColDelim;
            QuoteChar = quoteChar;
            RemoveColDelim = removeColDelim;
        }
    }
}