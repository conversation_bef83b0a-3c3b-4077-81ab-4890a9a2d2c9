﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Data.Entity;
using System.Linq;
using System.Web;
using System.Web.Mvc;
using System.Configuration;
using cmdiapp.n.core.Domain.Models;

using cmdiapp.n.core.Core;
using cmdiapp.n.core.Core.App_Start;

namespace cmdiapp.n.core.Areas.crm.Domain.Repositories
{
    public class __DBFactory : cmdiapp.n.core.Domain.Repositories.Disposable, I__DBFactory
    {
        private DbContext _db;
        private string _connectionString;

        #region [[ Constructors ]]
        public __DBFactory()
        {
            project currentProject = session.currentDomain_project;
            _connectionString = (currentProject != null ? currentProject._connectionString() : "no valid project");
        }

        public __DBFactory(project project)
        {
            _connectionString = (project != null ? project._connectionString() : "no valid project");
        }

        public __DBFactory(string connectionString)
        {
            _connectionString = connectionString;
        }
        #endregion

        #region [[ getContext - ConnectionString Key is specified here ]]
        public DbContext getContext()
        {
            return _db ?? (_db = new _crmDBContext(_connectionString));
        }

        public string get_connString()
        {
            var connectionString = ConfigurationManager.ConnectionStrings["kernelContext"];
            if (connectionString == null || string.IsNullOrEmpty(connectionString.ConnectionString))
            {
                throw new ConfigurationErrorsException(
                    string.Format("No such DB connectionstring found.")
                );
            }
            return connectionString.ConnectionString;
        }
        #endregion

        #region [[ Dispose ]]
        protected override void DisposeCore()
        {
            if (_db != null)
                _db.Dispose();
        }
        #endregion
    }
}