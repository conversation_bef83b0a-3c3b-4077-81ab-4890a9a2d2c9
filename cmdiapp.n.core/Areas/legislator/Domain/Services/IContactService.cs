﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using cmdiapp.n.core.Areas.legislator.Domain.Models;
using cmdiapp.n.core.Domain.ViewModels;

namespace cmdiapp.n.core.Areas.legislator.Domain.Services
{
    public interface IContactService
    {
        Contact Get(int id);

        List<Contact> GetByLegislator(string legislatorId, bool includeDeleted = false);

        genericResponse HandleChange(ContactChange change, ContactChangeStatus oldStatus, ContactChangeStatus newStatus);

        bool CanUpdateStatusTo(int contactId, int contactChangeId, string changeTypeCode, string oldStatusCode, string newStatusCode);
    }
}
