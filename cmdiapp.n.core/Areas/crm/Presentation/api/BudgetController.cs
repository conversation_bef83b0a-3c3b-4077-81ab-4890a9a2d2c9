﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Net;
using System.Web.Http;

using Ninject;
using Ninject.Web.Mvc;

using cmdiapp.n.core.Domain.ViewModels;
using cmdiapp.n.core.Areas.crm.Core;
using cmdiapp.n.core.Areas.crm.Domain.Models;
using cmdiapp.n.core.Areas.crm.Domain.ViewModels;
using cmdiapp.n.core.Areas.crm.Domain.Data;
using cmdiapp.n.core.Areas.crm.Domain.Repositories;
using cmdiapp.n.core.Presentation.Controllers;
using cmdiapp.n.core.Areas.crm.Domain.Services;
using cmdiapp.n.core.Core.App_Start;
using cmdiapp.n.core.Areas.query.Domain.Models;
using cmdiapp.n.core.Domain.Models;
using cmdiapp.n.core.Core;
using cmdiapp.n.core.Library;
using System.Data;
using AutoMapper;
using cmdiapp.n.core._Presentation.Attributes;

namespace cmdiapp.n.core.Areas.crm.Presentation.api
{

    [Authorize]
    public class BudgetController : ApiController
    {
        private I_entity_crm _entity_crm;

        private readonly IdashboardService _dashboardService;
        userSession _userSession = session.userSession;
        private readonly IReportDataService _reportService;
        

        public BudgetController()
        {
            _entity_crm = I_entityManager_crm.getEntity();
           _dashboardService = NinjectMVC.kernel.Get<IdashboardService>();
           _reportService = NinjectMVC.kernel.Get<IReportDataService>();
        }

        #region [[ Budget ]]

        #region [ payeeBudgetList ]
        [HttpGet, Route("crm/api/budget/payeeBudgetList")]
        public genericResponse payeeBudgetList(int entityid, string pd, int? page, int? pageSize)
        {
            //pd: ALL,LY=Last Year,TY=This Year
            #region [ Retrieve "Sort" options ]
            string sortField = HttpContext.Current.Request.QueryString["sort[0][field]"];
            string sortDir = HttpContext.Current.Request.QueryString["sort[0][dir]"];

            int _pageSize = (pageSize == null ? 10 : pageSize.Value);
            int _pageNo = (page == null ? 1 : page.Value);

            string _sortOptions = " TXNDTE DESC";
            if (!string.IsNullOrEmpty(sortField))
                _sortOptions = sortField;
            if (!string.IsNullOrEmpty(sortField) && !string.IsNullOrEmpty(sortDir))
                _sortOptions = _sortOptions + " " + sortDir;
            #endregion

            #region [[ sql Set up ]]
            string sqlText = getPayeeBudgetListSql(entityid, pd);
            sqlText = sqlText.Replace("'", "''");

            //Get the Data
            List<Txn_forbudget> _list = new List<Txn_forbudget>();
            _list = _entity_crm.getContext().Database.SqlQuery<Txn_forbudget>(String.Format("EXEC dbo.get_pagedDataSet '{0}', '{1}', {2}, {3}", sqlText, _sortOptions, pageSize, page)).ToList();
            #endregion

            IEnumerable<iItemType> results = _list.ToList();
            if (results.Count() > 0)
            {
                return new genericResponse() { success = true, __count = _list.FirstOrDefault().count_, results = results.ToList() };
            }
            else
            {
                return new genericResponse() { success = true, __count = 0 };
            }
        }
        [HttpPost, Route("crm/api/budget/ExportPayeeBudgetListKey")]
        [NotLaunchpadApiAuthorize]
        public string ExportPayeeBudgetListKey(int entityid, string pd)
        {

            string sqlText = getPayeeBudgetListSql(entityid, pd);

            string sToday = String.Format("{0:yyyyMMMdd__hh_mm_ss tt}", DateTime.Now);
            string moduleName = "Budget Spent List";
            string fileName = moduleName + "_" + System.Guid.NewGuid() + "_" + _userSession.UserName + "_" + sToday;

            DataSet ds = null;

            ds = _reportService.get_dataset_w_sql__single(sqlText, "Budget Spent List");

            if (ds != null)
            {
                //CreateExcelFile.CreateExcelDocument(ds.Copy(), fileName, HttpContext.Current.Response);
                string key_ = System.Guid.NewGuid().ToString();
                HttpRuntime.Cache.Insert(key_, ds, null, DateTime.UtcNow.Add(new TimeSpan(1, 0, 1)), System.Web.Caching.Cache.NoSlidingExpiration);
                //return the Key
                return key_;

            }
            else
            {
                return null;
            }

        }
        
        #endregion


        [HttpGet, Route("crm/api/budget/budgetSettingList")]
        public genericResponse budgetSettingList(int entityid, int? page, int? pageSize)
        {
            #region [ Retrieve "Sort" options ]
            string sortField = HttpContext.Current.Request.QueryString["sort[0][field]"];
            string sortDir = HttpContext.Current.Request.QueryString["sort[0][dir]"];

            int _pageSize = (pageSize == null ? 10 : pageSize.Value);
            int _pageNo = (page == null ? 1 : page.Value);

            string _sortOptions = " expectedDate DESC";
            if (!string.IsNullOrEmpty(sortField))
                _sortOptions = sortField;
            if (!string.IsNullOrEmpty(sortField) && !string.IsNullOrEmpty(sortDir))
                _sortOptions = _sortOptions + " " + sortDir;
            #endregion

            #region [[ sql Set up ]]
            string sqlText = getBudgetSettingListSql(entityid);
            sqlText = sqlText.Replace("'", "''");

            //Get the Data
            List<budgetAlloc_ext> _list = new List<budgetAlloc_ext>();
            _list = _entity_crm.getContext().Database.SqlQuery<budgetAlloc_ext>(String.Format("EXEC dbo.get_pagedDataSet '{0}', '{1}', {2}, {3}", sqlText, _sortOptions, pageSize, page)).ToList();
            #endregion

            IEnumerable<iItemType> results = _list.ToList();
            if (results.Count() > 0)
            {
                return new genericResponse() { success = true, __count = _list.FirstOrDefault().count_, results = results.ToList() };
            }
            else
            {
                return new genericResponse() { success = true, __count = 0 };
            }
        }

        [HttpPost, Route("crm/api/budget/ExportBudgetSettingListKey")]
        public string ExportBudgetSettingListKey(int entityid)
        {

            string sqlText = getBudgetSettingListSql(entityid);

            string sToday = String.Format("{0:yyyyMMMdd__hh_mm_ss tt}", DateTime.Now);
            string moduleName = "Budget Setting List";
            string fileName = moduleName + "_" + System.Guid.NewGuid() + "_" + _userSession.UserName + "_" + sToday;

            DataSet ds = null;

            ds = _reportService.get_dataset_w_sql__single(sqlText, "Budget Setting List");

            if (ds != null)
            {
                //CreateExcelFile.CreateExcelDocument(ds.Copy(), fileName, HttpContext.Current.Response);
                string key_ = System.Guid.NewGuid().ToString();
                HttpRuntime.Cache.Insert(key_, ds, null, DateTime.UtcNow.Add(new TimeSpan(1, 0, 1)), System.Web.Caching.Cache.NoSlidingExpiration);
                //return the Key
                return key_;

            }
            else
            {
                return null;
            }

        }

        [HttpGet, Route("crm/api/budget/getBudgetAllocById/{id}")]
        public BudgetAlloc getBudgetAllocById(int id)
        {
            try
            {
                BudgetAlloc _record = _entity_crm.Single<BudgetAlloc>(a => a.budgetAllocId == id);
                return _record;
            }
            catch(Exception ex)
            {
                return null;
            }
        }

        [HttpPost, Route("crm/api/budget/deleteBudgetAllocById/{id}")]
        public genericResponse deleteBudgetAllocById(int id)
        {
            try
            {
                BudgetAlloc _record = _entity_crm.Single<BudgetAlloc>(a => a.budgetAllocId == id);
                if (_record != null)
                {
                    _entity_crm.Delete(_record);
                    _entity_crm.CommitChanges();
                }
                genericResponse _response = new genericResponse() { success = true };
                return _response;
            }
            catch (Exception ex)
            {
                genericResponse _response = new genericResponse() { success = false };
                return _response;
            }
        }

        [HttpPost, Route("crm/api/budget/saveBudgetAlloc")]
        public genericResponse saveBudgetAlloc(BudgetAlloc ba)
        {
            try
            {
                int? bid = ba.budgetAllocId;
                BudgetAlloc _record;
                if (bid != null && bid != 0)
                {
                    _record = _entity_crm.Single<BudgetAlloc>(a => a.budgetAllocId == bid);
                    //existing record
                    _record.expectedDate = ba.expectedDate;
                    _record.amount = ba.amount;
                    _entity_crm.Update(_record);
                }
                else
                {
                    //new record
                    _record = new BudgetAlloc();
                    _record.expectedDate = ba.expectedDate;
                    _record.amount = ba.amount;
                    _record.ENTITYID = ba.ENTITYID;
                    _record.createdAt = ba.createdAt;
                    _record.createdBy = crmSession.UID();
                    _entity_crm.Add(_record);
                }
                _entity_crm.CommitChanges();
                genericResponse _response = new genericResponse() { success = true };
                return _response;
            }
            catch (Exception ex)
            {
                genericResponse _response = new genericResponse() { success = false };
                return _response;
            }
        }


        [HttpGet, Route("crm/api/budget/personalContribList")]
        public genericResponse personalContribList(int pid, int? page, int? pageSize)
        {
            #region [ Retrieve "Sort" options ]
            string sortField = HttpContext.Current.Request.QueryString["sort[0][field]"];
            string sortDir = HttpContext.Current.Request.QueryString["sort[0][dir]"];

            int _pageSize = (pageSize == null ? 10 : pageSize.Value);
            int _pageNo = (page == null ? 1 : page.Value);

            string _sortOptions = " contribDate DESC";
            if (!string.IsNullOrEmpty(sortField))
                _sortOptions = sortField;
            if (!string.IsNullOrEmpty(sortField) && !string.IsNullOrEmpty(sortDir))
                _sortOptions = _sortOptions + " " + sortDir;
            #endregion

            #region [[ sql Set up ]]
            string sqlText = String.Format(@"SELECT PC.*,SP.EVNTCODE,LP.personalContribType,CASE WHEN ISNULL(E.ORGNAME,'''')='''' THEN E.FNAME+'' ''+E.LNAME
	ELSE E.ORGNAME END AS payee FROM PersonalContrib PC INNER JOIN lkPersonalContribType LP ON PC.personalContribTypeId = LP.personalContribTypeId
	LEFT OUTER JOIN ENTITY E ON E.ENTITYID=PC.ENTITYID LEFT OUTER JOIN pmSPCEVNT SP ON SP.SPCEVNTID=PC.SPCEVNTID WHERE PC.PID = {0}", pid.ToString());

            //Get the Data
            List<PersonalContrib_ext> _list = new List<PersonalContrib_ext>();
            _list = _entity_crm.getContext().Database.SqlQuery<PersonalContrib_ext>(String.Format("EXEC dbo.get_pagedDataSet '{0}', '{1}', {2}, {3}", sqlText, _sortOptions, pageSize, page)).ToList();
            #endregion

            IEnumerable<iItemType> results = _list.ToList();
            if (results.Count() > 0)
            {
                return new genericResponse() { success = true, __count = _list.FirstOrDefault().count_, results = results.ToList() };
            }
            else
            {
                return new genericResponse() { success = true, __count = 0 };
            }
        }

        [HttpGet, Route("crm/api/budget/getPersonalContrib/{id}")]
        public PersonalContrib getPersonalContrib(int id)
        {
            try
            {
                PersonalContrib _record = _entity_crm.Single<PersonalContrib>(a => a.personalContribId == id);
                return _record;
            }
            catch (Exception ex)
            {
                return null;
            }
        }
        [HttpPost, Route("crm/api/budget/deletePersonalContribById/{id}")]
        public genericResponse deletePersonalContribById(int id)
        {
            try
            {
                PersonalContrib _record = _entity_crm.Single<PersonalContrib>(a => a.personalContribId == id);
                if (_record != null)
                {
                    _entity_crm.Delete(_record);
                    _entity_crm.CommitChanges();
                }
                genericResponse _response = new genericResponse() { success = true };
                return _response;
            }
            catch (Exception ex)
            {
                genericResponse _response = new genericResponse() { success = false };
                return _response;
            }
        }
        [HttpPost, Route("crm/api/budget/savePersonalContrib")]
        public genericResponse savePersonalContrib(PersonalContrib pc)
        {
            try
            {
                int? id = pc.personalContribId;
                PersonalContrib _record;
                if (id != null && id != 0)
                {
                    _record = _entity_crm.Single<PersonalContrib>(a => a.personalContribId == id);
                    //existing record
                    _record.personalContribTypeId = pc.personalContribTypeId;
                    _record.honoraryName = pc.honoraryName;
                    _record.contribDate = pc.contribDate;
                    _record.amount = pc.amount;
                    _record.SPCEVNTID = pc.SPCEVNTID;
                    _record.ENTITYID = pc.ENTITYID;
                    _entity_crm.Update(_record);
                }
                else
                {
                    //new record
                    _record = new PersonalContrib();
                    _record.PID = pc.PID;
                    _record.personalContribTypeId = pc.personalContribTypeId;
                    _record.honoraryName = pc.honoraryName;
                    _record.contribDate = pc.contribDate;
                    _record.amount = pc.amount;
                    _record.SPCEVNTID = pc.SPCEVNTID;
                    _record.ENTITYID = pc.ENTITYID;
                    _entity_crm.Add(_record);
                }
                _entity_crm.CommitChanges();
                genericResponse _response = new genericResponse() { success = true };
                return _response;
            }
            catch (Exception ex)
            {
                genericResponse _response = new genericResponse() { success = false };
                return _response;
            }
        }
        [HttpGet, Route("crm/api/budget/getPersonalContribType")]
        public List<lkPersonalContribType> getPersonalContribType()
        {
            try
            {
                List<lkPersonalContribType> _record = _entity_crm.All<lkPersonalContribType>().ToList();
                return _record;
            }
            catch (Exception ex)
            {
                return null;
            }
        }
        #region [[[  lists with export   ]]]
        [HttpGet, Route("crm/api/budget/ListDefById/{id}")]
        public listWithExportTemplateDef ListDefById(string id)
        {
            listWithExportTemplateDef def = new listWithExportTemplateDef();
            switch (id)
            {
                case "undesignatedContributions":
                    def = new listWithExportTemplateDef(
                id,
                "Undesignated Contributions",
                "crm/api/budget/ListWithExportData/" + id,
                "crm/api/budget/ExportUndesignatedContributions",
                new List<actionBtn>()
                {
                    new actionBtn("", "fa fa-pencil data-table-btn edit-btn", "gotoTXN", "TXNID", "View/Edit", true)
                },
                new List<fieldFormat>()
                {
                    new fieldFormat("TXNID","TXNID"),
                    new fieldFormat("VendorType","VendorType"),
                    new fieldFormat("VendorName","VendorName"),
                    new fieldFormat("Date","Date","date"),
                    new fieldFormat("Amount","Amount","money"),
                    new fieldFormat("FUNDCODE","FUNDCODE")
                }
                );
                    break;

                case "MissingFECID":
                    def = new listWithExportTemplateDef(
                id,
                "PAC Vendors Missing FEC ID",
                "crm/api/budget/ListWithExportData/" + id,
                "crm/api/budget/ExportMissingFECIDList",
                new List<actionBtn>()
                {
                    new actionBtn("", "fa fa-pencil data-table-btn edit-btn", "gotoPayee", "VendorID", "View/Edit", true)
                },
                new List<fieldFormat>()
                {
                    new fieldFormat("VendorID","VendorID"),
                    new fieldFormat("VendorType","VendorType"),
                    new fieldFormat("VendorName","VendorName"),
                    new fieldFormat("Exp_YTD","Exp_YTD"),
                    new fieldFormat("Exp_CTD","Exp_CTD")
                }
                );
                    break;
            }

            return def;
        }

        [HttpGet, Route("crm/api/budget/ListWithExportData/{dataPoint}")]
        public genericResponse ListWithExportData(string dataPoint, string sortOptions = "", int pageSize = 10, int page = 1)
        {
            //getsort
            string sortField = HttpContext.Current.Request.QueryString["sort[0][field]"];
            string sortDir = HttpContext.Current.Request.QueryString["sort[0][dir]"];
            if (!string.IsNullOrEmpty(sortField))
                sortOptions = sortField;
            if (!string.IsNullOrEmpty(sortField) && !string.IsNullOrEmpty(sortDir))
                sortOptions = sortOptions + " " + sortDir;

            return get_dbd_listData(dataPoint, sortOptions, pageSize, page);
        }
        private genericResponse get_dbd_listData(string dataPoint, string sortOptions, int pageSize, int pageNo)
        {
            if (dataPoint == "undesignatedContributions")
            {
                #region [[ sql ]]
                string sqlText = undesignatedContributionsSqlText();
                sqlText = sqlText.Replace("'", "''");
                #endregion

                List<undesignatedContributions> _list = _entity_crm.getContext().Database.SqlQuery<undesignatedContributions>(String.Format("EXEC dbo.get_pagedDataSet '{0}', '{1}', {2}, {3}", sqlText, string.IsNullOrEmpty(sortOptions)? "VendorType" : sortOptions, pageSize, pageNo)).ToList();
                IEnumerable<iItemType> result = _list.ToList();
                if (_list.Count() > 0)
                {
                    return new genericResponse() { success = true, __count = _list.FirstOrDefault().count_, results = result.ToList() };
                }
                else
                {
                    return new genericResponse() { success = true, __count = 0 };
                }
            }
            else if (dataPoint == "MissingFECID")
            {
                #region [[ sql ]]
                string sqlText = MissingFECIDSqlText();
                sqlText = sqlText.Replace("'", "''");
                #endregion

                List<PACVendorsMissingFECID> _list = _entity_crm.getContext().Database.SqlQuery<PACVendorsMissingFECID>(String.Format("EXEC dbo.get_pagedDataSet '{0}', '{1}', {2}, {3}", sqlText, string.IsNullOrEmpty(sortOptions) ? "VendorType" : sortOptions, pageSize, pageNo)).ToList();
                IEnumerable<iItemType> result = _list.ToList();
                if (_list.Count() > 0)
                {
                    return new genericResponse() { success = true, __count = _list.FirstOrDefault().count_, results = result.ToList() };
                }
                else
                {
                    return new genericResponse() { success = true, __count = 0 };
                }
            }
            else
            {
                return new genericResponse() { success = false, __count = 0 };
            }
        }

        [HttpPost, Route("crm/api/budget/ExportUndesignatedContributionsKey")]
        public string ExportUndesignatedContributionsKey(string searchText)
        {
            if (string.IsNullOrEmpty(searchText)) searchText = "";

            searchParam param = new searchParam();
            param.searchText = searchText;

            string key_ = System.Guid.NewGuid().ToString();
            HttpRuntime.Cache.Insert(key_, param, null, DateTime.UtcNow.Add(new TimeSpan(1, 0, 1)), System.Web.Caching.Cache.NoSlidingExpiration);

            return key_;
        }

        [HttpGet, Route("crm/api/budget/ExportUndesignatedContributions")]
        public string ExportUndesignatedContributions(string key)
        {
            searchParam _param = (searchParam)HttpRuntime.Cache[key];
            string sToday = String.Format("{0:yyyyMMMdd__hh_mm_ss tt}", DateTime.Now);
            string moduleName = "UndesignatedContributions";
            string fileName = moduleName + "_" + System.Guid.NewGuid() + "_" + _userSession.UserName + "_" + sToday;
            string exportSql = undesignatedContributionsSqlText();

            DataSet ds = null;
            
            ds = _reportService.get_dataset_w_sql__single(exportSql, "UndesignatedContributions");

            if (ds != null)
            {
                string key_ = System.Guid.NewGuid().ToString();
                HttpRuntime.Cache.Insert(key_, ds, null, DateTime.UtcNow.Add(new TimeSpan(1, 0, 1)), System.Web.Caching.Cache.NoSlidingExpiration);
                //return the Key
                return key_;
            }
            else
            {
                return null;
            }
        }

        [HttpPost, Route("crm/api/budget/ExportMissingFECIDListKey")]
        public string ExportMissingFECIDListKey(string searchText)
        {
            if (string.IsNullOrEmpty(searchText)) searchText = "";

            searchParam param = new searchParam();
            param.searchText = searchText;

            string key_ = System.Guid.NewGuid().ToString();
            HttpRuntime.Cache.Insert(key_, param, null, DateTime.UtcNow.Add(new TimeSpan(1, 0, 1)), System.Web.Caching.Cache.NoSlidingExpiration);

            return key_;
        }

        [HttpGet, Route("crm/api/budget/ExportMissingFECIDList")]
        public string ExportMissingFECIDList(string key)
        {
            searchParam _param = (searchParam)HttpRuntime.Cache[key];
            string sToday = String.Format("{0:yyyyMMMdd__hh_mm_ss tt}", DateTime.Now);
            string moduleName = "PACVendorsMissingFECID";
            string fileName = moduleName + "_" + System.Guid.NewGuid() + "_" + _userSession.UserName + "_" + sToday;
            string exportSql = MissingFECIDSqlText();

            DataSet ds = null;

            ds = _reportService.get_dataset_w_sql__single(exportSql, "PACVendorsMissingFECID");

            if (ds != null)
            {
                string key_ = System.Guid.NewGuid().ToString();
                HttpRuntime.Cache.Insert(key_, ds, null, DateTime.UtcNow.Add(new TimeSpan(1, 0, 1)), System.Web.Caching.Cache.NoSlidingExpiration);
                //return the Key
                return key_;
            }
            else
            {
                return null;
            }
        }
        [HttpGet, Route("crm/api/budget/unspentBudgetListDef")]
        public listWithExportTemplateDef unspentBudgetListDef()
        {
            listWithExportTemplateDef def = new listWithExportTemplateDef(
                "unspentBudgetList",
                "Unspent Budget",
                "crm/api/budget/unspentBudget/list",
                "crm/api/budget/ExportUnspentBudgetList",
                new List<actionBtn>()
                {
                    new actionBtn("", "fa fa-pencil data-table-btn edit-btn", "gotoPayee", "ENTITYID", "View/Edit", true)
                },
                new List<fieldFormat>()
                {
                    new fieldFormat("Name","Name"),
                    new fieldFormat("TotalBudget","TotalBudget","money"),
                    new fieldFormat("YTDContribution","YTDContribution","money"),
                    new fieldFormat("UnspentBudget","UnspentBudget","money")
                }
                );
            return def;
        }
        [HttpGet, Route("crm/api/budget/unspentBudget/list")]
        public genericResponse unspentBudgetList(string sortOptions = "Name", int pageSize = 10, int page = 1)
        {
            //getsort
            string sortField = HttpContext.Current.Request.QueryString["sort[0][field]"];
            string sortDir = HttpContext.Current.Request.QueryString["sort[0][dir]"];
            if (!string.IsNullOrEmpty(sortField))
                sortOptions = sortField;
            if (!string.IsNullOrEmpty(sortField) && !string.IsNullOrEmpty(sortDir))
                sortOptions = sortOptions + " " + sortDir;

            string sql = "SELECT * FROM [dbo].[unspentBudgetListPull] ()";
            List<unspentBudget> _list = _entity_crm.getContext().Database.SqlQuery<unspentBudget>(String.Format("EXEC dbo.get_pagedDataSet '{0}', '{1}', {2}, {3}", sql, sortOptions, pageSize, page)).ToList();
            IEnumerable<iItemType> result = _list.ToList();
            if (_list.Count() > 0)
            {
                return new genericResponse() { success = true, __count = _list.FirstOrDefault().count_, results = result.ToList() };
            }
            else
            {
                return new genericResponse() { success = true, __count = 0 };
            }
        }
        [HttpPost, Route("crm/api/budget/ExportUnspentBudgetListKey")]
        public string ExportUnspentBudgetListKey(string searchText)
        {
            if (string.IsNullOrEmpty(searchText)) searchText = "";

            searchParam param = new searchParam();
            param.searchText = searchText;

            string key_ = System.Guid.NewGuid().ToString();
            HttpRuntime.Cache.Insert(key_, param, null, DateTime.UtcNow.Add(new TimeSpan(1, 0, 1)), System.Web.Caching.Cache.NoSlidingExpiration);

            return key_;
        }

        [HttpGet, Route("crm/api/budget/ExportUnspentBudgetList")]
        public string ExportUnspentBudgetList(string key)
        {
            searchParam _param = (searchParam)HttpRuntime.Cache[key];
            string sToday = String.Format("{0:yyyyMMMdd__hh_mm_ss tt}", DateTime.Now);
            string moduleName = "UnspentBudget";
            string fileName = moduleName + "_" + System.Guid.NewGuid() + "_" + _userSession.UserName + "_" + sToday;
            string exportSql = @"SELECT * FROM [dbo].[unspentBudgetListPull] ()";

            DataSet ds = null;

            ds = _reportService.get_dataset_w_sql__single(exportSql, "UnspentBudget");

            if (ds != null)
            {
                string key_ = System.Guid.NewGuid().ToString();
                HttpRuntime.Cache.Insert(key_, ds, null, DateTime.UtcNow.Add(new TimeSpan(1, 0, 1)), System.Web.Caching.Cache.NoSlidingExpiration);
                //return the Key
                return key_;
            }
            else
            {
                return null;
            }
        }

        #endregion
        private string undesignatedContributionsSqlText()
        {
            return string.Format(@"SELECT T.TXNID, ET.CODE AS VendorType, E.ORGNAME AS VendorName, T.TXNDTE AS [Date], ISNULL(T.AMT,0) AS Amount,  F.FUNDCODE
FROM TXN T    INNER JOIN dmFUND F ON F.FUNDID = T.FUNDID
    INNER JOIN ENTITY E ON T.ENTITYID = E.ENTITYID
    INNER JOIN lkENTITYTYPE ET ON ET.ENTITYTYPEID = E.ENTITYTYPEID
WHERE ET.CODE in ('CCM', 'PTY', 'PAC') AND ISNULL(T.ELECTCDID, 0)= 0
AND YEAR(T.TXNDTE) = YEAR(GETDATE())");
        }
        private string MissingFECIDSqlText()
        {
            return string.Format(@"SELECT DISTINCT E.ENTITYID AS VendorID, ET.CODE AS VendorType, E.ORGNAME AS VendorName,ISNULL(E.YTDAMT_EXP,0) AS [Exp_YTD],ISNULL(E.CTDAMT_EXP,0) AS [Exp_CTD]
FROM ENTITY E	INNER JOIN lkENTITYTYPE ET ON ET.ENTITYTYPEID=E.ENTITYTYPEID
WHERE ET.CODE in ('CCM','PTY','PAC') AND ISNULL(E.FECCMTEID,'')=''");
        }
        private string getPayeeBudgetListSql(int entityid, string pd)
        {
            string sqlText = String.Format(@"SELECT T.TXNID,T.ENTITYID,
CASE WHEN ISNULL(E.ORGNAME,'')='' THEN E.FNAME+' '+E.LNAME ELSE E.ORGNAME END AS payee, T.[AMT],T.[TXNDTE],
LE.[CODE] AS ELECCODE,T.ELECTYR,T.ELECTOTH 
FROM TXN T INNER JOIN lkFECTXNTYPE LK ON T.FECTXNTYPEID=LK.FECTXNTYPEID  
LEFT OUTER JOIN ENTITY E ON E.ENTITYID=T.ENTITYID 
LEFT OUTER JOIN lkELECTCD LE ON LE.ELECTCDID=T.ELECTCDID 
WHERE LK.LINE='SB23' AND T.TXNTYPEID=2 ");
            if (entityid > 0)
            {
                sqlText = sqlText + String.Format("AND T.ENTITYID ={0} ", entityid);
            }
            if (string.IsNullOrEmpty(pd))
            {
                pd = "ALL";
            }
            pd = Library.util.kill_sqlBlacklistWord(pd);
            if (pd != "ALL")
            {
                sqlText = sqlText + " AND YEAR(T.TXNDTE)=YEAR(getdate()) " + (pd == "LY" ? "-1 " : " ");
            }
            return sqlText;
        }
        private string getBudgetSettingListSql(int entityid)
        {
            string sqlText = String.Format(@"SELECT BA.*,(U.FNAME + ' '+ U.LNAME) AS createdByName,
CASE WHEN ISNULL(E.ORGNAME,'')='' THEN E.FNAME+' '+E.LNAME ELSE E.ORGNAME END AS payee ,
(ISNULL((SELECT SUM(T.AMT) FROM TXN T INNER JOIN lkFECTXNTYPE LK ON T.FECTXNTYPEID=LK.FECTXNTYPEID
WHERE LK.LINE='SB23' AND T.TXNTYPEID=2 AND T.ENTITYID=E.ENTITYID AND YEAR(T.TXNDTE)=YEAR(getdate())),0)) AS YTDContribution
from BudgetAlloc BA INNER JOIN ENTITY E ON BA.ENTITYID=E.ENTITYID LEFT OUTER JOIN ssUSER U ON BA.createdBy=U.UID  ");
            if (entityid > 0)
            {
                sqlText = sqlText + " WHERE BA.ENTITYID = " + entityid.ToString();
            }
            return sqlText;
        }
        #endregion
    }
}
