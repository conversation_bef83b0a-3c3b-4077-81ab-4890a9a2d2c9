﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Data;
using System.Linq;
using System.Web.Mvc;
using System.Data.Linq;
using System.ComponentModel.DataAnnotations.Schema;
using cmdiapp.n.core.Areas.crm.Domain.Data;

using cmdiapp.n.core.Domain.ViewModels;

namespace cmdiapp.n.core.Areas.crm.Domain.Models
{   
    [Table("ACTHIST")]
    public class ACTHIST : _entityBase_crm, iItemType
    {
        [Column]
        [Key]
        public int ACTHISTID { get; set; }

        [Column]
        public int PID	 { get; set; }

        [Column]
        public int? CID	 { get; set; }

        [Column]
        public Int16? HISTTYPEID	 { get; set; }

        [Column]
        public Int16? SNDMTHDID	 { get; set; }

        [Column]
        public Int16? SNDSTATID { get; set; }

        [Column]
        public Int16? TEMPLATEID	 { get; set; }

        [Column]
        public DateTime? HISTDATE	 { get; set; }

        [Column]
        public string SUBJECT	 { get; set; }

        [Column]
        public string ASKMEMO { get; set; }

        [Column]
        public string NOTE	 { get; set; }

        [Column]
        public Int16? UID	 { get; set; }

        [Column]
        public Byte? SENT { get; set; }

        [Column]
        public Int16? SENTBY	 { get; set; }

        [Column]
        public DateTime? SENTON	 { get; set; }

        [Column]
        public DateTime? UPDATEDON	 { get; set; }

        [Column]
        public int? SIGNERID	 { get; set; }

        [Column]
        public int? TRACKNO	 { get; set; }

        [Column]
        public Int16? TRACKTYPEID	 { get; set; }

        [Column]
        public int? MOVEID	 { get; set; }

        [Column("_updating_uid")]
        public Int16? updating_uid { get; set; }

        [Column]
        public Int16? PIPELINEID { get; set; }

        [NotMapped]
        public string SessionKey { get; set; }

        [NotMapped]
        public string PHOTOFILENAME { get; set; }


    }

    [Table("ACTHISTDOC")]
    public class ACTHISTDOC : _entityBase_crm, iItemType
    {
        [Column]
        public int ACTHISTDOCID { get; set; }

        [Column]
        public int? ACTHISTID { get; set; }

        [Column]
        public string FILENAME { get; set; }

        [Column]
        public byte[] CONTENT { get; set; }

        [Column("_updating_uid")]
        public short? updating_uid { get; set; }
    }
}