﻿using cmdiapp.n.core.Areas.crm.Domain.Data;
using System.ComponentModel.DataAnnotations.Schema;
using System.Runtime.Serialization;

namespace cmdiapp.n.core.Areas.crm.Domain.Models
{
    [Table("x_va_reportTypes")]
    [DataContract]
    public class VirginiaReportType : _entityBase_crm
    {
        [Column("id")]
        [DataMember(Name = "id")]
        public int Id { get; set; }

        [Column("code")]
        [DataMember(Name = "code")]
        public string Code { get; set; }

        [Column("name")]
        [DataMember(Name = "name")]
        public string Name { get; set; }
    }
}