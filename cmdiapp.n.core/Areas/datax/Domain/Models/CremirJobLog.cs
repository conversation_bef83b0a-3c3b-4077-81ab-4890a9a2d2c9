﻿using cmdiapp.n.core.Areas.datax.Domain.Data;
using cmdiapp.n.core.Domain.ViewModels;
using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Runtime.Serialization;

namespace cmdiapp.n.core.Areas.datax.Domain.Models
{
    [Table("es_JobLog")]
    [DataContract]
    public class CremirJobLog : _entityBase_datax, iItemType
    {
        [Column("Id")]
        [Key]
        [DataMember(Name = "id")]
        public int Id { get; set; }

        [Column("jobId")]
        [DataMember(Name = "jobId")]
        public int JobId { get; set; }

        [Column("oldStatusId")]
        [DataMember(Name = "oldStatusId")]
        public int OldStatusId { get; set; }

        [Column("newStatusId")]
        [DataMember(Name = "newStatusId")]
        public int NewStatusId { get; set; }

        [Column("recordCount")]
        [DataMember(Name = "recordCount")]
        public int RecordCount { get; set; }

        [Column("logMessage")]
        [DataMember(Name = "logMessage")]
        public string LogMessage { get; set; }

        [Column("createdOn")]
        [DataMember(Name = "createdOn")]
        public DateTime CreatedOn { get; set; }
    }
}