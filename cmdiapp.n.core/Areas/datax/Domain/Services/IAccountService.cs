﻿using cmdiapp.n.core.Areas.datax.Domain.Models;
using cmdiapp.n.core.Areas.datax.Domain.Models.ViewModels;
using cmdiapp.n.core.Domain.ViewModels;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace cmdiapp.n.core.Areas.datax.Domain.Services
{
    /// <summary>
    /// Provides <see langword="interface"/> for CRUD operations
    /// related to <see cref="Account"/> model.
    /// </summary>
    public interface IAccountService
    {
        /// <summary>
        /// Updates an existing <see cref="Account"/> or adds if it doesn't exist.
        /// if newly added.
        /// </summary>
        /// <param name="account">the <see cref="Account"/> to update or add</param>
        /// <returns>
        /// a <see cref="genericResponse"/> conveying whether Save was successful
        /// and an error message if not;
        /// </returns>
        genericResponse Save(Account account);

        /// <summary>
        /// Deletes an existing <see cref="Account"/>
        /// </summary>
        /// <param name="accountId">the <see cref="Account.Id"/> of the record to delete</param>
        /// <returns>a response conveying whether it was successful and an error message if not</returns>
        genericResponse Delete(int accountId);

        /// <summary>
        /// Returns an <see cref="Account"/> if it exists, otherwise returns <see langword="null"/>
        /// </summary>
        /// <param name="accountId">the <see cref="Account.Id"/> of the record to get</param>
        /// <returns>the <see cref="Account"/> if it exists, otherwise <see langword="null"/></returns>
        Account Get(int accountId);

        /// <summary>
        /// Gets <see cref="List{T}"/> of <see cref="Account"/>s with given <see cref="Account.Id"/>s
        /// </summary>
        /// <param name="accountIds">the <see cref="Account.Id"/>s of the records to get</param>
        /// <returns><see cref="List{T}"/> of <see cref="Account"/>s if any, otherwise an empty <see cref="List{T}"/></returns>
        List<Account> Get(params int[] accountIds);

        /// <summary>
        /// Gets <see cref="List{T}"/> of <see cref="Account"/>s for the current project.
        /// </summary>
        /// <returns>a <see cref="List{T}"/> of <see cref="Account"/>s for the current project if any, otherwise an empty <see cref="List{T}"/></returns>
        List<Account> GetAccountsForCurrentProject();

        /// <summary>
        /// Gets <see cref="List{T}"/> of <see cref="Account"/>s for the current project with given <see cref="Account.callerId"/>.
        /// </summary>
        /// <param name="callerId"><see cref="Caller.Id"/></param>
        /// <returns>a <see cref="List{T}"/> of <see cref="Account"/>s with given <see cref="Account.callerId"/>
        /// for the current project if any, otherwise an empty <see cref="List{T}"/></returns>
        List<Account> GetAccountsForCurrentProject(int callerId);

        /// <summary>
        /// Gets <see cref="int"/> count of <see cref="Account"/>s for the current project with given <see cref="Account.callerId"/>. 
        /// </summary>
        /// <param name="callerId"><see cref="Account.callerId"/>; <seealso cref="Caller.Id"/></param>
        /// <returns>Count of <see cref="Account"/>s for the current project with given <see cref="Account.callerId"/></returns>
        int GetCountofAccountsForCurrentProject(int callerId);

        /// <summary>
        /// Creates starter <see cref="Account"/> model with newly generated <see cref="Account.appKey"/>
        /// </summary>
        /// <param name="info">required credentials and caller account information</param>
        /// <returns>starter <see cref="Account"/> model with newly generated <see cref="Account.appKey"/></returns>
        Account GetNewAccountModel(NewAccountInfo info);
        void AddAccount(Account account);
    }
}
