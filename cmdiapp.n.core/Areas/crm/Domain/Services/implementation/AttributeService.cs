﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

using Ninject;
using Ninject.Web.Mvc;

using cmdiapp.n.core.Core.App_Start;
using cmdiapp.n.core.Areas.crm.Domain.Models;
using cmdiapp.n.core.Areas.crm.Domain.ViewModels;
using cmdiapp.n.core.Areas.crm.Domain.Repositories;
using cmdiapp.n.core.Areas.query.Domain.Models;
using cmdiapp.n.core.Areas.crm.Domain.Data;
using System.Linq.Expressions;
using cmdiapp.n.core.Library;
using AutoMapper;
using cmdiapp.n.core.Domain.ViewModels;
using System.Data.SqlClient;

namespace cmdiapp.n.core.Areas.crm.Domain.Services
{
    public class AttributeService : IAttributeService
    {
        private I___unitOfWork _unitOfWork;
        private I__DBFactory _dbFactory;
        private IcontactFlagRepository _contactFlagRepository;
        private I_entity_crm _entity_crm;

        public AttributeService(I___unitOfWork unitOfWork, I__DBFactory dbFactory, IcontactFlagRepository ContactFlagRepository, I_entity_crm entity_crm)
        {
            _unitOfWork = unitOfWork;
            _dbFactory = dbFactory;
            _contactFlagRepository = ContactFlagRepository;
            _entity_crm = entity_crm;
        }

        public bool check_unqiuenessName(string s, int id)
        {
            string sql = "";
            int isZero = 0;
            if (id > 0)   // Existing Record
            {
                sql = String.Format("SELECT Count(*) as recordCount FROM Attribute WHERE name = @name AND id <> @id");
                isZero = _dbFactory.getContext().Database.SqlQuery<int>(sql, new SqlParameter("name", s), new SqlParameter("id", id)).FirstOrDefault();
            }
            else
            {
                sql = String.Format("SELECT Count(*) as recordCount FROM Attribute WHERE name = @name");
                isZero = _dbFactory.getContext().Database.SqlQuery<int>(sql, new SqlParameter("name", s)).FirstOrDefault();
            }
            return (isZero == 0) ? true : false;
        }

        public bool check_unqiuenessName_AttributeCategory(string s, int id)
        {
            string sql = "";
            int isZero = 0;
            if (id > 0)   // Existing Record
            {
                sql = String.Format("SELECT Count(*) as recordCount FROM AttributeCategory WHERE name = @name AND id <> @id");
                isZero = _dbFactory.getContext().Database.SqlQuery<int>(sql, new SqlParameter("name", s), new SqlParameter("id", id)).FirstOrDefault();
            }
            else
            {
                sql = String.Format("SELECT Count(*) as recordCount FROM AttributeCategory WHERE name = @name");
                isZero = _dbFactory.getContext().Database.SqlQuery<int>(sql, new SqlParameter("name", s)).FirstOrDefault();
            }
            return (isZero == 0) ? true : false;
        }
        public List<AttributeCategory_ext> get_all_AttributeCategory(string where, string orderBy, int pageSize, int pageNo)
        {
            string sql = (!string.IsNullOrEmpty(where) ? string.Format("SELECT * FROM AttributeCategory WHERE {0}", where) : "SELECT * FROM AttributeCategory");

            var q = _dbFactory.getContext().Database.SqlQuery<AttributeCategory_ext>(String.Format("EXEC dbo.get_pagedDataSet '{0}', '{1}', {2}, {3}", sql, orderBy, pageSize, pageNo));

            return q.ToList();
        }
        public List<Attribute_ext> get_all_Attributes(string where, string orderBy, int pageSize, int pageNo)
        {
            string sql = (!string.IsNullOrEmpty(where) ? string.Format("SELECT * FROM Attribute WHERE {0}", where) : "SELECT * FROM Attribute");

            var q = _dbFactory.getContext().Database.SqlQuery<Attribute_ext>(String.Format("EXEC dbo.get_pagedDataSet '{0}', '{1}', {2}, {3}", sql, orderBy, pageSize, pageNo));

            return q.ToList();
        }

        public Tuple<int, List<AttributePeople>> GetAttributePeopleWithCount(
            int pid, int page, int pageSize, QueryRuntimeSort sort, int? categoryId, bool? active, string date)
        {
            // start query
            var q = _entity_crm
                .All<AttributePeople>()
                .Where(APFilterCondition(pid, categoryId, active, date));
            // get total count
            int count = q.Count();
            string sortDir = sort?.dir ?? "desc";
            string sortField = sort?.field ?? "endDate";
            // confirm that sort field is valid property
            if (new AttributePeople().GetType().GetProperty(sortField) == null)
            {
                sortField = "endDate";
            }
            List<AttributePeople> list;
            // endDate ==null first
            if (sortField == "endDate" && sortDir == "desc")
            {
                list = q.OrderByDescending(a => a.endDate == null).ThenByDescending(a => a.endDate).ThenByDescending(a => a.startDate)
                    .AsEnumerable()
                    .Skip((page - 1) * pageSize)
                    .Take(pageSize)
                    .ToList();
            }
            else
            {
                // do sorting and paging to get list
                list = q.dynamicSortBy(sortField, sortDir == "desc")
                    .AsEnumerable()
                    .Skip((page - 1) * pageSize)
                    .Take(pageSize)
                    .ToList();
            }            

            return new Tuple<int, List<AttributePeople>>(count, list);
        }
        public Tuple<int, List<AttributePeople_export>> GetAttributePeopleWithCountForExport(
            int pid, int page, int pageSize, QueryRuntimeSort sort, int? categoryId, bool? active, string date)
        {
            // start query
            var q = _entity_crm
                .All<AttributePeople>()
                .Where(APFilterCondition(pid, categoryId, active, date));
            // get total count
            int count = q.Count();
            string sortDir = sort?.dir ?? "desc";
            string sortField = sort?.field ?? "updatedAtUtc";
            // confirm that sort field is valid property
            if (new AttributePeople().GetType().GetProperty(sortField) == null)
            {
                sortField = "updatedAtUtc";
            }
            // do sorting and paging to get list
            List<AttributePeople> list = q.dynamicSortBy(sortField, sortDir == "desc")
                .AsEnumerable()
                .Skip((page - 1) * pageSize)
                .Take(pageSize)
                .ToList();

            Mapper.CreateMap<AttributePeople, AttributePeople_export>();
            List<AttributePeople_export> results = list.Select(a => Mapper.Map<AttributePeople, AttributePeople_export>(a)).ToList();
            
            return new Tuple<int, List<AttributePeople_export>>(count, results);
        }
        private Expression<Func<AttributePeople, bool>> APFilterCondition(int pid, int? categoryId, bool? active, string date)
        {
            // build expression
            Expression<Func<AttributePeople, bool>> filterExpression =
               a => a.PID == pid
                   && (categoryId == null || a.Attribute.categoryId == categoryId)
                   && (active == null || a.active == true);

            if (string.IsNullOrEmpty(date))
            {
                return filterExpression;
            }
            else
            {
                // get date expression from DateFilterFactory
                Expression<Func<AttributePeople, bool>> dateExpression =
                    new DateFilterFactory<AttributePeople>(date, "updatedAtUtc", false, "updatedAtUtc").Predicate;
                // conjoin lambdas and return
                return filterExpression.AndAlsoSafe(dateExpression);
            }
        }


        public string MassDeleteAttribute(int attributeId, int UID, int maxAllowed)
        {
            var q = _dbFactory.getContext().Database.SqlQuery<SPResult>(String.Format("EXEC dbo.bt_remove_attribute_and_itsRelated {0},{1},{2}", attributeId, UID, maxAllowed));
            return q.FirstOrDefault().RESULT;
        }
        public string MassDeleteAttributeCategory(int attrcatid, int UID, int maxAllowed)
        {
            var q = _dbFactory.getContext().Database.SqlQuery<SPResult>(String.Format("EXEC dbo.bt_remove_attributecategory_and_itsRelated {0},{1},{2}", attrcatid, UID, maxAllowed));
            return q.FirstOrDefault().RESULT;
        }
    }
}