﻿using System.Collections.Generic;
using System.Web.Mvc;

using cmdiapp.n.core.Areas.crm.Domain.Models;
using cmdiapp.n.core.Areas.crm.Domain.ViewModels;
using cmdiapp.n.core.Areas.crm.Domain.Repositories;
using cmdiapp.n.core.Domain.ViewModels;
using System;
using System.Web;
using System.Linq;

namespace cmdiapp.n.core.Areas.crm.Domain.Services
{
   
    public interface IpackageService
    {
        List<PACKAGE_ext> get_all_initiatives(string sql, string orderBy, int pageSize, int pageNo);
        PACKAGE get_package(int pkgId);
        bool IsReferencedBySource(int pkgId);
        MessageBoard process_sql(string p_sql, string p_datasetName, MessageBoard msgBoard);

        genericResponse Delete(int packageId);

        genericResponse ManagePackage(PACKAGE package);
        genericResponse UploadPackageDocument(HttpPostedFileBase file, int packageId);
        
        //Get Additional Lists we need for the Add/Edit Page
        List<lkInitiativeType> get_InitiativeTypes();
        List<PROGRAM> get_Programs();
        List<dtPROG> get_SubPrograms();
        List<PACKAGE_ext> get_all_Packages(string sql);
        List<SOURCE_ext> get_Sources(string sql, string orderBy, int pageSize, int pageNo);
        _donationTOTALSfinal get_donationTOTALSfinal(int PkgeID);
        List<initiativeLineCHARTdata> getInitiativeFundGraphData(string sSTARTdt, string sENDdt, int PkgeID);
        List<initiativePieCHARTdata> getInitiativeInteractPieData(string _sql);
        List<initiativescrollLineCHARTdata> getInitiativeInteractLineData(string _sql);
        //For Package Image
        byte[] PackageImage(int pkgId);
        string PackageImageName(int pkgId);

        initiativeSourceCounts getSourceCounts(int pkgeId);
        initiativeSummary getInitiativeSummary(int pkgeId);
        IQueryable<Initiative> GetDisplayInitiativeQueryable();
        IQueryable<dtPROG> GetDisplaydtPROGQueryable();

        /// <summary>
        /// Checks if initiative has source codes that already have SPCEVNTIDs that are not null.
        /// </summary>
        /// <param name="pkgeId"></param>
        /// <returns></returns>
        bool CheckIfInitiativeHasEventSourceCodes(int pkgeId);

        /// <summary>
        /// Assigns the given <paramref name="eventId"/> as SPCEVNTID for all source codes in the initiative.
        /// </summary>
        /// <param name="eventId"></param>
        /// <param name="pkgeId"></param>
        /// <param name="overwriteExisting">
        /// If <see langword="true"/>, will update all source codes in initiative, else only those without SPCEVNTIDs already
        /// </param>
        /// <returns></returns>
        genericResponse LinkPackageSourceCodesToEvent(
            int eventId,
            int pkgeId,
            bool overwriteExisting);

        /// <summary>
        /// Whether to linking initiative to event is allowed for this user, project
        /// </summary>
        /// <returns></returns>
        bool CanLinkEvent();
    }
}