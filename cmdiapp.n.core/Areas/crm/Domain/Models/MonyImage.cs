﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Data;
using System.Linq;
using System.Web.Mvc;
using System.Data.Linq;
using System.ComponentModel.DataAnnotations.Schema;

using cmdiapp.n.core.Domain.ViewModels;
using cmdiapp.n.core.Areas.crm.Domain.Data;
namespace cmdiapp.n.core.Areas.crm.Domain.Models
{
    public class MonyImage : _entityBase_crm, iItemType
    {
        [Key]
        [Column]
        public int MONYimageID { get; set; }
        [Column]
        public int? FundID { get; set; }
        [Column]
        public DateTime BatchDTE { get; set; }
        [Column]
        public string BatchNO { get; set; }
        [Column]
        public Int16 Seq { get; set; }
        [Column]
        public char ImageType { get; set; }
        [Column]
        public DateTime CreatedAt { get; set; }
        [Column]
        public byte[] checkImage { get; set; }
        [Column]
        public int? ref_id { get; set; }
    }

    public class CheckImageId
    {
        public int MONYimageID { get; set; }
        public Int16 Seq { get; set; }
        public bool isLinked { get; set; }
        public int cagebatchdtlid { get; set; }
    }

    public class CCImage
    {
        public int MONYCCBATID { get; set; }
        public string PREFIX { get; set; }
        public string FNAME { get; set; }
        public string MNAME { get; set; }
        public string LNAME { get; set; }
        public string SUFFIX { get; set; }
        public string STREET { get; set; }
        public string ADDR1 { get; set; }
        public string ADDR2 { get; set; }
        public string CITY { get; set;  }
        public string STATE { get; set; }
        public string ZIP { get; set; }
        public string NAME { get; set; }
        public string ADDR { get; set; }
        public string CARDNO { get; set; }
        public string EXPDTE { get; set; }
        public decimal AMT { get; set; }
        public string STATUS { get; set; }
        public string APVCODE { get; set; }
        public string IDSTAMP { get; set; }
        public string RESPMSG { get; set; }
        public bool RECURRED { get; set; }
        public string RECURENDDTE { get; set; }
        public int? WEBGIFTID { get; set; }
        public bool isLinked { get; set; }
        public Int16? Seq { get; set; }
        public Int64? PID { get; set; }
        public string OCCUPATION { get; set; }
        public string EMPLOYER { get; set; }
        public string SRCECODE { get; set; }
        public string PHNNO { get; set; }
        public string EMAIL { get; set; }
    }
}