﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Data;
using System.Linq;
using System.Web.Mvc;
using System.Data.Linq;
using System.ComponentModel.DataAnnotations.Schema;
using cmdiapp.n.core.Domain.ViewModels;
using cmdiapp.n.core.Areas.crm.Domain.Data;

namespace cmdiapp.n.core.Areas.crm.Domain.Models
{
    public class v_people_note : _entityBase_crm, iItemType
    {
        [Column]
        [Key]
        public int ACTHISTID { get; set; }

        [Column]
        public int PID { get; set; }

        [Column]
        public string SUBJECT { get; set; }

        [Column]
        public string NOTE { get; set; }

        [Column]
        public DateTime? HISTDATE { get; set; }
        
        //[NotMapped]
        //public Int16 ACTTYPEID	{ get; set; }
        //[NotMapped]
        //public Int16 PURPOSEID { get; set; }
        //[NotMapped]
        //public Int16 PRIORITYID { get; set; }
        //[NotMapped]
        //public Int16? SCHEDBY_UID { get; set; }
        //[NotMapped]
        //public Int16? SCHEDFOR_UID { get; set; }
        //[NotMapped]
        //public Int16? DONEBY_UID { get; set; }
        //[NotMapped]
        //public Int16? CurrentUSER_UID { get; set; }
        //[NotMapped]
        //public Byte KeepAsHistory { get; set; }

    }
}