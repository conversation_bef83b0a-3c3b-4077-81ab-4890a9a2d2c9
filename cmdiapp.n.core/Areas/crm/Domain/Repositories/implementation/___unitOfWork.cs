﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Data.Entity;
using System.Linq;
using System.Web;
using System.Web.Mvc;

namespace cmdiapp.n.core.Areas.crm.Domain.Repositories
{
    public class ___unitOfWork : I___unitOfWork
    {
        private I__DBFactory _DBFactory;
        private DbContext _db;

        public ___unitOfWork(I__DBFactory databaseFactory)
        {
            _DBFactory = databaseFactory;
        }

        protected DbContext Database
        {
            get { return _db ?? (_db = _DBFactory.getContext()); }
        }

        public void commit()
        {
            Database.SaveChanges();
        }
    }
}