﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.Mvc;

using cmdiapp.n.core.Domain.Models;
using cmdiapp.n.core.Areas.query.Domain.Models;

using cmdiapp.n.core.Domain.Data;
using cmdiapp.n.core.Library;


namespace cmdiapp.n.core.Areas.query.Presentation.Controllers
{
    [Authorize]
    [RouteArea("query", AreaPrefix ="query/search")]
    public class SearchController : Controller
    {

        private I_entity _entity;

        public SearchController(I_entity entity)
        {
            _entity = entity;
        }

        [HttpGet, Route("hello")]
        public string hello()
        {
            return "hello";
        }
                
        [HttpGet, Route]
        public ActionResult Index(string qDefName, string iconCode, string udlo, string jsName)
        {
            // eg. http://demo1.cmdi2.com/query/search?qdefname=crm_peopleSearch&udlo=y
            // eg. http://demo1.cmdi2.com/navi?udlo=y&url=/query/search?qdefname=crm_peopleSearch
            // eg. http://demo1.cmdi2.com/Report/Run?reportId=3&udlo=y

            pageInfo _pageInfo = new pageInfo() { title = "Query Builder" };

            if (udlo == "y")
                _pageInfo.LayoutPage = "~/Views/Shared/_LayoutBlank.cshtml";
            else
                _pageInfo.LayoutPage = "";

            _pageInfo.pageData1 = (qDefName.StartsWith("report_") ? "report" : qDefName);
            _pageInfo.pageData2 = System.Guid.NewGuid().ToString().Substring(0, 5);  // Random ID
            _pageInfo.pageData3 = (qDefName.StartsWith("report_") ? qDefName.Substring(7,qDefName.Length-7) : "");  // Report ID
            _pageInfo.pageData4 = (qDefName.StartsWith("report_") ? "false" : "true");  // Server-side Paging, Sorting and Filtering
            _pageInfo.pageData5 = iconCode != null ? iconCode : "";  // Let us store IconCode for the report Here : Not applicable for other Searches

            //JS File Name : We do not need for the Report + New Distribution feature...
            _pageInfo.pageData6 = (string.IsNullOrEmpty(jsName) ? "Empty.js" : jsName + ".js"); 

            //For ToolTip Identifier Info..
            tooltipDef _record = _entity.Single<tooltipDef>(a => a.qDefName.ToUpper() == qDefName.ToUpper());

            if(_record != null && _record.tooltipIdentifier != null)
                _pageInfo.pageData7 = _record.tooltipIdentifier;
            else
                _pageInfo.pageData7 = "";
            
            //View located at : Web Project : Areas-Query-Search-Index.cshtml Page
            return View("Index", _pageInfo);

        }

        //Used for Voter Search
        [HttpGet, Route("IndexV")]
        public ActionResult IndexV(string udlo)
        {
            pageInfo _pageInfo = new pageInfo() { title = "Voter Search" };

            if (udlo == "y")
                _pageInfo.LayoutPage = "~/Views/Shared/_LayoutBlank.cshtml";
            else
                _pageInfo.LayoutPage = "";

            _pageInfo.pageData1 = "crm_voterSearch";
            return View("IndexV", _pageInfo);

        }



        /* NOT USED
        public ActionResult Index_Trial1(string qDefName, string udlo)
        {
            q_defOptions qDefOptions = new q_defOptions() { qDefName = qDefName };
            return View(qDefOptions);
        }
         */
    }
}
