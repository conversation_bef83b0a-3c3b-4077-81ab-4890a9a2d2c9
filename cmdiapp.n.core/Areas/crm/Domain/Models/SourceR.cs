﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Data;
using System.Linq;
using System.Web.Mvc;
using System.Data.Linq;
using System.ComponentModel.DataAnnotations.Schema;

using cmdiapp.n.core.Domain.ViewModels;
using cmdiapp.n.core.Areas.crm.Domain.Data;

namespace cmdiapp.n.core.Areas.crm.Domain.Models
{
    // (view)v_srce_pkge_prog  -  Used to list all source codes along with package and program ]]

    [Table("v_srce_pkge_prog")]
    public class SourceR : _entityBase_crm, iItemType
    {
        [Column]
        [Key]
        public int SRCEID { get; set; }

        [Column]
        public string SRCECODE { get; set; }

        [Column]
        public string SRCEDESCRIP { get; set; }

        [Column]
        public int PKGEID { get; set; }

        [Column]
        public string PKGECODE { get; set; }

        [Column]
        public string PKGEDESCRIP { get; set; }

        [Column]
        public short PROGID { get; set; }

        [Column]
        public string PROGTYPE { get; set; }

        [Column]
        public string PROGDESCRIP { get; set; }

    }    
}
