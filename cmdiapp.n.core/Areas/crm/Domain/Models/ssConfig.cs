﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Data;
using System.Linq;
using System.Web.Mvc;
using System.Data.Linq;
using System.ComponentModel.DataAnnotations.Schema;
using cmdiapp.n.core.Areas.crm.Domain.Data;

namespace cmdiapp.n.core.Areas.crm.Domain.Models
{
    [Table("ssCONFIG")]
    public class ssCONFIG : _entityBase_crm
    {
        [Column]
        [Key]
        public int CONFIGID { get; set; }

        [Column]
        public string CONFIGCODE { get; set; }

        [Column]
        public string CONFIGDESC { get; set; }

        [Column]
        public string CONFIGVALUE { get; set; }
    }
}