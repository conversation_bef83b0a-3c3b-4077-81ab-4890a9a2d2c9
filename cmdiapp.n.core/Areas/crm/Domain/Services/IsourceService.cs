﻿using System.Collections.Generic;
using System.Web.Mvc;

using cmdiapp.n.core.Areas.crm.Domain.Models;
using cmdiapp.n.core.Areas.crm.Domain.ViewModels;
using cmdiapp.n.core.Areas.crm.Domain.Repositories;
using cmdiapp.n.core.Domain.ViewModels;

namespace cmdiapp.n.core.Areas.crm.Domain.Services
{
    public interface IsourceService
    {
        bool check_unqiueness(SOURCE _SOURCE);
        bool check_record_exists(int sourceid);

        List<source_lineCHARTdata> getSourceLineChartData(string sSTARTdt, string sENDdt, int PkgeID, int SrceID);
        List<_pieCHARTdata> getPieChartData(string _sql);
        List<_interactionLineCHARTdata> get_Interaction_LineChartData(string _sql);

        void Add(SOURCE _SOURCE);
        void Update(SOURCE _SOURCE);
        void Delete(SOURCE _SOURCE);

        List<pmSPCEVNT> get_all_evntCode();

        string get_initiativeFromSourceID(int sourceID);
        pmSPCEVNT get_evntFromSourceID(int sourceID);
        string get_initiativeFromPkgeID(int packageID);
        SOURCE get_source_details(int sourceid);
        ActivityTotalsFinal get_source_activity_details(int sourceid, int packageid);
        DonationTotalsFinal get_source_donation_details(int sourceid, int packageid);

        List<SOURCE_ext> get_all_sources(string where, string orderBy, int pageSize, int pageNo);
        void applyCostInPkge(string changes, int packgID);

    }
}