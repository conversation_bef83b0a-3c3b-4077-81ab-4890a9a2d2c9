﻿using cmdiapp.n.core.Areas.crm.Domain.Data;
using System.ComponentModel.DataAnnotations.Schema;
using System.Runtime.Serialization;

namespace cmdiapp.n.core.Areas.crm.Domain.Models
{
    [Table("x_va_offices")]
    [DataContract]
    public class VirginiaOffice : _entityBase_crm
    {
        [Column("id")]
        [DataMember(Name = "id")]
        public int Id { get; set; }

        [Column("name")]
        [DataMember(Name = "name")]
        public string Name { get; set; }

        [Column("officeTypeId")]
        [DataMember(Name = "officeTypeId")]
        public int OfficeTypeId { get; set; }
    }
}