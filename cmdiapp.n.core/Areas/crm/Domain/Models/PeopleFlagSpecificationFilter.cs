﻿using cmdiapp.n.core.Areas.query.Domain.Models;
using System;

namespace cmdiapp.n.core.Areas.crm.Domain.Models
{
    public class PeopleFlagSpecificationFilter : IExternalApiSpecificationFilter
    {
        private const string FlagFilterId = "pspg_flag";
        private const int DateOnAfterIndex = 2;
        private const int DateOnBeforeIndex = 3;
        private const int DateBetweenIndex = 4;

        public string Flags { get; }

        public PeopleFlagSpecificationFilter(string flags)
        {
            Flags = flags;
        }

        public QueryFilterInstance ConvertToQueryInstanceFilter()
        {
            var filter = new QueryFilterInstance { key = FlagFilterId };

            filter.operatorIdx = 6;
            filter.values = new string[1] { Flags };
            return filter;
        }
    }
}