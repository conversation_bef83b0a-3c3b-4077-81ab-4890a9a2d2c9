﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Data;
using System.Linq;
using System.Web.Mvc;
using System.Data.Linq;
using System.ComponentModel.DataAnnotations.Schema;
using cmdiapp.n.core.Areas.query.Domain.Models;

namespace cmdiapp.n.core.Areas.crm.Domain.Models
{
    [Table("x_vm_demographic")]
    public class vm_Demographic
    {
        [Column]
        [Key]
        public Int64 voterSeq { get; set; }

        [Column]
        public string familyID { get; set; }

        [Column]
        public string mailingFamilyID { get; set; }

        [Column]
        public string TELEPHONE { get; set; }

        [Column]
        public byte? TELEPHONEConfidenceCode { get; set; }

        [Column]
        public bool? TELEPHONECellFlag { get; set; }

        [Column]
        public DateTime? DOB { get; set; }

        [Column]
        public int? Voters_Age { get; set; }

        [Column]
        public DateTime? RegistrationDate { get; set; }

        [Column]
        public string Ethnic_Description { get; set; }

        [Column]
        public string Religion { get; set; }

        [Column]
        public string Party { get; set; }

        [Column]
        public string Gender { get; set; }

        [Column]
        public string MaritalStatus { get; set; }

        [Column]
        public byte? HHCount { get; set; }

        [Column]
        public string HHParties { get; set; }

        [Column]
        public string HHGender { get; set; }

        [Column]
        public byte? mailingHHCount { get; set; }

        [Column]
        public string mailingHHParties { get; set; }

        [Column]
        public string mailingHHGender { get; set; }

        [Column]
        public string CD2001 { get; set; }

        [Column]
        public string CD2011 { get; set; }

        [Column]
        public string SD2001 { get; set; }

        [Column]
        public string SD2011 { get; set; }

        [Column]
        public string LD2001 { get; set; }

        [Column]
        public string LS2011 { get; set; }

        [Column]
        public string County { get; set; }

        [Column]
        public string Precinct { get; set; }

        [Column]
        public int? VoteFrequency { get; set; }

        [Column]
        public string PresenceOfChildrenCode { get; set; }

        [Column]
        public string ISPSA { get; set; }

        [Column]
        public string DwellingType { get; set; }

        [Column]
        public string EstimatedIncome { get; set; }

        [Column]
        public string Education { get; set; }

        [Column]
        public string OccupationGroup { get; set; }

        [Column]
        public string Occupation { get; set; }

        [Column]
        public string ReligiousContributorInHome { get; set; }

        [Column]
        public string PoliticalContributerInHome { get; set; }
    }


    [Table("x_vm_voter")]
    public class vm_Voter
    {
        [Column]
        [Key]
        public Int64 voterSeq { get; set; }

        [Column]
        public string voterId { get; set; }

        [Column]
        public string voterStateId { get; set; }

        [Column]
        public string FNAME { get; set; }
         
        [Column]
        public string MNAME { get; set; }
         
        [Column]
        public string LNAME { get; set; }
         
        [Column]
        public string SUFFIX { get; set; }
         
        [Column]
        public string CountyFIPS { get; set; }

        [Column]
        public string ADDR1 { get; set; }

        [Column]
        public string ADDR2 { get; set; }
         
        [Column]
        public string CITY { get; set; }

        [Column]
        public string STATE { get; set; }

        [Column]
        public string ZIP { get; set; }

        [Column]
        public string censusTract { get; set; }

        [Column]
        public string censusBlockGroup { get; set; }

        [Column]
        public string censusBlock { get; set; }
         
        [Column]
        public string mailingADDR1 { get; set; }

        [Column]
        public string mailingADDR2 { get; set; }

        [Column]
        public string mailingCITY { get; set; }

        [Column]
        public string mailingSTATE { get; set; }

        [Column]
        public string mailingZIP { get; set; }

    }
    
    [Table("x_vm_listInfo")]
    public class vm_ListInfo
    {
        [Column]
        [Key]
        public int listId { get; set; }

        [Column]
        public int? recCnt { get; set; }

        [Column]
        public DateTime? fileRecvDate { get; set; }

        [Column]
        public DateTime? statusDate { get; set; }

        [Column]
        public string status { get; set; }

        [Column]
        public string metaData { get; set; }

        [Column]
        public string LandL_listId { get; set; }

        [Column]
        public string LandL_uuId { get; set; }

        [Column]
        public string custName { get; set; }

        [Column]
        public string listName { get; set; }

        [Column]
        public string userName { get; set; }

        [Column]
        public int? recCount_shouldBe { get; set; }

        [Column]
        public int? recPurchased { get; set; }

        [Column]
        public decimal? amountDue { get; set; }
    }
    
    [Table("x_vm_voteHistory")]
    public class vm_VoteHistory
    {
        [Column]
        [Key]
        public int voterSeq { get; set; }

        [Column]
        [Key]
        public string ElectionType { get; set; }
        
        [Column]
        [Key]
        public DateTime? ElectionDate { get; set; }

        [Column]
        public string Voting { get; set; }
        
    }

    [Table("x_vm_jt_list_voter")]
    public class vm_jt_Listvoter
    {
        [Column]
        [Key]
        public int listId { get; set; }
        [ForeignKey("listId")]
        public virtual vm_ListInfo vm_ListInfo { get; set; }

        [Column]
        [Key]
        public int voterSeq { get; set; }
        [ForeignKey("voterSeq")]
        public virtual vm_Voter vm_Voter { get; set; }

        [Column]
        public int Walking_List_Sequence { get; set; }

    }

    public class CongDistData 
    {
        public string STATE { get; set; }

        public string CD2011 { get; set; }
                
        public string COMBINED
        {
            get
            {
                return (STATE == null ? CD2011 : STATE + " : " + CD2011);

            }
        }
                
    }

    public class StateDistData
    {
        public string STATE { get; set; }

        public string SD2011 { get; set; }

        public string COMBINED
        {
            get
            {
                return (STATE == null ? SD2011 : STATE + " : " + SD2011);

            }
        }
    }

    public class StateHouseAsslyDistData
    {
        public string STATE { get; set; }

        public string LS2011 { get; set; }

        public string COMBINED
        {
            get
            {
                return (STATE == null ? LS2011 : STATE + " : " + LS2011);

            }
        }
    }

    public class StatePrecinctData
    {
        public string STATE { get; set; }

        public string Precinct { get; set; }

        public string COMBINED
        {
            get
            {
                return (STATE == null ? Precinct : STATE + " : " + Precinct);

            }
        }
    }

    public class StateCountyData
    {
        public string STATE { get; set; }

        public string County { get; set; }

        public string COMBINED
        {
            get
            {
                return (STATE == null ? County : STATE + " : " + County);

            }
        }
    }

    public class CityData
    {
        public string STATE { get; set; }

        public string City { get; set; }

        public string COMBINED
        {
            get
            {
                return (STATE == null ? City : STATE + " : " + City);

            }
        }
    }

    public class ZipData
    {
        public string STATE { get; set; }

        public string Zip { get; set; }

        public string COMBINED
        {
            get
            {
                return (STATE == null ? Zip : STATE + " : " + Zip);

            }
        }
    }
    
    public class SearchVoterData
    {
        public string[] SELSTATES { get; set; }
        public string[] SELCONGDISTS { get; set; }
        public string[] SELSTATESENATES { get; set; }
        public string[] SELSTATEHASSYS { get; set; }
        public string[] SELSTATEPRECS { get; set; }
        public string[] SELSTATECNTYS { get; set; }
        public string[] SELCITYS { get; set; }
        public string[] SELZIPS { get; set; }
        public string[] SELFIRSTNAMES { get; set; }
        public string[] SELLASTNAMES { get; set; }
        public string[] SELGENDERS { get; set; }
        public string[] SELMARSTATUSS { get; set; }
        public string[] SELAGEGROUPS { get; set; }
        public string[] SELOCCUPATIONS { get; set; }
        public string[] SELESTINCOMES { get; set; }
        public string[] SELPARTYS { get; set; }
        public string[] SELHHPARTYS { get; set; }
        public string[] SELVMLISTS { get; set; }
        public string[] SELSUPVMLISTS { get; set; }
        public string[] SELELECTIONS { get; set; }
        public string[] SELELECTIONYEARS { get; set; }
        //Bool
        public bool VOTERWPHONE { get; set; }
        //Int or string
        public string VOTERID { get; set; }
        public string HHCOUNT { get; set; }
        public string VOTEFREQ { get; set; }
        //Date Range
        public DateTime? REGSDTE { get; set; }
        public DateTime? REGEDTE { get; set; }
        //For Kendo Grid Page Management
        public string searchText { get; set; }
        public int? page { get; set; }
        public int? pageSize { get; set; }
        public int? skip { get; set; }
        public int? take { get; set; }

        public List<searchSortOpt> sort { get; set; }

        #region [ Read-only Properties ]

        public string searchText_
        {
            get
            {
                string lc_searchText = (searchText ?? "");
                lc_searchText = lc_searchText.Trim().Replace("'", "''''");     // Double-Escape Single-Quote that is passed as a SQL-SELECT String.
                lc_searchText = Library.util.kill_sqlBlacklistWord(lc_searchText);

                return lc_searchText;
            }
        }

        public int page_ { get { return (page == null || page.Value == 0 ? 1 : page.Value); } }

        public int pageSize_ { get { return (pageSize == null || pageSize.Value == 0 ? 10 : pageSize.Value); } }

        public string sortOption1_
        {
            get
            {
                string sortField = (sort != null ? sort[0].field : "");
                string sortDir = (sort != null ? sort[0].dir : "");

                string _sortOptions = "";
                if (!string.IsNullOrEmpty(sortField))
                    _sortOptions = sortField;
                if (!string.IsNullOrEmpty(sortField) && !string.IsNullOrEmpty(sortDir))
                    _sortOptions = _sortOptions + " " + sortDir;

                return _sortOptions;
            }
        }
        #endregion

                       
    }

    public class TempStorageData
    {
        public string[] TEMPSTORAGE { get; set; }
    }

    public class ItemWithListName
    {
        public string Item { get; set; }
        public string ItemList { get; set; }

    }

    public class CongDistWithCntyData
    {
        public string[] SELSTATES { get; set; }
        public string[] SELCONGDISTS { get; set; }
        public string[] SELSTATECNTYS { get; set; }
    }


}