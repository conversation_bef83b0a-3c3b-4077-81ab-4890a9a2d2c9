﻿using cmdiapp.n.core.Areas.crm.Domain.Data;
using cmdiapp.n.core.Areas.crm.Domain.Models;
using cmdiapp.n.core.Areas.crm.ViewModels;
using cmdiapp.n.core.Domain.ViewModels;
using cmdiapp.n.core.Library;
using System;
using System.Collections.Generic;
using System.Data.Entity;
using System.Linq;
using System.Threading.Tasks;

namespace cmdiapp.n.core.Areas.crm.Domain.Services.implementation
{
    public class IllinoisReportService : IIllinoisReportService
    {
        private readonly I_entity_crm _entity;
        private readonly IErrorLogger _errorLogger;

        public IllinoisReportService(
            I_entity_crm entity,
            IErrorLogger errorLogger)
        {
            _entity = entity;
            _errorLogger = errorLogger;
        }

        public genericResponse Delete(int id)
        {
            try
            {
                var existing = _entity.Single<IllinoisReport>(report => report.Id == id);
                if (existing != null)
                {
                    _entity.Delete(existing);
                    _entity.CommitChanges();
                }
                return new genericResponse() { success = true };
            }
            catch (Exception ex)
            {
                _errorLogger.Log(
                    $"Failed to get IllinoisReport (id: {id}) due to {ex.GetType().Name}: {ex.Message}");
                return new genericResponse()
                {
                    success = false,
                    message = "An error occurred. Unable to delete report."
#if DEBUG
                    ,messageKey = $"{ex.GetType().Name}: ${ex.Message}"
#endif
                };
            }
        }

        public Task<List<IllinoisReportDisplay>> GetAllAsync(int filerinfoId)
        {
            try
            {
                return (from report in _entity.All<IllinoisReport>()
                        join reportType in _entity.All<IllinoisReportType>() on report.ReportTypeId equals reportType.Id
                        where report.FilerInfoId == filerinfoId
                        orderby report.FilingDate descending
                        select new IllinoisReportDisplay
                        {
                            Id = report.Id,
                            FilerInfoId = report.FilerInfoId,
                            FilingDate = report.FilingDate,
                            ReportPeriodStart = report.ReportPeriodStart,
                            ReportPeriodEnd = report.ReportPeriodEnd,
                            TotalDebts = report.TotalDebts,
                            TotalInkind = report.TotalInkind,
                            TotalExpenditures = report.TotalExpenditures,
                            TotalReceipts = report.TotalReceipts,
                            Invest = report.Invest,
                            EndFundsAvail = report.EndFundsAvail,
                            isAmendment = report.IsAmendment,
                            reasonForAmendment = report.reasonForAmendment,
                            CreatedOn = report.CreatedOn,
                            ReportType = reportType.Name,
                            ReportTypeCode = reportType.Code
                        }).ToListAsync();
            }
            catch (Exception ex)
            {
                _errorLogger.Log($"Failed to get IllinoisReports due to {ex.GetType().Name}: {ex.Message}");
                return Task.FromResult(new List<IllinoisReportDisplay>());
            }
        }

        public Task<List<IllinoisReportType>> GetReportTypesAsync()
        {
            return _entity.All<IllinoisReportType>().ToListAsync();
        }
    }
}