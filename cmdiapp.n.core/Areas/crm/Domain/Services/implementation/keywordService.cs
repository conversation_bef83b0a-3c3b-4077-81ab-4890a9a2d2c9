﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

using Ninject;
using Ninject.Web.Mvc;

using cmdiapp.n.core.Core.App_Start;
using cmdiapp.n.core.Areas.crm.Domain.Models;
using cmdiapp.n.core.Areas.crm.Domain.ViewModels;
using cmdiapp.n.core.Areas.crm.Domain.Repositories;
namespace cmdiapp.n.core.Areas.crm.Domain.Services
{
    public class keywordService : IkeywordService
    {
        private I___unitOfWork _unitOfWork;
        private I__DBFactory _dbFactory;
        private IkeywordRepository _keywordRepository;

        public keywordService(I___unitOfWork unitOfWork, I__DBFactory dbFactory, IkeywordRepository KeywordRepository)
        {
            _unitOfWork = unitOfWork;
            _dbFactory = dbFactory;
            _keywordRepository = KeywordRepository;
        }

        public int getKwrdID(string kwrdDesc)
        {
            // take care of apostrophe
            string sql = String.Format("SELECT * FROM dmKWRD WHERE UPPER(KWRD) = '{0}'", kwrdDesc.ToUpper());
            return _dbFactory.getContext().Database.SqlQuery<dmKWRD>(sql).FirstOrDefault().KWRDID;

        }

        public dmKWRD get_keyword(int kwrdid)
        {
            string sql = string.Format("SELECT * FROM dmKWRD WHERE KWRDID={0}", kwrdid);
            var q = _dbFactory.getContext().Database.SqlQuery<dmKWRD>(sql);
            return q.FirstOrDefault();
        }

        public bool check_unqiueness(dmKWRD _dmKWRD)
        {
            // take care of apostrophe
            string v = _dmKWRD.KWRD.Replace("'", "''");
            string sql = "";

            if (_dmKWRD.KWRDID > 0)   // Existing Record
                sql = String.Format("SELECT Count(*) as recordCount FROM dmKWRD WHERE UPPER(KWRD) = '{0}' AND KWRDID <> {1}", v.ToUpper(), _dmKWRD.KWRDID);
            else
                sql = String.Format("SELECT Count(*) as recordCount FROM dmKWRD WHERE UPPER(KWRD) = '{0}'", v.ToUpper());

            int isZero = _dbFactory.getContext().Database.SqlQuery<int>(sql).FirstOrDefault();

            return (isZero == 0) ? true : false;
        }

        public int get_kwrdId(dmKWRD _dmKWRD)
        {
            // take care of apostrophe
            string v = _dmKWRD.KWRD.Replace("'", "''");
            string sql = "";

            sql = String.Format("SELECT * FROM dmKWRD WHERE KWRD = '{0}'", v);

            var q = _dbFactory.getContext().Database.SqlQuery<dmKWRD>(sql);
            return q.ToList().FirstOrDefault().KWRDID;
        }

        public bool check_record_exists(int kwrdid)
        {
            //string sql = string.Format("SELECT * FROM jtKWRD WHERE KWRDID='{0}'", kwrdid);
            //var q = _dbFactory.getContext().Database.SqlQuery<jtKWRD>(sql);
            //return q.ToList().Count > 0 ? true : false;

            //Check in jtFLAG Table
            string sql = string.Format("SELECT Count(*) as recordCount FROM jtKWRD WHERE KWRDID={0}", kwrdid);
            var q = _dbFactory.getContext().Database.SqlQuery<UniquenessCheck>(sql);
            int isZero = q.FirstOrDefault().recordCount;
            return (isZero > 0) ? true : false;


        }

        public void Add(dmKWRD _dmKWRD)
        {
            _keywordRepository.Add(_dmKWRD);
            _unitOfWork.commit();
        }

        public void Update(dmKWRD _dmKWRD)
        {
            _keywordRepository.Update(_dmKWRD);
            _unitOfWork.commit();
        }

        public void Delete(dmKWRD _dmKWRD)
        {
            _keywordRepository.Delete(_dmKWRD);
            _unitOfWork.commit();
        }

        //Read all Keywords
        public List<dmKWRD> read_all_keywords()
        {
            return _keywordRepository.All().ToList();
        }

        public List<dmKWRD_ext> get_all_keywords(string where, string orderBy, int pageSize, int pageNo)
        {
            string sql = (!string.IsNullOrEmpty(where) ? string.Format("SELECT * FROM dmKWRD WHERE {0}", where) : "SELECT * FROM dmKWRD");

            var q = _dbFactory.getContext().Database.SqlQuery<dmKWRD_ext>(String.Format("EXEC dbo.get_pagedDataSet '{0}', '{1}', {2}, {3}", sql, orderBy, pageSize, pageNo));
            
            return q.ToList();
        }

        public string MassDeleteKwrd(int KwrdId, int UID, int maxAllowed)
        {
            var q = _dbFactory.getContext().Database.SqlQuery<SPResult>(String.Format("EXEC dbo.bt_remove_kwrd_and_itsRelated {0},{1},{2}", KwrdId, UID, maxAllowed));
            return q.FirstOrDefault().RESULT;
        }

    }
}