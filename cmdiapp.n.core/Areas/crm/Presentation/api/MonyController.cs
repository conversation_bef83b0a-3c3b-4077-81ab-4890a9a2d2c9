﻿using System;
using System.Net;
using System.Net.Http;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.Http;
using System.Data;
using System.Data.Common;

using System.IO;
using System.Web.Caching;

using Ninject;
using Ninject.Web.Mvc;

using cmdiapp.n.core.Core.App_Start;
using cmdiapp.n.core.Domain.ViewModels;
using cmdiapp.n.core.Areas.crm.Core;
using cmdiapp.n.core.Areas.crm.Domain.Models;
using cmdiapp.n.core.Areas.crm.Domain.ViewModels;
using cmdiapp.n.core.Areas.crm.Domain.Data;
using cmdiapp.n.core.Areas.crm.Domain.Repositories;
using cmdiapp.n.core.Areas.crm.Domain.Services;
using cmdiapp.n.core.Areas.dataService.Domain.Data;
using cmdiapp.n.core.Areas.dataService.Domain.Models;
using cmdiapp.n.core.Areas.dataService.Domain.Services;
using cmdiapp.n.core.Library;
using cmdiapp.n.core.Presentation.Controllers;
using System.ServiceModel;
using cmdiapp.n.core.ccServiceAdmin;
using cmdiapp.n.core.Domain.Models;
using cmdiapp.n.core.Core;
using cmdiapp.n.core.Domain.Data;
using cmdiapp.n.core.Areas.query.Domain.Models;
using System.Net.Http.Headers;
using System.Web.Http.Results;
using AutoMapper;
using cmdiapp.n.core.Areas.query;
using cmdiapp.n.core.Areas.crm.ViewModels;
using cmdiapp.n.core._Presentation.Attributes;
using System.Web.Script.Serialization;
using System.Drawing.Imaging;
using System.Drawing;
using DocumentFormat.OpenXml.Office2010.Excel;
using Org.BouncyCastle.Asn1.Mozilla;
using System.Data.Linq;

namespace cmdiapp.n.core.Areas.crm.Presentation.Controllers.api
{
    ///  (controller)Mony - Mony

    [Authorize]
    public class MonyController : ApiController
    {
        #region [ Declaration ]
        // Added by Lydia 2013-10-25
        public class MONYADJUST
        {
            public int MID { get; set; }
            public string ADJTYPE { get; set; }
            public DateTime ADJDTE { get; set; }
            public decimal ADJAMT { get; set; }
            public int ADJFUNDID { get; set; }
            public int ADJPID { get; set; }
        }

        // Added by Lydia 2013-10-25
        private class result_z_do_adjustment
        {
            public int MID { get; set; }
        }

        // Added by Lydia 2013-10-26
        private class result_z_undo_adjustment
        {
            public string Message { get; set; }
        }

        // Updated by Lydia on 2013-11-06
        private class result_wp_DelMony
        {
            public bool Succeed { get; set; }
            public string Message { get; set; }
        }
        // Added by Lydia on 2013-11-08
        public class ProgramR
        {
            public string PROGTYPE { get; set; }
            public string DESCRIP { get; set; }
            public decimal TOTAL { get; set; }
        }
        private class wp_complete_Result
        {
            public bool SUCCEED { get; set; }
            public string MESSAGE { get; set; }
        }

        public class p_stoprecur
        {
            public int MID { get; set; }
            public int WEBGIFTID { get; set; }
            public string CARDNO { get; set; }
            public decimal AMT { get; set; }
        }

        public class parm_linkAttrib
        {
            public int MID { get; set; }
            public int ATTRIBMID { get; set; }
            public string ADJTYPE { get; set; }
        }

        public class attribCount : iItemType
        {
            public int cnt { get; set; }
            public decimal? total { get; set; }
        }

        public class honmemLink
        {
            public int PID { get; set; }
            public string NAME { get; set; }
        }

        public class parm_honmemackw
        {
            public int MID { get; set; }
            public int PID { get; set; }
            public decimal AMT { get; set; }
        }

        public class result_delete_corpmatch
        {
            public bool SUCCESS { get; set; }
            public string MESSAGE { get; set; }
        }

        public class mony_export_data
        {
            public int pid { get; set; }
        }

        public class scanimage
        {
            public byte[] imagedata { get; set; }
        }

        private I_entity_crm _entity_crm;
        private I_entity _entity;
        private IdataService _dataService;
        private ImonyService _monyService;
        private readonly IReportDataService _reportService;
        //For Print Summary
        private System.Xml.XmlNode _node;
        System.Collections.Generic.List<string> ids = new System.Collections.Generic.List<string>();
        System.Collections.Generic.List<string> sqls = new System.Collections.Generic.List<string>();
        System.Collections.Generic.List<int> zeros = new System.Collections.Generic.List<int>();
        System.Xml.XmlNodeList xlist;
        System.Data.DataSet dSet;
        supportMethods _supportMethods = new supportMethods();

        private I__DBFactory _dbFactory;

        #endregion

        public MonyController()
        {
            _entity_crm = I_entityManager_crm.getEntity();
            _monyService = NinjectMVC.kernel.Get<ImonyService>();
            _reportService = NinjectMVC.kernel.Get<IReportDataService>();
            _dbFactory = NinjectMVC.kernel.Get<I__DBFactory>();
            _entity = I_entityManager.getEntity();
            _dataService = I_entityManager_ds.getService();

        }

        [HttpGet, Route("crm/api/Mony/AvailableBatchNo")]
        [apiAuthorize(AccessElement = "cmdiapp.dms.People_Money", AccessLevel = "a")]
        public string AvailableBatchNo(string date)
        {
            return _monyService.availableBatchNo(date);
        }


        [HttpGet, Route("crm/api/MonyR/Filters/{pid}")]
        [apiAuthorize(AccessElement = "cmdiapp.dms.People_Money", AccessLevel = "v")]
        public List<DataTableFilter> GetDataTableFilters(int pid)
        {
            var monys = _entity_crm.All<MONY>().Where(m => m.PID == pid);

            var sources = _entity_crm.All<SOURCE>()
                .Where(s => monys.Select(m => m.SRCEID).Distinct().ToList().Contains(s.SRCEID));
            var initiatives = _entity_crm.All<PACKAGE>()
                .Where(pkg => sources.Select(s => s.PKGEID).Distinct().ToList().Contains(pkg.PKGEID));
            var progs = _entity_crm.All<PROGRAM>()
                .Where(p => initiatives.Select(i => i.PROGID).Distinct().ToList().Contains(p.PROGID));

            var funds = _entity_crm.All<dmFUND>()
                .Where(f => monys.Select(m => m.FUNDID).Distinct().ToList().Contains(f.FUNDID));

            List<DataTableFilter> filters = new List<DataTableFilter>
            {
                new DataTableFilter
                {
                    label = "Fund",
                    options = funds
                        .Select(f => new DataTableFilterOption { label = f.FUNDCODE, value = f.FUNDCODE }).ToArray(),
                    paramName = "fundCode",
                    multiValue = true
                },
                new DataTableFilter
                {
                    label = "Source",
                    options = sources
                        .Select(s => new DataTableFilterOption { label = s.SRCECODE, value = s.SRCECODE }).ToArray(),
                    paramName = "srceCode",
                    multiValue = true
                },
                new DataTableFilter
                {
                    label = "Program",
                    options = progs.Select(p => new DataTableFilterOption { label = p.DESCRIP, value = p.PROGTYPE }).ToArray(),
                    paramName = "progType",
                    multiValue = true
                },
                DateFilterFactory<MONY>.GetDataTableFilter("Date", "date")
            };
            return filters;
        }

        [HttpPost, Route("crm/api/MonyR/Export")]
        [apiAuthorize(AccessElement = "cmdiapp.dms.People_Money", AccessLevel = "v")]
        [NotLaunchpadApiAuthorize]
        public object MonyRExport(int id,
            string fundCode = "",
            string srceCode = "",
            string progType = "",
            string date = "")
        {
            try
            {

                QueryRuntimeSort sort = new QueryRuntimeSort() { field = "BATCHDTE", dir = "desc" };
                Tuple<int, List<MonyR>> countAndResults = _monyService.getMonyRWithCount(
                            id,
                            1,
                            Query.maxRowsToExport(),
                            sort,
                            fundCode,
                            srceCode,
                            progType,
                            date
                        );
                DataTable table;
                if (session.currentDomain_project.projectType.appVersion.ToLower() == "nonprofit")
                {
                    Mapper.CreateMap<MonyR, MonyR_NP>();
                    List<MonyR_NP> results = countAndResults.Item2.Select(a => Mapper.Map<MonyR, MonyR_NP>(a)).ToList();
                    table = results.ToDataTable();
                }
                else
                    table = countAndResults.Item2.ToDataTable();

                if (table != null && table.Rows.Count > 0)
                {

                    Tuple<string, string> filenames = new QExportExcel().ToFile(table, $"Donor{id}");
                    return new
                    {
                        success = true,
                        serverFileName = filenames.Item1,
                        clientFileName = filenames.Item2
                    };
                }
                else
                {
                    return new
                    {
                        success = false,
                        message = "The query returned no results."
                    };
                }
            }
            catch (Exception ex)
            {
                return new genericResponse
                {
                    success = false,
                    message = "An error occurred.  Unable to export results.",
                    messageKey = (ex.Message ?? "") + (ex.InnerException?.Message ?? "")
                };
            }

        }

        [HttpGet, Route("crm/api/MonyR/GivingHistory/{pid}")]
        [apiAuthorize(AccessElement = "cmdiapp.dms.People_Money", AccessLevel = "v")]
        public IEnumerable<MonyGraphPoint> GetDonorGivingHistory(int pid)
        {
            return _monyService.GetDonorGivingHistory(pid);
        }

        [HttpGet, Route("crm/api/MonyR/ConsecutiveYears/{pid}")]
        [apiAuthorize(AccessElement = "cmdiapp.dms.People_Money", AccessLevel = "v")]
        public ConsecutiveDonorYears GetConsecutiveDonorYears(int pid)
        {
            return _monyService.GetConsecutiveDonorYears(pid);
        }

        [HttpGet, Route("crm/api/MonyR/Get")]
        [apiAuthorize(AccessElement = "cmdiapp.dms.People_Money", AccessLevel = "v")]
        public genericResponse GetByPid_forRead(
            int id,
            int? page,
            int? pageSize,
            string fundCode = "",
            string srceCode = "",
            string progType = "",
            string date = "")
        {
            try
            {
                int _pageSize = (pageSize == null || pageSize.Value == 0 ? 10 : pageSize.Value);
                int _pageNo = (page == null || page.Value == 0 ? 1 : page.Value);

                string sortField = HttpContext.Current.Request.QueryString["sort[0][field]"];
                string sortDir = HttpContext.Current.Request.QueryString["sort[0][dir]"];
                QueryRuntimeSort sort = new QueryRuntimeSort { dir = sortDir, field = sortField };
                Tuple<int, List<MonyR>> countAndResults = _monyService.getMonyRWithCount(
                        id,
                        _pageNo,
                        _pageSize,
                        sort,
                        fundCode,
                        srceCode,
                        progType,
                        date
                    );

                return new genericResponse()
                {
                    success = true,
                    __count = countAndResults.Item1,
                    results = countAndResults.Item2.ToList<iItemType>()
                };

            }
            catch (Exception ex)
            {
                return new genericResponse()
                {
                    success = false,
                    message = "An error occurred. Unable to get results.",
                    messageKey = (ex.Message ?? "") + (ex.InnerException?.Message ?? "")
                };
            }


        }


        #region [ crm/api/SummaryR ] GetBy PID
        [HttpGet, Route("crm/api/SummaryR/Get/{id}")]  // PID (Read)
        [apiAuthorize(AccessElement = "cmdiapp.dms.People_Money", AccessLevel = "v")]
        public List<SummaryR> GetSumByPid_forRead(int id)
        {
            string sql = "SELECT * FROM v_people_sum WHERE PID = {0} ORDER BY SEQ DESC, FUNDCODE DESC";
            return _entity_crm.getContext().Database.SqlQuery<SummaryR>(string.Format(sql, id)).ToList();
        }
        #endregion

        [HttpGet, Route("crm/api/SummaryD/Get/{pid}")]
        [apiAuthorize(AccessElement = "cmdiapp.dms.People_Money", AccessLevel = "v")]
        public SummaryD GetSummaryD(int pid)
        {
            string sql = "SELECT * FROM SUMMARYD WHERE PID = {0}";
            return _entity_crm.getContext().Database.SqlQuery<SummaryD>(string.Format(sql, pid)).FirstOrDefault();
        }

        [HttpGet, Route("crm/api/SummaryD/SoftMoney/{pid}")]
        [apiAuthorize(AccessElement = "cmdiapp.dms.People_Money", AccessLevel = "v")]
        public TotalAmountAndCount SoftMoneyDonorTotals(int pid)
        {
            IQueryable<decimal> q = _entity_crm.All<MONY>()
                .Where(m => m.PID == pid && m.SOFTMONEY == 1 && m.COUNTER == 1)
                .Select(m => m.AMT ?? 0);
            int count = q.Count();
            return new TotalAmountAndCount()
            {
                Count = count,
                // save a query compilation if no gifts
                TotalAmount = count > 0 ? q.Sum() : 0
            };
        }

        #region [ crm/api/MonyStatement ] GetBy PID
        //[HttpGet, Route("crm/api/MonyStatement/Get/{id}")]  // PID (Read)
        //[apiAuthorize(AccessElement = "cmdiapp.dms.People_Money", AccessLevel = "v")]
        //public List<MonyStatement> GetStatementByPid_forRead(int id)
        //{
        //    string sql = "EXEC dbo.iGenStatement {0}";
        //    return _entity_crm.getContext().Database.SqlQuery<MonyStatement>(string.Format(sql, id)).ToList();

        //}

        [HttpGet, Route("crm/api/MonyStatement/Filters/{pid}")]
        [apiAuthorize(AccessElement = "cmdiapp.dms.People_Money", AccessLevel = "v")]
        public List<DataTableFilter> GetStatementFilters(int pid)
        {
            var monys = _entity_crm.All<MONY>().Where(m => m.PID == pid);

            var funds = _entity_crm.All<dmFUND>()
                .Where(f => monys.Select(m => m.FUNDID).Distinct().ToList().Contains(f.FUNDID));
            // need to convert adjtypeids to byte and back 
            // since ADJTYPEID is inexplicably a tinyint in MONY table
            // but smallint in lkADJTYPE table
            var adjTypeIdsConverted = _entity_crm.All<lkADJTYPE>().Select(a => a.ADJTYPEID).ToList()
                .Select(id => Convert.ToByte(id));
            var includedIds = adjTypeIdsConverted
                .Where(id => monys.Select(m => m.ADJTYPEID).Distinct().ToList().Contains(id)).ToList()
                .Select(id => Convert.ToInt16(id));
            var adjTypes = _entity_crm.All<lkADJTYPE>()
                .Where(a => includedIds.Contains(a.ADJTYPEID));
            List<DataTableFilter> filters = new List<DataTableFilter>
            {
                new DataTableFilter
                {
                    label = "Fund",
                    options = funds
                        .Select(f => new DataTableFilterOption { label = f.FUNDCODE, value = f.FUNDCODE }).ToArray(),
                    paramName = "fundCode",
                    multiValue = true
                },
                new DataTableFilter
                {
                    label = "Adjustment Type",
                    options = adjTypes
                        .Select(a => new DataTableFilterOption { label = a.DESCRIP, value = a.DESCRIP }).ToArray(),
                    paramName = "adjType",
                    multiValue = true
                },
                DateFilterFactory<MONY>.GetDataTableFilter("Batch Date", "date"),
                DateFilterFactory<MONY>.GetDataTableFilter("Adjustment Date", "adjDate")
            };
            return filters;
        }

        [HttpGet, Route("crm/api/MonyStatement/Get")]  // PID (Read)
        [apiAuthorize(AccessElement = "cmdiapp.dms.People_Money", AccessLevel = "v")]
        public genericResponse GetStatementByPid_forRead(int id, int? page, int? pageSize,
            string fundCode = "", string adjType = "", string date = "", string adjDate = "")
        {
            try
            {
                int _pageSize = (pageSize == null || pageSize.Value == 0 ? 10 : pageSize.Value);
                int _pageNo = (page == null || page.Value == 0 ? 1 : page.Value);
                string sortField = HttpContext.Current.Request.QueryString["sort[0][field]"];
                string sortDir = HttpContext.Current.Request.QueryString["sort[0][dir]"];
                QueryRuntimeSort sort = new QueryRuntimeSort() { field = sortField, dir = sortDir };
                Tuple<int, List<MonyStatement>> countAndResults = _monyService.getMonyStatement(
                    id, _pageNo, _pageSize, sort, fundCode, adjType, date, adjDate);
                return new genericResponse()
                {
                    success = true,
                    __count = countAndResults.Item1,
                    results = countAndResults.Item2.ToList<iItemType>()
                };
            }
            catch (Exception ex)
            {
                return new genericResponse()
                {
                    success = false,
                    message = "An error occurred. Unable to get results."
                };
            }

        }
        #endregion
        [HttpPost, Route("crm/api/MonyStatement/Export")]
        [apiAuthorize(AccessElement = "cmdiapp.dms.People_Money", AccessLevel = "v")]
        [NotLaunchpadApiAuthorize]
        public object ExportMonyStatement(int id,
            string fundCode = "", string adjType = "", string date = "", string adjDate = "")
        {
            try
            {

                QueryRuntimeSort sort = new QueryRuntimeSort() { field = "ORDERNO", dir = "asc" };
                Tuple<int, List<MonyStatement>> countAndResults = _monyService.getMonyStatement(
                            id,
                            1,
                            100000,
                            sort,
                            fundCode,
                            adjType,
                            date,
                            adjDate
                        );

                DataTable table = countAndResults.Item2
                    // select display columns
                    .Select(m => new
                    {
                        ROOT = m.ROOT,
                        MID = m.MID,
                        DONOR = m.DONOR,
                        BATCHDTE = m.BATCHDTE,
                        ADJFLAG = m.ADJFLAG,
                        ADJDESC = m.ADJDESC,
                        ADJDTE = m.ADJDTE,
                        CODE = m.ACCTCODE,
                        AMOUNT = m.OAMT,
                        ADJAMT = m.ADJAMT,
                        NETAMT = m.AMT
                    }).ToList().ToDataTable();

                if (table != null && table.Rows.Count > 0)
                {

                    Tuple<string, string> filenames = new QExportExcel().ToFile(table, $"Donor{id}_Statement");
                    return new
                    {
                        success = true,
                        serverFileName = filenames.Item1,
                        clientFileName = filenames.Item2
                    };
                }
                else
                {
                    return new
                    {
                        success = false,
                        message = "The query returned no results."
                    };
                }
            }
            catch (Exception ex)
            {
                return new
                {
                    success = false,
                    message = "An error occurred.  Unable to export results."
                };
            }
        }

        #region [ crm/api/Mony/Get ] GetBy MID
        [HttpGet, Route("crm/api/Mony/Get/{id}")]  // MID (Read)
        [apiAuthorize(AccessElement = "cmdiapp.dms.People_Money", AccessLevel = "v")]
        public MONY GetByMid(int id)
        {
            return _entity_crm.Single<MONY>(a => a.MID == id);
        }
        #endregion

        #region [ crm/api/MonyTrack/Get ] GetBy MID
        [HttpGet, Route("crm/api/MonyTrack/Get/{id}")]  // MID (Read)
        [apiAuthorize(AccessElement = "cmdiapp.dms.People_Money", AccessLevel = "v")]
        public IEnumerable<MONYTRACK> GetMonyTrackByMid(int id)
        {
            /*
            string _sql = @"SELECT 
                            M.TRACKID,
                            M.MID, 
                            M.TRACKNO,
                            M.TRACKAMT,
                            ISNULL((SELECT TOP 1 V.FUNDRAISER FROM v_trackno V WHERE V.TRACKNO = M.TRACKNO), M.TRACKNO) as FUNDRAISER
                            FROM MONYTRACK M
                            WHERE M.MID =  " + id;
            return _entity_crm.getContext().Database.SqlQuery<MONYTRACK>(_sql).ToList();
            */
            return _entity_crm.All<MONYTRACK>().Where(a => a.MID == id).AsEnumerable();

        }
        #endregion

        #region [ crm/api/MonyAddi/Get ] GetBy MID
        [HttpGet, Route("crm/api/MonyAddi/Get/{id}")]  // MID (Read)
        [apiAuthorize(AccessElement = "cmdiapp.dms.People_Money", AccessLevel = "v")]
        public MONYADDI GetMonyAddiByMid(int id)
        {
            return _entity_crm.Single<MONYADDI>(a => a.MID == id);
        }
        #endregion

        #region [ crm/api/Mony/Save] Save Mony record
        [HttpPost, Route("crm/api/Mony/Save/{id}")]
        [apiAuthorize(AccessElement = "cmdiapp.dms.People_Money", AccessLevel = "e")]
        public genericResponse Save(MONY_ext mony, int id)
        {
            try
            {
                int mid = mony.MID;
                int batchid = 0;
                BATCH batch = null;
                bool IsBatchExists = false;
                DateTime batchdte = Convert.ToDateTime(mony.BATCHDTE.ToShortDateString());

                MONY _record = _entity_crm.Single<MONY>(a => a.MID == mid);

                //Added by Bhavesh after discussing with Lydia and Melissa about Diane Black issue where new Batch header was created for batch date change
                if (_record != null && _record.BATCHID != null && _record.BATCHID > 0)
                    IsBatchExists = true;

                if (_record != null && mid != 0)
                {
                    // Batch
                    if (IsBatchExists)
                    {
                        batch = _entity_crm.Single<BATCH>(a => a.BATCHID == _record.BATCHID);
                    }
                    else
                    {
                        batch = _entity_crm.Single<BATCH>(a => a.BATCHNO == mony.BATCHNO && a.BATCHDTE == batchdte);
                    }

                    if (batch == null && !IsBatchExists)
                    {
                        BATCH newbatch = new BATCH();
                        newbatch.BATCHNO = mony.BATCHNO;
                        newbatch.BATCHDTE = batchdte;
                        lkGIFTTYPE gifttype = _entity_crm.Single<lkGIFTTYPE>(a => a.GIFTTYPEID == mony.GIFTTYPEID);
                        if (gifttype != null)
                        {
                            lkBATTYPE battype = _entity_crm.Single<lkBATTYPE>(a => a.BATTYPE == gifttype.GIFTTYPE);
                            if (battype != null)
                            {
                                newbatch.BATTYPEID = battype.BATTYPEID;
                            }
                            else
                            {
                                newbatch.BATTYPEID = 4; // default to Regular
                            }
                        }
                        else
                        {
                            newbatch.BATTYPEID = 4; // default to Regular
                        }
                        newbatch.FUNDID = mony.FUNDID;
                        newbatch.BATCHCNT = 1;
                        newbatch.BATCHAMT = mony.AMT;
                        newbatch.cENTRYCNT = 1;
                        newbatch.cENTRYAMT = mony.AMT;
                        newbatch.cLOADCNT = 1;
                        newbatch.cLOADAMT = mony.AMT;
                        newbatch.LOADED = 1;
                        newbatch.CREATEDBY = crmSession.UID();
                        newbatch.cCREATEDON = DateTime.Now;
                        _entity_crm.Add(newbatch);
                        _entity_crm.CommitChanges();
                        _record.BATCHID = newbatch.BATCHID;
                    }
                    /*
                    else
                    {
                        batchid = batch.BATCHID;
                        batch.BATCHCNT += 1;
                        batch.BATCHAMT += mony.AMT;
                        _entity_crm.Update(batch);
                        _entity_crm.CommitChanges();
                    }
                    */
                    // existing record
                    if (_record.GIFTTYPEID != mony.GIFTTYPEID) {
                        lkGIFTTYPE lkgifttype = _entity_crm.Single<lkGIFTTYPE>(a => a.GIFTTYPEID == _record.GIFTTYPEID);
                        if (lkgifttype.GIFTTYPE == "HM" || lkgifttype.GIFTTYPE == "TR")
                        {
                            // check/delete Hon/Mem
                            List<MONYMEM> mm = _entity_crm.All<MONYMEM>().Where(a => a.MID == mony.MID).ToList();
                            foreach (MONYMEM hm in mm)
                            {
                                DelMonyMem((int)hm.MONYMEMID);
                            }
                            // check/delete Hon/Mem Ackw
                            lkADJTYPE adjtype = _entity_crm.Single<lkADJTYPE>(a => a.ADJTYPE == "HA");
                            List<MONY> hmack = _entity_crm.All<MONY>().Where(a => a.ORIGMID == mony.MID && a.ADJTYPEID == adjtype.ADJTYPEID).ToList();
                            foreach (MONY m in hmack)
                            {
                                DelHonMemAckw(m.MID);
                            }
                        }
                        else if (lkgifttype.GIFTTYPE == "MG" || lkgifttype.GIFTTYPE == "CM")
                        {
                            List<dtMATCH> dtmatch = _entity_crm.All<dtMATCH>().Where(a => a.MGMID == mony.MID).ToList();
                            foreach (dtMATCH mt in dtmatch)
                            {
                                deleteCorpMatch(mt.dtMATCHID);
                            }
                        }
                        _record.GIFTTYPEID = mony.GIFTTYPEID;
                    }
                    _record.UPDATING_UID = crmSession.UID();
                    _record.MONYCODE = mony.MONYCODE;
                    _record.SRCEID = mony.SRCEID;
                    _record.FUNDID = mony.FUNDID;
                    _record.CENTERID = mony.CENTERID;
                    _record.CAMPGNID = mony.CAMPGNID;
                    _record.MONYTYPEID = mony.MONYTYPEID;
                    _record.CHANNELID = mony.CHANNELID;
                    _record.BATCHDTE = batchdte;
                    _record.AMT = mony.AMT;
                    _record.CKNO = mony.CKNO;
                    // JFC or EM or WR original record must be soft credit
                    if (mony.MONYTYPEID == 30 || mony.MONYTYPEID == 31 || mony.MONYTYPEID == 82)
                    {
                        if ((mony.MONYTYPEID == 30 && (mony.ADJTYPEID == 0 || mony.ADJTYPEID == null)) ||
                            ((mony.MONYTYPEID == 31 || mony.MONYTYPEID == 82) && (mony.ADJTYPEID == 0 || mony.ADJTYPEID == null)))
                            _record.SOFTMONEY = 1;
                        else
                            _record.SOFTMONEY = 0;
                    }
                    else
                        _record.SOFTMONEY = mony.SOFTMONEY;
                    _record.ADJTYPEID = mony.ADJTYPEID;
                    _record.ADJAMT = mony.ADJAMT;
                    if (mony.ADJDTE == null)
                        _record.ADJDTE = null;
                    else
                        _record.ADJDTE = Convert.ToDateTime(Convert.ToDateTime(mony.ADJDTE).ToShortDateString());
                    _record.COMMENT = mony.COMMENT;
                    _record.UPDATEDON = DateTime.Now;
                    _record.ACKW = mony.ACKW;
                    _record.EXCEPID = mony.EXCEPID;
                    if (mony.EXCEPDTE == null)
                        _record.EXCEPDTE = null;
                    else
                        _record.EXCEPDTE = Convert.ToDateTime(Convert.ToDateTime(mony.EXCEPDTE).ToShortDateString());
                    _record.TRACKNO = mony.TRACKNO;
                    if (mony.TRACKNO == null || mony.TRACKNO == 0)
                        _record.TRACKAMT = null;
                    else
                        _record.TRACKAMT = mony.TRACKAMT;
                    _record.EARMARKED = mony.EARMARKED;
                    _record.CCEXPMO = mony.CCEXPMO;
                    _record.CCEXPYR = mony.CCEXPYR;
                    _record.CCAUTHCODE = mony.CCAUTHCODE;
                    _record.CCREFNO = mony.CCREFNO;
                    if (mony.RECVDTE == null)
                        _record.RECVDTE = null;
                    else
                        _record.RECVDTE = Convert.ToDateTime(Convert.ToDateTime(mony.RECVDTE).ToShortDateString());
                    _record.MATCHID = mony.MATCHID;
                    if (mony.MATCHDTE == null)
                        _record.MATCHDTE = null;
                    else
                        _record.MATCHDTE = Convert.ToDateTime(Convert.ToDateTime(mony.MATCHDTE).ToShortDateString());
                    _record.DISCLOSED = mony.DISCLOSED;
                    _record.FECXFER = mony.FECXFER;
                    // JFC original record must be soft credit
                    //if (mony.MONYTYPEID == 30) 
                    //{
                    //    if (mony.ORIGMID == 0 || mony.ORIGMID == null)
                    //        _record.SOFTMONEY = 1;
                    //}
                    _entity_crm.Update(_record);
                    _entity_crm.CommitChanges();
                }
                else
                {

                    // Batch
                    if (IsBatchExists && _record != null)
                    {
                        batch = _entity_crm.Single<BATCH>(a => a.BATCHID == _record.BATCHID);
                    }
                    else
                    {
                        batch = _entity_crm.Single<BATCH>(a => a.BATCHNO == mony.BATCHNO && a.BATCHDTE == batchdte);
                    }

                    if (batch == null && !IsBatchExists)
                    {
                        BATCH newbatch = new BATCH();
                        newbatch.BATCHNO = mony.BATCHNO;
                        newbatch.BATCHDTE = batchdte;
                        newbatch.BATTYPEID = 4;
                        newbatch.FUNDID = mony.FUNDID;
                        newbatch.BATCHCNT = 1;
                        newbatch.BATCHAMT = mony.AMT;
                        newbatch.cENTRYCNT = 1;
                        newbatch.cENTRYAMT = mony.AMT;
                        newbatch.cLOADCNT = 1;
                        newbatch.cLOADAMT = mony.AMT;
                        newbatch.LOADED = 1;
                        newbatch.CREATEDBY = crmSession.UID();
                        newbatch.cCREATEDON = DateTime.Now;
                        _entity_crm.Add(newbatch);
                        _entity_crm.CommitChanges();
                        batchid = newbatch.BATCHID;
                    }
                    else
                    {
                        batchid = batch.BATCHID;
                        // Alf already enter Batch count and amount in Batch Entry module
                        if (!(crmSession.configValue(crmConstants.alf_nonprofit) == "Y"))
                        {
                            batch.BATCHCNT += 1;
                            batch.BATCHAMT += mony.AMT;
                        }
                        batch.cENTRYCNT += 1;
                        batch.cENTRYAMT += mony.AMT;
                        batch.cLOADCNT += 1;
                        batch.cLOADAMT += mony.AMT;
                        _entity_crm.Update(batch);
                        _entity_crm.CommitChanges();
                    }
                    _record = new MONY();
                    _record.BATCHID = batchid;
                    _record.UPDATING_UID = crmSession.UID();
                    _record.UID = crmSession.UID();
                    _record.PID = id;
                    _record.MONYCODE = mony.MONYCODE;
                    _record.SRCEID = mony.SRCEID;
                    _record.SRCTYPEID = 0;
                    _record.FUNDID = mony.FUNDID;
                    _record.CENTERID = mony.CENTERID;
                    _record.CAMPGNID = mony.CAMPGNID;
                    _record.MONYTYPEID = mony.MONYTYPEID;
                    _record.CHANNELID = mony.CHANNELID;
                    _record.BATCHNO = mony.BATCHNO;
                    _record.BATCHDTE = batchdte;
                    _record.ENTRYDTE = Convert.ToDateTime(DateTime.Now.ToShortDateString());
                    _record.AMT = mony.AMT;
                    _record.CKNO = mony.CKNO;
                    _record.COUNTER = 1;
                    // JFC or EM or WR original record must be soft credit
                    if (mony.MONYTYPEID == 30 || mony.MONYTYPEID == 31 || mony.MONYTYPEID == 82)
                    { 
                        if ((mony.MONYTYPEID == 30 && mony.ADJTYPEID != 18) ||
                            ((mony.MONYTYPEID == 31 || mony.MONYTYPEID == 82) && mony.ADJTYPEID != 19))
                            _record.SOFTMONEY = 1;
                        else
                            _record.SOFTMONEY = 0;
                    }
                    else
                        _record.SOFTMONEY = 0;
                    _record.ADJTYPEID = mony.ADJTYPEID;
                    _record.ADJAMT = mony.ADJAMT;
                    if (mony.ADJDTE == null)
                        _record.ADJDTE = null;
                    else
                        _record.ADJDTE = Convert.ToDateTime(Convert.ToDateTime(mony.ADJDTE).ToShortDateString());
                    _record.ORIGMID = mony.ORIGMID;
                    _record.UID = crmSession.UID();
                    _record.COMMENT = mony.COMMENT;
                    _record.ACKW = mony.ACKW;
                    _record.EXCEPID = mony.EXCEPID;
                    if (mony.EXCEPDTE == null)
                        _record.EXCEPDTE = null;
                    else
                        _record.EXCEPDTE = Convert.ToDateTime(Convert.ToDateTime(mony.EXCEPDTE).ToShortDateString());
                    _record.TRACKNO = mony.TRACKNO;
                    if (mony.TRACKNO == null || mony.TRACKNO == 0)
                        _record.TRACKAMT = null;
                    else
                        _record.TRACKAMT = mony.TRACKAMT;
                    _record.EARMARKED = mony.EARMARKED;
                    _record.CCEXPMO = mony.CCEXPMO;
                    _record.CCEXPYR = mony.CCEXPYR;
                    _record.CCAUTHCODE = mony.CCAUTHCODE;
                    _record.CCREFNO = mony.CCREFNO;
                    if (mony.RECVDTE == null)
                        _record.RECVDTE = null;
                    else
                        _record.RECVDTE = Convert.ToDateTime(Convert.ToDateTime(mony.RECVDTE).ToShortDateString());
                    _record.MATCHID = mony.MATCHID;
                    if (mony.MATCHDTE == null)
                        _record.MATCHDTE = null;
                    else
                        _record.MATCHDTE = Convert.ToDateTime(Convert.ToDateTime(mony.MATCHDTE).ToShortDateString());
                    _record.GIFTTYPEID = mony.GIFTTYPEID;
                    _record.DISCLOSED = mony.DISCLOSED;
                    _record.FECXFER = mony.FECXFER;
                    _entity_crm.Add(_record);
                    _entity_crm.CommitChanges();
                    mid = _record.MID;
                }

                #region [[ MonyAddi ]]
                MONYADDI monyaddi = _entity_crm.Single<MONYADDI>(a => a.MID == mid);
                if (monyaddi == null)
                {
                    monyaddi = new MONYADDI();
                    monyaddi.MID = mid;
                    monyaddi.REFERENCEID = mony.MONYADDI.REFERENCEID;
                    monyaddi.RECURRED = false;
                    _entity_crm.Add(monyaddi);
                    _entity_crm.CommitChanges();
                }
                else
                {
                    monyaddi.REFERENCEID = mony.MONYADDI.REFERENCEID;
                    _entity_crm.Update(monyaddi);
                    _entity_crm.CommitChanges();
                }
                #endregion

                #region [[ Track# ]]
                if (mid > 0)
                {
                    IEnumerable<MONYTRACK> monytrack = _entity_crm.All<MONYTRACK>().Where(a => a.MID == mid).AsEnumerable();
                    foreach (MONYTRACK mt in monytrack)
                    {
                        var m = mony.MONYTRACK.FirstOrDefault(t => t.TRACKID == mt.TRACKID);
                        if (m == null)
                        {
                            // Delete
                            _entity_crm.Delete(mt);
                        }
                    }
                    foreach (MONYTRACK _monytrack in mony.MONYTRACK)
                    {
                        if (_monytrack.TRACKID == 0)
                        {
                            // Insert
                            MONYTRACK mt = new MONYTRACK();
                            mt.MID = mid;
                            mt.TRACKNO = _monytrack.TRACKNO;
                            mt.TRACKAMT = _monytrack.TRACKAMT;
                            _entity_crm.Add(mt);

                        }
                        else
                        {
                            MONYTRACK mt = _entity_crm.Single<MONYTRACK>(t => t.TRACKID == _monytrack.TRACKID);
                            // Update 
                            mt.TRACKNO = _monytrack.TRACKNO;
                            mt.TRACKAMT = _monytrack.TRACKAMT;
                            _entity_crm.Update(mt);
                        }
                    }
                    _entity_crm.CommitChanges();
                }
                #endregion 

                #region [[ Conduit# ]]
                if (mid > 0)
                {
                    IEnumerable<MONYCONDUIT> monyconduit = _entity_crm.All<MONYCONDUIT>().Where(a => a.MID == mid).AsEnumerable();
                    foreach (MONYCONDUIT mc in monyconduit)
                    {
                        var c = mony.MONYCONDUIT.FirstOrDefault(t => t.MONYCONDUITID == mc.MONYCONDUITID);
                        if (c == null)
                        {
                            // Delete
                            _entity_crm.Delete(mc);
                        }
                    }
                    foreach (MONYCONDUIT _monyconduit in mony.MONYCONDUIT)
                    {
                        if (_monyconduit.MONYCONDUITID == 0)
                        {
                            // Insert
                            MONYCONDUIT mc = new MONYCONDUIT();
                            mc.MID = mid;
                            mc.CONDUITNO = _monyconduit.CONDUITNO;
                            mc.AMT = _monyconduit.AMT;
                            mc.FEE = _monyconduit.FEE;
                            mc.MEMO = _monyconduit.MEMO;
                            _entity_crm.Add(mc);

                        }
                        else
                        {
                            MONYCONDUIT mc = _entity_crm.Single<MONYCONDUIT>(t => t.MONYCONDUITID == _monyconduit.MONYCONDUITID);
                            if (_monyconduit.CONDUITNO == 0)
                            {
                                // Delete
                                _entity_crm.Delete(mc);
                            }
                            else {
                                // Update 
                                mc.CONDUITNO = _monyconduit.CONDUITNO;
                                mc.AMT = _monyconduit.AMT;
                                mc.FEE = _monyconduit.FEE;
                                mc.MEMO = _monyconduit.MEMO; 
                                _entity_crm.Update(mc);
                            }
                        }
                    }
                    _entity_crm.CommitChanges();
                }
                #endregion

                if (mid <= 0)
                {
                    List<iItemType> _dataset = new List<iItemType>();
                    _dataset.Add(_record);

                    genericResponse _response = new genericResponse() { success = false, __count = _dataset.Count, results = _dataset.ToList() };
                    return _response;
                }
                else
                {
                    MONY _mony = _entity_crm.Single<MONY>(a => a.MID == mid);

                    List<iItemType> _dataset = new List<iItemType>();
                    _dataset.Add(_mony);

                    genericResponse _response = new genericResponse() { success = true, __count = _dataset.Count, results = _dataset.ToList() };
                    return _response;
                } 
            }
            catch (Exception e)
            {
                genericResponse _response = new genericResponse() { success = false, __count = 0 };
                return _response;
            }
        }
        #endregion

        #region [ crm/api/Mony/Adjust ] Adjust Mony record
        [HttpPost, Route("crm/api/Mony/Adjust/{id}")]
        [apiAuthorize(AccessElement = "cmdiapp.dms.People_Money", AccessLevel = "e")]
        public genericResponse Adjust(int id, MONYADJUST monyadjust)
        {
            try
            {
                string _sql = "";
                if (monyadjust.ADJTYPE == "RM" || monyadjust.ADJTYPE == "DB" || monyadjust.ADJTYPE == "FR" ||
                    monyadjust.ADJTYPE == "CB" || monyadjust.ADJTYPE == "MF" || monyadjust.ADJTYPE == "DP")
                {
                    _sql = "exec z_do_adjustment '" + monyadjust.ADJTYPE + "'," + monyadjust.MID +
                            ",'" + monyadjust.ADJDTE.ToShortDateString() + "'," + "null" + "," + crmSession.UID() +
                            "," + "null" + "," + "null";
                }
                else if (monyadjust.ADJTYPE == "PR" || monyadjust.ADJTYPE == "CT" || monyadjust.ADJTYPE == "PC" || monyadjust.ADJTYPE == "MP")
                {
                    _sql = "exec z_do_adjustment '" + monyadjust.ADJTYPE + "'," + monyadjust.MID +
                            ",'" + monyadjust.ADJDTE.ToShortDateString() + "'," + monyadjust.ADJAMT + "," + crmSession.UID() +
                            "," + "null" + "," + "null";
                }
                else if (monyadjust.ADJTYPE == "TR")
                {
                    _sql = "exec z_do_adjustment '" + monyadjust.ADJTYPE + "'," + monyadjust.MID +
                            ",'" + monyadjust.ADJDTE.ToShortDateString() + "'," + "null" + "," + crmSession.UID() +
                            "," + monyadjust.ADJPID + "," + "null";
                }
                else if (monyadjust.ADJTYPE == "SP" || monyadjust.ADJTYPE == "RA")
                {
                    _sql = "exec z_do_adjustment '" + monyadjust.ADJTYPE + "'," + monyadjust.MID +
                            ",'" + monyadjust.ADJDTE.ToShortDateString() + "'," + monyadjust.ADJAMT + "," + crmSession.UID() +
                            "," + monyadjust.ADJPID + "," + "null";
                }
                else if (monyadjust.ADJTYPE == "RD" || monyadjust.ADJTYPE == "XF")
                {
                    _sql = "exec z_do_adjustment '" + monyadjust.ADJTYPE + "'," + monyadjust.MID +
                            ",'" + monyadjust.ADJDTE.ToShortDateString() + "'," + "null" + "," + crmSession.UID() +
                            "," + "null" + "," + monyadjust.ADJFUNDID;
                }
                else if (monyadjust.ADJTYPE == "AT" || monyadjust.ADJTYPE == "PT" || monyadjust.ADJTYPE == "DA" || monyadjust.ADJTYPE == "SU" || monyadjust.ADJTYPE == "TA" || monyadjust.ADJTYPE == "EG" )
                {
                    _sql = "exec z_do_adjustment '" + monyadjust.ADJTYPE + "'," + monyadjust.MID +
                            ",'" + monyadjust.ADJDTE.ToShortDateString() + "'," + monyadjust.ADJAMT + "," + crmSession.UID() +
                            "," + monyadjust.ADJPID + "," + "null";
                }
                else if (monyadjust.ADJTYPE == "ST")
                {
                    _sql = "exec z_do_adjustment '" + monyadjust.ADJTYPE + "'," + monyadjust.MID +
                            ",'" + monyadjust.ADJDTE.ToShortDateString() + "'," + monyadjust.ADJAMT + "," + crmSession.UID() +
                            "," + "null" + "," + monyadjust.ADJFUNDID;
                }
                else if (monyadjust.ADJTYPE == "SF")
                {
                    _sql = "exec z_do_adjustment '" + monyadjust.ADJTYPE + "'," + monyadjust.MID +
                            ",'" + monyadjust.ADJDTE.ToShortDateString() + "'," + "null" + "," + crmSession.UID() +
                            "," + monyadjust.ADJPID + "," + "null";
                }
                else if (monyadjust.ADJTYPE == "AB" || monyadjust.ADJTYPE == "EM")
                {
                    _sql = "exec z_do_adjustment '" + monyadjust.ADJTYPE + "'," + monyadjust.MID +
                            ",'" + monyadjust.ADJDTE.ToShortDateString() + "'," + monyadjust.ADJAMT + "," + crmSession.UID() +
                            "," + monyadjust.ADJPID + "," + "null";
                }

                var q = _entity_crm.getContext().Database.SqlQuery<result_z_do_adjustment>(_sql);
                result_z_do_adjustment result = q.FirstOrDefault();

                #region [[ send notification and/or email for configured client only ]]
                if (session.userSession.getConfigVal(crmConstants.NotifiOnAdj).ToUpper() == "Y")
                {
                    try
                    {
                        string _adjustsql = getAdjustsql(result.MID);
                        AdjustmentDetail _AdjustmentDetail = _entity_crm.getContext().Database.SqlQuery<AdjustmentDetail>(_adjustsql).FirstOrDefault();
                        string _content = getContentString(_AdjustmentDetail);
                        string nsql = String.Format("EXEC dbo.notific_send '{0}','{1}','{2}','{3}', '{4}', {5}", "Crimson", "Contribution Adjustment", _content, session.userSession.Email, "", session.currentDomain_project.appId);
                        var qa = _entity.getContext().Database.SqlQuery<noticProcess>(nsql).ToList();
                    }
                    catch (Exception ex)
                    {
                        //notification not sent;                        
                    }
                }

                #endregion

                genericResponse _response = new genericResponse() { success = true, __count = 1, UniqueId = result.MID };
                return _response;
            }
            catch
            {
                genericResponse _response = new genericResponse() { success = false, __count = 0 };
                return _response;
            }
        }
        #endregion
        private string getAdjustsql(int Mid)
        {
            string sql = @"SELECT 
KN.DESCRIP AS ADJUST,
N.ADJDTE,
(SELECT TOP 1 USERID FROM ssUSER WHERE UID = N.UID) AS [USER],
GETDATE() AS TIMESTAMP,
RIGHT('00000000'+RTRIM(CAST(PN.PID AS CHAR(8))),8) AS NPID,
LTRIM(LEFT(REPLACE(ISNULL(PN.PREFIX,'')+' '+
                ISNULL(PN.FNAME,'')+' '+ISNULL(PN.MNAME,'')+' '+ISNULL(PN.LNAME,'')+
                CASE WHEN ISNULL(PN.SUFFIX,'')<>'' 
                                THEN ', '+ISNULL(PN.SUFFIX,'') ELSE '' END,'  ',' '),50)) AS NNAME,
AN.STREET AS NSTREET,
AN.ADDR1 AS NADDR1,
AN.ADDR2 AS NADDR2,
LTRIM( REPLACE(
                ISNULL(AN.CITY,'') + 
                CASE WHEN ISNULL(AN.CITY,'')='' THEN '' ELSE ', ' END + 
                ISNULL(AN.STATE,'') + ' ' + ISNULL(AN.ZIP,'') +
                CASE WHEN ISNULL(AN.PLUS4,'')='' THEN '' ELSE '-'+AN.PLUS4 END,
                '  ', ' ')) AS NCISTZIP,
N.MID AS NMID,
N.BATCHDTE AS NBATCHDTE,
N.ENTRYDTE AS NENTRYDTE,
SN.SRCECODE AS NSOURCE,
FN.FUNDDESC AS NFUND,
TN.DESCRIP AS NMONYTYPE,
N.AMT AS NAMOUNT,

CASE WHEN PN.PID = PO.PID THEN NULL ELSE RIGHT('00000000'+RTRIM(CAST(PO.PID AS CHAR(8))),8) END AS OPID,
CASE WHEN PN.PID = PO.PID THEN NULL ELSE LTRIM(LEFT(REPLACE(ISNULL(PO.PREFIX,'')+' '+
                ISNULL(PO.FNAME,'')+' '+ISNULL(PO.MNAME,'')+' '+ISNULL(PO.LNAME,'')+
                CASE WHEN ISNULL(PO.SUFFIX,'')<>'' 
                                THEN ', '+ISNULL(PO.SUFFIX,'') ELSE '' END,'  ',' '),50)) END AS ONAME,
CASE WHEN PN.PID = PO.PID THEN NULL ELSE AO.STREET END AS OSTREET,
CASE WHEN PN.PID = PO.PID THEN NULL ELSE AO.ADDR1 END AS OADDR1,
CASE WHEN PN.PID = PO.PID THEN NULL ELSE AO.ADDR2 END AS OADDR2,
CASE WHEN PN.PID = PO.PID THEN NULL ELSE LTRIM( REPLACE(
                ISNULL(AN.CITY,'') + 
                CASE WHEN ISNULL(AN.CITY,'')='' THEN '' ELSE ', ' END + 
                ISNULL(AN.STATE,'') + ' ' + ISNULL(AN.ZIP,'') +
                CASE WHEN ISNULL(AN.PLUS4,'')='' THEN '' ELSE '-'+AN.PLUS4 END,
                '  ', ' ')) END AS OCISTZIP,
O.MID AS OMID,
CASE WHEN N.BATCHDTE = O.BATCHDTE THEN NULL ELSE O.BATCHDTE END AS OBATCHDTE,
CASE WHEN N.ENTRYDTE = O.ENTRYDTE THEN NULL ELSE O.ENTRYDTE END AS OENTRYDTE,
CASE WHEN N.SRCEID = O.SRCEID THEN NULL ELSE SO.SRCECODE END AS OSOURCE,
CASE WHEN N.FUNDID = O.FUNDID THEN NULL ELSE FO.FUNDDESC END AS OFUND,
CASE WHEN N.MONYTYPEID = O.MONYTYPEID THEN NULL ELSE [TO].DESCRIP END AS OMONYTYPE,
O.AMT AS OAMOUNT,
CASE WHEN ISNULL(N.ADJAMT,0) = 0 THEN NULL ELSE N.ADJAMT END AS ADJUSTAMT
FROM   MONY N
   INNER JOIN PEOPLE PN 
      LEFT JOIN ADDRESS AN ON PN.PID = AN.PID AND AN.PRIME = 1 
   ON N.PID = PN.PID 
   INNER JOIN SOURCE SN ON N.SRCEID = SN.SRCEID
   INNER JOIN dmFUND FN ON N.FUNDID = FN.FUNDID 
   LEFT JOIN lkMONYTYPE TN ON N.MONYTYPEID = TN.MONYTYPEID 
   LEFT JOIN lkADJTYPE KN ON N.ADJTYPEID = KN.ADJTYPEID 
   INNER JOIN MONY O 
   INNER JOIN PEOPLE PO
      LEFT JOIN ADDRESS AO ON PO.PID = AO.PID AND AO.PRIME = 1 
    ON O.PID = PO.PID
   ON N.ORIGMID = O.MID 
   INNER JOIN SOURCE SO ON O.SRCEID = SO.SRCEID
   INNER JOIN dmFUND FO ON O.FUNDID = FO.FUNDID 
   LEFT JOIN lkMONYTYPE [TO] ON O.MONYTYPEID = [TO].MONYTYPEID
WHERE N.MID = {0}";
            return string.Format(sql, Mid);
        }
        private string getContentString(AdjustmentDetail _AdjustmentDetail)
        {
            string _content = @"
                <!DOCTYPE html>
<html>
<style>
table {
    border-collapse: collapse;
}
table, td, th {
    border: 1px solid black;
    text-align: left;
    padding:5px;
}
</style>
<body>
<div style='width:45%;float:left'>
<h4>Adjustment Type: " + _AdjustmentDetail.ADJUST + @"</h4>
</div>
<div style='width:25%;float:left'>
<h4>User: " + _AdjustmentDetail.USER + @"</h4>
</div>
<div style='width:25%;float:left'>
<h4>Date: " + String.Format("{0:MM/dd/yyyy}", _AdjustmentDetail.TIMESTAMP) + @"</h4>
</div>
<br />
<table style='width:100%;'>
<tr>
    <th></th>
    <th>New</th> 
    <th>Old*</th>
  </tr>
  <tr>
    <td>Transaction#</td>
    <td>" + _AdjustmentDetail.NMID + @"</td>
    <td>" + _AdjustmentDetail.OMID + @"</td>
  </tr>
  <tr>
    <td>Donor</td>
    <td>" + (_AdjustmentDetail.NPID != null && _AdjustmentDetail.NPID != "" ? "#" + _AdjustmentDetail.NPID : _AdjustmentDetail.NPID) + @"<br />" + _AdjustmentDetail.NNAME + @"<br />" + _AdjustmentDetail.NSTREET + @"<br />" + _AdjustmentDetail.NADDR1 + @"<br />" + _AdjustmentDetail.NCISTZIP + @"</td>
    <td>" + (_AdjustmentDetail.OPID != null && _AdjustmentDetail.OPID != "" ? "#" + _AdjustmentDetail.OPID : _AdjustmentDetail.OPID) + @"<br />" + _AdjustmentDetail.ONAME + @"<br />" + _AdjustmentDetail.OSTREET + @"<br />" + _AdjustmentDetail.OADDR1 + @"<br />" + _AdjustmentDetail.OCISTZIP + @"</td>
  </tr>
  <tr>
    <td>Batch Date</td>
    <td>" + String.Format("{0:MM/dd/yyyy}", _AdjustmentDetail.NBATCHDTE) + @"</td>
    <td>" + String.Format("{0:MM/dd/yyyy}", _AdjustmentDetail.OBATCHDTE) + @"</td>
  </tr>
  <tr>
    <td>Entry Date</td>
    <td>" + String.Format("{0:MM/dd/yyyy}", _AdjustmentDetail.NENTRYDTE) + @"</td>
    <td>" + String.Format("{0:MM/dd/yyyy}", _AdjustmentDetail.OENTRYDTE) + @"</td>
  </tr>
  <tr>
    <td>Fund</td>
    <td>" + _AdjustmentDetail.NFUND + @"</td>
    <td>" + _AdjustmentDetail.OFUND + @"</td>
  </tr>
  <tr>
    <td>Source</td>
    <td>" + _AdjustmentDetail.NSOURCE + @"</td>
    <td>" + _AdjustmentDetail.OSOURCE + @"</td>
  </tr>
  <tr>
    <td>Type</td>
    <td>" + _AdjustmentDetail.NMONYTYPE + @"</td>
    <td>" + _AdjustmentDetail.OMONYTYPE + @"</td>
  </tr>
  <tr>
    <td>Amount</td>
    <td>" + String.Format("{0:C2}", _AdjustmentDetail.NAMOUNT) + @"</td>
    <td>" + String.Format("{0:C2}", _AdjustmentDetail.OAMOUNT) + @"<span style='float:right'>Adj.Amount: " + String.Format("{0:C2}", _AdjustmentDetail.ADJUSTAMT) + @"</span></td>
  </tr>
</table>
</body>
</html>";
            return _content.Replace("'", "''");
        }

        #region [crm/api/Mony/VoidRefund ] Void Refund Mony record]
        [HttpPost, Route("crm/api/Mony/VoidRefund/{id}")]
        [apiAuthorize(AccessElement = "cmdiapp.dms.People_Money", AccessLevel = "e")]
        public genericResponse VoidRefund(int id)
        {
            try
            {
                string _sql = "";

                _sql = "exec z_do_adjustment '" + "VO" + "'," + id +
                            "," + "null" + "," + "null" + "," + crmSession.UID() +
                            "," + "null" + "," + "null";


                var q = _entity_crm.getContext().Database.SqlQuery<result_z_do_adjustment>(_sql);
                result_z_do_adjustment result = q.FirstOrDefault();

                genericResponse _response = new genericResponse() { success = true, __count = 1 };
                return _response;
            }
            catch
            {
                genericResponse _response = new genericResponse() { success = false, __count = 0 };
                return _response;
            }


        }
        #endregion

        #region [ crm/api/Mony/UndoAdjust ] Undo Adjustment
        [HttpPost, Route("crm/api/Mony/UndoAdjust/{id}")]
        [apiAuthorize(AccessElement = "cmdiapp.dms.People_Money", AccessLevel = "d")]
        public genericResponse UndoAdjust(int id)
        {
            genericResponse _response;
            try
            {
                int mid = id;
                string _sql = "DECLARE @Message varchar(200);\n" + "exec z_undo_adjustment_v3 " + mid + "," + crmSession.UID() + ",@Message OUTPUT;\n Select @Message as MESSAGE;";
                var q = _entity_crm.getContext().Database.SqlQuery<result_z_undo_adjustment>(_sql);
                result_z_undo_adjustment result = q.FirstOrDefault();
                if (result.Message != "")
                    _response = new genericResponse() { success = false, __count = 0, message = result.Message };
                else
                    _response = new genericResponse() { success = true, __count = 0, message = result.Message };
                return _response;
            }
            catch (Exception e)
            {
                _response = new genericResponse() { success = false, __count = 0, message = "Error while undoing Adjustment!!" };
                return _response;
            }
        }
        #endregion

        #region [ crm/api/Mony/Delete ] Delete Mony record
        [HttpPost, Route("crm/api/Mony/Delete/{id}")]
        [apiAuthorize(AccessElement = "cmdiapp.dms.People_Money", AccessLevel = "d")]
        public genericResponse Delete(int id)
        {
            genericResponse _response;
            try
            {
                int mid = id;
                string _sql = String.Format(@"DECLARE @SUCCEED BIT ,@MESSAGE VARCHAR(200)
                                            EXEC  dbo.wp_DelMony {0},{1},@SUCCEED OUTPUT,@MESSAGE OUTPUT
                                            SELECT @SUCCEED AS SUCCEED, @MESSAGE AS MESSAGE", mid, crmSession.UID());
                var q = _entity_crm.getContext().Database.SqlQuery<result_wp_DelMony>(_sql);
                result_wp_DelMony result = q.FirstOrDefault();
                if (result.Succeed)
                    _response = new genericResponse() { success = true, __count = 0, message = "" };
                else
                    _response = new genericResponse() { success = false, __count = 0, message = result.Message };
                return _response;
            }
            catch (Exception e)
            {
                _response = new genericResponse() { success = false, __count = 0, message = "Error while deleting!!" };
                return _response;
            }
        }
        #endregion

        #region [ crm/api/ProgByPid ] GetBy PID
        [HttpGet, Route("crm/api/ProgByPid/Get/{id}")]  // PID (Read)
        [apiAuthorize(AccessElement = "cmdiapp.dms.People_Money", AccessLevel = "v")]
        public List<ProgramR> GetProgByPid(int id)
        {
            string sql = "select p.PROGTYPE, p.DESCRIP, SUM(m.AMT) as TOTAL " +
                        "from PROGRAM p " +
                        "inner join PACKAGE k " +
                        "inner join SOURCE s " +
                        "inner join MONY m " +
                        "inner join dmFUND f on m.FUNDID = f.FUNDID and f.INCLUDE_IN_SUMCALC = 1 " +
                        "on s.SRCEID = m.SRCEID and m.COUNTER=1 " +
                        "on k.PKGEID = s.PKGEID " +
                        "on p.PROGID = k.PROGID " +
                        "where m.PID = {0} " +
                        "group by p.PROGID, p.PROGTYPE, p.DESCRIP ";
            return _entity_crm.getContext().Database.SqlQuery<ProgramR>(string.Format(sql, id)).ToList();
        }
        #endregion

        #region [ crm/api/MonyAlloc ] GetBy MID
        [HttpGet, Route("crm/api/Mony/GetjfcAlloc/{id}")]  // MID (Read)
        [apiAuthorize(AccessElement = "cmdiapp.dms.People_Money", AccessLevel = "v")]
        public List<jfc_mony_alloc> GetjfcAlloc_forRead(int id)
        {
            string sql = "EXEC dbo.iJFC_get_alloc {0}";
            return _entity_crm.getContext().Database.SqlQuery<jfc_mony_alloc>(string.Format(sql, id)).ToList();
        }
        #endregion

        #region [ crm/api/MonyPend ] GetBy MID
        [HttpGet, Route("crm/api/Mony/GetjfcPend/{id}")]  // MID (Read)
        [apiAuthorize(AccessElement = "cmdiapp.dms.People_Money", AccessLevel = "v")]
        public List<jfc_mony_pend> GetjfcPend_forRead(int id)
        {
            string sql = "EXEC dbo.iJFC_get_pend {0}";
            return _entity_crm.getContext().Database.SqlQuery<jfc_mony_pend>(string.Format(sql, id)).ToList();
        }
        #endregion

        #region [ crm/api/MonyDistrib ] GetBy MID
        [HttpGet, Route("crm/api/Mony/GetjfcDistrib/{id}")]  // MID (Read)
        [apiAuthorize(AccessElement = "cmdiapp.dms.People_Money", AccessLevel = "v")]
        public DataTable GetjfcDistrib_forRead(int id)
        {
            using (var cmd = _entity_crm.getContext().Database.Connection.CreateCommand())
            {
                DataTable result = new DataTable();
                _entity_crm.getContext().Database.Connection.Open();
                cmd.CommandText = string.Format("EXEC dbo.iGetDistributionDetails {0}", id);
                using (var reader = cmd.ExecuteReader())
                {
                    DataTable schemaTable = reader.GetSchemaTable();
                    foreach (DataRow dr in schemaTable.Rows)
                    {
                        DataColumn column = new DataColumn();
                        column.ColumnName = dr["ColumnName"].ToString();
                        result.Columns.Add(column);
                    }
                    if (reader.HasRows)
                    {
                        while (reader.Read())
                        {
                            // create new DataRow
                            DataRow dr = result.NewRow();

                            for (int i = 0; i < reader.FieldCount; i++)
                            {
                                dr[i] = reader.IsDBNull(i) ? String.Empty : reader.GetValue(i).ToString();
                            }
                            result.Rows.Add(dr);
                        }
                    }
                    return result;
                }
            }
        }
        #endregion

        #region [ crm/api/Mony/calcAlloc] Calc Allocation
        [HttpPost, Route("crm/api/Mony/calcAlloc")]
        [apiAuthorize(AccessElement = "cmdiapp.dms.People_Money(Allocation)", AccessLevel = "e")]
        public genericResponse CalcAlloc(int id, string lockcmtes, int dmjfccmteid)
        {
            try
            {
                var _sql = @"EXEC [dbo].[iJFC_calc_alloc_v3] {0}, {1}, {2}, {3}";
                int result = _entity_crm.getContext().Database.ExecuteSqlCommand(_sql, id, crmSession.UID(), lockcmtes, dmjfccmteid);

                genericResponse _response = new genericResponse() { success = true, __count = 1 };
                return _response;
            }
            catch
            {
                genericResponse _response = new genericResponse() { success = false, __count = 0 };
                return _response;
            }
        }
        #endregion

        #region [ crm/api/Mony/replaceAlloc] Copy Allocation record
        [HttpPost, Route("crm/api/Mony/replaceAlloc")]
        [apiAuthorize(AccessElement = "cmdiapp.dms.People_Money(Allocation)", AccessLevel = "e")]
        public genericResponse ReplaceAlloc(int id, bool jfclock, List<jfc_mony_alloc> allocations)
        {

            try
            {
                string sql = "";
                int result;
                // Save allocation lock in MONY table
                int mid = id;
                MONY _record = _entity_crm.Single<MONY>(a => a.MID == mid);
                if (_record != null)
                {
                    int matchexcepid = jfclock ? 2 : 1;
                    if (_record.MATCHEXCEPID != matchexcepid)
                    {
                        _record.UPDATING_UID = crmSession.UID();
                        _record.MATCHEXCEPID = (short)matchexcepid;
                        _entity_crm.Update(_record);
                        _entity_crm.CommitChanges();
                    }
                }

                // Delete existing allocations
                sql = "DELETE a FROM MONYALLOC a WHERE MID = {0}";
                result = _entity_crm.getContext().Database.ExecuteSqlCommand(sql, id);

                // Copy allocations
                foreach (jfc_mony_alloc alloc in allocations)
                {
                    if ((alloc.amt != null && alloc.amt != 0) || alloc.locked)
                    {
                        MONYALLOC newmonyalloc = new MONYALLOC();
                        newmonyalloc.MID = mid;
                        newmonyalloc.JFCCMTEID = alloc.jfccmteid;
                        newmonyalloc.AMT = alloc.amt;
                        newmonyalloc.UPDATING_UID = crmSession.UID();
                        newmonyalloc.LOCKED = alloc.locked;
                        _entity_crm.Add(newmonyalloc);
                        _entity_crm.CommitChanges();
                    }
                }

                // Recalc pending
                sql = @"EXEC [dbo].[iJFC_update_pend] {0}";
                result = _entity_crm.getContext().Database.ExecuteSqlCommand(sql, id);

                genericResponse _response = new genericResponse() { success = true };
                return _response;
            }
            catch (Exception ex)
            {
                genericResponse _response = new genericResponse() { success = false };
                return _response;
            }
        }
        #endregion

        #region [ crm/api/Mony/saveAlloc] Save  Allocation record
        [HttpPost, Route("crm/api/Mony/saveAlloc")]
        [apiAuthorize(AccessElement = "cmdiapp.dms.People_Money(Allocation)", AccessLevel = "e")]
        public genericResponse SaveAlloc(int id, bool jfclock, List<jfc_mony_alloc> allocations)
        {
            try
            {
                // Save allocation lock in MONY table
                int mid = id;
                MONY _record = _entity_crm.Single<MONY>(a => a.MID == mid);
                if (_record != null)
                {
                    int matchexcepid = jfclock ? 2 : 1;
                    if (_record.MATCHEXCEPID != matchexcepid)
                    {
                        _record.UPDATING_UID = crmSession.UID();
                        _record.MATCHEXCEPID = (short)matchexcepid;
                        _entity_crm.Update(_record);
                        _entity_crm.CommitChanges();
                    }
                }

                // Save allocations to MONYALLOC table
                foreach (jfc_mony_alloc alloc in allocations)
                {
                    if (alloc.allocid != 0 && alloc.allocid != null)
                    {
                        MONYALLOC monyalloc = _entity_crm.Single<MONYALLOC>(a => a.ALLOCID == alloc.allocid);
                        if (monyalloc == null)
                        {
                            if ((alloc.amt != null && alloc.amt != 0) || alloc.locked) // only for non zero or locked
                            {
                                MONYALLOC monyalloc2 = _entity_crm.Single<MONYALLOC>(a => a.JFCCMTEID == alloc.jfccmteid && a.MID == alloc.mid);
                                if (monyalloc2 == null)
                                {
                                    MONYALLOC newmonyalloc = new MONYALLOC();
                                    newmonyalloc.MID = mid;
                                    newmonyalloc.JFCCMTEID = alloc.jfccmteid;
                                    newmonyalloc.AMT = alloc.amt;
                                    newmonyalloc.UPDATING_UID = crmSession.UID();
                                    newmonyalloc.LOCKED = alloc.locked;
                                    _entity_crm.Add(newmonyalloc);
                                    _entity_crm.CommitChanges();
                                }
                                else
                                {
                                    monyalloc2.AMT = alloc.amt;
                                    monyalloc2.LOCKED = alloc.locked;
                                    monyalloc2.UPDATING_UID = crmSession.UID();
                                    _entity_crm.Update(monyalloc2);
                                    _entity_crm.CommitChanges();
                                }
                            }
                        }
                        else 
                        {
                            monyalloc.AMT = alloc.amt;
                            monyalloc.LOCKED = alloc.locked;
                            monyalloc.UPDATING_UID = crmSession.UID();
                            _entity_crm.Update(monyalloc);
                            _entity_crm.CommitChanges();
                        }
                    }
                    else
                    {
                        if ((alloc.amt != null && alloc.amt != 0) || alloc.locked)
                        {
                            MONYALLOC monyalloc3 = _entity_crm.Single<MONYALLOC>(a => a.JFCCMTEID == alloc.jfccmteid && a.MID == alloc.mid);
                            if (monyalloc3 == null)
                            {
                                MONYALLOC monyalloc = new MONYALLOC();
                                monyalloc.MID = mid;
                                monyalloc.JFCCMTEID = alloc.jfccmteid;
                                monyalloc.AMT = alloc.amt;
                                monyalloc.UPDATING_UID = crmSession.UID();
                                monyalloc.LOCKED = alloc.locked;
                                _entity_crm.Add(monyalloc);
                                _entity_crm.CommitChanges();
                            }
                            else
                            {
                                monyalloc3.AMT = alloc.amt;
                                monyalloc3.LOCKED = alloc.locked;
                                monyalloc3.UPDATING_UID = crmSession.UID();
                                _entity_crm.Update(monyalloc3);
                                _entity_crm.CommitChanges();
                            }
                        }
                    }
                }

                // Recalc pending
                var _sql = @"EXEC [dbo].[iJFC_update_pend] {0}";
                int result = _entity_crm.getContext().Database.ExecuteSqlCommand(_sql, mid);

                genericResponse _response = new genericResponse() { success = true, __count = 1 };
                return _response;
            }
            catch (Exception ex)
            {
                genericResponse _response = new genericResponse() { success = false, __count = 0 };
                return _response;
            }
        }
        #endregion

        #region [ crm/api/Mony/GetjfcDistr] GetBy jtDISTRIBID
        [HttpGet, Route("crm/api/Mony/GetjfcDist")]
        public jfc_dist_rec GetjfcDist(int id, int jtdistribid)
        {
            jfc_dist_rec result = new jfc_dist_rec();
            try
            {
                jtJFCDISTRIB jtjfcdistrib = _entity_crm.Single<jtJFCDISTRIB>(a => a.jtDISTRIBID == jtdistribid);
                if (jtjfcdistrib != null)
                {
                    result.jtdistribid = jtjfcdistrib.jtDISTRIBID;
                    result.distribid = jtjfcdistrib.DISTRIBID;
                    result.mid = jtjfcdistrib.MID;
                    result.amt = jtjfcdistrib.AMT;

                    JFCDISTRIB jfcdistrib = _entity_crm.Single<JFCDISTRIB>(a => a.DISTRIBID == result.distribid);
                    if (jfcdistrib != null)
                    {
                        result.distribno = jfcdistrib.DISTRIBNO;
                        result.distribdte = jfcdistrib.DISTRIBDTE;
                        result.fundid = jfcdistrib.FUNDID;
                        result.statusid = jfcdistrib.STATUSID;
                        result.comment = jfcdistrib.COMMENT;
                    }
                }
                string sql = "EXEC dbo.iJFC_get_dist {0}, {1}";
                result.distributions = _entity_crm.getContext().Database.SqlQuery<jfc_mony_dist>(string.Format(sql, jtdistribid, id)).ToList();

                return result;
            }
            catch
            {
                return result;
            }
        }
        #endregion

        #region [ crm/api/Mony/NewjfcDist]
        [HttpGet, Route("crm/api/Mony/NewjfcDist")]
        public jfc_dist_rec NewjfcDist(int id, decimal amt)
        {
            jfc_dist_rec result = new jfc_dist_rec();
            result.jtdistribid = 0;
            result.mid = id;
            result.amt = amt;
            string sql = "EXEC dbo.iJFC_get_dist {0}, {1}";
            result.distributions = _entity_crm.getContext().Database.SqlQuery<jfc_mony_dist>(string.Format(sql, 0, id)).ToList();
            return result;
        }
        #endregion

        #region [ crm/api/Mony/SavejfcDistrib] Save Distribution record
        [HttpPost, Route("crm/api/Mony/SavejfcDistrib/{id}")]
        [apiAuthorize(AccessElement = "cmdiapp.dms.distributions", AccessLevel = "e")]
        public genericResponse SavejfcDistrib(int id, jfc_dist_rec jfcDist)
        {
            try
            {
                if (jfcDist.jtdistribid == 0)
                {
                    JFCDISTRIB jfcdistrib = _entity_crm.Single<JFCDISTRIB>(a => a.DISTRIBNO == jfcDist.distribno && a.DISTRIBDTE == jfcDist.distribdte);
                    // Create Header if not exist
                    if (jfcdistrib == null)
                    {
                        jfcdistrib = new JFCDISTRIB();
                        jfcdistrib.DISTRIBNO = jfcDist.distribno;
                        jfcdistrib.DISTRIBDTE = jfcDist.distribdte;
                        jfcdistrib.FUNDID = (short)jfcDist.fundid;
                        jfcdistrib.STATUSID = 1;
                        jfcdistrib.COMMENT = jfcDist.comment;
                        jfcdistrib.CREATEDBY = (short)crmSession.UID();
                        jfcdistrib.CREATEDON = DateTime.Now;
                        jfcdistrib.UPDATEDBY = crmSession.UID();
                        jfcdistrib.UPDATEDON = DateTime.Now;
                        _entity_crm.Add(jfcdistrib);
                        _entity_crm.CommitChanges();
                    }
                    jtJFCDISTRIB jtjfcdistrib = new jtJFCDISTRIB();
                    jtjfcdistrib.DISTRIBID = jfcdistrib.DISTRIBID;
                    jtjfcdistrib.MID = (int)jfcDist.mid;
                    jtjfcdistrib.AMT = jfcDist.amt;
                    _entity_crm.Add(jtjfcdistrib);
                    _entity_crm.CommitChanges();
                    jfcDist.jtdistribid = jtjfcdistrib.jtDISTRIBID;
                }
                else
                {
                    JFCDISTRIB jfcdistrib = _entity_crm.Single<JFCDISTRIB>(a => a.DISTRIBID == jfcDist.distribid);
                    if (jfcdistrib.COMMENT != jfcDist.comment)
                    {
                        jfcdistrib.COMMENT = jfcDist.comment;
                        jfcdistrib.UPDATEDBY = crmSession.UID();
                        jfcdistrib.UPDATEDON = DateTime.Now;
                        _entity_crm.Update(jfcdistrib);
                        _entity_crm.CommitChanges();
                    }
                }

                foreach (jfc_mony_dist dist in jfcDist.distributions)
                {
                    dtJFCDISTRIB dtjfcdistrib = null;
                    if (dist.dtdistribid > 0)
                    {
                        dtjfcdistrib = _entity_crm.Single<dtJFCDISTRIB>(a => a.dtDISTRIBID == dist.dtdistribid);
                    }
                    if (dtjfcdistrib == null)
                    {
                        // Not existed in db
                        if (dist.distribamt != null && dist.distribamt != 0)
                        {
                            dtJFCDISTRIB newdtjfcdistrib = new dtJFCDISTRIB();
                            newdtjfcdistrib.jtDISTRIBID = jfcDist.jtdistribid;
                            newdtjfcdistrib.JFCCMTEID = dist.jfccmteid;
                            newdtjfcdistrib.DISTRIBAMT = dist.distribamt;
                            newdtjfcdistrib.UPDATING_UID = crmSession.UID();
                            _entity_crm.Add(newdtjfcdistrib);
                            _entity_crm.CommitChanges();
                        }
                    }
                    else
                    {
                        if (dist.distribamt == null || dist.distribamt == 0)
                        {
                            _entity_crm.Delete(dtjfcdistrib);
                            _entity_crm.CommitChanges();
                        }
                        else if (dist.distribamt != dtjfcdistrib.DISTRIBAMT)
                        {
                            dtjfcdistrib.DISTRIBAMT = dist.distribamt;
                            dtjfcdistrib.UPDATING_UID = crmSession.UID();
                            _entity_crm.Update(dtjfcdistrib);
                            _entity_crm.CommitChanges();
                        }
                    }

                }

                // Recalc pending
                var _sql = @"EXEC [dbo].[iJFC_update_pend] {0}";
                int result = _entity_crm.getContext().Database.ExecuteSqlCommand(_sql, jfcDist.mid);

                genericResponse _response = new genericResponse() { success = true, __count = 1 };
                return _response;
            }
            catch (Exception ex)
            {
                genericResponse _response = new genericResponse() { success = false, __count = 0 };
                return _response;
            }
        }
        #endregion

        #region [ crm/api/Mony/DeletejfcDistrib ] Delete Distribution record
        [HttpPost, Route("crm/api/Mony/DeletejfcDistrib/{id}")]
        [apiAuthorize(AccessElement = "cmdiapp.dms.distributions", AccessLevel = "d")]
        public genericResponse DeletejfcDistrib(int id, jfc_mony_dist dist)
        {
            try
            {
                int jtdistribid = dist.jtdistribid;
                int mid = id;
                string _sql;

                _sql = "Insert into zWebAppLog (UPDTYPE,TABLENAME,KEYFIELD,KEYVALUE,UID) " +
                    "select 'D','dtJFCDISTRIB','dtDISTRIBID',dtDISTRIBID, {0} FROM dtJFCDISTRIB WHERE JTDISTRIBID ={1}";
                _entity_crm.getContext().Database.ExecuteSqlCommand(_sql, crmSession.UID(), jtdistribid);

                _sql = "DELETE FROM dtJFCDISTRIB WHERE JTDISTRIBID = {0}";
                _entity_crm.getContext().Database.ExecuteSqlCommand(_sql, jtdistribid);

                _sql = "DELETE FROM jtJFCDISTRIB WHERE jtDISTRIBID = {0}";
                _entity_crm.getContext().Database.ExecuteSqlCommand(_sql, jtdistribid);

                // Recalc pending
                _sql = @"EXEC [dbo].[iJFC_update_pend] {0}";
                int result = _entity_crm.getContext().Database.ExecuteSqlCommand(_sql, mid);

                genericResponse _response = new genericResponse() { success = true, __count = 1 };
                return _response;
            }
            catch (Exception e)
            {
                genericResponse _response = new genericResponse() { success = false, __count = 0 };
                return _response;
            }
        }
        #endregion

        #region [[ By Bhavesh - Fundraised Module in the PB]]

        [HttpGet, Route("crm/api/Mony/Fundraise/{trackNo}")]
        public FUNDRAISE GetFundraisedInfoByTrackNo(int trackNo)
        {
            FUNDRAISE _fundraise = _entity_crm.Single<FUNDRAISE>(a => a.TRACKNO == trackNo);

            //Let us fill CreatedAt if it is NULL
            if (_fundraise.CreatedAt == null && _fundraise.UPDATEDON != null)
                _fundraise.CreatedAt = _fundraise.UPDATEDON;

            return _fundraise;
        }

        [HttpGet, Route("crm/api/Mony/SelectedDonorDetails/{trackNo}")]
        public FUNDRAISEDONOR SelectedDonorDetails(int trackNo)
        {
            string sql = String.Format(_sql_alldonors, trackNo);
            return _entity_crm.getContext().Database.SqlQuery<FUNDRAISEDONOR>(sql)
                .Single<FUNDRAISEDONOR>(d => d.TRACKNO == trackNo);
        }

        [HttpGet, Route("crm/api/Mony/AllDonorDetails/{parentTrackNo}/{TRACKNO}")]
        public List<FUNDRAISEDONOR> AllDonorDetails(int parentTrackNo, int TRACKNO)
        {
            string sql = String.Format(_sql_alldonors, parentTrackNo);
            List<FUNDRAISEDONOR> _allDonorDetails = new List<FUNDRAISEDONOR>();
            if (TRACKNO == 0)
            {
                //First time getting Data
                _allDonorDetails = _entity_crm.getContext().Database.SqlQuery<FUNDRAISEDONOR>(sql).Where(a => a.TRACKNO == parentTrackNo).ToList();
            }
            else
            {
                _allDonorDetails = _entity_crm.getContext().Database.SqlQuery<FUNDRAISEDONOR>(sql).Where(a => a.RECRUITERNO == TRACKNO).ToList();
            }
            //All donor records
            return _allDonorDetails;
        }

        #region [[ SQL statements to pull up Donor Treeview Data ]]
        const string _sql_alldonors =
                @" With A_cte                    
                    (
                        PID,
                        cRAISED,
                        cSHARED,
                        cROLLUP,
                        cREFERRED,
                        RECRUITERNO,
                        TRACKNO,
                        PREV_COMMITMENT,
                        PREV_cRAISED,
                        PREV_cSHARED,
                        PREV_cREFERRED,
                        PREV_ROLLUP,
                        LEVEL
                    )
                    AS
                    (
                    SELECT  P.pid,Isnull(f.cRaised,0) as cRaised,Isnull(f.cShared,0) as cShared, 
	                    dbo.oRollupFundraise_w_Exclude(f.trackno) as cRollup, Isnull(f.cREFERRED,0) as cREFERRED,
	                   
                        f.recruiterno, f.Trackno,
                     	ISNULL(f.PREV_COMMITMENT,0) AS PREV_COMMITMENT,
	                    ISNULL(f.PREV_cRAISED,0) AS PREV_cRAISED,
	                    ISNULL(f.PREV_cSHARED,0) AS PREV_cSHARED,
	                    ISNULL(f.PREV_cREFERRED,0) AS PREV_cREFERRED,
	                    dbo.oRollUpFundraise_Prev(f.trackno) as PREV_ROLLUP,
	                    0 AS LEVEL
	                    
                    FROM fundraise f 
                        INNER JOIN People P on f.trackno = P.trackno
                    WHERE f.trackno = {0}

                    UNION all

                    SELECT   p.pid, Isnull(f.cRaised,0) as cRaised,Isnull(f.cShared,0) as cShared, 
	                    dbo.oRollupFundraise_w_Exclude(f.trackno) as cRollup,Isnull(f.cREFERRED,0) as cREFERRED,
                        f.recruiterno, f.Trackno,
                        ISNULL(f.PREV_COMMITMENT,0) AS PREV_COMMITMENT,
	                    ISNULL(f.PREV_cRAISED,0) AS PREV_cRAISED,
	                    ISNULL(f.PREV_cSHARED,0) AS PREV_cSHARED,
	                    ISNULL(f.PREV_cREFERRED,0) AS PREV_cREFERRED,
	                    dbo.oRollUpFundraise_Prev(f.trackno) as PREV_ROLLUP,
	                    Level +1
                    FROM fundraise f                         
                    Inner Join People p on f.trackno = p.trackno
                    Inner join A_cte acte on f.recruiterno = acte.trackno
                    )

                    Select Distinct a.* INTO #BCTE from A_cte a;

                    Select dbo.oFULLNAME(0,p.PREFIX,p.FNAME,p.MNAME,p.LNAME,p.SUFFIX) AS NAME,
                    ('#'+ Convert(varchar,a.TrackNO) + ' ' + dbo.oFULLNAME(0,p.PREFIX,p.FNAME,p.MNAME,p.LNAME,p.SUFFIX) + ' $' + Convert(varchar,(a.cRAISED + a.cSHARED))) as DISPNAMECURR,
                    ('#'+ Convert(varchar,a.TrackNO) + ' ' + dbo.oFULLNAME(0,p.PREFIX,p.FNAME,p.MNAME,p.LNAME,p.SUFFIX) + ' $' + Convert(varchar,(a.PREV_cRAISED + a.PREV_cSHARED))) as DISPNAMEPREV,
                    a.*,
                    (CASE 
		   			   WHEN (Select count(*) from #BCTE p where p.Recruiterno = a.TrackNo) <= 0 THEN 0 
        			   ELSE 1
       			    END) as hasChildren,
                    p.PICTURE 
                    from #BCTE a
                    inner join people p on a.pid = p.pid
                    ORDER BY LEVEL
                    OPTION (MAXRECURSION 1000)";
        #endregion
        [HttpPost, Route("crm/api/Mony/AddFundGrpForFundraiser")]
        public genericResponse AddFundGrpForFundraiser(jtFNDRGRP fndrGrp)
        {
            try
            {
                fndrGrp.UPDATEDON = DateTime.Now;
                _monyService.Add(fndrGrp);
                return new genericResponse()
                {
                    success = true
                };
            }
            catch (Exception e)
            {
                return new genericResponse()
                {
                    success = false,
                    message = "An error occurred.  Unable to add fundraiser group."
                };
            }
        }

        [HttpPost, Route("crm/api/Mony/DeleteFundGrpForFundraiser")]
        public genericResponse DeleteFundGrpForFundraiser(jtFNDRGRP fndrGrp)
        {
            try
            {
                jtFNDRGRP _fndrGrp = fndrGrp;
                if (_fndrGrp.jtFNDRGRPID <= 0)
                {
                    _fndrGrp = _monyService.get_jtfndgroup(_fndrGrp.FUNDRAISEID, _fndrGrp.FNDRGRPID);
                }
                _monyService.Delete(_fndrGrp);
                return new genericResponse()
                {
                    success = true
                };
            }
            catch (Exception e)
            {
                return new genericResponse()
                {
                    success = false,
                    message = "An error occurred.  Unable to add fundraiser group. " + e.Message
                };
            }
        }

        [HttpPost, Route("crm/api/Mony/SaveFundraisedData")]
        public genericResponse SaveFundraisedData(FUNDRAISE _fundraise)
        {
            return _monyService.SaveFundraise(_fundraise);
        }

        #endregion

        //#region [[ crm/api/Money/giftInfo - Brief Gift Info (09/08/2014 by Junho) ]]
        //[HttpGet, Route("crm/api/Money/giftInfo/{id}")]
        //[apiAuthorize(AccessElement = "cmdiapp.dms.People_Money", AccessLevel = "v")]
        //public MoneyP giftInfo (int? id)
        //{
        //    if (id == null)
        //        return null;
        //    else
        //    {
        //        var q = _entity_crm.getContext().Database.SqlQuery<MoneyP>(string.Format("SELECT * FROM [dbo].[fn_mony_info]({0}) ", id));
        //        return q.FirstOrDefault();
        //    }
        //}
        //#endregion

        #region [[ crm/api/Money/PrintCheckDataKey - Brief Gift Info for Report ]]
        [HttpPost, Route("crm/api/Money/PrintCheckDataKey")]
        [apiAuthorize(AccessElement = @"Fundraising\Money\CheckImage", AccessLevel = "v")]
        public genericResponseWithKey PrintCheckDataKey(MoneyP moneyP)
        {
            try
            {
                if (moneyP != null && moneyP.MID > 0)
                {
                    //Create Unique GUID Here
                    string p_key = System.Guid.NewGuid().ToString();

                    #region [[ Create SQL Statement ]]
                    string _tmp_srchSQL = string.Format("SELECT * FROM [dbo].[fn_mony_info]({0}) ", moneyP.MID);
                    #endregion

                    #region [[ For XML + Collection ]]
                    string _xml = _supportMethods.get_report_data_PrintCheck(_tmp_srchSQL);
                    //Create collection
                    System.Xml.XmlDocument doc = new System.Xml.XmlDocument();
                    doc.LoadXml(_xml);
                    _node = doc.DocumentElement.SelectSingleNode("/ReportParameters");

                    System.Collections.Specialized.NameValueCollection _collection = new System.Collections.Specialized.NameValueCollection();
                    _collection.Add("cc", _node.Attributes["clientcode"].Value);
                    _collection.Add("rn", _node.Attributes["reportcode"].Value);
                    _collection.Add("XML", _node.OuterXml);
                    //Save the Report Parameter collection in the Session
                    string cacheKey_ = p_key + "_c";
                    HttpRuntime.Cache.Remove(cacheKey_);
                    //Now add the 
                    HttpRuntime.Cache.Insert(cacheKey_, _collection, null, DateTime.UtcNow.Add(new TimeSpan(1, 1, 0)), Cache.NoSlidingExpiration);
                    #endregion

                    #region [[ For DataSet ]]
                    DataSet _psDataset = _reportService.get_dataset_w_sql__single(_tmp_srchSQL, "GiftInfo");
                    //First Data Set 
                    cacheKey_ = p_key + "_fd";
                    HttpRuntime.Cache.Remove(cacheKey_);
                    //Now add the 
                    HttpRuntime.Cache.Insert(cacheKey_, _psDataset, null, DateTime.UtcNow.Add(new TimeSpan(1, 1, 0)), Cache.NoSlidingExpiration);
                    #endregion

                    if (_psDataset.Tables[0].Rows.Count > 0)
                    {
                        return new genericResponseWithKey() { success = true, Key = p_key };
                    }
                    else
                    {
                        return new genericResponseWithKey() { success = false, message = "There is some issue in processing Check Print request. Please try again later." };

                    }
                }
                else
                {
                    return new genericResponseWithKey() { success = false, message = "There is some issue in processing Check Print request. Please try again later." };
                }
            }
            catch (Exception ex)
            {
                return new genericResponseWithKey() { success = false, message = "There is some issue in processing Check Print request. Please try again later." };
            }
        }
        #endregion

        #region [[ crm/api/Money/checkImage - Check Image (09/08/2014 by Junho) ]]
        [HttpGet, Route("crm/api/Money/checkImage/{id}/{seed}")]
        [apiAuthorize(AccessElement = @"Fundraising\Money\CheckImage", AccessLevel = "v")]
        public HttpResponseMessage checkImage(int? id, string seed)
        {

            string anonymousPic_path = Path.Combine(System.Web.Hosting.HostingEnvironment.MapPath(@"~/contents/_shared/image/notAvailable.png"));

            HttpResponseMessage response = new HttpResponseMessage(HttpStatusCode.OK);

            if (id == null)
            {
                var fileStream = new System.IO.FileStream(anonymousPic_path, System.IO.FileMode.Open, FileAccess.Read, FileShare.ReadWrite);
                response.Content = new StreamContent(fileStream);
            }
            else
            {

                var q = _entity_crm.getContext().Database.SqlQuery<MoneyP>(string.Format("SELECT * FROM [dbo].[fn_mony_info]({0}) ", id));
                MoneyP _moneyInfo = q.FirstOrDefault();
                if (_moneyInfo != null && _moneyInfo.MID > 0)
                {
                    byte[] checkImage = _moneyInfo.checkImage;
                    if (checkImage != null)
                    {
                        var memoryStream = new MemoryStream(checkImage);
                        response.Content = new StreamContent(memoryStream);
                    }
                    else
                    {
                        var fileStream = new System.IO.FileStream(anonymousPic_path, System.IO.FileMode.Open, FileAccess.Read, FileShare.ReadWrite);
                        response.Content = new StreamContent(fileStream);
                    }
                }
                else
                {
                    var fileStream = new System.IO.FileStream(anonymousPic_path, System.IO.FileMode.Open, FileAccess.Read, FileShare.ReadWrite);
                    response.Content = new StreamContent(fileStream);
                }


            }
            response.Content.Headers.ContentType = new MediaTypeHeaderValue("image/jpeg");
            return response;
        }
        #endregion

        //For Import Partnership Attribution Show/Hide
        [HttpGet, Route("crm/api/MonyR/Get/{id}")]
        [apiAuthorize(AccessElement = "cmdiapp.dms.People_Money", AccessLevel = "v")]
        public string GetPeoType(int id)
        {
            string sql = "select T.PEOTYPE from MONY M LEFT JOIN PEOPLE P INNER JOIN lkPEOTYPE T ON P.PEOTYPEID = T.PEOTYPEID ON M.PID = P.PID WHERE M.MID = {0}";
            return _entity_crm.getContext().Database.SqlQuery<string>(string.Format(sql, id)).SingleOrDefault();
        }

        [HttpGet, Route("crm/api/Mony/GetjtJfcDistStatus/{id}")]
        [apiAuthorize(AccessElement = "cmdiapp.dms.People_Money", AccessLevel = "v")]
        public Int16 GetjtJfcDistStatus(int id)
        {
            string sql = "SELECT D.STATUSID from JFCDISTRIB D LEFT JOIN jtJFCDISTRIB J ON J.DISTRIBID = D.DISTRIBID WHERE jtDISTRIBID = {0}";
            return _entity_crm.getContext().Database.SqlQuery<Int16>(string.Format(sql, id)).SingleOrDefault();
        }

        #region [ crm/api/MonyAddi/Save] Save Mony Addi record

        [HttpPost, Route("crm/api/MonyAddi/Save")]
        [apiAuthorize(AccessElement = "cmdiapp.dms.People_Money", AccessLevel = "e")]
        public genericResponse SaveMonyAddi(MONYADDI monyaddi)
        {
            genericResponse _response;
            try
            {
                if (crmSession.configValue(crmConstants.Money_custom_Reference_Id) == "Y")
                {
                    MONYADDI _record = _entity_crm.Single<MONYADDI>(a => a.MID == monyaddi.MID);

                    if (_record != null)
                    {
                        // existing record
                        _record.REFERENCEID = monyaddi.REFERENCEID;
                        _entity_crm.Update(_record);
                        _entity_crm.CommitChanges();
                    }
                    else
                    {
                        _record = new MONYADDI();
                        _record.MID = monyaddi.MID;
                        _record.REFERENCEID = monyaddi.REFERENCEID;
                        //_record.RECURRED = monyaddi.RECURRED;
                        _record.RECURRED = false;
                        _entity_crm.Add(_record);
                        _entity_crm.CommitChanges();
                    }

                    MONYADDI _monyaddi = _entity_crm.Single<MONYADDI>(a => a.MID == monyaddi.MID);
                    List<iItemType> _dataset = new List<iItemType>();
                    _dataset.Add(_monyaddi);
                    _response = new genericResponse() { success = true, __count = _dataset.Count, results = _dataset.ToList() };
                }
                else
                {
                    _response = new genericResponse() { success = false, __count = 0 };
                }

                return _response;
            }

            catch (Exception e)
            {
                _response = new genericResponse() { success = false, __count = 0 };
                return _response;
            }
        }

        [HttpPost, Route("crm/api/MonyExpenditure/Save")]
        [apiAuthorize(AccessElement = "cmdiapp.dms.People_Money", AccessLevel = "e")]
        public genericResponse SaveMonyExpenditure(MONYADDI monyaddi)
        {
            genericResponse _response;
            try
            {

                string _sql = String.Format("DECLARE @SUCCEED bit, @MESSAGE VARCHAR(200);\n" +
                        "EXEC dbo.iCreate_InKindPayment_Exp {0}, {1}, @SUCCEED OUTPUT, @MESSAGE OUTPUT;" +
                        "\n Select @SUCCEED as SUCCEED, @MESSAGE AS MESSAGE;",
                           monyaddi.MID,
                           crmSession.UID().Value);

                wp_complete_Result result = _entity_crm.getContext().Database.SqlQuery<wp_complete_Result>(String.Format("{0}", _sql)).FirstOrDefault();

                if (result.SUCCEED)
                {
                    _response = new genericResponse() { success = true, __count = 0 };
                }
                else
                {
                    _response = new genericResponse() { success = false, __count = 0 };
                }

                return _response;
            }

            catch (Exception e)
            {
                _response = new genericResponse() { success = false, __count = 0 };
                return _response;
            }
        }
        #endregion

        #region [ crm/api/Mony/StopRecur ] Stop Recurring Gift
        [HttpPost, Route("crm/api/Mony/StopRecur")]
        [apiAuthorize(AccessElement = "cmdiapp.dms.People_Money", AccessLevel = "d")]
        public genericResponse StopRecur(p_stoprecur _stoprecur)
        {
            try
            {
                genericResponse _response = new genericResponse();
                userSession _userSession = session.userSession;
                // Call the Webservice and get Result
                BasicHttpBinding basicHttpBinding = new BasicHttpBinding();
                basicHttpBinding.Security.Mode = BasicHttpSecurityMode.Transport;
                EndpointAddress endpointAddress = new EndpointAddress(System.Configuration.ConfigurationManager.AppSettings["PCIUrl"] + "/ccServiceHost/ccAdmin.svc");
                IAdmin ccAdmin = new ChannelFactory<IAdmin>(basicHttpBinding, endpointAddress).CreateChannel();
                string result = ccAdmin.stopRecurbyId("1C8E3A22-BB9A-46F9-BBD1-CD2DEAC144D5", "crimson", _stoprecur.WEBGIFTID, _stoprecur.CARDNO.Substring(_stoprecur.CARDNO.Length - 4, 4), _stoprecur.AMT, _userSession.UserName);

                // Now work with the WS Response /
                string code = result.Substring(result.IndexOf("<code>") + 6, result.IndexOf("</code>") - (result.IndexOf("<code>") + 6));
                string msg = result.Substring(result.IndexOf("<msg>") + 5, result.IndexOf("</msg>") - (result.IndexOf("<msg>") + 5));
                if (code.StartsWith("-"))
                {
                    _response.success = false;
                    _response.message = String.Format("Problem stopping this recurred gift. Please contact Support (Error: {0}).", msg);
                }
                else
                {
                    MONYADDI monyaddi = _entity_crm.Single<MONYADDI>(a => a.MID == _stoprecur.MID);
                    monyaddi.RECURRED = false;
                    monyaddi.RECURENDDTE = null;
                    _entity_crm.Update(monyaddi);
                    _entity_crm.CommitChanges();
                    _response.success = true;
                    _response.message = String.Format("This recurred gift is successfully stoped.");
                }
                return _response;
            }
            catch (Exception e)
            {
                return new genericResponse() { success = false, __count = 0, message = "Error while stopping Recurring Gift!" };
            }
        }
        #endregion

        [HttpGet, Route("crm/api/mony/monyattributions/{id}")]
        [apiAuthorize(AccessElement = "cmdiapp.dms.People_Money", AccessLevel = "v")]
        public genericResponse getMonyAttributions(int id, int? page, int? pageSize)
        {
            /*
            string sql = "SELECT * FROM v_mony_adjustment WHERE ORIGMID = {0} AND ADJTYPE in ('PT','EM','AB') ORDER BY MID";
            List<vwMonyAdjustment> attribdata = _entity_crm.getContext().Database.SqlQuery<vwMonyAdjustment>(string.Format(sql, id)).ToList();
            genericResponse _response = new genericResponse() { success = true, __count = attribdata.Count, results = attribdata.ToList<iItemType>() };
            return _response;
            */
            genericResponse _response;
            try
            {
                #region [ Retrieve "Sort" options ]
                string sortField = HttpContext.Current.Request.QueryString["sort[0][field]"];
                string sortDir = HttpContext.Current.Request.QueryString["sort[0][dir]"];

                int _pageSize = (pageSize == null ? 10 : pageSize.Value);
                int _pageNo = (page == null ? 1 : page.Value);

                string _sortOptions = "";
                if (!string.IsNullOrEmpty(sortField))
                    _sortOptions = sortField;
                if (!string.IsNullOrEmpty(sortField) && !string.IsNullOrEmpty(sortDir))
                    _sortOptions = _sortOptions + " " + sortDir;
                if (string.IsNullOrEmpty(_sortOptions)) _sortOptions = "MID";
                #endregion

                List<vwMonyAdjustment_ext> _list = new List<vwMonyAdjustment_ext>();
                string _sql = String.Format("SELECT * FROM v_mony_adjustment WHERE ORIGMID = {0} AND ADJTYPE in (''PT'',''EM'',''AB'',''DA'',''EG'')", id);
                var q = _entity_crm.getContext().Database.SqlQuery<vwMonyAdjustment_ext>(String.Format("EXEC dbo.get_pagedDataSet '{0}', '{1}', {2}, {3}", _sql, _sortOptions, _pageSize, _pageNo));
                _list = q.ToList();
                Mapper.CreateMap<vwMonyAdjustment_ext, vwMonyAdjustment>();
                IEnumerable<iItemType> results = _list.Select(a => Mapper.Map<vwMonyAdjustment_ext, vwMonyAdjustment>(a)).ToList();
                if (results.Count() > 0)
                {
                    _response = new genericResponse() { success = true, __count = _list.FirstOrDefault().count_, results = results.ToList() };
                }
                else
                {
                    _response = new genericResponse() { success = true, __count = 0 };
                }
                return _response;
            }
            catch (Exception ex)
            {
                _response = new genericResponse() { success = false, __count = 0 };
                return _response;
            }
        }

        #region[[Export Attribution Details]]

        const string sql_exportAttributionDetails = @"SELECT MID as Id, 
                        PID as 'Donor Id', 
                        NAME as 'Donor Name',
                        ADDR as 'Address',
                        BATCHDTE as 'Batch Date',
                        BATCHNO as 'Batch No',
                        ADJDTE as 'Adjust Date',
                        AMT as 'Amount',
                        FUNDCODE as 'Fund Code',
                        SRCECODE as 'Source Code',
                        ADJDESC as 'Adjustment',
                        ORIGMID as 'Original Id' 
                    FROM v_mony_adjustment WHERE ADJTYPE in ('PT','EM','AB','DA','EG')";

        [HttpPost, Route("crm/api/Mony/ExportAttributionDetailKey")]
        [apiAuthorize(AccessElement = "cmdiapp.dms.People_Money", AccessLevel = "v")]
        [NotLaunchpadApiAuthorize]
        public string ExportAttributionDetailsKey(string searchText)
        {
            if (string.IsNullOrEmpty(searchText)) searchText = "";

            searchParam param = new searchParam();
            param.searchText = searchText;

            string key_ = System.Guid.NewGuid().ToString();
            HttpRuntime.Cache.Insert(key_, param, null, DateTime.UtcNow.Add(new TimeSpan(1, 0, 1)), System.Web.Caching.Cache.NoSlidingExpiration);

            return key_;

        }

        [HttpGet, Route("crm/api/Mony/ExportAttributionDetail")]
        [apiAuthorize(AccessElement = "cmdiapp.dms.People_Money", AccessLevel = "v")]
        public string ExportAttributionDetails(string key)
        {
            userSession _userSession = session.userSession;
            searchParam _param = (searchParam)HttpRuntime.Cache[key];
            string sToday = String.Format("{0:yyyyMMMdd__hh_mm_ss tt}", DateTime.Now);
            string moduleName = "Attribution Details";
            string fileName = moduleName + "_" + System.Guid.NewGuid() + "_" + _userSession.UserName + "_" + sToday;

            //if (_param == null)
            //    return false;

            //System.Data.DataTable _dt = null;
            DataSet ds = null;

            if (string.IsNullOrEmpty(_param.searchText))
            {
                //_dt = _reportService.get_dataset_w_sql__single(sql_exportTxnLinkedDetails, "Transaction").Tables[0];
                ds = _reportService.get_dataset_w_sql__single(sql_exportAttributionDetails, "Attribution Details");
            }
            else
            {
                _param.searchText = Library.util.kill_sqlBlacklistWord(_param.searchText);

                if (!string.IsNullOrEmpty(_param.searchText))
                {
                    string sql = string.Format(sql_exportAttributionDetails + " AND ORIGMID = {0}", _param.searchText);
                    ds = _reportService.get_dataset_w_sql__single(sql, "Attribution Details");
                }
            }

            if (ds != null)
            {
                //CreateExcelFile.CreateExcelDocument(ds.Copy(), fileName, HttpContext.Current.Response);
                string key_ = System.Guid.NewGuid().ToString();
                HttpRuntime.Cache.Insert(key_, ds, null, DateTime.UtcNow.Add(new TimeSpan(1, 0, 1)), System.Web.Caching.Cache.NoSlidingExpiration);
                //return the Key
                return key_;

            }
            else
            {
                return null;
            }
        }
        #endregion

        #region [[ Search available gifts ]]
        [HttpGet, Route("crm/api/Mony/QuickSearch")]
        [apiAuthorize(AccessElement = "cmdiapp.dms.People_Money", AccessLevel = "v")]
        public genericResponse SearchMoney(string searchText, int? page, int? pageSize)
        {
            genericResponse _response;
            try
            {
                string _sql = "select * from v_donor_donation_list";

                #region [ Retrieve "Sort" options ]
                string sortField = HttpContext.Current.Request.QueryString["sort[0][field]"];
                string sortDir = HttpContext.Current.Request.QueryString["sort[0][dir]"];

                int _pageSize = (pageSize == null ? 10 : pageSize.Value);
                int _pageNo = (page == null ? 1 : page.Value);

                string _sortOptions = "";
                if (!string.IsNullOrEmpty(sortField))
                    _sortOptions = sortField;
                if (!string.IsNullOrEmpty(sortField) && !string.IsNullOrEmpty(sortDir))
                    _sortOptions = _sortOptions + " " + sortDir;
                #endregion

                List<v_donor_donation_list_ext> _list = new List<v_donor_donation_list_ext>();


                if (!string.IsNullOrEmpty(searchText))
                {
                    searchText = Library.util.kill_sqlBlacklistWord(searchText);
                    int mid;

                    if (string.IsNullOrEmpty(_sortOptions)) _sortOptions = "LNAME, FNAME";

                    #region [[ by MID ]]
                    if (int.TryParse(searchText, out mid))
                    {
                        _sql += String.Format(" where MID = {0}", mid);
                        var q = _entity_crm.getContext().Database.SqlQuery<v_donor_donation_list_ext>(String.Format("EXEC dbo.get_pagedDataSet '{0}', '{1}', {2}, {3}", _sql, _sortOptions, _pageSize, _pageNo));
                        _list = q.ToList();
                    }
                    #endregion

                    #region [[ by Name ]]
                    else
                    {
                        string _where = "";

                        if (!string.IsNullOrEmpty(searchText))
                        {
                            #region [ Split Search Text > string1, string2 & string3 and create "Where" statement ]
                            char[] delimiterChars = { ' ', ',', '.', ';', ':', '|', '\t' };

                            searchText = searchText.Replace("''", "'");

                            string[] words = searchText.Split(delimiterChars);

                            if (words.Count() > 0)
                            { 
                                string string1 = words[0];
                                string string2 = (words.Count() > 1 ? words[1] : "");
                                string string3 = (words.Count() > 2 ? words[2] : "");

                                string _FNAME_where = "";

                                #region [ Where Clause ]

                                _FNAME_where = q_library.get_normFNAMEwhere("FNAME", string1, _dataService);

                                switch (words.Count())
                                {
                                    case 1: // First or Last Name
                                        _where = _where + string.Format("(" + _FNAME_where + " OR LNAME LIKE '{0}%')", string1);
                                        break;

                                    case 2: // First and Last Name  OR First OR Last
                                        _where = _where + string.Format("((" + _FNAME_where + " AND LNAME LIKE '{0}%') OR (FNAME LIKE '{1}%' AND LNAME LIKE '{1}%'))", string2, searchText);
                                        break;

                                    case 3: // First, Middle and Last Name  OR Last 
                                        _where = _where + string.Format("((" + _FNAME_where + " AND MNAME LIKE '{0}%' AND LNAME LIKE '{1}%') OR (FNAME LIKE '{2}%' AND LNAME LIKE '{2}%'))", string2, string3, searchText);
                                        break;
                                    default:
                                        break;
                                }
                                #endregion
                            }
                            #endregion
                            _sql += " where " + _where;
                            _sql = _sql.Trim().Replace("'", "''");
                            var q = _entity_crm.getContext().Database.SqlQuery<v_donor_donation_list_ext>(String.Format("EXEC dbo.get_pagedDataSet '{0}', '{1}', {2}, {3}", _sql, _sortOptions, _pageSize, _pageNo));
                            _list = q.ToList();
                        }
                        #endregion
                    }

                    Mapper.CreateMap<v_donor_donation_list_ext, v_donor_donation_list>();
                    IEnumerable<iItemType> results = _list.Select(a => Mapper.Map<v_donor_donation_list_ext, v_donor_donation_list>(a)).ToList();
                    if (results.Count() > 0)
                    {
                        _response = new genericResponse() { success = true, __count = _list.FirstOrDefault().count_, results = results.ToList() };
                    }
                    else
                    {
                        _response = new genericResponse() { success = true, __count = 0 };
                    }

                }
                else // All records
                {
                    if (string.IsNullOrEmpty(_sortOptions)) _sortOptions = "LNAME, FNAME";
                    var q = _entity_crm.getContext().Database.SqlQuery<v_donor_donation_list_ext>(String.Format("EXEC dbo.get_pagedDataSet '{0}', '{1}', {2}, {3}", _sql, _sortOptions, _pageSize, _pageNo));
                    _list = q.ToList();
                    Mapper.CreateMap<v_donor_donation_list_ext, v_donor_donation_list>();
                    IEnumerable<iItemType> results = _list.Select(a => Mapper.Map<v_donor_donation_list_ext, v_donor_donation_list>(a)).ToList();
                    if (results.Count() > 0)
                    {
                        _response = new genericResponse() { success = true, __count = _list.FirstOrDefault().count_, results = results.ToList() };
                    }
                    else
                    {
                        _response = new genericResponse() { success = true, __count = 0 };
                    }
                }

                return _response;
            }
            catch (Exception ex)
            {
                _response = new genericResponse() { success = false, __count = 0 };
                return _response;
            }
        }
        #endregion

        #region [[ Link Attribution  ]]
        [HttpPost, Route("crm/api/Mony/linkAttrib")]
        [apiAuthorize(AccessElement = "cmdiapp.dms.People_Money", AccessLevel = "e")]
        public genericResponse linkAttrib(parm_linkAttrib parm)
        {
            genericResponse _response;
            try
            {
                MONY _mony = _entity_crm.Single<MONY>(m => m.MID == parm.ATTRIBMID);
                if (_mony != null)
                {
                    _mony.ORIGMID = parm.MID;
                    _mony.ADJTYPEID = Convert.ToByte(parm.ADJTYPE == "EM" ? 19 : (parm.ADJTYPE == "JF" ? 18 : 13));
                    _mony.ADJDTE = Convert.ToDateTime(DateTime.Today.ToShortDateString());
                    _mony.ADJAMT = 0;
                    _mony.SOFTMONEY = Convert.ToByte(parm.ADJTYPE == "EM" || parm.ADJTYPE == "JF" ? 0 : 1); // PT is SOFT CREDIT               
                    _entity_crm.Update(_mony);
                    _entity_crm.CommitChanges();

                    List<iItemType> _dataset = new List<iItemType>();
                    _dataset.Add(_mony);
                    _response = new genericResponse() { success = true, __count = _dataset.Count, results = _dataset.ToList() };
                }
                else
                {
                    _response = new genericResponse() { success = false, __count = 0 };
                }

                return _response;
            }

            catch (Exception e)
            {
                _response = new genericResponse() { success = false, __count = 0 };
                return _response;
            }
        }
        #endregion

        #region [[ Unlink Attribution  ]]
        [HttpPost, Route("crm/api/Mony/unlinkAttrib/{id}")]
        [apiAuthorize(AccessElement = "cmdiapp.dms.People_Money", AccessLevel = "e")]
        public genericResponse unlinkAttrib(int id)
        {
            genericResponse _response;
            try
            {
                MONY _mony = _entity_crm.Single<MONY>(m => m.MID == id);
                if (_mony != null)
                {
                    _mony.ORIGMID = null;
                    _mony.ADJTYPEID = 0;
                    _mony.ADJDTE = null;
                    _mony.ADJAMT = null;
                    _mony.SOFTMONEY = 0;
                    _entity_crm.Update(_mony);
                    _entity_crm.CommitChanges();

                    List<iItemType> _dataset = new List<iItemType>();
                    _dataset.Add(_mony);
                    _response = new genericResponse() { success = true, __count = _dataset.Count, results = _dataset.ToList() };
                }
                else
                {
                    _response = new genericResponse() { success = false, __count = 0 };
                }

                return _response;
            }

            catch (Exception e)
            {
                _response = new genericResponse() { success = false, __count = 0 };
                return _response;
            }
        }
        #endregion

        [HttpGet, Route("crm/api/mony/monyattribCount/{id}")]
        [apiAuthorize(AccessElement = "cmdiapp.dms.People_Money", AccessLevel = "v")]
        public genericResponse getMonyAttribCount(int id)
        {
            string sql = "SELECT COUNT(*) as cnt, SUM(AMT) as total FROM v_mony_adjustment WHERE ORIGMID = {0} AND ADJTYPE in ('PT','EM','AB','DA','EG')";
            List<attribCount> attribcount = _entity_crm.getContext().Database.SqlQuery<attribCount>(string.Format(sql, id)).ToList();
            genericResponse _response = new genericResponse() { success = true, __count = attribcount[0].cnt, results = attribcount.ToList<iItemType>() };
            return _response;
        }

        [HttpGet, Route("crm/api/mony/topmonyattributions/{id}/{cnt}")]
        [apiAuthorize(AccessElement = "cmdiapp.dms.People_Money", AccessLevel = "v")]
        public genericResponse topMonyAttributions(int id, int cnt)
        {
            string sql = "";
            if (cnt > 0)
                sql = "SELECT TOP " + cnt.ToString() + " * FROM v_mony_adjustment WHERE ORIGMID = {0} AND ADJTYPE in ('PT','EM','AB','DA','EG') ORDER BY MID";
            else
                sql = "SELECT * FROM v_mony_adjustment WHERE ORIGMID = {0} AND ADJTYPE in ('PT','EM','AB','DA','EG') ORDER BY MID";
            List<vwMonyAdjustment> attribdata = _entity_crm.getContext().Database.SqlQuery<vwMonyAdjustment>(string.Format(sql, id)).ToList();
            genericResponse _response = new genericResponse() { success = true, __count = attribdata.Count, results = attribdata.ToList<iItemType>() };
            return _response;
        }

        #region [[ Mail History ]]

        [HttpGet, Route("crm/api/mony/MailHistList")]
        public genericResponse MailHistList(int pid, int? page, int? pageSize)
        {
            #region [ Retrieve "Sort" options ]
            string sortField = HttpContext.Current.Request.QueryString["sort[0][field]"];
            string sortDir = HttpContext.Current.Request.QueryString["sort[0][dir]"];

            int _pageSize = (pageSize == null ? 10 : pageSize.Value);
            int _pageNo = (page == null ? 1 : page.Value);

            string _sortOptions = " MAILDTE DESC";
            if (!string.IsNullOrEmpty(sortField))
                _sortOptions = sortField;
            if (!string.IsNullOrEmpty(sortField) && !string.IsNullOrEmpty(sortDir))
                _sortOptions = _sortOptions + " " + sortDir;
            #endregion

            #region [[ sql Set up ]]
            string sqlText = String.Format(@"SELECT 
                        D.OUTGOID,
                        D.SELEDTE,
                        S.MAILDTE, 
                        D.OUTGOCODE,
                        D.DESCRIP,
                        (SELECT TOP 1 PKGECODE FROM PACKAGE WHERE PKGEID=S.PKGEID) AS PKGECODE,
                        T.SRCECODE,
                        S.SRCEDESC,
                        CASE WHEN (SELECT COUNT(*) FROM MONY M1 WHERE M1.SRCEID=S.SRCEID AND M1.PID = J.PID AND M1.COUNTER = 1 AND M1.SOFTMONEY <> 1 AND M1.BATCHDTE >= D.MAILDTE) > 0 THEN ''Y'' ELSE ''N'' END AS RESPONDED,
                        (SELECT TOP 1 M2.BATCHDTE FROM MONY M2 WHERE M2.SRCEID = S.SRCEID AND M2.PID = J.PID AND M2.COUNTER = 1 AND M2.SOFTMONEY <>1 AND M2.BATCHDTE >= D.MAILDTE ORDER BY M2.BATCHDTE) AS BATCHDTE,
                        (SELECT TOP 1 M3.AMT FROM MONY M3 WHERE M3.SRCEID = S.SRCEID AND M3.PID = J.PID AND M3.COUNTER = 1 AND M3.SOFTMONEY <>1 AND M3.BATCHDTE >= D.MAILDTE ORDER BY M3.BATCHDTE) AS AMT 
                        FROM dmOUTGO D 
	                        INNER JOIN jtOUTGO J ON D.OUTGOID = J.OUTGOID
	                        INNER JOIN dtOUTGO T ON J.dtOUTGOID = T.dtOUTGOID
	                        LEFT OUTER JOIN SOURCE S ON T.SRCECODE = S.SRCECODE
                        WHERE  J.PID = {0}", pid.ToString());

            //Get the Data
            List<MailHistList_ext> _list = new List<MailHistList_ext>();
            _list = _entity_crm.getContext().Database.SqlQuery<MailHistList_ext>(String.Format("EXEC dbo.get_pagedDataSet '{0}', '{1}', {2}, {3}", sqlText, _sortOptions, pageSize, page)).ToList();
            #endregion

            IEnumerable<iItemType> results = _list.ToList();
            if (results.Count() > 0)
            {
                return new genericResponse() { success = true, __count = _list.FirstOrDefault().count_, results = results.ToList() };
            }
            else
            {
                return new genericResponse() { success = true, __count = 0 };
            }
        }
        #endregion

        #region [[ ALF Honor/Memorial gifts ]]

        #region [[ crm/api/MonyMem/Get ]] GetBy MID
        [HttpGet, Route("crm/api/MonyMem/Get/{id}")]  // MID (Read)
        [apiAuthorize(AccessElement = "cmdiapp.dms.People_Money", AccessLevel = "v")]
        public List<MONYMEM_R> GetMonyMemByMid(int id)
        {
            var q = from mm in _entity_crm.All<MONYMEM>()
                    join m in _entity_crm.All<MONY>() on mm.MEMMID equals m.MID into mms
                    from result1 in mms.DefaultIfEmpty()
                    join p in _entity_crm.All<PeopleF>() on result1.PID equals p.PID into pms
                    from result2 in pms.DefaultIfEmpty()
                    where mm.MID == id
                    select new MONYMEM_R { MONYMEMID = mm.MONYMEMID, MID = mm.MID, MEMMID = mm.MEMMID, MEMTYPEID = mm.MEMTYPEID, PID = result2.PID, HONOREE = result2.FULLNAME, MEMNAME = mm.MEMNAME };
            return q.ToList();
        }
        #endregion

        #region [[ crm/api/MonyMem/Save ]]
        [HttpPost, Route("crm/api/MonyMem/Save")]
        [apiAuthorize(AccessElement = "cmdiapp.dms.People_Money", AccessLevel = "e")]
        public genericResponse SaveMonyMem(MONYMEM_R monymem)
        {
            genericResponse _response;
            string _sql;
            MONYMEM _record = null;
            try
            {
                lkMEMTYPE memtype = _entity_crm.Single<lkMEMTYPE>(a => a.MEMTYPEID == monymem.MEMTYPEID);
                string adjtype = (memtype != null && memtype.MEMTYPE == "SUP") ? "SU" : "HH"; 
                if (monymem.MONYMEMID != null && monymem.MONYMEMID > 0)
                    _record = _entity_crm.Single<MONYMEM>(a => a.MONYMEMID == monymem.MONYMEMID);
                if (_record == null)
                {
                    _record = new MONYMEM() { MID = monymem.MID, MEMTYPEID = monymem.MEMTYPEID, MEMNAME = monymem.MEMNAME, UPDATEDON = DateTime.Now };

                    if (monymem.PID != null && monymem.PID > 0)
                    {
                        MONY _mony = _entity_crm.Single<MONY>(a => a.MID == monymem.MID);
                        _sql = "exec z_do_adjustment '" + adjtype + "'," + monymem.MID +
                            ",'" + DateTime.Today.ToShortDateString() + "'," + _mony.AMT.ToString() + "," + crmSession.UID() +
                            "," + monymem.PID.ToString() + "," + "null";
                        var q = _entity_crm.getContext().Database.SqlQuery<result_z_do_adjustment>(_sql);
                        result_z_do_adjustment result2 = q.FirstOrDefault();
                        if (result2.MID > 0)
                        {
                            _record.MEMMID = result2.MID;
                        }
                        else
                        {
                            _response = new genericResponse() { success = false, __count = 0, message = "Problem creating Hon/Mem honoree gift record!" };
                            return _response;
                        }
                    }

                    _entity_crm.Add(_record);
                    _entity_crm.CommitChanges();
                }
                else
                {
                    if (_record.MEMMID != null && _record.MEMMID > 0)
                    {
                        MONY _honoree = _entity_crm.Single<MONY>(a => a.MID == _record.MEMMID);
                        if (_honoree.PID != monymem.PID || (monymem.PID == null || monymem.PID == 0))
                        {
                            // remove existing soft credit record
                            genericResponse result = UndoAdjust((int)_record.MEMMID);
                            if (!result.success)
                            {
                                _response = new genericResponse() { success = false, __count = 0, message = "Problem removing existing Hon/Mem honoree gift record!" };
                                return _response;
                            }
                            // undo adjustment delete linked MONYMEM record
                            _record = new MONYMEM();
                            _record.MEMMID = null;
                        }
                    }
                    
                    if (monymem.PID != null && monymem.PID > 0 && (_record.MEMMID == null || _record.MEMMID == 0))
                    {
                        MONY _mony = _entity_crm.Single<MONY>(a => a.MID == monymem.MID);
                        _sql = "exec z_do_adjustment '" + adjtype + "'," + monymem.MID +
                            ",'" + DateTime.Today.ToShortDateString() + "'," + _mony.AMT.ToString() + "," + crmSession.UID() +
                            "," + monymem.PID.ToString() + "," + "null";
                        var q = _entity_crm.getContext().Database.SqlQuery<result_z_do_adjustment>(_sql);
                        result_z_do_adjustment result2 = q.FirstOrDefault();
                        if (result2.MID > 0)
                            _record.MEMMID = result2.MID;
                        else
                        {
                            _response = new genericResponse() { success = false, __count = 0, message = "Problem creating Hon/Mem honoree gift record!" };
                            return _response;
                        }
                    }

                    _record.MID = monymem.MID;
                    _record.MEMTYPEID = monymem.MEMTYPEID;
                    _record.MEMNAME = monymem.MEMNAME;
                    _record.UPDATEDON = DateTime.Now;

                    if (_record.MONYMEMID == null)
                        _entity_crm.Add(_record);
                    else
                        _entity_crm.Update(_record);
                    _entity_crm.CommitChanges();
                }

                List<iItemType> _dataset = new List<iItemType>();
                _record.PID = monymem.PID;
                _dataset.Add(_record);
                _response = new genericResponse() { success = true, __count = _dataset.Count, results = _dataset.ToList() };
                return _response;
            }

            catch (Exception e)
            {
                _response = new genericResponse() { success = false, __count = 0, message = "Problem saving Hon/Mem honoree!" + e.Message };
                return _response;
            }
        }
        #endregion

        #region [[ crm/api/MonyMem/updMonyMemType  ]]
        [HttpPost, Route("crm/api/MonyMem/updMonyMemType")]
        [apiAuthorize(AccessElement = "cmdiapp.dms.People_Money", AccessLevel = "e")]
        public genericResponse updMonyMemType(MONYMEM monymem)
        {
            genericResponse _response;
            try
            {
                MONYMEM _record = _entity_crm.Single<MONYMEM>(a => a.MONYMEMID == monymem.MONYMEMID);
                if (_record != null)
                {
                    _record.MEMTYPEID = monymem.MEMTYPEID;
                    _entity_crm.Update(_record);
                    _entity_crm.CommitChanges();

                    if (_record.MEMMID != null && _record.MEMMID > 0)
                    {
                        // update adjustment type
                        lkMEMTYPE memtype = _entity_crm.Single<lkMEMTYPE>(a => a.MEMTYPEID == monymem.MEMTYPEID);
                        string adjtype = (memtype != null && memtype.MEMTYPE == "SUP") ? "SU" : "HH";

                        string _sql = @"UPDATE m SET ADJTYPEID = (SELECT TOP 1 ADJTYPEID FROM lkADJTYPE WHERE ADJTYPE = '{0}'), _updating_uid = {1} FROM MONY m WHERE m.MID = {2}";
                        string _updatesql = String.Format(_sql, adjtype, crmSession.UID(), _record.MEMMID);
                        var result = _entity_crm.getContext().Database.ExecuteSqlCommand(_updatesql);
                    }
                    _response = new genericResponse() { success = true };
                }
                else
                    _response = new genericResponse() { success = false, __count = 0, message = "Problem updating Hon/Mem Type!" };
                return _response;
            }
            catch (Exception ex)
            {
                _response = new genericResponse() { success = false, __count = 0, message = "Problem updating Hon/Mem Type!" };
                return _response;
            }
        }
        #endregion

        #region [[ crm/api/MonyMem/Delete/{id}  ]]
        [HttpPost, Route("crm/api/MonyMem/Delete/{id}")]
        [apiAuthorize(AccessElement = "cmdiapp.dms.People_Money", AccessLevel = "d")]
        public genericResponse DelMonyMem(int id)
        {
            genericResponse _response;
            try
            {
                MONYMEM _record = _entity_crm.Single<MONYMEM>(a => a.MONYMEMID == id);
                if (_record != null)
                {
                    // remove existing soft credit record
                    if (_record.MEMMID != null && _record.MEMMID > 0)
                    {
                        genericResponse result = UndoAdjust((int)_record.MEMMID);
                        if (!result.success)
                        {
                            _response = new genericResponse() { success = false, __count = 0, message = result.message };
                            return _response;
                        }
                    }
                    else
                    {
                        _entity_crm.Delete(_record);
                        _entity_crm.CommitChanges();
                    }
                }
                _response = new genericResponse() { success = true };
                return _response;
            }
            catch (Exception ex)
            {
                _response = new genericResponse() { success = false, __count = 0, message = "Problem removing Hon/Mem honoree!" };
                return _response;
            }
        }
        #endregion

        #region [[ crm/api/MonyMem/GetLinks ]]
        [HttpGet, Route("crm/api/MonyMem/GetLinks/{id}")]  
        [apiAuthorize(AccessElement = "cmdiapp.dms.People_Money", AccessLevel = "v")]
        public List<honmemLink> GetMonyMemLinks(int id)
        {
            string _sql = @"SELECT a.RELATETO as PID, p1.FULLNAME + ' (' + b.RECIPROCAL+ ') ' + p.FULLNAME as NAME 
                FROM MONYMEM mm INNER JOIN MONY m ON mm.MEMMID = m.MID
                    INNER JOIN jtRELATE a ON m.PID = a.PID 
                    INNER JOIN lkRELATETYPE b ON a.RELATETYPEID = b.RELATETYPEID
                    INNER JOIN v_people p1 ON a.RELATETO = p1.PID
                    INNER JOIN v_people p ON a.PID = p.PID
                WHERE mm.MID = {0}
                UNION
                SELECT c.PID as PID, p2.FULLNAME + ' (' + d.DESCRIP + ') ' + p.FULLNAME as NAME
                FROM MONYMEM mm INNER JOIN MONY m ON mm.MEMMID = m.MID
                    INNER JOIN jtRELATE c ON m.PID = c.RELATETO 
                    INNER JOIN lkRELATETYPE d ON c.RELATETYPEID = d.RELATETYPEID
                    INNER JOIN v_people p2 ON c.PID = p2.PID
                    INNER JOIN v_people p ON c.RELATETO = p.PID 
                WHERE mm.MID = {0}
                ORDER BY NAME";
            return _entity_crm.getContext().Database.SqlQuery<honmemLink>(String.Format(_sql, id)).ToList();
        }
        #endregion

        #region [[ crm/api/mony/getMonyAdj/{adjtype}/{id} ]]
        [HttpGet, Route("crm/api/mony/getMonyAdj/{adjtype}/{id}")]
        [apiAuthorize(AccessElement = "cmdiapp.dms.People_Money", AccessLevel = "v")]
        public genericResponse getMonyAdj(string adjtype, int id, int? page, int? pageSize)
        {
            genericResponse _response;
            try
            {
                #region [ Retrieve "Sort" options ]
                string sortField = HttpContext.Current.Request.QueryString["sort[0][field]"];
                string sortDir = HttpContext.Current.Request.QueryString["sort[0][dir]"];

                int _pageSize = (pageSize == null ? 10 : pageSize.Value);
                int _pageNo = (page == null ? 1 : page.Value);

                string _sortOptions = "";
                if (!string.IsNullOrEmpty(sortField))
                    _sortOptions = sortField;
                if (!string.IsNullOrEmpty(sortField) && !string.IsNullOrEmpty(sortDir))
                    _sortOptions = _sortOptions + " " + sortDir;
                if (string.IsNullOrEmpty(_sortOptions)) _sortOptions = "MID";
                #endregion

                List<vwMonyAdjustment_ext> _list = new List<vwMonyAdjustment_ext>();
                string _sql = String.Format("SELECT * FROM v_mony_adjustment WHERE ORIGMID = {0} AND ADJTYPE = ''{1}''", id, adjtype);
                var q = _entity_crm.getContext().Database.SqlQuery<vwMonyAdjustment_ext>(String.Format("EXEC dbo.get_pagedDataSet '{0}', '{1}', {2}, {3}", _sql, _sortOptions, _pageSize, _pageNo));
                _list = q.ToList();
                Mapper.CreateMap<vwMonyAdjustment_ext, vwMonyAdjustment>();
                IEnumerable<iItemType> results = _list.Select(a => Mapper.Map<vwMonyAdjustment_ext, vwMonyAdjustment>(a)).ToList();
                if (results.Count() > 0)
                {
                    _response = new genericResponse() { success = true, __count = _list.FirstOrDefault().count_, results = results.ToList() };
                }
                else
                {
                    _response = new genericResponse() { success = true, __count = 0 };
                }
                return _response;
            }
            catch (Exception ex)
            {
                _response = new genericResponse() { success = false, __count = 0 };
                return _response;
            }
        }
        #endregion

        #region [[ crm/api/MonyMemAckw/Add ]]
        [HttpPost, Route("crm/api/MonyMemAckw/Add")]
        [apiAuthorize(AccessElement = "cmdiapp.dms.People_Money", AccessLevel = "e")]
        public genericResponse AddMonyMemAckw(parm_honmemackw _ackw)
        {
            genericResponse _response;
            try
            {
                if (_ackw.PID > 0 )
                {
                    string _sql = "exec z_do_adjustment 'HA'," + _ackw.MID +
                        ",'" + DateTime.Today.ToShortDateString() + "'," + _ackw.AMT.ToString() + "," + crmSession.UID() +
                        "," + _ackw.PID + "," + "null";
                    var q = _entity_crm.getContext().Database.SqlQuery<result_z_do_adjustment>(_sql);
                    result_z_do_adjustment result = q.FirstOrDefault();
                    if (result.MID > 0)
                    {
                        _response = new genericResponse() { success = true };
                        return _response;
                    }
                    else
                    {
                        _response = new genericResponse() { success = false, message = "Problem adding Hon/Mem Acknowledgment!" };
                        return _response;
                    }
                }
                else
                {
                    _response = new genericResponse() { success = false, message = "Problem adding Hon/Mem Acknowledgment!" };
                    return _response;
                }
            }
            catch (Exception e)
            {
                _response = new genericResponse() { success = false, message = "Problem adding Hon/Mem Acknowledgment!" };
                return _response;
            }
        }
        #endregion

        #region [[ crm/api/HonMemAckw/Delete/{id} ] ]
        [HttpPost, Route("crm/api/HonMemAckw/Delete/{id}")]
        [apiAuthorize(AccessElement = "cmdiapp.dms.People_Money", AccessLevel = "d")]
        public genericResponse DelHonMemAckw(int id)
        {
            genericResponse _response;
            try
            {
                genericResponse result = UndoAdjust(id);
                if (!result.success)
                {
                    _response = new genericResponse() { success = false, message = result.message };
                    return _response;
                }
                else
                {
                    _response = new genericResponse() { success = true };
                    return _response;
                }
            }
            catch
            {
                _response = new genericResponse() { success = false, message = "Problem removing Hon/Mem Acknowledgment!" };
                return _response;
            }
        }
        #endregion

        #region [[ crm/api/mony/HonMemAckw/{id} ]]
        [HttpGet, Route("crm/api/mony/HonMemAckw/{id}")]
        [apiAuthorize(AccessElement = "cmdiapp.dms.People_Money", AccessLevel = "v")]
        public object getHonMemAckw(int id)
        {
            string _sql = String.Format("select distinct m.pid from mony m inner join lkadjtype t on m.adjtypeid = t.adjtypeid where m.origmid = {0} and t.adjtype = 'HA' and m.counter = 1", id);
            var q = _entity_crm.getContext().Database.SqlQuery<int>(_sql).ToList();
            return q;
        }
        #endregion

        #region [[ crm/api/MonyMem/GetOrigDnr ]] GetBy MID
        [HttpGet, Route("crm/api/MonyMem/GetOrigDnr/{id}")]  // MID (Read)
        [apiAuthorize(AccessElement = "cmdiapp.dms.People_Money", AccessLevel = "v")]
        public object GetMonyMemOrigDnr(int id)
        {
            var q = from m in _entity_crm.All<MONY>()
                    join p in _entity_crm.All<PeopleF>() on m.PID equals p.PID
                    where m.MID == id
                    select new { PID = p.PID, NAME = p.FULLNAME };
            return q;
        }
        #endregion

        #region [[ crm/api/MonyMem/GetMonyMemHonor ]] GetBy MID
        [HttpGet, Route("crm/api/MonyMem/GetMonyMemHonor/{id}")]  // MID (Read)
        [apiAuthorize(AccessElement = "cmdiapp.dms.People_Money", AccessLevel = "v")]
        public object GetMonyMemHonor(int id)
        {
            var q = from mm in _entity_crm.All<MONYMEM>()
                    join m in _entity_crm.All<MONY>() on mm.MEMMID equals m.MID 
                    join p in _entity_crm.All<PeopleF>() on m.PID equals p.PID
                    join lm in _entity_crm.All<lkMEMTYPE>() on mm.MEMTYPEID equals lm.MEMTYPEID
                    where mm.MID == id
                    select new { PID = p.PID, NAME = p.FULLNAME, TYPE = lm.DESCRIP };
            return q;
        }
        #endregion

        #region
        [HttpPost, Route("crm/api/MonyMem/MailMergeKey/{id}")]
        public string MailMergeKey(int id)
        {
            string _sql = String.Format(@"SELECT TOP 500
                    p.PID,
                    a.MID,
                    p.PREFIX,
                    p.FNAME,
                    p.MNAME,
                    p.LNAME,
                    p.SUFFIX,
                    p.SALUTATION,
                    p.INFSALUT,
                    p.STREET,
                    p.ADDR1,
                    p.ADDR2,
                    p.CITY,
                    p.STATE,
                    p.ZIP,
                    p.PLUS4,
                    a.BATCHDTE,
                    a.AMT 
                FROM v_mony_adjustment a INNER JOIN v_people p ON a.PID = p.PID WHERE ORIGMID = {0} AND ADJTYPE = '{1}'", id, "HA");
            var q = _entity_crm.getContext().Database.SqlQuery<HonMemAckw>(_sql);
                              
            DataTable table = q.ToList().ToDataTable();

            if (table != null)
            {
                //HttpResponse _resp = CreateExcelFile.CreateExcelDocument(_dt.Copy(), fileName, HttpContext.Current.Response);
                string key_ = System.Guid.NewGuid().ToString();
                //cache datatable
                DataTable dt = table;
                HttpRuntime.Cache.Insert(key_, dt, null, DateTime.UtcNow.Add(new TimeSpan(1, 0, 1)), System.Web.Caching.Cache.NoSlidingExpiration);
                //return the Key
                return key_;
            }
            else
            {
                return null;
            }
        }
        #endregion

        #endregion

        #region [[ ALF Corp Match gifts ]]

        #region [ crm/api/Mony/isEmployer/{pid}/{donorpid} ]
        [HttpGet, Route("crm/api/Mony/isEmployer/{pid}/{donorpid}")]
        public bool isEmployer(int pid, int donorpid)
        {
            return _entity_crm.All<jtRELATE>()
                .Where(r => r.PID == pid && r.RELATETO == donorpid && r.RELATETYPEID == 1)
                .Any();
        }
        #endregion

        #region [ crm/api/Mony/GetCorpMatched/{id} ]
        [HttpGet, Route("crm/api/Mony/GetCorpMatched/{id}")]
        [apiAuthorize(AccessElement = "cmdiapp.dms.People_Money", AccessLevel = "v")]
        public genericResponse GetCorpMatched(int id, int? page, int? pageSize)
        {
            genericResponse _response;
            try
            {
                #region [ Retrieve "Sort" options ]
                string sortField = HttpContext.Current.Request.QueryString["sort[0][field]"];
                string sortDir = HttpContext.Current.Request.QueryString["sort[0][dir]"];

                int _pageSize = (pageSize == null ? 10 : pageSize.Value);
                int _pageNo = (page == null ? 1 : page.Value);

                string _sortOptions = "";
                if (!string.IsNullOrEmpty(sortField))
                    _sortOptions = sortField;
                if (!string.IsNullOrEmpty(sortField) && !string.IsNullOrEmpty(sortDir))
                    _sortOptions = _sortOptions + " " + sortDir;
                if (string.IsNullOrEmpty(_sortOptions)) _sortOptions = "MID";
                #endregion

                List<corpmatchLink_ext> _list = new List<corpmatchLink_ext>();
                string _sql = String.Format(@"select a.dtMATCHID, a.MID, p.PID, dbo.oFULLNAME(0, p.PREFIX, p.FNAME, p.MNAME, p.LNAME, p.SUFFIX) as NAME, e.DESCRIP as GIFTTYPE,
	                indmatch.BATCHDTE, indmatch.AMT, ISNULL(f.FUNDCODE,'') + '-' + ISNULL(c.CENTERCODE,'') + '-' + ISNULL(CAST(indmatch.MONYCODE as VARCHAR),'') + '-' +
	                ISNULL(t.SRCTYPE,'') + '-' + ISNULL(g.CAMPGNCODE,'') as ACCTCODE
                from dtMATCH a 
	                inner join MONY corpmatch on a.MGMID = corpmatch.MID   
	                inner join MONY indmatch on a.MID = indmatch.MID
		                inner join PEOPLE p on indmatch.PID = p.PID 
		                left join dmFUND f on indmatch.FUNDID = f.FUNDID
		                left join dmCENTER c on indmatch.CENTERID = c.CENTERID
		                left join lkSRCTYPE t on indmatch.SRCTYPEID = t.SRCTYPEID
		                left join dmCAMPGN g on indmatch.CAMPGNID = g.CAMPGNID
		                left join lkGIFTTYPE e on indmatch.GIFTTYPEID = e.GIFTTYPEID 		
	                left join MONY softcredit on softcredit.MID = a.CRMID
                where a.MGMID = {0}", id);
                _sql = _sql.Trim().Replace("'", "''");
                var q = _entity_crm.getContext().Database.SqlQuery<corpmatchLink_ext>(String.Format("EXEC dbo.get_pagedDataSet '{0}', '{1}', {2}, {3}", _sql, _sortOptions, _pageSize, _pageNo));
                _list = q.ToList();
                Mapper.CreateMap<corpmatchLink_ext, corpmatchLink>();
                IEnumerable<iItemType> results = _list.Select(a => Mapper.Map<corpmatchLink_ext, corpmatchLink>(a)).ToList();
                if (results.Count() > 0)
                {
                    _response = new genericResponse() { success = true, __count = _list.FirstOrDefault().count_, results = results.ToList() };
                }
                else
                {
                    _response = new genericResponse() { success = true, __count = 0 };
                }
                return _response;
            }
            catch
            {
                _response = new genericResponse() { success = false, __count = 0 };
                return _response;
            }
        }
        #endregion

        #region [ crm/api/Mony/GetCorpNotMatched/{id} ]
        [HttpGet, Route("crm/api/Mony/GetCorpNotMatched/{id}")]
        [apiAuthorize(AccessElement = "cmdiapp.dms.People_Money", AccessLevel = "v")]
        public genericResponse GetCorpNotMatched(string searchText, int id, int? page, int? pageSize)
        {
            genericResponse _response;
            try
            {
                #region [ Retrieve "Sort" options ]
                string sortField = HttpContext.Current.Request.QueryString["sort[0][field]"];
                string sortDir = HttpContext.Current.Request.QueryString["sort[0][dir]"];

                int _pageSize = (pageSize == null ? 10 : pageSize.Value);
                int _pageNo = (page == null ? 1 : page.Value);

                string _sortOptions = "";
                if (!string.IsNullOrEmpty(sortField))
                    _sortOptions = sortField;
                if (!string.IsNullOrEmpty(sortField) && !string.IsNullOrEmpty(sortDir))
                    _sortOptions = _sortOptions + " " + sortDir;
                if (string.IsNullOrEmpty(_sortOptions)) _sortOptions = "NAME, BATCHDTE DESC";
                #endregion

                List<corpmatchLink_ext> _list = new List<corpmatchLink_ext>();
                string _sql = "";
                if (!string.IsNullOrEmpty(searchText))
                {
                    searchText = Library.util.kill_sqlBlacklistWord(searchText);
                    int pid;

                    #region [[ by PID ]]
                    if (int.TryParse(searchText, out pid))
                    {
                        _sql = String.Format(@"select 0 as dtMATCHID, M.MID, p.PID, dbo.oFULLNAME(0, p.PREFIX, p.FNAME, p.MNAME, p.LNAME, p.SUFFIX) as NAME, GT.DESCRIP as GIFTTYPE,
                            M.BATCHDTE, M.AMT, ISNULL(f.FUNDCODE,'') + '-' + ISNULL(ct.CENTERCODE,'') + '-' + ISNULL(CAST(m.MONYCODE as VARCHAR),'') + '-' +
	                        ISNULL(t.SRCTYPE,'') + '-' + ISNULL(cp.CAMPGNCODE,'') as ACCTCODE
                        FROM    MONY m

                                INNER JOIN PEOPLE p ON m.PID = p.PID
                                INNER JOIN dmFUND f ON m.FUNDID = f.FUNDID

                                INNER JOIN lkGIFTTYPE gt ON m.GIFTTYPEID = gt.GIFTTYPEID 

                                LEFT OUTER JOIN lkSRCTYPE t ON m.SRCTYPEID = t.SRCTYPEID
                                LEFT OUTER JOIN dmCENTER ct ON m.CENTERID=ct.CENTERID
                                LEFT OUTER JOIN dmCAMPGN cp ON m.CAMPGNID= cp.CAMPGNID
                        WHERE
                            p.PID = {0}
                                AND
                        m.COUNTER = 1 and m.SOFTMONEY<> 1 AND
                        m.MID not in (SELECT a.MID FROM dtMATCH a, MONY b, MONY c WHERE a.MID = b.MID and a.MGMID = c.MID AND b.COUNTER = 1 and c.COUNTER = 1) AND
                        m.MID not in (SELECT ORIGMID FROM MONY WHERE PID = p.PID AND ORIGMID IS NOT NULL AND COUNTER = 1)", pid);

                    }
                    #endregion

                    #region [[ by Name ]]
                    else
                    {
                        string _where = "";

                        if (!string.IsNullOrEmpty(searchText))
                        {
                            #region [ Split Search Text > string1, string2 & string3 and create "Where" statement ]
                            char[] delimiterChars = { ' ', ',', '.', ';', ':', '|', '\t' };

                            searchText = searchText.Replace("''", "'");

                            string[] words = searchText.Split(delimiterChars);

                            if (words.Count() > 0)
                            {
                                string string1 = words[0];
                                string string2 = (words.Count() > 1 ? words[1] : "");
                                string string3 = (words.Count() > 2 ? words[2] : "");

                                string _FNAME_where = "";

                                #region [ Where Clause ]

                                _FNAME_where = q_library.get_normFNAMEwhere("FNAME", string1, _dataService);

                                switch (words.Count())
                                {
                                    case 1: // First or Last Name
                                        _where = _where + string.Format("(" + _FNAME_where + " OR LNAME LIKE '{0}%')", string1);
                                        break;

                                    case 2: // First and Last Name  OR First OR Last
                                        _where = _where + string.Format("((" + _FNAME_where + " AND LNAME LIKE '{0}%') OR (FNAME LIKE '{1}%' AND LNAME LIKE '{1}%'))", string2, searchText);
                                        break;

                                    case 3: // First, Middle and Last Name  OR Last 
                                        _where = _where + string.Format("((" + _FNAME_where + " AND MNAME LIKE '{0}%' AND LNAME LIKE '{1}%') OR (FNAME LIKE '{2}%' AND LNAME LIKE '{2}%'))", string2, string3, searchText);
                                        break;
                                    default:
                                        break;
                                }
                                #endregion
                            }
                            #endregion
                            _sql = String.Format(@"select 0 as dtMATCHID, M.MID, p.PID, dbo.oFULLNAME(0, p.PREFIX, p.FNAME, p.MNAME, p.LNAME, p.SUFFIX) as NAME, GT.DESCRIP as GIFTTYPE,
                                    M.BATCHDTE, M.AMT, ISNULL(f.FUNDCODE,'') + '-' + ISNULL(ct.CENTERCODE,'') + '-' + ISNULL(CAST(m.MONYCODE as VARCHAR),'') + '-' +
	                                ISNULL(t.SRCTYPE,'') + '-' + ISNULL(cp.CAMPGNCODE,'') as ACCTCODE
                                FROM    MONY m

                                        INNER JOIN PEOPLE p ON m.PID = p.PID
                                        INNER JOIN dmFUND f ON m.FUNDID = f.FUNDID

                                        INNER JOIN lkGIFTTYPE gt ON m.GIFTTYPEID = gt.GIFTTYPEID 

                                        LEFT OUTER JOIN lkSRCTYPE t ON m.SRCTYPEID = t.SRCTYPEID
                                        LEFT OUTER JOIN dmCENTER ct ON m.CENTERID=ct.CENTERID
                                        LEFT OUTER JOIN dmCAMPGN cp ON m.CAMPGNID= cp.CAMPGNID
                                WHERE {0} AND
                                m.COUNTER = 1 and m.SOFTMONEY<> 1 AND
                                m.MID not in (SELECT a.MID FROM dtMATCH a, MONY b, MONY c WHERE a.MID = b.MID and a.MGMID = c.MID AND b.COUNTER = 1 and c.COUNTER = 1) AND
                                m.MID not in (SELECT ORIGMID FROM MONY WHERE PID = p.PID AND ORIGMID IS NOT NULL AND COUNTER = 1)", _where);
                        }
                        #endregion
                    }
                }
                else { 
                    _sql = String.Format(@"select 0 as dtMATCHID, M.MID, p.PID, dbo.oFULLNAME(0, p.PREFIX, p.FNAME, p.MNAME, p.LNAME, p.SUFFIX) as NAME, GT.DESCRIP as GIFTTYPE,
                            M.BATCHDTE, M.AMT, ISNULL(f.FUNDCODE,'') + '-' + ISNULL(ct.CENTERCODE,'') + '-' + ISNULL(CAST(m.MONYCODE as VARCHAR),'') + '-' +
	                        ISNULL(t.SRCTYPE,'') + '-' + ISNULL(cp.CAMPGNCODE,'') as ACCTCODE
                        FROM    MONY m

                                INNER JOIN PEOPLE p ON m.PID = p.PID
                                INNER JOIN dmFUND f ON m.FUNDID = f.FUNDID

                                INNER JOIN lkGIFTTYPE gt ON m.GIFTTYPEID = gt.GIFTTYPEID /* AND gt.GIFTTYPE in ('HM','MT','TP') */

                                LEFT OUTER JOIN lkSRCTYPE t ON m.SRCTYPEID = t.SRCTYPEID
                                LEFT OUTER JOIN dmCENTER ct ON m.CENTERID=ct.CENTERID
                                LEFT OUTER JOIN dmCAMPGN cp ON m.CAMPGNID= cp.CAMPGNID

                                INNER JOIN jtRELATE jr ON P.PID = jr.RELATETO AND jr.RELATETYPEID = 1
                        WHERE
                            jr.PID = {0}
                                AND
                        m.COUNTER = 1 and m.SOFTMONEY<> 1 AND
                        m.MID not in (SELECT a.MID FROM dtMATCH a, MONY b, MONY c WHERE a.MID = b.MID and a.MGMID = c.MID AND b.COUNTER = 1 and c.COUNTER = 1) AND
                        m.MID not in (SELECT ORIGMID FROM MONY WHERE PID = p.PID AND ORIGMID IS NOT NULL AND COUNTER = 1)", id);
                }
                _sql = _sql.Trim().Replace("'", "''");
                var q = _entity_crm.getContext().Database.SqlQuery<corpmatchLink_ext>(String.Format("EXEC dbo.get_pagedDataSet '{0}', '{1}', {2}, {3}", _sql, _sortOptions, _pageSize, _pageNo));
                _list = q.ToList();
                Mapper.CreateMap<corpmatchLink_ext, corpmatchLink>();
                IEnumerable<iItemType> results = _list.Select(a => Mapper.Map<corpmatchLink_ext, corpmatchLink>(a)).ToList();
                if (results.Count() > 0)
                {
                    _response = new genericResponse() { success = true, __count = _list.FirstOrDefault().count_, results = results.ToList() };
                }
                else
                {
                    _response = new genericResponse() { success = true, __count = 0 };
                }
                return _response;
            }
            catch
            {
                _response = new genericResponse() { success = false, __count = 0 };
                return _response;
            }
        }
        #endregion

        #region [[ crm/api/CorpMatch/Add ]]
        [HttpPost, Route("crm/api/CorpMatch/Add")]
        [apiAuthorize(AccessElement = "cmdiapp.dms.People_Money", AccessLevel = "e")]
        public genericResponse AddCorpMatch(dtMATCH dtmatch)
        {
            genericResponse _response;
            string _sql;
            try
            {
                MONY _mony = _entity_crm.Single<MONY>(a => a.MID == dtmatch.MID);
                if (_mony != null)
                {
                    // create soft credit record
                    _sql = "exec z_do_adjustment 'MT'," + dtmatch.MGMID +
                        ",'" + DateTime.Today.ToShortDateString() + "'," + _mony.AMT.ToString() + ',' + crmSession.UID() +
                        "," + _mony.PID.ToString() + "," + "null";
                    var q = _entity_crm.getContext().Database.SqlQuery<result_z_do_adjustment>(_sql);
                    result_z_do_adjustment result = q.FirstOrDefault();
                    if (result.MID > 0)
                    {
                        // carry SUPPORT adjustments from Ind Match gift
                        lkADJTYPE adjtype = _entity_crm.Single<lkADJTYPE>(a => a.ADJTYPE == "SU");
                        List<MONY> _adjust = _entity_crm.All<MONY>().Where(m => m.ORIGMID == dtmatch.MID && m.ADJTYPEID == adjtype.ADJTYPEID).ToList();
                        for (int i = 0; i < _adjust.Count; i++)
                        {
                            _sql = "exec z_do_adjustment 'SU'," + dtmatch.MGMID +
                                ",'" + DateTime.Today.ToShortDateString() + "'," + _adjust[i].AMT.ToString() + ',' + crmSession.UID() +
                                "," + _adjust[i].PID.ToString() + "," + "null";
                            result_z_do_adjustment adjresult = _entity_crm.getContext().Database.SqlQuery<result_z_do_adjustment>(_sql).FirstOrDefault();
                        }

                        // save Corp Match link
                        dtMATCH _record = new dtMATCH();
                        _record.MGMID = dtmatch.MGMID;
                        _record.MID = dtmatch.MID;
                        _record.CRMID = result.MID;
                        _record.UPDATEDON = DateTime.Now;
                        _entity_crm.Add(_record);

                        // check Employer relationship
                        int pid = _entity_crm.Single<MONY>(m => m.MID == dtmatch.MGMID).PID;
                        int donorpid = _entity_crm.Single<MONY>(m => m.MID == dtmatch.MID).PID;
                        if (!isEmployer(pid, donorpid))
                        {
                            jtRELATE _jtrelate = new jtRELATE() { PID = pid, RELATETO = donorpid, RELATETYPEID = 1 };
                            _entity_crm.Add(_jtrelate);
                        }                      

                        _entity_crm.CommitChanges();
                        _response = new genericResponse() { success = true };
                        return _response;
                    }
                    else
                    {
                        _response = new genericResponse() { success = false, message = "Problem creating Corp Match Soft Credit record!" };
                        return _response;
                    }
                }
                else
                {
                    _response = new genericResponse() { success = false, message = "Missing Matched record!" };
                    return _response;
                }
            }

            catch (Exception e)
            {
                _response = new genericResponse() { success = false, message = "Problem creating Corp Match Link!" };
                return _response;
            }
        }
        #endregion

        #region [[ crm/api/CorpMatch/Delete  ]]
        [HttpPost, Route("crm/api/CorpMatch/Delete/{id}")]
        [apiAuthorize(AccessElement = "cmdiapp.dms.People_Money", AccessLevel = "d")]
        public genericResponse deleteCorpMatch(int id)
        {
            genericResponse _response;
            try
            {
                string _sql = "";
                _sql = string.Format("exec wp_delete_corpmatch {0}, {1}", id, crmSession.UID());
                var q = _entity_crm.getContext().Database.SqlQuery<result_delete_corpmatch>(_sql);
                result_delete_corpmatch result = q.FirstOrDefault();
                if (result.SUCCESS)
                {
                    _response = new genericResponse() { success = true };
                    return _response;
                }
                else
                {
                    _response = new genericResponse() { success = false, message = result.MESSAGE };
                    return _response;
                }
            }
            catch (Exception ex)
            {
                _response = new genericResponse() { success = false, __count = 0, message = "Problem removing Corp Match Link!" };
                return _response;
            }
        }

        #endregion

        #region [ crm/api/Mony/GetIndMatched/{id} ]
        [HttpGet, Route("crm/api/Mony/GetIndMatched/{id}")]
        [apiAuthorize(AccessElement = "cmdiapp.dms.People_Money", AccessLevel = "v")]
        public genericResponse GetIndMatched(int id, int? page, int? pageSize)
        {
            genericResponse _response;
            try
            {
                #region [ Retrieve "Sort" options ]
                string sortField = HttpContext.Current.Request.QueryString["sort[0][field]"];
                string sortDir = HttpContext.Current.Request.QueryString["sort[0][dir]"];

                int _pageSize = (pageSize == null ? 10 : pageSize.Value);
                int _pageNo = (page == null ? 1 : page.Value);

                string _sortOptions = "";
                if (!string.IsNullOrEmpty(sortField))
                    _sortOptions = sortField;
                if (!string.IsNullOrEmpty(sortField) && !string.IsNullOrEmpty(sortDir))
                    _sortOptions = _sortOptions + " " + sortDir;
                if (string.IsNullOrEmpty(_sortOptions)) _sortOptions = "MID";
                #endregion

                List<corpmatchLink_ext> _list = new List<corpmatchLink_ext>();
                string _sql = String.Format(@"select a.dtMATCHID, a.MGMID as MID, p.PID, dbo.oFULLNAME(0, p.PREFIX, p.FNAME, p.MNAME, p.LNAME, p.SUFFIX) as NAME, e.DESCRIP as GIFTTYPE,
	                corpmatch.BATCHDTE, corpmatch.AMT, ISNULL(f.FUNDCODE,'') + '-' + ISNULL(c.CENTERCODE,'') + '-' + ISNULL(CAST(corpmatch.MONYCODE as VARCHAR),'') + '-' +
	                ISNULL(t.SRCTYPE,'') + '-' + ISNULL(g.CAMPGNCODE,'') as ACCTCODE
                from dtMATCH a 
	                inner join MONY corpmatch on a.MGMID = corpmatch.MID   
	                inner join MONY indmatch on a.MID = indmatch.MID
		                inner join PEOPLE p on corpmatch.PID = p.PID 
		                left join dmFUND f on corpmatch.FUNDID = f.FUNDID
		                left join dmCENTER c on corpmatch.CENTERID = c.CENTERID
		                left join lkSRCTYPE t on corpmatch.SRCTYPEID = t.SRCTYPEID
		                left join dmCAMPGN g on corpmatch.CAMPGNID = g.CAMPGNID
		                left join lkGIFTTYPE e on corpmatch.GIFTTYPEID = e.GIFTTYPEID 		
	                left join MONY softcredit on softcredit.MID = a.CRMID
                where a.MID = {0}", id);
                _sql = _sql.Trim().Replace("'", "''");
                var q = _entity_crm.getContext().Database.SqlQuery<corpmatchLink_ext>(String.Format("EXEC dbo.get_pagedDataSet '{0}', '{1}', {2}, {3}", _sql, _sortOptions, _pageSize, _pageNo));
                _list = q.ToList();
                Mapper.CreateMap<corpmatchLink_ext, corpmatchLink>();
                IEnumerable<iItemType> results = _list.Select(a => Mapper.Map<corpmatchLink_ext, corpmatchLink>(a)).ToList();
                if (results.Count() > 0)
                {
                    _response = new genericResponse() { success = true, __count = _list.FirstOrDefault().count_, results = results.ToList() };
                }
                else
                {
                    _response = new genericResponse() { success = true, __count = 0 };
                }
                return _response;
            }
            catch
            {
                _response = new genericResponse() { success = false, __count = 0 };
                return _response;
            }
        }
        #endregion

        #region [[ crm/api/mony/CorpMatched/{id} ]]
        [HttpGet, Route("crm/api/mony/CorpMatched/{id}")]
        [apiAuthorize(AccessElement = "cmdiapp.dms.People_Money", AccessLevel = "v")]
        public object getCorpMatched(int id)
        {
            List<corpmatchLink> _list = new List<corpmatchLink>();
            string _sql = String.Format(@"select a.dtMATCHID, a.MGMID as MID, p.PID, dbo.oFULLNAME(0, p.PREFIX, p.FNAME, p.MNAME, p.LNAME, p.SUFFIX) as NAME, e.DESCRIP as GIFTTYPE,
	                indmatch.BATCHDTE, indmatch.AMT, ISNULL(f.FUNDCODE,'') + '-' + ISNULL(c.CENTERCODE,'') + '-' + ISNULL(CAST(indmatch.MONYCODE as VARCHAR),'') + '-' +
	                ISNULL(t.SRCTYPE,'') + '-' + ISNULL(g.CAMPGNCODE,'') as ACCTCODE
                from dtMATCH a 
	                inner join MONY corpmatch on a.MGMID = corpmatch.MID   
	                inner join MONY indmatch on a.MID = indmatch.MID
		                inner join PEOPLE p on corpmatch.PID = p.PID 
		                left join dmFUND f on corpmatch.FUNDID = f.FUNDID
		                left join dmCENTER c on corpmatch.CENTERID = c.CENTERID
		                left join lkSRCTYPE t on corpmatch.SRCTYPEID = t.SRCTYPEID
		                left join dmCAMPGN g on corpmatch.CAMPGNID = g.CAMPGNID
		                left join lkGIFTTYPE e on corpmatch.GIFTTYPEID = e.GIFTTYPEID 		
	                left join MONY softcredit on softcredit.MID = a.CRMID
                where a.MID = {0}", id);
            var q = _entity_crm.getContext().Database.SqlQuery<corpmatchLink>(_sql);
            _list = q.ToList();
            return _list;
        }
        #endregion

        #endregion

        #region [[ crm/api/MonyConduit/Delete  ]]
        [HttpPost, Route("crm/api/MonyConduit/Delete/{id}")]
        [apiAuthorize(AccessElement = "cmdiapp.dms.People_Money", AccessLevel = "d")]
        public genericResponse deleteMonyConduit(int id)
        {
            genericResponse _response;
            try
            {
                // check if conduit in distribution
                dtCONDUITDIST _dist = _entity_crm.Single<dtCONDUITDIST>(a => a.MONYCONDUITID == id);
                if (_dist != null)
                {
                    _response = new genericResponse() { success = false, __count = 0, message = "Conduit already in Distribution. It cannot be deleted!" };
                    return _response;
                }
                MONYCONDUIT _record = _entity_crm.Single<MONYCONDUIT>(a => a.MONYCONDUITID == id);
                if (_record != null)
                { 
                    _entity_crm.Delete(_record);
                    _entity_crm.CommitChanges();
                }
                _response = new genericResponse() { success = true };
                return _response;
            }
            catch
            {
                _response = new genericResponse() { success = false, __count = 0, message = "Problem removing Conduit!" };
                return _response;
            }
        }

        #endregion

        #region
        [HttpGet, Route("crm/api/Mony/isOverLimit/{fundcode}/{id}/{amt}")]
        [apiAuthorize(AccessElement = "cmdiapp.dms.People_Money", AccessLevel = "v")]
        public bool isOverLimit(string fundcode, int id, decimal amt)
        {
            string sql = "SELECT * FROM v_people_sum WHERE PID = {0} and FUNDCODE = '{1}'";
            SummaryR _sum = _entity_crm.getContext().Database.SqlQuery<SummaryR>(string.Format(sql, id, fundcode)).FirstOrDefault();
            if (_sum == null)
                return false;
            else if (_sum.REMAIN == null)
                return false;
            else if (_sum.REMAIN < amt)
                return true;
            else
                return false;
        }
        #endregion

        #region [[ Export in Background]]
        [HttpPost, Route("crm/api/MonyStatement/ExportKey")]
        [apiAuthorize(AccessElement = "cmdiapp.dms.People_Money", AccessLevel = "v")]
        [NotLaunchpadApiAuthorize]
        public string StatementExportKey(string searchText)
        {
            string key_ = System.Guid.NewGuid().ToString();
            mony_export_data data = new JavaScriptSerializer().Deserialize<mony_export_data>(searchText);
            string _sql = $"SELECT * FROM fn_GenStatement_v2({QzLib.sanitize(data.pid.ToString())}) order by ORDERNO";
            HttpRuntime.Cache.Insert(key_, _sql, null, DateTime.UtcNow.Add(new TimeSpan(0, 0, 1)), System.Web.Caching.Cache.NoSlidingExpiration);
            return key_;
        }

        private void exportAsync(object parameter)
        {

            // 1. Retrieve Parameters
            cmdiapp.n.core.Domain.Models.asyncParam_export _param = (cmdiapp.n.core.Domain.Models.asyncParam_export)parameter;

            // 2. Run Query
            System.Data.DataSet _ds = q_library.get_dataset_w_sql__single(_param.dbConnString, _param.sql, "result", 0, 0);

            // 3. Save to an Excel file and send a Crimson notification
            cmdiapp.n.core.Areas.crm.cl_query.completeExport(_ds, _param.fileId, _param.filePath, _param.notificSql);
        }

        [HttpGet, Route("crm/api/MonyStatement/ExportAsync")]
        [apiAuthorize(AccessElement = "cmdiapp.dms.People_Money", AccessLevel = "v")]
        public bool StatementExportAsync(string key)
        {
            // Retrieve a cached SQL
            string _sql = (string)HttpRuntime.Cache[key];

            #region [ Parameters required to process ]
            string _fileId = System.Guid.NewGuid().ToString();
            cmdiapp.n.core.Domain.Models.asyncParam_export _param = new cmdiapp.n.core.Domain.Models.asyncParam_export()
            {
                sql = _sql,
                dbConnString = session.currentDomain_project._connectionString(),
                fileId = _fileId,
                filePath = System.Web.Hosting.HostingEnvironment.MapPath("~/tmp/")      // Directory
                            + String.Format("{0}_{1}_{2}__",                           // File Key (GUID + Project + User)
                                    _fileId,
                                    session.currentDomain_project.code,
                                    session.userSession.UserName)
                            + "StatementPull_"
                            + System.DateTime.Now.ToString("yyyy-MM-dd-h.mmtt")
                            + ".xlsx"
            };

            // sendNotificSQL
            string notificContent = string.Format(@"
                    <html>
                        <body style=""font-size: small; amp: ; font-family: ''Segoe UI Light'', Tahoma, arial, sans-serif;"">
                            <p>Your file is ready to download.</p>
                            <p><a href=''{0}'' target=''_blank''>Download</a></p>
                        </body>
                    </html>
                ", session.currentHostUrl + "/crm/export/download?key=" + _fileId).Replace("\r", "").Replace("\n", "").Replace("  ", " ");
            // subject
            string _pid = _sql.Substring(_sql.IndexOf("(") + 1, _sql.IndexOf(")") - _sql.IndexOf("(") - 1);
            int pid = Int32.Parse(_pid);
            PeopleF _people = _entity_crm.Single<PeopleF>(a => a.PID == pid);
            string subject = $"Gift Statement Export - File is ready for {_people.FULLNAME} ({_pid}) in {session.currentDomain_project.name}!";

            _param.notificSql = String.Format("EXEC dbo.notific_send '{0}','{1}','{2}','{3}', '{4}', {5}",
                        "Crimson",
                        subject,
                        notificContent,
                        cmdiapp.n.core.Core.session.userSession.UserName,
                        session.currentDomain_projectId,
                        session.currentDomain_project.appId);
            #endregion

            // run it asynchronously
            cmdiapp.n.core.Library.util.runItAsync(exportAsync, _param);

            return true;
        }

        [HttpPost, Route("crm/api/Mony/ExportKey")]
        [apiAuthorize(AccessElement = "cmdiapp.dms.People_Money", AccessLevel = "v")]
        [NotLaunchpadApiAuthorize]
        public string ExportKey(string searchText)
        {
            string key_ = System.Guid.NewGuid().ToString();
            mony_export_data data = new JavaScriptSerializer().Deserialize<mony_export_data>(searchText);
            string _sql = $"SELECT * FROM v_people_mony where pid = {QzLib.sanitize(data.pid.ToString())} order by BATCHDTE";
            HttpRuntime.Cache.Insert(key_, _sql, null, DateTime.UtcNow.Add(new TimeSpan(0, 0, 1)), System.Web.Caching.Cache.NoSlidingExpiration);
            return key_;
        }

        [HttpGet, Route("crm/api/Mony/ExportAsync")]
        [apiAuthorize(AccessElement = "cmdiapp.dms.People_Money", AccessLevel = "v")]
        public bool ExportAsync(string key)
        {
            // Retrieve a cached SQL
            string _sql = (string)HttpRuntime.Cache[key];

            #region [ Parameters required to process ]
            string _fileId = System.Guid.NewGuid().ToString();
            cmdiapp.n.core.Domain.Models.asyncParam_export _param = new cmdiapp.n.core.Domain.Models.asyncParam_export()
            {
                sql = _sql,
                dbConnString = session.currentDomain_project._connectionString(),
                fileId = _fileId,
                filePath = System.Web.Hosting.HostingEnvironment.MapPath("~/tmp/")      // Directory
                            + String.Format("{0}_{1}_{2}__",                           // File Key (GUID + Project + User)
                                    _fileId,
                                    session.currentDomain_project.code,
                                    session.userSession.UserName)
                            + "GiftsPull_"
                            + System.DateTime.Now.ToString("yyyy-MM-dd-h.mmtt")
                            + ".xlsx"
            };

            // sendNotificSQL
            string notificContent = string.Format(@"
                    <html>
                        <body style=""font-size: small; amp: ; font-family: ''Segoe UI Light'', Tahoma, arial, sans-serif;"">
                            <p>Your file is ready to download.</p>
                            <p><a href=''{0}'' target=''_blank''>Download</a></p>
                        </body>
                    </html>
                ", session.currentHostUrl + "/crm/export/download?key=" + _fileId).Replace("\r", "").Replace("\n", "").Replace("  ", " ");
            // subject
            string _pid = _sql.Substring(_sql.IndexOf("=") + 1, _sql.IndexOf("order by") - _sql.IndexOf("=") - 1);
            int pid = Int32.Parse(_pid);
            PeopleF _people = _entity_crm.Single<PeopleF>(a => a.PID == pid);
            string subject = $"Gift Export - File is ready for {_people.FULLNAME} ({pid}) in {session.currentDomain_project.name}!";
            _param.notificSql = String.Format("EXEC dbo.notific_send '{0}','{1}','{2}','{3}', '{4}', {5}",
                        "Crimson",
                        subject,
                        notificContent,
                        cmdiapp.n.core.Core.session.userSession.UserName,
                        session.currentDomain_projectId,
                        session.currentDomain_project.appId);
            #endregion

            // run it asynchronously
            cmdiapp.n.core.Library.util.runItAsync(exportAsync, _param);

            return true;
        }
        #endregion

        #region [ crm/api/Mony/ExportAlloc ] Export Allocations per gift
        [HttpPost, Route("crm/api/Mony/ExportAllocKey")]
        [apiAuthorize(AccessElement = "cmdiapp.dms.People_Money", AccessLevel = "v")]
        public string ExportAllocKey(string searchText)
        {
            if (string.IsNullOrEmpty(searchText)) searchText = "";

            searchParam param = new searchParam();
            param.searchText = searchText;

            string key_ = System.Guid.NewGuid().ToString();
            HttpRuntime.Cache.Insert(key_, param, null, DateTime.UtcNow.Add(new TimeSpan(1, 0, 1)), System.Web.Caching.Cache.NoSlidingExpiration);

            return key_;

        }

        [HttpGet, Route("crm/api/Mony/ExportAlloc")]
        public string ExportAlloc(string key)
        {
            userSession _userSession = session.userSession;
            searchParam _param = (searchParam)HttpRuntime.Cache[key];
            string sToday = String.Format("{0:yyyyMMMdd__hh_mm_ss tt}", DateTime.Now);
            string moduleName = "JFCAllocations";
            string fileName = moduleName + "_" + System.Guid.NewGuid() + "_" + _userSession.UserName + "_" + sToday;

            DataSet ds = null;

            string sql = string.Format("SELECT a.MID, c.PRIORITY, c.SEQ, c.CMTENAME, a.AMT FROM MONYALLOC a INNER JOIN lkJFCCMTE c ON a.JFCCMTEID = c.JFCCMTEID WHERE MID = {0} ORDER BY SEQ ", _param.searchText);
            ds = _reportService.get_dataset_w_sql__single(sql, "JFCAllocations");

            if (ds != null)
            {
                //CreateExcelFile.CreateExcelDocument(ds.Copy(), fileName, HttpContext.Current.Response);
                string key_ = System.Guid.NewGuid().ToString();
                HttpRuntime.Cache.Insert(key_, ds, null, DateTime.UtcNow.Add(new TimeSpan(1, 0, 1)), System.Web.Caching.Cache.NoSlidingExpiration);
                //return the Key
                return key_;

            }
            else
            {
                return null;
            }
        }
        #endregion

        #region [[  bulk redesignation  ]]
        [HttpGet, Route("crm/api/Mony/GetForAdjust/{pid}")]
        [apiAuthorize(AccessElement = "cmdiapp.dms.People_Money", AccessLevel = "v")]
        public List<MonyForAdj> GetMonyForAdjust(int pid)
        {
            return _monyService.GetMonyForAdjust(pid);
        }
        [HttpPost, Route("crm/api/Mony/bulkRedesignations")]
        [apiAuthorize(AccessElement = "cmdiapp.dms.People_Money", AccessLevel = "e")]
        public genericResponse bulkRedesignations(bulkRedesignationData data)
        {
            data.UID = (int)crmSession.UID();
            bool result = _monyService.bulkRedesignation(data);
            return new genericResponse() { success = result };
        }
        #endregion


        #region [[ Caging Scanned Images  ]]
        [HttpGet, Route("crm/api/Mony/ScanImage/Get/{id}")]
        [apiAuthorize(AccessElement = "cmdiapp.dms.People_Money", AccessLevel = "v")]
        [apiAuthorize(AccessElement = "/crm/DataEntryDE", AccessLevel = "v")]
        public v_cagebatch_checkdocimage GetScanImage(int id)
        {
            return _entity_crm.Single<v_cagebatch_checkdocimage>(i => i.cagebatchdtlid == id);
        }

        [HttpGet, Route("crm/api/Mony/ScanImageData/{id}")]
        [apiAuthorize(AccessElement = @"Fundraising\Money\CheckImage", AccessLevel = "v")]
        [apiAuthorize(AccessElement = "/crm/DataEntryDE", AccessLevel = "v")]
        public HttpResponseMessage scanImage(int id)
        {
            var q = _entity.getContext().Database.SqlQuery<scanimage>(string.Format("EXEC dbo.z_caging_getimage {0}", id));
            scanimage image = q.FirstOrDefault();
            byte[] imagebytes = image.imagedata.ToArray();
            HttpResponseMessage response = new HttpResponseMessage(HttpStatusCode.OK);
            var memoryStream = new MemoryStream(imagebytes);
            response.Content = new StreamContent(memoryStream);
            response.Content.Headers.ContentType = new MediaTypeHeaderValue("image/png");
            return response;
        }

        [HttpPost, Route("crm/api/Money/PrintScanCheckDataKey")]
        [apiAuthorize(AccessElement = @"Fundraising\Money\CheckImage", AccessLevel = "v")]
        public genericResponseWithKey PrintScanCheckDataKey(MoneyP moneyP)
        {
            try
            {
                if (moneyP != null && moneyP.MID > 0)
                {
                    //Create Unique GUID Here
                    string p_key = System.Guid.NewGuid().ToString();

                    #region [[ Create SQL Statement ]]
                    string _tmp_srchSQL = string.Format("EXEC dbo.cagebatch_mony_info {0}", moneyP.MID);
                    #endregion

                    #region [[ For XML + Collection ]]
                    string _xml = _supportMethods.get_report_data_PrintCheck(_tmp_srchSQL);
                    //Create collection
                    System.Xml.XmlDocument doc = new System.Xml.XmlDocument();
                    doc.LoadXml(_xml);
                    _node = doc.DocumentElement.SelectSingleNode("/ReportParameters");

                    System.Collections.Specialized.NameValueCollection _collection = new System.Collections.Specialized.NameValueCollection();
                    _collection.Add("cc", _node.Attributes["clientcode"].Value);
                    _collection.Add("rn", _node.Attributes["reportcode"].Value);
                    _collection.Add("XML", _node.OuterXml);
                    //Save the Report Parameter collection in the Session
                    string cacheKey_ = p_key + "_c";
                    HttpRuntime.Cache.Remove(cacheKey_);
                    //Now add the 
                    HttpRuntime.Cache.Insert(cacheKey_, _collection, null, DateTime.UtcNow.Add(new TimeSpan(1, 1, 0)), Cache.NoSlidingExpiration);
                    #endregion

                    #region [[ For DataSet ]]
                    DataSet _psDataset = _reportService.get_dataset_w_sql__single(_tmp_srchSQL, "GiftInfo");
                    //First Data Set 
                    cacheKey_ = p_key + "_fd";
                    HttpRuntime.Cache.Remove(cacheKey_);
                    //Now add the 
                    HttpRuntime.Cache.Insert(cacheKey_, _psDataset, null, DateTime.UtcNow.Add(new TimeSpan(1, 1, 0)), Cache.NoSlidingExpiration);
                    #endregion

                    if (_psDataset.Tables[0].Rows.Count > 0)
                    {
                        return new genericResponseWithKey() { success = true, Key = p_key };
                    }
                    else
                    {
                        return new genericResponseWithKey() { success = false, message = "There is some issue in processing Check Print request. Please try again later." };

                    }
                }
                else
                {
                    return new genericResponseWithKey() { success = false, message = "There is some issue in processing Check Print request. Please try again later." };
                }
            }
            catch (Exception ex)
            {
                return new genericResponseWithKey() { success = false, message = "There is some issue in processing Check Print request. Please try again later." };
            }
        }
        #endregion
    }
}
