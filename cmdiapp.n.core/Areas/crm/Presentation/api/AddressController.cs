﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Net;
using System.Web.Http;

using Ninject;
using Ninject.Web.Mvc;

using cmdiapp.n.core.Domain.ViewModels;
using cmdiapp.n.core.Areas.crm.Core;
using cmdiapp.n.core.Areas.crm.Domain.Models;
using cmdiapp.n.core.Areas.crm.Domain.ViewModels;
using cmdiapp.n.core.Areas.crm.Domain.Data;
using cmdiapp.n.core.Areas.crm.Domain.Repositories;
using cmdiapp.n.core.Presentation.Controllers;

namespace cmdiapp.n.core.Areas.crm.Presentation.Controllers.api
{
    ///  (controller)Address - Address
 
    [Authorize]
    [apiAuthorize(AccessElement = "cmdiapp.dms.People_Address", AccessLevel = "v")]
    public class AddressController : ApiController
    {
        private I_entity_crm _entity_crm;
        private string sql_addressReadOnly = @"
                SELECT	
	                A.ADDRESSID,
	                A.<PERSON>,
	                A<PERSON>DR<PERSON>EI<PERSON>, K.ADDRTY<PERSON>, K.<PERSON>SCRIP AS ADDRTYPEDESC,
	                STREET, ADDR1, ADDR2, 
	                CITY, A.[STATE], ZIP, PLUS4, dbo.oAddrCSZ(CITY,A.[STATE],ZIP,PLUS4) AS CiStZip,
	                C.FIPS, C.COUNTY, CDCODE, SDCODE, LDCODE,
	                LATITUDE, LONGITUDE,
	                CAST(PRIME AS BIT) AS [PRIMARY]
                FROM	ADDRESS A
		                LEFT OUTER JOIN lkADDRTYPE K ON A.ADDRTYPEID=K.ADDRTYPEID
		                LEFT OUTER JOIN lkCOUNTY C ON A.STATE=C.STATE AND A.COUNTY=C.FIPS
                ";

        public AddressController()
        {
            _entity_crm = I_entityManager_crm.getEntity();
        }

        #region [ crm/api/AddressR & Address ] GetBy AddressId
        [HttpGet, Route("crm/api/AddressR/Get/{id}")]  // AddressId (Read)
        public AddressR GetById_forRead(int id)
        {
            string sql = sql_addressReadOnly + " WHERE ADDRESSID = {0}";
            return _entity_crm.getContext().Database.SqlQuery<AddressR>(string.Format(sql, id)).ToList().FirstOrDefault();
        }

        [HttpGet, Route("crm/api/Address/Get/{id}")]  // AddressId 
        public Address GetById(int id)
        {
            return _entity_crm.Single<Address>(a => a.ADDRESSID == id);
        }
        #endregion

        #region [ crm/api/AddressR & Address ] GetBy PID 
        [HttpGet, Route("crm/api/AddressesR/Get/{id}")]  // PID (Read)
        public List<AddressR> GetByPid_forRead(int id)
        {
            string sql = sql_addressReadOnly + " WHERE PID = {0} ORDER BY PRIME DESC";
            return _entity_crm.getContext().Database.SqlQuery<AddressR>(string.Format(sql, id)).ToList();
        }

        [HttpGet, Route("crm/api/Addresses/Get/{id}")]  // PID
        public List<Address> GetByPid(int id)
        {
            return _entity_crm.All<Address>().Where(a => a.PID == id).OrderByDescending(a => a.PRIME).ToList();
        }
        #endregion

        #region [ crm/api/Address ] Save
        [HttpPost, Route("crm/api/Address/Save")]
        [apiAuthorize(AccessElement = "cmdiapp.dms.People_Address", AccessLevel = "e")]
        public genericResponse Save(Address _address)
        {
            try
            {
                int _addressid = _address.ADDRESSID;
                Address _record = _entity_crm.Single<Address>(a => a.ADDRESSID == _addressid);

                if (_record != null && _addressid != 0)
                {
                    // existing record
                    _record.updating_uid = crmSession.UID();
                    _record.PID = _address.PID;
                    _record.ADDRTYPEID = _address.ADDRTYPEID;
                    _record.PRIME = _address.PRIME;
                    _record.STREET = _address.STREET;
                    _record.ADDR1 = _address.ADDR1;
                    _record.ADDR2 = _address.ADDR2;
                    _record.CITY = _address.CITY;
                    _record.STATE = _address.STATE;
                    _record.ZIP = _address.ZIP;
                    _record.PLUS4 = _address.PLUS4;
                    _record.COMMENT = _address.COMMENT;
                    _record.CDCODE = _address.CDCODE;
                    _record.SDCODE = _address.SDCODE;
                    _record.LDCODE = _address.LDCODE;
                    _record.COUNTY = _address.COUNTY;
                    _record.CRRT = _address.CRRT;
                    _record.POSTNET = _address.POSTNET;
                    _record.COUNTRYID = _address.COUNTRYID;
                    _record.BAD = _address.BAD;
                    _entity_crm.Update(_record);
                }
                else
                {
                    // new record
                    _record = new Address();
                    _record.updating_uid = crmSession.UID();
                    _record.PID = _address.PID;
                    _record.ADDRTYPEID = _address.ADDRTYPEID;
                    _record.PRIME = _address.PRIME;
                    _record.STREET = _address.STREET;
                    _record.ADDR1 = _address.ADDR1;
                    _record.ADDR2 = _address.ADDR2;
                    _record.CITY = _address.CITY;
                    _record.STATE = _address.STATE;
                    _record.ZIP = _address.ZIP;
                    _record.PLUS4 = _address.PLUS4;
                    _record.COMMENT = _address.COMMENT;
                    _record.CDCODE = _address.CDCODE;
                    _record.SDCODE = _address.SDCODE;
                    _record.LDCODE = _address.LDCODE;
                    _record.COUNTY = _address.COUNTY;
                    _record.CRRT = _address.CRRT;
                    _record.POSTNET = _address.POSTNET;
                    _record.COUNTRYID = _address.COUNTRYID;
                    _record.BAD = _address.BAD;
                    _entity_crm.Add(_record);
                }
                _entity_crm.CommitChanges();
                _addressid = _record.ADDRESSID;

                if (_addressid <= 0)
                {
                    List<iItemType> _dataset = new List<iItemType>();
                    _dataset.Add(_address);

                    genericResponse _response = new genericResponse() { success = false, __count = _dataset.Count, results = _dataset.ToList() };
                    return _response;
                }
                else
                {
                    _address = _entity_crm.Single<Address>(a => a.ADDRESSID == _addressid);

                    List<iItemType> _dataset = new List<iItemType>();
                    _dataset.Add(_address);

                    genericResponse _response = new genericResponse() { success = true, __count = _dataset.Count, results = _dataset.ToList() };
                    return _response;
                }
            }
            catch (Exception ex)
            {
                genericResponse _response = new genericResponse() { success = false, __count = 0 };
                return _response;
            }
        }
        #endregion

        #region [ crm/api/Address ] Delete
        [HttpPost, Route("crm/api/Address/Delete")]
        [apiAuthorize(AccessElement = "cmdiapp.dms.People_Address", AccessLevel = "d")]
        public genericResponse Delete(Address _address)
        {
            try
            {
                int _addressid = _address.ADDRESSID;

                // log for history
                string _sql = "Insert into zWebAppLog (UPDTYPE,TABLENAME,KEYFIELD,KEYVALUE,UID) values " +
                    "('D','ADDRESS','ADDRESSID', {0}, {1})";
                _entity_crm.getContext().Database.ExecuteSqlCommand(_sql, _addressid, crmSession.UID());
 
                _entity_crm.Delete<Address>(a => a.ADDRESSID == _addressid);
                _entity_crm.CommitChanges();

                genericResponse _response = new genericResponse() { success = true, __count = 0 };
                return _response;
            }
            catch
            {
                genericResponse _response = new genericResponse() { success = false, __count = 0 };
                return _response;
            }
        }
        #endregion

        #region [ crm/api/Address/GetCountyName ] Retrieve County Name 
        [HttpGet, Route("crm/api/Address/GetCountyName")]  // AddressId 
        public string GetCountyName(string state, string fips)
        {
            if (string.IsNullOrEmpty(fips) || string.IsNullOrEmpty(state))
                return "";
            else
            {
                County _county = _entity_crm.Single<County>(a => a.STATE == state && a.FIPS == fips);
                if (_county == null)
                    return "";
                else
                    return _county.COUNTY;
            }
        }
        #endregion

    }
}
