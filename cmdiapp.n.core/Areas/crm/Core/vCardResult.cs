﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.Mvc;
using System.Data;
using System.Data.SqlClient;
using System.Data.OleDb;
using System.IO;
using System.Text;
using Newtonsoft.Json;
using OfficeOpenXml;
using OfficeOpenXml.Drawing.Chart;
using OfficeOpenXml.Style;
using System.Drawing;
using cmdiapp.n.core.Areas.crm.Domain.Models;
using System.Resources;
using System.Reflection;
using System.Globalization;
using System.Xml.Linq;

namespace cmdiapp.n.core.Areas.crm.Core
{
    public class vCardResult : ActionResult
    {
        #region [[ Declaration ]]
        public string _fileName { get; set; }
        public string _cardDetails { get; set; }
        public PeopleF _pplrecord { get; set; }

        public class myVcardConfig
        {
            public string nodeName { get; set; }
            public string nodeValue { get; set; }
        }

        private ResourceManager rm = null;
        private List<myVcardConfig> _myVcardConfig = new List<myVcardConfig>();
        private List<myVcardConfig> _XmlTovCardMapping = new List<myVcardConfig>();
        private List<myVcardConfig> _CrimsonToXmlMappingGeneral = new List<myVcardConfig>();
        private List<myVcardConfig> _CrimsonToXmlMappingSpecific = new List<myVcardConfig>();
        private List<myVcardConfig> _vCardTagsToCrimson = new List<myVcardConfig>();
        private List<myVcardConfig> _vCardFieldDetailsToCrimson = new List<myVcardConfig>();
        private List<myVcardConfig> _vCardPhoneSpaceBetweenValuesCheck = new List<myVcardConfig>();
        #endregion
        
        public vCardResult(string fileName, PeopleF pplRecord)
        {
            _fileName = fileName;
            _pplrecord = pplRecord;
        }

        public vCardResult()
        {
            // TODO: Complete member initialization
        }

        #region [[ Helper Methods ]]
        
        void getVcardConfigList(string sResourceFile)
        {
            rm = null;
            _myVcardConfig.Clear();
            if (sResourceFile == "cmdiapp.n.core.Resources.XmlTovCardMapping") _XmlTovCardMapping.Clear();
            if (sResourceFile == "cmdiapp.n.core.Resources.CrimsonToXmlMappingGeneral") _CrimsonToXmlMappingGeneral.Clear();
            if (sResourceFile == "cmdiapp.n.core.Resources.CrimsonToXmlMappingSpecific") _CrimsonToXmlMappingSpecific.Clear();
            if (sResourceFile == "cmdiapp.n.core.Resources.vCardTagsToCrimson") _vCardTagsToCrimson.Clear();
            if (sResourceFile == "cmdiapp.n.core.Resources.vCardFieldDetailsToCrimson") _vCardFieldDetailsToCrimson.Clear();
            if (sResourceFile == "cmdiapp.n.core.Resources.vCardPhoneSpaceBetweenValuesCheck") _vCardPhoneSpaceBetweenValuesCheck.Clear();

            rm = new ResourceManager(sResourceFile, Assembly.GetExecutingAssembly());

            if (rm != null)
            {
                foreach (var resource in rm.GetResourceSet(CultureInfo.CurrentCulture, true, true))
                {
                    var r = (System.Collections.DictionaryEntry)resource;
                    _myVcardConfig.Add(new myVcardConfig { nodeName = r.Key.ToString(), nodeValue = r.Value.ToString() });
                }
                var q = from a in _myVcardConfig orderby a.nodeName select a;

                if (sResourceFile == "cmdiapp.n.core.Resources.XmlTovCardMapping") _XmlTovCardMapping = q.ToList();
                if (sResourceFile == "cmdiapp.n.core.Resources.CrimsonToXmlMappingGeneral") _CrimsonToXmlMappingGeneral = q.ToList();
                if (sResourceFile == "cmdiapp.n.core.Resources.CrimsonToXmlMappingSpecific") _CrimsonToXmlMappingSpecific = q.ToList();
                if (sResourceFile == "cmdiapp.n.core.Resources.vCardTagsToCrimson") _vCardTagsToCrimson = q.ToList();
                if (sResourceFile == "cmdiapp.n.core.Resources.vCardFieldDetailsToCrimson") _vCardFieldDetailsToCrimson = q.ToList();
                if (sResourceFile == "cmdiapp.n.core.Resources.vCardPhoneSpaceBetweenValuesCheck") _vCardPhoneSpaceBetweenValuesCheck = q.ToList();
            }
        }

        public string getXmlStringForVCard(PeopleF ppl)
        {
            string sRetXmlStr = "<contacts><contact>";
            string snodeName = "";
            string sTempStr = "";

            if (_CrimsonToXmlMappingGeneral != null)
            {
                for (int iGenLoop = 0; iGenLoop < _CrimsonToXmlMappingGeneral.Count; iGenLoop++)
                {
                    sTempStr = _CrimsonToXmlMappingGeneral[iGenLoop].nodeValue;
                    for (int iSpecLoop = 0; iSpecLoop < _CrimsonToXmlMappingSpecific.Count; iSpecLoop++)
                    {
                        snodeName = _CrimsonToXmlMappingSpecific[iSpecLoop].nodeName.Substring(2);
                        sTempStr = sTempStr.Replace(snodeName, getValueOfEditComponent(_CrimsonToXmlMappingSpecific[iSpecLoop].nodeValue, ppl));
                        sTempStr = sTempStr.Replace("&", "");
                    }
                    sRetXmlStr = sRetXmlStr + sTempStr;
                }
                sRetXmlStr = sRetXmlStr + "</contact></contacts>";
            }
            return sRetXmlStr;
        }

        public string getValueOfEditComponent(string sWhatEditComp, PeopleF ppl)
        {

            string recordValue = "";

            if (sWhatEditComp == "edLName2")
                return ppl.LNAME;

            if (sWhatEditComp == "edFName2")
                return ppl.FNAME;

            if (sWhatEditComp == "edMName2")
                return ppl.MNAME;

            if (sWhatEditComp == "edTitle2")
                return ppl.TITLE;

            if (sWhatEditComp == "edStreet2")
                return ppl.STREET;

            if (sWhatEditComp == "edCity2")
                return ppl.CITY;

            if (sWhatEditComp == "edState2")
                return ppl.STATE;

            if (sWhatEditComp == "edZip2")
                return ppl.ZIP;

            if (sWhatEditComp == "edHmPhn2")
                return ppl.HMPH;

            if (sWhatEditComp == "edBsPhn2")
                return ppl.BUSPH;

            if (sWhatEditComp == "edCell2")
                return ppl.CELLPH;

            if (sWhatEditComp == "edFax2")
                return ppl.FAX;

            if (sWhatEditComp == "edEmail2")
                return ppl.EMAIL;

            if (sWhatEditComp == "edPID2")
                return ppl.PID.ToString();

            if (sWhatEditComp == "edPrefix2")
                return ppl.PREFIX;

            if (sWhatEditComp == "edOccup2")
                return ppl.OCCUPATION;

            if (sWhatEditComp == "edEmp2")
                return ppl.EMPLOYER;

            if (sWhatEditComp == "edSuffix2")
                return ppl.SUFFIX;

            if (sWhatEditComp == "edAddr21")
                return ppl.ADDR1;

            if (sWhatEditComp == "edAddr22")
                return ppl.ADDR2;

            if (sWhatEditComp == "edPlus4_2")
                return ppl.PLUS4;

            if (sWhatEditComp == "edAssistant2")
                return ppl.ASSISTANT;

            if (sWhatEditComp == "edInfSal2")
                return ppl.INFSALUT;

            if (sWhatEditComp == "edSalut2")
                return ppl.SALUTATION;

            if (sWhatEditComp == "edSpouse2")
                return ppl.SPOUSENAME;


            return recordValue;
        }

        public string getVCardKey(string sKey)
        {
            string sRetStr = "";
            for (int iLoop = 0; iLoop < _XmlTovCardMapping.Count; iLoop++)
            {
                string sNodeName = _XmlTovCardMapping[iLoop].nodeName.Substring(2);
                if (sNodeName == sKey)
                {
                    sRetStr = _XmlTovCardMapping[iLoop].nodeValue;
                    break;
                }
            }
            return sRetStr;
        }

        public string processXmlStringAndSaveVCard(string sThisXml, PeopleF ppl)
        {
            string sVCARD = "";
            sVCARD = "BEGIN:VCARD" + "\r\n";
            sVCARD = sVCARD + "VERSION:2.1" + "\r\n";

            TextReader tr = new StringReader(sThisXml);
            XDocument oDoc = XDocument.Load(tr);

            IEnumerable<XElement> oContactList = oDoc.Element("contacts").Elements("contact");
            var oContact = from c in oContactList.Elements() select c;

            for (int iLoop = 0; iLoop < oContact.Count(); iLoop++)
            {
                sVCARD = sVCARD + getVCardKey(oContact.ElementAt(iLoop).Name.ToString()) + oContact.ElementAt(iLoop).Value.ToString() + "\r\n";
            }

            sVCARD = sVCARD + "END:VCARD";

            return sVCARD;
        }
        
        #endregion
        
        public override void ExecuteResult(ControllerContext context)
        {

            #region [[ Configure VCard Now ]]
            getVcardConfigList("cmdiapp.n.core.Resources.XmlTovCardMapping");
            getVcardConfigList("cmdiapp.n.core.Resources.CrimsonToXmlMappingGeneral");
            getVcardConfigList("cmdiapp.n.core.Resources.CrimsonToXmlMappingSpecific");
            getVcardConfigList("cmdiapp.n.core.Resources.vCardTagsToCrimson");
            getVcardConfigList("cmdiapp.n.core.Resources.vCardFieldDetailsToCrimson");
            getVcardConfigList("cmdiapp.n.core.Resources.vCardPhoneSpaceBetweenValuesCheck");
            //now get the VCard as string...
            string _cardDetails = processXmlStringAndSaveVCard(getXmlStringForVCard(_pplrecord), _pplrecord);
            #endregion

            #region [[ Create the Response ]]
            var response = context.HttpContext.Response;
            response.ContentType = "text/vcard";
            response.AddHeader("Content-Disposition", "attachment; fileName=\"" + _fileName + ".VCF\"");

            var cardString = _cardDetails.ToString();
            var inputEncoding = Encoding.Default;
            var outputEncoding = Encoding.GetEncoding("windows-1257");
            var cardBytes = inputEncoding.GetBytes(cardString);

            var outputBytes = Encoding.Convert(inputEncoding,
                                    outputEncoding, cardBytes);
            #endregion
            
            response.OutputStream.Write(outputBytes, 0, outputBytes.Length);
        }


    }
}