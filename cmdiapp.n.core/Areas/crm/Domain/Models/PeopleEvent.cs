﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Data;
using System.Linq;
using System.Web.Mvc;
using System.Data.Linq;
using System.ComponentModel.DataAnnotations.Schema;

using cmdiapp.n.core.Areas.query.Domain.Models;
using cmdiapp.n.core.Domain.ViewModels;
using cmdiapp.n.core.Areas.crm.Domain.Data;
using System.Xml;
using System.Xml.Linq;

namespace cmdiapp.n.core.Areas.crm.Domain.Models
{

    [Table("pmSPCEVNT")]
    public class EventF : _entityBase_crm, iItemType
    {
        [Column]
        [Key]
        public int SPCEVNTID { get; set; }

        [Column]
        public string EVNTCODE { get; set; }

        [Column]
        public string EVNTDESC { get; set; }

        [Column]
        public DateTime? STARTDTE { get; set; }

        [NotMapped]
        public DateTime? STARTDTEc { get; set; }

        [Column]
        public DateTime? ENDDTE { get; set; }

        [NotMapped]
        public DateTime? ENDDTEc { get; set; }

        [Column]
        public string COMMENT { get; set; }

        [Column]
        public DateTime? LASTUSED { get; set; }

        [Column]
        public DateTime? UPDATEDON { get; set; }

        [Column]
        public decimal? GOAL { get; set; }

        [Column]
        public decimal? COST { get; set; }

        [Column]
        public string HOST { get; set; }

        [Column]
        public DateTime? EVNTTIME { get; set; }
        
        [NotMapped]
        public DateTime? EVNTTIMEc { get; set; }

        [Column]
        public string REGION { get; set; }

        [Column]
        public string STATE { get; set; }

        [Column]
        public string STAFFLEAD { get; set; }

        [Column]
        public string VENUE { get; set; }

        [Column]
        public DateTime? ENDTIME { get; set; }
        
        [NotMapped]
        public DateTime? ENDTIMEc { get; set; }

        [Column]
        public string LOCATION { get; set; }

        [Column]
        public decimal? GOALLOW { get; set; }

        [Column]
        public decimal? GOALMID { get; set; }

        [Column]
        public decimal? GOALHIGH { get; set; }

        [Column]
        public decimal? TICKETCOST { get; set; }

        [Column]
        public decimal? UNIT { get; set; }

        [Column]
        public int? TICKETSOLD { get; set; }

        [Column]
        public string STREET { get; set; }

        [Column]
        public string CITY { get; set; }

        [Column]
        public string ZIP { get; set; }

        [Column]
        public string MAINEVNTCODE { get; set; }

        [Column]
        public bool? PRIVATE { get; set; }

        [Column]
        public Int16? FUNDID { get; set; }

        [Column]
        public bool TICKET { get; set; }

        [NotMapped]
        public string DISPLAYNAMEc { get { return $"{EVNTCODE }-{EVNTDESC ?? ""}"; } }

    }

    public class EventPrintCallSheet
    {
         public string pVersion { get; set; }

         public int SPCEVNTID { get; set; }
    }

    public class Event : _entityBase_crm, iItemType
    {
        public int SPCEVNTID { get; set; }

        public int? EventCount { get; set; }
        
        public string EVNTCODE { get; set; }

        public string EVNTFULL { get; set; }
                
        public string EVNTDESC { get; set; }
                
        public DateTime? STARTDTE { get; set; }
                
        public DateTime? ENDDTE { get; set; }
          
    }

    public class AttendeesByState : _entityBase_crm, iItemType
    {
        public int TOTAL  { get; set; }

        public string STATE  { get; set; }

    }
    
    public class costData : _entityBase_crm, iItemType
    {
        public decimal? raised { get; set; }

        public DateTime? fgiftdte { get; set; }

        public DateTime? lgiftdte { get; set; }

        public decimal? fgift { get; set; }

        public decimal? lgift { get; set; }

    }

    public class raisedValueData : _entityBase_crm, iItemType
    {
        public decimal? raisedValue { get; set; }

    }
    
    [Table("pmSPCEVNTFLD")]
    public class EventQuestion: _entityBase_crm, iItemType
    {
        [Column]
        [Key]
        public int SPCEVNTFLDID { get; set; }

        [Column]
        public int SPCEVNTID { get; set; }

        [Column]
        public string FIELDNAME { get; set; }

        [Column]
        public Int16 FIELDSEQ { get; set; }

        [Column]
        public bool TOTALFLD { get; set; }

    }

    [Table("pmSPCEVNTDOC")]
    public class EventDoc : _entityBase_crm, iItemType
    {
        [Column]
        [Key]
        public int SPCEVNTDOCID { get; set; }

        [Column]
        public int SPCEVNTID { get; set; }

        [Column]
        public string FILENAME { get; set; }

        [Column]
        public Byte[]  CONTENT { get; set; }
    }

    public class EventQuestionAdd : _entityBase_crm, iItemType
    {
        public int SPCEVNTID { get; set; }

        public string FIELDNAME { get; set; }
    }

    public class DocEmail : _entityBase_crm, iItemType
    {
       public int SPCEVNTID { get; set; }

        public int SPCEVNTDOCID { get; set; }

        public string FILENAME { get; set; }

        public string USREMAIL { get; set; }
                
        public string MAILCC { get; set; }

        public string MAILTO { get; set; }

        public string SUBJECT { get; set; }

        public string MESSAGE { get; set; }
       
    }

    public class ProfileEmail : _entityBase_crm, iItemType
    {
        public int gPID { get; set; }

        public string PREFIX { get; set; }

        public string FULLNAME { get; set; }

        public string FULLNAME2 { get; set; }

        public string USREMAIL { get; set; }

        public string MAILCC { get; set; }

        public string MAILTO { get; set; }

        public string SUBJECT { get; set; }

        public string MESSAGE { get; set; }

    }

    public class cl_email
    {
        public cl_email()
        {
            ATTACHMENTS = new System.Collections.Generic.Stack<string>();
        }

        public string FROM { get; set; }
        public string TO { get; set; }
        public string CC { get; set; }
        public string BCC { get; set; }
        public string SUBJECT { get; set; }
        public string BODY { get; set; }
        public System.Collections.Generic.Stack<string> ATTACHMENTS { get; set; }
        public string REPORT { get; set; }
        public string REPORT_NAME { get; set; }
        public string BLOB { get; set; }
        public string BLOB_NAME { get; set; }

        public void AddAttachment(System.IO.FileInfo file)
        {
            // upload to server, get the file name
            ATTACHMENTS.Push(string.Empty);
        }

        public void AddAttachment(string query)
        {
            // upload to server, get the file name
            ATTACHMENTS.Push(query);
        }

        // Override function that returns XML string
        public override string ToString()
        {
            // get list of attachments
            XElement attachment = new XElement("ATTACHMENTS");

            while (ATTACHMENTS.Count > 0)
            {
                attachment.Add(new XElement("ATTACHMENT", ATTACHMENTS.Pop()));
            }

            // form the main XML node
            XElement report = new XElement("REPORT", REPORT);
            if (!string.IsNullOrEmpty(REPORT_NAME))
            {
                report.Add(new XAttribute("filename", REPORT_NAME));
            }

            // form the blob XML node
            XElement blob = new XElement("BLOB", BLOB);
            if (!string.IsNullOrEmpty(BLOB_NAME))
            {
                blob.Add(new XAttribute("filename", BLOB_NAME));
            }

            XElement email = new XElement("EMAIL", new XElement("FROM", FROM),
                                                   new XElement("TO", TO),
                                                   new XElement("CC", CC),
                                                   new XElement("BCC", BCC),
                                                   new XElement("SUBJECT", SUBJECT),
                                                   new XElement("BODY", BODY),
                                                   blob,
                                                   report,
                                                   attachment);
            return email.ToString();
        }
    }    

    [NotMapped]
    public class EventDoc_ext1 : EventDoc
    {
        public int count_ { get; set; }

        public Int64 rowNo { get; set; }
    }
        
    [NotMapped]
    public class Event_ext1 : Event
    {
        public int count_ { get; set; }

        public Int64 rowNo { get; set; }
    }

    [NotMapped]
    public class EventF_ext1 : EventF
    {
        public int? EventCount { get; set; }

        public decimal AMTRECEIVED { get; set; }

        public decimal OUTSTANDINGPLEDGES { get; set; }

        public int NUMOFINVITEE { get; set; }

        public int NUMOFDONATION { get; set; }
    }

    [NotMapped]
    public class EventF_ext2 : EventF_ext1
    {
        public int count_ { get; set; }

        public Int64 rowNo { get; set; }
    }


    public class EventDocSplExt : _entityBase_crm, iItemType
    {
        public int SPCEVNTDOCID { get; set; }
                
        public int SPCEVNTID { get; set; }
                
        public string FILENAME { get; set; }

        public int count_ { get; set; }

        public Int64 rowNo { get; set; }
                
    }




    # region [People Browser - Events]

    [Table("v_people_event")]
    public class v_people_event : _entityBase_crm, iItemType
    {
        [Column]
        [Key]
        public int jtSPCEVNTID { get; set; }

        [Column]
        public int PID { get; set; }

        [Column]
        public string EVNTCODE { get; set; }

        [Column]
        public string EVNTDESC { get; set; }

        [Column]
        public DateTime? STARTDTE { get; set; }

        public string STARTDTEs
        {
            get
            {
                return string.Format("{0:MM/dd/yyyy}", STARTDTE);
            }
        }

        [Column]
        public DateTime? ATTENDDTE { get; set; }

        public string ATTENDDTEs
        {
            get
            {
                return string.Format("{0:MM/dd/yyyy}", ATTENDDTE);
            }
        }

        [Column]
        public string STATUS { get; set; }
    }

    public class PeopleEventWithPayment
    {
        public v_people_event peopleEvent;
        public List<v_event_payments> eventPayments;
    }

    [Table("jtSPCEVNT")]
    public class jtSPCEVNT : _entityBase_crm, iItemType
    {
        [Column]
        [Key]
        public int jtSPCEVNTID { get; set; }

        [Column]
        public int SPCEVNTID { get; set; }

        [Column]
        public int PID { get; set; }

        [Column]
        public int? MID { get; set; }

        [Column]
        public DateTime? ATTENDDTE { get; set; }

        [Column]
        public string COMMENT { get; set; }

        [Column]
        public DateTime? UPDATEDON { get; set; }
                
        [Column]
        public short? STATUS { get; set; }

        [Column]
        public string TABLENO { get; set; }

        [Column]
        public bool CHECKEDIN { get; set; }

        [Column]
        public Int16? INVITEETYPEID { get; set; }
    }

    [Table("jtINVITEETICKET")]
    public class jtINVITEETICKET: _entityBase_crm, iItemType
    {
        [Column]
        [Key]
        public int jtINVITEETICKETID { get; set; }

        [Column]
        public int jtSPCEVNTID { get; set; }

        [Column]
        public int SPCEVNTTICKETID { get; set; }

        [Column]
        public int QUANTITY { get; set; }
    }

    [Table("v_event_payments")]
    public class v_event_payments : _entityBase_crm, iItemType
    {
        [Column]
        [Key]
        public int dtSPCEVNTMONYID { get; set; }

        [Column]
        public int MID { get; set; }

        [Column]
        public DateTime? BATCHDTE { get; set; }
        

        public string BATCHDTEs
        {
            get
            {
                return string.Format("{0:MM/dd/yyyy}", BATCHDTE);
            }
        }

        [Column]
        public decimal? AMT { get; set; }

        [Column]
        public string SRCECODE { get; set; }

        [Column]
        public int jtSPCEVNTID { get; set; }

    }

    public class EventMonyList : _entityBase_crm, iItemType
    {        
        public int MID { get; set; }
     
        public int PID { get; set; }
        
        public string FUNDCODE { get; set; }
      
        public string SRCECODE { get; set; }
       
        public string CENTERCODE { get; set; }
       
        public DateTime? BATCHDTE { get; set; }

        public string BATCHDTEs
        {
            get
            {
                return string.Format("{0:MM/dd/yyyy}", BATCHDTE);
            }
        }
       
        public decimal? AMT { get; set; }

    }

    public class PeopleEventQuesAns : _entityBase_crm, iItemType {

        public int? dtSPCEVNTID { get; set; }

        public int jtSPCEVNTID { get; set; }

        public string FIELDVALUE { get; set; }

        public int SPCEVNTFLDID { get; set; }

        public string FIELDNAME { get; set; }
    }

    [Table("dtSPCEVNT")]
    public class dtSPCEVNT : _entityBase_crm, iItemType
    {
        [Column]
        [Key]
        public int dtSPCEVNTID { get; set; }

        [Column]
        public int jtSPCEVNTID { get; set; }

        [Column]
        public int SPCEVNTFLDID { get; set; }

        [Column]
        public string FIELDVALUE { get; set; }
    }

    [Table("dtSPCEVNTMONY")]
    public class dtSPCEVNTMONY : _entityBase_crm, iItemType
    {
        [Column]
        [Key]
        public int dtSPCEVNTMONYID { get; set; }

        [Column]
        public int jtSPCEVNTID { get; set; }

        [Column]
        public int MID { get; set; }

    }

    [Table("dtSPCEVNTGUEST")]
    public class dtSPCEVNTGUEST : _entityBase_crm, iItemType
    {
        [Column]
        [Key]
        public int dtSPCEVNTGUESTID { get; set; }

        [Column]
        public int jtSPCEVNTID { get; set; }

        [Column]
        public string PREFIX { get; set; }

        [Column]
        public string FNAME { get; set; }
        
        [Column]
        public string MNAME { get; set; }
        
        [Column]
        public string LNAME { get; set; }
        
        [Column]
        public string SUFFIX { get; set; }
        
        [Column]
        public string CITY { get; set; }
        
        [Column]
        public string STATE { get; set; }
        
        [Column]
        public string OCCUPATION { get; set; }
        
        [Column]
        public string EMPLOYER { get; set; }
        
        [Column]
        public string COMMENT { get; set; }
        
        [Column]
        public string TABLENO { get; set; }
        
        [Column]
        public string STREET { get; set; }
        
        [Column]
        public string ADDR1 { get; set; }
        
        [Column]
        public string ADDR2 { get; set; }
        
        [Column]
        public string ZIP { get; set; }
        
        [Column]
        public string PLUS4 { get; set; }
        
        [Column]
        public string HMPHN { get; set; }
        
        [Column]
        public string BSPHN { get; set; }
        
        [Column]
        public string FAX { get; set; }
        
        [Column]
        public string CELL { get; set; }
        
        [Column]
        public string EMAIL { get; set; }
        
        [Column]
        public string INFSALUT { get; set; }

    }

    public class PeopleEventGuestQuesAns : _entityBase_crm, iItemType
    {

        public int? dtSPCEVNTGUESTFLDID { get; set; }

        public int dtSPCEVNTGUESTID { get; set; }

        public string FIELDVALUE { get; set; }

        public int SPCEVNTFLDID { get; set; }

        public string FIELDNAME { get; set; }
    }

    [Table("dtSPCEVNTGUESTFLD")]
    public class dtSPCEVNTGUESTFLD : _entityBase_crm, iItemType
    {
        [Column]
        [Key]
        public int dtSPCEVNTGUESTFLDID { get; set; }

        [Column]
        public int dtSPCEVNTGUESTID { get; set; }

        [Column]
        public int SPCEVNTFLDID { get; set; }

        [Column]
        public string FIELDVALUE { get; set; }
    }

    [Table("lkINVITEETYPE")]
    public class lkINVITEETYPE: _entityBase_crm, iItemType
    {
        [Column]
        [Key]
        public Int16 INVITEETYPEID { get; set; }

        [Column]
        public string DESCRIP { get; set; }

        [Column]
        public bool SYSTEM { get; set; }
    }

    [Table("SPCEVNTTICKET")]
    public class SPCEVNTTICKET: _entityBase_crm, iItemType
    {
        [Column]
        [Key]
        public int SPCEVNTTICKETID { get; set; }

        [Column]
        public int SPCEVNTID { get; set; }

        [Column]
        public string DESCRIP { get; set; }

        [Column]
        public decimal PARTICLEVEL { get; set; }

        public SPCEVNTTICKET() { }

        public SPCEVNTTICKET(int spcevntId)
        {
            SPCEVNTID = spcevntId;
            DESCRIP = "General Admission";
            PARTICLEVEL = 0;
        }
    }

    public class PeopleEventGuestContactQues : _entityBase_crm, iItemType
    {

        public int dtSPCEVNTGUESTID { get; set; }

        public int SPCEVNTID { get; set; }

    }

    public class AddInviteeSearchParams
    {
        public QueryInstanceInfo queryInstance { get; set; }
        public int eventId { get; set; }
        public int page { get; set; }
        public int pageSize { get; set; }
        public QueryRuntimeSort[] sort { get; set; }
    }

    public class EventInviteeGuestDisplay: iItemType
    {
        public int jtSPCEVNTID { get; set; }
        public int PID { get; set; }
        public int GUESTID { get; set; }
        public DateTime? RSVP { get; set; }
        public DateTime? UPDATEDON { get; set; }
        public string STATUS { get; set; }
        public string TYPE { get; set; }
        public string PREFIX { get; set; }
        public string FNAME { get; set; }
        public string MNAME { get; set; }
        public string LNAME { get; set; }
        public string SUFFIX { get; set; }
        public string SALUTATION { get; set; }
        public string MAILNAME { get; set; }
        public string STREET { get; set; }
        public string ADDR1 { get; set; }
        public string ADDR2 { get; set; }
        public string CITY { get; set; }
        public string STATE { get; set; }
        public string ZIP { get; set; }
        public string PLUS4 { get; set; }
        public string HMPHN { get; set; }
        public string BSPHN { get; set; }
        public string EMAIL { get; set; }
        public string CELL { get; set; }
        public string EMPLOYER { get; set; }
        public string OCCUPATION { get; set; }
        public decimal DONATED { get; set; }
        public decimal PLEDGED { get; set; }
        public decimal CTD { get; set; }
        public decimal OUTSTANDINGPLEDGE { get; set; }
        public bool CHECKEDIN { get; set; }
        public string ASSISTANT { get; set; }
        public string ASSTPHONE { get; set; }
        public string ASSTEMAIL { get; set; }
        public string PRESSALUT { get; set; }
    }

    public class InviteeSearchParams
    {
        public int eventId { get; set; }
        public string searchText { get; set; }
        public string status { get; set; } = "";
        public string type { get; set; } = "";
        public string checkedIn { get; set; } = "";
    }

    public class InviteeMassUpdateParams
    {
        public InviteeSearchParams searchParams { get; set; }
        public bool? checkedIn { get; set; }
        public int statusId { get; set; }
        public bool runSearch { get; set; }
        public int[] exceptionIds { get; set; } = new int[0];
        public int[] fewIncludeIds { get; set; } = new int[0];
    }

    # endregion
}