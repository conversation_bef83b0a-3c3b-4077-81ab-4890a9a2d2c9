﻿using cmdiapp.n.core._Domain.Models;
using cmdiapp.n.core.Areas.query.Domain.Models;
using cmdiapp.n.core.Areas.query.Domain.Services;
using System;

namespace cmdiapp.n.core._Domain.Services.implementation
{
    public class EmailReportQueryCompilerFactory : IEmailReportQueryCompilerFactory
    {
        private readonly ISavedQueryService _savedQueryService;
        private readonly IReportInstanceService _reportInstanceService;
        private readonly IReportQService _reportQService;

        public EmailReportQueryCompilerFactory(
            ISavedQueryService savedQueryService,
            IReportInstanceService reportInstanceService,
            IReportQService reportQService)
        {
            _savedQueryService = savedQueryService;
            _reportInstanceService = reportInstanceService;
            _reportQService = reportQService;
        }

        public IEmailReportQueryCompiler GetCompiler(string compilationSourceTypeCode)
        {
            switch (compilationSourceTypeCode)
            {
                case EmailReportCompilationSourceTypeCode.Search:
                    return new EmailReportSearchQueryCompiler(_savedQueryService);
                case EmailReportCompilationSourceTypeCode.Report:
                    return new ReportQueryCompiler(_reportInstanceService, _reportQService);
                case EmailReportCompilationSourceTypeCode.QueryExport:
                    return new EmailReportSearchQueryCompiler(_savedQueryService, getInstanceFromLogTable: true);
                default:
                    throw new ArgumentException(
                        $"{compilationSourceTypeCode} is not a supported compilation source type.",
                        nameof(compilationSourceTypeCode));
            }
        }
    }
}