﻿using cmdiapp.n.core.Areas.datax.Domain.Data;
using cmdiapp.n.core.Areas.datax.Domain.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace cmdiapp.n.core.Areas.datax.Domain.Services.implementation
{
    /// <summary>
    /// Implementation of <see cref="ILookupService"/>
    /// </summary>
    public class LookupService : ILookupService
    {
        private readonly I_entity_datax _entity;
        public LookupService(I_entity_datax entity)
        {
            _entity = entity;
        }

        public List<SqlTimeZone> GetTimeZones()
        {
            try
            {
                var results = _entity.getContext().Database.SqlQuery<SqlTimeZone>(
                        "SELECT Label, Value, OffsetHours, OffsetMinutes FROM getTimeZones()"
                    ).OrderBy(tz => tz.OffsetHours)
                    .ThenBy(tz => tz.OffsetMinutes)
                    .ThenBy(tz => tz.Value)
                    .ToList();
                return results ?? new List<SqlTimeZone>();
            }
            catch (Exception)
            {
                return new List<SqlTimeZone>();
            }
        }
    }
}