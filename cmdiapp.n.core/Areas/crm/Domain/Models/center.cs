﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Data;
using System.Linq;
using System.Web.Mvc;
using System.Data.Linq;
using System.ComponentModel.DataAnnotations.Schema;

using cmdiapp.n.core.Domain.ViewModels;
using cmdiapp.n.core.Areas.crm.Domain.Data;

namespace cmdiapp.n.core.Areas.crm.Domain.Models
{
    // Updated by Lydia 2013-10-17
    [Table("dmCENTER")]
    public class dmCENTER : _entityBase_crm, iItemType
    {
        [Column]
        [Key]
        public Int16 CENTERID { get; set; }

        [Column]
        public string CENTERCODE { get; set; }

        [Column]
        public string DESCRIP { get; set; }

        public string CODEDESCRIP
        {
            get
            {
                return string.Format("{0} - {1}", CENTERCODE, DESCRIP);
            }
        }

        [Column]
        public Nullable<DateTime> UPDATEDON { get; set; }

        [Column]
        public string CCPROFILE { get; set; }

        [Column]
        public string BANKACCT { get; set; }

        [Column]
        public Nullable<decimal> BEGINBAL { get; set; }

        [Column]
        public Nullable<DateTime> BEGINBALDTE { get; set; }

        [Column]
        public Nullable<Byte> DEFFLAG { get; set; }

        [Column]
        public Nullable<Byte> SYSTEM { get; set; }

        [Column]
        public bool ACTIVE { get; set; }
    }
    //for save purpose because of DEFFLAG and SYSTEM data type
    public class dmCENTER2 : _entityBase_crm, iItemType
    {
        public Int16 CENTERID { get; set; }
        public string CENTERCODE { get; set; }
        public string DESCRIP { get; set; }
        public string CODEDESCRIP
        {
            get
            {
                return string.Format("{0} - {1}", CENTERCODE, DESCRIP);
            }
        }
        public Nullable<DateTime> UPDATEDON { get; set; }
        public string CCPROFILE { get; set; }
        public string BANKACCT { get; set; }
        public Nullable<decimal> BEGINBAL { get; set; }
        public Nullable<DateTime> BEGINBALDTE { get; set; }
        public bool? DEFFLAG { get; set; }
        public bool? SYSTEM { get; set; }
        public bool ACTIVE { get; set; }
    }

    public class _ssCONFIG
    {
        public int CONFIGID { get; set; }
        public string CONFIGCODE { get; set; }
        public string CONFIGDESC { get; set; }
        public string CONFIGVALUE { get; set; }
    }

    public class dmCENTERForDataEntry
    {
        public Int16 CENTERID { get; set; }
                
        public string CENTERCODE { get; set; }

        public string CENTERDESC { get; set; }

    }
}