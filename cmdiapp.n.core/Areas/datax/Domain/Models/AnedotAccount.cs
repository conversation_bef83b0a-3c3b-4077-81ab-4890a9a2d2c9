﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.Serialization;
using System.Web;

namespace cmdiapp.n.core.Areas.datax.Domain.Models
{
    /// <summary>
    /// (partial) account class sent back from Anedot
    /// </summary>
    [DataContract]
    public class AnedotAccount
    {
        [DataMember(Name = "uid")]
        public string Id { get; set; }

        [DataMember(Name = "name")]
        public string Name { get; set; }

        [DataMember(Name = "display_name")]
        public string DisplayName { get; set; }        
    }
}