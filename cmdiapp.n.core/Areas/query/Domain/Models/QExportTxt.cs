﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Web.Hosting;

using cmdiapp.n.core.Core;
using cmdiapp.n.core.Areas.crm.Core;
using System.IO;

namespace cmdiapp.n.core.Areas.query.Domain.Models
{
    public class QExportTxt : IQExportFormat
    {
        public string FileFormat { get; set; }
        public bool IncludeHeader { get; set; }
        public TxtFormatParams FormatParams { get; set; }

        public QExportTxt(TxtFormatParams formatParams, bool includeHeader=false, string fileExtension="txt")
        {
            FileFormat = fileExtension;
            IncludeHeader = includeHeader;
            FormatParams = formatParams;
        }

        public Tuple<string, string> ToFile(DataTable result, string sheetName)
        {
            Tuple<string, string> fileNames = GetFileNames(sheetName);
            SaveFile(result, fileNames.Item1);

            return fileNames;
        }

        private void SaveFile(DataTable result, string fileName)
        {
            string fileString = GetFileString(result);
            using (StreamWriter writer = new StreamWriter(HostingEnvironment.MapPath($"~/tmp/{fileName}")))
            {
                writer.Write(fileString);
            }
        }

        private string GetFileString(DataTable result)
        {
            StringBuilder sb = new StringBuilder();
            if (IncludeHeader)
            {
                IEnumerable<string> colNames = result.Columns.Cast<DataColumn>().Select(col => col.ColumnName);
                sb.Append(string.Join(FormatParams.ColDelimiter, colNames));
                sb.Append(FormatParams.RowDelimiter);
            }
            sb.Append(string.Join(FormatParams.RowDelimiter,
                result.Rows.OfType<DataRow>()
                    .Select(row => string.Join(FormatParams.ColDelimiter,
                        row.ItemArray.Select(val => FormatValue(val))))));
            string fileString = sb.ToString();
            return fileString;
        }

        private string FormatValue(object value)
        {
            string formattedResult;
            if (value.GetType().Name == "DateTime")
            {
                formattedResult = String.Format("{0:MM/dd/yyyy}", value);
            }
            else
            {
                formattedResult = value?.ToString() ?? "";
            }

            formattedResult = formattedResult.Replace(FormatParams.RowDelimiter, "");
            formattedResult = EscapeAnyColDelimiters(formattedResult);

            return formattedResult;

        }

        private string EscapeAnyColDelimiters(string str)
        {
            string result = str;
            if (FormatParams.QuoteFieldsWithColDelim
                && result.Contains(FormatParams.ColDelimiter))
            {
                // double any quotes within field
                result =
                    result.Replace(
                        FormatParams.QuoteChar.ToString(),
                        FormatParams.QuoteChar.ToString() + FormatParams.QuoteChar.ToString());
                // add quotes
                result = FormatParams.QuoteChar.ToString() + result + FormatParams.QuoteChar.ToString();
            }

            if (FormatParams.RemoveColDelim && result.Contains(FormatParams.ColDelimiter))
            {
                // remove any col delimiters within field
                result = result.Replace(FormatParams.ColDelimiter, "");
            }

            return result;
        }

        private Tuple<string, string> GetFileNames(string sheetName)
        {
            string sToday = String.Format("{0:yyyyMMMdd__hh_mm_ss tt}", DateTime.Now);
            string clientFileName =
                $"{sheetName.Trim().Replace(" ", "")}_{session.userSession.UserName}_{sToday}.{FileFormat}";
            string serverFileName =
                $"{sheetName.Trim().Replace(" ", "")}_{Guid.NewGuid()}_{session.userSession.UserName}_{sToday}.{FileFormat}";

            return new Tuple<string, string>(serverFileName, clientFileName);
        }
    }
}