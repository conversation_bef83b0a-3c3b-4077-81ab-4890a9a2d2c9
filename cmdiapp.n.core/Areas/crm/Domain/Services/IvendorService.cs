﻿using System.Collections.Generic;
using System.Web.Mvc;

using cmdiapp.n.core.Areas.crm.Domain.Models;
using cmdiapp.n.core.Areas.crm.Domain.ViewModels;
using cmdiapp.n.core.Areas.crm.Domain.Repositories;
using System;

namespace cmdiapp.n.core.Areas.crm.Domain.Services
{
    public interface IvendorService
    {
        List<v_entity_ext> get_v_vendor_list(string where, string orderBy, int pageSize, int pageNo);
        byte[] pictureOf(int entityid);
        int saveVendor(Vendor _vendor);
        int addVendor(Vendor _vendor);
        bool check_TxnExistsForVendor(int entityid);
        int deleteVendor(string _sql);

        /// <summary>
        /// Returns Tuple of total count and paged, filtered List of TXN statement records
        /// </summary>
        /// <param name="entityId"></param>
        /// <param name="txntype"></param>
        /// <param name="page"></param>
        /// <param name="pageSize"></param>
        /// <param name="fundCode"></param>
        /// <param name="adjType"></param>
        /// <param name="txnDate"></param>
        /// <param name="adjDate"></param>
        /// <returns></returns>
        Tuple<int, List<v_entity_txn>> GetTxnStatement(
            int entityId, string txntype, int page, int pageSize,
            string fundCode = "", string adjType = "", string txnDate = "", string adjDate = "");
        
        /// <summary>
        /// Gets filters with applicable options for TXN statement view
        /// </summary>
        /// <param name="entityId"></param>
        /// <param name="txnType"></param>
        /// <returns></returns>
        List<DataTableFilter> GetTxnStatementFilters(int entityId, string txnType);
    }
}
