﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

using cmdiapp.n.core.Domain.ViewModels;
using cmdiapp.n.core.Areas.crm.Domain.Data;

namespace cmdiapp.n.core.Areas.crm.Domain.Models
{
    public class MapLatLng : _entityBase_crm, iItemType
    {
        public int PID { get; set; }

        public decimal? LATITUDE { get; set; }

        public decimal? LONGITUDE { get; set; }

    }

    public class EventAddresses : _entityBase_crm, iItemType
    {
        public int SPCEVNTID { get; set; }

        public string STREET { get; set; }

        public string CITY { get; set; }

        public string STATE { get; set; }

        public string ZIP { get; set; }

        public string VENUE { get; set; }

        public decimal? LATITUDE { get; set; }

        public decimal? LONGITUDE { get; set; }

    }
}