﻿using cmdiapp.n.core.Areas.legislator.Domain.Data;
using cmdiapp.n.core.Domain.ViewModels;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Web;

namespace cmdiapp.n.core.Areas.legislator.Domain.Models
{
    [Table("Contact")]
    public class Contact : _entityBase_legislator, iItemType
    {
        [Column, Key]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        public int Id { get; set; }

        [Column, MaxLength(25)]
        public string legislatorId { get; set; }

        [Column]
        public bool deleted { get; set; }

        [Column, MaxLength(40)]
        public string PREFIX { get; set; }

        [Column, MaxLength(30)]
        public string FNAME { get; set; }

        [Column, MaxLength(20)]
        public string MNAME { get; set; }

        [Column, <PERSON>Length(80)]
        public string LNAME { get; set; }

        [Column, <PERSON><PERSON><PERSON><PERSON>(20)]
        public string SUFFIX { get; set; }

        [Column, <PERSON><PERSON>ength(80)]
        public string ORGANIZATION { get; set; }

        [Column, <PERSON>Length(75)]
        public string TITLE { get; set; }

        [Column, MaxLength(60)]
        public string STREET { get; set; }

        [Column, MaxLength(60)]
        public string ADDR1 { get; set; }

        [Column, MaxLength(60)]
        public string ADDR2 { get; set; }

        [Column, MaxLength(50)]
        public string CITY { get; set; }

        [Column, MaxLength(2)]
        public string STATE { get; set; }

        [Column, MaxLength(5)]
        public string ZIP { get; set; }

        [Column, MaxLength(4)]
        public string PLUS4 { get; set; }

        [Column, MaxLength(50)]
        public string EMAIL { get; set; }

        [Column, MaxLength(20)]
        public string HMPHN { get; set; }

        [Column, MaxLength(20)]
        public string BSPHN { get; set; }

        [Column, MaxLength(20)]
        public string FAX { get; set; }

        [Column, MaxLength(20)]
        public string CELL { get; set; }

        [Column, MaxLength(100)]
        public string COMMENT { get; set; }

        [Column]
        public bool PRIME { get; set; }

        [Column]
        public DateTime? UPDATEDON { get; set; }

        [Column, MaxLength(50)]
        public string INFSALUT { get; set; }

        [Column, MaxLength(50)]
        public string ASSISTANT { get; set; }

        [Column]
        public int updatingUserId_i { get; set; }

        [Column]
        public int userId_i { get; set; }

        [Column]
        public int projectId { get; set; }

        [Column]
        public bool INACTIVE { get; set; }

        [Column]
        public DateTime? INACTIVEDTE { get; set; }

    }
}