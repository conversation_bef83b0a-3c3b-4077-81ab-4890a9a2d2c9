﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Data;
using System.Linq;
using System.Web.Mvc;
using System.Data.Linq;
using System.ComponentModel.DataAnnotations.Schema;
using cmdiapp.n.core.Domain.ViewModels;
using cmdiapp.n.core.Areas.crm.Domain.Data;

namespace cmdiapp.n.core.Areas.crm.Domain.Models
{
   
    [Table("webIMPPREF")]
    public class webIMPPREF : _entityBase_crm, iItemType
    {
        [Column]
        [Key]
        public int IMPPREFID { get; set; }

        [Column]
        public Int16? UID { get; set; }

        [Column]
        public byte? IMPTYPE { get; set; }

        [Column]
        public string PREFNAME { get; set; }

        [Column]
        public string FILECOLUMN { get; set; }

        [Column]
        public string DBCOLUMN { get; set; }

        [Column]
        public string MISCCOLUMN { get; set; }
    }
}