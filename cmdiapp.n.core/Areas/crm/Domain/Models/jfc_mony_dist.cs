﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Data;
using System.Linq;
using System.Web.Mvc;
using System.Data.Linq;
using System.ComponentModel.DataAnnotations.Schema;

using cmdiapp.n.core.Domain.ViewModels;
using cmdiapp.n.core.Areas.crm.Domain.Data;


namespace cmdiapp.n.core.Areas.crm.Domain.Models
{
    [Table("dtJFCDISTRIB")]
    public class dtJFCDISTRIB : _entityBase_crm, iItemType
    {
        [Column]
        [Key]
        public int dtDISTRIBID { get; set; }

        [Column]
        public int jtDISTRIBID { get; set; }

        [Column]
        public Int16 JFCCMTEID { get; set; }

        [Column]
        public Nullable<decimal> DISTRIBAMT { get; set; }

        [Column("_updating_UID")]
        public Nullable<Int16> UPDATING_UID { get; set; }

    }

    [Table("jtJFCDISTRIB")]
    public class jtJFCDISTRIB : _entityBase_crm, iItemType
    {
        [Column]
        [Key]
        public int jtDISTRIBID { get; set; }

        [Column]
        public int DISTRIBID { get; set; }

        [Column]
        public int MID { get; set; }

        [Column]
        public Nullable<decimal> AMT { get; set; }
    }

    [Table("JFCDISTRIB")]
    public class JFCDISTRIB : _entityBase_crm, iItemType
    {
        [Column]
        [Key]
        public int DISTRIBID { get; set; }
        [Column]
        public string DISTRIBNO { get; set; }
        [Column]
        public DateTime? DISTRIBDTE { get; set; }
        [Column]
        public short? FUNDID { get; set; }
        [Column]
        public short? STATUSID { get; set; }
        [Column]
        public string COMMENT { get; set; }
        [Column]
        public short? CREATEDBY { get; set; }
        [Column]
        public DateTime? CREATEDON { get; set; }
        [Column]
        public short? UPDATEDBY { get; set; }
        [Column]
        public DateTime? UPDATEDON { get; set; }
    }
    
   
    public class jfc_mony_dist
    {
        public Int16 jfccmteid { get; set; }

        public string cmtename { get; set; }

        public Int16 seq { get; set; }

        public Nullable<int> dtdistribid { get; set; }

        public int jtdistribid { get; set; }

        public Nullable<decimal> distribamt { get; set; }

    }

    public class jfc_dist_rec
    {
        public int jtdistribid { get; set; }

        public Nullable<int> distribid { get; set; }

        public string distribno { get; set; }

        public Nullable<DateTime> distribdte { get; set; }

        public Nullable<Int16> fundid { get; set; }

        public Nullable<Int16> statusid { get; set; }

        public string comment { get; set; }

        public Nullable<int> mid { get; set; }

        public Nullable<decimal> amt { get; set; }

        public List<jfc_mony_dist> distributions { get; set; }

    }

}
