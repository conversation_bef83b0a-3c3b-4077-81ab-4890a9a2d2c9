﻿using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Web;

namespace cmdiapp.n.core.Areas.datax.Domain.Models.ViewModels
{
    /// <summary>
    /// View model for displaying <see cref="Caller"/>.
    /// </summary>
    public class CallerDisplay
    {
        /// <summary>
        /// Initializes instance of <see cref="CallerDisplay"/>
        /// </summary>
        public CallerDisplay()
        {
        }

        /// <summary>
        /// <see cref="Caller.Id"/>
        /// </summary>
        public int Id { get; set; }

        /// <summary>
        /// Whether the Caller is active or not.
        /// SysAdmins can see inactive Callers.
        /// </summary>
        public bool IsActive { get; set; }

        /// <summary>
        /// JSON string of <see cref="DisplayConfig"/>;
        /// see <seealso cref="Caller.displayConfig"/>
        /// </summary>
        [JsonIgnore]
        public string ConfigJson { get; set; }

        /// <summary>
        /// Deserialized readonly property of <see cref="ConfigJson"/>;
        /// see <seealso cref="Caller.displayConfig"/>
        /// </summary>
        public CallerDisplayConfig DisplayConfig
        {
            get
            {
                if (string.IsNullOrEmpty(ConfigJson))
                {
                    return null;
                }
                try
                {
                    JsonSerializer serializer = new JsonSerializer();
                    JsonReader reader = new JsonTextReader(new StringReader(ConfigJson));
                    return serializer.Deserialize<CallerDisplayConfig>(reader);
                }
                catch (Exception)
                {
                    return null;
                }
            }
        }
    }
}