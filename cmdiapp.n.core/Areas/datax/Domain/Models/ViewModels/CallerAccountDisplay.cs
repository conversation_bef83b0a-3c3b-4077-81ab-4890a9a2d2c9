﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace cmdiapp.n.core.Areas.datax.Domain.Models.ViewModels
{
    /// <summary>
    /// View model for displaying accounts from caller/integrators
    /// </summary>
    public class CallerAccountDisplay
    {
        /// <summary>
        /// The account's Id.
        /// </summary>
        public string Id { get; set; }
        /// <summary>
        /// The name to display for this account.
        /// </summary>
        public string Name { get; set; }
    }
}