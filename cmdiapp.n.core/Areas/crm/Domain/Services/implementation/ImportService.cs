﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

using System.Data;
using System.Data.SqlClient;

using Ninject;
using Ninject.Web.Mvc;

using cmdiapp.n.core.Core.App_Start;
using cmdiapp.n.core.Areas.crm.Core;
using cmdiapp.n.core.Areas.crm.Domain.Models;
using cmdiapp.n.core.Areas.crm.Domain.ViewModels;
using cmdiapp.n.core.Areas.crm.Domain.Repositories;

using cmdiapp.n.core.Library;

namespace cmdiapp.n.core.Areas.crm.Domain.Services
{
    public class ImportService : IimportService
    {
        private I___unitOfWork _unitOfWork;
        private I__DBFactory _dbFactory;
        supportMethods _supportMethods = new supportMethods();

        public ImportService(I___unitOfWork unitOfWork, I__DBFactory dbFactory)
        {
            _unitOfWork = unitOfWork;
            _dbFactory = dbFactory;
        }

        public MessageBoard process_sql(string p_sql, string p_datasetName, MessageBoard _msgBoard)
        {
            try
            {
                //get the data
                DataSet _data = _supportMethods.GetDataSet(_dbFactory.getContext().Database.Connection.ConnectionString, p_sql, p_datasetName);
                //cehck what we have
                if (_data != null)
                {
                    _msgBoard.status = true;
                    _msgBoard.xmlresponse = _data.GetXml();
                }
                else
                {
                    _msgBoard.status = false;
                    _msgBoard.xmlresponse = "<error msg=\"error occurred during data retrieval\"/>";
                    _msgBoard.message = "Error in DB Process: Import " + p_datasetName + " " + " : Dataset is null.";
                }
                
            }
            catch (System.Exception ex)
            {
                //we have some issue
                _msgBoard.status = false;
                _msgBoard.xmlresponse = "";
                _msgBoard.message = "Error in DB Process: Import " +  p_datasetName + " " + ex.InnerException.ToString();
            }
            //return response
            return _msgBoard;
        }

        //public DataSet GetDataSet(string ConnectionString, string SQL, string p_datasetName)
        //{
        //    SqlConnection conn = new SqlConnection(ConnectionString);
        //    SqlDataAdapter da = new SqlDataAdapter();
        //    SqlCommand cmd = conn.CreateCommand();
        //    cmd.CommandText = SQL;
        //    cmd.CommandTimeout = 600;
        //    da.SelectCommand = cmd;
        //    DataSet ds = new DataSet();
        //    try
        //    {
        //        //Let us open the connection
        //        conn.Open();
        //        da.Fill(ds, p_datasetName);
        //    }
        //    catch (Exception ex)
        //    {
        //        //Some Issue
        //        conn.Close();
        //    }
        //    finally
        //    {
        //        //Close the connection
        //        conn.Close();
        //    }
        //    //get the Dataset
        //    return ds;
        //}

        //Read all Import Data Records stored in the Temp Table - For Step 5
        
        public List<ImportStep5ViewModel> read_all_import_data(string sql)
        {
            var q = _dbFactory.getContext().Database.SqlQuery<ImportStep5ViewModel>(String.Format("EXEC dbo.get_allDataSet '{0}'", sql));            
            return q.ToList();
        }

        public List<ImportStep5ViewModelJFC> read_all_import_dataJFC(string sql)
        {
            var q = _dbFactory.getContext().Database.SqlQuery<ImportStep5ViewModelJFC>(String.Format("EXEC dbo.get_allDataSet '{0}'", sql));
            return q.ToList();
        }

        public List<ImportStep5ViewModelSRCCODE> read_all_import_dataSRCCODE(string sql)
        {
            var q = _dbFactory.getContext().Database.SqlQuery<ImportStep5ViewModelSRCCODE>(String.Format("EXEC dbo.get_allDataSet '{0}'", sql));
            return q.ToList();
        }

        public List<ImportEventStep5ViewModel> read_all_event_import_data(string sql)
        {            
            var q = _dbFactory.getContext().Database.SqlQuery<ImportEventStep5ViewModel>(String.Format("EXEC dbo.get_allDataSet '{0}'", sql));
            return q.ToList();
        }

        public List<SOURCE> sourcesByFilter(string term)
        {
            //remove the spaces from source code and desc and get it
            string sql = string.Format("Select Top 10 [SRCEID] ,[PKGEID] ,[SRCECODE] , rtrim(ltrim([SRCECODE])) + '' - '' + rtrim(ltrim([SRCEDESC])) as SRCEDESC ,[LISTNOG] ,[LISTNO] ,[MAILDTE] ,[FIRSTCAGE],[LASTCAGE] ,[COSTPROD1],[COSTPROD2] ,[COSTPROD3] ,[COSTPOSTG] ,[COSTRESP1] ,[COSTRESP2] ,[COSTRESP3] ,[sQTYMAIL] ,[sMONY] ,[sPEOPLE] ,[sGROSS] ,[sNET] ,[sCOST] ,[sGROSSPM] ,[sNETPM] ,[sCOSTPM] ,[sGROSSRSP] ,[sNETRSP] ,[sCOSTRSP] ,[sRSPPCT],[isLM_SOURCE] ,[COMMENT] ,[UPDATEDON] ,[universe] ,[PROGSETID] ,[SPCEVNTID] ,[COSTACTI] ,[COSTPERC] ,[FIRSTACTI] ,[LASTACTI] ,[sACTIVITY]  ,[sPEOPLEi] ,[sRSPPCTi]  ,[COSTFLAT], [teleM_completedCalls], [teleM_noPledges], [teleM_totalPledges], [teleM],NonDonorCount from SOURCE where Upper(SRCECODE) LIKE ''{0}%''", term.ToUpper());

            var q = _dbFactory.getContext().Database.SqlQuery<SOURCE>(String.Format("EXEC dbo.get_allDataSet '{0}'", sql));
            //return the result
            return q.ToList();

        }

        public List<ExcelInputData> CheckIfColumnExists (string columnName, string tableName)
        {
            //remove the spaces from source code and desc and get it
            string sql = "Select Top 10 " + columnName + " as ColumnData FROM " + tableName;

            var q = _dbFactory.getContext().Database.SqlQuery<ExcelInputData>(String.Format("EXEC dbo.get_allDataSet '{0}'", sql));
            //return the result
            return q.ToList();

        }

        public List<ImportTxnStep5ViewModel> read_all_txn_import_data(string sql)
        {
            var q = _dbFactory.getContext().Database.SqlQuery<ImportTxnStep5ViewModel>(String.Format("EXEC dbo.get_allDataSet '{0}'", sql));
            return q.ToList();
        }

        public List<ImportTxnUpdateStep5ViewModel> read_all_txnupdate_import_data(string sql)
        {
            var q = _dbFactory.getContext().Database.SqlQuery<ImportTxnUpdateStep5ViewModel>(String.Format("EXEC dbo.get_allDataSet '{0}'", sql));
            return q.ToList();
        }

        public List<ImportAdjStep5ViewModel> read_all_adj_import_data(string sql)
        {
            var q = _dbFactory.getContext().Database.SqlQuery<ImportAdjStep5ViewModel>(String.Format("EXEC dbo.get_allDataSet '{0}'", sql));
            return q.ToList();
        }

        public List<ImportCorpMatchStep5ViewModel> read_all_corpmatch_import_data(string sql)
        {
            var q = _dbFactory.getContext().Database.SqlQuery<ImportCorpMatchStep5ViewModel>(String.Format("EXEC dbo.get_allDataSet '{0}'", sql));
            return q.ToList();
        }

        public List<ImportPledgeStep5ViewModel> read_all_pledge_import_data(string sql)
        {
            var q = _dbFactory.getContext().Database.SqlQuery<ImportPledgeStep5ViewModel>(String.Format("EXEC dbo.get_allDataSet '{0}'", sql));
            return q.ToList();
        }

        public List<ImportTributeStep5ViewModel> read_all_tribute_import_data(string sql)
        {
            var q = _dbFactory.getContext().Database.SqlQuery<ImportTributeStep5ViewModel>(String.Format("EXEC dbo.get_allDataSet '{0}'", sql));
            return q.ToList();
        }

        public List<ImportSupportStep5ViewModel> read_all_support_import_data(string sql)
        {
            var q = _dbFactory.getContext().Database.SqlQuery<ImportSupportStep5ViewModel>(String.Format("EXEC dbo.get_allDataSet '{0}'", sql));
            return q.ToList();
        }

        public int getCountFromTempTable(string sql)
        {
            var q = _dbFactory.getContext().Database.SqlQuery<UniquenessCheck>(sql);
            return q.FirstOrDefault().recordCount;
        }

        public List<ImportUpdateStep5ViewModel> read_all_update_import_data(string sql)
        {
            var q = _dbFactory.getContext().Database.SqlQuery<ImportUpdateStep5ViewModel>(String.Format("EXEC dbo.get_allDataSet '{0}'", sql));
            return q.ToList();
        }
        public List<ImportStep5ViewModelAction> read_all_action_import_data_by_sql(string sql)
        {
            var q = _dbFactory.getContext().Database.SqlQuery<ImportStep5ViewModelAction>(String.Format("EXEC dbo.get_allDataSet '{0}'", sql));
            return q.ToList();
        }
        public List<ImportStep5ViewModelMassAdjustment> read_all_massAdjustment_import_data(string sql)
        {
            var q = _dbFactory.getContext().Database.SqlQuery<ImportStep5ViewModelMassAdjustment>(String.Format("EXEC dbo.get_allDataSet '{0}'", sql));
            return q.ToList();
        }
        public List<ImportStep5ViewModelMassAdjustmentUnmatched> read_all_massAdjustmentUnmatched_import_data(string sql)
        {
            var q = _dbFactory.getContext().Database.SqlQuery<ImportStep5ViewModelMassAdjustmentUnmatched>(String.Format("EXEC dbo.get_allDataSet '{0}'", sql));
            return q.ToList();
        }
        public List<ImportStep5ViewModelMassAdjustmentProcessed> read_all_massAdjustmentProcessed_import_data(string sql)
        {
            var q = _dbFactory.getContext().Database.SqlQuery<ImportStep5ViewModelMassAdjustmentProcessed>(String.Format("EXEC dbo.get_allDataSet '{0}'", sql));
            return q.ToList();
        }
    }
}