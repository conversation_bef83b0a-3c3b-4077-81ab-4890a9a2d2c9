﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Data;
using System.Linq;
using System.Web.Mvc;
using System.Data.Linq;
using System.ComponentModel.DataAnnotations.Schema;

using cmdiapp.n.core.Domain.ViewModels;
using cmdiapp.n.core.Areas.crm.Domain.Data;

namespace cmdiapp.n.core.Areas.crm.Domain.Models
{

    [Table("PLEDGE")]
    public class PLEDGE : _entityBase_crm, iItemType
    {
        [Column]
        [Key]
        public int PLEDGEID { get; set; }
       
        [Column]
        public Nullable<int> TRACKNO { get; set; }

        [Column]
        public DateTime? PLEDGEDTE { get; set; }

        [Column]
        public Nullable<decimal> PLEDGEAMT { get; set; }

        [Column]
        public string SRCECODE { get; set; }

        [Column]
        public Nullable<short> FUNDID { get; set; }

        [Column]
        public DateTime? ENTRYDTE { get; set; }

        [Column]
        public Nullable<short> UID { get; set; }

        [Column]
        public DateTime? EXPECTDTE { get; set; }

        [Column]
        public string COMMENT { get; set; }

        [Column]
        public Nullable<decimal> HOUSEAMT { get; set; }

        [Column]
        public DateTime? HOUSEDTE { get; set; }

        [Column]
        public Nullable<decimal> TRACKAMT { get; set; }

        [Column("_updating_uid")]
        public Nullable<short> updating_uid { get; set; }

        [Column]
        public Nullable<int> CLUBID { get; set; }

        public virtual IEnumerable<PLEDGETRACK> PLEDGETRACK { get; set; }
    }

    [Table("jtPEOPLEDGE")]
    public class jtPEOPLEDGE : _entityBase_crm, iItemType
    {
        [Column]
        [Key]
        public int jtPEOPLEDGEID { get; set; }

        [Column]
        public int PID { get; set; }

        [Column]
        public int PLEDGEID { get; set; }

        [Column("_updating_uid")]
        public Nullable<short> updating_uid { get; set; }
    }

    [Table("PEOPLEDGEDOC")]
    public class PEOPLEDGEDOC : _entityBase_crm, iItemType
    {
        [Column]
        public int PEOPLEDGEDOCID { get; set; }

        [Column]
        public int PLEDGEID { get; set; }

        [Column]
        public string FILENAME { get; set; }

        [Column]
        public byte[] CONTENT { get; set; }

        [Column("_updating_uid")]
        public short? updating_uid { get; set; }
    }

    [Table("jtMONYPLEDGE")]
    public class jtMONYPLEDGE : _entityBase_crm, iItemType
    {
        [Column]
        [Key]
        public int jtMONYPLEDGEID { get; set; }

        [Column]
        public int MID { get; set; }

        [Column]
        public int PLEDGEID { get; set; }

        [Column("_updating_uid")]
        public Nullable<short> updating_uid { get; set; }
    }

    // (view)v_people_pledge  -  Used to list all pledges per donor ]]
    // Updated by Lydia on 11/4/2013
    [Table("v_people_pledge")]
    public class PledgeR : _entityBase_crm, iItemType
    {
        [Column]
        [Key]
        public int PLEDGEID { get; set; }

        [Column]
        public int PID { get; set; }

        [Column]
        public DateTime? PLEDGEDTE { get; set; }

        [Column]
        public DateTime? EXPECTDTE { get; set; }

        [Column]
        public decimal? PLEDGEAMT { get; set; }

        [Column]
        public decimal? PAYMENT { get; set; }

        [Column]
        public string FUNDCODE { get; set; }

        [Column]
        public string SRCECODE { get; set; }

        [Column]
        public Nullable<int> TRACKNO { get; set; }

        [Column]
        public string CLUBCODE { get; set; }
    }

    /// <summary>
    /// Lists pledges by donor with payment amounts.
    /// The above class <see cref="PledgeR"/> is linked to a view
    /// (v_people_pledge) that only lists pledges in-cycle.
    /// </summary>
    [Table("v_peopleprofile_pledge")]
    public class PledgeView : _entityBase_crm, iItemType
    {
        [Column]
        [Key]
        public int PLEDGEID { get; set; }

        [Column]
        public int PID { get; set; }

        [Column]
        public DateTime? PLEDGEDTE { get; set; }

        [Column]
        public DateTime? EXPECTDTE { get; set; }

        [Column]
        public decimal? PLEDGEAMT { get; set; }

        [Column]
        public decimal? PAYMENT { get; set; }

        [Column]
        public string FUNDCODE { get; set; }

        [Column]
        public string SRCECODE { get; set; }

        [Column]
        public int? TRACKNO { get; set; }

        [Column]
        public string CLUBCODE { get; set; }
    }

    public class pledge_payments
    {
        public int jtMONYPLEDGEID { get; set; }

        public int MID { get; set; }

        public DateTime BATCHDTE { get; set; }

        public string BATCHDTEs
        {
            get
            {
                return string.Format("{0:MM/dd/yyyy}", BATCHDTE);
            }
        }

        public Nullable<decimal> AMT { get; set; }

        public string SRCECODE { get; set; }

        public Nullable<int> TRACKNO { get; set; }
    }

    public class pledge_balance
    {
        public decimal PLEDGEBAL { get; set; }
    }

    public class pledge_unlinks
    {
        public int MID { get; set; }

        public string FUNDCODE { get; set; }

        public string SRCECODE { get; set; }

        public DateTime BATCHDTE { get; set; }

        public string BATCHDTEs
        {
            get
            {
                return string.Format("{0:MM/dd/yyyy}", BATCHDTE);
            }
        }

        public Nullable<decimal> AMT { get; set; }

        public Nullable<int> TRACKNO { get; set; }
    }

    public class pledge_linkPayment: pledge_unlinks
    {
        public int PLEDGEID { get; set; }
    }

    [Table("PLEDGETRACK")]
    public class PLEDGETRACK : _entityBase_crm, iItemType
    {
        [Column]
        [Key]
        public int TRACKID { get; set; }

        [Column]
        public int PLEDGEID { get; set; }

        [Column]
        public Nullable<int> TRACKNO { get; set; }

        [Column]
        public decimal TRACKAMT { get; set; }

        [Column("_updating_uid")]
        public Int16? updating_uid { get; set; }
    }

    [NotMapped]
    public class PledgeR_ext1 : PledgeR
    {
        public int count_ { get; set; }

        public Int64 rowNo { get; set; }
    }
    public class pledge_summary
    {
        public decimal? TOTAL { get; set; }
        public decimal? PAID { get; set; }
        public int TOTALCOUNT { get; set; }
        public int PAIDCOUNT { get; set; }
        public int OUTSTANDINGCOUNT { get; set; }
        public decimal? OUTSTANDING { get; set; }
        public DateTime? LGIFTDTE { get; set; }
        public DateTime? FGIFTDTE { get; set; }
        public decimal? HPC { get; set; }
        public decimal? LGIFT { get; set; }
        public decimal? FGIFT { get; set; }
        public DateTime? HPCDTE { get; set; }
        public decimal? YTD { get; set; }
        public int? YTDCOUNT { get; set; }
        public decimal? PREV1YRAMT { get; set; }
        public decimal? PREV2YRAMT { get; set; }
        public decimal? PREV3YRAMT { get; set; }
    }
    public class pledge_SRC_Summary
    {
        public string SRCECODE { get; set; }
        public decimal? AMT { get; set; }
        public int COUNT { get; set; }
    }
    public class pledge_Fund_Summary
    {
        public string FUNDCODE { get; set; }
        public decimal? AMT { get; set; }
        public int COUNT { get; set; }
    }
}
