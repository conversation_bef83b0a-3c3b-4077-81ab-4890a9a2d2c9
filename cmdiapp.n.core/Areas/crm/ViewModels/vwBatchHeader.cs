﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using cmdiapp.n.core.Areas.crm.Domain.Models;

namespace cmdiapp.n.core.Domain.ViewModels
{
    public class vwBatchHeader
    {
        public IEnumerable<dmFUNDForDataEntry> dmFUND { get; set; }
        public IEnumerable<SOURCE> SOURCE { get; set; }
        public IEnumerable<dmCENTERForDataEntry> dmCENTER { get; set; }
        public BATCH BATCH { get; set; }
        public BatchExtended BatchExtended { get; set; }
        public bool ShowAcctCode { get; set; }
    }
}